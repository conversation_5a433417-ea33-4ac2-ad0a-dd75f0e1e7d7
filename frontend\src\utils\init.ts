import { useUserStore, useAppStore, useCacheStore } from '@/stores'

/**
 * 初始化应用状态
 */
export async function initializeApp() {
  const userStore = useUserStore()
  const appStore = useAppStore()
  const cacheStore = useCacheStore()

  try {
    // 初始化应用设置
    appStore.initAppSettings()

    // 初始化用户状态
    userStore.initUserState()

    // 启动缓存自动清理
    cacheStore.startAutoCleanup()

    console.log('应用初始化完成')
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
}

/**
 * 清理应用状态
 */
export function cleanupApp() {
  const userStore = useUserStore()
  const cacheStore = useCacheStore()

  try {
    // 清除用户数据
    userStore.clearUserData()

    // 清除缓存
    cacheStore.clear()

    console.log('应用清理完成')
  } catch (error) {
    console.error('应用清理失败:', error)
  }
}
