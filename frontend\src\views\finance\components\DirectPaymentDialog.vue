<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建直接收款记录"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent
    >
      <el-form-item label="选择对账单" prop="statement_id">
        <el-select
          v-model="form.statement_id"
          placeholder="请选择对账单"
          filterable
          style="width: 100%"
          @change="handleStatementChange"
        >
          <el-option
            v-for="statement in statements"
            :key="statement.id"
            :label="`${statement.statement_number} - ${statement.customer_name}`"
            :value="statement.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="对账单信息" v-if="selectedStatement">
        <div class="statement-info">
          <div class="info-row">
            <span class="label">客户名称：</span>
            <span class="value">{{ selectedStatement.customer_name }}</span>
          </div>
          <div class="info-row">
            <span class="label">对账单金额：</span>
            <span class="value amount">{{ formatCurrency(selectedStatement.adjusted_total_amount) }}</span>
          </div>
          <div class="info-row">
            <span class="label">已收金额：</span>
            <span class="value">{{ formatCurrency(selectedStatement.paid_amount || 0) }}</span>
          </div>
          <div class="info-row">
            <span class="label">未收金额：</span>
            <span class="value unpaid">{{ formatCurrency((selectedStatement.adjusted_total_amount || 0) - (selectedStatement.paid_amount || 0)) }}</span>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="收款金额" prop="amount">
        <el-input-number
          v-model="form.amount"
          :min="0.01"
          :max="maxAmount"
          :precision="2"
          :step="100"
          style="width: 100%"
          placeholder="请输入收款金额"
        />
      </el-form-item>

      <el-form-item label="支付方式" prop="payment_method">
        <el-select
          v-model="form.payment_method"
          placeholder="请选择支付方式"
          style="width: 100%"
        >
          <el-option label="银行转账" value="bank_transfer" />
          <el-option label="现金" value="cash" />
          <el-option label="支票" value="check" />
          <el-option label="在线支付" value="online_payment" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <el-form-item label="收款日期" prop="payment_date">
        <el-date-picker
          v-model="form.payment_date"
          type="date"
          placeholder="选择收款日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="交易流水号" prop="reference_number">
        <el-input
          v-model="form.reference_number"
          placeholder="请输入交易流水号（可选）"
          maxlength="100"
        />
      </el-form-item>

      <el-form-item label="收款账户" prop="bank_account">
        <el-input
          v-model="form.bank_account"
          placeholder="请输入收款账户（可选）"
          maxlength="200"
        />
      </el-form-item>

      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          确认收款
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { paymentApi, type DirectPaymentRequest } from '@/api/payment'
import { statementApi } from '@/api/statement'

// 定义props和emits
const props = defineProps<{
  modelValue: boolean
  statementId?: number
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const statements = ref<any[]>([])
const selectedStatement = ref<any>(null)

// 表单数据
const form = reactive<DirectPaymentRequest>({
  statement_id: 0,
  amount: 0,
  payment_method: '',
  payment_date: new Date().toISOString().split('T')[0],
  reference_number: '',
  bank_account: '',
  notes: ''
})

// 计算最大收款金额
const maxAmount = computed(() => {
  if (!selectedStatement.value) return 999999.99
  const unpaidAmount = (selectedStatement.value.adjusted_total_amount || 0) - (selectedStatement.value.paid_amount || 0)
  return Math.max(unpaidAmount, 0.01)
})

// 表单验证规则
const rules: FormRules = {
  statement_id: [
    { required: true, message: '请选择对账单', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入收款金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '收款金额必须大于0', trigger: 'blur' }
  ],
  payment_method: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  payment_date: [
    { required: true, message: '请选择收款日期', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 格式化货币
const formatCurrency = (amount: number): string => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 加载对账单列表
const loadStatements = async () => {
  try {
    const response = await statementApi.getList({
      per_page: 1000,
      status: '已确认' // 只显示已确认的对账单
    })
    statements.value = response.list || []
  } catch (error) {
    console.error('加载对账单列表失败:', error)
  }
}

// 对账单选择改变
const handleStatementChange = (statementId: number) => {
  selectedStatement.value = statements.value.find(s => s.id === statementId)
  
  // 自动设置收款金额为未付金额
  if (selectedStatement.value) {
    const unpaidAmount = (selectedStatement.value.adjusted_total_amount || 0) - (selectedStatement.value.paid_amount || 0)
    form.amount = Math.max(unpaidAmount, 0)
  }
}

// 重置表单
const resetForm = () => {
  form.statement_id = props.statementId || 0
  form.amount = 0
  form.payment_method = ''
  form.payment_date = new Date().toISOString().split('T')[0]
  form.reference_number = ''
  form.bank_account = ''
  form.notes = ''
  
  selectedStatement.value = null
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 确认对话框
    await ElMessageBox.confirm(
      `确认收款 ${formatCurrency(form.amount)} 吗？`,
      '确认收款',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    submitLoading.value = true
    
    await paymentApi.createDirectPayment(form)
    
    ElMessage.success('收款记录创建成功')
    emit('success')
    handleClose()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('创建收款记录失败:', error)
      ElMessage.error(error.message || '创建收款记录失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    loadStatements()
    resetForm()
    
    // 如果传入了对账单ID，自动选择
    if (props.statementId) {
      form.statement_id = props.statementId
      // 延迟执行，等待对账单列表加载完成
      setTimeout(() => {
        handleStatementChange(props.statementId!)
      }, 100)
    }
  }
})
</script>

<style scoped>
.statement-info {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
}

.value.amount {
  color: #409EFF;
  font-weight: bold;
}

.value.unpaid {
  color: #F56C6C;
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
