# EMB项目API文档

## 📋 API概览

EMB系统提供完整的RESTful API接口，支持工程物资管理的所有业务功能。API采用Flask-RESTX框架构建，自动生成Swagger文档。

### API特性
- **RESTful设计**: 标准化的HTTP接口
- **自动文档**: Swagger/OpenAPI 3.0自动生成
- **统一响应**: 标准化的JSON响应格式
- **数据验证**: 完整的输入数据验证
- **错误处理**: 详细的错误信息返回

### 基础信息
- **API基础路径**: `/api/v1`
- **文档地址**: http://localhost:5001/docs/
- **健康检查**: http://localhost:5001/test/health
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 🔧 API架构

### 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "errors": {
    "name": ["客户名称不能为空"],
    "phone": ["手机号格式不正确"]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页响应格式
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "pages": 5,
      "has_prev": false,
      "has_next": true
    }
  }
}
```

## 👥 客户管理API

### 客户基础操作
```http
GET    /api/v1/customers              # 获取客户列表
POST   /api/v1/customers              # 创建客户
GET    /api/v1/customers/{id}         # 获取客户详情
PUT    /api/v1/customers/{id}         # 更新客户
DELETE /api/v1/customers/{id}         # 删除客户
```

### 客户列表查询参数
```
page: 页码 (默认: 1)
per_page: 每页数量 (默认: 20, 最大: 100)
search: 搜索关键词 (客户名称、联系人、电话)
status: 状态筛选 (正常, 禁用)
level: 客户等级筛选
sort: 排序字段 (name, created_at)
order: 排序方向 (asc, desc)
```

### 客户银行账户管理
```http
GET    /api/v1/customers/{id}/bank-accounts        # 获取客户银行账户列表
POST   /api/v1/customers/{id}/bank-accounts        # 添加银行账户
PUT    /api/v1/customers/{id}/bank-accounts/{aid}  # 更新银行账户
DELETE /api/v1/customers/{id}/bank-accounts/{aid}  # 删除银行账户
```

### 客户送货地址管理
```http
GET    /api/v1/customers/{id}/delivery-addresses        # 获取送货地址列表
POST   /api/v1/customers/{id}/delivery-addresses        # 添加送货地址
PUT    /api/v1/customers/{id}/delivery-addresses/{aid}  # 更新送货地址
DELETE /api/v1/customers/{id}/delivery-addresses/{aid}  # 删除送货地址
```

## 📦 产品管理API

### 产品基础操作
```http
GET    /api/v1/products               # 获取产品列表
POST   /api/v1/products               # 创建产品
GET    /api/v1/products/{id}          # 获取产品详情
PUT    /api/v1/products/{id}          # 更新产品
DELETE /api/v1/products/{id}          # 删除产品
```

### 产品分类管理
```http
GET    /api/v1/products/categories    # 获取产品分类列表
POST   /api/v1/products/categories    # 创建产品分类
PUT    /api/v1/products/categories/{id}    # 更新产品分类
DELETE /api/v1/products/categories/{id}    # 删除产品分类
```

### 品牌管理
```http
GET    /api/v1/products/brands        # 获取品牌列表
POST   /api/v1/products/brands        # 创建品牌
PUT    /api/v1/products/brands/{id}   # 更新品牌
DELETE /api/v1/products/brands/{id}   # 删除品牌
```

### 产品规格管理
```http
GET    /api/v1/products/{id}/specifications        # 获取产品规格列表
POST   /api/v1/products/{id}/specifications        # 添加产品规格
PUT    /api/v1/products/{id}/specifications/{sid}  # 更新产品规格
DELETE /api/v1/products/{id}/specifications/{sid}  # 删除产品规格
```

## 💰 报价管理API

### 报价需求管理
```http
GET    /api/v1/quotations/requests     # 获取报价需求列表
POST   /api/v1/quotations/requests     # 创建报价需求
GET    /api/v1/quotations/requests/{id}    # 获取报价需求详情
PUT    /api/v1/quotations/requests/{id}    # 更新报价需求
DELETE /api/v1/quotations/requests/{id}    # 删除报价需求
```

### 报价单管理
```http
GET    /api/v1/quotations              # 获取报价单列表
POST   /api/v1/quotations              # 创建报价单
GET    /api/v1/quotations/{id}         # 获取报价单详情
PUT    /api/v1/quotations/{id}         # 更新报价单
DELETE /api/v1/quotations/{id}         # 删除报价单
```

### 报价单状态管理
```http
PUT    /api/v1/quotations/{id}/status  # 更新报价单状态
```

### 从需求生成报价单
```http
POST   /api/v1/quotations/from-request/{request_id}  # 从需求生成报价单
```

## 📋 订单管理API

### 订单基础操作
```http
GET    /api/v1/orders                  # 获取订单列表
POST   /api/v1/orders                  # 创建订单
GET    /api/v1/orders/{id}             # 获取订单详情
PUT    /api/v1/orders/{id}             # 更新订单
DELETE /api/v1/orders/{id}             # 删除订单
```

### 订单状态管理
```http
PUT    /api/v1/orders/{id}/status      # 更新订单状态
GET    /api/v1/orders/{id}/status-history  # 获取订单状态历史
```

### 从报价单生成订单
```http
POST   /api/v1/orders/from-quotation/{quotation_id}  # 从报价单生成订单
```

## 🚚 发货管理API

### 发货单管理
```http
GET    /api/v1/delivery-notes          # 获取发货单列表
POST   /api/v1/delivery-notes          # 创建发货单
GET    /api/v1/delivery-notes/{id}     # 获取发货单详情
PUT    /api/v1/delivery-notes/{id}     # 更新发货单
DELETE /api/v1/delivery-notes/{id}     # 删除发货单
```

### 发货单状态管理
```http
PUT    /api/v1/delivery-notes/{id}/status  # 更新发货单状态
```

## 💳 财务管理API

### 收款记录管理
```http
GET    /api/v1/finance/payments        # 获取收款记录列表
POST   /api/v1/finance/payments        # 创建收款记录
GET    /api/v1/finance/payments/{id}   # 获取收款记录详情
PUT    /api/v1/finance/payments/{id}   # 更新收款记录
DELETE /api/v1/finance/payments/{id}   # 删除收款记录
```

### 应收款管理
```http
GET    /api/v1/finance/receivables     # 获取应收款列表
GET    /api/v1/finance/receivables/summary  # 获取应收款汇总
```

### 财务报表
```http
GET    /api/v1/finance/reports/revenue      # 收入报表
GET    /api/v1/finance/reports/aging       # 账龄分析
GET    /api/v1/finance/reports/customer    # 客户财务分析
```

## 📊 工作台API

### 统计数据
```http
GET    /api/v1/dashboard/stats         # 获取统计数据
```

### 待处理业务
```http
GET    /api/v1/dashboard/pending-quotations  # 待处理报价单
GET    /api/v1/dashboard/pending-orders      # 待发货订单
GET    /api/v1/dashboard/overdue-receivables # 逾期应收款
```

### 图表数据
```http
GET    /api/v1/dashboard/charts/revenue      # 收入趋势图表
GET    /api/v1/dashboard/charts/orders       # 订单趋势图表
GET    /api/v1/dashboard/charts/products     # 产品分布图表
GET    /api/v1/dashboard/charts/customers    # 客户分析图表
```

## ⚙️ 系统管理API

### 企业信息管理
```http
GET    /api/v1/system/company          # 获取企业信息
PUT    /api/v1/system/company          # 更新企业信息
```

### 系统设置
```http
GET    /api/v1/system/settings         # 获取系统设置
PUT    /api/v1/system/settings         # 更新系统设置
GET    /api/v1/system/settings/{key}   # 获取特定设置项
PUT    /api/v1/system/settings/{key}   # 更新特定设置项
```

### 数据备份恢复
```http
POST   /api/v1/system/backup           # 创建数据备份
POST   /api/v1/system/restore          # 恢复数据
GET    /api/v1/system/backup/list      # 获取备份列表
DELETE /api/v1/system/backup/{id}      # 删除备份文件
```

### 错误日志管理
```http
GET    /api/v1/error-logs              # 获取错误日志列表
GET    /api/v1/error-logs/{id}         # 获取错误日志详情
DELETE /api/v1/error-logs/{id}         # 删除错误日志
POST   /api/v1/error-logs/clear        # 清空错误日志
```

### 品牌管理
```http
GET    /api/v1/system/brands           # 获取品牌列表
POST   /api/v1/system/brands           # 创建品牌
PUT    /api/v1/system/brands/{id}      # 更新品牌
DELETE /api/v1/system/brands/{id}      # 删除品牌
```

## 📁 文件管理API

### 文件上传
```http
POST   /api/v1/uploads                 # 上传文件
```

### 文件下载
```http
GET    /api/v1/uploads/{filename}      # 下载文件
```

### 产品图片管理
```http
POST   /api/v1/product-images          # 上传产品图片
DELETE /api/v1/product-images/{id}     # 删除产品图片
```

## 🔍 查询参数说明

### 通用查询参数
```
page: 页码 (默认: 1)
per_page: 每页数量 (默认: 20, 最大: 100)
search: 搜索关键词
sort: 排序字段
order: 排序方向 (asc, desc)
```

### 日期范围查询
```
start_date: 开始日期 (YYYY-MM-DD)
end_date: 结束日期 (YYYY-MM-DD)
date_field: 日期字段 (created_at, updated_at)
```

### 状态筛选
```
status: 状态值 (多个状态用逗号分隔)
```

## 🚨 错误码说明

### HTTP状态码
- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 数据验证失败
- `500` - 服务器内部错误

### 业务错误码
- `1001` - 客户名称已存在
- `1002` - 产品型号已存在
- `1003` - 报价单编号已存在
- `1004` - 订单编号已存在
- `2001` - 数据验证失败
- `2002` - 关联数据不存在
- `3001` - 文件上传失败
- `3002` - 文件格式不支持

## 📝 请求示例

### 创建客户
```bash
curl -X POST http://localhost:5001/api/v1/customers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试公司",
    "contact": "张三",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "address": "北京市朝阳区"
  }'
```

### 获取客户列表
```bash
curl -X GET "http://localhost:5001/api/v1/customers?page=1&per_page=20&search=测试"
```

### 创建产品
```bash
curl -X POST http://localhost:5001/api/v1/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试产品",
    "model": "TP-001",
    "unit": "个",
    "category_id": 1,
    "description": "这是一个测试产品"
  }'
```

---

**API文档版本**: v1.0  
**最后更新**: 2025年1月  
**API版本**: v1  
**维护状态**: 🔄 持续更新中
