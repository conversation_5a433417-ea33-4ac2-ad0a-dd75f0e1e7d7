<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建数据备份"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="备份名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入备份名称"
          maxlength="200"
          show-word-limit
          clearable
        />
      </el-form-item>
      
      <el-form-item label="备份描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入备份描述（可选）"
          :rows="3"
          maxlength="500"
          show-word-limit
          clearable
        />
      </el-form-item>
    </el-form>

    <!-- 进度显示 -->
    <div v-if="creating" class="progress-container">
      <el-progress
        :percentage="progress"
        :status="progressStatus"
        :stroke-width="8"
        text-inside
      />
      <p class="progress-text">{{ progressText }}</p>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="creating">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm" 
          :loading="creating"
          :disabled="creating"
        >
          {{ creating ? '备份中...' : '开始备份' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { backupApi } from '@/api/system'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const creating = ref(false)
const progress = ref(0)
const progressStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const progressText = ref('')

// 表单数据
const form = reactive({
  name: '',
  description: ''
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入备份名称', trigger: 'blur' },
    { min: 1, max: 200, message: '备份名称长度在 1 到 200 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  form.name = generateDefaultName()
  form.description = ''
  creating.value = false
  progress.value = 0
  progressStatus.value = ''
  progressText.value = ''
  
  // 清除表单验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 生成默认备份名称
const generateDefaultName = () => {
  const now = new Date()
  const dateStr = now.toLocaleDateString('zh-CN').replace(/\//g, '-')
  const timeStr = now.toLocaleTimeString('zh-CN', { hour12: false }).replace(/:/g, '-')
  return `手动备份_${dateStr}_${timeStr}`
}

// 模拟进度更新
const updateProgress = () => {
  const steps = [
    { progress: 10, text: '正在准备备份...' },
    { progress: 30, text: '正在读取数据库...' },
    { progress: 60, text: '正在创建备份文件...' },
    { progress: 80, text: '正在计算校验和...' },
    { progress: 95, text: '正在保存备份记录...' },
    { progress: 100, text: '备份完成！' }
  ]
  
  let currentStep = 0
  
  const updateStep = () => {
    if (currentStep < steps.length && creating.value) {
      const step = steps[currentStep]
      progress.value = step.progress
      progressText.value = step.text
      
      if (step.progress === 100) {
        progressStatus.value = 'success'
      }
      
      currentStep++
      
      if (currentStep < steps.length) {
        setTimeout(updateStep, 500 + Math.random() * 1000)
      }
    }
  }
  
  updateStep()
}

// 处理确认
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    creating.value = true
    progressStatus.value = ''
    
    // 开始进度更新
    updateProgress()
    
    // 调用API创建备份
    await backupApi.createBackup({
      name: form.name,
      description: form.description || undefined
    })
    
    ElMessage.success('备份创建成功')
    emit('success')
    dialogVisible.value = false
    
  } catch (error) {
    console.error('创建备份失败:', error)
    creating.value = false
    progress.value = 0
    progressStatus.value = 'exception'
    progressText.value = '备份失败'
    
    if (error && typeof error === 'object' && 'message' in error) {
      ElMessage.error(`创建备份失败: ${error.message}`)
    } else {
      ElMessage.error('创建备份失败')
    }
  }
}

// 处理取消
const handleCancel = () => {
  if (creating.value) {
    ElMessage.warning('备份正在进行中，无法取消')
    return
  }
  
  dialogVisible.value = false
}
</script>

<style scoped>
.progress-container {
  margin: 20px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.progress-text {
  margin-top: 12px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-progress-bar__outer) {
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
}
</style>
