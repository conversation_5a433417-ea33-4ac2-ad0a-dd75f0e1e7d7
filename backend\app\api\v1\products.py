"""
产品管理API
提供产品信息、产品类别和产品属性的CRUD操作
基于原项目API实现，确保与现有数据库结构100%兼容
"""
from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from sqlalchemy import or_, desc
from sqlalchemy.orm import selectinload
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
from io import BytesIO
from werkzeug.datastructures import FileStorage
from datetime import datetime
import os

from app.models.product import Product, ProductCategory, ProductAttribute, ProductSpecification, ProductImage
from app.models.brand import Brand
from app.schemas.product import (
    ProductSchema, 
    ProductSimpleSchema, 
    ProductCategorySchema, 
    ProductAttributeSchema, 
    ProductSpecificationSchema,
    ProductImageSchema,
    BrandSchema,
    BrandSimpleSchema
)
from app.utils.response import (
    success_response,
    error_response,
    paginated_response,
    validation_error_response,
    not_found_response
)
from app.utils.pagination import PaginationHelper
from app.utils.exceptions import ValidationError as CustomValidationError, NotFoundError
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('products', description='产品管理API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 自动从Marshmallow Schema生成API模型
product_input_model = create_input_model(api, ProductSchema, 'ProductInput')
product_output_model = create_output_model(api, ProductSchema, 'ProductOutput')

# 为了向后兼容，保留原来的product_model名称
product_model = product_input_model

# 自动生成其他模型
category_model = create_input_model(api, ProductCategorySchema, 'ProductCategory')

specification_model = create_input_model(api, ProductSpecificationSchema, 'ProductSpecification')
attribute_model = create_input_model(api, ProductAttributeSchema, 'ProductAttribute')
brand_model = create_input_model(api, BrandSchema, 'Brand')


@api.route('')
class ProductList(Resource):
    @api.doc('get_products')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('name', '产品名称搜索')
    @api.param('model', '产品型号搜索')
    @api.param('search', '综合搜索（同时搜索名称和型号）')
    @api.param('category_id', '产品分类ID', type='integer')
    @api.param('brand_id', '品牌ID', type='integer')
    @api.param('status', '产品状态')
    @api.param('with_specifications', '是否包含规格信息', type='boolean')
    @api.param('with_attributes', '是否包含属性信息', type='boolean')
    def get(self):
        """获取产品列表"""
        try:
            # 查询参数处理
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            name = request.args.get('name', '')
            model = request.args.get('model', '')
            search = request.args.get('search', '')  # 新增综合搜索参数
            category_id = request.args.get('category_id', type=int)
            brand_id = request.args.get('brand_id', type=int)
            status = request.args.get('status', '')
            with_specifications = request.args.get('with_specifications', '').lower() in ('true', '1', 'yes')
            with_attributes = request.args.get('with_attributes', '').lower() in ('true', '1', 'yes')

            # 构建查询
            query = Product.query

            if with_specifications:
                query = query.options(selectinload(Product.specifications))

            # 注意：attributes使用lazy='dynamic'，不能使用selectinload
            # 我们在序列化时手动加载属性数据

            # 应用过滤条件
            if search:
                # 综合搜索：同时搜索名称和型号（OR条件）
                query = query.filter(
                    db.or_(
                        Product.name.ilike(f'%{search}%'),
                        Product.model.ilike(f'%{search}%')
                    )
                )
            else:
                # 分别搜索名称和型号（AND条件）
                if name:
                    query = query.filter(Product.name.ilike(f'%{name}%'))
                if model:
                    query = query.filter(Product.model.ilike(f'%{model}%'))

            if category_id:
                query = query.filter(Product.category_id == category_id)
            if brand_id:
                query = query.filter(Product.brand_id == brand_id)
            if status:
                query = query.filter(Product.status == status)
            
            # 排序
            query = query.order_by(Product.created_at.desc())
            
            # 选择Schema
            if with_specifications:
                serializer_func = lambda item: ProductSchema().dump(item)
            else:
                serializer_func = lambda item: ProductSimpleSchema().dump(item)
            
            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query,
                serializer_func=serializer_func,
                page=page,
                per_page=per_page,
                message="获取产品列表成功"
            )
            
        except Exception as e:
            current_app.logger.error(f"获取产品列表失败: {str(e)}")
            return make_response(error_response, f"获取产品列表失败: {str(e)}")

    @api.doc('create_product',
             body=product_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建新产品"""
        try:
            data = request.get_json() or {}
            
            # 分离嵌套数据
            specifications_data = data.pop('specifications', [])
            attributes_data = data.pop('attributes', [])
            images_data = data.pop('images', [])
            files_to_delete = data.pop('files_to_delete', None)
            
            # 数据验证
            product_schema = ProductSchema(exclude=(
                'id', 'specifications', 'attributes', 'images', 'category', 'brand',
                'category_name', 'brand_name', 'price_range', 'main_image', 'created_at', 'updated_at'
            ))
            validated_data = product_schema.load(data)

            # 检查产品类别是否存在
            if not ProductCategory.query.get(validated_data['category_id']):
                return make_response(error_response, "产品类别不存在", errors={"category_id": "指定的产品类别不存在"})

            # 检查品牌是否存在（如果提供了brand_id）
            if validated_data.get('brand_id') and not Brand.query.get(validated_data['brand_id']):
                return make_response(error_response, "品牌不存在", errors={"brand_id": "指定的品牌不存在"})

            # 检查产品名称和型号组合是否唯一
            if Product.query.filter_by(name=validated_data['name'], model=validated_data['model']).first():
                return make_response(error_response, "产品已存在", errors={"name": "此产品名称和型号组合已存在"})

            # 验证规格：至少要有一个规格
            if not specifications_data or len(specifications_data) == 0:
                return make_response(error_response, "产品至少需要一个规格", errors={"specifications": "请至少添加一个产品规格"})

            # 创建产品对象
            product = Product(**validated_data)
            db.session.add(product)
            db.session.flush()  # 获取产品ID

            # 创建规格信息
            if specifications_data:
                spec_schema = ProductSpecificationSchema(many=True, exclude=('id', 'product_id'))
                validated_specs = spec_schema.load(specifications_data)
                
                for spec_data in validated_specs:
                    spec_data['product_id'] = product.id
                    specification = ProductSpecification(**spec_data)
                    db.session.add(specification)

            # 创建属性信息
            if attributes_data:
                attr_schema = ProductAttributeSchema(many=True, exclude=('id', 'product_id'))
                validated_attrs = attr_schema.load(attributes_data)
                
                for attr_data in validated_attrs:
                    attr_data['product_id'] = product.id
                    attribute = ProductAttribute(**attr_data)
                    db.session.add(attribute)

            # 创建图片信息
            if images_data:
                image_schema = ProductImageSchema(many=True, exclude=('id', 'product_id'))
                validated_images = image_schema.load(images_data)
                
                # 如果没有指定主图，则第一张图片默认为主图
                has_main = any(img.get('is_main', False) for img in validated_images)
                
                for i, img_data in enumerate(validated_images):
                    img_data['product_id'] = product.id
                    
                    # 第一张图设为主图（如果没有指定主图）
                    if not has_main and i == 0:
                        img_data['is_main'] = True
                        # 同时更新产品的image字段（向后兼容）
                        product.image = img_data['url']
                    
                    product_image = ProductImage(**img_data)
                    db.session.add(product_image)

            # 处理需要删除的临时文件
            if files_to_delete:
                current_app.logger.info(f"删除临时文件: {files_to_delete}")
                for file_url in files_to_delete:
                    delete_product_image_file(file_url)

            db.session.commit()

            # 返回创建的产品信息
            created_product = Product.query.get(product.id)
            response_data = ProductSchema().dump(created_product)
            return make_response(success_response, response_data, "产品创建成功", 201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建产品失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建产品失败: {str(e)}")


@api.route('/<int:product_id>')
class ProductDetail(Resource):
    @api.doc('get_product')
    def get(self, product_id):
        """获取产品详情"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            product_data = ProductSchema().dump(product)
            return make_response(success_response, product_data, "获取产品详情成功")

        except Exception as e:
            current_app.logger.error(f"获取产品详情失败: {str(e)}")
            return make_response(error_response, f"获取产品详情失败: {str(e)}")

    @api.doc('update_product',
             body=product_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Product Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, product_id):
        """更新产品信息"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            data = request.get_json() or {}

            # 分离规格、属性、图片数据和需要删除的文件
            specifications_data = data.pop('specifications', None)
            attributes_data = data.pop('attributes', None)
            images_data = data.pop('images', None)
            files_to_delete = data.pop('files_to_delete', None)

            # 验证并更新产品基本信息
            product_schema = ProductSchema(exclude=(
                'specifications', 'attributes', 'images', 'category', 'brand',
                'category_name', 'brand_name', 'price_range', 'main_image'
            ))
            validated_product_data = product_schema.load(data, partial=True)

            # 检查类别和品牌
            if 'category_id' in validated_product_data:
                if not ProductCategory.query.get(validated_product_data['category_id']):
                    return make_response(error_response, "产品类别不存在", errors={"category_id": "指定的产品类别不存在"})

            if 'brand_id' in validated_product_data and validated_product_data['brand_id']:
                if not Brand.query.get(validated_product_data['brand_id']):
                    return make_response(error_response, "品牌不存在", errors={"brand_id": "指定的品牌不存在"})

            # 检查唯一性约束
            if ('name' in validated_product_data or 'model' in validated_product_data):
                check_name = validated_product_data.get('name', product.name)
                check_model = validated_product_data.get('model', product.model)
                if (check_name != product.name or check_model != product.model):
                    if Product.query.filter(
                        Product.id != product_id,
                        Product.name == check_name,
                        Product.model == check_model
                    ).first():
                        return make_response(error_response, "产品已存在", errors={"name": "此产品名称和型号组合已存在"})

            # 更新基本信息
            for key, value in validated_product_data.items():
                setattr(product, key, value)

            # 处理规格信息
            if specifications_data is not None:
                # 验证规格：至少要有一个规格
                if len(specifications_data) == 0:
                    return make_response(error_response, "产品至少需要一个规格", errors={"specifications": "请至少添加一个产品规格"})

                spec_schema = ProductSpecificationSchema(many=True)
                validated_specs = spec_schema.load(specifications_data)

                current_spec_ids = {spec.id for spec in product.specifications}
                incoming_spec_ids = {spec_data.get('id') for spec_data in validated_specs if spec_data.get('id')}

                # 删除不再存在的规格（但要检查是否被引用）
                ids_to_delete = current_spec_ids - incoming_spec_ids
                if ids_to_delete:
                    # 检查哪些规格可以安全删除（没有被其他表引用）
                    from app.models.order import OrderProduct
                    from app.models.quotation import QuotationItem

                    safe_to_delete = []
                    cannot_delete = []

                    for spec_id in ids_to_delete:
                        # 检查是否被订单产品引用
                        order_refs = OrderProduct.query.filter_by(product_specification_id=spec_id).count()
                        # 检查是否被报价项引用
                        quotation_refs = QuotationItem.query.filter_by(product_specification_id=spec_id).count()

                        if order_refs == 0 and quotation_refs == 0:
                            safe_to_delete.append(spec_id)
                        else:
                            cannot_delete.append(spec_id)

                    # 只删除安全的规格
                    if safe_to_delete:
                        ProductSpecification.query.filter(
                            ProductSpecification.product_id == product_id,
                            ProductSpecification.id.in_(safe_to_delete)
                        ).delete(synchronize_session=False)

                    # 如果有不能删除的规格，记录警告但不阻止更新
                    if cannot_delete:
                        current_app.logger.warning(f"产品 {product_id} 的规格 {cannot_delete} 因被其他记录引用而无法删除")

                # 更新或添加规格
                for spec_data in validated_specs:
                    spec_id = spec_data.get('id')
                    if spec_id:
                        # 更新现有规格
                        spec_to_update = ProductSpecification.query.filter_by(id=spec_id, product_id=product_id).first()
                        if spec_to_update:
                            for k, v in spec_data.items():
                                if k != 'id':
                                    setattr(spec_to_update, k, v)
                    else:
                        # 添加新规格 - 检查是否已存在相同规格描述
                        specification_desc = spec_data.get('specification')
                        existing_spec = ProductSpecification.query.filter_by(
                            product_id=product_id,
                            specification=specification_desc
                        ).first()

                        if existing_spec:
                            # 如果已存在相同规格描述，更新现有规格而不是插入新的
                            for k, v in spec_data.items():
                                if k != 'id':
                                    setattr(existing_spec, k, v)
                        else:
                            # 真正的新规格
                            spec_data['product_id'] = product_id
                            new_spec = ProductSpecification(**spec_data)
                            db.session.add(new_spec)

            # 处理属性信息
            if attributes_data is not None:
                attr_schema = ProductAttributeSchema(many=True)
                validated_attrs = attr_schema.load(attributes_data)

                current_attr_ids = {attr.id for attr in product.attributes}
                incoming_attr_ids = {attr_data.get('id') for attr_data in validated_attrs if attr_data.get('id')}

                # 删除不再存在的属性
                ids_to_delete = current_attr_ids - incoming_attr_ids
                if ids_to_delete:
                    ProductAttribute.query.filter(
                        ProductAttribute.product_id == product_id,
                        ProductAttribute.id.in_(ids_to_delete)
                    ).delete(synchronize_session=False)

                # 更新或添加属性
                for attr_data in validated_attrs:
                    attr_id = attr_data.get('id')
                    if attr_id:
                        # 更新现有属性
                        attr_to_update = ProductAttribute.query.filter_by(id=attr_id, product_id=product_id).first()
                        if attr_to_update:
                            for k, v in attr_data.items():
                                if k != 'id' and k != 'product_id':
                                    setattr(attr_to_update, k, v)
                    else:
                        # 添加新属性
                        attr_data['product_id'] = product_id
                        new_attr = ProductAttribute(**attr_data)
                        db.session.add(new_attr)

            # 处理图片信息
            if images_data is not None:
                current_app.logger.info(f"处理图片信息，接收到的数据: {images_data}")
                image_schema = ProductImageSchema(many=True)
                validated_images = image_schema.load(images_data)

                # 获取当前图片URL集合和新图片URL集合
                current_image_urls = {img.url for img in product.images}
                incoming_image_urls = {img_data.get('url') for img_data in validated_images if img_data.get('url')}

                current_app.logger.info(f"当前图片URLs: {current_image_urls}")
                current_app.logger.info(f"新提交的图片URLs: {incoming_image_urls}")

                # 找出要删除的图片URL
                urls_to_delete = current_image_urls - incoming_image_urls
                current_app.logger.info(f"要删除的图片URLs: {urls_to_delete}")

                if urls_to_delete:
                    # 先获取要删除的图片记录，用于删除文件
                    images_to_delete = ProductImage.query.filter(
                        ProductImage.product_id == product_id,
                        ProductImage.url.in_(urls_to_delete)
                    ).all()

                    # 删除对应的图片文件
                    for img in images_to_delete:
                        current_app.logger.info(f"删除图片文件: {img.url}")
                        delete_product_image_file(img.url)

                    # 删除数据库记录
                    ProductImage.query.filter(
                        ProductImage.product_id == product_id,
                        ProductImage.url.in_(urls_to_delete)
                    ).delete(synchronize_session=False)

                # 检查是否有新的主图
                has_main_in_update = any(img_data.get('is_main', False) for img_data in validated_images)

                # 更新或添加图片
                for img_data in validated_images:
                    img_url = img_data.get('url')
                    if img_url:
                        # 检查是否是现有图片（基于URL）
                        existing_img = ProductImage.query.filter_by(url=img_url, product_id=product_id).first()
                        if existing_img:
                            # 更新现有图片
                            # 如果将此图片设为主图，则更新其他图片
                            if img_data.get('is_main', False) and not existing_img.is_main:
                                ProductImage.query.filter(
                                    ProductImage.product_id == product_id,
                                    ProductImage.url != img_url
                                ).update({'is_main': False})
                                # 更新产品的主图字段
                                product.image = img_url

                            # 更新字段
                            for k, v in img_data.items():
                                if k != 'product_id':
                                    setattr(existing_img, k, v)
                        else:
                            # 添加新图片
                            img_data['product_id'] = product_id

                            # 如果没有主图，且这是第一张新图，则设为主图
                            if not has_main_in_update and not ProductImage.query.filter_by(product_id=product_id, is_main=True).first():
                                img_data['is_main'] = True
                                product.image = img_data['url']

                            # 如果此图片被指定为主图，则更新其他图片
                            if img_data.get('is_main', False):
                                ProductImage.query.filter_by(product_id=product_id).update({'is_main': False})
                                product.image = img_data['url']

                            new_img = ProductImage(**img_data)
                            db.session.add(new_img)

                # 确保至少有一张主图
                if validated_images:
                    if not ProductImage.query.filter_by(product_id=product_id, is_main=True).first():
                        first_image = ProductImage.query.filter_by(product_id=product_id).order_by(ProductImage.id).first()
                        if first_image:
                            first_image.is_main = True
                            product.image = first_image.url

            # 处理需要删除的临时文件
            if files_to_delete:
                current_app.logger.info(f"删除临时文件: {files_to_delete}")
                for file_url in files_to_delete:
                    delete_product_image_file(file_url)

            db.session.commit()

            # 返回更新后的产品
            updated_data = ProductSchema().dump(product)
            return make_response(success_response, updated_data, "产品更新成功")

        except ValidationError as e:
            db.session.rollback()
            current_app.logger.error(f"产品更新验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新产品失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新产品失败: {str(e)}")

    @api.doc('delete_product')
    def delete(self, product_id):
        """删除产品"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            # 删除产品关联的图片文件
            for image in product.images:
                delete_product_image_file(image.url)

            db.session.delete(product)
            db.session.commit()
            return make_response(success_response, message="产品删除成功")

        except Exception as e:
            current_app.logger.error(f"删除产品失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除产品失败: {str(e)}")


# 产品分类相关API
@api.route('/categories')
class ProductCategoryList(Resource):
    @api.doc('get_product_categories')
    @api.param('parent_id', '父分类ID')
    @api.param('tree', '是否返回树状结构', type='boolean')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=10)
    def get(self):
        """获取产品分类列表（支持树状结构或按父ID查询）"""
        try:
            parent_id_str = request.args.get('parent_id')
            get_tree = request.args.get('tree', type=bool, default=False)
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)

            if get_tree:
                # 获取所有分类并构建树状结构
                all_categories = ProductCategory.query.order_by(ProductCategory.sort_order).all()

                # 构建ID到对象的映射
                category_map = {cat.id: cat for cat in all_categories}
                # 构建父ID到子列表的映射
                children_map = {}
                for cat in all_categories:
                    if cat.parent_id:
                        if cat.parent_id not in children_map:
                            children_map[cat.parent_id] = []
                        children_map[cat.parent_id].append(cat)

                # 构建树
                root_categories = []
                for cat in all_categories:
                    cat.children = children_map.get(cat.id, [])  # 附加子节点
                    if cat.parent_id is None or cat.parent_id == 0:
                        root_categories.append(cat)

                category_schema = ProductCategorySchema(many=True)
                categories_data = category_schema.dump(root_categories)
                response_data = {
                    "items": categories_data,
                    "page_info": None  # 不分页
                }
                return make_response(success_response, response_data, "获取产品分类树成功")

            else:
                # 按父ID查询（如果提供了parent_id）或分页查询顶层分类
                query = ProductCategory.query

                if parent_id_str is not None:
                    if parent_id_str.lower() == 'null' or parent_id_str == '0':
                        # 查询顶级分类
                        query = query.filter(or_(ProductCategory.parent_id.is_(None), ProductCategory.parent_id == 0))
                    else:
                        try:
                            parent_id = int(parent_id_str)
                            query = query.filter(ProductCategory.parent_id == parent_id)
                        except ValueError:
                            return make_response(error_response, "无效的parent_id", code=400)

                # 使用分页助手
                return make_response(
                    PaginationHelper.paginate_and_response,
                    query=query.order_by(ProductCategory.level, ProductCategory.sort_order, ProductCategory.name),
                    serializer_func=lambda item: ProductCategorySchema(exclude=('children',)).dump(item),
                    page=page,
                    per_page=per_page,
                    message="获取产品分类列表成功"
                )

        except Exception as e:
            current_app.logger.error(f"获取产品分类失败: {str(e)}")
            return make_response(error_response, f"获取产品分类失败: {str(e)}")

    @api.doc('create_product_category')
    def post(self):
        """创建新产品分类"""
        try:
            data = request.get_json() or {}
            category_schema = ProductCategorySchema(exclude=(
                'id', 'level', 'children', 'full_category_path', 'created_at', 'updated_at'
            ))

            validated_data = category_schema.load(data)

            # 检查名称是否重复（在同一父级下）
            existing = ProductCategory.query.filter_by(
                name=validated_data['name'],
                parent_id=validated_data.get('parent_id')
            ).first()
            if existing:
                return make_response(error_response, f"名称 '{validated_data['name']}' 在此级别下已存在", code=400)

            # 计算level
            parent_level = 0
            if validated_data.get('parent_id'):
                parent = ProductCategory.query.get(validated_data['parent_id'])
                if not parent:
                    return make_response(error_response, "父类别不存在", code=400)
                parent_level = parent.level

            validated_data['level'] = parent_level + 1

            category = ProductCategory(**validated_data)
            db.session.add(category)
            db.session.commit()

            # 返回完整数据
            return make_response(success_response, ProductCategorySchema().dump(category), "产品类别创建成功", 201)

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建产品类别失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建产品类别失败: {str(e)}")


@api.route('/categories/<int:category_id>')
class ProductCategoryDetail(Resource):
    @api.doc('get_product_category')
    def get(self, category_id):
        """获取单个产品分类详情"""
        try:
            category = ProductCategory.query.get(category_id)
            if not category:
                return make_response(not_found_response, "产品分类不存在")

            category_data = ProductCategorySchema().dump(category)
            return make_response(success_response, category_data, "获取产品类别详情成功")

        except Exception as e:
            current_app.logger.error(f"获取产品分类详情失败: {str(e)}")
            return make_response(error_response, f"获取产品分类详情失败: {str(e)}")

    @api.doc('update_product_category')
    def put(self, category_id):
        """更新产品分类"""
        try:
            category = ProductCategory.query.get(category_id)
            if not category:
                return make_response(not_found_response, "产品分类不存在")

            data = request.get_json() or {}
            category_schema = ProductCategorySchema(exclude=(
                'id', 'level', 'children', 'full_category_path', 'created_at', 'updated_at'
            ))

            validated_data = category_schema.load(data, partial=True)

            # 检查名称重复（如果名称或父ID改变了）
            new_name = validated_data.get('name', category.name)
            new_parent_id = validated_data.get('parent_id', category.parent_id)
            if new_name != category.name or new_parent_id != category.parent_id:
                existing = ProductCategory.query.filter(
                    ProductCategory.id != category_id,  # 排除自身
                    ProductCategory.name == new_name,
                    ProductCategory.parent_id == new_parent_id
                ).first()
                if existing:
                    return make_response(error_response, f"名称 '{new_name}' 在此级别下已存在", code=400)

            # 检查是否将父级设置为自身或子孙
            if 'parent_id' in validated_data:
                if validated_data['parent_id'] == category_id:
                    return make_response(error_response, "不能将父类别设置为自身", code=400)

                # 检查循环依赖
                parent_id_to_check = validated_data['parent_id']
                while parent_id_to_check is not None:
                    if parent_id_to_check == category_id:
                        return make_response(error_response, "不能将父类别设置为自身的子类别", code=400)
                    parent_check = ProductCategory.query.get(parent_id_to_check)
                    if not parent_check:
                        break
                    parent_id_to_check = parent_check.parent_id

            # 更新level（如果parent_id改变）
            if 'parent_id' in validated_data and validated_data['parent_id'] != category.parent_id:
                parent_level = 0
                if validated_data['parent_id']:
                    parent = ProductCategory.query.get(validated_data['parent_id'])
                    if not parent:
                        return make_response(error_response, "父类别不存在", code=400)
                    parent_level = parent.level
                validated_data['level'] = parent_level + 1

            # 更新字段
            for key, value in validated_data.items():
                setattr(category, key, value)

            db.session.commit()

            return make_response(success_response, ProductCategorySchema().dump(category), "产品类别更新成功")

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新产品类别失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新产品类别失败: {str(e)}")

    @api.doc('delete_product_category')
    def delete(self, category_id):
        """删除产品分类"""
        try:
            category = ProductCategory.query.get(category_id)
            if not category:
                return make_response(not_found_response, "产品分类不存在")

            # 检查是否有子类别
            if ProductCategory.query.filter_by(parent_id=category_id).first():
                return make_response(error_response, "请先删除子类别", code=400)

            # 检查是否有产品关联
            if Product.query.filter_by(category_id=category_id).first():
                return make_response(error_response, "该类别下有产品，无法删除", code=400)

            db.session.delete(category)
            db.session.commit()
            return make_response(success_response, message="产品类别删除成功")

        except Exception as e:
            current_app.logger.error(f"删除产品类别失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除产品类别失败: {str(e)}")


# 品牌管理相关API
@api.route('/brands')
class BrandList(Resource):
    @api.doc('get_brands')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=10)
    @api.param('name', '品牌名称搜索')
    def get(self):
        """获取品牌列表（支持分页）"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)
            name = request.args.get('name')

            query = Brand.query

            if name:
                query = query.filter(Brand.name.ilike(f'%{name}%'))

            # 根据per_page判断是否返回简化版Schema（例如，获取所有品牌用于下拉）
            if per_page == 999:  # 假设999表示获取全部
                serializer_func = lambda item: BrandSimpleSchema().dump(item)
            else:
                serializer_func = lambda item: BrandSchema().dump(item)

            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query.order_by(Brand.sort_order, Brand.name),
                serializer_func=serializer_func,
                page=page,
                per_page=per_page,
                message="获取品牌列表成功"
            )

        except Exception as e:
            current_app.logger.error(f"获取品牌列表失败: {str(e)}")
            return make_response(error_response, f"获取品牌列表失败: {str(e)}")

    @api.doc('create_brand')
    def post(self):
        """创建新品牌"""
        try:
            data = request.get_json() or {}
            brand_schema = BrandSchema(exclude=('id', 'created_at', 'updated_at'))

            validated_data = brand_schema.load(data)

            # 检查品牌名称是否已存在
            if Brand.query.filter_by(name=validated_data['name']).first():
                return make_response(error_response, "品牌名称已存在", code=400)

            brand = Brand(**validated_data)
            db.session.add(brand)
            db.session.commit()

            return make_response(success_response, BrandSchema().dump(brand), "品牌创建成功", 201)

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建品牌失败: {str(e)}")
            return make_response(error_response, "创建品牌失败", code=500)


@api.route('/brands/<int:brand_id>')
class BrandDetail(Resource):
    @api.doc('get_brand')
    def get(self, brand_id):
        """获取单个品牌详情"""
        try:
            brand = Brand.query.get(brand_id)
            if not brand:
                return make_response(not_found_response, "品牌不存在")

            brand_data = BrandSchema().dump(brand)
            return make_response(success_response, brand_data, "获取品牌详情成功")

        except Exception as e:
            current_app.logger.error(f"获取品牌详情失败: {str(e)}")
            return make_response(error_response, f"获取品牌详情失败: {str(e)}")

    @api.doc('update_brand')
    def put(self, brand_id):
        """更新品牌信息"""
        try:
            brand = Brand.query.get(brand_id)
            if not brand:
                return make_response(not_found_response, "品牌不存在")

            data = request.get_json() or {}
            brand_schema = BrandSchema(exclude=('id', 'created_at', 'updated_at'))

            validated_data = brand_schema.load(data, partial=True)

            # 检查如果名称被修改，新名称是否已存在
            if 'name' in validated_data and validated_data['name'] != brand.name:
                if Brand.query.filter(Brand.id != brand_id, Brand.name == validated_data['name']).first():
                    return make_response(error_response, "品牌名称已存在", code=400)

            # 更新属性
            for key, value in validated_data.items():
                setattr(brand, key, value)

            db.session.commit()
            return make_response(success_response, BrandSchema().dump(brand), "品牌更新成功")

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新品牌失败: {str(e)}")
            return make_response(error_response, "更新品牌失败", code=500)

    @api.doc('delete_brand')
    def delete(self, brand_id):
        """删除品牌"""
        try:
            brand = Brand.query.get(brand_id)
            if not brand:
                return make_response(not_found_response, "品牌不存在")

            # 检查是否有产品关联
            if Product.query.filter_by(brand_id=brand_id).first():
                return make_response(error_response, "无法删除，该品牌下有关联的产品", code=400)

            db.session.delete(brand)
            db.session.commit()
            return make_response(success_response, message="品牌删除成功")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除品牌失败: {str(e)}")
            return make_response(error_response, "删除品牌失败", code=500)


# 产品属性管理相关API
@api.route('/<int:product_id>/attributes')
class ProductAttributeList(Resource):
    @api.doc('get_product_attributes')
    def get(self, product_id):
        """获取产品属性列表"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            attributes = ProductAttribute.query.filter_by(product_id=product_id).order_by(ProductAttribute.sort_order).all()
            attributes_data = ProductAttributeSchema(many=True).dump(attributes)

            return make_response(success_response, attributes_data, "获取产品属性列表成功")

        except Exception as e:
            current_app.logger.error(f"获取产品属性列表失败: {str(e)}")
            return make_response(error_response, f"获取产品属性列表失败: {str(e)}")

    @api.doc('create_product_attribute')
    def post(self, product_id):
        """创建产品属性"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            data = request.get_json() or {}
            data['product_id'] = product_id

            attribute_schema = ProductAttributeSchema(exclude=('id', 'created_at', 'updated_at'))
            validated_data = attribute_schema.load(data)

            # 检查属性名称是否已存在
            existing = ProductAttribute.query.filter_by(
                product_id=product_id,
                attribute_name=validated_data['attribute_name']
            ).first()
            if existing:
                return make_response(error_response, "该产品已存在相同名称的属性", code=400)

            attribute = ProductAttribute(**validated_data)
            db.session.add(attribute)
            db.session.commit()

            return make_response(success_response, ProductAttributeSchema().dump(attribute), "产品属性创建成功", 201)

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建产品属性失败: {str(e)}")
            return make_response(error_response, f"创建产品属性失败: {str(e)}")


@api.route('/attributes/<int:attribute_id>')
class ProductAttributeDetail(Resource):
    @api.doc('update_product_attribute')
    def put(self, attribute_id):
        """更新产品属性"""
        try:
            attribute = ProductAttribute.query.get(attribute_id)
            if not attribute:
                return make_response(not_found_response, "产品属性不存在")

            data = request.get_json() or {}
            attribute_schema = ProductAttributeSchema(exclude=('id', 'product_id', 'created_at', 'updated_at'))
            validated_data = attribute_schema.load(data, partial=True)

            # 检查属性名称是否已存在（如果名称被修改）
            if 'attribute_name' in validated_data and validated_data['attribute_name'] != attribute.attribute_name:
                existing = ProductAttribute.query.filter(
                    ProductAttribute.id != attribute_id,
                    ProductAttribute.product_id == attribute.product_id,
                    ProductAttribute.attribute_name == validated_data['attribute_name']
                ).first()
                if existing:
                    return make_response(error_response, "该产品已存在相同名称的属性", code=400)

            # 更新属性
            for key, value in validated_data.items():
                setattr(attribute, key, value)

            db.session.commit()
            return make_response(success_response, ProductAttributeSchema().dump(attribute), "产品属性更新成功")

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新产品属性失败: {str(e)}")
            return make_response(error_response, f"更新产品属性失败: {str(e)}")

    @api.doc('delete_product_attribute')
    def delete(self, attribute_id):
        """删除产品属性"""
        try:
            attribute = ProductAttribute.query.get(attribute_id)
            if not attribute:
                return make_response(not_found_response, "产品属性不存在")

            db.session.delete(attribute)
            db.session.commit()
            return make_response(success_response, message="产品属性删除成功")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除产品属性失败: {str(e)}")
            return make_response(error_response, f"删除产品属性失败: {str(e)}")


# 产品导入导出相关API
@api.route('/export')
class ProductExport(Resource):
    @api.doc('export_products')
    @api.param('name', '产品名称搜索')
    @api.param('model', '产品型号搜索')
    @api.param('category_id', '产品分类ID', type='integer')
    @api.param('brand_id', '品牌ID', type='integer')
    @api.param('status', '产品状态')
    def get(self):
        """导出产品信息到Excel文件"""
        try:
            # 获取查询参数
            name = request.args.get('name', '')
            model = request.args.get('model', '')
            category_id = request.args.get('category_id', type=int)
            brand_id = request.args.get('brand_id', type=int)
            status = request.args.get('status', '')

            # 构建查询
            query = Product.query.options(
                selectinload(Product.category),
                selectinload(Product.brand),
                selectinload(Product.specifications)
            )

            if name:
                query = query.filter(Product.name.ilike(f'%{name}%'))
            if model:
                query = query.filter(Product.model.ilike(f'%{model}%'))
            if category_id:
                query = query.filter(Product.category_id == category_id)
            if brand_id:
                query = query.filter(Product.brand_id == brand_id)
            if status:
                query = query.filter(Product.status == status)

            products = query.order_by(Product.created_at.desc()).all()

            if not products:
                return make_response(error_response, "没有符合条件的产品可导出", code=404)

            # 准备数据
            export_data = []
            for product in products:
                product_data = {
                    '产品编号': product.id,
                    '产品名称': product.name,
                    '产品型号': product.model,
                    '产品分类': product.category.name if product.category else '',
                    '品牌': product.brand.name if product.brand else '',
                    '单位': product.unit,
                    '状态': product.status,
                    '描述': product.description or '',
                    '创建时间': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else '',
                    '更新时间': product.updated_at.strftime('%Y-%m-%d %H:%M:%S') if product.updated_at else ''
                }

                # 添加规格信息
                if product.specifications:
                    for i, spec in enumerate(product.specifications):
                        if i == 0:
                            product_data['规格'] = spec.specification
                            product_data['成本价'] = float(spec.cost_price) if spec.cost_price else 0
                            product_data['建议价'] = float(spec.suggested_price) if spec.suggested_price else 0
                            product_data['税率'] = float(spec.tax_rate) if spec.tax_rate else 0
                        else:
                            # 如果有多个规格，创建新行
                            new_row = product_data.copy()
                            new_row['规格'] = spec.specification
                            new_row['成本价'] = float(spec.cost_price) if spec.cost_price else 0
                            new_row['建议价'] = float(spec.suggested_price) if spec.suggested_price else 0
                            new_row['税率'] = float(spec.tax_rate) if spec.tax_rate else 0
                            export_data.append(new_row)
                else:
                    product_data['规格'] = ''
                    product_data['成本价'] = 0
                    product_data['建议价'] = 0
                    product_data['税率'] = 0

                export_data.append(product_data)

            df = pd.DataFrame(export_data)

            # 创建Excel文件到内存
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='产品列表')
            output.seek(0)

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'产品列表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            )

        except Exception as e:
            current_app.logger.error(f"导出产品失败: {str(e)}")
            return make_response(error_response, f"导出产品失败: {str(e)}")


@api.route('/export-template')
class ProductExportTemplate(Resource):
    @api.doc('get_product_export_template')
    def get(self):
        """获取产品导入模板"""
        try:
            # 创建模板数据
            template_data = {
                '产品名称': ['示例产品1', '示例产品2'],
                '产品型号': ['MODEL001', 'MODEL002'],
                '产品分类ID': [1, 1],
                '品牌ID': [1, 1],
                '单位': ['个', '套'],
                '状态': ['正常', '正常'],
                '描述': ['这是示例产品1的描述', '这是示例产品2的描述'],
                '规格': ['标准规格', '高级规格'],
                '成本价': [80.00, 120.00],
                '建议价': [100.00, 150.00],
                '税率': [13.0, 13.0]
            }

            df = pd.DataFrame(template_data)

            # 创建Excel文件到内存
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='产品导入模板')

                # 添加说明工作表
                instructions = pd.DataFrame({
                    '字段说明': [
                        '产品名称：必填，产品的名称',
                        '产品型号：必填，产品的型号',
                        '产品分类ID：必填，产品分类的ID，请先查询分类列表获取',
                        '品牌ID：可选，品牌的ID，请先查询品牌列表获取',
                        '单位：必填，产品的计量单位',
                        '状态：可选，产品状态（正常/停用），默认为正常',
                        '描述：可选，产品的详细描述',
                        '规格：可选，产品规格描述',
                        '成本价：可选，产品成本价格',
                        '建议价：可选，产品建议销售价格',
                        '税率：可选，产品税率（百分比）'
                    ]
                })
                instructions.to_excel(writer, index=False, sheet_name='字段说明')

            output.seek(0)

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'产品导入模板_{datetime.now().strftime("%Y%m%d")}.xlsx'
            )

        except Exception as e:
            current_app.logger.error(f"获取产品导入模板失败: {str(e)}")
            return make_response(error_response, f"获取产品导入模板失败: {str(e)}")


@api.route('/batch-delete')
class ProductBatchDelete(Resource):
    @api.doc('batch_delete_products')
    def post(self):
        """批量删除产品"""
        try:
            data = request.get_json() or {}
            ids = data.get('ids')

            if not ids or not isinstance(ids, list):
                return make_response(error_response, "请求参数错误，需要提供产品ID列表", code=400)

            # 删除指定ID的产品
            num_deleted = Product.query.filter(Product.id.in_(ids)).delete(synchronize_session=False)
            db.session.commit()

            return make_response(success_response, message=f"成功删除了 {num_deleted} 个产品")

        except Exception as e:
            current_app.logger.error(f"批量删除产品失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"批量删除产品失败: {str(e)}")


@api.route('/batch-export')
class ProductBatchExport(Resource):
    @api.doc('batch_export_products')
    def post(self):
        """批量导出产品信息到Excel文件"""
        try:
            data = request.get_json() or {}
            ids = data.get('ids')

            if not ids or not isinstance(ids, list):
                return make_response(error_response, "请求参数错误，需要提供产品ID列表", code=400)

            # 查询指定ID的产品
            products = Product.query.options(
                selectinload(Product.category),
                selectinload(Product.brand),
                selectinload(Product.specifications)
            ).filter(Product.id.in_(ids)).order_by(Product.created_at.desc()).all()

            if not products:
                return make_response(error_response, "没有找到指定ID的产品", code=404)

            # 准备导出数据
            export_data = []
            for product in products:
                if product.specifications:
                    # 有规格的产品，每个规格一行
                    for spec in product.specifications:
                        product_data = {
                            '产品编号': product.id,
                            '产品名称': product.name,
                            '产品型号': product.model or '',
                            '单位': product.unit or '',
                            '产品分类': product.category.name if product.category else '',
                            '品牌': product.brand.name if product.brand else '',
                            '规格名称': spec.specification or '',
                            '成本价': spec.cost_price or 0,
                            '最低售价': spec.min_price or 0,
                            '最高售价': spec.max_price or 0,
                            '建议售价': spec.suggested_price or 0,
                            '税率': spec.tax_rate or 0,
                            '状态': product.status,
                            '描述': product.description or '',
                            '创建时间': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else ''
                        }
                        export_data.append(product_data)
                else:
                    # 没有规格的产品
                    product_data = {
                        '产品编号': product.id,
                        '产品名称': product.name,
                        '产品型号': product.model or '',
                        '单位': product.unit or '',
                        '产品分类': product.category.name if product.category else '',
                        '品牌': product.brand.name if product.brand else '',
                        '规格名称': '',
                        '成本价': 0,
                        '最低售价': 0,
                        '最高售价': 0,
                        '建议售价': 0,
                        '税率': 0,
                        '状态': product.status,
                        '描述': product.description or '',
                        '创建时间': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else ''
                    }
                    export_data.append(product_data)

            df = pd.DataFrame(export_data)

            # 创建Excel文件到内存
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='产品列表')
            output.seek(0)

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'选中产品_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            )

        except Exception as e:
            current_app.logger.error(f"批量导出产品失败: {str(e)}")
            return make_response(error_response, f"批量导出产品失败: {str(e)}")


@api.route('/upload-image')
class ProductImageUpload(Resource):
    @api.doc('upload_product_image')
    def post(self):
        """上传产品图片"""
        try:
            if 'image' not in request.files:
                return make_response(error_response, "没有上传文件", code=400)

            file = request.files['image']
            if file.filename == '':
                return make_response(error_response, "没有选择文件", code=400)

            # 检查文件类型
            allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
            if not ('.' in file.filename and
                    file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
                return make_response(error_response, "不支持的文件格式，请上传png、jpg、jpeg或gif格式的图片", code=400)

            # 检查文件大小（10MB）
            file.seek(0, 2)  # 移动到文件末尾
            file_size = file.tell()
            file.seek(0)  # 重置到文件开头

            if file_size > 10 * 1024 * 1024:  # 10MB
                return make_response(error_response, "文件大小不能超过10MB", code=400)

            # 创建上传目录
            import os
            upload_dir = os.path.join(current_app.root_path, '..', 'uploads', 'products')
            os.makedirs(upload_dir, exist_ok=True)

            # 生成唯一文件名
            import uuid
            file_extension = file.filename.rsplit('.', 1)[1].lower()
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
            file_path = os.path.join(upload_dir, unique_filename)

            # 保存文件
            file.save(file_path)

            # 生成访问URL (匹配前端API路径)
            file_url = f"/uploads/products/{unique_filename}"

            return make_response(success_response, {
                "url": file_url,
                "filename": unique_filename,
                "original_name": file.filename
            }, "图片上传成功")

        except Exception as e:
            current_app.logger.error(f"上传产品图片失败: {str(e)}")
            return make_response(error_response, f"上传产品图片失败: {str(e)}")


def delete_product_image_file(image_url):
    """删除产品图片文件"""
    try:
        if not image_url or not image_url.startswith('/uploads/products/'):
            return False

        # 提取文件名
        filename = image_url.replace('/uploads/products/', '')
        if not filename:
            return False

        # 构建文件路径
        upload_dir = os.path.join(current_app.root_path, '..', 'uploads', 'products')
        file_path = os.path.join(upload_dir, filename)

        # 安全检查：确保文件在指定目录内
        if not os.path.abspath(file_path).startswith(os.path.abspath(upload_dir)):
            current_app.logger.warning(f"尝试删除不安全的文件路径: {file_path}")
            return False

        # 删除文件
        if os.path.exists(file_path):
            os.remove(file_path)
            current_app.logger.info(f"成功删除图片文件: {file_path}")
            return True
        else:
            current_app.logger.warning(f"图片文件不存在: {file_path}")
            return False

    except Exception as e:
        current_app.logger.error(f"删除图片文件失败: {str(e)}")
        return False


@api.route('/preview-import')
class ProductPreviewImport(Resource):
    @api.doc('preview_product_import')
    def post(self):
        """预览产品导入数据 - 智能Excel处理"""
        try:
            if 'file' not in request.files:
                return make_response(error_response, "请选择要导入的文件", code=400)

            file = request.files['file']
            if file.filename == '':
                return make_response(error_response, "请选择要导入的文件", code=400)

            if not file.filename.lower().endswith(('.xlsx', '.xls')):
                return make_response(error_response, "请上传Excel文件", code=400)

            # 使用智能Excel处理
            return preview_import_from_excel(file)

        except Exception as e:
            current_app.logger.error(f"预览导入失败: {str(e)}")
            return make_response(error_response, f"预览导入失败: {str(e)}", code=500)


def preview_import_from_excel(file):
    """智能预览Excel文件内容，支持复杂格式和合并单元格"""
    try:
        import pandas as pd
        import numpy as np
        from openpyxl import load_workbook
        from io import BytesIO

        # 记录详细日志
        current_app.logger.info(f"开始预览Excel文件: {file.filename}")

        # 检查文件大小
        file.seek(0, 2)
        file_size = file.tell()
        file.seek(0)

        if file_size == 0:
            return make_response(error_response, "文件为空", code=400)

        if file_size > 10 * 1024 * 1024:  # 10MB限制
            return make_response(error_response, "文件过大，请控制在10MB以内", code=400)

        # 使用openpyxl读取合并单元格信息
        file_content = file.read()
        file.seek(0)

        try:
            workbook = load_workbook(BytesIO(file_content), data_only=True)
            worksheet = workbook.active
            merged_ranges = worksheet.merged_cells.ranges
        except Exception as e:
            current_app.logger.warning(f"无法读取合并单元格信息: {str(e)}")
            merged_ranges = []

        # 使用pandas读取数据
        try:
            df = pd.read_excel(BytesIO(file_content))
        except Exception as e:
            return make_response(error_response, f"读取Excel文件失败: {str(e)}", code=400)

        if df.empty:
            return make_response(error_response, "Excel文件为空", code=400)

        current_app.logger.info(f"预览Excel文件内容前5行：\n{df.head(5)}")

        # 不在这里处理表头，让process_preview_data函数来处理
        # 这样可以保持一致的处理逻辑

        return process_preview_data(df, merged_ranges)

    except Exception as e:
        current_app.logger.error(f"预览Excel处理失败: {str(e)}")
        import traceback
        error_trace = traceback.format_exc()
        current_app.logger.error(f"详细错误信息: {error_trace}")
        return make_response(error_response, f"预览Excel失败: {str(e)}", code=500)


def find_header_row(df):
    """查找真正的表头行"""
    try:
        # 关键词列表，用于识别表头
        header_keywords = ['产品名称', '产品型号', '型号', '名称', '规格', '单位', '价格']

        for i in range(min(5, len(df))):  # 只检查前5行
            row_values = [str(val).strip() for val in df.iloc[i].values if pd.notna(val)]
            # 检查这一行是否包含足够多的关键词
            keyword_count = sum(1 for val in row_values for keyword in header_keywords if keyword in val)
            if keyword_count >= 2:  # 至少包含2个关键词
                return i
        return None
    except Exception:
        return None


def fill_merged_cells(df):
    """处理合并单元格，向下填充空值"""
    try:
        import pandas as pd

        # 对每一列进行前向填充，但只填充连续的空值
        for col in df.columns:
            if col in df.columns:
                # 使用新的pandas语法进行前向填充
                df[col] = df[col].ffill(limit=20)

        return df
    except Exception as e:
        current_app.logger.warning(f"填充合并单元格失败: {e}")
        return df


def process_preview_data(df, merged_ranges):
    """处理预览数据，智能识别产品信息"""
    try:
        import pandas as pd
        import numpy as np

        # 首先尝试找到真正的表头行
        header_row = find_header_row(df)
        if header_row is not None and header_row > 0:
            # 使用找到的表头行重新读取数据
            new_columns = df.iloc[header_row].values
            # 处理空的列名
            processed_columns = []
            for i, col in enumerate(new_columns):
                if pd.isna(col) or str(col).strip() == '':
                    processed_columns.append(f'Unnamed: {i}')
                else:
                    processed_columns.append(str(col).strip())

            df.columns = processed_columns
            df = df.iloc[header_row + 1:].reset_index(drop=True)

        # 处理合并单元格，向下填充空值
        df = fill_merged_cells(df)

        # 扩展的列名映射字典 - 按优先级排序
        column_mappings = {
            'product_name': ['产品名称', 'name', 'product_name', 'productname', '名称',
                           'product', '商品名称', '货品名称', '物品名称'],
            'model': ['产品型号', 'model', 'product_model', 'productmodel', '型号',
                     'model_number', '规格型号', '产品编号', '编号', 'code'],
            'unit': ['单位', 'unit', 'measure_unit', 'uom', '计量单位', '包装单位'],
            'category_name': ['产品分类', 'category', 'product_category', '分类', '类别',
                            'category_name', '产品类别', '商品分类'],
            'specification': ['规格', 'specification', 'spec', '产品规格', '技术规格',
                            '规格参数', '参数'],
            'cost_price': ['成本价', 'cost_price', 'cost', '采购价', '进价', '成本'],
            'suggested_price': ['建议价', 'suggested_price', 'list_price', '市场价',
                              '标准价', '零售价', '售价', '单价'],
            'tax_rate': ['税率', 'tax_rate', 'tax', '增值税率', 'vat_rate'],
            'description': ['描述', 'description', 'desc', '说明', '备注', 'remark',
                          'note', '产品描述', '详细描述'],
            'status': ['状态', 'status', 'product_status', '产品状态', '商品状态']
        }

        # 智能列映射 - 优先精确匹配
        mapped_columns = {}
        df_columns = [str(col).strip() for col in df.columns]

        for field, possible_names in column_mappings.items():
            # 首先尝试精确匹配
            for possible_name in possible_names:
                for col in df_columns:
                    if col == possible_name:
                        mapped_columns[field] = col
                        break
                if field in mapped_columns:
                    break

            # 如果精确匹配失败，尝试包含匹配
            if field not in mapped_columns:
                for possible_name in possible_names:
                    for col in df_columns:
                        if possible_name in col:
                            mapped_columns[field] = col
                            break
                    if field in mapped_columns:
                        break

        current_app.logger.info(f"列映射结果: {mapped_columns}")

        # 处理数据行
        preview_data = []
        errors = []

        for index, row in df.iterrows():
            try:
                # 跳过空行
                if row.isna().all():
                    continue

                # 提取基础产品信息
                product_data = {
                    'name': str(row.get(mapped_columns.get('product_name', ''), '')).strip(),
                    'model': str(row.get(mapped_columns.get('model', ''), '')).strip(),
                    'unit': str(row.get(mapped_columns.get('unit', ''), '个')).strip(),
                    'category_name': str(row.get(mapped_columns.get('category_name', ''), '')).strip(),
                    'description': str(row.get(mapped_columns.get('description', ''), '')).strip(),
                    'status': str(row.get(mapped_columns.get('status', ''), '正常')).strip(),
                    'specifications': [],
                    'attributes': {},
                    'errors': []
                }

                # 处理规格信息
                if mapped_columns.get('specification'):
                    spec_value = str(row.get(mapped_columns['specification'], '')).strip()
                    if spec_value:
                        spec_data = {
                            'specification': spec_value,
                            'cost_price': float(row.get(mapped_columns.get('cost_price', ''), 0)) or 0,
                            'suggested_price': float(row.get(mapped_columns.get('suggested_price', ''), 0)) or 0,
                            'tax_rate': float(row.get(mapped_columns.get('tax_rate', ''), 13)) or 13
                        }
                        product_data['specifications'].append(spec_data)

                # 如果没有规格，创建默认规格
                if not product_data['specifications']:
                    product_data['specifications'].append({
                        'specification': '默认',
                        'cost_price': 0,
                        'suggested_price': 0,
                        'tax_rate': 13
                    })

                # 处理其他属性
                for col in df_columns:
                    if col not in mapped_columns.values() and pd.notna(row[col]):
                        value = str(row[col]).strip()
                        if value:
                            product_data['attributes'][col] = value

                # 基础验证
                if not product_data['name']:
                    product_data['errors'].append('产品名称不能为空')
                if not product_data['model']:
                    product_data['errors'].append('产品型号不能为空')

                preview_data.append(product_data)

            except Exception as e:
                errors.append(f"第{index + 2}行处理失败: {str(e)}")
                continue

        # 获取产品分类列表
        categories = ProductCategory.query.all()
        category_list = []
        for cat in categories:
            category_list.append({
                'id': cat.id,
                'name': cat.name,
                'level': cat.level or 1,
                'parent_id': cat.parent_id
            })

        # 智能匹配分类
        for product in preview_data:
            if product['category_name']:
                # 查找匹配的分类
                matched_category = None
                for cat in category_list:
                    if cat['name'] == product['category_name'] or product['category_name'] in cat['name']:
                        matched_category = cat
                        break

                if matched_category:
                    product['category_id'] = matched_category['id']
                else:
                    product['category_id'] = -1  # 标记为需要创建新分类

        result = {
            'preview_data': preview_data,
            'categories': category_list,
            'mappings': mapped_columns,
            'errors': errors,
            'total_count': len(preview_data),
            'debug_info': {
                'detected_columns': df_columns,
                'mapped_columns': mapped_columns,
                'specs_count': sum(len(p['specifications']) for p in preview_data)
            }
        }

        current_app.logger.info(f"预览结果: 检测到 {len(preview_data)} 个产品")

        return make_response(success_response, result, "Excel文件解析成功，请确认导入数据")

    except Exception as e:
        current_app.logger.error(f"处理预览数据失败: {str(e)}")
        return make_response(error_response, f"处理预览数据失败: {str(e)}", code=500)


@api.route('/import')
class ProductImport(Resource):
    @api.doc('import_products')
    def post(self):
        """导入产品数据 - 支持Excel文件和JSON预览数据"""
        try:
            # 检查请求类型
            content_type = request.headers.get('Content-Type', '')

            # 情况1: 直接上传Excel文件
            if 'multipart/form-data' in content_type and 'file' in request.files:
                file = request.files['file']
                if file.filename == '':
                    return make_response(error_response, "请选择要导入的文件", code=400)

                if not file.filename.lower().endswith(('.xlsx', '.xls')):
                    return make_response(error_response, "请上传Excel文件", code=400)

                # 使用原有的Excel导入逻辑
                return import_from_excel_direct(file)

            # 情况2: 从预览数据导入
            elif 'application/json' in content_type:
                return import_from_preview_data()

            else:
                return make_response(error_response, "不支持的请求格式", code=400)

        except Exception as e:
            current_app.logger.error(f"产品导入失败: {str(e)}")
            return make_response(error_response, f"产品导入失败: {str(e)}", code=500)


def import_from_preview_data():
    """从预览数据导入产品"""
    try:
        data = request.json
        if not data or 'products' not in data:
            return make_response(error_response, "缺少产品数据", code=400)

        products_data = data.get('products', [])
        if not products_data:
            return make_response(error_response, "产品数据为空", code=400)

        imported_count = 0
        failed_count = 0
        warnings = []
        errors = []

        # 确保有默认分类
        default_category = ProductCategory.query.filter_by(name='默认类别').first()
        if not default_category:
            default_category = ProductCategory(name='默认类别', level=1)
            db.session.add(default_category)
            db.session.flush()

        for product_data in products_data:
            try:
                # 验证必填字段
                if not product_data.get('name') or not product_data.get('model'):
                    errors.append(f"产品 '{product_data.get('name', '未知')}' 缺少必填字段")
                    failed_count += 1
                    continue

                # 处理分类
                category_id = product_data.get('category_id')
                if category_id == -1:  # 需要创建新分类
                    category_name = product_data.get('category_name', '默认类别')
                    category = ProductCategory.query.filter_by(name=category_name).first()
                    if not category:
                        category = ProductCategory(name=category_name, level=1)
                        db.session.add(category)
                        db.session.flush()
                    category_id = category.id
                elif not category_id:
                    category_id = default_category.id

                # 检查产品是否已存在
                existing_product = Product.query.filter_by(
                    name=product_data['name'],
                    model=product_data['model']
                ).first()

                if existing_product:
                    warnings.append(f"产品 '{product_data['name']}' 已存在，跳过导入")
                    continue

                # 创建产品
                product = Product(
                    name=product_data['name'],
                    model=product_data['model'],
                    unit=product_data.get('unit', '个'),
                    category_id=category_id,
                    description=product_data.get('description'),
                    status=product_data.get('status', '正常')
                )

                db.session.add(product)
                db.session.flush()

                # 创建产品规格
                specifications = product_data.get('specifications', [])
                for spec_data in specifications:
                    if spec_data.get('specification'):
                        spec = ProductSpecification(
                            product_id=product.id,
                            specification=spec_data['specification'],
                            cost_price=spec_data.get('cost_price', 0),
                            suggested_price=spec_data.get('suggested_price', 0),
                            tax_rate=spec_data.get('tax_rate', 13)
                        )
                        db.session.add(spec)

                # 创建产品属性
                attributes = product_data.get('attributes', [])
                for attr_data in attributes:
                    if attr_data.get('attribute_name') and attr_data.get('attribute_value'):
                        attr = ProductAttribute(
                            product_id=product.id,
                            attribute_name=attr_data['attribute_name'],
                            attribute_value=attr_data['attribute_value']
                        )
                        db.session.add(attr)

                imported_count += 1

            except Exception as e:
                current_app.logger.error(f"导入产品失败: {str(e)}")
                errors.append(f"产品 '{product_data.get('name', '未知')}' 导入失败: {str(e)}")
                failed_count += 1
                continue

        # 提交所有更改
        if imported_count > 0:
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"提交导入数据失败: {str(e)}")
                return make_response(error_response, f"提交导入数据失败: {str(e)}", code=500)

        # 返回导入结果
        result = {
            'imported_count': imported_count,
            'failed_count': failed_count,
            'total_count': len(products_data),
            'warnings': warnings,
            'errors': errors[:20]  # 只返回前20个错误
        }

        success = imported_count > 0
        message = f"成功导入 {imported_count} 个产品"
        if failed_count > 0:
            message += f"，失败 {failed_count} 个"
        if warnings:
            message += f"，警告 {len(warnings)} 个"

        return make_response(success_response if success else error_response, result, message)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"从预览数据导入失败: {str(e)}")
        return make_response(error_response, f"从预览数据导入失败: {str(e)}", code=500)


def import_from_excel_direct(file):
    """直接从Excel文件导入（保持原有逻辑）"""
    try:
        # 读取Excel文件
        try:
            df = pd.read_excel(file)
        except Exception as e:
            return make_response(error_response, f"读取Excel文件失败: {str(e)}", code=400)

        if df.empty:
            return make_response(error_response, "Excel文件为空", code=400)

        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            row_num = index + 2
            try:
                # 数据验证
                product_name = str(row.get('产品名称', '')).strip()
                model = str(row.get('产品型号', '')).strip()
                category_id = row.get('产品分类ID')
                unit = str(row.get('单位', '')).strip()

                if not all([product_name, model, category_id, unit]):
                    errors.append(f"第{row_num}行: 必填字段不能为空")
                    error_count += 1
                    continue

                # 检查分类是否存在
                try:
                    category_id = int(category_id)
                    if not ProductCategory.query.get(category_id):
                        errors.append(f"第{row_num}行: 产品分类ID {category_id} 不存在")
                        error_count += 1
                        continue
                except (ValueError, TypeError):
                    errors.append(f"第{row_num}行: 产品分类ID必须是数字")
                    error_count += 1
                    continue

                # 检查品牌
                brand_id = row.get('品牌ID')
                if brand_id:
                    try:
                        brand_id = int(brand_id)
                        if not Brand.query.get(brand_id):
                            errors.append(f"第{row_num}行: 品牌ID {brand_id} 不存在")
                            error_count += 1
                            continue
                    except (ValueError, TypeError):
                        errors.append(f"第{row_num}行: 品牌ID必须是数字")
                        error_count += 1
                        continue
                else:
                    brand_id = None

                # 检查产品是否已存在
                existing_product = Product.query.filter_by(
                    name=product_name,
                    model=model
                ).first()
                if existing_product:
                    errors.append(f"第{row_num}行: 产品名称和型号组合已存在")
                    error_count += 1
                    continue

                # 创建产品
                product_data = {
                    'name': product_name,
                    'model': model,
                    'category_id': category_id,
                    'brand_id': brand_id,
                    'unit': unit,
                    'status': str(row.get('状态', '正常')).strip(),
                    'description': str(row.get('描述', '')).strip() or None
                }

                product = Product(**product_data)
                db.session.add(product)
                db.session.flush()

                # 创建产品规格（如果有）
                specification = str(row.get('规格', '')).strip()
                if specification:
                    spec_data = {
                        'product_id': product.id,
                        'specification': specification,
                        'cost_price': float(row.get('成本价', 0)) or None,
                        'suggested_price': float(row.get('建议价', 0)) or None,
                        'tax_rate': float(row.get('税率', 0)) or None
                    }
                    product_spec = ProductSpecification(**spec_data)
                    db.session.add(product_spec)

                success_count += 1

            except Exception as e:
                errors.append(f"第{row_num}行: {str(e)}")
                error_count += 1
                continue

        db.session.commit()

        result = {
            'success_count': success_count,
            'error_count': error_count,
            'total_count': len(df),
            'errors': errors[:20]  # 只返回前20个错误
        }

        success = success_count > 0
        message = f"成功导入 {success_count} 个产品"
        if error_count > 0:
            message += f"，失败 {error_count} 个"

        return make_response(success_response if success else error_response, result, message)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Excel导入失败: {str(e)}")
        return make_response(error_response, f"Excel导入失败: {str(e)}", code=500)
