<template>
  <div class="customer-form" v-loading="loading">
    <!-- 页面头部 -->
    <el-card class="mb-20">
      <div class="flex-between">
        <h2 class="form-title">{{ isEdit ? '编辑客户' : '新增客户' }}</h2>
        <div>
          <el-button @click="$router.back()">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 客户表单 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>客户基本信息</span>
        </div>
      </template>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="customer-form-content"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contact">
              <el-input v-model="form.contact" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入电子邮箱" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="客户地址" prop="address">
              <el-input 
                v-model="form.address" 
                type="textarea" 
                :rows="3"
                placeholder="请输入客户地址" 
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="统一社会信用代码" prop="tax_id">
              <el-input v-model="form.tax_id" placeholder="请输入统一社会信用代码（可选）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户来源" prop="source">
              <el-select v-model="form.source" placeholder="请选择客户来源" style="width: 100%">
                <el-option label="网络渠道" value="网络推广" />
                <el-option label="客户推荐" value="老客户推荐" />
                <el-option label="展会" value="展会" />
                <el-option label="销售拜访" value="销售拜访" />
                <el-option label="老客户" value="老客户" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户等级" prop="level">
              <el-select v-model="form.level" placeholder="请选择客户等级" style="width: 100%">
                <el-option label="普通客户" value="normal" />
                <el-option label="VIP客户" value="vip" />
                <el-option label="重要客户" value="important" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio value="正常">正常</el-radio>
                <el-radio value="禁用">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="notes">
              <el-input
                v-model="form.notes"
                type="textarea"
                :rows="4"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 银行账户管理 -->
    <el-card class="mt-4" header="银行账户">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>银行账户</span>
          <el-button type="primary" size="small" @click="handleAddBankAccount">
            <el-icon><Plus /></el-icon>
            添加账户
          </el-button>
        </div>
      </template>

      <div v-if="bankAccounts.length === 0" class="text-center text-gray-500 py-8">
        暂无银行账户信息，点击上方"添加账户"按钮添加
      </div>

      <el-table v-else :data="bankAccounts" border stripe>
        <el-table-column prop="bank_name" label="开户行" min-width="120" />
        <el-table-column prop="account_name" label="账户名称" min-width="120" />
        <el-table-column prop="account_number" label="账号" min-width="150" />
        <el-table-column prop="is_default" label="默认账户" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.is_default" type="success" size="small">默认</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="140" align="center">
          <template #default="{ row, $index }">
            <div style="display: flex; gap: 8px; justify-content: center;">
              <el-button type="primary" size="small" @click="handleEditBankAccount(row, $index)">
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteBankAccount($index)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 送货地址管理 -->
    <el-card class="mt-4" header="送货地址">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>送货地址</span>
          <el-button type="primary" size="small" @click="handleAddDeliveryAddress">
            <el-icon><Plus /></el-icon>
            添加地址
          </el-button>
        </div>
      </template>

      <div v-if="deliveryAddresses.length === 0" class="text-center text-gray-500 py-8">
        暂无送货地址信息，点击上方"添加地址"按钮添加
      </div>

      <el-table v-else :data="deliveryAddresses" border stripe>
        <el-table-column prop="contact_person" label="联系人" width="120" />
        <el-table-column prop="contact_phone" label="联系电话" width="130" />
        <el-table-column label="详细地址" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.full_address || `${row.province || ''}${row.city || ''}${row.district || ''}${row.detailed_address || ''}` }}
          </template>
        </el-table-column>
        <el-table-column prop="is_default" label="默认地址" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.is_default" type="success" size="small">默认</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="140" align="center">
          <template #default="{ row, $index }">
            <div style="display: flex; gap: 8px; justify-content: center;">
              <el-button type="primary" size="small" @click="handleEditDeliveryAddress(row, $index)">
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteDeliveryAddress($index)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 银行账户对话框 -->
    <BankAccountDialog
      v-model:visible="bankAccountDialogVisible"
      :type="currentBankAccountIndex >= 0 ? 'edit' : 'add'"
      :data="currentBankAccount"
      :customer-id="0"
      @submit="handleBankAccountSubmit"
    />

    <!-- 送货地址对话框 -->
    <DeliveryAddressDialog
      v-model:visible="deliveryAddressDialogVisible"
      :type="currentDeliveryAddressIndex >= 0 ? 'edit' : 'add'"
      :data="currentDeliveryAddress"
      :customer-id="0"
      @submit="handleDeliveryAddressSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Check,
  Plus
} from '@element-plus/icons-vue'
import { customerApi } from '@/api/customer'
import BankAccountDialog from '@/components/customers/BankAccountDialog.vue'
import DeliveryAddressDialog from '@/components/customers/DeliveryAddressDialog.vue'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref()

// 状态
const loading = ref(false)
const submitting = ref(false)

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)

// 银行账户和送货地址管理
const bankAccounts = ref<any[]>([])
const deliveryAddresses = ref<any[]>([])

// 原始数据，用于编辑模式下的比较
const originalBankAccounts = ref<any[]>([])
const originalDeliveryAddresses = ref<any[]>([])

// 银行账户对话框
const bankAccountDialogVisible = ref(false)
const currentBankAccount = ref(null)
const currentBankAccountIndex = ref(-1)

// 送货地址对话框
const deliveryAddressDialogVisible = ref(false)
const currentDeliveryAddress = ref(null)
const currentDeliveryAddressIndex = ref(-1)

// 表单数据
const form = reactive({
  name: '',
  contact: '',
  phone: '',
  email: '',
  address: '',
  tax_id: '',
  source: '',
  level: 'normal',
  status: '正常',
  notes: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系人', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  address: [
    { max: 255, message: '地址长度不能超过 255 个字符', trigger: 'blur' }
  ],
  tax_id: [
    { max: 50, message: '统一社会信用代码长度不能超过 50 个字符', trigger: 'blur' }
  ],
  source: [
    { max: 50, message: '客户来源长度不能超过 50 个字符', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择客户等级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择客户状态', trigger: 'change' }
  ],
  notes: [
    { max: 1000, message: '备注长度不能超过 1000 个字符', trigger: 'blur' }
  ]
}

// 获取客户详情（编辑模式）
const getCustomerDetail = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const customerId = Number(route.params.id)
    const response = await customerApi.getById(customerId) as any
    const customer = response.data || response
    
    // 填充表单数据
    Object.assign(form, {
      name: customer.name || '',
      contact: customer.contact || '',
      phone: customer.phone || '',
      email: customer.email || '',
      address: customer.address || '',
      tax_id: customer.tax_id || '',
      source: customer.source || '',
      level: customer.level || 'normal',
      status: customer.status || '正常',
      notes: customer.notes || ''
    })

    // 加载银行账户和送货地址数据
    await getBankAccounts(customerId)
    await getDeliveryAddresses(customerId)

    // 保存原始数据用于比较
    originalBankAccounts.value = JSON.parse(JSON.stringify(bankAccounts.value))
    originalDeliveryAddresses.value = JSON.parse(JSON.stringify(deliveryAddresses.value))
  } catch (error) {
    console.error('获取客户详情失败:', error)
    ElMessage.error('获取客户详情失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  if (!isEdit.value) {
    Object.assign(form, {
      name: '',
      contact: '',
      phone: '',
      email: '',
      address: '',
      tax_id: '',
      source: '',
      level: 'normal',
      status: '正常',
      notes: ''
    })
    bankAccounts.value = []
    deliveryAddresses.value = []
    originalBankAccounts.value = []
    originalDeliveryAddresses.value = []
  }
}

// 获取银行账户列表
const getBankAccounts = async (customerId: number) => {
  try {
    const response = await customerApi.getBankAccounts(customerId) as any
    bankAccounts.value = response.data || response || []
  } catch (error) {
    console.error('获取银行账户失败:', error)
  }
}

// 获取送货地址列表
const getDeliveryAddresses = async (customerId: number) => {
  try {
    const response = await customerApi.getDeliveryAddresses(customerId) as any
    deliveryAddresses.value = response.data || response || []
  } catch (error) {
    console.error('获取送货地址失败:', error)
  }
}

// 银行账户管理方法
const handleAddBankAccount = () => {
  currentBankAccount.value = null
  currentBankAccountIndex.value = -1
  bankAccountDialogVisible.value = true
}

const handleEditBankAccount = (account: any, index: number) => {
  currentBankAccount.value = { ...account }
  currentBankAccountIndex.value = index
  bankAccountDialogVisible.value = true
}

const handleDeleteBankAccount = (index: number) => {
  ElMessageBox.confirm('确定要删除这个银行账户吗？', '确认删除', {
    type: 'warning'
  }).then(() => {
    bankAccounts.value.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

const handleBankAccountSubmit = (accountData: any) => {
  if (currentBankAccountIndex.value >= 0) {
    // 编辑模式
    bankAccounts.value[currentBankAccountIndex.value] = accountData
    ElMessage.success('更新银行账户成功')
  } else {
    // 添加模式
    bankAccounts.value.push(accountData)
    ElMessage.success('添加银行账户成功')
  }
  bankAccountDialogVisible.value = false
}

// 送货地址管理方法
const handleAddDeliveryAddress = () => {
  currentDeliveryAddress.value = null
  currentDeliveryAddressIndex.value = -1
  deliveryAddressDialogVisible.value = true
}

const handleEditDeliveryAddress = (address: any, index: number) => {
  currentDeliveryAddress.value = { ...address }
  currentDeliveryAddressIndex.value = index
  deliveryAddressDialogVisible.value = true
}

const handleDeleteDeliveryAddress = (index: number) => {
  ElMessageBox.confirm('确定要删除这个送货地址吗？', '确认删除', {
    type: 'warning'
  }).then(() => {
    deliveryAddresses.value.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

const handleDeliveryAddressSubmit = (addressData: any) => {
  if (currentDeliveryAddressIndex.value >= 0) {
    // 编辑模式
    deliveryAddresses.value[currentDeliveryAddressIndex.value] = addressData
    ElMessage.success('更新送货地址成功')
  } else {
    // 添加模式
    deliveryAddresses.value.push(addressData)
    ElMessage.success('添加送货地址成功')
  }
  deliveryAddressDialogVisible.value = false
}

// 同步银行账户数据到后端
const syncBankAccounts = async (customerId: number) => {
  try {
    // 获取当前后端的银行账户数据
    const currentBackendAccounts = originalBankAccounts.value
    const currentFrontendAccounts = bankAccounts.value

    // 找出需要删除的账户（在原始数据中存在，但在当前数据中不存在）
    for (const originalAccount of currentBackendAccounts) {
      if (originalAccount.id && !currentFrontendAccounts.find(acc => acc.id === originalAccount.id)) {
        try {
          await customerApi.deleteBankAccount(customerId, originalAccount.id)
        } catch (error: any) {
          // 如果账户已经不存在（404错误），忽略这个错误
          if (error.response?.status !== 404) {
            console.error(`删除银行账户 ${originalAccount.id} 失败:`, error)
            throw error
          }
        }
      }
    }

    // 处理新增和更新的账户
    for (const account of currentFrontendAccounts) {
      if (account.id) {
        // 更新现有账户
        const originalAccount = currentBackendAccounts.find(acc => acc.id === account.id)
        if (originalAccount && JSON.stringify(account) !== JSON.stringify(originalAccount)) {
          try {
            const accountData: any = {
              bank_name: account.bank_name,
              account_name: account.account_name,
              account_number: account.account_number,
              is_default: account.is_default || false
            }
            if (account.notes && account.notes.trim()) {
              accountData.notes = account.notes
            }
            await customerApi.updateBankAccount(customerId, account.id, accountData)
          } catch (error: any) {
            // 如果账户已经不存在（404错误），尝试创建新账户
            if (error.response?.status === 404) {
              console.warn(`银行账户 ${account.id} 不存在，尝试创建新账户`)
              const accountData: any = {
                bank_name: account.bank_name,
                account_name: account.account_name,
                account_number: account.account_number,
                is_default: account.is_default || false
              }
              if (account.notes && account.notes.trim()) {
                accountData.notes = account.notes
              }
              await customerApi.addBankAccount(customerId, accountData)
            } else {
              console.error(`更新银行账户 ${account.id} 失败:`, error)
              throw error
            }
          }
        }
      } else {
        // 新增账户
        const accountData: any = {
          bank_name: account.bank_name,
          account_name: account.account_name,
          account_number: account.account_number,
          is_default: account.is_default || false
        }
        if (account.notes && account.notes.trim()) {
          accountData.notes = account.notes
        }
        await customerApi.addBankAccount(customerId, accountData)
      }
    }
  } catch (error) {
    console.error('同步银行账户失败:', error)
    throw error
  }
}

// 同步送货地址数据到后端
const syncDeliveryAddresses = async (customerId: number) => {
  try {
    // 获取当前后端的送货地址数据
    const currentBackendAddresses = originalDeliveryAddresses.value
    const currentFrontendAddresses = deliveryAddresses.value

    // 找出需要删除的地址（在原始数据中存在，但在当前数据中不存在）
    for (const originalAddress of currentBackendAddresses) {
      if (originalAddress.id && !currentFrontendAddresses.find(addr => addr.id === originalAddress.id)) {
        try {
          await customerApi.deleteDeliveryAddress(customerId, originalAddress.id)
        } catch (error: any) {
          // 如果地址已经不存在（404错误），忽略这个错误
          if (error.response?.status !== 404) {
            console.error(`删除送货地址 ${originalAddress.id} 失败:`, error)
            throw error
          }
        }
      }
    }

    // 处理新增和更新的地址
    for (const address of currentFrontendAddresses) {
      if (address.id) {
        // 更新现有地址
        const originalAddress = currentBackendAddresses.find(addr => addr.id === address.id)
        if (originalAddress && JSON.stringify(address) !== JSON.stringify(originalAddress)) {
          try {
            const addressData: any = {
              contact_person: address.contact_person,
              contact_phone: address.contact_phone,
              province: address.province,
              city: address.city,
              district: address.district,
              detailed_address: address.detailed_address || address.full_address,
              is_default: address.is_default || false
            }
            if (address.notes && address.notes.trim()) {
              addressData.notes = address.notes
            }
            await customerApi.updateDeliveryAddress(customerId, address.id, addressData)
          } catch (error: any) {
            // 如果地址已经不存在（404错误），尝试创建新地址
            if (error.response?.status === 404) {
              console.warn(`送货地址 ${address.id} 不存在，尝试创建新地址`)
              const addressData: any = {
                contact_person: address.contact_person,
                contact_phone: address.contact_phone,
                province: address.province,
                city: address.city,
                district: address.district,
                detailed_address: address.detailed_address || address.full_address,
                is_default: address.is_default || false
              }
              if (address.notes && address.notes.trim()) {
                addressData.notes = address.notes
              }
              await customerApi.addDeliveryAddress(customerId, addressData)
            } else {
              console.error(`更新送货地址 ${address.id} 失败:`, error)
              throw error
            }
          }
        }
      } else {
        // 新增地址
        const addressData: any = {
          contact_person: address.contact_person,
          contact_phone: address.contact_phone,
          province: address.province,
          city: address.city,
          district: address.district,
          detailed_address: address.detailed_address || address.full_address,
          is_default: address.is_default || false
        }
        if (address.notes && address.notes.trim()) {
          addressData.notes = address.notes
        }
        await customerApi.addDeliveryAddress(customerId, addressData)
      }
    }
  } catch (error) {
    console.error('同步送货地址失败:', error)
    throw error
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData: any = { ...form }

    // 只在新增模式下添加银行账户和送货地址数据
    if (!isEdit.value) {
      if (bankAccounts.value.length > 0) {
        submitData.bank_accounts = bankAccounts.value.map(account => ({
          bank_name: account.bank_name,
          account_name: account.account_name,
          account_number: account.account_number,
          is_default: account.is_default || false,
          notes: account.notes || null
        }))
      }

      if (deliveryAddresses.value.length > 0) {
        submitData.delivery_addresses = deliveryAddresses.value.map(address => ({
          contact_person: address.contact_person,
          contact_phone: address.contact_phone,
          province: address.province,
          city: address.city,
          district: address.district,
          detailed_address: address.detailed_address || address.full_address,
          is_default: address.is_default || false,
          notes: address.notes || null
        }))
      }
    }

    // 处理空值 - 删除空字段而不是设置为null
    if (!submitData.email || submitData.email.trim() === '') {
      delete submitData.email
    }
    if (!submitData.address || submitData.address.trim() === '') {
      delete submitData.address
    }
    if (!submitData.tax_id || submitData.tax_id.trim() === '') {
      delete submitData.tax_id
    }
    if (!submitData.source || submitData.source.trim() === '') {
      delete submitData.source
    }
    if (!submitData.notes || submitData.notes.trim() === '') {
      delete submitData.notes
    }
    
    if (isEdit.value) {
      // 更新客户基本信息
      const customerId = Number(route.params.id)
      await customerApi.update(customerId, submitData)

      // 同步银行账户和送货地址数据
      await syncBankAccounts(customerId)
      await syncDeliveryAddresses(customerId)

      ElMessage.success('更新客户成功')
    } else {
      // 创建客户（包含银行账户和送货地址）
      await customerApi.create(submitData)
      ElMessage.success('创建客户成功')
    }
    
    // 返回列表页
    router.push('/customers')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(isEdit.value ? '更新客户失败' : '创建客户失败')
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  if (isEdit.value) {
    getCustomerDetail()
  }
})
</script>

<style lang="scss" scoped>
.customer-form {
  .card-header {
    font-size: 18px;
    font-weight: bold;
  }
  
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .customer-form-content {
    max-width: 800px;
    
    .el-form-item {
      margin-bottom: 24px;
    }
  }
}
</style>
