<template>
  <div class="statement-create">
    <div class="page-header">
      <el-page-header @back="handleBack" content="新建对账单" />
    </div>

    <div v-loading="loading" class="content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="statement-form"
      >
        <el-card class="basic-info">
          <template #header>
            <span>基本信息</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户" prop="customer_id">
                <el-select
                  v-model="form.customer_id"
                  filterable
                  remote
                  :remote-method="handleSearchCustomers"
                  placeholder="请选择客户"
                  @change="handleCustomerChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in customerOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="对账日期" prop="statement_date">
                <el-date-picker
                  v-model="form.statement_date"
                  type="date"
                  placeholder="请选择对账日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="应付款日期" prop="due_date">
                <el-date-picker
                  v-model="form.due_date"
                  type="date"
                  placeholder="请选择应付款日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="总金额">
                <el-input
                  :value="formatAmount(totalAmount)"
                  readonly
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="优惠金额" prop="discount_amount">
                <el-input-number
                  v-model="form.discount_amount"
                  :min="0"
                  :max="totalAmount"
                  :precision="2"
                  placeholder="请输入优惠金额"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="调整后总金额">
                <el-input
                  :value="formatAmount(adjustedTotalAmount)"
                  readonly
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注">
            <el-input
              v-model="form.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-card>

        <el-card class="delivery-notes">
          <template #header>
            <div class="card-header">
              <span>选择发货单</span>
              <el-button type="primary" @click="showDeliveryNoteDialog">
                添加发货单
              </el-button>
            </div>
          </template>

          <!-- 有数据时显示表格 -->
          <el-table v-if="selectedDeliveryNotes.length > 0" :data="selectedDeliveryNotes" border>
            <el-table-column prop="delivery_number" label="发货单号" width="180" />
            <el-table-column prop="order.order_number" label="订单号" width="180" />
            <el-table-column prop="delivery_date" label="发货日期" width="120" />
            <el-table-column label="发货金额" width="120">
              <template #default="{ row }">
                {{ calculateDeliveryAmount(row) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row, $index }">
                <el-button
                  link
                  type="danger"
                  @click="removeDeliveryNote($index)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 无数据时显示简洁提示 -->
          <div v-else class="empty-state">
            <el-icon class="empty-icon"><DocumentAdd /></el-icon>
            <p class="empty-text">暂无发货单，点击上方"添加发货单"按钮选择</p>
          </div>
        </el-card>

        <el-card class="return-orders">
          <template #header>
            <div class="card-header">
              <span>选择退货单</span>
              <el-button type="primary" @click="showReturnOrderDialog">
                添加退货单
              </el-button>
            </div>
          </template>

          <!-- 有数据时显示表格 -->
          <el-table v-if="selectedReturnOrders.length > 0" :data="selectedReturnOrders" border>
            <el-table-column prop="return_number" label="退货单号" width="180" />
            <el-table-column prop="order.order_number" label="订单号" width="180" />
            <el-table-column prop="return_date" label="退货日期" width="120" />
            <el-table-column label="退货金额" width="120">
              <template #default="{ row }">
                {{ calculateReturnAmount(row) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row, $index }">
                <el-button
                  link
                  type="danger"
                  @click="removeReturnOrder($index)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 无数据时显示简洁提示 -->
          <div v-else class="empty-state">
            <el-icon class="empty-icon"><ShoppingCart /></el-icon>
            <p class="empty-text">暂无退货单，点击上方"添加退货单"按钮选择</p>
          </div>
        </el-card>

        <div class="form-actions">
          <el-button @click="handleBack">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            保存
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 发货单选择对话框 -->
    <el-dialog
      v-model="deliveryNoteDialogVisible"
      title="选择发货单"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="delivery-note-search">
        <el-form :model="deliveryNoteSearchForm" inline>
          <el-form-item label="发货单号">
            <el-input
              v-model="deliveryNoteSearchForm.delivery_number"
              placeholder="请输入发货单号"
              clearable
            />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input
              v-model="deliveryNoteSearchForm.order_number"
              placeholder="请输入订单号"
              clearable
            />
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="deliveryNoteSearchForm.date_range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchDeliveryNotes">搜索</el-button>
            <el-button @click="resetDeliveryNoteSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        ref="deliveryNoteTableRef"
        v-loading="deliveryNoteLoading"
        :data="availableDeliveryNotes"
        border
        @selection-change="handleDeliveryNoteSelectionChange"
        @row-click="handleDeliveryNoteRowClick"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="delivery_number" label="发货单号" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-tag
                v-if="isDeliveryNoteSelected(row.id)"
                type="success"
                size="small"
                effect="plain"
                class="mr-1"
              >
                已选择
              </el-tag>
              <span>{{ row.delivery_number }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order_number" label="订单号" width="180" />
        <el-table-column prop="delivery_date" label="发货日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getDeliveryStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发货金额" width="120">
          <template #default="{ row }">
            {{ calculateDeliveryAmount(row) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态提示 -->
      <div v-if="availableDeliveryNotes.length === 0 && !deliveryNoteLoading" class="empty-state-dialog">
        <el-empty description="">
          <template #description>
            <div class="empty-description">
              <p class="empty-title">暂无可用的发货单</p>
              <p class="empty-subtitle">可能的原因：</p>
              <ul class="empty-reasons">
                <li>该客户暂无已签收的发货单</li>
                <li>所有发货单已被其他对账单关联</li>
                <li>发货单状态不符合对账条件</li>
              </ul>
            </div>
          </template>
        </el-empty>
      </div>

      <div class="dialog-pagination">
        <el-pagination
          v-model:current-page="deliveryNotePagination.page"
          v-model:page-size="deliveryNotePagination.per_page"
          :page-sizes="[10, 20, 50]"
          :total="deliveryNotePagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="searchDeliveryNotes"
          @current-change="searchDeliveryNotes"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deliveryNoteDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelectDeliveryNotes">
            确定选择 ({{ tempSelectedDeliveryNotes.length }})
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 退货单选择对话框 -->
    <el-dialog
      v-model="returnOrderDialogVisible"
      title="选择退货单"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="return-order-search">
        <el-form :model="returnOrderSearchForm" inline>
          <el-form-item label="退货单号">
            <el-input
              v-model="returnOrderSearchForm.return_number"
              placeholder="请输入退货单号"
              clearable
            />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input
              v-model="returnOrderSearchForm.order_number"
              placeholder="请输入订单号"
              clearable
            />
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="returnOrderSearchForm.date_range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchReturnOrders">搜索</el-button>
            <el-button @click="resetReturnOrderSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        ref="returnOrderTableRef"
        v-loading="returnOrderLoading"
        :data="availableReturnOrders"
        border
        @selection-change="handleReturnOrderSelectionChange"
        @row-click="handleReturnOrderRowClick"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="return_number" label="退货单号" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-tag
                v-if="isReturnOrderSelected(row.id)"
                type="success"
                size="small"
                effect="plain"
                class="mr-1"
              >
                已选择
              </el-tag>
              <span>{{ row.return_number }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order.order_number" label="订单号" width="180" show-overflow-tooltip />
        <el-table-column prop="return_date" label="退货日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getReturnStatusType(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="退货金额" width="120">
          <template #default="{ row }">
            {{ calculateReturnAmount(row) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态提示 -->
      <div v-if="availableReturnOrders.length === 0 && !returnOrderLoading" class="empty-state-dialog">
        <el-empty description="">
          <template #description>
            <div class="empty-description">
              <p class="empty-title">暂无可用的退货单</p>
              <p class="empty-subtitle">可能的原因：</p>
              <ul class="empty-reasons">
                <li>该客户暂无已完成的退货单</li>
                <li>所有退货单已被其他对账单关联</li>
                <li>退货单状态不符合对账条件</li>
              </ul>
            </div>
          </template>
        </el-empty>
      </div>

      <div class="dialog-pagination">
        <el-pagination
          v-model:current-page="returnOrderPagination.page"
          v-model:page-size="returnOrderPagination.per_page"
          :page-sizes="[10, 20, 50]"
          :total="returnOrderPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="searchReturnOrders"
          @current-change="searchReturnOrders"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="returnOrderDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelectReturnOrders">
            确定选择 ({{ tempSelectedReturnOrders.length }})
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { DocumentAdd, ShoppingCart } from '@element-plus/icons-vue'
import type { FormInstance, TableInstance } from 'element-plus'
import type { CustomerSimple } from '@/types/customer'
import { searchCustomers } from '@/api/customer'
import { statementApi } from '@/api/statement'
import { formatCurrency } from '@/utils/formatter'

const router = useRouter()

const loading = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  customer_id: '',
  statement_date: new Date().toISOString().split('T')[0], // 默认今天
  due_date: '',
  notes: '',
  delivery_note_ids: [] as string[],
  return_order_ids: [] as string[],
  discount_amount: 0
})

// 表单验证规则
const rules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  statement_date: [
    { required: true, message: '请选择对账日期', trigger: 'change' }
  ]
}

// 客户选项
const customerOptions = ref<CustomerSimple[]>([])

// 选中的发货单
const selectedDeliveryNotes = ref<any[]>([])

// 选中的退货单
const selectedReturnOrders = ref<any[]>([])

// 发货单选择对话框
const deliveryNoteDialogVisible = ref(false)
const deliveryNoteLoading = ref(false)
const deliveryNoteTableRef = ref<TableInstance>()
const availableDeliveryNotes = ref<any[]>([])
const tempSelectedDeliveryNotes = ref<any[]>([])

// 发货单搜索表单
const deliveryNoteSearchForm = reactive({
  delivery_number: '',
  order_number: '',
  date_range: []
})

// 发货单分页
const deliveryNotePagination = reactive({
  page: 1,
  per_page: 10,
  total: 0
})

// 退货单选择对话框
const returnOrderDialogVisible = ref(false)
const returnOrderLoading = ref(false)
const returnOrderTableRef = ref<TableInstance>()
const availableReturnOrders = ref<any[]>([])
const tempSelectedReturnOrders = ref<any[]>([])

// 退货单搜索表单
const returnOrderSearchForm = reactive({
  return_number: '',
  order_number: '',
  date_range: []
})

// 退货单分页
const returnOrderPagination = reactive({
  page: 1,
  per_page: 10,
  total: 0
})

// 计算属性
const totalAmount = computed(() => {
  let total = 0

  // 加上发货单金额
  selectedDeliveryNotes.value.forEach(deliveryNote => {
    // 直接使用数据库中的total_amount字段
    total += parseFloat(deliveryNote.total_amount || 0)
  })

  // 减去退货单金额
  selectedReturnOrders.value.forEach(returnOrder => {
    const returnAmount = calculateReturnOrderAmount(returnOrder)
    total -= returnAmount
  })

  return total
})

// 调整后总金额
const adjustedTotalAmount = computed(() => {
  return totalAmount.value - (form.discount_amount || 0)
})

// 搜索客户
const handleSearchCustomers = async (query: string) => {
  if (query) {
    try {
      const response = await searchCustomers({ name: query, per_page: 20 })
      console.log('客户搜索响应:', response)

      // 处理不同的响应格式
      let customers = []
      if (Array.isArray(response)) {
        customers = response
      } else if (response.data && Array.isArray(response.data)) {
        customers = response.data
      } else if (response.list && Array.isArray(response.list)) {
        customers = response.list
      } else if (response.items && Array.isArray(response.items)) {
        customers = response.items
      }

      customerOptions.value = customers
      console.log('设置客户选项:', customers)
    } catch (error) {
      console.error('搜索客户失败:', error)
    }
  } else {
    customerOptions.value = []
  }
}

// 客户变化处理
const handleCustomerChange = () => {
  // 清空已选择的发货单和退货单
  selectedDeliveryNotes.value = []
  selectedReturnOrders.value = []
  form.delivery_note_ids = []
  form.return_order_ids = []
}

// 显示发货单选择对话框
const showDeliveryNoteDialog = () => {
  if (!form.customer_id) {
    ElMessage.warning('请先选择客户')
    return
  }

  deliveryNoteDialogVisible.value = true
  tempSelectedDeliveryNotes.value = [...selectedDeliveryNotes.value]
  searchDeliveryNotes()
}

// 搜索可用发货单
const searchDeliveryNotes = async () => {
  if (!form.customer_id) return

  deliveryNoteLoading.value = true
  try {
    const params = {
      customer_id: form.customer_id,
      page: deliveryNotePagination.page,
      per_page: deliveryNotePagination.per_page,
      delivery_number: deliveryNoteSearchForm.delivery_number,
      order_number: deliveryNoteSearchForm.order_number,
      start_date: deliveryNoteSearchForm.date_range?.[0],
      end_date: deliveryNoteSearchForm.date_range?.[1]
    }

    const response = await statementApi.getAvailableDeliveryNotes(form.customer_id, undefined, params)
    availableDeliveryNotes.value = response.data || response || []
    deliveryNotePagination.total = response.pagination?.total || 0

    // 设置已选中的发货单
    setTimeout(() => {
      if (deliveryNoteTableRef.value) {
        tempSelectedDeliveryNotes.value.forEach(selected => {
          const found = availableDeliveryNotes.value.find(item => item.id === selected.id)
          if (found) {
            deliveryNoteTableRef.value!.toggleRowSelection(found, true)
          }
        })
      }
    }, 100)
  } catch (error) {
    console.error('获取可用发货单失败:', error)
    ElMessage.error('获取可用发货单失败')
  } finally {
    deliveryNoteLoading.value = false
  }
}

// 重置发货单搜索
const resetDeliveryNoteSearch = () => {
  deliveryNoteSearchForm.delivery_number = ''
  deliveryNoteSearchForm.order_number = ''
  deliveryNoteSearchForm.date_range = []
  deliveryNotePagination.page = 1
  searchDeliveryNotes()
}

// 发货单选择变化
const handleDeliveryNoteSelectionChange = (selection: any[]) => {
  tempSelectedDeliveryNotes.value = selection
}

// 确认选择发货单
const confirmSelectDeliveryNotes = () => {
  selectedDeliveryNotes.value = [...tempSelectedDeliveryNotes.value]
  form.delivery_note_ids = selectedDeliveryNotes.value.map(dn => dn.id)
  deliveryNoteDialogVisible.value = false
}

// 移除发货单
const removeDeliveryNote = (index: number) => {
  selectedDeliveryNotes.value.splice(index, 1)
  form.delivery_note_ids = selectedDeliveryNotes.value.map(dn => dn.id)
}

// 计算发货单金额
const calculateDeliveryAmount = (deliveryNote: any) => {
  // 直接使用数据库中的total_amount字段
  return formatAmount(deliveryNote.total_amount || 0)
}

// 发货单状态类型
const getDeliveryStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待发货': 'warning',
    '已发货': 'primary',
    '已签收': 'success',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

// 格式化金额
const formatAmount = (amount: number | string) => {
  if (amount === undefined || amount === null) return formatCurrency(0)
  return formatCurrency(amount)
}

// ========== 退货单相关方法 ==========

// 显示退货单选择对话框
const showReturnOrderDialog = () => {
  if (!form.customer_id) {
    ElMessage.warning('请先选择客户')
    return
  }

  returnOrderDialogVisible.value = true
  tempSelectedReturnOrders.value = [...selectedReturnOrders.value]
  searchReturnOrders()
}

// 搜索可用退货单
const searchReturnOrders = async () => {
  if (!form.customer_id) return

  returnOrderLoading.value = true
  try {
    const params = {
      customer_id: form.customer_id,
      page: returnOrderPagination.page,
      per_page: returnOrderPagination.per_page,
      return_number: returnOrderSearchForm.return_number,
      order_number: returnOrderSearchForm.order_number,
      start_date: returnOrderSearchForm.date_range?.[0],
      end_date: returnOrderSearchForm.date_range?.[1]
    }

    const response = await statementApi.getAvailableReturnOrders(form.customer_id, undefined, params)
    availableReturnOrders.value = response.data || response || []
    returnOrderPagination.total = response.pagination?.total || 0

    // 设置已选中的退货单
    setTimeout(() => {
      if (returnOrderTableRef.value) {
        tempSelectedReturnOrders.value.forEach(selected => {
          const found = availableReturnOrders.value.find(item => item.id === selected.id)
          if (found) {
            returnOrderTableRef.value!.toggleRowSelection(found, true)
          }
        })
      }
    }, 100)
  } catch (error) {
    console.error('获取可用退货单失败:', error)
    ElMessage.error('获取可用退货单失败')
  } finally {
    returnOrderLoading.value = false
  }
}

// 重置退货单搜索
const resetReturnOrderSearch = () => {
  returnOrderSearchForm.return_number = ''
  returnOrderSearchForm.order_number = ''
  returnOrderSearchForm.date_range = []
  returnOrderPagination.page = 1
  searchReturnOrders()
}

// 退货单选择变化
const handleReturnOrderSelectionChange = (selection: any[]) => {
  tempSelectedReturnOrders.value = selection
}

// 确认选择退货单
const confirmSelectReturnOrders = () => {
  selectedReturnOrders.value = [...tempSelectedReturnOrders.value]
  form.return_order_ids = selectedReturnOrders.value.map(ro => ro.id)
  returnOrderDialogVisible.value = false
}

// 移除退货单
const removeReturnOrder = (index: number) => {
  selectedReturnOrders.value.splice(index, 1)
  form.return_order_ids = selectedReturnOrders.value.map(ro => ro.id)
}

// 计算退货单金额
const calculateReturnAmount = (returnOrder: any) => {
  const amount = calculateReturnOrderAmount(returnOrder)
  return formatAmount(amount)
}

// 计算退货单金额（数值）
const calculateReturnOrderAmount = (returnOrder: any) => {
  let total = 0

  // 如果后端已经计算好了总金额，直接使用
  if (returnOrder.total_amount !== undefined) {
    return parseFloat(returnOrder.total_amount || 0)
  }

  // 否则根据items计算
  if (returnOrder.items) {
    returnOrder.items.forEach((item: any) => {
      // 适配后端返回的数据结构
      if (item.total_price !== undefined) {
        // 后端已经计算好的单项金额
        total += parseFloat(item.total_price || 0)
      } else if (item.order_product) {
        // 兼容原有的数据结构
        const unitPrice = parseFloat(item.order_product.unit_price || 0)
        const discount = parseFloat(item.order_product.discount || 0) / 100
        const taxRate = parseFloat(item.order_product.tax_rate || 0) / 100
        const quantity = parseFloat(item.quantity || 0)

        const actualPrice = unitPrice * (1 - discount) * (1 + taxRate)
        total += actualPrice * quantity
      } else {
        // 直接从item中获取价格信息
        const unitPrice = parseFloat(item.unit_price || 0)
        const discount = parseFloat(item.discount || 0) / 100
        const taxRate = parseFloat(item.tax_rate || 0) / 100
        const quantity = parseFloat(item.quantity || 0)

        const actualPrice = unitPrice * (1 - discount) * (1 + taxRate)
        total += actualPrice * quantity
      }
    })
  }
  return total
}

// 退货单状态类型
const getReturnStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待确认': 'warning',
    '退货中': 'primary',
    '已签收': 'success',
    '已拒绝': 'danger',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

// 检查退货单是否已被选择
const isReturnOrderSelected = (returnOrderId: number) => {
  return form.return_order_ids.includes(returnOrderId)
}

// 处理退货单行点击事件
const handleReturnOrderRowClick = (row: any, column: any, event: Event) => {
  if (!returnOrderTableRef.value) return

  // 延迟检查，确保文本选择操作完成
  setTimeout(() => {
    // 检查是否有文本被选中（用户正在选择文字）
    const selection = window.getSelection()
    if (selection && selection.toString().trim().length > 0) {
      return // 如果有文本被选中，不触发行选择
    }

    // 检查当前行是否已被选中
    const isSelected = selectedReturnOrders.value.some(item => item.id === row.id)

    if (isSelected) {
      // 如果已选中，则取消选中
      returnOrderTableRef.value.toggleRowSelection(row, false)
    } else {
      // 如果未选中，则选中
      returnOrderTableRef.value.toggleRowSelection(row, true)
    }
  }, 10) // 短暂延迟，让文本选择操作先完成
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (selectedDeliveryNotes.value.length === 0 && selectedReturnOrders.value.length === 0) {
        ElMessage.warning('请至少选择一个发货单或退货单')
        return
      }

      submitting.value = true
      try {
        const data = {
          customer_id: form.customer_id,
          statement_date: form.statement_date,
          due_date: form.due_date || undefined,
          notes: form.notes || undefined,
          delivery_note_ids: form.delivery_note_ids,
          return_order_ids: form.return_order_ids,
          discount_amount: form.discount_amount || 0,
          total_amount: totalAmount.value,
          adjusted_total_amount: adjustedTotalAmount.value
        }

        const response = await statementApi.create(data)

        ElMessage.success('创建成功')
        router.push(`/statements/${response.data?.id || response.id}`)
      } catch (error) {
        console.error('创建对账单失败:', error)
        ElMessage.error('创建对账单失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回
const handleBack = () => {
  router.push('/statements')
}

// 检查发货单是否已被选择
const isDeliveryNoteSelected = (deliveryNoteId: number) => {
  return form.delivery_note_ids.includes(deliveryNoteId)
}

// 处理发货单行点击事件
const handleDeliveryNoteRowClick = (row: any, column: any, event: Event) => {
  if (!deliveryNoteTableRef.value) return

  // 延迟检查，确保文本选择操作完成
  setTimeout(() => {
    // 检查是否有文本被选中（用户正在选择文字）
    const selection = window.getSelection()
    if (selection && selection.toString().trim().length > 0) {
      return // 如果有文本被选中，不触发行选择
    }

    // 检查当前行是否已被选中
    const isSelected = selectedDeliveryNotes.value.some(item => item.id === row.id)

    if (isSelected) {
      // 如果已选中，则取消选中
      deliveryNoteTableRef.value.toggleRowSelection(row, false)
    } else {
      // 如果未选中，则选中
      deliveryNoteTableRef.value.toggleRowSelection(row, true)
    }
  }, 10) // 短暂延迟，让文本选择操作先完成
}

onMounted(() => {
  // 设置默认应付款日期为30天后
  const dueDate = new Date()
  dueDate.setDate(dueDate.getDate() + 30)
  form.due_date = dueDate.toISOString().split('T')[0]
})
</script>

<style lang="scss" scoped>
.statement-create {
  .page-header {
    margin-bottom: 20px;
    padding: 16px 0;
    border-bottom: 1px solid #ebeef5;
  }

  .content {
    .statement-form {
      .basic-info {
        margin-bottom: 20px;
      }

      .delivery-notes {
        margin-bottom: 20px;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .empty-placeholder {
          padding: 40px 0;
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 30px 20px;
          color: var(--el-text-color-secondary);

          .empty-icon {
            font-size: 48px;
            margin-bottom: 12px;
            color: var(--el-color-info-light-3);
          }

          .empty-text {
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }

      .return-orders {
        margin-bottom: 20px;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .empty-placeholder {
          padding: 40px 0;
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 30px 20px;
          color: var(--el-text-color-secondary);

          .empty-icon {
            font-size: 48px;
            margin-bottom: 12px;
            color: var(--el-color-info-light-3);
          }

          .empty-text {
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }

      .form-actions {
        display: flex;
        justify-content: center;
        gap: 16px;
        padding: 20px 0;
      }
    }
  }

  .delivery-note-search {
    margin-bottom: 16px;
  }

  .return-order-search {
    margin-bottom: 16px;
  }

  .dialog-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .empty-state-dialog {
    padding: 40px 20px;

    .empty-description {
      text-align: center;
      color: var(--el-text-color-regular);

      .empty-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0 0 12px 0;
        color: var(--el-text-color-primary);
      }

      .empty-subtitle {
        font-size: 14px;
        margin: 0 0 8px 0;
        color: var(--el-text-color-regular);
      }

      .empty-reasons {
        list-style: none;
        padding: 0;
        margin: 0;
        font-size: 13px;
        color: var(--el-text-color-secondary);

        li {
          padding: 4px 0;
          position: relative;
          padding-left: 16px;

          &:before {
            content: '•';
            position: absolute;
            left: 0;
            color: var(--el-color-info);
          }
        }
      }
    }
  }
}
</style>
