import{J as e,r as a,w as l,d as t,f as r,M as n,o,g as i,h as d,i as u,k as s,N as c,c as p,t as v,a as m,af as f,U as _,u as y,b as h,L as b,e as g,j as w,l as k,F as D,m as C,W as N,Z as O,_ as x,$ as V,a4 as S,ac as q}from"./index-3d4c440c.js";import{h as I,c as A,i as U,u as P,j as z}from"./order-d6035a15.js";import{u as T,d as E}from"./delivery-8900e898.js";import{h as $,l as F}from"./error-handler-b38ec056.js";import{_ as L}from"./_plugin-vue_export-helper-1b428a4d.js";const j={key:0},B={key:1},Q={class:"mt-20 text-right"},R=L(e({__name:"OrderProductQuantityDialog",props:{visible:{type:Boolean,default:!1},orderId:{type:Number,default:null},orderData:{type:Object,default:null}},emits:["update:visible","saved"],setup(e,{emit:y}){const h=e,b=y,g=a(!1),w=a(!1),k=a(!1),D=a(null),C=a([]),N=new Map;l((()=>h.visible),(e=>{e&&(N.clear(),h.orderData?(D.value=f(h.orderData),h.orderData.products?C.value=f(h.orderData.products).map((e=>(N.set(e.id,e.quantity),{...e,original_quantity:e.quantity}))):C.value=[],g.value=!1,w.value=!1):h.orderId?(async e=>{if(e){w.value=!0,g.value=!0;try{const a=(await A(String(e))).data;D.value=a,a.products?C.value=f(a.products).map((e=>(N.set(e.id,e.quantity),{...e,original_quantity:e.quantity}))):C.value=[]}catch(a){_.error("获取订单详情失败"),console.error("Failed to fetch order details:",a)}finally{w.value=!1,g.value=!1}}})(h.orderId):(D.value=null,C.value=[],_.warning("未提供订单ID或订单数据")))}));const O=e=>N.get(e.id)??e.quantity,x=e=>{const a=O(e);return(e.unit_price||0)*a},V=t((()=>C.value.reduce(((e,a)=>e+x(a)),0))),S=t((()=>C.value.reduce(((e,a)=>e+(a.unit_price||0)*(a.quantity||0)),0))),q=async()=>{if(!D.value||!C.value.length)return void _.warning("无产品数据可保存");k.value=!0;let e=0,a=0;for(const t of C.value){const r=O(t);if(t.quantity!==r){if(!t.id){console.error("Product missing ID, cannot update:",t),_.error(`产品 ${t.product_name} 缺少ID，无法更新`),a++;continue}try{const a={product_id:t.product_id,product_specification_id:t.product_specification_id,quantity:t.quantity,unit_price:t.unit_price,tax_rate:t.tax_rate,discount:t.discount,notes:t.notes};await I(t.id,a),e++}catch(l){a++,console.error(`Failed to update product ${t.product_name} (ID: ${t.id}):`,l),_.error(`更新产品 ${t.product_name} 数量失败`)}}}k.value=!1,a>0?_.warning(`${e} 个产品数量已更新, ${a} 个更新失败。`):e>0?(_.success("产品数量更新成功！"),b("saved"),b("update:visible",!1)):(_.info("产品数量未发生变化。"),b("update:visible",!1))},U=()=>{D.value=null,C.value=[],N.clear(),g.value=!1,w.value=!1,k.value=!1},P=e=>{if(!e)return"info";switch(e){case"pending":return"warning";case"confirmed":case"delivered":case"completed":return"success";case"production":case"ready_to_ship":case"shipping":return"primary";case"cancelled":return"danger";default:return"info"}},z=e=>{if(!e)return"未知";return{pending:"待确认",confirmed:"已确认",production:"生产中",ready_to_ship:"待发货",shipping:"发货中",delivered:"已送达",completed:"已完成",cancelled:"已取消",draft:"草稿",approved:"已审核"}[e]||e},T=(e,a="CNY",l="zh-CN")=>null==e?"-":new Intl.NumberFormat(l,{style:"currency",currency:a}).format(e);return(a,l)=>{const t=r("el-descriptions-item"),f=r("el-tag"),_=r("el-descriptions"),y=r("el-table-column"),h=r("el-input-number"),b=r("el-table"),N=r("el-button"),I=r("el-dialog"),A=n("loading");return o(),i(I,{"model-value":e.visible,title:"订单产品数量变更",width:"70%","onUpdate:modelValue":l[1]||(l[1]=e=>a.$emit("update:visible",e)),onClosed:U,"destroy-on-close":""},{footer:d((()=>[u(N,{onClick:l[0]||(l[0]=e=>a.$emit("update:visible",!1))},{default:d((()=>l[3]||(l[3]=[s("取消")]))),_:1}),u(N,{type:"primary",onClick:q,loading:k.value},{default:d((()=>l[4]||(l[4]=[s("保存")]))),_:1},8,["loading"])])),default:d((()=>[D.value?(o(),p("div",B,[u(_,{column:2,border:"",class:"mb-20"},{default:d((()=>[u(t,{label:"订单号"},{default:d((()=>[s(v(D.value.order_number),1)])),_:1}),u(t,{label:"客户名称"},{default:d((()=>{var e;return[s(v(null==(e=D.value.customer)?void 0:e.name),1)]})),_:1}),u(t,{label:"项目名称"},{default:d((()=>[s(v(D.value.project_name),1)])),_:1}),u(t,{label:"订单状态"},{default:d((()=>[u(f,{type:P(D.value.status)},{default:d((()=>[s(v(z(D.value.status)),1)])),_:1},8,["type"])])),_:1})])),_:1}),c((o(),i(b,{data:C.value,border:"",stripe:""},{default:d((()=>[u(y,{prop:"product_name",label:"产品名称","min-width":"150"}),u(y,{prop:"product_model",label:"型号","min-width":"100"}),u(y,{label:"规格","min-width":"120"},{default:d((({row:e})=>{var a;return[s(v((null==(a=e.specification)?void 0:a.specification)||"-"),1)]})),_:1}),u(y,{prop:"unit_price",label:"原单价",width:"100"},{default:d((({row:e})=>[s(v(T(e.unit_price)),1)])),_:1}),u(y,{label:"原数量",width:"100"},{default:d((({row:e})=>[s(v(O(e)),1)])),_:1}),u(y,{label:"新数量",width:"150"},{default:d((({row:e})=>[u(h,{modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,min:1,"controls-position":"right",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),u(y,{label:"原小计",width:"120"},{default:d((({row:e})=>[s(v(T(x(e))),1)])),_:1}),u(y,{label:"新小计",width:"120"},{default:d((({row:e})=>[s(v(T(e.unit_price*e.quantity)),1)])),_:1})])),_:1},8,["data"])),[[A,g.value]]),m("div",Q," 总计变更: "+v(T(V.value))+" -> "+v(T(S.value)),1)])):c((o(),p("div",j,l[2]||(l[2]=[s("正在加载订单信息...")]))),[[A,w.value]])])),_:1},8,["model-value"])}}}),[["__scopeId","data-v-2e2787cb"]]),M={key:0,class:"order-detail"},Y={class:"flex-between"},G={class:"info-body"},J={class:"info-item"},W={class:"value"},Z={class:"info-item"},H={class:"value"},K={class:"info-item"},X={class:"value"},ee={class:"info-item"},ae={class:"value"},le={class:"info-item"},te={class:"value"},re={key:1},ne={class:"info-body"},oe={class:"info-item"},ie={class:"value"},de={class:"info-item"},ue={class:"value"},se={class:"info-item"},ce={class:"value"},pe={class:"info-item"},ve={class:"value"},me={class:"info-item"},fe={class:"value"},_e={class:"info-body"},ye={class:"info-item"},he={class:"value highlight"},be={class:"info-item"},ge={class:"value"},we={class:"info-item"},ke={class:"value"},De={class:"info-item"},Ce={class:"value"},Ne={class:"info-item"},Oe={class:"value"},xe={key:0},Ve={class:"delivery-summary mb-20"},Se={class:"flex-between"},qe={key:0},Ie={key:1},Ae={class:"flex-between"},Ue={class:"delivery-info"},Pe={class:"info-item"},ze={class:"value"},Te={class:"info-item"},Ee={class:"value"},$e={class:"info-item"},Fe={class:"value"},Le={class:"mt-10"},je={key:0},Be={class:"mt-20"},Qe={key:0},Re={class:"timeline-content"},Me={key:0},Ye={class:"notes-content"},Ge={class:"card-header flex-between"},Je={key:0},We={class:"dialog-footer"},Ze={class:"dialog-footer"},He={class:"dialog-footer"},Ke={key:1,class:"loading-container"},Xe={key:2,class:"empty-container"},ea=L(e({__name:"OrderDetail",setup(e,{expose:l}){const n="待发出",c="已发出",f="draft",I="pending",L="confirmed",j="in_production",B="ready_to_ship",Q="shipping",ea="partial_shipped",aa="all_shipped",la="pending_statement",ta="partial_statement",ra="all_statement",na="pending_payment",oa="partial_payment",ia="completed",da="cancelled",ua=y(),sa=h(),ca=a(null),pa=a(null),va=a(!0),ma=t((()=>pa.value)),fa=t((()=>{var e;return Boolean(null==(e=ma.value)?void 0:e.id)})),_a=a("products"),ya=a(!1),ha=a(!1),ba=a(!1);a(null);const ga=a(),wa=a(!1),ka=a(null),Da=a(null),Ca=b({status:L,comment:""}),Na=b({order_id:0,amount:0,payment_date:(new Date).toISOString().split("T")[0],payment_method:"bank_transfer",account:"",notes:""}),Oa=e=>{if(null==e)return"N/A";const a=Number(e);return isNaN(a)?"N/A":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY"}).format(a)},xa=async()=>{const e=ca.value;if(!e)return console.warn("OrderDetail: No orderId provided in route params."),_.error("订单ID缺失"),void sa.push({name:"OrderList"});console.log(`OrderDetail: Attempting to fetch order with ID: ${e}`);try{va.value=!0,console.log("OrderDetail: Calling getOrder API...");const l=await A(String(e));if(console.log("OrderDetail: Fetched order data directly:",l),200!==l.code&&0!==l.code||!l.data)console.error("OrderDetail: API returned non-success code or no data:",l),$(l,"加载订单详情失败");else if(pa.value=l.data,console.log("OrderDetail: Order data loaded successfully.",pa.value),console.log("OrderDetail: order.value data structure:",pa.value),console.log("OrderDetail: Statement amount from API:",pa.value.statement_amount),pa.value.products&&pa.value.products.length>0)console.log("OrderDetail: Sample product data:",pa.value.products[0]),console.log("OrderDetail: All products data:",pa.value.products);else{console.warn("OrderDetail: No products data found in order");try{console.log("OrderDetail: Attempting to fetch available products");const a=await U(String(e));a&&(200===a.code||0===a.code)&&a.data&&Array.isArray(a.data)?(console.log("OrderDetail: Available products data:",a.data),a.data.length>0?(pa.value.products=a.data,console.log("OrderDetail: Updated order with available products")):console.warn("OrderDetail: No available products found for this order")):console.warn("OrderDetail: Failed to get available products or invalid data format")}catch(a){console.error("OrderDetail: Error fetching available products:",a)}}}catch(a){console.error("OrderDetail: Error fetching order data:",a),$(a,"加载订单详情失败")}finally{va.value=!1,console.log("OrderDetail: fetchOrderData finished. loading=",va.value)}},Va=e=>{const{columns:a,data:l}=e,t=[];return a.forEach(((e,a)=>{if(0!==a)if("数量"!==e.label)if("金额"!==e.label)t[a]="";else{const e=l.reduce(((e,a)=>e+(Number(a.total_price)||0)),0);t[a]=Oa(e)}else{const e=l.reduce(((e,a)=>e+(Number(a.quantity)||0)),0);t[a]=String(e)}else t[a]="合计"})),t},Sa=e=>({[f]:"info",[I]:"warning",[L]:"success",[j]:"primary",[B]:"success",[Q]:"warning",[ea]:"warning",[aa]:"success",[la]:"info",[ta]:"warning",[ra]:"success",[na]:"info",[oa]:"warning",[ia]:"success",[da]:"danger"}[e]??"info"),qa=e=>({[f]:"草稿",[I]:"待确认",[L]:"已确认",[j]:"生产中",[B]:"待发货",[Q]:"发货中",[ea]:"部分发货",[aa]:"已发货",[la]:"待对账",[ta]:"部分对账",[ra]:"已对账",[na]:"待付款",[oa]:"部分付款",[ia]:"已完成",[da]:"已取消"}[e]??e),Ia=e=>{if(!e)return"info";return{unpaid:"danger",partially_paid:"warning",paid:"success",refunded:"info"}[e]??"info"},Aa=e=>{if(!e)return"未知";return{unpaid:"未付款",partially_paid:"部分付款",paid:"已付清",refunded:"已退款"}[e]??e},Ua=e=>{if(!e)return"N/A";return{bank_transfer:"银行转账",alipay:"支付宝",wechat_pay:"微信支付",check:"支票",cash:"现金",other:"其他"}[e]??e},Pa=()=>{sa.back()},za=()=>{var e;(null==(e=ma.value)?void 0:e.id)?sa.push({name:"OrderEdit",params:{id:ma.value.id.toString()}}):_.error("无法编辑，订单ID不存在")},Ta=async()=>{try{va.value=!0,_.success("正在导出订单数据，请稍候...")}catch(e){F(e,"OrderDetail.handleExport"),$(e,"导出订单失败")}finally{va.value=!1}},Ea=()=>{var e;(null==(e=ma.value)?void 0:e.quotation_id)?sa.push({name:"QuotationDetail",params:{id:ma.value.quotation_id.toString()}}):_.warning("关联报价单ID不存在")},$a=()=>{var e,a,l,t;console.log("OrderDetail: handleApprove called, order status:",null==(e=ma.value)?void 0:e.status),console.log("OrderDetail: ORDER_STATUS.PENDING =",I),console.log("OrderDetail: Status comparison result:",(null==(a=ma.value)?void 0:a.status)===I),console.log("OrderDetail: 直接与中文状态比较:","待确认"===(null==(l=ma.value)?void 0:l.status)),(null==(t=ma.value)?void 0:t.id)?"待确认"===ma.value.status||ma.value.status===I?(Ca.status=L,Ca.comment="",ya.value=!0):_.warning(`只有待确认状态的订单可以进行审核，当前状态: ${ma.value.status}`):_.error("订单数据不存在，无法审核")},Fa=async()=>{var e;if(null==(e=ma.value)?void 0:e.id)if(Ca.status)try{va.value=!0;const e={[L]:"已确认",[da]:"已取消"}[Ca.status]||Ca.status;console.log("提交审核请求，使用状态更新API，状态:",e);const a=await P(ma.value.id.toString(),{status:e,comment:Ca.comment||""});if(200===a.code&&a.data){const e=Ca.status===L?"订单已确认":"订单已拒绝";_.success(e),ya.value=!1,await xa()}else $(a,"订单审核失败")}catch(a){console.error("订单审核失败:",a),$(a,"订单审核失败")}finally{va.value=!1}else _.error("请选择审核结果状态");else _.error("订单信息不完整，无法提交审核")},La=()=>{var e;try{if(!(null==(e=ma.value)?void 0:e.id))throw new Error("订单数据不存在，无法创建发货单");sa.push({name:"DeliveryNoteCreate",params:{orderId:ma.value.id.toString()}})}catch(a){F(a,"OrderDetail.handleCreateDelivery"),$(a,"创建发货单失败")}},ja=()=>{var e;Na.order_id=(null==(e=ma.value)?void 0:e.id)??0,Na.amount=0,Na.payment_date=(new Date).toISOString().split("T")[0],Na.payment_method="bank_transfer",Na.account="",Na.notes="",ha.value=!0},Ba=async()=>{var e;if(null==(e=ma.value)?void 0:e.id)if(!Na.amount||Na.amount<=0)_.error("请输入有效的收款金额");else if(Na.payment_date)if(Na.payment_method)try{va.value=!0;const e={...Na,order_id:ma.value.id},a=await z(ma.value.id,e);200===a.code&&a.data?(_.success("收款记录已添加"),ha.value=!1,await xa()):$(a,"添加收款记录失败")}catch(a){console.error("添加收款记录失败:",a),$(a,"添加收款记录失败")}finally{va.value=!1}else _.error("请选择收款方式");else _.error("请选择收款日期");else _.error("订单数据不存在，无法添加收款记录")},Qa=()=>{ba.value=!0},Ra=async({file:e})=>{var a,l;try{if(va.value=!0,!(null==(a=ma.value)?void 0:a.id))throw new Error("订单数据不存在，无法上传附件");const l=new FormData;l.append("file",e),l.append("order_id",ma.value.id.toString()),_.success("文件上传成功（模拟）")}catch(t){throw F(t,"OrderDetail.handleAttachmentUpload"),413===(null==(l=t.response)?void 0:l.status)?_.error("文件大小超出限制"):$(t,"文件上传失败"),t}},Ma=()=>{_.warning("最多只能上传5个文件")},Ya=async()=>{try{if(!ga.value)return;ga.value.submit(),ba.value=!1,await xa()}catch(e){F(e,"OrderDetail.confirmUpload"),$(e,"文件上传失败")}},Ga=e=>{_.success(`正在下载附件: ${e.file_name}`),e.url?window.open(e.url,"_blank"):_.warning("附件下载链接不可用")},Ja=e=>{N.confirm(`确定要删除附件"${e.file_name}"吗？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{var a;if(null==(a=ma.value)?void 0:a.attachments){const a=ma.value.attachments.findIndex((a=>a.id===e.id));a>-1?(ma.value.attachments.splice(a,1),_.success("附件删除成功（模拟）"),await xa()):_.warning("未找到要删除的附件")}else _.warning("附件列表为空")})).catch((()=>{_.info("已取消删除")}))},Wa=e=>{if(!e)return"N/A";try{const a=new Date(e);return isNaN(a.getTime())?(console.warn("Invalid date string for formatting:",e),e):a.toLocaleDateString("zh-CN")}catch(a){return console.error("Error formatting date:",a),e}},Za=e=>{if(!e)return"N/A";try{const a=new Date(e);return isNaN(a.getTime())?(console.warn("Invalid date time string for formatting:",e),e):a.toLocaleString("zh-CN")}catch(a){return console.error("Error formatting date time:",a),e}},Ha=t((()=>{var e;const a=(null==(e=ma.value)?void 0:e.products)??[];if(!a.length)return{allDelivered:!1,pendingProducts:0};return{allDelivered:a.every((e=>(e.shipped_quantity??0)>=(e.quantity??0))),pendingProducts:a.filter((e=>(e.shipped_quantity??0)<(e.quantity??0))).length}})),Ka=t((()=>{var e;const a=(null==(e=ma.value)?void 0:e.status)??"";if(!a)return!1;const l=[L,j,B,Q,ea],t=Ha.value.pendingProducts>0;return(l.includes(a)||["已确认","生产中","待发货","发货中","部分发货"].includes(a))&&t})),Xa=e=>{if(!e)return"info";return{pending:"warning",delivered:"success",cancelled:"danger"}[e]??"info"},el=e=>{if(!e)return"未知";return{pending:"待发出",delivered:"已发出",cancelled:"已作废"}[e]??e},al=e=>{sa.push({name:"DeliveryNoteDetail",params:{id:e.toString()}})},ll=async()=>{wa.value=!1,await xa(),_.success("产品数量已更新")},tl=()=>{wa.value=!1,ka.value=null,Da.value=null},rl=e=>null==e?"13.00":e>1?e.toFixed(2):(100*e).toFixed(2);g((async()=>{const e=ua.params.id;if(!e)return _.error("未找到订单ID"),void sa.push({name:"OrderList"});const a=Array.isArray(e)?e[0]:e,l=Number(a);if(isNaN(l))return _.error("无效的订单ID格式"),void sa.push({name:"OrderList"});ca.value=l,await xa(),"true"===ua.query.openApprove&&il.value&&(console.log("OrderDetail: Auto opening approve dialog from URL parameter"),$a())}));const nl=t((()=>{var e;const a=(null==(e=ma.value)?void 0:e.status)??"";return console.log("OrderDetail: Current order status is",a),a})),ol=t((()=>nl.value===f||"草稿"===nl.value)),il=t((()=>{const e=nl.value===I||"待确认"===nl.value;return console.log("OrderDetail: canApprove =",e,"for status",nl.value),e}));return l({isOrderLoaded:fa,orderInfo:ma,activeTab:_a,orderProductSpanMethod:({row:e,column:a})=>{var l,t,r;if(a.property&&["product_id","product_name","product_model"].includes(a.property)){const a=(null==(l=ma.value)?void 0:l.products)||[],n=a.findIndex((a=>a.id===e.id));if(n>-1){const e=a[n],l=a[n-1];if(l&&l.product_id===e.product_id&&l.product_model===e.product_model)return{rowspan:0,colspan:0};{let l=1;for(let o=n+1;o<a.length&&((null==(t=a[o])?void 0:t.product_id)===e.product_id&&(null==(r=a[o])?void 0:r.product_model)===e.product_model);o++)l++;return{rowspan:l,colspan:1}}}}return{}},getSummaries:Va,getStatusType:Sa,getStatusLabel:qa,formatDate:Wa,viewQuotation:Ea,getPaymentStatusType:Ia,getPaymentStatusLabel:Aa,formatCurrency:Oa,formatTaxRate:rl,deliveryProgress:Ha,handleCreateDelivery:La,getDeliveryStatusType:Xa,viewDeliveryNote:al,getPaymentMethodLabel:Ua,downloadAttachment:Ga,deleteAttachment:Ja,goBack:Pa,handleEdit:za,handleExport:Ta,handleApprove:$a,handleShowProductQuantityDialog:e=>{var a;ka.value=e??(null==(a=ma.value)?void 0:a.id)??null,Da.value=ma.value,wa.value=!0},isProductQuantityDialogVisible:wa,selectedOrderIdForQuantityDialog:ka,selectedOrderForQuantityDialog:Da,handleProductQuantityDialogSaved:ll,handleProductQuantityDialogClosed:tl,canEdit:ol,canApprove:il,canCreateDeliveryNote:Ka}),(e,a)=>{const l=r("el-button"),t=r("el-card"),f=r("el-step"),y=r("el-steps"),h=r("el-tag"),b=r("el-link"),g=r("el-col"),A=r("el-row"),U=r("el-table-column"),P=r("el-table"),z=r("el-tab-pane"),F=r("el-icon"),ua=r("el-alert"),ca=r("el-space"),pa=r("el-descriptions-item"),nl=r("el-descriptions"),dl=r("el-empty"),ul=r("el-timeline-item"),sl=r("el-timeline"),cl=r("el-button-group"),pl=r("el-tabs"),vl=r("el-radio"),ml=r("el-radio-group"),fl=r("el-form-item"),_l=r("el-input"),yl=r("el-form"),hl=r("el-dialog"),bl=r("el-input-number"),gl=r("el-date-picker"),wl=r("el-option"),kl=r("el-select"),Dl=r("el-upload"),Cl=r("el-skeleton");return fa.value?(o(),p("div",M,[u(t,{class:"header-card mb-20"},{default:d((()=>[m("div",Y,[a[20]||(a[20]=m("h2",{class:"form-title"},"订单详情",-1)),m("div",null,[u(l,{onClick:Pa},{default:d((()=>a[15]||(a[15]=[s("返回")]))),_:1}),ol.value?(o(),i(l,{key:0,type:"primary",onClick:za},{default:d((()=>a[16]||(a[16]=[s("编辑")]))),_:1})):w("",!0),u(l,{type:"success",onClick:Ta},{default:d((()=>a[17]||(a[17]=[s("导出订单")]))),_:1}),il.value?(o(),i(l,{key:1,type:"warning",onClick:$a},{default:d((()=>a[18]||(a[18]=[s("审核")]))),_:1})):w("",!0),Ka.value?(o(),i(l,{key:2,type:"info",onClick:La},{default:d((()=>a[19]||(a[19]=[s("创建发货单")]))),_:1})):w("",!0)])])])),_:1}),u(t,{class:"mb-20"},{default:d((()=>{var e,a;return[u(y,{active:(a=(null==(e=ma.value)?void 0:e.status)??"",{[I]:0,[L]:1,[j]:2,[B]:3,[Q]:4,[aa]:5,[ra]:6,[ia]:7,[da]:-1,[ea]:4,[ta]:5,[la]:5,[na]:6,[oa]:6}[a]??0),"finish-status":"success",simple:""},{default:d((()=>[u(f,{title:"待确认"}),u(f,{title:"已确认"}),u(f,{title:"生产中"}),u(f,{title:"待发货"}),u(f,{title:"发货中"}),u(f,{title:"已发货"}),u(f,{title:"已对账"}),u(f,{title:"已结清"})])),_:1},8,["active"])]})),_:1}),u(A,{gutter:20,class:"mb-20"},{default:d((()=>[u(g,{span:8},{default:d((()=>[u(t,{class:"info-card"},{header:d((()=>a[21]||(a[21]=[m("div",{class:"info-header"},[m("span",null,"基本信息")],-1)]))),default:d((()=>{var e,l,t,r,n,c;return[m("div",G,[m("div",J,[a[22]||(a[22]=m("span",{class:"label"},"订单号:",-1)),m("span",W,v((null==(e=ma.value)?void 0:e.order_number)??""),1)]),m("div",Z,[a[23]||(a[23]=m("span",{class:"label"},"订单状态:",-1)),m("span",H,[u(h,{type:Sa((null==(l=ma.value)?void 0:l.status)??"")},{default:d((()=>{var e;return[s(v(qa((null==(e=ma.value)?void 0:e.status)??"")),1)]})),_:1},8,["type"])])]),m("div",K,[a[24]||(a[24]=m("span",{class:"label"},"订单日期:",-1)),m("span",X,v((null==(t=ma.value)?void 0:t.created_at)?Wa(ma.value.created_at):(new Date).toLocaleDateString("zh-CN")),1)]),m("div",ee,[a[25]||(a[25]=m("span",{class:"label"},"预计交期:",-1)),m("span",ae,v((null==(r=ma.value)?void 0:r.expected_date)?Wa(ma.value.expected_date):""),1)]),m("div",le,[a[26]||(a[26]=m("span",{class:"label"},"关联报价单:",-1)),m("span",te,[(null==(c=null==(n=ma.value)?void 0:n.quotation)?void 0:c.quotation_number)?(o(),i(b,{key:0,type:"primary",onClick:Ea},{default:d((()=>{var e,a;return[s(v((null==(a=null==(e=ma.value)?void 0:e.quotation)?void 0:a.quotation_number)??""),1)]})),_:1})):(o(),p("span",re,"无"))])])])]})),_:1})])),_:1}),u(g,{span:8},{default:d((()=>[u(t,{class:"info-card"},{header:d((()=>a[27]||(a[27]=[m("div",{class:"info-header"},[m("span",null,"客户信息")],-1)]))),default:d((()=>{var e,l,t,r,n,o,i,d;return[m("div",ne,[m("div",oe,[a[28]||(a[28]=m("span",{class:"label"},"客户名称:",-1)),m("span",ie,v((null==(l=null==(e=ma.value)?void 0:e.customer)?void 0:l.name)??""),1)]),m("div",de,[a[29]||(a[29]=m("span",{class:"label"},"联系人:",-1)),m("span",ue,v((null==(r=null==(t=ma.value)?void 0:t.customer)?void 0:r.contact)??""),1)]),m("div",se,[a[30]||(a[30]=m("span",{class:"label"},"联系电话:",-1)),m("span",ce,v((null==(o=null==(n=ma.value)?void 0:n.customer)?void 0:o.phone)??""),1)]),m("div",pe,[a[31]||(a[31]=m("span",{class:"label"},"项目名称:",-1)),m("span",ve,v((null==(i=ma.value)?void 0:i.project_name)??""),1)]),m("div",me,[a[32]||(a[32]=m("span",{class:"label"},"项目地址:",-1)),m("span",fe,v((null==(d=ma.value)?void 0:d.project_address)??""),1)])])]})),_:1})])),_:1}),u(g,{span:8},{default:d((()=>[u(t,{class:"info-card"},{header:d((()=>a[33]||(a[33]=[m("div",{class:"info-header"},[m("span",null,"收款信息")],-1)]))),default:d((()=>{var e,l,t,r,n,o;return[m("div",_e,[m("div",ye,[a[34]||(a[34]=m("span",{class:"label"},"订单金额:",-1)),m("span",he,v(Oa(Number((null==(e=ma.value)?void 0:e.total_amount)??0))),1)]),m("div",be,[a[35]||(a[35]=m("span",{class:"label"},"已收款:",-1)),m("span",ge,v(Oa((null==(l=ma.value)?void 0:l.paid_amount)??0)),1)]),m("div",we,[a[36]||(a[36]=m("span",{class:"label"},"待收款:",-1)),m("span",ke,v(Oa(Number((null==(t=ma.value)?void 0:t.total_amount)??0)-((null==(r=ma.value)?void 0:r.paid_amount)??0))),1)]),m("div",De,[a[37]||(a[37]=m("span",{class:"label"},"收款状态:",-1)),m("span",Ce,[u(h,{type:Ia(null==(n=ma.value)?void 0:n.payment_status)},{default:d((()=>{var e;return[s(v(Aa(null==(e=ma.value)?void 0:e.payment_status)),1)]})),_:1},8,["type"])])]),m("div",Ne,[a[38]||(a[38]=m("span",{class:"label"},"付款条件:",-1)),m("span",Oe,v((null==(o=ma.value)?void 0:o.payment_terms)??""),1)])])]})),_:1})])),_:1})])),_:1}),u(t,null,{default:d((()=>[u(pl,{modelValue:_a.value,"onUpdate:modelValue":a[0]||(a[0]=e=>_a.value=e)},{default:d((()=>[u(z,{label:"产品明细",name:"products"},{default:d((()=>{var e;return[u(P,{data:null==(e=ma.value)?void 0:e.products,border:"",stripe:"","show-summary":"","summary-method":Va},{default:d((()=>[u(U,{label:"编号",width:"80"},{default:d((({row:e})=>[s(v(e.product_id||e.id||""),1)])),_:1}),u(U,{label:"产品名称","min-width":"180"},{default:d((({row:e})=>[s(v(e.product_name||""),1)])),_:1}),u(U,{label:"型号",width:"120"},{default:d((({row:e})=>[s(v(e.product_model||""),1)])),_:1}),u(U,{label:"规格","min-width":"150"},{default:d((({row:e})=>{var a;return[s(v(e.specification_description||(null==(a=e.product_specification)?void 0:a.specification)||e.specification_name||""),1)]})),_:1}),u(U,{label:"单位",width:"80"},{default:d((({row:e})=>[s(v(e.product_unit||e.unit||""),1)])),_:1}),u(U,{label:"数量",width:"80"},{default:d((({row:e})=>[s(v(e.quantity||0),1)])),_:1}),u(U,{label:"单价",width:"120"},{default:d((({row:e})=>[s(v(Oa(e.unit_price)),1)])),_:1}),u(U,{label:"税率(%)",width:"80"},{default:d((({row:e})=>[s(v(rl(e.tax_rate)||"13.00"),1)])),_:1}),u(U,{label:"金额",width:"120"},{default:d((({row:e})=>[s(v(Oa(e.total_price)),1)])),_:1})])),_:1},8,["data"])]})),_:1}),u(z,{label:"送货信息",name:"delivery"},{default:d((()=>{var e,r;return[((null==(e=ma.value)?void 0:e.delivery_notes)||[]).length>0?(o(),p("div",xe,[m("div",Ve,[u(ua,{type:"info",closable:!1,"show-icon":""},{default:d((()=>{var e;return[m("div",Se,[m("div",null,[a[39]||(a[39]=m("strong",null,"发货情况：",-1)),s(" 共 "+v(((null==(e=ma.value)?void 0:e.delivery_notes)||[]).length)+" 个发货单， ",1),Ha.value.allDelivered?(o(),p("span",qe,"全部产品已发货完成")):(o(),p("span",Ie,"部分产品待发货 ("+v(Ha.value.pendingProducts)+" 个产品)",1))]),m("div",null,[Ka.value?(o(),i(l,{key:0,type:"primary",size:"small",onClick:La},{default:d((()=>[u(F,null,{default:d((()=>[u(k(O))])),_:1}),a[40]||(a[40]=s(" 继续发货 "))])),_:1})):w("",!0)])])]})),_:1})]),(o(!0),p(D,null,C(null==(r=ma.value)?void 0:r.delivery_notes,((e,r)=>(o(),p("div",{key:e.id,class:"delivery-note"},[u(t,{class:"mb-20"},{header:d((()=>[m("div",Ae,[m("span",null,"发货单 #"+v(r+1)+": "+v(e.delivery_number||`发货单-${e.id}`),1),m("div",null,[u(h,{type:Xa(e.status),size:"small"},{default:d((()=>[s(v(el(e.status)),1)])),_:2},1032,["type"]),u(ca,null,{default:d((()=>[u(l,{type:"primary",size:"small",link:"",onClick:a=>al(Number(e.id))},{default:d((()=>a[41]||(a[41]=[s(" 查看详情 ")]))),_:2},1032,["onClick"]),e.status===n?(o(),i(l,{key:0,type:"warning",size:"small",link:"",onClick:a=>{return l=Number(e.id),void sa.push({name:"DeliveryNoteEdit",params:{id:l.toString()}});var l}},{default:d((()=>a[42]||(a[42]=[s(" 编辑 ")]))),_:2},1032,["onClick"])):w("",!0),e.status===n?(o(),i(l,{key:1,type:"danger",size:"small",link:"",onClick:a=>(async e=>{try{await N.confirm("确认取消此发货单？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"});const a=await E(e.toString());200===a.code?(_.success("发货单已取消"),await xa()):$(a,"取消发货单失败")}catch(a){"cancel"!==a&&(console.error("取消发货单失败:",a),$(a,"操作失败，请重试"))}})(Number(e.id))},{default:d((()=>a[43]||(a[43]=[s(" 取消 ")]))),_:2},1032,["onClick"])):w("",!0),e.status===n?(o(),i(l,{key:2,type:"success",size:"small",link:"",onClick:a=>(async e=>{try{await N.confirm("确认此发货单已发出？","确认操作",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"});const a=await T(e.toString(),{status:c});200===a.code&&a.data?(_.success("发货单状态已更新"),await xa()):$(a,"确认发货单发出失败")}catch(a){"cancel"!==a&&(console.error("确认发货单发出失败:",a),$(a,"操作失败，请重试"))}})(Number(e.id))},{default:d((()=>a[44]||(a[44]=[s(" 确认发出 ")]))),_:2},1032,["onClick"])):w("",!0),u(l,{type:"primary",size:"small",link:"",onClick:a=>(Number(e.id),void _.info("打印功能开发中..."))},{default:d((()=>a[45]||(a[45]=[s(" 打印 ")]))),_:2},1032,["onClick"])])),_:2},1024)])])])),default:d((()=>[m("div",Ue,[m("div",Pe,[a[46]||(a[46]=m("span",{class:"label"},"收货人:",-1)),m("span",ze,v(e.recipient_name||"未指定"),1)]),m("div",Te,[a[47]||(a[47]=m("span",{class:"label"},"联系电话:",-1)),m("span",Ee,v(e.recipient_phone||"未指定"),1)]),m("div",$e,[a[48]||(a[48]=m("span",{class:"label"},"送货地址:",-1)),m("span",Fe,v(e.delivery_address_snapshot||"未指定"),1)])]),u(A,{gutter:20,class:"mt-10"},{default:d((()=>[u(g,{span:12},{default:d((()=>[u(nl,{column:1,size:"small",border:""},{default:d((()=>[u(pa,{label:"发货日期"},{default:d((()=>[s(v(Wa(e.delivery_date)),1)])),_:2},1024),u(pa,{label:"配送方式"},{default:d((()=>[s(v("self"===e.delivery_method?"自主配送":"物流配送"),1)])),_:2},1024),u(pa,{label:"物流公司"},{default:d((()=>[s(v(e.logistics_company||"N/A"),1)])),_:2},1024),u(pa,{label:"物流单号"},{default:d((()=>[s(v(e.tracking_number||"N/A"),1)])),_:2},1024),u(pa,{label:"创建时间"},{default:d((()=>[s(v(Za(e.created_at)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),u(g,{span:12},{default:d((()=>[u(nl,{column:1,size:"small",border:""},{default:d((()=>[u(pa,{label:"备注"},{default:d((()=>[s(v(e.notes||"无"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),m("div",Le,[u(P,{data:e.products||e.items||[],border:"",size:"small",style:{width:"100%"}},{default:d((()=>[u(U,{type:"index",width:"50",label:"#"}),u(U,{prop:"product_name",label:"产品名称","min-width":"180"}),u(U,{prop:"product_model",label:"型号",width:"100"}),u(U,{label:"规格","min-width":"120"},{default:d((({row:e})=>[s(v(e.specification||e.specification_description||""),1)])),_:1}),u(U,{prop:"quantity",label:"发货数量",width:"100"}),u(U,{label:"单位",width:"80"},{default:d((({row:e})=>[s(v(e.unit||e.product_unit||"个"),1)])),_:1}),u(U,{prop:"notes",label:"备注","min-width":"150","show-overflow-tooltip":""})])),_:2},1032,["data"])])])),_:2},1024)])))),128))])):(o(),i(dl,{key:1,description:"暂无送货记录"},{description:d((()=>[m("div",null,[a[50]||(a[50]=m("p",null,"该订单尚未创建送货单",-1)),Ka.value?(o(),i(l,{key:0,type:"primary",onClick:La,class:"mt-10"},{default:d((()=>[u(F,null,{default:d((()=>[u(k(O))])),_:1}),a[49]||(a[49]=s(" 创建送货单 "))])),_:1})):w("",!0)])])),_:1}))]})),_:1}),u(z,{label:"收款记录",name:"payments"},{default:d((()=>{var e,t;return[((null==(e=ma.value)?void 0:e.payment_records)||[]).length>0?(o(),p("div",je,[u(P,{data:null==(t=ma.value)?void 0:t.payment_records,border:"",stripe:""},{default:d((()=>[u(U,{type:"index",width:"50"}),u(U,{prop:"payment_date",label:"收款日期",width:"120"}),u(U,{prop:"amount",label:"收款金额",width:"120"},{default:d((({row:e})=>[s(v(Oa(e.amount)),1)])),_:1}),u(U,{prop:"payment_method",label:"收款方式",width:"120"},{default:d((({row:e})=>[s(v(Ua(e.payment_method)),1)])),_:1}),u(U,{prop:"account",label:"收款账户","min-width":"180"}),u(U,{prop:"operator",label:"操作人",width:"100"}),u(U,{prop:"notes",label:"备注","min-width":"200","show-overflow-tooltip":""})])),_:1},8,["data"])])):(o(),i(dl,{key:1,description:"暂无收款记录"})),m("div",Be,[u(l,{type:"primary",onClick:ja},{default:d((()=>[u(F,null,{default:d((()=>[u(k(O))])),_:1}),a[51]||(a[51]=s(" 添加收款记录 "))])),_:1})])]})),_:1}),u(z,{label:"状态历史",name:"history"},{default:d((()=>{var e;return[((null==(e=ma.value)?void 0:e.status_history)||[]).length>0?(o(),p("div",Qe,[u(sl,null,{default:d((()=>{var e;return[(o(!0),p(D,null,C(null==(e=ma.value)?void 0:e.status_history,((e,a)=>(o(),i(ul,{key:a,timestamp:Wa(e.created_at),placement:"top"},{default:d((()=>[m("div",Re,[m("h4",null,v(qa(e.status)),1),e.comment?(o(),p("p",Me,"备注: "+v(e.comment),1)):w("",!0)])])),_:2},1032,["timestamp"])))),128))]})),_:1})])):(o(),i(dl,{key:1,description:"暂无状态历史"}))]})),_:1}),u(z,{label:"备注与附件",name:"notes"},{default:d((()=>[u(t,{class:"mb-20"},{header:d((()=>a[52]||(a[52]=[m("div",{class:"card-header"},[m("span",null,"订单备注")],-1)]))),default:d((()=>{var e;return[m("div",Ye,v((null==(e=ma.value)?void 0:e.notes)||"暂无备注"),1)]})),_:1}),u(t,null,{header:d((()=>[m("div",Ge,[a[54]||(a[54]=m("span",null,"订单附件",-1)),u(l,{type:"primary",size:"small",onClick:Qa},{default:d((()=>[u(F,null,{default:d((()=>[u(k(x))])),_:1}),a[53]||(a[53]=s(" 上传附件 "))])),_:1})])])),default:d((()=>{var e,t;return[((null==(e=ma.value)?void 0:e.attachments)||[]).length>0?(o(),p("div",Je,[u(P,{data:null==(t=ma.value)?void 0:t.attachments,border:"",stripe:""},{default:d((()=>[u(U,{type:"index",width:"50"}),u(U,{prop:"file_name",label:"文件名","min-width":"200"}),u(U,{prop:"file_size",label:"大小",width:"100"}),u(U,{prop:"upload_time",label:"上传时间",width:"150"}),u(U,{prop:"uploader",label:"上传人",width:"100"}),u(U,{label:"操作",width:"150"},{default:d((({row:e})=>[u(cl,null,{default:d((()=>[u(l,{type:"primary",link:"",onClick:a=>Ga(e)},{default:d((()=>[u(F,null,{default:d((()=>[u(k(V))])),_:1}),a[55]||(a[55]=s(" 下载 "))])),_:2},1032,["onClick"]),u(l,{type:"danger",link:"",onClick:a=>Ja(e)},{default:d((()=>[u(F,null,{default:d((()=>[u(k(S))])),_:1}),a[56]||(a[56]=s(" 删除 "))])),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["data"])])):(o(),i(dl,{key:1,description:"暂无附件"}))]})),_:1})])),_:1})])),_:1},8,["modelValue"])])),_:1}),u(hl,{modelValue:ya.value,"onUpdate:modelValue":a[4]||(a[4]=e=>ya.value=e),title:"订单审核",width:"500px"},{footer:d((()=>[m("span",We,[u(l,{onClick:a[3]||(a[3]=e=>ya.value=!1)},{default:d((()=>a[59]||(a[59]=[s("取消")]))),_:1}),u(l,{type:"primary",onClick:Fa,loading:va.value},{default:d((()=>a[60]||(a[60]=[s(" 确认 ")]))),_:1},8,["loading"])])])),default:d((()=>[u(yl,{model:Ca,"label-width":"100px"},{default:d((()=>[u(fl,{label:"审核结果"},{default:d((()=>[u(ml,{modelValue:Ca.status,"onUpdate:modelValue":a[1]||(a[1]=e=>Ca.status=e)},{default:d((()=>[u(vl,{label:L},{default:d((()=>a[57]||(a[57]=[s("通过")]))),_:1},8,["label"]),u(vl,{label:da},{default:d((()=>a[58]||(a[58]=[s("拒绝")]))),_:1},8,["label"])])),_:1},8,["modelValue"])])),_:1}),u(fl,{label:"审核意见"},{default:d((()=>[u(_l,{modelValue:Ca.comment,"onUpdate:modelValue":a[2]||(a[2]=e=>Ca.comment=e),type:"textarea",rows:3,placeholder:"请输入审核意见"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),u(hl,{modelValue:ha.value,"onUpdate:modelValue":a[11]||(a[11]=e=>ha.value=e),title:"添加收款记录",width:"500px"},{footer:d((()=>[m("span",Ze,[u(l,{onClick:a[10]||(a[10]=e=>ha.value=!1)},{default:d((()=>a[61]||(a[61]=[s("取消")]))),_:1}),u(l,{type:"primary",onClick:Ba,loading:va.value},{default:d((()=>a[62]||(a[62]=[s(" 确认 ")]))),_:1},8,["loading"])])])),default:d((()=>[u(yl,{model:Na,"label-width":"100px"},{default:d((()=>[u(fl,{label:"收款金额",required:""},{default:d((()=>[u(bl,{modelValue:Na.amount,"onUpdate:modelValue":a[5]||(a[5]=e=>Na.amount=e),min:0,precision:2,step:100},null,8,["modelValue"])])),_:1}),u(fl,{label:"收款日期",required:""},{default:d((()=>[u(gl,{modelValue:Na.payment_date,"onUpdate:modelValue":a[6]||(a[6]=e=>Na.payment_date=e),type:"date",placeholder:"选择日期"},null,8,["modelValue"])])),_:1}),u(fl,{label:"收款方式",required:""},{default:d((()=>[u(kl,{modelValue:Na.payment_method,"onUpdate:modelValue":a[7]||(a[7]=e=>Na.payment_method=e)},{default:d((()=>[u(wl,{label:"银行转账",value:"bank_transfer"}),u(wl,{label:"现金",value:"cash"}),u(wl,{label:"支票",value:"check"}),u(wl,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1}),u(fl,{label:"收款账户"},{default:d((()=>[u(_l,{modelValue:Na.account,"onUpdate:modelValue":a[8]||(a[8]=e=>Na.account=e),placeholder:"请输入收款账户信息"},null,8,["modelValue"])])),_:1}),u(fl,{label:"备注"},{default:d((()=>[u(_l,{modelValue:Na.notes,"onUpdate:modelValue":a[9]||(a[9]=e=>Na.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),u(hl,{modelValue:ba.value,"onUpdate:modelValue":a[13]||(a[13]=e=>ba.value=e),title:"上传附件",width:"500px","destroy-on-close":""},{footer:d((()=>[m("div",He,[u(l,{onClick:a[12]||(a[12]=e=>ba.value=!1)},{default:d((()=>a[65]||(a[65]=[s("取消")]))),_:1}),u(l,{type:"primary",onClick:Ya},{default:d((()=>a[66]||(a[66]=[s("开始上传")]))),_:1})])])),default:d((()=>[u(Dl,{ref_key:"uploadRef",ref:ga,class:"upload-demo",drag:"",action:"#","http-request":Ra,"auto-upload":!1,limit:5,multiple:"","on-exceed":Ma},{tip:d((()=>a[63]||(a[63]=[m("div",{class:"el-upload__tip"}," 单个文件大小不超过10MB, 最多上传5个文件。 ",-1)]))),default:d((()=>[u(F,{class:"el-icon--upload"},{default:d((()=>[u(k(q))])),_:1}),a[64]||(a[64]=m("div",{class:"el-upload__text"},[s("拖拽文件到此处或 "),m("em",null,"点击上传")],-1))])),_:1},512)])),_:1},8,["modelValue"]),u(R,{visible:wa.value,"onUpdate:visible":a[14]||(a[14]=e=>wa.value=e),"order-id":ka.value,"order-data":Da.value,onSaved:ll,onClosed:tl},null,8,["visible","order-id","order-data"])])):va.value?(o(),p("div",Ke,[u(Cl,{rows:10,animated:""})])):(o(),p("div",Xe,[u(dl,{description:"订单信息加载失败或不存在"}),u(l,{onClick:Pa},{default:d((()=>a[67]||(a[67]=[s("返回列表")]))),_:1})]))}}}),[["__scopeId","data-v-28bb9cd7"]]);export{ea as default};
