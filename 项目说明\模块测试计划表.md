# EMB项目前端模块测试清单

## 📋 测试概述

### 🎯 前端测试目标
- 验证8个核心模块的前端功能完整性
- 确保用户界面交互正常
- 验证前端与后端API对接正确性
- 确保响应式设计和用户体验

### 📊 前端测试范围
- **页面功能测试**: 所有页面和组件
- **用户交互测试**: 表单、按钮、导航
- **数据展示测试**: 列表、详情、图表
- **响应式测试**: 不同屏幕尺寸适配
- **浏览器兼容测试**: 主流浏览器支持

## � 前端模块测试清单

### 🚀 第1阶段：基础环境测试

#### 环境准备检查清单
- [x] 🔴 **前端服务启动** - 访问 http://localhost:3001 确认页面正常加载 ✅
- [x] 🔴 **后端服务连接** - 确认前端能正常调用后端API (http://localhost:5001) ✅
- [x] � **路由导航** - 测试主要页面路由跳转正常
- [x] � **样式加载** - 确认ElementPlus样式和自定义样式正常加载
- [x] 🟡 **图标显示** - 检查所有图标是否正常显示 ✅

### 📱 第2阶段：核心前端模块测试

#### 🏠 2.1 工作台/仪表板模块 (路径: /dashboard)

**页面功能测试**
- [x] 🔴 **页面加载** - Dashboard.vue 页面正常渲染，无白屏或错误 ✅
- [x] 🔴 **统计卡片** - 显示客户数量、产品数量、订单数量、销售额等统计数据 ✅
- [x] 🔴 **图表展示** - ECharts图表正常渲染（销售趋势、产品分布等） ✅
- [x] 🟡 **数据刷新** - 点击刷新按钮能重新加载数据 ✅
- [x] 🟡 **响应式布局** - 在不同屏幕尺寸下布局正常 ✅

**交互功能测试**
- [x] 🔴 **导航跳转** - 点击统计卡片能跳转到对应模块页面 ✅
- [x] 🟡 **图表交互** - 图表支持鼠标悬停显示详细数据 ✅
- [x] � **时间筛选** - 如有时间筛选器，测试筛选功能正常

#### 👥 2.2 客户管理模块 (路径: /customers)

**客户列表页面测试**
- [ ] 🔴 **列表显示** - CustomerList.vue 正常显示客户列表
- [ ] 🔴 **分页功能** - 分页组件正常工作，能切换页码
- [ ] 🔴 **搜索功能** - 客户名称、联系人搜索功能正常
- [ ] 🔴 **操作按钮** - 新增、编辑、删除、查看按钮正常显示和点击
- [ ] 🟡 **排序功能** - 点击表头能按字段排序
- [ ] 🟡 **筛选功能** - 如有筛选条件，测试筛选正常

**客户表单页面测试**
- [ ] 🔴 **表单显示** - CustomerForm.vue 表单字段正常显示
- [ ] 🔴 **必填验证** - 客户名称、联系人等必填字段验证
- [ ] 🔴 **格式验证** - 手机号、邮箱格式验证正常
- [ ] 🔴 **提交功能** - 表单提交成功后跳转到列表页
- [ ] � **重置功能** - 重置按钮能清空表单数据
- [ ] 🟡 **取消功能** - 取消按钮能返回列表页

**客户详情页面测试**
- [ ] 🔴 **详情显示** - CustomerDetail.vue 正常显示客户详细信息
- [ ] 🟡 **编辑跳转** - 点击编辑按钮跳转到编辑页面
- [ ] 🟡 **关联数据** - 显示客户相关的订单、报价等信息

#### 📦 2.3 产品管理模块 (路径: /products)

**产品列表页面测试**
- [ ] 🔴 **列表显示** - ProductList.vue 正常显示产品列表
- [ ] 🔴 **分类筛选** - 产品分类下拉筛选功能正常
- [ ] 🔴 **搜索功能** - 产品名称、型号搜索功能正常
- [ ] 🔴 **操作按钮** - 新增、编辑、删除、查看按钮正常
- [ ] 🟡 **品牌筛选** - 品牌筛选功能正常
- [ ] 🟡 **批量操作** - 批量删除、批量导出等功能

**产品表单页面测试**
- [ ] 🔴 **表单显示** - ProductForm.vue 表单字段正常显示
- [ ] � **分类选择** - 产品分类下拉选择正常
- [ ] 🔴 **必填验证** - 产品名称、型号等必填字段验证
- [ ] 🔴 **数据提交** - 表单提交成功保存产品信息
- [ ] 🟡 **图片上传** - 如有图片上传功能，测试上传正常
- [ ] 🟡 **规格参数** - 产品规格参数录入功能

**分类和品牌管理测试**
- [ ] � **分类管理** - 产品分类的增删改查功能
- [ ] 🟡 **品牌管理** - 产品品牌的增删改查功能

#### 💰 2.4 报价管理模块 (路径: /quotations)

**报价单列表页面测试**
- [ ] 🔴 **列表显示** - QuotationList.vue 正常显示报价单列表
- [ ] 🔴 **状态筛选** - 按报价单状态筛选（草稿、已发送、已确认等）
- [ ] 🔴 **客户筛选** - 按客户筛选报价单
- [ ] 🔴 **操作按钮** - 新增、编辑、删除、查看、转订单按钮
- [ ] 🟡 **时间筛选** - 按创建时间、有效期筛选
- [ ] 🟡 **状态标识** - 不同状态的报价单有明显的视觉区分

**报价单表单页面测试**
- [ ] 🔴 **基本信息** - QuotationForm.vue 客户选择、报价日期等基本信息
- [ ] 🔴 **产品明细** - 产品选择、数量、单价、小计计算
- [ ] 🔴 **金额计算** - 总金额、税额、优惠等自动计算
- [ ] 🔴 **保存功能** - 保存草稿和提交功能正常
- [ ] � **产品搜索** - 在明细中搜索和选择产品
- [ ] 🟡 **明细操作** - 添加、删除、修改明细行

**报价单详情和转换测试**
- [ ] 🔴 **详情显示** - QuotationDetail.vue 完整显示报价单信息
- [ ] 🔴 **转订单功能** - 报价单转换为订单功能正常
- [ ] 🟡 **打印功能** - 报价单打印或导出PDF功能
- [ ] 🟡 **状态更新** - 报价单状态更新功能

#### 📋 2.5 订单管理模块 (路径: /orders)

**订单列表页面测试**
- [ ] 🔴 **列表显示** - OrderList.vue 正常显示订单列表
- [ ] 🔴 **状态筛选** - 按订单状态筛选（待确认、已确认、生产中、已发货等）
- [ ] 🔴 **时间筛选** - 按下单时间、交货时间筛选
- [ ] 🔴 **客户筛选** - 按客户筛选订单
- [ ] 🔴 **操作按钮** - 新增、编辑、查看、状态更新按钮
- [ ] � **金额显示** - 订单金额、已付金额、欠款等信息显示

**订单表单页面测试**
- [ ] 🔴 **基本信息** - OrderForm.vue 客户选择、订单日期等
- [ ] 🔴 **产品明细** - 从报价单导入或手动添加产品明细
- [ ] 🔴 **金额计算** - 订单总金额、税额等自动计算
- [ ] 🔴 **保存功能** - 订单保存和提交功能
- [ ] 🟡 **交货信息** - 交货地址、交货日期设置

**订单详情页面测试**
- [ ] 🔴 **详情显示** - OrderDetail.vue 完整显示订单信息
- [ ] 🔴 **状态更新** - 订单状态流转功能（确认→生产→发货→完成）
- [ ] 🟡 **关联信息** - 显示相关的报价单、发货单、对账单
- [ ] 🟡 **操作记录** - 订单操作历史记录

#### 🚚 2.6 发货管理模块 (路径: /delivery)

**发货单列表页面测试**
- [ ] 🟡 **列表显示** - DeliveryList.vue 正常显示发货单列表
- [ ] 🟡 **订单关联** - 显示关联的订单信息
- [ ] 🟡 **状态筛选** - 按发货状态筛选
- [ ] 🟡 **操作按钮** - 新增、编辑、查看、打印按钮

**发货单表单页面测试**
- [ ] 🟡 **订单选择** - DeliveryForm.vue 选择待发货订单
- [ ] 🟡 **发货明细** - 发货产品和数量录入
- [ ] 🟡 **物流信息** - 物流公司、运单号等信息
- [ ] 🟡 **数量验证** - 发货数量不能超过订单数量

**退货管理测试**
- [ ] 🟡 **退货单列表** - 退货单列表显示
- [ ] 🟡 **退货申请** - 创建退货申请功能
- [ ] 🟡 **退货处理** - 退货审核和处理流程

#### 💳 2.7 财务管理模块 (路径: /finance)

**对账单管理测试**
- [ ] 🟡 **对账单列表** - StatementList.vue 显示对账单列表
- [ ] 🟡 **客户筛选** - 按客户筛选对账单
- [ ] 🟡 **时间筛选** - 按对账期间筛选
- [ ] 🟡 **生成对账单** - 自动生成客户对账单功能

**财务统计测试**
- [ ] 🟡 **收入统计** - 销售收入统计图表
- [ ] 🟡 **应收账款** - 应收账款统计和明细
- [ ] 🟡 **利润分析** - 利润分析图表和数据
- [ ] 🟡 **导出功能** - 财务报表导出功能

#### ⚙️ 2.8 系统设置模块 (路径: /system)

**系统配置测试**
- [ ] 🟢 **基本设置** - SystemConfig.vue 系统基本配置
- [ ] 🟢 **用户管理** - 用户账号的增删改查
- [ ] 🟢 **权限设置** - 用户角色和权限配置
- [ ] 🟢 **数据备份** - 数据备份和恢复功能

**个人设置测试**
- [ ] 🟢 **个人信息** - 个人资料修改
- [ ] 🟢 **密码修改** - 密码修改功能
- [ ] 🟢 **偏好设置** - 界面主题、语言等设置

### 🔗 第3阶段：前端集成和交互测试

#### 导航和路由测试
- [ ] 🔴 **主导航** - 侧边栏导航菜单正常工作
- [ ] 🔴 **面包屑** - 页面面包屑导航正确显示当前位置
- [ ] 🔴 **页面跳转** - 所有页面间跳转正常，无404错误
- [ ] 🟡 **浏览器前进后退** - 浏览器前进后退按钮正常工作
- [ ] 🟡 **页面刷新** - 刷新页面后状态保持正常

#### 数据交互测试
- [ ] 🔴 **API调用** - 前端正确调用后端API接口
- [ ] 🔴 **数据显示** - API返回数据正确显示在页面上
- [ ] 🔴 **错误处理** - API错误时显示友好的错误提示
- [ ] 🔴 **加载状态** - 数据加载时显示loading状态
- [ ] 🟡 **数据缓存** - 合理使用数据缓存提升性能

#### 用户体验测试
- [ ] 🔴 **表单验证** - 所有表单字段验证提示清晰
- [ ] 🔴 **操作反馈** - 用户操作后有明确的成功/失败反馈
- [ ] 🟡 **确认对话框** - 删除等危险操作有确认提示
- [ ] 🟡 **快捷键** - 常用功能支持键盘快捷键

### 🔄 第4阶段：业务流程端到端测试

#### 完整销售流程测试
- [ ] 🔴 **新增客户** → **添加产品** → **创建报价** → **转为订单** → **生成发货单** → **财务对账**
- [ ] 🔴 **流程数据一致性** - 各环节数据正确传递和更新
- [ ] 🔴 **状态流转** - 业务状态正确流转（草稿→确认→完成等）

#### 典型业务场景测试
- [ ] 🔴 **客户管理场景** - 新增客户→查看详情→编辑信息→关联订单
- [ ] 🔴 **产品管理场景** - 创建分类→添加产品→编辑规格→应用到报价
- [ ] 🔴 **报价业务场景** - 选择客户→添加产品→计算金额→发送客户→转订单
- [ ] � **订单处理场景** - 接收订单→确认→安排生产→发货→收款
- [ ] 🟡 **退货处理场景** - 客户退货→创建退货单→库存更新→财务调整

### 📱 第5阶段：响应式和兼容性测试

#### 响应式设计测试
- [ ] 🟡 **桌面端** - 1920x1080分辨率下界面正常
- [ ] 🟡 **平板端** - 768px宽度下界面适配良好
- [ ] 🟢 **手机端** - 375px宽度下基本功能可用
- [ ] 🟡 **缩放测试** - 浏览器缩放125%、150%下界面正常

#### 浏览器兼容性测试
- [ ] 🟡 **Chrome浏览器** - 最新版本功能完全正常
- [ ] 🟡 **Firefox浏览器** - 最新版本功能完全正常
- [ ] 🟡 **Edge浏览器** - 最新版本功能完全正常
- [ ] 🟢 **Safari浏览器** - Mac版本基本功能正常

#### 性能测试
- [ ] 🟡 **首页加载** - 首次访问3秒内完成加载
- [ ] 🟡 **页面切换** - 页面间切换1秒内完成
- [ ] 🟡 **大数据列表** - 100+条数据列表流畅显示
- [ ] 🟢 **内存使用** - 长时间使用无明显内存泄漏

## �️ 前端测试执行指南

### 环境准备步骤
```bash
# 1. 确保后端服务运行
cd backend
python run.py
# 访问 http://localhost:5001/docs/ 确认API正常

# 2. 启动前端开发服务
cd frontend
npm run dev
# 访问 http://localhost:3001 确认前端正常

# 3. 打开浏览器开发者工具
# F12 → Console 查看错误信息
# F12 → Network 查看API请求
```

### 测试数据准备
**建议准备以下测试数据：**
- 🏢 **测试客户**: 至少5个不同类型的客户（大客户、小客户、新客户等）
- 📦 **测试产品**: 至少10个产品，包含不同分类和品牌
- 💰 **测试报价**: 2-3个不同状态的报价单
- 📋 **测试订单**: 2-3个不同状态的订单

### 测试执行建议
1. **按优先级测试**: 先测🔴高优先级，再测🟡中优先级，最后测🟢低优先级
2. **模块化测试**: 一个模块测试完成后再进行下一个
3. **记录问题**: 发现问题立即记录，包含截图和复现步骤
4. **状态更新**: 及时更新测试状态 ✅通过 / ❌失败 / ⏳待测试

### 常见问题检查
- [ ] **控制台错误** - 检查浏览器控制台是否有JavaScript错误
- [ ] **API请求失败** - 检查Network面板API请求是否正常
- [ ] **样式问题** - 检查CSS样式是否正确加载
- [ ] **路由问题** - 检查页面跳转是否正常
- [ ] **数据绑定** - 检查数据是否正确显示

## 📊 测试完成标准

### 前端测试通过标准
- 🔴 **高优先级功能**: 100%通过（核心业务功能）
- 🟡 **中优先级功能**: 95%通过（重要辅助功能）
- 🟢 **低优先级功能**: 90%通过（优化功能）
- 🔄 **业务流程**: 100%通过（端到端流程）

### 前端验收标准
- ✅ **功能完整性** - 所有页面和功能正常工作
- ✅ **用户体验** - 界面友好，操作流畅
- ✅ **数据准确性** - 前后端数据同步正确
- ✅ **响应式设计** - 不同设备下显示正常
- ✅ **性能表现** - 页面加载和响应速度良好

### 问题分级标准
- 🚨 **严重问题** - 功能无法使用，影响核心业务
- ⚠️ **一般问题** - 功能可用但体验不佳
- 💡 **优化建议** - 可以改进的地方

---

**测试重点**: 前端用户界面和交互体验
**测试范围**: 8个核心模块的前端功能
**预计时间**: 3-5个工作日
**文档状态**: 🔄 持续更新中
