工程物资报价及订单管理系统 - 产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目背景
基于现有EMB系统的完全重构项目，旨在为工程物资供应商提供一个现代化、规范化、易维护的综合管理解决方案。

### 1.2 项目目标
- **主要目标**: 构建一个功能完整、架构清晰、代码规范的工程物资管理系统
- **业务目标**: 提高报价效率、规范订单流程、优化客户管理、提升财务监控能力
- **技术目标**: 建立可维护、可扩展、高质量的代码基础
- **重构策略**: 后端代码和数据库结构基本参考原项目，如果可用则直接使用

### 1.3 项目范围
- ✅ **包含**: 后端API服务重构
- ✅ **复用**: 原项目的后端代码和数据库结构（经过评估后可用的部分）
- ❌ **不包含**: 前端重构、桌面应用打包、移动端应用、多租户支持

### 1.4 重构原则
- **后端优先复用**: 原项目的Flask后端代码、SQLAlchemy模型、API接口如果功能正确且代码质量可接受，则直接使用
- **数据库结构完全保持**: 数据库表结构、字段定义、约束关系必须与原项目保持100%一致，不允许任何结构性变更
- **代码质量提升**: 对复用的代码进行代码规范化、注释完善、错误处理改进，但不改变数据库结构
- **API标准化**: 统一API响应格式、错误处理机制和文档规范

### 1.5 目标用户
- **主要用户**: 工程物资供应商的业务人员
- **使用场景**: 客户管理、产品管理、报价处理、订单跟踪、财务管理

## 2. 功能需求

### 2.1 客户管理模块

#### 2.1.1 客户基本信息管理
- **功能描述**: 管理客户的基本信息
- **具体需求**:
  - 客户信息CRUD操作（创建、查询、更新、删除）
  - 必填字段：公司名称、联系人、联系电话
  - 可选字段：邮箱、地址、税号、客户来源、客户等级、备注
  - 客户状态管理（正常、禁用）
  - 客户信息导入导出（Excel格式）
  - 客户信息搜索和筛选

#### 2.1.2 客户银行账户管理
- **功能描述**: 管理客户的银行账户信息
- **具体需求**:
  - 银行账户CRUD操作
  - 必填字段：银行名称、账户名称、账户号码
  - 默认账户设置（每个客户可设置一个默认账户）
  - 账户状态管理

#### 2.1.3 客户送货地址管理
- **功能描述**: 管理客户的送货地址信息
- **具体需求**:
  - 送货地址CRUD操作
  - 地址信息：省份、城市、区县、详细地址
  - 联系信息：联系人、联系电话
  - 默认地址设置
  - 地址验证和格式化

### 2.2 产品管理模块

#### 2.2.1 产品基本信息管理
- **功能描述**: 管理产品的基本信息
- **具体需求**:
  - 产品信息CRUD操作
  - 必填字段：产品名称、产品型号、单位、产品分类
  - 可选字段：品牌、产品描述、备注
  - 产品状态管理（正常、禁用）
  - 产品信息导入导出
  - 产品搜索和筛选

#### 2.2.2 产品分类管理
- **功能描述**: 管理产品的分类体系
- **具体需求**:
  - 分类CRUD操作
  - 树形分类结构支持
  - 分类排序功能
  - 分类状态管理

#### 2.2.3 产品规格和价格管理
- **功能描述**: 管理产品的规格和价格信息
- **具体需求**:
  - 产品规格CRUD操作
  - 价格信息：成本价、建议售价、最低售价、最高售价
  - 税率设置
  - 默认规格设置
  - 价格历史记录

#### 2.2.4 产品图片管理
- **功能描述**: 管理产品的图片资源
- **具体需求**:
  - 图片上传、删除、预览
  - 支持格式：JPG、PNG、GIF
  - 图片大小限制：单张不超过5MB
  - 图片压缩和缩略图生成

#### 2.2.5 品牌管理
- **功能描述**: 管理产品品牌信息
- **具体需求**:
  - 品牌CRUD操作
  - 品牌信息：名称、描述、状态
  - 品牌关联产品统计

### 2.3 报价管理模块

#### 2.3.1 报价需求管理
- **功能描述**: 管理客户的报价需求
- **具体需求**:
  - 报价需求CRUD操作
  - 需求信息：客户、项目名称、需求描述、截止日期
  - 需求状态：待处理、处理中、已完成、已取消
  - 需求产品清单管理
  - 需求文件附件上传

#### 2.3.2 报价单管理
- **功能描述**: 生成和管理报价单
- **具体需求**:
  - 报价单CRUD操作
  - 基于报价需求生成报价单
  - 报价单信息：编号、客户、项目、有效期、总金额
  - 报价产品明细管理
  - 报价单状态：草稿、已发送、已确认、已失效
  - 报价单PDF导出和打印
  - 报价单版本管理

#### 2.3.3 报价模板管理
- **功能描述**: 管理报价单模板
- **具体需求**:
  - 模板CRUD操作
  - 模板分类管理
  - 模板应用到报价单
  - 模板预览功能

### 2.4 订单管理模块

#### 2.4.1 订单基本管理
- **功能描述**: 管理订单的全生命周期
- **具体需求**:
  - 订单CRUD操作
  - 订单信息：编号、客户、项目、预计采购时间、付款条件、交货条件
  - 订单状态流转：待确认→已确认→生产中→待发货→部分发货→全部发货→待对账→部分对账→全部对账→待收款→部分收款→已完成
  - 基于报价单创建订单
  - 订单产品明细管理
  - 订单金额计算

#### 2.4.2 发货单管理
- **功能描述**: 管理订单的发货信息
- **具体需求**:
  - 发货单CRUD操作
  - 发货单信息：编号、订单、发货日期、发货数量、物流信息
  - 发货单状态：待发货、已发货、已签收
  - 发货单PDF导出和打印
  - 发货进度跟踪

#### 2.4.3 退货单管理
- **功能描述**: 管理订单的退货信息
- **具体需求**:
  - 退货单CRUD操作
  - 退货原因管理
  - 退货状态流转
  - 退货金额计算
  - 退货单审批流程

### 2.5 财务管理模块

#### 2.5.1 收款记录管理
- **功能描述**: 管理客户的收款信息
- **具体需求**:
  - 收款记录CRUD操作
  - 收款信息：客户、订单、收款金额、收款日期、收款方式
  - 收款凭证上传
  - 收款状态管理

#### 2.5.2 对账单管理
- **功能描述**: 生成和管理对账单
- **具体需求**:
  - 对账单生成（基于发货单）
  - 对账单信息：编号、客户、对账期间、对账金额
  - 对账单状态：待确认、已确认、已收款
  - 对账单PDF导出

#### 2.5.3 应收款统计
- **功能描述**: 统计和分析应收款情况
- **具体需求**:
  - 应收款汇总统计
  - 账龄分析
  - 客户应收款排行
  - 应收款趋势分析
  - 逾期应收款提醒

#### 2.5.4 退款处理
- **功能描述**: 处理客户退款
- **具体需求**:
  - 退款记录CRUD操作
  - 退款审批流程
  - 退款凭证管理

### 2.6 系统管理模块

#### 2.6.1 系统设置
- **功能描述**: 管理系统的基本配置
- **具体需求**:
  - 公司信息设置
  - 系统参数配置
  - 单据编号规则设置
  - 税率设置

#### 2.6.2 数据备份恢复
- **功能描述**: 数据的备份和恢复
- **具体需求**:
  - 手动数据备份
  - 自动定期备份
  - 数据恢复功能
  - 备份文件管理

#### 2.6.3 操作日志
- **功能描述**: 记录用户的操作行为
- **具体需求**:
  - 操作日志记录
  - 日志查询和筛选
  - 日志导出功能

#### 2.6.4 错误日志
- **功能描述**: 记录系统的错误信息
- **具体需求**:
  - 错误日志记录
  - 错误分类和统计
  - 错误日志分析

## 3. 后端代码复用策略

### 3.1 复用评估原则
对原项目后端代码进行逐模块评估，按以下标准决定是否复用：

#### 3.1.1 直接复用条件
- ✅ 功能完整且正确
- ✅ 代码逻辑清晰
- ✅ 无明显性能问题
- ✅ 符合基本编码规范

#### 3.1.2 改进后复用条件
- 🔧 功能基本正确，但需要小幅调整
- 🔧 代码可读性需要改进（注释、命名等）
- 🔧 错误处理需要完善
- 🔧 性能可以优化但不影响使用

#### 3.1.3 重写条件
- ❌ 功能存在严重缺陷
- ❌ 代码逻辑混乱难以维护
- ❌ 存在安全隐患
- ❌ 性能问题严重

### 3.2 具体复用计划

#### 3.2.1 数据模型层（app/models/）
- **复用策略**: 基本全部复用，微调优化
- **评估重点**:
  - 表结构设计合理性
  - 字段类型和约束正确性
  - 关系定义完整性
- **改进方向**:
  - 统一注释语言为中文
  - 完善字段注释说明
  - 优化索引设计

#### 3.2.2 API接口层（app/api/v1/）
- **复用策略**: 评估后选择性复用
- **评估重点**:
  - API功能完整性
  - 响应格式一致性
  - 错误处理完善性
- **改进方向**:
  - 统一API响应格式
  - 完善错误处理和状态码
  - 改进API文档注释

#### 3.2.3 数据验证层（app/schemas/）
- **复用策略**: 基本复用，规范化改进
- **评估重点**:
  - 验证规则完整性
  - 错误信息友好性
- **改进方向**:
  - 统一验证错误信息
  - 完善字段验证规则

#### 3.2.4 工具函数（app/utils/）
- **复用策略**: 选择性复用
- **评估重点**:
  - 函数功能正确性
  - 代码复用性
- **改进方向**:
  - 重构通用工具函数
  - 完善函数文档

#### 3.2.5 配置管理（config.py）
- **复用策略**: 重构改进
- **改进重点**:
  - 统一配置管理
  - 环境变量规范化
  - 配置验证机制

## 4. 技术需求

### 4.1 技术架构
- **后端框架**: Python Flask（保持原项目技术栈）
- **数据库**: SQLite（与原项目保持一致）
- **ORM**: SQLAlchemy（保持原项目版本兼容性）
- **API文档**: Flask-RESTX 或 Flask-Swagger-UI（保持原项目风格）
- **数据验证**: Marshmallow（保持原项目验证方案）
- **配置管理**: 环境变量 + 配置文件

### 4.2 开发规范
- **代码风格**: Python PEP8，基于原项目风格进行规范化改进
- **提交规范**: Conventional Commits
- **分支策略**: Git Flow
- **测试要求**: 在原有测试基础上补充和完善
- **文档要求**: 完善API文档和代码注释

### 4.3 API设计
- **风格**: RESTful API（保持原项目API风格）
- **响应格式**: 改进原项目的响应格式，实现统一化
- **错误处理**: 完善原项目的错误处理机制
- **数据验证**: 使用原项目的Marshmallow验证，必要时改进
- **分页**: 改进原项目的分页实现，统一分页格式

### 4.4 数据库设计复用
- **表结构**: 完全保持原项目的表结构设计，不允许任何变更
- **字段定义**: 严格保持原有字段类型、长度、约束等所有属性
- **关系设计**: 完全维持原有的表间关系，包括外键约束
- **索引保持**: 保持原有的所有索引，不添加、删除或修改任何索引
- **数据兼容**: 确保新系统能够直接使用原项目的数据库文件

## 5. 数据需求

### 5.1 数据模型（基于原项目）
基本保持原项目的数据库表结构，具体包括：

- **客户相关**: customers, customer_bank_accounts, customer_delivery_addresses
- **产品相关**: products, product_categories, product_specifications, product_images, brands
- **报价相关**: quotation_requests, quotation_request_items, quotations, quotation_items, quotation_templates
- **订单相关**: orders, order_products, delivery_notes, delivery_note_items, return_orders, return_order_items
- **财务相关**: payment_records, statements, statement_delivery_notes, receivables, refund_records
- **系统相关**: system_settings, operation_logs, error_logs

**数据模型复用原则**:
- 严格保持原有表名和字段名，不允许任何修改
- 完全维持原有的数据类型、长度和所有约束
- 完全保留原有的外键关系和索引
- 不允许添加、删除或修改任何字段
- 不允许新增表或删除表
- 确保100%向后兼容，可直接使用原项目数据

### 5.2 数据完整性
- **主键约束**: 所有表都有主键
- **外键约束**: 维护表间关系的完整性
- **唯一约束**: 防止重复数据
- **检查约束**: 确保数据的有效性

### 5.3 数据备份
- **备份策略**: 每日自动备份
- **备份保留**: 保留30天的备份文件
- **恢复测试**: 定期进行恢复测试

## 6. API接口需求

### 6.1 API设计原则
- **RESTful风格**: 遵循REST API设计规范
- **统一响应格式**: 标准化的JSON响应结构
- **错误处理**: 统一的错误码和错误信息格式
- **版本控制**: 支持API版本管理

### 6.2 响应格式标准
- **成功响应**: 包含code、message、data字段
- **错误响应**: 包含code、message、errors字段
- **分页响应**: 包含total、page、per_page、data字段
- **状态码**: 正确使用HTTP状态码

### 6.3 API文档要求
- **接口文档**: 完整的API接口文档
- **参数说明**: 详细的请求参数和响应参数说明
- **示例代码**: 提供请求和响应示例
- **错误码说明**: 完整的错误码对照表

## 7. 非功能性需求

### 7.1 性能需求
- **响应时间**: API响应时间 < 2秒
- **并发用户**: 支持10个并发用户
- **数据量**: 支持10万条订单数据

### 7.2 可用性需求
- **系统可用性**: 99.9%
- **API稳定性**: API接口稳定可靠
- **错误恢复**: 具备良好的错误恢复机制

### 7.3 安全性需求
- **数据加密**: 敏感数据加密存储
- **输入验证**: 所有用户输入进行验证和过滤
- **SQL注入防护**: 使用ORM防止SQL注入
- **XSS防护**: 前端输出转义

### 7.4 可维护性需求
- **代码质量**: 代码规范、注释完整
- **模块化**: 高内聚、低耦合的模块设计
- **文档**: 完整的API文档和开发文档
- **日志**: 完善的日志记录机制

### 7.5 质量要求

#### 7.5.1 测试要求
- **单元测试**: 在原有测试基础上补充，覆盖率 > 70%
- **集成测试**: 覆盖主要业务流程
- **端到端测试**: 覆盖关键用户场景
- **回归测试**: 确保复用代码功能正常

#### 7.5.2 代码质量
- **代码审查**: 所有新增和修改的代码都要经过审查
- **静态分析**: 使用工具进行代码质量检查
- **文档**: 完整的代码注释和API文档
- **重构记录**: 详细记录代码重构过程和原因

## 8. 项目约束

### 8.1 时间约束
- **项目周期**: 预计2-4周完成（由于复用后端代码，开发周期缩短）
- **里程碑**: 每周进行进度评估

### 8.2 技术约束
- **后端技术栈**: 必须基于原项目的Flask技术栈
- **数据库**: 保持SQLite，维持原有数据结构
- **API兼容性**: 保持与原项目API的兼容性
- **部署**: 暂时只考虑开发环境部署

### 8.3 代码复用约束
- **后端代码**: 优先使用原项目代码，确保功能连续性
- **数据库**: 不进行破坏性的结构变更
- **API接口**: 保持原有API的兼容性
- **业务逻辑**: 在原有逻辑基础上进行改进

### 8.4 资源约束
- **开发人员**: 1-2名开发人员
- **硬件**: 标准开发环境即可

## 9. 验收标准

### 9.1 功能验收
- 所有功能模块按需求实现（基于原项目功能）
- 业务流程完整可用，与原系统保持一致
- 数据操作正确无误，兼容原有数据

### 9.2 质量验收
- 代码质量在原基础上有所提升
- 复用代码经过规范化处理
- 新增代码符合开发规范
- 系统稳定性不低于原项目

### 9.3 兼容性验收
- 数据库结构兼容原项目
- API接口保持向后兼容
- 业务逻辑与原系统一致
- 用户操作习惯保持连续性

### 9.4 文档验收
- API文档基于原项目完善
- 重构说明文档清晰
- 代码变更记录完整

## 10. 开发计划

### 10.1 开发阶段

#### 阶段1：项目准备和评估（1周）
- **代码评估**: 对原项目后端代码进行全面评估
- **环境搭建**: 建立开发环境和版本控制
- **文档整理**: 整理原项目文档和API说明
- **技术选型确认**: 确认复用的技术组件

#### 阶段2：后端重构和优化（2-3周）
- **模型层优化**: 改进数据模型注释和索引
- **API层重构**: 统一API响应格式和错误处理
- **配置管理**: 重构配置管理系统
- **工具函数重构**: 优化通用工具函数
- **数据验证改进**: 完善Marshmallow验证规则
- **测试补充**: 补充和完善后端测试

#### 阶段3：API文档和测试（1周）
- **API文档**: 完善API接口文档
- **功能测试**: 全面测试各功能模块
- **性能优化**: 优化系统性能
- **部署准备**: 准备部署相关配置

### 10.2 里程碑

- **M1**: 代码评估完成，开发环境就绪
- **M2**: 后端重构完成，API测试通过
- **M3**: API文档完成，功能测试通过

### 10.3 风险控制

- **代码备份**: 每个阶段都要有完整的代码备份
- **功能验证**: 每个模块重构后都要进行功能验证
- **回滚方案**: 准备代码回滚方案，确保系统可用性
- **进度监控**: 每周评估进度，及时调整计划

---

**文档版本**: v2.0
**创建日期**: 2025-06-20
**最后更新**: 2025-06-20
**创建人**: Augment Agent
