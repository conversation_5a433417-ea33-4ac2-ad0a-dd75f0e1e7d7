import request from './request'
import type {
  Customer,
  CustomerBankAccount,
  CustomerDeliveryAddress,
  PaginatedResponse,
  PaginationParams,
} from '@/types/api'

// 客户管理API
export const customerApi = {
  // 获取客户列表
  getList: (params?: PaginationParams) => {
    return request.get<PaginatedResponse<Customer>>('/customers', { params })
  },

  // 获取客户详情
  getById: (id: number) => {
    return request.get<Customer>(`/customers/${id}`)
  },

  // 创建客户
  create: (data: Partial<Customer>) => {
    return request.post<Customer>('/customers', data)
  },

  // 更新客户
  update: (id: number, data: Partial<Customer>) => {
    return request.put<Customer>(`/customers/${id}`, data)
  },

  // 删除客户
  delete: (id: number) => {
    return request.delete(`/customers/${id}`)
  },

  // 获取客户银行账户
  getBankAccounts: (customerId: number) => {
    return request.get<CustomerBankAccount[]>(`/customers/${customerId}/bank-accounts`)
  },

  // 添加银行账户
  addBankAccount: (customerId: number, data: Partial<CustomerBankAccount>) => {
    return request.post<CustomerBankAccount>(`/customers/${customerId}/bank-accounts`, data)
  },

  // 更新银行账户
  updateBankAccount: (
    customerId: number,
    accountId: number,
    data: Partial<CustomerBankAccount>
  ) => {
    return request.put<CustomerBankAccount>(
      `/customers/bank-accounts/${accountId}`,
      data
    )
  },

  // 删除银行账户
  deleteBankAccount: (customerId: number, accountId: number) => {
    return request.delete(`/customers/bank-accounts/${accountId}`)
  },

  // 获取送货地址
  getDeliveryAddresses: (customerId: number) => {
    return request.get<CustomerDeliveryAddress[]>(`/customers/${customerId}/delivery-addresses`)
  },

  // 获取客户应收账款
  getReceivables: (customerId: number) => {
    return request.get(`/customers/${customerId}/receivables`)
  },

  // 添加送货地址
  addDeliveryAddress: (customerId: number, data: Partial<CustomerDeliveryAddress>) => {
    return request.post<CustomerDeliveryAddress>(
      `/customers/${customerId}/delivery-addresses`,
      data
    )
  },

  // 更新送货地址
  updateDeliveryAddress: (
    customerId: number,
    addressId: number,
    data: Partial<CustomerDeliveryAddress>
  ) => {
    return request.put<CustomerDeliveryAddress>(
      `/customers/delivery-addresses/${addressId}`,
      data
    )
  },

  // 删除送货地址
  deleteDeliveryAddress: (customerId: number, addressId: number) => {
    return request.delete(`/customers/delivery-addresses/${addressId}`)
  },

  // 批量删除客户
  batchDelete: (ids: number[]) => {
    return request.delete('/customers/batch', { data: { ids } })
  },

  // 导出客户数据
  export: (params?: any) => {
    return request.get('/customers/export', {
      params,
      responseType: 'blob'
    })
  },

  // 批量导出客户数据
  batchExport: (ids: number[]) => {
    return request.post('/customers/batch/export', { ids }, {
      responseType: 'blob'
    })
  },

  // 导入客户数据
  import: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return request.post('/customers/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 下载导入模板
  downloadTemplate: () => {
    return request.get('/customers/import/template', {
      responseType: 'blob'
    })
  },
}

// 搜索客户（用于下拉选择等场景）
export const searchCustomers = (params?: any) => {
  return request.get('/customers', { params })
}
