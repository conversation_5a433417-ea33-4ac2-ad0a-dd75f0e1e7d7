# ===== 后端 Python/Flask =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Flask stuff:
instance/
.webassets-cache

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
backend/venv/
backend/.venv/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# ===== 前端 Node.js/Vue =====
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
dev-debug.log

# Dependencies
node_modules/
frontend/node_modules/

# Production builds
dist/
dist-ssr/
build/
*.local

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Temporary folders
tmp/
temp/

# ===== 编辑器和IDE =====
# VSCode
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# ===== 操作系统 =====
# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ===== 项目特定 =====
# 数据库文件
*.db
*.sqlite
*.sqlite3
app.db

# 上传文件
uploads/
static/uploads/
backend/uploads/

# 日志文件
logs/
backend/logs/

# 备份文件
*.bak
*.backup
*.old
*.orig

# 临时文件和构建产物
temp/
staging/
build/

# 配置文件（包含敏感信息）
.env.local
.env.production
config/local.py
config/production.py

# 测试覆盖率报告
coverage/
htmlcov/

# 原项目文件夹（参考用）
origin/

# Added by Task Master AI
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
