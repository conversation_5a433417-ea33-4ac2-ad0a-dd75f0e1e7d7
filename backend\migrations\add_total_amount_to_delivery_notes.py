#!/usr/bin/env python3
"""
添加total_amount字段到delivery_notes表
"""

import sqlite3
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models.order import DeliveryNote, DeliveryNoteItem

def add_total_amount_field():
    """添加total_amount字段到delivery_notes表"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查字段是否已存在
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('delivery_notes')]
            
            if 'total_amount' in columns:
                print("total_amount字段已存在，跳过添加")
                return
            
            # 添加字段
            print("正在添加total_amount字段到delivery_notes表...")
            db.engine.execute('ALTER TABLE delivery_notes ADD COLUMN total_amount DECIMAL(10,2) DEFAULT 0.00')
            
            print("字段添加成功，正在计算并更新现有发货单的总金额...")
            
            # 更新现有发货单的总金额
            delivery_notes = DeliveryNote.query.all()
            updated_count = 0
            
            for dn in delivery_notes:
                try:
                    dn.update_total_amount()
                    updated_count += 1
                    print(f"更新发货单 {dn.delivery_number}: ¥{dn.total_amount}")
                except Exception as e:
                    print(f"更新发货单 {dn.delivery_number} 失败: {e}")
            
            # 提交更改
            db.session.commit()
            print(f"成功更新了 {updated_count} 个发货单的总金额")
            
        except Exception as e:
            print(f"迁移失败: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    add_total_amount_field()
