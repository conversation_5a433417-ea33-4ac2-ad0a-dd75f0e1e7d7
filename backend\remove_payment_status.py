#!/usr/bin/env python3
"""
删除对账单payment_status字段的迁移脚本
简化对账单状态管理，只使用status字段
"""

import sqlite3
import os
from datetime import datetime

def remove_payment_status_field():
    """删除payment_status字段并更新状态"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("开始删除payment_status字段...")
        
        # 1. 检查当前对账单数据
        print("1. 检查当前对账单数据...")
        cursor.execute("""
            SELECT id, status, payment_status, paid_amount, adjusted_total_amount 
            FROM statements
        """)
        statements = cursor.fetchall()
        print(f"   找到 {len(statements)} 条对账单记录")
        
        # 2. 根据payment_status更新status字段
        print("2. 根据payment_status更新status字段...")
        for statement_id, status, payment_status, paid_amount, total_amount in statements:
            new_status = status  # 默认保持原状态
            
            if payment_status == '已支付':
                new_status = '已结清'
            elif payment_status == '部分支付':
                new_status = '部分收款'
            elif payment_status == '未支付' and status == '已确认':
                new_status = '已确认'  # 保持已确认状态
            
            if new_status != status:
                cursor.execute("""
                    UPDATE statements 
                    SET status = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                """, (new_status, statement_id))
                print(f"   对账单 {statement_id}: {status} → {new_status}")
        
        # 3. 创建新的statements表（不包含payment_status字段）
        print("3. 创建新的statements表...")
        cursor.execute("""
            CREATE TABLE statements_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                statement_number VARCHAR(50) NOT NULL UNIQUE,
                customer_id INTEGER NOT NULL,
                statement_date DATE NOT NULL,
                due_date DATE,
                status VARCHAR(20) NOT NULL DEFAULT '待确认',
                total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
                discount_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
                adjusted_total_amount DECIMAL(12,2) NOT NULL,
                notes TEXT,
                paid_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
                settlement_date DATE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                CHECK (status IN ('待确认', '已确认', '部分收款', '已结清', '已取消'))
            )
        """)
        
        # 4. 复制数据到新表（排除payment_status字段）
        print("4. 复制数据到新表...")
        cursor.execute("""
            INSERT INTO statements_new (
                id, statement_number, customer_id, statement_date, due_date, status,
                total_amount, discount_amount, adjusted_total_amount, notes,
                paid_amount, settlement_date, created_at, updated_at
            )
            SELECT 
                id, statement_number, customer_id, statement_date, due_date, status,
                total_amount, discount_amount, adjusted_total_amount, notes,
                paid_amount, settlement_date, created_at, updated_at
            FROM statements
        """)
        
        # 5. 删除旧表并重命名新表
        print("5. 替换旧表...")
        cursor.execute("DROP TABLE statements")
        cursor.execute("ALTER TABLE statements_new RENAME TO statements")
        
        # 6. 重新创建索引
        print("6. 重新创建索引...")
        cursor.execute("CREATE INDEX idx_statements_customer_id ON statements(customer_id)")
        cursor.execute("CREATE INDEX idx_statements_status ON statements(status)")
        cursor.execute("CREATE INDEX idx_statements_statement_date ON statements(statement_date)")
        
        conn.commit()
        print("✅ payment_status字段删除完成！")
        
        # 7. 验证结果
        print("\n📊 迁移结果验证:")
        cursor.execute("PRAGMA table_info(statements)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        print(f"对账单表字段: {', '.join(column_names)}")
        
        if 'payment_status' not in column_names:
            print("✅ payment_status字段已成功删除")
        else:
            print("❌ payment_status字段仍然存在")
        
        # 检查状态分布
        cursor.execute("""
            SELECT status, COUNT(*) 
            FROM statements 
            GROUP BY status
        """)
        status_counts = cursor.fetchall()
        print("\n状态分布:")
        for status, count in status_counts:
            print(f"  {status}: {count} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    success = remove_payment_status_field()
    if success:
        print("\n🎉 对账单状态简化完成！现在只使用status字段管理状态。")
    else:
        print("\n💥 对账单状态简化失败！")
