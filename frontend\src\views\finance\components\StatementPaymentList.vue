<template>
  <div class="statement-payment-list">
    <!-- 搜索筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select
            v-model="searchForm.payment_source"
            placeholder="付款来源"
            clearable
            @change="handleSearch"
          >
            <el-option label="直接付款" value="direct" />
            <el-option label="余额支付" value="balance" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column prop="statement_number" label="对账单号" width="180" />
      
      <el-table-column prop="payment_date" label="收款日期" width="120">
        <template #default="{ row }">
          {{ formatDate(row.payment_date) }}
        </template>
      </el-table-column>

      <el-table-column prop="amount" label="收款金额" width="150">
        <template #default="{ row }">
          <span class="amount">
            {{ formatCurrency(row.amount) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="payment_method" label="支付方式" width="120" />

      <el-table-column prop="payment_source" label="付款来源" width="100">
        <template #default="{ row }">
          <el-tag
            :type="row.payment_source === 'direct' ? 'primary' : 'success'"
            size="small"
          >
            {{ row.payment_source === 'direct' ? '直接付款' : '余额支付' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="reference_number" label="交易流水号" width="150">
        <template #default="{ row }">
          <span v-if="row.reference_number">{{ row.reference_number }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="bank_account" label="收款账户" min-width="200">
        <template #default="{ row }">
          <span v-if="row.bank_account">{{ row.bank_account }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag
            :type="row.status === '已确认' ? 'success' : 'warning'"
            size="small"
          >
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_by" label="创建人" width="100">
        <template #default="{ row }">
          <span v-if="row.created_by">{{ row.created_by }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button
            type="info"
            size="small"
            @click="handleViewDetail(row)"
          >
            <el-icon><View /></el-icon>
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.per_page"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, View } from '@element-plus/icons-vue'
import { paymentApi } from '@/api/payment'
import type { StatementPayment } from '@/types/payment'

// 定义事件
const emit = defineEmits<{
  directPayment: [statementId: number]
  balancePayment: [data: any]
  viewDetail: [payment: StatementPayment]
}>()

// 响应式数据
const loading = ref(false)
const tableData = ref<StatementPayment[]>([])
const dateRange = ref<[string, string] | null>(null)

// 搜索表单
const searchForm = reactive({
  payment_source: '',
  start_date: '',
  end_date: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

// 格式化货币
const formatCurrency = (amount: number): string => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 格式化日期
const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      per_page: pagination.per_page,
      ...searchForm
    }

    const response = await paymentApi.getStatementPayments(params)
    tableData.value = response.data || []
    pagination.total = response.pagination?.total || 0
  } catch (error) {
    console.error('加载对账单收款记录失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 日期范围改变
const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.start_date = dates[0]
    searchForm.end_date = dates[1]
  } else {
    searchForm.start_date = ''
    searchForm.end_date = ''
  }
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.per_page = size
  pagination.page = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 查看详情
const handleViewDetail = (payment: StatementPayment) => {
  emit('viewDetail', payment)
}

// 刷新数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  refresh
})

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.statement-payment-list {
  padding: 0;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.amount {
  font-weight: bold;
  color: #67C23A;
}

.text-muted {
  color: #C0C4CC;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>
