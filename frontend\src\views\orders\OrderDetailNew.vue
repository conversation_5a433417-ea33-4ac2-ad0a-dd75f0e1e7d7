<template>
  <div v-if="isOrderLoaded" class="order-detail">
    <!-- 头部区域 -->
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="form-title">订单详情</h2>
        <div>
          <el-button @click="goBack">返回</el-button>
          <el-button
            v-if="canEdit"
            type="primary"
            @click="handleEdit"
          >编辑</el-button>
          <el-button type="success" @click="handleExport">导出订单</el-button>
          <el-button
            v-if="canApprove"
            type="warning"
            @click="handleApprove"
          >审核</el-button>
          <el-button
            v-if="canCreateDeliveryNote"
            type="info"
            @click="handleCreateDelivery"
          >创建发货单</el-button>
        </div>
      </div>
    </el-card>

    <!-- 订单状态流程图 -->
    <el-card class="mb-20">
      <el-steps :active="getStatusStep(orderInfo?.status ?? '')" finish-status="success" simple>
        <el-step title="待确认" />
        <el-step title="已确认" />
        <el-step title="生产中" />
        <el-step title="待发货" />
        <el-step title="发货中" />
        <el-step title="已发货" />
        <el-step title="已对账" />
        <el-step title="已结清" />
      </el-steps>
    </el-card>

    <!-- 信息卡片 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :span="8">
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <span>基本信息</span>
            </div>
          </template>
          <div class="info-body">
            <div class="info-item">
              <span class="label">订单号:</span>
              <span class="value">{{ orderInfo?.order_number ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">订单状态:</span>
              <span class="value">
                <el-tag :type="getStatusType(orderInfo?.status ?? '')">{{ getStatusLabel(orderInfo?.status ?? '') }}</el-tag>
              </span>
            </div>
            <div class="info-item">
              <span class="label">订单日期:</span>
              <span class="value">{{ orderInfo?.created_at ? formatDate(orderInfo.created_at) : new Date().toLocaleDateString('zh-CN') }}</span>
            </div>
            <div class="info-item">
              <span class="label">预计交期:</span>
              <span class="value">{{ orderInfo?.expected_date ? formatDate(orderInfo.expected_date) : '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">关联报价单:</span>
              <span class="value">
                <el-link v-if="orderInfo?.quotation?.quotation_number" type="primary" @click="viewQuotation">
                  {{ orderInfo?.quotation?.quotation_number ?? '' }}
                </el-link>
                <span v-else>无</span>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <span>客户信息</span>
            </div>
          </template>
          <div class="info-body">
            <div class="info-item">
              <span class="label">客户名称:</span>
              <span class="value">{{ orderInfo?.customer?.name ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系人:</span>
              <span class="value">{{ orderInfo?.customer?.contact ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系电话:</span>
              <span class="value">{{ orderInfo?.customer?.phone ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">项目名称:</span>
              <span class="value">{{ orderInfo?.project_name ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">项目地址:</span>
              <span class="value">{{ orderInfo?.project_address ?? '' }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <span>商务信息</span>
            </div>
          </template>
          <div class="info-body">
            <div class="info-item">
              <span class="label">订单总额:</span>
              <span class="value amount">{{ formatCurrency(orderInfo?.total_amount ?? 0) }}</span>
            </div>
            <div class="info-item">
              <span class="label">已收款:</span>
              <span class="value">{{ formatCurrency(orderInfo?.paid_amount ?? 0) }}</span>
            </div>
            <div class="info-item">
              <span class="label">待收款:</span>
              <span class="value">{{ formatCurrency((orderInfo?.total_amount ?? 0) - (orderInfo?.paid_amount ?? 0)) }}</span>
            </div>
            <div class="info-item">
              <span class="label">付款条件:</span>
              <span class="value">{{ orderInfo?.payment_terms ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">交货条件:</span>
              <span class="value">{{ orderInfo?.delivery_terms ?? '' }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 产品明细 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>产品明细</span>
        </div>
      </template>
      
      <el-table :data="orderInfo?.order_products || []" border stripe>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="product_name" label="产品名称" min-width="150" />
        <el-table-column prop="product_model" label="产品型号" width="120" />
        <el-table-column prop="specification" label="规格" width="120" />
        <el-table-column prop="quantity" label="数量" width="100" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column label="单价" width="120">
          <template #default="{ row }">
            {{ formatCurrency(row.unit_price || 0) }}
          </template>
        </el-table-column>
        <el-table-column label="折扣(%)" width="100">
          <template #default="{ row }">
            {{ row.discount || 0 }}%
          </template>
        </el-table-column>
        <el-table-column label="小计" width="120">
          <template #default="{ row }">
            {{ formatCurrency(row.total_amount || 0) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 合计信息 -->
      <div class="total-section mt-20">
        <el-row :gutter="20">
          <el-col :span="18"></el-col>
          <el-col :span="6">
            <div class="total-info">
              <div class="total-row">
                <span>订单总额：</span>
                <span class="total-amount">{{ formatCurrency(orderInfo?.total_amount ?? 0) }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 备注信息 -->
    <el-card v-if="orderInfo?.notes" class="mb-20">
      <template #header>
        <div class="card-header">
          <span>备注信息</span>
        </div>
      </template>
      <p>{{ orderInfo.notes }}</p>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { orderApi } from '@/api/order'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const isOrderLoaded = ref(false)

// 订单数据
const orderInfo = ref<any>({})

// 计算属性
const canEdit = computed(() => {
  return ['draft', 'pending'].includes(orderInfo.value?.status)
})

const canApprove = computed(() => {
  return orderInfo.value?.status === 'pending'
})

const canCreateDeliveryNote = computed(() => {
  return ['confirmed', 'in_production'].includes(orderInfo.value?.status)
})

// 获取状态步骤
const getStatusStep = (status: string) => {
  const statusMap: Record<string, number> = {
    'pending': 0,
    'confirmed': 1,
    'in_production': 2,
    'ready_to_ship': 3,
    'shipping': 4,
    'all_shipped': 5,
    'all_statement': 6,
    'completed': 7
  }
  return statusMap[status] || 0
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'draft': 'info',
    'pending': 'warning',
    'confirmed': 'success',
    'in_production': 'primary',
    'ready_to_ship': 'warning',
    'shipping': 'primary',
    'all_shipped': 'success',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'draft': '草稿',
    'pending': '待确认',
    'confirmed': '已确认',
    'in_production': '生产中',
    'ready_to_ship': '待发货',
    'shipping': '发货中',
    'all_shipped': '已发货',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return labelMap[status] || status
}

// 格式化货币
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('zh-CN', { 
    style: 'currency', 
    currency: 'CNY' 
  }).format(value)
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

// 返回
const goBack = () => {
  router.back()
}

// 编辑订单
const handleEdit = () => {
  router.push(`/orders/edit/${route.params.id}`)
}

// 审核订单
const handleApprove = () => {
  ElMessage.info('审核功能开发中...')
}

// 导出订单
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 创建发货单
const handleCreateDelivery = () => {
  ElMessage.info('创建发货单功能开发中...')
}

// 查看报价单
const viewQuotation = () => {
  if (orderInfo.value?.quotation?.id) {
    router.push(`/quotations/view/${orderInfo.value.quotation.id}`)
  }
}

// 获取订单详情
const getOrderDetail = async () => {
  try {
    loading.value = true
    const response = await orderApi.getById(route.params.id as string)
    orderInfo.value = response
    isOrderLoaded.value = true
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  getOrderDetail()
})
</script>

<style lang="scss" scoped>
.order-detail {
  .header-card {
    .form-title {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
    }

    .flex-between {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .info-card {
    height: 100%;

    .info-header {
      font-weight: bold;
    }

    .info-body {
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
        }

        .value {
          color: #333;
          font-size: 14px;
          text-align: right;

          &.amount {
            color: #e6a23c;
            font-weight: bold;
          }
        }
      }
    }
  }

  .card-header {
    font-size: 16px;
    font-weight: bold;
  }

  .total-section {
    .total-info {
      text-align: right;

      .total-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-top: 1px solid #ebeef5;

        .total-amount {
          font-size: 18px;
          font-weight: bold;
          color: #e6a23c;
        }
      }
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .mt-20 {
    margin-top: 20px;
  }
}
</style>
