import type { Customer } from './customer'
import type { DeliveryNote } from './delivery'

/**
 * 对账单状态类型
 */
export type StatementStatus = '草稿' | '待确认' | '已确认' | '已收款' | '已取消' | 'draft' | 'confirmed' | 'paid' | 'cancelled'

/**
 * 对账单接口
 */
export interface Statement {
  id: string
  statement_number: string
  customer_id: string
  customer: Customer
  statement_date: string
  due_date: string | null
  total_amount: number
  status: StatementStatus
  notes: string | null
  created_at: string
  updated_at: string
  delivery_notes: DeliveryNote[]
}

/**
 * 用于创建对账单的Payload接口
 */
export interface StatementCreate {
  customer_id: string
  statement_date: string
  due_date?: string
  notes?: string
  delivery_note_ids: string[]
}

/**
 * 用于更新对账单的Payload接口
 */
export interface StatementUpdate {
  statement_date?: string
  due_date?: string
  notes?: string
  delivery_note_ids?: string[]
}

/**
 * 对账单列表项接口
 */
export interface StatementListItem {
  id: string
  statement_number: string
  customer_id: string
  customer: {
    id: string
    name: string
  }
  statement_date: string
  due_date: string | null
  total_amount: number
  status: StatementStatus
  created_at: string
  updated_at: string
}
