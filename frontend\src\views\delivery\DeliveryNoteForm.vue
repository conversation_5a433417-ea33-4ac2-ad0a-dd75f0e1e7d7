<template>
  <div class="delivery-edit">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>{{ isNew ? '新建发货单' : '编辑发货单' }}</span>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <!-- 基本信息 -->
        <el-form-item label="订单" prop="order_id">
          <el-select
            v-model="form.order_id"
            filterable
            remote
            :remote-method="handleSearchOrders"
            placeholder="请输入订单号"
            @change="handleOrderChange"
          >
            <el-option
              v-for="item in orderOptions"
              :key="item.id"
              :label="`${item.order_number} - ${item.customer_name || item.customer?.name || ''}`"
              :value="String(item.id)"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="客户">
          <el-input
            v-model="selectedCustomerName"
            disabled
            placeholder="选择订单后自动填充"
          />
        </el-form-item>

        <el-form-item label="收货人" prop="receiver">
          <el-input
            v-model="form.receiver"
            placeholder="请输入收货人姓名"
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="contact_phone">
          <el-input
            v-model="form.contact_phone"
            placeholder="请输入联系电话"
          />
        </el-form-item>

        <el-form-item label="送货地址" prop="delivery_address">
          <el-input
            v-model="form.delivery_address"
            placeholder="请输入送货地址"
          />
        </el-form-item>

        <el-form-item label="发货日期" prop="delivery_date">
          <el-date-picker
            v-model="form.delivery_date"
            type="date"
            placeholder="请选择发货日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="物流公司" prop="logistics_company">
          <el-input
            v-model="form.logistics_company"
            placeholder="请输入物流公司"
          />
        </el-form-item>

        <el-form-item label="物流单号" prop="tracking_number">
          <el-input
            v-model="form.tracking_number"
            placeholder="请输入物流单号"
          />
        </el-form-item>

        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 产品明细 -->
        <el-form-item label="产品明细" prop="products">
          <el-table
            :data="selectedProducts"
            border
            style="width: 100%"
            :row-class-name="getRowClassName"
          >
            <el-table-column prop="product_name" label="产品名称" min-width="180">
              <template #default="{ row }">
                <span :class="{ 'fully-delivered': isFullyDelivered(row) }">
                  {{ row.product_name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="product_model" label="型号" width="120" />
            <el-table-column prop="specification" label="规格" width="120" />
            <el-table-column prop="product_unit" label="单位" width="80" />
            <el-table-column prop="order_quantity" label="订单数量" width="120" />
            <el-table-column prop="delivered_quantity" label="已发货" width="120" />
            <el-table-column prop="quantity" label="本次发货" width="150" fixed="right">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.quantity"
                  :min="0"
                  :max="row.max_quantity"
                  size="small"
                  @change="handleQuantityChange"
                />
              </template>
            </el-table-column>
            <el-table-column prop="unit_price" label="单价" width="120">
              <template #default="{ row }">
                {{ formatCurrency(row.unit_price) }}
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="金额" width="120">
              <template #default="{ row }">
                {{ formatCurrency(row.amount) }}
              </template>
            </el-table-column>
            <el-table-column prop="notes" label="备注" min-width="120">
              <template #default="{ row }">
                <el-input
                  v-model="row.notes"
                  placeholder="产品备注"
                />
              </template>
            </el-table-column>
          </el-table>

          <div class="total-amount">
            <span>总金额：</span>
            <span class="amount">{{ formatCurrency(totalAmount) }}</span>
          </div>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { deliveryNoteApi } from '@/api/delivery'
import { orderApi } from '@/api/order'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()
const selectedOrder = ref<any>(null)

// 判断是否为新建
const isNew = computed(() => !route.params.id)

// 表单数据
interface FormState {
  order_id: string
  delivery_date: string
  logistics_company?: string
  tracking_number?: string
  notes?: string
  receiver: string
  contact_phone: string
  delivery_address: string
}

const form = reactive<FormState>({
  order_id: '',
  delivery_date: '',
  logistics_company: '',
  tracking_number: '',
  notes: '',
  receiver: '',
  contact_phone: '',
  delivery_address: ''
})

// 表单验证规则
const rules = {
  order_id: [
    { required: true, message: '请选择订单', trigger: 'change' }
  ],
  delivery_date: [
    { required: true, message: '请选择发货日期', trigger: 'change' }
  ],
  receiver: [
    { required: true, message: '请输入收货人', trigger: 'blur' }
  ],
  contact_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  delivery_address: [
    { required: true, message: '请输入送货地址', trigger: 'blur' }
  ]
}

// 订单选项
const orderOptions = ref<any[]>([])
const selectedCustomerName = ref('')
const selectedProducts = ref<any[]>([])

// 搜索订单
const handleSearchOrders = async (query: string) => {
  if (query) {
    try {
      const response = await orderApi.getList({
        order_number: query,
        status: 'confirmed',
        per_page: 20
      }) as any

      // 处理API响应格式
      let orderList: any[] = []
      if (Array.isArray(response)) {
        orderList = response
      } else if (response && response.data) {
        if (Array.isArray(response.data)) {
          orderList = response.data
        } else if (response.data.list && Array.isArray(response.data.list)) {
          orderList = response.data.list
        }
      }

      orderOptions.value = orderList
    } catch (error) {
      console.error('搜索订单失败:', error)
    }
  }
}

// 订单变更
const handleOrderChange = async (orderId: string, excludeDeliveryNoteId?: number) => {
  if (orderId) {
    try {
      // 构建查询参数
      const params: any = {}
      if (excludeDeliveryNoteId) {
        params.exclude_delivery_note_id = excludeDeliveryNoteId
      }

      const response = await orderApi.getById(Number(orderId), params) as any
      selectedOrder.value = response.data || response
      console.log('订单详情数据:', selectedOrder.value)

      if (selectedOrder.value) {
        // 获取客户信息
        selectedCustomerName.value = selectedOrder.value.customer?.name || selectedOrder.value.customer_name || ''

        // 如果有客户联系信息，填充收货信息
        form.receiver = selectedOrder.value.customer?.contact || selectedOrder.value.contact_person || ''
        form.contact_phone = selectedOrder.value.customer?.phone || selectedOrder.value.contact_phone || ''

        // 如果有项目地址，用作送货地址
        form.delivery_address = selectedOrder.value.project_address || selectedOrder.value.delivery_address || ''

        // 确保订单选项中包含当前订单
        if (!orderOptions.value.find(opt => opt.id === selectedOrder.value.id)) {
          orderOptions.value.push({
            id: selectedOrder.value.id,
            order_number: selectedOrder.value.order_number,
            customer_name: selectedOrder.value.customer?.name || selectedOrder.value.customer_name || ''
          })
        }

        // 处理订单产品数据 - 检查多种可能的数据结构
        let productItems = []
        if (selectedOrder.value.items && Array.isArray(selectedOrder.value.items)) {
          productItems = selectedOrder.value.items
        } else if (selectedOrder.value.products && Array.isArray(selectedOrder.value.products)) {
          productItems = selectedOrder.value.products
        } else if (selectedOrder.value.order_products && Array.isArray(selectedOrder.value.order_products)) {
          productItems = selectedOrder.value.order_products
        }

        console.log('找到的产品数据:', productItems)

        if (productItems.length > 0) {
          selectedProducts.value = productItems.map((p: any) => {
            // 计算实际单价（考虑税率和折扣）
            const basePrice = p.unit_price || p.price || 0
            const taxRate = (p.tax_rate || 0) / 100
            const discount = (p.discount || 0) / 100
            const actualUnitPrice = basePrice * (1 - discount) * (1 + taxRate)

            return {
              id: p.id,  // 订单产品ID，用于提交时的order_product_id
              product_id: p.product_id || p.id,
              product_name: p.product_name || p.name,
              product_model: p.product_model || p.model || '',
              specification: p.specification_description || p.specification || p.spec || '',
              product_unit: p.product_unit || p.unit || '个',
              unit_price: actualUnitPrice,
              order_quantity: p.quantity || 0,
              delivered_quantity: p.delivered_quantity || 0,
              quantity: 0,
              max_quantity: (p.quantity || 0) - (p.delivered_quantity || 0), // 最大可发货数量
              amount: 0,
              notes: ''
            }
          })
          console.log('处理后的产品数据:', selectedProducts.value)
        } else {
          console.log('未找到产品数据，订单结构:', selectedOrder.value)
        }
      }
    } catch (error) {
      console.error('获取订单详情失败:', error)
      ElMessage.error('获取订单详情失败')
    }
  } else {
    selectedOrder.value = null
    selectedCustomerName.value = ''
    selectedProducts.value = []
    form.receiver = ''
    form.contact_phone = ''
    form.delivery_address = ''
  }
}

// 判断产品是否已完全发货
const isFullyDelivered = (row) => {
  const remainingQuantity = row.order_quantity - (row.delivered_quantity || 0)
  return remainingQuantity <= 0
}

// 获取表格行样式类名
const getRowClassName = ({ row }) => {
  if (isFullyDelivered(row)) {
    return 'fully-delivered-row'
  }
  return ''
}

// 数量变更
const handleQuantityChange = () => {
  selectedProducts.value.forEach(p => {
    p.amount = p.quantity * p.unit_price
  })
}

// 总金额
const totalAmount = computed(() => {
  return selectedProducts.value.reduce((sum, p) => sum + p.amount, 0)
})

// 获取详情
const fetchDetail = async (id: string) => {
  try {
    const response = await deliveryNoteApi.getById(Number(id)) as any
    const deliveryNote = response.data || response

    // 设置基本信息
    form.order_id = String(deliveryNote.order_id)
    form.delivery_date = deliveryNote.delivery_date
    form.logistics_company = deliveryNote.logistics_company || ''
    form.tracking_number = deliveryNote.tracking_number || ''
    form.notes = deliveryNote.notes || ''

    // 获取关联的订单信息
    if (deliveryNote.order) {
      // 从嵌套的order对象获取信息
      selectedCustomerName.value = deliveryNote.order.customer?.name || ''
      form.receiver = deliveryNote.order.contact_person || deliveryNote.recipient_name || ''
      form.contact_phone = deliveryNote.order.contact_phone || deliveryNote.recipient_phone || ''
      form.delivery_address = deliveryNote.order.delivery_address ||
                              deliveryNote.order.project_address ||
                              deliveryNote.delivery_address_snapshot || ''

      // 获取订单详情，以便展示产品信息（排除当前发货单的贡献）
      await handleOrderChange(String(deliveryNote.order_id), Number(id))

      // 设置产品发货数量
      if (deliveryNote.items && deliveryNote.items.length > 0) {
        // 匹配送货单项目和已加载的产品列表
        deliveryNote.items.forEach((item: any) => {
          const productIndex = selectedProducts.value.findIndex(
            p => p.product_id === item.order_product_id || p.product_id === item.product_id
          )
          if (productIndex !== -1) {
            selectedProducts.value[productIndex].quantity = item.quantity
            selectedProducts.value[productIndex].notes = item.notes || ''
            // 重新计算金额
            selectedProducts.value[productIndex].amount =
              selectedProducts.value[productIndex].quantity *
              selectedProducts.value[productIndex].unit_price
          }
        })
      }
    }
  } catch (error) {
    console.error('获取发货单详情失败:', error)
    ElMessage.error('获取发货单详情失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const products = selectedProducts.value
          .filter(p => p.quantity > 0)
          .map(p => ({
            product_id: String(p.product_id),
            quantity: p.quantity,
            notes: p.notes
          }))

        if (products.length === 0) {
          ElMessage.warning('请至少选择一个产品并设置数量')
          return
        }

        if (isNew.value) {
          console.log('selectedProducts.value:', selectedProducts.value)

          const filteredProducts = selectedProducts.value.filter(p => p.quantity > 0)
          console.log('过滤后的产品:', filteredProducts)

          if (filteredProducts.length === 0) {
            ElMessage.warning('请至少选择一个产品并设置发货数量')
            return
          }

          console.log('过滤后的产品详细信息:', JSON.stringify(filteredProducts, null, 2))

          const items = filteredProducts.map(p => {
            console.log('处理产品:', p)
            console.log('产品字段:', Object.keys(p))

            // 根据后端要求构建items数据
            const item = {
              order_product_id: p.product_id,
              quantity: p.quantity,
              notes: p.notes || ''
            }

            // 尝试添加product_specification_id字段
            if (p.product_specification_id) {
              item.product_specification_id = p.product_specification_id
            } else if (p.specification_id) {
              item.product_specification_id = p.specification_id
            } else {
              // 如果没有找到specification_id，使用product_id作为fallback
              console.warn('未找到product_specification_id，使用product_id作为fallback:', p.product_id)
              item.product_specification_id = p.product_id
            }

            console.log('构建的item:', item)
            return item
          })

          console.log('构建的items:', items)

          const submitData = {
            order_id: Number(form.order_id),
            delivery_date: form.delivery_date + 'T00:00:00.000Z',
            recipient_name: form.receiver,
            recipient_phone: form.contact_phone,
            delivery_address_snapshot: form.delivery_address,
            logistics_company: form.logistics_company || '',
            tracking_number: form.tracking_number || '',
            notes: form.notes || '',
            status: '待发出'
          }

          // 确保items字段被正确添加
          submitData.items = items

          console.log('最终提交数据:', submitData)
          console.log('items字段检查:', submitData.items)

          // 在API调用前再次确认数据完整性
          console.log('即将提交的发货单数据:', JSON.stringify(submitData, null, 2))

          await deliveryNoteApi.create(submitData)
          ElMessage.success('创建发货单成功')
        } else {
          // 编辑模式：准备产品明细数据
          const items = selectedProducts.value
            .filter(p => p.quantity > 0)
            .map(p => ({
              order_product_id: p.id,
              quantity: p.quantity,
              notes: p.notes || ''
            }))

          const updateData = {
            order_id: Number(form.order_id),
            delivery_date: form.delivery_date,
            recipient_name: form.receiver,
            recipient_phone: form.contact_phone,
            delivery_address_snapshot: form.delivery_address,
            logistics_company: form.logistics_company,
            tracking_number: form.tracking_number,
            notes: form.notes,
            items: items  // 添加产品明细数据
          }

          console.log('编辑模式提交数据:', updateData)
          console.log('产品明细数据:', items)

          await deliveryNoteApi.update(Number(route.params.id), updateData)
          ElMessage.success('更新发货单成功')
        }
        router.push('/delivery-notes')
      } catch (error) {
        console.error('保存发货单失败:', error)
        console.error('错误详情:', error.response?.data)
        console.error('错误状态:', error.response?.status)
        console.error('错误消息:', error.response?.data?.message)
        console.error('验证错误详情:', JSON.stringify(error.response?.data?.errors, null, 2))

        // 显示详细的错误信息
        const errorMessage = error.response?.data?.message || '保存发货单失败'
        ElMessage.error(errorMessage)
      }
    }
  })
}

// 取消
const handleCancel = () => {
  router.back()
}

// 格式化货币
const formatCurrency = (amount: number) => {
  return amount ? `¥${amount.toFixed(2)}` : '¥0.00'
}

onMounted(async () => {
  if (!isNew.value) {
    await fetchDetail(route.params.id as string)
  } else {
    // 新建模式：设置默认发货日期为当日
    const today = new Date()
    form.delivery_date = today.toISOString().split('T')[0]

    // 检查是否从订单页面跳转过来
    const orderId = route.query.order_id
    if (orderId) {
      console.log('从订单页面跳转，订单ID:', orderId)
      form.order_id = String(orderId)
      await handleOrderChange(String(orderId))
    }
  }
})
</script>

<style scoped>
.delivery-edit {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-amount {
  margin-top: 20px;
  text-align: right;
  font-size: 16px;
}

.amount {
  font-size: 20px;
  font-weight: bold;
  color: #f56c6c;
  margin-left: 10px;
}

/* 已完全发货的产品行样式 */
:deep(.fully-delivered-row) {
  background-color: #f0f9ff !important;
}

:deep(.fully-delivered-row:hover) {
  background-color: #e1f5fe !important;
}

/* 已完全发货的产品名称样式 */
.fully-delivered {
  color: #67c23a;
  font-weight: 500;
}

/* 已完全发货的文字样式 */
.fully-delivered-text {
  color: #67c23a;
  font-weight: 500;
}
</style>
