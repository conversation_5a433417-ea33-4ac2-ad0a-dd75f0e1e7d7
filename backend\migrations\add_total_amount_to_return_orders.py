#!/usr/bin/env python3
"""
添加退货单总金额字段迁移脚本
"""

import sqlite3
import sys
import os
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def add_total_amount_column():
    """添加total_amount字段到return_orders表"""
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(return_orders)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'total_amount' not in columns:
            print("添加total_amount字段到return_orders表...")
            cursor.execute("""
                ALTER TABLE return_orders 
                ADD COLUMN total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00
            """)
            print("✓ total_amount字段添加成功")
        else:
            print("total_amount字段已存在，跳过添加")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"添加字段失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def calculate_and_update_total_amounts():
    """计算并更新所有现有退货单的总金额"""
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("开始计算现有退货单的总金额...")
        
        # 获取所有退货单
        cursor.execute("SELECT id FROM return_orders")
        return_order_ids = [row[0] for row in cursor.fetchall()]
        
        updated_count = 0
        
        for return_order_id in return_order_ids:
            # 计算该退货单的总金额
            cursor.execute("""
                SELECT 
                    roi.quantity,
                    op.unit_price,
                    op.discount,
                    op.tax_rate
                FROM return_order_items roi
                JOIN order_products op ON roi.order_product_id = op.id
                WHERE roi.return_order_id = ?
            """, (return_order_id,))
            
            items = cursor.fetchall()
            total_amount = Decimal('0.00')
            
            for quantity, unit_price, discount, tax_rate in items:
                if unit_price is not None and quantity is not None:
                    unit_price = Decimal(str(unit_price or 0))
                    discount_rate = Decimal(str(discount or 0)) / Decimal('100')
                    tax_rate_decimal = Decimal(str(tax_rate or 0)) / Decimal('100')
                    quantity_decimal = Decimal(str(quantity or 0))
                    
                    # 计算含税金额：单价 × (1 - 折扣率) × (1 + 税率) × 数量
                    item_total = unit_price * (Decimal('1') - discount_rate) * (Decimal('1') + tax_rate_decimal) * quantity_decimal
                    total_amount += item_total
            
            # 更新退货单总金额
            cursor.execute("""
                UPDATE return_orders 
                SET total_amount = ? 
                WHERE id = ?
            """, (str(total_amount), return_order_id))
            
            updated_count += 1
            print(f"✓ 退货单 {return_order_id} 总金额更新为: ¥{total_amount}")
        
        conn.commit()
        print(f"✓ 成功更新 {updated_count} 个退货单的总金额")
        return True
        
    except Exception as e:
        print(f"计算总金额失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("开始执行退货单总金额字段迁移...")
    
    # 1. 添加字段
    if not add_total_amount_column():
        print("❌ 添加字段失败，迁移终止")
        return False
    
    # 2. 计算并更新现有数据
    if not calculate_and_update_total_amounts():
        print("❌ 更新现有数据失败，迁移终止")
        return False
    
    print("✅ 退货单总金额字段迁移完成！")
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
