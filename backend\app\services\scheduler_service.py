"""
定时任务调度服务
使用APScheduler实现自动备份等定时任务
"""
import os
import logging
from datetime import datetime, time
from typing import Optional, Dict, Any
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from flask import current_app

from app import db
from app.models.system import BackupSettings
from app.services.backup_service import BackupService


class SchedulerService:
    """调度器服务类"""
    
    def __init__(self):
        self.scheduler = None
        self.backup_service = BackupService()
        self._initialized = False
        self.app = None
    
    def init_app(self, app):
        """初始化调度器"""
        self.app = app
        with app.app_context():
            try:
                # 创建调度器
                self.scheduler = BackgroundScheduler(
                    timezone='Asia/Shanghai',
                    job_defaults={
                        'coalesce': False,
                        'max_instances': 1,
                        'misfire_grace_time': 300  # 5分钟的容错时间
                    }
                )
                
                # 添加事件监听器
                self.scheduler.add_listener(self._job_executed, EVENT_JOB_EXECUTED)
                self.scheduler.add_listener(self._job_error, EVENT_JOB_ERROR)
                
                # 启动调度器
                self.scheduler.start()
                self._initialized = True
                
                # 加载现有的自动备份任务
                self._load_backup_jobs()
                
                app.logger.info("调度器服务启动成功")
                
            except Exception as e:
                app.logger.error(f"调度器服务启动失败: {str(e)}")
                raise
    
    def shutdown(self):
        """关闭调度器"""
        if self.scheduler and self.scheduler.running:
            self.scheduler.shutdown(wait=False)
            current_app.logger.info("调度器服务已关闭")
    
    def _job_executed(self, event):
        """任务执行成功回调"""
        try:
            if self.app:
                with self.app.app_context():
                    self.app.logger.info(f"任务执行成功: {event.job_id}")
            else:
                print(f"任务执行成功: {event.job_id}")
        except:
            print(f"任务执行成功: {event.job_id}")

    def _job_error(self, event):
        """任务执行失败回调"""
        try:
            if self.app:
                with self.app.app_context():
                    self.app.logger.error(f"任务执行失败: {event.job_id}, 错误: {event.exception}")
            else:
                print(f"任务执行失败: {event.job_id}, 错误: {event.exception}")
        except:
            print(f"任务执行失败: {event.job_id}, 错误: {event.exception}")
    
    def _load_backup_jobs(self):
        """加载自动备份任务"""
        try:
            settings = BackupSettings.query.first()
            if settings and settings.auto_backup_enabled:
                self.schedule_backup_job(settings)
                current_app.logger.info("自动备份任务已加载")
        except Exception as e:
            current_app.logger.error(f"加载自动备份任务失败: {str(e)}")
    
    def schedule_backup_job(self, settings: BackupSettings):
        """调度备份任务"""
        if not self._initialized or not self.scheduler:
            current_app.logger.warning("调度器未初始化，无法调度备份任务")
            return
        
        try:
            # 移除现有的备份任务
            self.remove_backup_job()
            
            if not settings.auto_backup_enabled:
                current_app.logger.info("自动备份已禁用")
                return
            
            # 解析备份时间
            backup_time_parts = settings.backup_time.split(':')
            hour = int(backup_time_parts[0])
            minute = int(backup_time_parts[1])
            second = int(backup_time_parts[2]) if len(backup_time_parts) > 2 else 0
            
            # 根据频率创建触发器
            trigger = self._create_backup_trigger(settings, hour, minute, second)
            
            if trigger:
                # 添加备份任务
                self.scheduler.add_job(
                    func=self._execute_auto_backup,
                    trigger=trigger,
                    id='auto_backup',
                    name='自动数据备份',
                    replace_existing=True,
                    kwargs={'settings_id': settings.id}
                )
                
                current_app.logger.info(f"自动备份任务已调度: {settings.backup_frequency} {settings.backup_time}")
            
        except Exception as e:
            current_app.logger.error(f"调度备份任务失败: {str(e)}")
            raise
    
    def _create_backup_trigger(self, settings: BackupSettings, hour: int, minute: int, second: int):
        """创建备份触发器"""
        if settings.backup_frequency == 'daily':
            # 每日备份
            return CronTrigger(
                hour=hour,
                minute=minute,
                second=second
            )
        elif settings.backup_frequency == 'weekly':
            # 每周备份
            day_of_week = settings.backup_day - 1 if settings.backup_day else 0  # 转换为0-6格式
            return CronTrigger(
                day_of_week=day_of_week,
                hour=hour,
                minute=minute,
                second=second
            )
        elif settings.backup_frequency == 'monthly':
            # 每月备份
            day = settings.backup_day if settings.backup_day else 1
            return CronTrigger(
                day=day,
                hour=hour,
                minute=minute,
                second=second
            )
        else:
            current_app.logger.error(f"不支持的备份频率: {settings.backup_frequency}")
            return None
    
    def remove_backup_job(self):
        """移除备份任务"""
        if not self._initialized or not self.scheduler:
            return
        
        try:
            if self.scheduler.get_job('auto_backup'):
                self.scheduler.remove_job('auto_backup')
                current_app.logger.info("自动备份任务已移除")
        except Exception as e:
            current_app.logger.error(f"移除备份任务失败: {str(e)}")
    
    def _execute_auto_backup(self, settings_id: int):
        """执行自动备份"""
        # 创建应用上下文
        with self.app.app_context():
            try:
                current_app.logger.info("开始执行自动备份")

                # 获取备份设置
                settings = BackupSettings.query.get(settings_id)
                if not settings or not settings.auto_backup_enabled:
                    current_app.logger.warning("自动备份已禁用或设置不存在")
                    return

                # 生成备份名称
                now = datetime.now()
                backup_name = f"自动备份_{now.strftime('%Y%m%d_%H%M%S')}"
                description = f"系统自动备份 - {settings.backup_frequency}"

                # 创建备份
                backup_record = self.backup_service.create_backup(
                    name=backup_name,
                    description=description,
                    backup_type='auto'
                )

                # 更新最后备份时间
                settings.last_backup_time = now
                db.session.commit()

                current_app.logger.info(f"自动备份完成: {backup_record.filename}")

            except Exception as e:
                current_app.logger.error(f"自动备份执行失败: {str(e)}")
                # 这里可以添加邮件通知或其他告警机制
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if not self._initialized or not self.scheduler:
            return None
        
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                return {
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.strftime('%Y-%m-%d %H:%M:%S') if job.next_run_time else None,
                    'trigger': str(job.trigger)
                }
        except Exception as e:
            current_app.logger.error(f"获取任务状态失败: {str(e)}")
        
        return None
    
    def get_backup_job_status(self) -> Optional[Dict[str, Any]]:
        """获取备份任务状态"""
        return self.get_job_status('auto_backup')
    
    def list_jobs(self) -> list:
        """列出所有任务"""
        if not self._initialized or not self.scheduler:
            return []
        
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.strftime('%Y-%m-%d %H:%M:%S') if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
            return jobs
        except Exception as e:
            current_app.logger.error(f"列出任务失败: {str(e)}")
            return []


# 创建全局调度器服务实例
scheduler_service = SchedulerService()
