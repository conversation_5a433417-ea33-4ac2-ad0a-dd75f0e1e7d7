import{u as e,b as t,r as l,d as a,w as n,e as u,f as i,o as s,g as o,h as d,i as c,a as r,c as f,j as p,n as m,k as _,l as v,m as h,F as x,t as b,T as g,K as w,p as y,q as F,s as k,v as q,x as M,y as C,z as E,A as T,B as D,C as I,D as S,E as G,G as P,H as j}from"./index-3d4c440c.js";import{_ as A}from"./_plugin-vue_export-helper-1b428a4d.js";const H={key:0},U={class:"header-left"},V={class:"header-right"},z={class:"user-info"},B={class:"username"},R={class:"tabs-view-container"},K={class:"main-content"},L=A({__name:"MainLayout",setup(A){const L=e(),O=t(),$=l(!1),J=l("管理员"),N=l("https://placehold.co/40x40/4A6FE3/FFFFFF.svg?text=HC"),Q=l(!1),W=a((()=>{const{meta:e,path:t}=L;return e.activeMenu?e.activeMenu:t})),X={"/":{title:"工作台",icon:"Monitor"},"/customers":{title:"客户管理",icon:"User"},"/customers/new":{title:"新增客户",icon:"User"},"/products":{title:"产品管理",icon:"Goods"},"/products/new":{title:"新增产品",icon:"Goods"},"/product-categories":{title:"产品分类",icon:"Goods"},"/orders":{title:"订单管理",icon:"Document"},"/orders/new":{title:"新增订单",icon:"Document"},"/delivery":{title:"送货单",icon:"Document"},"/return-orders":{title:"退货管理",icon:"Document"},"/quotations":{title:"报价管理",icon:"PriceTag"},"/quotations/new":{title:"新增报价单",icon:"PriceTag"},"/quotation-requests":{title:"报价需求表",icon:"PriceTag"},"/quotation-templates":{title:"报价模板",icon:"PriceTag"},"/finance":{title:"财务管理",icon:"Money"},"/payment-records":{title:"收款记录",icon:"Money"},"/refund-record":{title:"退款记录",icon:"Money"},"/receivables":{title:"应收款项",icon:"Money"},"/financial-reports":{title:"财务报表",icon:"Money"},"/settings":{title:"系统设置",icon:"Setting"},"/brands":{title:"品牌管理",icon:"Setting"},"/production-bases":{title:"生产基地",icon:"Setting"},"/data-backup":{title:"数据备份",icon:"Setting"}},Y=a((()=>{const e=[],t=L.path.split("/").filter(Boolean);let l="";return t.forEach((t=>{l+=`/${t}`;const a=X[l];a&&e.push({title:a.title,path:l})})),e})),Z=l([{title:"工作台",path:"/",icon:"Monitor"}]),ee=l([]),te=l("/");n((()=>L.path),(e=>{te.value=e,(e=>{var t;const{path:l}=e,a=X[l];a&&(Z.value.some((e=>e.path===l))||(Z.value.push({title:a.title,path:l,icon:a.icon}),(null==(t=e.meta)?void 0:t.keepAlive)&&ee.value.push(e.name)))})(L)}),{immediate:!0});const le=e=>{const t=Z.value;if(te.value===e){const l=t.findIndex((t=>t.path===e));l===t.length-1?te.value=t[l-1].path:te.value=t[l+1].path,O.push(te.value)}Z.value=t.filter((t=>t.path!==e));const l=ee.value.indexOf(L.name);l>-1&&ee.value.splice(l,1)},ae=e=>{O.push(e.props.name)},ne=()=>{$.value=!$.value},ue=()=>{document.fullscreenElement?document.exitFullscreen&&(document.exitFullscreen(),Q.value=!1):(document.documentElement.requestFullscreen(),Q.value=!0)},ie=()=>{localStorage.removeItem("token"),O.push("/login")};return u((()=>{const{path:e}=L,t=X[e];t&&"/"!==e&&Z.value.push({title:t.title,path:e,icon:t.icon})})),(e,t)=>{const l=i("el-icon"),a=i("el-menu-item"),n=i("el-sub-menu"),u=i("el-menu"),A=i("el-aside"),L=i("el-button"),O=i("el-breadcrumb-item"),Q=i("el-breadcrumb"),X=i("el-avatar"),se=i("el-dropdown-item"),oe=i("el-dropdown-menu"),de=i("el-dropdown"),ce=i("el-header"),re=i("el-tab-pane"),fe=i("el-tabs"),pe=i("router-view"),me=i("el-main"),_e=i("el-container");return s(),o(_e,{class:"main-container"},{default:d((()=>[c(A,{width:$.value?"64px":"220px",class:"aside"},{default:d((()=>[r("div",{class:m(["logo",{"collapsed-logo":$.value}])},[r("img",{src:"https://placehold.co/40x40/007bff/FFFFFF.svg?text=HC",alt:"华创物资",onError:t[0]||(t[0]=(...t)=>e.handleImgError&&e.handleImgError(...t))},null,32),$.value?p("",!0):(s(),f("span",H,"华创物资供应"))],2),c(u,{"default-active":W.value,class:"el-menu-vertical",router:!0,collapse:$.value,"collapse-transition":!1,"unique-opened":"","text-color":"#bfcbd9","active-text-color":"#ffffff"},{default:d((()=>[c(a,{index:"/"},{title:d((()=>t[2]||(t[2]=[_("工作台")]))),default:d((()=>[c(l,null,{default:d((()=>[c(v(F))])),_:1})])),_:1}),c(n,{index:"/customers"},{title:d((()=>[c(l,null,{default:d((()=>[c(v(k))])),_:1}),t[3]||(t[3]=r("span",null,"客户管理",-1))])),default:d((()=>[c(a,{index:"/customers"},{default:d((()=>t[4]||(t[4]=[_("客户列表")]))),_:1}),c(a,{index:"/customers/new"},{default:d((()=>t[5]||(t[5]=[_("新增客户")]))),_:1})])),_:1}),c(n,{index:"/products"},{title:d((()=>[c(l,null,{default:d((()=>[c(v(q))])),_:1}),t[6]||(t[6]=r("span",null,"产品管理",-1))])),default:d((()=>[c(a,{index:"/products"},{default:d((()=>t[7]||(t[7]=[_("产品列表")]))),_:1}),c(a,{index:"/products/new"},{default:d((()=>t[8]||(t[8]=[_("新增产品")]))),_:1}),c(a,{index:"/product-categories"},{default:d((()=>t[9]||(t[9]=[_("产品分类")]))),_:1})])),_:1}),c(n,{index:"/orders"},{title:d((()=>[c(l,null,{default:d((()=>[c(v(M))])),_:1}),t[10]||(t[10]=r("span",null,"订单管理",-1))])),default:d((()=>[c(a,{index:"/orders"},{default:d((()=>t[11]||(t[11]=[_("订单列表")]))),_:1}),c(a,{index:"/orders/new"},{default:d((()=>t[12]||(t[12]=[_("新增订单")]))),_:1}),c(a,{index:"/delivery"},{default:d((()=>t[13]||(t[13]=[_("送货单")]))),_:1}),c(a,{index:"/return-orders"},{default:d((()=>t[14]||(t[14]=[_("退货管理")]))),_:1}),c(a,{index:"/statements"},{default:d((()=>t[15]||(t[15]=[_("对账单")]))),_:1})])),_:1}),c(n,{index:"/quotation"},{title:d((()=>[c(l,null,{default:d((()=>[c(v(C))])),_:1}),t[16]||(t[16]=r("span",null,"报价管理",-1))])),default:d((()=>[c(a,{index:"/quotation-requests"},{default:d((()=>t[17]||(t[17]=[_("报价需求表")]))),_:1}),c(a,{index:"/quotations"},{default:d((()=>t[18]||(t[18]=[_("报价单列表")]))),_:1}),c(a,{index:"/quotation-templates"},{default:d((()=>t[19]||(t[19]=[_("报价模板")]))),_:1})])),_:1}),c(n,{index:"/finance"},{title:d((()=>[c(l,null,{default:d((()=>[c(v(E))])),_:1}),t[20]||(t[20]=r("span",null,"财务管理",-1))])),default:d((()=>[c(a,{index:"/payment-records"},{default:d((()=>t[21]||(t[21]=[_("收款记录")]))),_:1}),c(a,{index:"/refund-record"},{default:d((()=>t[22]||(t[22]=[_("退款记录")]))),_:1}),c(a,{index:"/receivables"},{default:d((()=>t[23]||(t[23]=[_("应收款项")]))),_:1})])),_:1}),c(n,{index:"/settings"},{title:d((()=>[c(l,null,{default:d((()=>[c(v(T))])),_:1}),t[24]||(t[24]=r("span",null,"系统设置",-1))])),default:d((()=>[c(a,{index:"/settings"},{default:d((()=>t[25]||(t[25]=[_("基本设置")]))),_:1}),c(a,{index:"/brands"},{default:d((()=>t[26]||(t[26]=[_("品牌管理")]))),_:1}),c(a,{index:"/data-backup"},{default:d((()=>t[27]||(t[27]=[_("数据备份")]))),_:1})])),_:1})])),_:1},8,["default-active","collapse"])])),_:1},8,["width"]),c(_e,{class:"right-container"},{default:d((()=>[c(ce,{class:"header"},{default:d((()=>[r("div",U,[c(L,{link:"",onClick:ne,class:"collapse-btn"},{default:d((()=>[c(l,null,{default:d((()=>[$.value?(s(),o(v(I),{key:1})):(s(),o(v(D),{key:0}))])),_:1})])),_:1}),c(Q,{separator:"/"},{default:d((()=>[c(O,{to:{path:"/"}},{default:d((()=>t[28]||(t[28]=[_("首页")]))),_:1}),(s(!0),f(x,null,h(Y.value,((e,t)=>(s(),o(O,{key:t,to:e.path},{default:d((()=>[_(b(e.title),1)])),_:2},1032,["to"])))),128))])),_:1})]),r("div",V,[c(L,{link:"",onClick:ue},{default:d((()=>[c(l,null,{default:d((()=>[c(v(S))])),_:1})])),_:1}),c(de,null,{dropdown:d((()=>[c(oe,null,{default:d((()=>[c(se,null,{default:d((()=>[c(l,null,{default:d((()=>[c(v(G))])),_:1}),t[29]||(t[29]=_("个人信息 "))])),_:1}),c(se,null,{default:d((()=>[c(l,null,{default:d((()=>[c(v(P))])),_:1}),t[30]||(t[30]=_("修改密码 "))])),_:1}),c(se,{divided:"",onClick:ie},{default:d((()=>[c(l,null,{default:d((()=>[c(v(j))])),_:1}),t[31]||(t[31]=_("退出登录 "))])),_:1})])),_:1})])),default:d((()=>[r("span",z,[c(X,{size:32,src:N.value},{default:d((()=>[_(b(J.value.substring(0,1)),1)])),_:1},8,["src"]),r("span",B,b(J.value),1)])])),_:1})])])),_:1}),c(me,{class:"main"},{default:d((()=>[r("div",R,[c(fe,{modelValue:te.value,"onUpdate:modelValue":t[1]||(t[1]=e=>te.value=e),type:"card",class:"tabs-view",onTabRemove:le,onTabClick:ae},{default:d((()=>[(s(!0),f(x,null,h(Z.value,(e=>(s(),o(re,{key:e.path,label:e.title,name:e.path,closable:"/"!==e.path},null,8,["label","name","closable"])))),128))])),_:1},8,["modelValue"])]),r("div",K,[c(pe,null,{default:d((({Component:e})=>[c(g,{name:"fade-transform",mode:"out-in"},{default:d((()=>[(s(),o(w,{include:ee.value},[(s(),o(y(e)))],1032,["include"]))])),_:2},1024)])),_:1})])])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-375f10a9"]]);export{L as default};
