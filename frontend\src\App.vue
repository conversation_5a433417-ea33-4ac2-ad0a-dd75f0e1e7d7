<script setup lang="ts">
// App.vue - 应用根组件
// 只需要包含RouterView，让路由系统处理页面渲染
import { RouterView } from 'vue-router'
</script>

<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  margin: 0;
  padding: 0;
  max-width: none;
}

/* Element Plus 全局样式调整 */
.el-message-box {
  border-radius: 8px;
}

.el-button {
  border-radius: 4px;
}

.el-card {
  border-radius: 8px;
}
</style>
