"""
报价相关模型
包括报价单、报价项目、报价需求、报价模板
"""
from app.models.base import BaseModel
from app import db
from typing import Optional
from datetime import datetime


class Quotation(BaseModel):
    """报价单表模型"""
    __tablename__ = 'quotations'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    quotation_number = db.Column(db.String(50), nullable=False, unique=True, comment='报价单编号')
    request_id = db.Column(db.Integer, db.ForeignKey('quotation_requests.id'), nullable=True, comment='关联报价需求表ID')
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False, comment='客户ID')
    project_name = db.Column(db.String(100), nullable=False, comment='项目名称')
    project_address = db.Column(db.String(200), nullable=True, comment='项目地址')
    valid_until = db.Column(db.DateTime, nullable=False, comment='有效期至')
    payment_terms = db.Column(db.String(100), nullable=True, comment='付款条件')
    delivery_terms = db.Column(db.String(100), nullable=True, comment='交货条件')
    status = db.Column(db.String(20), nullable=False, default='待确认', comment='状态')
    total_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0, comment='总金额')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 关联关系
    customer = db.relationship('Customer', back_populates='quotations')
    quotation_request = db.relationship('QuotationRequest', back_populates='quotations')
    items = db.relationship('QuotationItem', back_populates='quotation', cascade='all, delete-orphan')

    def get_related_orders(self):
        """获取使用该报价单的订单列表"""
        from .order import Order
        import json

        orders = []
        all_orders = Order.query.filter(Order.quotation_ids.isnot(None)).all()

        for order in all_orders:
            try:
                quotation_ids = json.loads(order.quotation_ids)
                if self.id in quotation_ids:
                    orders.append(order)
            except:
                continue

        return orders

    def __repr__(self) -> str:
        return f"<Quotation {self.quotation_number}>"
    
    def calculate_total_amount(self) -> float:
        """计算报价单总金额"""
        total = 0.0
        for item in self.items:
            total += float(item.total_price) if item.total_price else 0.0
        return total
    
    def update_status(self, status: str) -> None:
        """更新报价单状态"""
        self.status = status
        self.updated_at = datetime.now()
        db.session.commit()


class QuotationItem(BaseModel):
    """报价单项目表模型"""
    __tablename__ = 'quotation_items'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    quotation_id = db.Column(db.Integer, db.ForeignKey('quotations.id'), nullable=False, comment='报价单ID')
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False, comment='产品ID')
    product_specification_id = db.Column(db.Integer, db.ForeignKey('product_specifications.id'), nullable=False, comment='产品规格ID')
    quantity = db.Column(db.Integer, nullable=False, default=1, comment='数量')
    unit_price = db.Column(db.Numeric(10, 2), nullable=False, comment='单价')
    discount = db.Column(db.Numeric(5, 2), nullable=True, comment='折扣')
    tax_rate = db.Column(db.Numeric(5, 2), nullable=True, default=13, comment='税率')
    total_price = db.Column(db.Numeric(12, 2), nullable=False, comment='总价')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 冗余字段，保存快照数据
    product_name = db.Column(db.String(100), nullable=False, comment='产品名称(冗余)')
    product_model = db.Column(db.String(50), nullable=False, comment='产品型号(冗余)')
    product_specification = db.Column(db.String(100), nullable=True, comment='产品规格(冗余)')
    product_unit = db.Column(db.String(20), nullable=False, comment='产品单位(冗余)')

    # 关联关系
    quotation = db.relationship('Quotation', back_populates='items')
    product = db.relationship('Product')
    specification = db.relationship('ProductSpecification')

    def __repr__(self) -> str:
        return f"<QuotationItem {self.id}>"
    
    def calculate_total_price(self) -> float:
        """计算项目总价 (基于快照价格/折扣/税率)"""
        price = float(self.unit_price)
        discount_percentage = float(self.discount) if self.discount is not None else 0.0
        tax_rate_percentage = float(self.tax_rate) if self.tax_rate is not None else 0.0

        # 应用折扣：折扣是打折力度，20%折扣意味着最终价格 = (1-0.2) × 单价
        if 0 <= discount_percentage <= 100:
            price = price * (1 - discount_percentage / 100)

        # 应用税率：税率是在折扣后价格基础上增加的
        if tax_rate_percentage > 0:
            price = price * (1 + tax_rate_percentage / 100)

        return price * float(self.quantity)
    
    def update_total_price(self) -> None:
        """更新项目总价"""
        self.total_price = self.calculate_total_price()


class QuotationRequest(BaseModel):
    """报价需求表模型"""
    __tablename__ = 'quotation_requests'

    id = db.Column(db.Integer, primary_key=True, index=True)
    request_number = db.Column(db.String(50), nullable=False, unique=True, comment='需求表编号')
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=True, comment='客户ID')
    project_name = db.Column(db.String(100), nullable=True, comment='项目名称')
    project_address = db.Column(db.String(200), nullable=True, comment='项目地址')
    quotation_date = db.Column(db.Date, default=datetime.utcnow, comment='报价日期')
    expected_date = db.Column(db.Date, nullable=True, comment='期望交付日期/期望报价日期')
    expiry_date = db.Column(db.Date, nullable=True, comment='截止日期')
    notes = db.Column(db.Text, nullable=True, comment='备注')
    status = db.Column(db.String(20), nullable=False, default='待确认', comment='状态')

    # 关联关系
    customer = db.relationship('Customer', back_populates='quotation_requests')
    items = db.relationship('QuotationRequestItem', back_populates='quotation_request', cascade='all, delete-orphan')
    quotations = db.relationship('Quotation', back_populates='quotation_request')

    def __repr__(self) -> str:
        return f"<QuotationRequest {self.request_number}>"
    
    def update_status(self, status: str) -> None:
        """更新报价需求表状态"""
        self.status = status
        self.updated_at = datetime.now()
        db.session.commit()


class QuotationRequestItem(BaseModel):
    """报价需求表项目表模型"""
    __tablename__ = 'quotation_request_items'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    request_id = db.Column(db.Integer, db.ForeignKey('quotation_requests.id'), nullable=False, comment='报价需求表ID')

    # 用户原始输入/从Excel导入的字段
    original_product_name = db.Column(db.String(200), nullable=True, comment='原始产品名称')
    original_product_model = db.Column(db.String(100), nullable=True, comment='原始产品型号')
    original_product_spec = db.Column(db.String(200), nullable=True, comment='原始产品规格')
    original_unit = db.Column(db.String(50), nullable=True, comment='原始单位')

    # 用于前端直接编辑的字段，也作为匹配的输入源之一
    product_name = db.Column(db.String(100), nullable=False, comment='产品名称')
    product_model = db.Column(db.String(100), nullable=True, comment='产品型号')
    product_spec = db.Column(db.String(100), nullable=True, comment='产品规格')
    quantity = db.Column(db.Integer, nullable=False, default=1, comment='数量')
    unit = db.Column(db.String(20), nullable=False, default='个', comment='单位')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 匹配结果字段
    matched_product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=True, comment='匹配的产品ID')
    matched_product_specification_id = db.Column(db.Integer, db.ForeignKey('product_specifications.id'), nullable=True, comment='匹配的产品规格ID')
    match_notes = db.Column(db.Text, nullable=True, comment='匹配备注信息')
    match_type = db.Column(db.String(20), nullable=True, comment='匹配类型：manual手动，auto自动')

    # 关联关系
    quotation_request = db.relationship('QuotationRequest', back_populates='items')
    matched_product = db.relationship('Product', foreign_keys=[matched_product_id])
    matched_specification = db.relationship('ProductSpecification', foreign_keys=[matched_product_specification_id])

    def __repr__(self) -> str:
        return f"<QuotationRequestItem {self.product_name}>"

    def set_matched_info(self, product_id: Optional[int] = None, specification_id: Optional[int] = None) -> None:
        """设置匹配到的产品ID和规格ID"""
        if product_id is not None:
            self.matched_product_id = product_id
        if specification_id is not None:
            self.matched_product_specification_id = specification_id
        self.updated_at = datetime.now()


class QuotationTemplate(BaseModel):
    """报价模板表模型"""
    __tablename__ = 'quotation_templates'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False, unique=True, comment='模板名称')
    description = db.Column(db.Text, nullable=True, comment='模板描述')
    is_default = db.Column(db.Boolean, default=False, nullable=False, comment='是否为默认模板')

    # 关联关系
    items = db.relationship('QuotationTemplateItem', back_populates='template', cascade='all, delete-orphan')

    def __repr__(self) -> str:
        return f"<QuotationTemplate {self.name}>"


class QuotationTemplateItem(BaseModel):
    """报价模板项目表模型"""
    __tablename__ = 'quotation_template_items'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    template_id = db.Column(db.Integer, db.ForeignKey('quotation_templates.id'), nullable=False, comment='报价模板ID')
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False, comment='产品ID')
    product_specification_id = db.Column(db.Integer, db.ForeignKey('product_specifications.id'), nullable=False, comment='产品规格ID')
    quantity = db.Column(db.Integer, nullable=False, default=1, comment='数量')
    unit_price = db.Column(db.Numeric(10, 2), nullable=True, comment='单价(可选,若无则取产品当前价格)')
    discount = db.Column(db.Numeric(5, 2), nullable=True, comment='折扣(百分比,可选)')
    tax_rate = db.Column(db.Numeric(5, 2), nullable=True, comment='税率(百分比,可选,若无则取产品当前税率)')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 产品快照字段 (冗余存储，避免产品信息变更影响历史模板)
    product_name_snapshot = db.Column(db.String(100), nullable=True, comment='产品名称快照')
    product_model_snapshot = db.Column(db.String(50), nullable=True, comment='产品型号快照')
    product_specification_snapshot = db.Column(db.String(100), nullable=True, comment='产品规格快照')
    product_unit_snapshot = db.Column(db.String(20), nullable=True, comment='产品单位快照')

    # 关联关系
    template = db.relationship('QuotationTemplate', back_populates='items')
    product = db.relationship('Product')
    specification = db.relationship('ProductSpecification')

    def __repr__(self) -> str:
        return f"<QuotationTemplateItem ProductID:{self.product_id} SpecID:{self.product_specification_id}>"
