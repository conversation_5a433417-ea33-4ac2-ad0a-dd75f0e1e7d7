#!/usr/bin/env python3
"""
检查报价单状态，特别是"已转订单"状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.quotation import Quotation

def check_quotation_status():
    """检查报价单状态"""
    app = create_app()
    
    with app.app_context():
        print("🔍 检查报价单状态...")
        print("=" * 60)
        
        try:
            # 查询所有报价单
            quotations = Quotation.query.all()
            
            print(f"📊 总报价单数量: {len(quotations)}")
            
            # 统计各种状态
            status_count = {}
            for quotation in quotations:
                status = quotation.status
                status_count[status] = status_count.get(status, 0) + 1
            
            print(f"\n📋 状态统计:")
            for status, count in status_count.items():
                print(f"   {status}: {count}个")
            
            # 查找"已转订单"状态的报价单
            converted_quotations = Quotation.query.filter_by(status='已转订单').all()
            
            if converted_quotations:
                print(f"\n⚠️  发现 {len(converted_quotations)} 个'已转订单'状态的报价单:")
                for quotation in converted_quotations:
                    print(f"   - {quotation.quotation_number}: {quotation.project_name}")
                    print(f"     客户: {quotation.customer.name if quotation.customer else '未知'}")
                    print(f"     创建时间: {quotation.created_at}")
                    print(f"     状态: {quotation.status}")
                    print()
                
                # 询问是否要修改这些状态
                print("💡 建议操作:")
                print("   1. 将'已转订单'状态改为'已确认'")
                print("   2. 或者删除这些报价单")
                
                choice = input("\n请选择操作 (1=改为已确认, 2=删除, 其他=跳过): ")
                
                if choice == '1':
                    for quotation in converted_quotations:
                        quotation.status = '已确认'
                        print(f"   ✅ {quotation.quotation_number} 状态已改为'已确认'")
                    
                    db.session.commit()
                    print(f"\n🎉 已成功修改 {len(converted_quotations)} 个报价单的状态!")

                elif choice == '2':
                    for quotation in converted_quotations:
                        print(f"   🗑️ 删除报价单: {quotation.quotation_number}")
                        db.session.delete(quotation)

                    db.session.commit()
                    print(f"\n🎉 已成功删除 {len(converted_quotations)} 个报价单!")
                
                else:
                    print("\n⏭️ 跳过修改")
            
            else:
                print(f"\n✅ 没有发现'已转订单'状态的报价单")
            
            # 显示当前有效的状态列表
            print(f"\n📝 当前系统支持的状态:")
            valid_statuses = ['待确认', '已确认', '已拒绝', '已过期']
            for status in valid_statuses:
                print(f"   - {status}")
            
        except Exception as e:
            print(f"❌ 检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_quotation_status()
