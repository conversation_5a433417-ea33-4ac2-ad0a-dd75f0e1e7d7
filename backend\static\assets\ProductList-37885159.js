import{J as e,r as a,d as l,e as t,w as i,f as s,M as o,o as r,c as n,g as c,N as u,h as d,i as p,l as m,F as g,m as v,j as f,k as _,a as y,U as b,a4 as h,t as w,b as k,L as x,W as C,V,aa as S,X as U,Y as P,Z as z,_ as $,$ as I,a0 as N,a1 as D,a2 as A,a3 as E,ab as T,ac as O,x as L}from"./index-3d4c440c.js";import{s as B,l as j,d as F,p as J,g as W,e as M}from"./product-ab10366a.js";import{l as R}from"./productCategory-3670b9eb.js";import{g as H}from"./order-d6035a15.js";import{_ as K}from"./_plugin-vue_export-helper-1b428a4d.js";import{a as q,b as X}from"./apiAdapters-232bdd53.js";import{d as Y}from"./download-f99c5d93.js";const G={class:"product-import-preview"},Z={key:0},Q={key:0},ee={key:0},ae={key:0},le={class:"spec-row"},te={key:0,class:"attribute-list"},ie={key:3,class:"batch-actions mt-20 mb-20"},se={key:0,class:"attribute-editor"},oe={class:"attribute-list"},re={class:"dialog-footer"},ne=K(e({__name:"ProductImportPreview",props:{previewData:{type:Array,default:()=>[]},categories:{type:Array,default:()=>[]},mappings:{type:Object,default:()=>({})}},emits:["success","cancel","update:previewData"],setup(e,{emit:k}){const x=e,C=k,V=a(!1),S=a(!1),U=a(!1),P=a(null),z=a(-1),$=a([]),I=a([]),N=l((()=>JSON.parse(JSON.stringify(x.categories)).sort(((e,a)=>{if((e.level||0)!==(a.level||0))return(e.level||0)-(a.level||0);const l=(e.label||e.name||"").toLowerCase(),t=(a.label||a.name||"").toLowerCase();return l.localeCompare(t)}))));t((()=>{console.log("ProductImportPreview 已挂载，检查数据:",{propsData:x.previewData,localData:I.value,categories:x.categories.length,mappings:x.mappings}),x.previewData&&x.previewData.length>0&&D(x.previewData)}));const D=e=>{console.log("处理预览数据:",e);try{I.value=JSON.parse(JSON.stringify(e)),I.value=I.value.map(((e,a)=>(console.log(`处理第${a+1}个产品:`,e.name),e.preview_id||(e.preview_id=a+1),e.specifications&&Array.isArray(e.specifications)&&0!==e.specifications.length||(e.specifications=[{specification:"默认规格",cost_price:0,suggested_price:0,tax_rate:13}]),e.attributes&&Array.isArray(e.attributes)||(e.attributes=[]),e))),console.log("ProductImportPreview: 最终处理后的数据:",I.value)}catch(a){console.error("处理预览数据出错:",a),I.value=[]}};i((()=>x.previewData),(e=>{console.log("ProductImportPreview: previewData变化",e),e&&Array.isArray(e)&&e.length>0?D(e):(console.warn("ProductImportPreview: 收到无效的previewData",e),I.value=[])}),{deep:!0,immediate:!0});const A=e=>{P.value=e,z.value=I.value.findIndex((a=>a.preview_id===e.preview_id)),$.value=JSON.parse(JSON.stringify(e.attributes||[])),U.value=!0},E=()=>{$.value.push({attribute_name:"",attribute_value:""})},T=()=>{if(P.value&&z.value>=0){const e=$.value.filter((e=>e.attribute_name.trim()));I.value[z.value].attributes=e}U.value=!1},O=()=>{const e=Math.max(0,...I.value.map((e=>e.preview_id)))+1;let a=null;if(x.categories.length>0){const e=x.categories[0];a=void 0!==e.id?e.id:void 0!==e.value?e.value:null}I.value.push({preview_id:e,name:"",model:"",unit:"个",status:"正常",category_id:a,specifications:[{specification:"默认规格",cost_price:0,suggested_price:0,tax_rate:13}],attributes:[]})},L=async()=>{console.log("准备提交产品导入数据",I.value);const e=(()=>{const e=[];return I.value.forEach(((a,l)=>{a.name&&""!==a.name.trim()||e.push(`第 ${l+1} 行产品名称不能为空`),a.specifications&&Array.isArray(a.specifications)&&0!==a.specifications.length?a.specifications.forEach(((t,i)=>{t.specification&&""!==t.specification.trim()||e.push(`第 ${l+1} 行产品 "${a.name||"未命名"}" 的第 ${i+1} 个规格名称不能为空`)})):e.push(`第 ${l+1} 行产品 "${a.name||"未命名"}" 缺少规格信息`),a.category_id||e.push(`第 ${l+1} 行产品 "${a.name||"未命名"}" 未选择产品类别`)})),e})();if(e.length>0)b.error(`数据有误，请修正后再提交：${e[0]}`);else{S.value=!0;try{const e=I.value.map((e=>{const a={preview_id:e.preview_id,name:e.name,model:e.model||"",unit:e.unit||"个",category_id:Number(e.category_id)||null,status:e.status||"active"};return e.specifications&&Array.isArray(e.specifications)?a.specifications=e.specifications.map((a=>{let l=a.suggested_price;const t=e.attributes&&e.attributes.find((e=>["市场价","建议售价","售价","单价","价格"].includes(e.attribute_name)));return t&&!isNaN(Number(t.attribute_value))&&(l=Number(t.attribute_value)),{specification:a.specification||"默认规格",cost_price:void 0!==a.cost_price?Number(a.cost_price):0,suggested_price:void 0!==l?Number(l):0,tax_rate:void 0!==a.tax_rate?Number(a.tax_rate):13,...void 0!==a.min_price&&null!==a.min_price?{min_price:Number(a.min_price)}:{},...void 0!==a.max_price&&null!==a.max_price?{max_price:Number(a.max_price)}:{}}})):a.specifications=[{specification:"默认规格",cost_price:0,suggested_price:0,tax_rate:13}],e.attributes&&Array.isArray(e.attributes)&&(a.attributes=e.attributes.filter((e=>e.attribute_name&&e.attribute_name.trim())).map((e=>({attribute_name:e.attribute_name.trim(),attribute_value:e.attribute_value||""})))),a}));console.log("提交的导入数据:",e),b.info("正在导入产品数据，请稍候...");const a=await B(e);if(console.log("导入响应:",a),!a||0!==a.code&&200!==a.code&&"success"!==a.status){const e=(null==a?void 0:a.message)||(null==a?void 0:a.msg)||"导入失败，请检查数据格式";b.error(e),console.error("导入失败详情:",a)}else b.success(a.message||"产品导入成功"),C("success",a.data||a)}catch(a){console.error("提交导入失败:",a);let e="提交导入失败";if(a.response){const l=a.response.data;l&&("string"==typeof l?e=l:l.message?e=l.message:l.error&&(e=l.error))}else a.message&&(e=a.message);b.error(e)}finally{S.value=!1}}},j=e=>{const a=e.label||e.name||"未命名类别",l=e.level||0;return`${l>0?"　".repeat(l):""}${a}`};return(e,a)=>{const l=s("el-alert"),t=s("el-icon"),i=s("el-button"),b=s("el-table-column"),k=s("el-input"),C=s("el-option"),z=s("el-select"),D=s("el-input-number"),B=s("el-tag"),F=s("el-table"),J=s("el-dialog"),W=o("loading");return r(),n("div",G,[I.value.length>0?(r(),c(l,{key:0,type:"info",title:"请确认识别结果是否正确，可以进行编辑",closable:!1,"show-icon":"",class:"mb-20"})):(r(),c(l,{key:1,type:"warning",title:"未能识别任何有效产品数据，请检查Excel文件格式",closable:!1,"show-icon":"",class:"mb-20"})),I.value.length>0?u((r(),c(F,{key:2,data:I.value,border:"",stripe:"",style:{width:"100%"},"max-height":"500px"},{default:d((()=>[p(b,{fixed:"left",label:"操作",width:"80",align:"center"},{default:d((({row:e,$index:a})=>[p(i,{type:"danger",size:"small",circle:"",onClick:e=>{return l=a,void I.value.splice(l,1);var l},title:"删除此行"},{default:d((()=>[p(t,null,{default:d((()=>[p(m(h))])),_:1})])),_:2},1032,["onClick"])])),_:1}),p(b,{label:"产品名称","min-width":"150"},{default:d((({row:e})=>[p(k,{modelValue:e.name,"onUpdate:modelValue":a=>e.name=a,placeholder:"必填",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(b,{label:"产品型号","min-width":"120"},{default:d((({row:e})=>[p(k,{modelValue:e.model,"onUpdate:modelValue":a=>e.model=a,placeholder:"型号",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(b,{label:"单位",width:"80"},{default:d((({row:e})=>[p(k,{modelValue:e.unit,"onUpdate:modelValue":a=>e.unit=a,placeholder:"个",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),p(b,{label:"产品类别","min-width":"150"},{default:d((({row:e})=>[p(z,{modelValue:e.category_id,"onUpdate:modelValue":a=>e.category_id=a,placeholder:"选择类别",size:"small",style:{width:"100%"}},{default:d((()=>{return[(r(!0),n(g,null,v(N.value,(e=>(r(),c(C,{key:e.value||e.id,label:j(e),value:e.value||e.id},null,8,["label","value"])))),128)),!e.category_name||(a=e.category_id,a&&x.categories.find((e=>e.id===a||e.value===a)))?f("",!0):(r(),c(C,{key:0,label:`创建新类别: ${e.category_name}`,value:-1},null,8,["label"]))];var a})),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),p(b,{label:"规格","min-width":"120"},{default:d((({row:e})=>[e.specifications&&e.specifications.length?(r(),n("div",Z,[(r(!0),n(g,null,v(e.specifications,((e,a)=>(r(),n("div",{key:a,class:"specification-item"},[p(k,{modelValue:e.specification,"onUpdate:modelValue":a=>e.specification=a,placeholder:"规格名称",size:"small",style:{"margin-bottom":"5px"}},null,8,["modelValue","onUpdate:modelValue"])])))),128)),p(i,{type:"primary",link:"",size:"small",onClick:a=>{e.specifications.push({specification:"新规格",cost_price:0,suggested_price:0,tax_rate:0})}},{default:d((()=>a[2]||(a[2]=[_(" 添加规格 ")]))),_:2},1032,["onClick"])])):f("",!0)])),_:1}),p(b,{label:"成本价",width:"100"},{default:d((({row:e})=>[e.specifications&&e.specifications.length?(r(),n("div",Q,[(r(!0),n(g,null,v(e.specifications,((e,a)=>(r(),n("div",{key:a,class:"specification-item"},[p(D,{modelValue:e.cost_price,"onUpdate:modelValue":a=>e.cost_price=a,precision:2,step:.1,min:0,"controls-position":"right",size:"small",style:{width:"100%","margin-bottom":"5px"}},null,8,["modelValue","onUpdate:modelValue"])])))),128))])):f("",!0)])),_:1}),p(b,{label:"建议售价",width:"100"},{default:d((({row:e})=>[e.specifications&&e.specifications.length?(r(),n("div",ee,[(r(!0),n(g,null,v(e.specifications,((e,a)=>(r(),n("div",{key:a,class:"specification-item"},[p(D,{modelValue:e.suggested_price,"onUpdate:modelValue":a=>e.suggested_price=a,precision:2,step:.1,min:0,"controls-position":"right",size:"small",style:{width:"100%","margin-bottom":"5px"}},null,8,["modelValue","onUpdate:modelValue"])])))),128))])):f("",!0)])),_:1}),p(b,{label:"税率(%)",width:"100"},{default:d((({row:e})=>[e.specifications&&e.specifications.length?(r(),n("div",ae,[(r(!0),n(g,null,v(e.specifications,((a,l)=>(r(),n("div",{key:l,class:"specification-item"},[y("div",le,[p(D,{modelValue:a.tax_rate,"onUpdate:modelValue":e=>a.tax_rate=e,precision:2,step:.5,min:0,max:100,"controls-position":"right",size:"small",style:{width:"80%","margin-bottom":"5px"}},null,8,["modelValue","onUpdate:modelValue"]),e.specifications.length>1?(r(),c(i,{key:0,type:"danger",size:"small",circle:"",style:{"margin-left":"4px"},onClick:a=>{return t=l,void e.specifications.splice(t,1);var t}},{default:d((()=>[p(t,null,{default:d((()=>[p(m(h))])),_:1})])),_:2},1032,["onClick"])):f("",!0)])])))),128))])):f("",!0)])),_:1}),p(b,{label:"自定义属性","min-width":"150"},{default:d((({row:e})=>[e.attributes&&e.attributes.length>0?(r(),n("div",te,[(r(!0),n(g,null,v(e.attributes,((e,a)=>(r(),c(B,{key:a,size:"small",class:"attribute-tag"},{default:d((()=>[_(w(e.attribute_name)+": "+w(e.attribute_value),1)])),_:2},1024)))),128)),e.attributes.length>0?(r(),c(i,{key:0,type:"primary",size:"small",link:"",onClick:a=>A(e)},{default:d((()=>a[3]||(a[3]=[_(" 编辑属性 ")]))),_:2},1032,["onClick"])):f("",!0)])):(r(),c(i,{key:1,type:"primary",link:"",size:"small",onClick:a=>A(e)},{default:d((()=>a[4]||(a[4]=[_(" 添加属性 ")]))),_:2},1032,["onClick"]))])),_:1})])),_:1},8,["data"])),[[W,V.value]]):f("",!0),I.value.length>0?(r(),n("div",ie,[p(i,{type:"primary",onClick:L,loading:S.value,disabled:0===I.value.length},{default:d((()=>a[5]||(a[5]=[_(" 提交导入 ")]))),_:1},8,["loading","disabled"]),p(i,{onClick:O},{default:d((()=>a[6]||(a[6]=[_(" 添加空白产品 ")]))),_:1})])):f("",!0),p(J,{modelValue:U.value,"onUpdate:modelValue":a[1]||(a[1]=e=>U.value=e),title:"编辑自定义属性",width:"500px"},{footer:d((()=>[y("span",re,[p(i,{onClick:a[0]||(a[0]=e=>U.value=!1)},{default:d((()=>a[8]||(a[8]=[_("取消")]))),_:1}),p(i,{type:"primary",onClick:T},{default:d((()=>a[9]||(a[9]=[_("确定")]))),_:1})])])),default:d((()=>[P.value?(r(),n("div",se,[y("div",oe,[(r(!0),n(g,null,v($.value,((e,a)=>(r(),n("div",{key:a,class:"attribute-item"},[p(k,{modelValue:e.attribute_name,"onUpdate:modelValue":a=>e.attribute_name=a,placeholder:"属性名",size:"small",class:"attribute-name"},null,8,["modelValue","onUpdate:modelValue"]),p(k,{modelValue:e.attribute_value,"onUpdate:modelValue":a=>e.attribute_value=a,placeholder:"属性值",size:"small",class:"attribute-value"},null,8,["modelValue","onUpdate:modelValue"]),p(i,{type:"danger",icon:"Delete",circle:"",size:"small",onClick:e=>(e=>{$.value.splice(e,1)})(a)},null,8,["onClick"])])))),128))]),p(i,{type:"primary",onClick:E},{default:d((()=>a[7]||(a[7]=[_("添加属性")]))),_:1})])):f("",!0)])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-ab54b1d8"]]),ce={class:"product-list"},ue={class:"flex-between"},de={key:0,class:"mt-5"},pe={key:0},me={key:1},ge={key:0},ve={key:1},fe={key:0},_e={key:1},ye={class:"flex-between mt-20"},be={class:"flex-between"},he={class:"product-name"},we={class:"product-image"},ke={class:"image-slot"},xe={class:"product-info mt-10"},Ce={class:"product-actions mt-10"},Ve={class:"flex-center mt-20"},Se={class:"import-dialog-content"},Ue={key:1},Pe={class:"import-tips mb-20"},ze={key:0,class:"selected-file mt-20"},$e={class:"file-info"},Ie={class:"file-size"},Ne={key:2},De={key:3,class:"import-result"},Ae={key:2,class:"messages-container mt-20"},Ee={class:"import-messages"},Te={class:"import-messages error-messages"},Oe={class:"dialog-footer"},Le=K(e({__name:"ProductList",setup(e){const i=k(),B=a(!1),K=a("table"),G=a([]),Z=a([]),Q=a([]),ee=a(0),ae=a(),le=a(!1),te=a(!1),ie=a(null),se=a(null),oe=a(0),re=a([]),Le=a({}),Be=x({name:"",model:"",category_id:void 0,status:void 0}),je=x({currentPage:1,pageSize:10,total:0}),Fe=l((()=>{const e=[];return G.value&&0!==G.value.length?(console.log("处理产品数据用于显示:",G.value),G.value.forEach((a=>{if(!a)return void console.warn("发现空的产品数据项");console.log("产品详情:",JSON.stringify(a));let l=[];a.specifications&&Array.isArray(a.specifications)&&a.specifications.length>0?(l=a.specifications,console.log(`产品 ${a.name} 有 ${l.length} 个规格`)):(l=[{id:null,specification:"默认规格",cost_price:null,suggested_price:null,tax_rate:null}],console.log(`产品 ${a.name} 没有规格，使用默认规格`));const t=l.length;l.forEach(((l,i)=>{e.push({productId:a.id,name:a.name,model:a.model,unit:a.unit||"个",category_name:a.category_name||"未分类",status:a.status||"active",image:a.image,notes:a.notes,specId:l.id,specification:l.specification||"默认规格",cost_price:l.cost_price,suggested_price:l.suggested_price,tax_rate:l.tax_rate,isFirstSpec:0===i,numSpecs:t,originalProduct:a})}))})),console.log("生成的展平列表数据:",e),e):(console.log("没有产品数据可以展示"),e)})),Je=async()=>{B.value=!0,ee.value+=1;try{const e={page:je.currentPage,per_page:je.pageSize,with_specifications:!0};Be.name&&Be.name.trim()&&(e.name=Be.name.trim()),Be.model&&Be.model.trim()&&(e.model=Be.model.trim()),Be.category_id&&(e.category_id=Be.category_id),Be.status&&(e.status="active"===Be.status?"正常":"禁用"),console.log("发送产品列表请求参数:",e);const a=await j(e);console.log("获取到的产品列表数据 (来自拦截器):",a);const l=q(a);console.log("适配后的产品数据:",l),l.items&&l.items.length>0?(G.value=l.items.map((e=>({...e,specifications:e.specifications||[],status:"正常"===e.status?"active":"inactive"}))),l.page_info?je.total=l.page_info.total_items||0:je.total=G.value.length):(console.log("没有产品数据可以展示"),G.value=[],je.total=0)}catch(e){console.error("获取产品列表失败:",e);const a=e instanceof Error?e.message:"获取产品列表失败";b.error(a),G.value=[],je.total=0}finally{B.value=!1,ee.value+=1}},We=({row:e,column:a,rowIndex:l,columnIndex:t})=>{if(a.property,[0,1,2,3,4,5,10].includes(t))return e.isFirstSpec?{rowspan:e.numSpecs,colspan:1}:{rowspan:0,colspan:0}},Me=()=>{je.currentPage=1,Je()},Re=()=>{Be.name="",Be.model="",Be.category_id=void 0,Be.status=void 0,Me()},He=e=>{je.pageSize=e,je.currentPage=1,Je()},Ke=e=>{je.currentPage=e,Je()},qe=()=>{i.push({name:"ProductNew"})},Xe=e=>{let a;if(e.originalProduct&&e.originalProduct.id)a=e.originalProduct.id;else{if(!e.id)return console.error("无法获取产品ID进行编辑",e),void b.error("无法获取产品信息，编辑失败");a=e.id}i.push({name:"ProductEdit",params:{id:a}})},Ye=e=>{let a;if(e.originalProduct&&e.originalProduct.id)a=e.originalProduct.id;else{if(!e.id)return console.error("无法获取产品ID进行查看",e),void b.error("无法获取产品信息，查看失败");a=e.id}i.push({name:"ProductDetail",params:{id:a}})},Ge=e=>{let a,l;if(e.originalProduct&&e.originalProduct.id)a=e.originalProduct.id,l=e.name;else{if(!e.id)return console.error("无法获取产品ID进行删除",e),void b.error("无法获取产品信息，删除失败");a=e.id,l=e.name}C.confirm(`确定要删除产品 "${l}" (ID: ${a}) 吗？这将删除该产品及其所有规格。`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await F(a),b.success("产品删除成功"),Je()}catch(e){console.error("删除产品失败:",e),console.log("错误响应完整信息:",JSON.stringify(e));let a="删除产品失败",t="",i="";if(e.response&&e.response.data){const a=e.response.data;i="string"==typeof a?a:a.message?a.message:JSON.stringify(a)}else i=e.message?e.message:String(e);if(console.log("提取的错误消息:",i),i.includes("order_products.product_specification_id")){a="产品规格已被订单引用";let e=[];try{e=(i.match(/\(None,[^)]+, (\d+)\)/g)||[]).map((e=>{const a=e.match(/\d+/);return a?a[0]:null})).filter(Boolean)}catch(l){console.error("提取订单ID失败:",l)}let s="";if(s=e.length>0?`该产品规格被以下订单项引用:<br>订单项ID: ${e.join(", ")}<br><br>正在查询订单编号...`:"该产品规格已被订单引用，请先解除订单项的引用后再尝试删除产品。",C.alert(s,a,{confirmButtonText:"知道了",dangerouslyUseHTMLString:!0,type:"error",closeOnClickModal:!1,closeOnPressEscape:!1,showClose:!1}),e.length>0){try{const a=e.map((e=>parseInt(e))),l=await H(a);console.log("获取订单信息结果:",l),C.close();let i=[];if(l&&200===l.code&&l.data){i=(Array.isArray(l.data)?l.data:[l.data]).map((e=>e.order_number||"未知订单")).filter(((e,a,l)=>l.indexOf(e)===a))}t=i.length>0?`该产品规格被以下订单引用:<br>订单编号: ${i.join(", ")}<br><br>请先删除这些订单项后再尝试删除产品。`:`该产品规格被订单引用，但无法获取具体订单编号。<br>请检查订单项ID: ${e.join(", ")}`}catch(l){console.error("获取订单编号失败:",l),C.close(),t=`该产品规格被订单引用，但无法获取订单编号。<br>订单项ID: ${e.join(", ")}`}C.alert(t,a,{confirmButtonText:"知道了",dangerouslyUseHTMLString:!0,type:"error"})}}else if(i.includes("quotation_items.product_id")){a="产品已被报价单引用";let e=[];try{e=(i.match(/\(None,[^)]+, (\d+)\)/g)||[]).map((e=>{const a=e.match(/\d+/);return a?a[0]:null})).filter(Boolean)}catch(l){console.error("提取报价单ID失败:",l)}t=e.length>0?`该产品被以下报价单项目引用:<br>报价单项目ID: ${e.join(", ")}<br><br>请先删除这些报价单项目后再尝试删除产品。`:"该产品已被报价单引用，请先解除报价单项目的引用后再尝试删除产品。",C.alert(t,a,{confirmButtonText:"知道了",dangerouslyUseHTMLString:!0,type:"error",callback:()=>{}})}else i.includes("FOREIGN KEY constraint failed")?(a="产品已被其他数据引用",t="该产品被系统中的其他数据引用，无法直接删除。请先解除相关引用关系后再尝试删除产品。",C.alert(t,a,{confirmButtonText:"知道了",dangerouslyUseHTMLString:!0,type:"error",callback:()=>{}})):i.includes("constraint")?(a="产品删除受到约束限制",t="由于数据完整性约束，该产品无法被删除。请确保没有其他数据引用此产品。",C.alert(t,a,{confirmButtonText:"知道了",dangerouslyUseHTMLString:!0,type:"error",callback:()=>{}})):(a="删除产品失败",t="无法删除产品，请稍后重试。如果问题持续存在，请联系系统管理员。",C.alert(t,a,{confirmButtonText:"知道了",dangerouslyUseHTMLString:!0,type:"error",callback:()=>{}}))}})).catch((()=>{b.info("已取消删除")}))},Ze=e=>{if(!e||!e.image)return"https://placeholder.pics/svg/200x150/DEDEDE/555555/无图片";let a=e.image;console.log("原始图片URL信息:",{url:a,productId:e.id,productName:e.name});try{if(a&&a.includes("/api/v1/product_images/")){if(!a.startsWith("http")){const e={}.VITE_API_URL||window.location.origin;a=e.endsWith("/")&&a.startsWith("/")?e+a.substring(1):e.endsWith("/")||a.startsWith("/")?e+a:e+"/"+a}}else if(a&&!a.startsWith("http")&&!a.startsWith("data:")){a.startsWith("/")||(a="/"+a);const e={}.VITE_API_URL||window.location.origin;a=e.endsWith("/")&&a.startsWith("/")?e+a.substring(1):e+a}if(a&&a.includes("uploads/products/")&&!a.startsWith("http")&&!a.includes("/api/")){a=`${{}.VITE_API_URL||window.location.origin}/api/v1/static/${a.replace(/^\/+/,"")}`}return console.log("处理后的图片URL:",a),a}catch(l){return console.error("处理图片URL时出错:",l),e.image||"https://placeholder.pics/svg/200x150/DEDEDE/555555/加载失败"}},Qe=e=>{Q.value=e.map((e=>e.originalProduct.id))},ea=()=>{Q.value.length?C.confirm(`确定要删除选中的 ${Q.value.length} 个产品吗？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{for(const e of Q.value)await F(e);b.success("批量删除成功"),Je(),Q.value=[]}catch(e){console.error("Failed to batch delete products:",e),b.error("批量删除失败")}})).catch((()=>{b.info("已取消批量删除")})):b.warning("请先选择要删除的产品")},aa=()=>{le.value=!1,setTimeout((()=>{oe.value=0,ie.value=null,se.value=null,re.value=[],Le.value={},te.value=!1,ee.value+=1,setTimeout((()=>{console.log("准备打开导入对话框，状态已重置:",{importStep:oe.value,previewProductsLength:re.value.length,tableKey:ee.value}),le.value=!0,ae.value&&V((()=>{try{ae.value.clearFiles()}catch(e){console.warn("重置上传组件失败",e)}}))}),100)}),300)},la=e=>{if(!e||!e.raw)return void(ie.value=null);if(!("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===e.raw.type||"application/vnd.ms-excel"===e.raw.type||e.name.endsWith(".xlsx")||e.name.endsWith(".xls")))return b.error("请上传Excel文件(.xlsx或.xls)！"),ie.value=null,void(ae.value&&ae.value.clearFiles());const a=52428800;if(e.raw.size>a)return b.error(`文件大小超出限制(${Math.floor(50)}MB)！`),ie.value=null,void(ae.value&&ae.value.clearFiles());ie.value=e.raw},ta=async()=>{if(ie.value){te.value=!0;try{b.info("正在解析Excel文件，请稍候...");const e=await J(ie.value);if(console.log("预览响应:",e),e&&0===e.code)console.log("服务器成功返回数据，准备处理"),e.data?(Array.isArray(e.data.data)&&(re.value=e.data.data.map((e=>(e.attributes||(e.attributes=[]),e.specifications&&Array.isArray(e.specifications)&&e.specifications.forEach((a=>{a.specification||(a.specification="默认规格"),void 0===a.tax_rate&&(a.tax_rate=13);const l=e.attributes.find((e=>["市场价","建议售价","单价","售价","价格"].includes(e.attribute_name)));l&&!isNaN(Number(l.attribute_value))&&(a.suggested_price=Number(l.attribute_value),a.min_price||(a.min_price=a.suggested_price),a.max_price||(a.max_price=a.suggested_price))})),e))),console.log("从响应中提取产品数据:",re.value)),e.data.mappings&&(Le.value=e.data.mappings),re.value.length>0?(re.value=re.value.map(((e,a)=>({preview_id:a+1,name:e.name||"",model:e.model||"",unit:e.unit||"个",category_id:e.category_id||null,category_name:e.category_name||"",specifications:Array.isArray(e.specifications)?e.specifications:[{specification:"默认规格",cost_price:0,suggested_price:0,tax_rate:13}],attributes:Array.isArray(e.attributes)?e.attributes:[]}))),console.log("处理后的产品数据:",re.value),ee.value+=1,V((()=>{console.log("即将切换到步骤1，当前数据:",{productsCount:re.value.length,mappingsKeys:Object.keys(Le.value)}),oe.value=1,console.log("已切换到预览编辑步骤，importStep =",oe.value),V((()=>{ee.value+=1,b.success("Excel文件解析成功，请确认导入数据")}))}))):b.warning("未能从文件中识别出有效的产品数据，请检查文件格式")):b.warning("返回的数据格式不正确，请联系系统管理员");else{const a=(null==e?void 0:e.message)||(null==e?void 0:e.msg)||"文件解析失败，请检查文件格式";b.error(a)}}catch(e){console.error("预览失败:",e),b.error("预览失败: "+(e.message||"未知错误"))}finally{te.value=!1}}else b.warning("请先选择要导入的Excel文件")},ia=e=>{se.value=e,oe.value=2,Je()},sa=()=>{te.value?C.confirm("导入操作正在进行，确定要取消吗？","提示",{confirmButtonText:"确定",cancelButtonText:"继续等待",type:"warning"}).then((()=>{oa()})).catch((()=>{})):oa()},oa=()=>{le.value=!1,oe.value=0,ie.value=null,se.value=null,re.value=[],Le.value={},ae.value&&ae.value.clearFiles(),setTimeout((()=>{te.value=!1}),300)},ra=async()=>{try{const e=await W(),a=window.URL.createObjectURL(new Blob([e])),l=document.createElement("a");l.href=a,l.download="产品导入模板.xlsx",document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(a),b.success("模板下载成功")}catch(e){console.error("下载模板失败:",e),b.error("下载模板失败")}};let na=null;const ca=async()=>{try{na=S.service({text:"正在准备导出文件...",background:"rgba(255, 255, 255, 0.7)",lock:!0,customClass:"exportable-loading"});const e=document.querySelector(".exportable-loading .el-loading-spinner");if(e){const a=document.createElement("button");a.innerText="取消导出",a.className="el-button el-button--danger el-button--small",a.style.marginTop="10px",a.onclick=()=>{na&&(na.close(),na=null,b.info("已取消导出操作"))},e.appendChild(a)}const a={};Be.name&&(a.name=Be.name),Be.model&&(a.model=Be.model),Be.category_id&&(a.category_id=Be.category_id),Be.status&&(a.status="active"===Be.status?"正常":"禁用"),console.log("发送产品导出请求，过滤条件:",a);const l=await M({filter:a});if(!na)return void console.log("导出已被用户取消");if(na&&(na.close(),na=null),l instanceof Blob){const e=(new Date).toISOString().replace(/[:.]/g,"-").substring(0,19);Y(l,`产品列表_${e}.xlsx`),b.success("产品导出成功")}else console.error("导出产品失败: 返回数据类型错误",l),b.error("导出失败：返回数据类型错误")}catch(e){console.error("导出产品失败:",e);let l="导出产品失败，请稍后重试";if(e.response){if(e.response.data&&"object"==typeof e.response.data)l=e.response.data.message||l;else if("string"==typeof e.response.data)try{l=JSON.parse(e.response.data).message||l}catch(a){l=e.response.data||l}}else e.message&&(l=e.message);b.error(l)}finally{na&&(na.close(),na=null)}},ua=async()=>{if(Q.value.length)try{na=S.service({text:"正在准备批量导出文件...",background:"rgba(255, 255, 255, 0.7)",lock:!0,customClass:"exportable-loading"});const e=document.querySelector(".exportable-loading .el-loading-spinner");if(e){const a=document.createElement("button");a.innerText="取消导出",a.className="el-button el-button--danger el-button--small",a.style.marginTop="10px",a.onclick=()=>{na&&(na.close(),na=null,b.info("已取消批量导出操作"))},e.appendChild(a)}console.log("发送批量导出请求，产品IDs:",Q.value);const a=await M({ids:Q.value});if(!na)return void console.log("批量导出已被用户取消");if(na&&(na.close(),na=null),a instanceof Blob){const e=(new Date).toISOString().replace(/[:.]/g,"-").substring(0,19);Y(a,`批量导出产品_${e}.xlsx`),b.success("批量导出成功")}else console.error("批量导出产品失败: 返回数据类型错误",a),b.error("批量导出失败：返回数据类型错误")}catch(e){console.error("批量导出产品失败:",e);let l="批量导出产品失败，请稍后重试";if(e.response){if(e.response.data&&"object"==typeof e.response.data)l=e.response.data.message||l;else if("string"==typeof e.response.data)try{l=JSON.parse(e.response.data).message||l}catch(a){l=e.response.data||l}}else e.message&&(l=e.message);b.error(l)}finally{na&&(na.close(),na=null)}else b.warning("请先选择要导出的产品")};t((()=>{Je(),(async()=>{try{const e=await R({per_page:999}),a=X(e);console.log("获取到的分类数据:",a),Z.value=a.map((e=>({value:e.id,label:e.name,level:e.level||1,parent_id:e.parent_id})))}catch(e){console.error("获取产品类别失败:",e);const a=e instanceof Error?e.message:"获取产品类别失败";b.error(a)}})()})),a([]),a(!1),a("");const da=e=>{if(!e)return null;if(!e.specifications||!Array.isArray(e.specifications)||0===e.specifications.length)return null;const a=e.specifications.find((e=>e.is_default));return a||e.specifications[0]},pa=l((()=>JSON.parse(JSON.stringify(Z.value)).sort(((e,a)=>{if((e.level||0)!==(a.level||0))return(e.level||0)-(a.level||0);const l=(e.label||"").toLowerCase(),t=(a.label||"").toLowerCase();return l.localeCompare(t)})))),ma=e=>{const a=e.label||"未命名类别",l=e.level||0;return`${l>1?"　".repeat(l-1):""}${a}`},ga=()=>{console.log("导入对话框打开，当前步骤:",oe.value),console.log("预览数据:",re.value)};return(e,a)=>{const l=s("el-input"),t=s("el-form-item"),i=s("el-option"),b=s("el-select"),k=s("el-icon"),x=s("el-button"),C=s("el-form"),V=s("el-card"),S=s("el-button-group"),j=s("el-table-column"),F=s("el-tag"),J=s("el-table"),W=s("el-pagination"),M=s("el-image"),R=s("el-col"),H=s("el-row"),q=s("el-step"),X=s("el-steps"),Y=s("el-alert"),Je=s("el-upload"),oa=s("el-empty"),na=s("el-result"),va=s("el-divider"),fa=s("el-collapse-item"),_a=s("el-collapse"),ya=s("el-dialog"),ba=o("loading");return r(),n("div",ce,[p(V,{class:"search-card mb-20"},{default:d((()=>[p(C,{inline:!0,model:Be,class:"search-form"},{default:d((()=>[p(t,{label:"产品名称"},{default:d((()=>[p(l,{modelValue:Be.name,"onUpdate:modelValue":a[0]||(a[0]=e=>Be.name=e),placeholder:"请输入产品名称",clearable:""},null,8,["modelValue"])])),_:1}),p(t,{label:"产品型号"},{default:d((()=>[p(l,{modelValue:Be.model,"onUpdate:modelValue":a[1]||(a[1]=e=>Be.model=e),placeholder:"请输入产品型号",clearable:""},null,8,["modelValue"])])),_:1}),p(t,{label:"产品类别"},{default:d((()=>[p(b,{modelValue:Be.category_id,"onUpdate:modelValue":a[2]||(a[2]=e=>Be.category_id=e),placeholder:"请选择产品类别",clearable:""},{default:d((()=>[(r(!0),n(g,null,v(pa.value,(e=>(r(),c(i,{key:e.value,label:ma(e),value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(t,{label:"产品状态"},{default:d((()=>[p(b,{modelValue:Be.status,"onUpdate:modelValue":a[3]||(a[3]=e=>Be.status=e),placeholder:"请选择产品状态",clearable:""},{default:d((()=>[p(i,{label:"正常",value:"active"}),p(i,{label:"停用",value:"inactive"})])),_:1},8,["modelValue"])])),_:1}),p(t,null,{default:d((()=>[p(x,{type:"primary",onClick:Me},{default:d((()=>[p(k,null,{default:d((()=>[p(m(U))])),_:1}),a[13]||(a[13]=_(" 搜索 "))])),_:1}),p(x,{onClick:Re},{default:d((()=>[p(k,null,{default:d((()=>[p(m(P))])),_:1}),a[14]||(a[14]=_(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),p(V,{class:"mb-20"},{default:d((()=>[y("div",ue,[y("div",null,[p(x,{type:"primary",onClick:qe},{default:d((()=>[p(k,null,{default:d((()=>[p(m(z))])),_:1}),a[15]||(a[15]=_(" 新增产品 "))])),_:1}),p(x,{type:"success",onClick:aa},{default:d((()=>[p(k,null,{default:d((()=>[p(m($))])),_:1}),a[16]||(a[16]=_(" 导入产品 "))])),_:1}),p(x,{type:"warning",onClick:ca},{default:d((()=>[p(k,null,{default:d((()=>[p(m(I))])),_:1}),a[17]||(a[17]=_(" 导出产品 "))])),_:1})]),y("div",null,[p(S,null,{default:d((()=>[p(x,{type:"table"===K.value?"primary":"",onClick:a[4]||(a[4]=e=>K.value="table")},{default:d((()=>[p(k,null,{default:d((()=>[p(m(N))])),_:1})])),_:1},8,["type"]),p(x,{type:"card"===K.value?"primary":"",onClick:a[5]||(a[5]=e=>K.value="card")},{default:d((()=>[p(k,null,{default:d((()=>[p(m(D))])),_:1})])),_:1},8,["type"])])),_:1})])])])),_:1}),"table"===K.value?(r(),c(V,{key:0},{default:d((()=>[u((r(),c(J,{data:Fe.value,"span-method":We,onSelectionChange:Qe,border:"",stripe:"",key:ee.value},{default:d((()=>[p(j,{type:"selection",width:"55",selectable:e=>e.isFirstSpec},null,8,["selectable"]),p(j,{prop:"productId",label:"ID",width:"50"}),p(j,{prop:"name",label:"产品名称/操作","min-width":"140","show-overflow-tooltip":""},{default:d((({row:e})=>[y("span",null,w(e.name),1),e.isFirstSpec?(r(),n("div",de,[p(S,{size:"small"},{default:d((()=>[p(x,{type:"primary",link:"",onClick:a=>Xe(e)},{default:d((()=>[p(k,null,{default:d((()=>[p(m(A))])),_:1}),a[18]||(a[18]=_(" 编辑 "))])),_:2},1032,["onClick"]),p(x,{type:"primary",link:"",onClick:a=>Ye(e)},{default:d((()=>[p(k,null,{default:d((()=>[p(m(E))])),_:1}),a[19]||(a[19]=_(" 查看 "))])),_:2},1032,["onClick"]),p(x,{type:"danger",link:"",onClick:a=>Ge(e)},{default:d((()=>[p(k,null,{default:d((()=>[p(m(h))])),_:1}),a[20]||(a[20]=_(" 删除 "))])),_:2},1032,["onClick"])])),_:2},1024)])):f("",!0)])),_:1}),p(j,{prop:"model",label:"产品型号",width:"90"}),p(j,{prop:"unit",label:"单位",width:"60"}),p(j,{prop:"category_name",label:"产品类别",width:"100"}),p(j,{prop:"specification",label:"产品规格",width:"90"}),p(j,{prop:"cost_price",label:"成本价",width:"100"},{default:d((({row:e})=>[null!==e.cost_price?(r(),n("span",pe,"¥"+w(e.cost_price),1)):(r(),n("span",me,"-"))])),_:1}),p(j,{prop:"suggested_price",label:"建议售价",width:"100"},{default:d((({row:e})=>[null!==e.suggested_price?(r(),n("span",ge,"¥"+w(e.suggested_price),1)):(r(),n("span",ve,"-"))])),_:1}),p(j,{prop:"tax_rate",label:"税率(%)",width:"80"},{default:d((({row:e})=>[null!==e.tax_rate?(r(),n("span",fe,w(e.tax_rate)+"%",1)):(r(),n("span",_e,"-"))])),_:1}),p(j,{prop:"status",label:"状态",width:"100"},{default:d((({row:e})=>[e.isFirstSpec?(r(),c(F,{key:0,type:"active"===e.status?"success":"danger"},{default:d((()=>[_(w("active"===e.status?"正常":"停用"),1)])),_:2},1032,["type"])):f("",!0)])),_:1})])),_:1},8,["data"])),[[ba,B.value]]),y("div",ye,[p(S,null,{default:d((()=>[p(x,{disabled:!Q.value.length,onClick:ea},{default:d((()=>a[21]||(a[21]=[_(" 批量删除 ")]))),_:1},8,["disabled"]),p(x,{disabled:!Q.value.length,onClick:ua},{default:d((()=>a[22]||(a[22]=[_(" 批量导出 ")]))),_:1},8,["disabled"])])),_:1}),p(W,{"current-page":je.currentPage,"onUpdate:currentPage":a[6]||(a[6]=e=>je.currentPage=e),"page-size":je.pageSize,"onUpdate:pageSize":a[7]||(a[7]=e=>je.pageSize=e),total:je.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:He,onCurrentChange:Ke},null,8,["current-page","page-size","total"])])])),_:1})):(r(),n(g,{key:1},[p(H,{gutter:20},{default:d((()=>[(r(!0),n(g,null,v(G.value,(e=>(r(),c(R,{key:e.id,xs:24,sm:12,md:8,lg:6},{default:d((()=>[p(V,{class:"product-card mb-20"},{header:d((()=>[y("div",be,[y("span",he,w(e.name),1),p(F,{type:"active"===e.status?"success":"danger"},{default:d((()=>[_(w("active"===e.status?"正常":"停用"),1)])),_:2},1032,["type"])])])),default:d((()=>{var l,t;return[y("div",we,[p(M,{src:Ze(e),fit:"contain",style:{width:"100%",height:"150px"}},{error:d((()=>[y("div",ke,[p(k,null,{default:d((()=>[p(m(T))])),_:1})])])),_:2},1032,["src"])]),y("div",xe,[y("p",null,[a[23]||(a[23]=y("span",{class:"info-label"},"型号：",-1)),_(w(e.model),1)]),y("p",null,[a[24]||(a[24]=y("span",{class:"info-label"},"规格：",-1)),_(w((null==(l=da(e))?void 0:l.specification)||"默认规格"),1)]),y("p",null,[a[25]||(a[25]=y("span",{class:"info-label"},"单位：",-1)),_(w(e.unit),1)]),y("p",null,[a[26]||(a[26]=y("span",{class:"info-label"},"类别：",-1)),_(w(e.category_name),1)]),y("p",null,[a[27]||(a[27]=y("span",{class:"info-label"},"建议售价：",-1)),_("¥"+w((null==(t=da(e))?void 0:t.suggested_price)||"0.00"),1)])]),y("div",Ce,[p(S,null,{default:d((()=>[p(x,{type:"primary",link:"",onClick:a=>Xe(e)},{default:d((()=>[p(k,null,{default:d((()=>[p(m(A))])),_:1}),a[28]||(a[28]=_(" 编辑 "))])),_:2},1032,["onClick"]),p(x,{type:"primary",link:"",onClick:a=>Ye(e)},{default:d((()=>[p(k,null,{default:d((()=>[p(m(E))])),_:1}),a[29]||(a[29]=_(" 查看 "))])),_:2},1032,["onClick"]),p(x,{type:"danger",link:"",onClick:a=>Ge(e)},{default:d((()=>[p(k,null,{default:d((()=>[p(m(h))])),_:1}),a[30]||(a[30]=_(" 删除 "))])),_:2},1032,["onClick"])])),_:2},1024)])]})),_:2},1024)])),_:2},1024)))),128))])),_:1}),y("div",Ve,[p(W,{"current-page":je.currentPage,"onUpdate:currentPage":a[8]||(a[8]=e=>je.currentPage=e),"page-size":je.pageSize,"onUpdate:pageSize":a[9]||(a[9]=e=>je.pageSize=e),total:je.total,"page-sizes":[12,24,48,96],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:He,onCurrentChange:Ke},null,8,["current-page","page-size","total"])])],64)),p(ya,{modelValue:le.value,"onUpdate:modelValue":a[12]||(a[12]=e=>le.value=e),title:"导入产品",width:"90%",top:"5vh","before-close":sa,onOpen:ga},{footer:d((()=>[y("span",Oe,[p(x,{onClick:sa},{default:d((()=>[_(w(2===oe.value?"完成":"取消"),1)])),_:1}),0===oe.value?(r(),c(x,{key:0,type:"primary",onClick:ta,loading:te.value},{default:d((()=>a[44]||(a[44]=[_(" 下一步：预览 ")]))),_:1},8,["loading"])):f("",!0),1===oe.value?(r(),c(x,{key:1,onClick:a[11]||(a[11]=e=>oe.value=0)},{default:d((()=>a[45]||(a[45]=[_(" 返回上一步 ")]))),_:1})):f("",!0)]),f("",!0)])),default:d((()=>{var e,l,t;return[y("div",Se,[f("",!0),p(X,{active:oe.value,"finish-status":"success",simple:"",style:{"margin-bottom":"20px"}},{default:d((()=>[p(q,{title:"上传文件"}),p(q,{title:"预览编辑"}),p(q,{title:"导入结果"})])),_:1},8,["active"]),0===oe.value?(r(),n("div",Ue,[y("div",Pe,[p(Y,{type:"info","show-icon":"",title:"导入说明"},{default:d((()=>[y("div",null,[y("p",null,[a[32]||(a[32]=_("1. 请先下载")),p(x,{type:"text",onClick:ra},{default:d((()=>a[31]||(a[31]=[_("导入模板")]))),_:1}),a[33]||(a[33]=_("，并根据模板格式填写产品信息。"))]),a[34]||(a[34]=y("p",null,"2. 导入过程中会自动识别表头，支持产品名称、型号、规格、价格等常见字段。",-1)),a[35]||(a[35]=y("p",null,"3. 未匹配的表头将作为产品自定义属性。",-1)),a[36]||(a[36]=y("p",null,"4. 文件大小不能超过50MB，仅支持Excel格式(.xlsx, .xls)。",-1))])])),_:1})]),p(Je,{ref_key:"uploadRef",ref:ae,class:"upload-demo",drag:"",action:"","auto-upload":!1,"on-change":la,limit:1,accept:".xlsx,.xls"},{tip:d((()=>a[37]||(a[37]=[y("div",{class:"el-upload__tip"}," 请上传Excel文件(.xlsx或.xls)，表头会自动模糊匹配。 ",-1)]))),default:d((()=>[p(k,{class:"el-icon--upload"},{default:d((()=>[p(m(O))])),_:1}),a[38]||(a[38]=y("div",{class:"el-upload__text"},[_("拖拽文件到此处或 "),y("em",null,"点击上传")],-1))])),_:1},512),ie.value?(r(),n("div",ze,[y("div",$e,[p(k,null,{default:d((()=>[p(m(L))])),_:1}),y("span",null,w(ie.value.name),1),y("span",Ie,w((t=ie.value.size,t<1024?t+" B":t<1048576?(t/1024).toFixed(2)+" KB":(t/1048576).toFixed(2)+" MB")),1)])])):f("",!0)])):1===oe.value?(r(),n("div",Ne,[f("",!0),re.value.length>0?(r(),c(ne,{"preview-data":re.value,categories:Z.value,mappings:Le.value,key:`preview-${ee.value}`,onSuccess:ia},null,8,["preview-data","categories","mappings"])):(r(),c(oa,{key:2,description:"未检测到有效的产品数据"}))])):2===oe.value?(r(),n("div",De,[se.value&&se.value.imported_count>0?(r(),c(na,{key:0,icon:"success",title:`成功导入 ${se.value.imported_count} 个产品`,"sub-title":se.value.failed_count>0?`${se.value.failed_count} 个产品导入失败`:"所有产品导入成功"},{extra:d((()=>[p(x,{type:"primary",onClick:sa},{default:d((()=>a[39]||(a[39]=[_("关闭")]))),_:1})])),_:1},8,["title","sub-title"])):(r(),c(na,{key:1,icon:"error",title:"导入失败","sub-title":se.value?`无法导入产品，${se.value.errors&&se.value.errors.length>0?se.value.errors[0]:"请检查数据格式"}`:"导入过程出现错误"},{extra:d((()=>[p(x,{onClick:a[10]||(a[10]=e=>oe.value=0)},{default:d((()=>a[40]||(a[40]=[_("返回重试")]))),_:1}),p(x,{type:"primary",onClick:sa},{default:d((()=>a[41]||(a[41]=[_("关闭")]))),_:1})])),_:1},8,["sub-title"])),se.value&&((null==(e=se.value.warnings)?void 0:e.length)>0||(null==(l=se.value.errors)?void 0:l.length)>0)?(r(),n("div",Ae,[se.value.warnings&&se.value.warnings.length>0?(r(),n(g,{key:0},[p(va,{"content-position":"left"},{default:d((()=>a[42]||(a[42]=[_("警告信息")]))),_:1}),p(_a,null,{default:d((()=>[p(fa,{title:"查看警告",name:"warnings"},{default:d((()=>[y("div",Ee,[(r(!0),n(g,null,v(se.value.warnings,((e,a)=>(r(),n("p",{key:`warning-${a}`},w(e),1)))),128))])])),_:1})])),_:1})],64)):f("",!0),se.value.errors&&se.value.errors.length>0?(r(),n(g,{key:1},[p(va,{"content-position":"left"},{default:d((()=>a[43]||(a[43]=[_("错误信息")]))),_:1}),p(_a,null,{default:d((()=>[p(fa,{title:"查看错误",name:"errors"},{default:d((()=>[y("div",Te,[(r(!0),n(g,null,v(se.value.errors,((e,a)=>(r(),n("p",{key:`error-${a}`},w(e),1)))),128))])])),_:1})])),_:1})],64)):f("",!0)])):f("",!0)])):f("",!0)])]})),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-d701f97f"]]);export{Le as default};
