from collections.abc import Collection
from collections.abc import Iterable
from collections.abc import Iterator

class ETags(Collection[str]):
    _strong: frozenset[str]
    _weak: frozenset[str]
    star_tag: bool
    def __init__(
        self,
        strong_etags: Iterable[str] | None = None,
        weak_etags: Iterable[str] | None = None,
        star_tag: bool = False,
    ) -> None: ...
    def as_set(self, include_weak: bool = False) -> set[str]: ...
    def is_weak(self, etag: str) -> bool: ...
    def is_strong(self, etag: str) -> bool: ...
    def contains_weak(self, etag: str) -> bool: ...
    def contains(self, etag: str) -> bool: ...
    def contains_raw(self, etag: str) -> bool: ...
    def to_header(self) -> str: ...
    def __call__(
        self,
        etag: str | None = None,
        data: bytes | None = None,
        include_weak: bool = False,
    ) -> bool: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[str]: ...
    def __contains__(self, item: str) -> bool: ...  # type: ignore
