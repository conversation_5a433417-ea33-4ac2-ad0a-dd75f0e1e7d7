<template>
  <el-dialog
    v-model="visible"
    title="导出报价单配置"
    width="500px"
    @close="handleClose"
  >
    <el-form :model="exportConfig" label-width="120px">
      <el-form-item label="导出格式">
        <el-radio-group v-model="exportConfig.format">
          <el-radio label="xlsx">Excel (XLSX)</el-radio>
          <el-radio label="pdf">PDF</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="包含表头">
        <el-switch v-model="exportConfig.includeHeader" />
      </el-form-item>
      
      <el-form-item label="导出列">
        <div class="export-columns-container">
          <el-checkbox v-model="exportConfig.selectAll" @change="handleSelectAllChange" class="select-all-checkbox">全选</el-checkbox>
          <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes">
            <el-checkbox v-for="column in availableColumns" :key="column.prop" :label="column.prop" @change="handleColumnChange">
              {{ column.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exporting">
          确认导出
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { quotationApi } from '@/api/quotation'

interface Props {
  modelValue: boolean
  quotationId?: number
  quotationNumber?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'export-success'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  quotationId: undefined,
  quotationNumber: ''
})

const emit = defineEmits<Emits>()

const visible = ref(false)
const exporting = ref(false)

// 定义导出配置
const availableColumns = [
  { prop: 'product_name', label: '产品名称' },
  { prop: 'product_model', label: '型号' },
  { prop: 'product_specification', label: '规格' },
  { prop: 'product_unit', label: '单位' },
  { prop: 'quantity', label: '数量' },
  { prop: 'unit_price', label: '单价' },
  { prop: 'discount', label: '折扣(%)' },
  { prop: 'tax_rate', label: '税率(%)' },
  { prop: 'total_price', label: '金额' },
  { prop: 'notes', label: '备注' }
]

const exportConfig = reactive({
  format: 'xlsx' as 'xlsx' | 'pdf',
  includeHeader: true,
  selectedColumns: availableColumns.map(col => col.prop),
  selectAll: true
})

const handleSelectAllChange = (val: boolean) => {
  if (val) {
    exportConfig.selectedColumns = availableColumns.map(col => col.prop)
  } else {
    exportConfig.selectedColumns = []
  }
}

const handleColumnChange = () => {
  exportConfig.selectAll = exportConfig.selectedColumns.length === availableColumns.length
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleClose = () => {
  visible.value = false
}

// 导出报价单
const handleExport = async () => {
  if (!props.quotationId) {
    ElMessage.error('缺少报价单ID')
    return
  }

  if (exportConfig.selectedColumns.length === 0) {
    ElMessage.warning('请至少选择一列进行导出')
    return
  }

  exporting.value = true
  try {
    const response = await quotationApi.export(props.quotationId, {
      format: exportConfig.format,
      columns: exportConfig.selectedColumns,
      include_header: exportConfig.includeHeader
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(response.data)
    const link = document.createElement('a')
    link.href = url
    link.download = `报价单_${props.quotationNumber || props.quotationId}.${exportConfig.format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
    emit('export-success')
    handleClose()
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped>
.export-columns-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.select-all-checkbox {
  margin-bottom: 8px;
}

.column-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 20px;
}

.column-checkboxes .el-checkbox {
  margin-right: 0;
}
</style>
