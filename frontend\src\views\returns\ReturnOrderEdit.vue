<template>
  <div class="return-order-edit">
    <div class="page-header">
      <h2>编辑退货单</h2>
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </div>
    </div>

    <div v-loading="loading" class="form-container">
      <el-card v-if="returnOrder">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="退货单号">
                <el-input v-model="returnOrder.return_number" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联订单">
                <el-input :value="returnOrder.order?.order_number" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="退货日期" prop="return_date">
                <el-date-picker
                  v-model="form.return_date"
                  type="date"
                  placeholder="选择退货日期"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态">
                <el-tag :type="getStatusType(returnOrder.status)">{{ returnOrder.status }}</el-tag>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="退货原因" prop="reason">
                <el-input
                  v-model="form.reason"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入退货原因"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input
                  v-model="form.notes"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <el-card style="margin-top: 20px;" v-if="returnOrder">
        <template #header>
          <span>退货产品明细</span>
        </template>
        
        <el-table :data="form.items" border stripe>
          <el-table-column prop="product_name" label="产品名称" min-width="150" />
          <el-table-column prop="product_model" label="产品型号" min-width="120" />
          <el-table-column prop="specification_description" label="规格描述" min-width="150" />
          <el-table-column label="退货数量" width="120">
            <template #default="{ row, $index }">
              <el-input-number
                v-model="row.quantity"
                :min="0.01"
                :precision="2"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column prop="product_unit" label="单位" width="80" />
          <el-table-column label="备注" min-width="200">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.notes"
                placeholder="请输入备注"
                size="small"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { ReturnOrder, ReturnOrderStatus, UpdateReturnOrderData } from '@/types/return'
import { returnApi } from '@/api/return'

const router = useRouter()
const route = useRoute()

// 状态
const loading = ref(false)
const saving = ref(false)
const formRef = ref()
const returnOrder = ref<ReturnOrder | null>(null)

// 表单数据
const form = reactive<UpdateReturnOrderData>({
  return_date: '',
  reason: '',
  notes: '',
  items: []
})

// 验证规则
const rules = {
  return_date: [
    { required: true, message: '请选择退货日期', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入退货原因', trigger: 'blur' }
  ]
}

// 获取退货单详情
const fetchReturnOrder = async () => {
  const id = Number(route.params.id)
  if (!id) {
    ElMessage.error('无效的退货单ID')
    router.back()
    return
  }

  loading.value = true
  try {
    const response = await returnApi.getById(id)
    returnOrder.value = response.data || response
    
    // 填充表单数据
    if (returnOrder.value) {
      form.return_date = returnOrder.value.return_date
      form.reason = returnOrder.value.reason
      form.notes = returnOrder.value.notes || ''
      form.items = returnOrder.value.items.map(item => ({
        order_product_id: item.order_product_id,
        product_specification_id: item.product_specification_id,
        quantity: item.quantity,
        notes: item.notes || '',
        // 保留显示字段
        product_name: item.product_name,
        product_model: item.product_model,
        product_unit: item.product_unit,
        specification_description: item.specification_description
      }))
    }
  } catch (error) {
    console.error('获取退货单详情失败:', error)
    ElMessage.error('获取退货单详情失败')
  } finally {
    loading.value = false
  }
}

// 状态样式
const getStatusType = (status: ReturnOrderStatus | undefined) => {
  const statusMap: Record<ReturnOrderStatus, string> = {
    '待确认': 'warning',
    '退货中': 'primary',
    '已签收': 'success',
    '已拒绝': 'danger',
    '已取消': 'info'
  }
  return statusMap[status || '待确认']
}

// 操作
const handleCancel = () => {
  router.back()
}

const handleSave = async () => {
  if (!returnOrder.value) return
  
  try {
    await formRef.value?.validate()
    
    if (form.items?.length === 0) {
      ElMessage.error('请至少保留一个退货产品')
      return
    }
    
    saving.value = true
    
    // 准备提交数据，只包含必要字段
    const submitData: UpdateReturnOrderData = {
      return_date: form.return_date,
      reason: form.reason,
      notes: form.notes,
      items: form.items?.map(item => ({
        order_product_id: item.order_product_id,
        product_specification_id: item.product_specification_id,
        quantity: item.quantity,
        notes: item.notes
      }))
    }
    
    await returnApi.update(returnOrder.value.id!, submitData)
    ElMessage.success('退货单更新成功')
    router.push(`/returns/${returnOrder.value.id}`)
  } catch (error) {
    console.error('更新退货单失败:', error)
    ElMessage.error('更新退货单失败')
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  fetchReturnOrder()
})
</script>

<style scoped>
.return-order-edit {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-container {
  max-width: 1200px;
}
</style>
