import{J as e,r as l,w as a,f as o,M as t,o as n,g as s,h as r,a as i,i as u,k as d,c,m,F as p,l as f,N as v,t as g,a8 as _,j as h,U as y,X as b,b as w,L as k,e as C,W as V}from"./index-3d4c440c.js";import{l as x,e as j,u as U,d as $,c as z}from"./quotation-219e97aa.js";import{a as L,k as A}from"./order-d6035a15.js";import{a as B}from"./apiAdapters-232bdd53.js";import{s as R}from"./customer-471ca075.js";import{f as q,a as E}from"./format-552375ee.js";import{_ as I}from"./_plugin-vue_export-helper-1b428a4d.js";const D={class:"search-container"},T={key:0,class:"loading-container"},H={key:2,class:"empty-container"},M={class:"dialog-footer"},O=I(e({__name:"SelectOrderDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","confirm"],setup(e,{emit:w}){const k=e,C=w,V=l(k.modelValue);a((()=>k.modelValue),(e=>V.value=e)),a(V,(e=>C("update:modelValue",e)));const x=l(""),j=l(null),U=l([]),$=l([]),z=l(!1),A=e=>{if(!e)return"info";return{"草稿":"info","待确认":"warning","已确认":"success","生产中":"primary","待发货":"success","发货中":"warning","部分发货":"warning","已发货":"success","待对账":"info","部分对账":"warning","已对账":"success","待付款":"info","部分付款":"warning","已完成":"success","已取消":"danger"}[e]||"info"},B=async()=>{z.value=!0;try{const e={page:1,per_page:50,status:["已确认","生产中","待发货","发货中","部分发货"]};j.value&&(e.customer_id=j.value),x.value&&(e.order_number=x.value);const l=await L(e);l&&200===l.code&&l.data?(U.value=l.data.list||[],0===U.value.length&&y.info("没有找到符合条件的订单")):(U.value=[],y.warning("获取订单列表失败: "+((null==l?void 0:l.message)||"未知错误")))}catch(e){console.error("加载订单失败:",e),y.error("加载订单失败: "+(e.message||"未知错误")),U.value=[]}finally{z.value=!1}},q=async e=>{if(e)try{const l=await R(e);$.value=l.list||[]}catch(l){console.error("搜索客户失败:",l),$.value=[]}},E=()=>{B()};a(j,(()=>{B()}));const I=e=>{e.id?(C("confirm",e.id),V.value=!1):y.warning("无效的订单，请选择其他订单")},O=()=>{x.value="",j.value=null,U.value=[],C("update:modelValue",!1)};return a(V,(e=>{e&&B()})),(e,l)=>{const a=o("el-option"),y=o("el-select"),w=o("el-icon"),k=o("el-button"),C=o("el-input"),L=o("el-skeleton"),B=o("el-table-column"),R=o("el-tag"),S=o("el-table"),X=o("el-empty"),F=o("el-dialog"),P=t("loading");return n(),s(F,{modelValue:V.value,"onUpdate:modelValue":l[3]||(l[3]=e=>V.value=e),title:"选择目标订单",width:"600px",onClose:O},{footer:r((()=>[i("span",M,[u(k,{onClick:l[2]||(l[2]=e=>V.value=!1)},{default:r((()=>l[5]||(l[5]=[d("取消")]))),_:1})])])),default:r((()=>[i("div",D,[u(y,{modelValue:j.value,"onUpdate:modelValue":l[0]||(l[0]=e=>j.value=e),filterable:"",remote:"",placeholder:"请输入客户名称搜索","remote-method":q,clearable:"",style:{width:"100%","margin-bottom":"15px"}},{default:r((()=>[(n(!0),c(p,null,m($.value,(e=>(n(),s(a,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),u(C,{modelValue:x.value,"onUpdate:modelValue":l[1]||(l[1]=e=>x.value=e),placeholder:"输入订单号搜索",clearable:"",style:{"margin-bottom":"15px"}},{append:r((()=>[u(k,{onClick:E},{default:r((()=>[u(w,null,{default:r((()=>[u(f(b))])),_:1})])),_:1})])),_:1},8,["modelValue"])]),z.value?(n(),c("div",T,[u(L,{rows:3,animated:""})])):v((n(),s(S,{key:1,data:U.value,style:{width:"100%"},"highlight-current-row":"",onRowClick:I},{default:r((()=>[u(B,{prop:"order_number",label:"订单号",width:"180"}),u(B,{prop:"customer_name",label:"客户"}),u(B,{prop:"status",label:"状态"},{default:r((({row:e})=>[u(R,{type:A(e.status)},{default:r((()=>[d(g(e.status),1)])),_:2},1032,["type"])])),_:1}),u(B,{label:"操作",width:"100"},{default:r((({row:e})=>[u(k,{type:"primary",size:"small",onClick:_((l=>I(e)),["stop"])},{default:r((()=>l[4]||(l[4]=[d(" 选择 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[P,z.value]]),z.value||0!==U.value.length?h("",!0):(n(),c("div",H,[u(X,{description:"暂无可用订单"})]))])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-4185abf3"]]),S={class:"quotation-list"},X={class:"card-header"},F={class:"pagination"},P={class:"dialog-footer"},W=I({__name:"QuotationList",setup(e){const a=w(),_=l(!1),b=l([]),L=l([]),I=l(1),D=l(10),T=l(0),H=l([]),M=l(!1),W=l(null),J=k({customer_id:void 0,project_name:"",status:""}),N=l(!1),Q=l(!1),G=[{prop:"quotation_number",label:"报价单号"},{prop:"customer_name",label:"客户名称"},{prop:"project_name",label:"项目名称"},{prop:"valid_until",label:"有效期至"},{prop:"status",label:"状态"},{prop:"created_at",label:"创建时间"}],K=k({format:"xlsx",includeHeader:!0,selectedColumns:G.map((e=>e.prop)),selectAll:!0}),Y=e=>{H.value=e},Z=()=>{0!==H.value.length?(K.format="xlsx",K.includeHeader=!0,K.selectedColumns=G.map((e=>e.prop)),K.selectAll=!0,N.value=!0):y.warning("请至少选择一个报价单进行导出。")},ee=e=>{K.selectedColumns=e?G.map((e=>e.prop)):[]},le=()=>{K.selectAll=K.selectedColumns.length===G.length},ae=async()=>{if(0===H.value.length)return void y.warning("请至少选择一个报价单进行导出。");if(0===K.selectedColumns.length)return void y.warning("请至少选择一列进行导出");Q.value=!0;y.info({message:"正在批量导出报价单，可能需要一些时间，请耐心等待...",duration:0,showClose:!0,key:"exportingListMsg"});try{const l=[],a=[];for(const o of H.value)if(o.id)try{console.log(`开始导出报价单 ${o.id}，配置:`,K);const e=await j(o.id,{columns:K.selectedColumns,format:K.format,include_header:K.includeHeader});if(!(e instanceof Blob))throw console.error(`导出报价单 ${o.id} 响应不是Blob类型:`,e),new Error("服务器返回的不是有效的文件数据");const a=e.type||("xlsx"===K.format?"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":"application/pdf");console.log(`导出报价单 ${o.id} 成功，MIME类型:`,a);const t=new Blob([e],{type:a}),n=window.URL.createObjectURL(t),s=document.createElement("a");s.href=n;const r=K.format;s.download=`报价单_${o.quotation_number||o.id}.${r}`,document.body.appendChild(s),s.click(),document.body.removeChild(s),setTimeout((()=>{window.URL.revokeObjectURL(n)}),100),l.push(o.id)}catch(e){console.error(`导出报价单 ${o.id} 失败:`,e),a.push(o.id)}else a.push(o.id||0);y.closeAll(),l.length>0&&0===a.length?y.success(`成功导出 ${l.length} 个报价单`):l.length>0&&a.length>0?y.warning(`成功导出 ${l.length} 个报价单，${a.length} 个导出失败`):y.error("所有报价单导出失败")}catch(l){y.closeAll(),console.error("批量导出报价单失败:",l),y.error(`批量导出失败: ${l instanceof Error?l.message:"未知错误"}`)}finally{Q.value=!1,N.value=!1}},oe=async()=>{_.value=!0;try{const e={page:I.value,per_page:D.value,customer_id:J.customer_id||void 0,project_name:J.project_name||void 0,status:J.status||void 0},l=await x(e),a=B(l);console.log("适配后的报价单数据:",a),a.items&&a.items.length>=0?(b.value=a.items.map((e=>({...e,exporting:!1}))),a.page_info?T.value=a.page_info.total_items||0:T.value=a.items.length):(console.warn("获取报价单列表响应格式不符合预期"),y.error("获取报价单列表数据格式错误"),b.value=[],T.value=0)}catch(e){console.error("加载报价单列表失败:",e),y.error("加载报价单列表失败")}finally{_.value=!1}},te=()=>{I.value=1,oe()},ne=()=>{J.customer_id=void 0,J.project_name="",J.status="",te()},se=()=>{a.push("/quotations/new")},re=async(e,l,a)=>{var o;if(e.id||"number"==typeof e.id)try{await V.confirm(a,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await U(e.id,l),y.success("状态更新成功"),oe()}catch(t){if("cancel"!==t){console.error(`更新状态到 ${l} 失败:`,t);const e=null==(o=null==t?void 0:t.response)?void 0:o.data;y.error((null==e?void 0:e.message)||(null==e?void 0:e.error)||`状态更新失败: ${t.message||"请重试"}`)}}else y.error("报价单ID无效，无法更新状态。")},ie=async e=>{var l;if(W.value&&e)try{await A(e,W.value),y.success("成功将报价单项目添加到订单！"),M.value=!1,oe()}catch(a){console.error("添加到现有订单失败:",a);const e=null==(l=null==a?void 0:a.response)?void 0:l.data;y.error((null==e?void 0:e.message)||(null==e?void 0:e.error)||`添加失败: ${a.message||"请重试"}`)}else y.warning("未选择报价单或订单。")},ue=e=>{D.value=e,oe()},de=e=>{I.value=e,oe()};return C((()=>{oe()})),(e,l)=>{const w=o("el-button"),k=o("el-option"),C=o("el-select"),x=o("el-form-item"),U=o("el-input"),A=o("el-form"),B=o("el-table-column"),H=o("el-tag"),ce=o("el-table"),me=o("el-pagination"),pe=o("el-card"),fe=o("el-radio"),ve=o("el-radio-group"),ge=o("el-switch"),_e=o("el-checkbox"),he=o("el-checkbox-group"),ye=o("el-dialog"),be=t("loading");return n(),c("div",S,[u(pe,{class:"header-card"},{header:r((()=>[i("div",X,[l[14]||(l[14]=i("span",null,"报价单列表",-1)),u(w,{type:"primary",onClick:se},{default:r((()=>l[12]||(l[12]=[d("新增报价单")]))),_:1}),u(w,{type:"info",onClick:Z},{default:r((()=>l[13]||(l[13]=[d("批量导出")]))),_:1})])])),default:r((()=>[u(A,{inline:!0,model:J,class:"search-form"},{default:r((()=>[u(x,{label:"客户"},{default:r((()=>[u(C,{modelValue:J.customer_id,"onUpdate:modelValue":l[0]||(l[0]=e=>J.customer_id=e),filterable:"",remote:"","remote-method":f(R),placeholder:"请输入客户名称",clearable:""},{default:r((()=>[(n(!0),c(p,null,m(L.value,(e=>(n(),s(k,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","remote-method"])])),_:1}),u(x,{label:"项目名称"},{default:r((()=>[u(U,{modelValue:J.project_name,"onUpdate:modelValue":l[1]||(l[1]=e=>J.project_name=e),placeholder:"请输入项目名称",clearable:""},null,8,["modelValue"])])),_:1}),u(x,{label:"状态"},{default:r((()=>[u(C,{modelValue:J.status,"onUpdate:modelValue":l[2]||(l[2]=e=>J.status=e),placeholder:"请选择状态",clearable:""},{default:r((()=>[u(k,{label:"草稿",value:"草稿"}),u(k,{label:"已提交",value:"已提交"}),u(k,{label:"已确认",value:"已确认"}),u(k,{label:"已转订单",value:"已转订单"}),u(k,{label:"已拒绝",value:"已拒绝"}),u(k,{label:"已过期",value:"已过期"})])),_:1},8,["modelValue"])])),_:1}),u(x,null,{default:r((()=>[u(w,{type:"primary",onClick:te},{default:r((()=>l[15]||(l[15]=[d("搜索")]))),_:1}),u(w,{onClick:ne},{default:r((()=>l[16]||(l[16]=[d("重置")]))),_:1})])),_:1})])),_:1},8,["model"]),v((n(),s(ce,{data:b.value,border:"",style:{width:"100%"},onSelectionChange:Y},{default:r((()=>[u(B,{type:"selection",width:"55"}),u(B,{prop:"quotation_number",label:"报价单号",width:"180"}),u(B,{prop:"customer_name",label:"客户名称",width:"180"}),u(B,{prop:"project_name",label:"项目名称",width:"180"}),u(B,{prop:"valid_until",label:"有效期至",width:"180"},{default:r((({row:e})=>[d(g(f(q)(e.valid_until)),1)])),_:1}),u(B,{prop:"status",label:"状态",width:"100"},{default:r((({row:e})=>{return[u(H,{type:(l=e.status,{"草稿":"info","已提交":"warning","已确认":"success","已转订单":"success","已拒绝":"danger","已过期":"info"}[l]||"info")},{default:r((()=>[d(g(e.status),1)])),_:2},1032,["type"])];var l})),_:1}),u(B,{prop:"created_at",label:"创建时间",width:"180"},{default:r((({row:e})=>[d(g(f(E)(e.created_at)),1)])),_:1}),u(B,{label:"操作",fixed:"right",width:"auto","min-width":"280"},{default:r((({row:e})=>[u(w,{type:"primary",link:"",size:"small",onClick:l=>(e=>{e.id?a.push(`/quotations/${e.id}`):y.warning("无效的报价单ID")})(e),disabled:!e.id},{default:r((()=>l[17]||(l[17]=[d("查看")]))),_:2},1032,["onClick","disabled"]),e.id&&"string"==typeof e.status&&["草稿","已提交"].includes(e.status)?(n(),s(w,{key:0,type:"primary",link:"",size:"small",onClick:l=>(e=>{e.id?"string"==typeof e.status?["草稿","已提交"].includes(e.status)?a.push(`/quotations/${e.id}/edit`):y.warning("当前状态的报价单不可编辑。"):y.warning("报价单状态无效或未定义，无法编辑。"):y.warning("无效的报价单ID")})(e)},{default:r((()=>l[18]||(l[18]=[d(" 编辑 ")]))),_:2},1032,["onClick"])):h("",!0),e.id&&"草稿"===e.status?(n(),s(w,{key:1,type:"success",link:"",size:"small",onClick:l=>re(e,"已提交","确认要提交此报价单吗？")},{default:r((()=>l[19]||(l[19]=[d(" 提交 ")]))),_:2},1032,["onClick"])):h("",!0),e.id&&"已提交"===e.status?(n(),s(w,{key:2,type:"success",link:"",size:"small",onClick:l=>re(e,"已确认","确认此报价单吗？")},{default:r((()=>l[20]||(l[20]=[d(" 标记已确认 ")]))),_:2},1032,["onClick"])):h("",!0),e.id&&"已提交"===e.status?(n(),s(w,{key:3,type:"danger",link:"",size:"small",onClick:l=>re(e,"已拒绝","确认要拒绝此报价单吗？")},{default:r((()=>l[21]||(l[21]=[d(" 标记已拒绝 ")]))),_:2},1032,["onClick"])):h("",!0),e.id&&"已确认"===e.status?(n(),s(w,{key:4,type:"success",link:"",size:"small",onClick:l=>(async e=>{if(e.id)try{if(await V.confirm("确定要将此报价单转为订单吗？","提示",{type:"warning"}),"已确认"!==e.status)return void y.warning("只能从已确认状态的报价单创建订单，请先确认报价单。");const l={customer_id:e.customer_id,project_name:e.project_name||"",project_address:e.project_address||"",status:"draft"};console.log("创建订单请求数据:",l),e.creating_order=!0;const a=await z(e.id,l);console.log("创建订单成功:",a),y.success("订单创建成功"),oe()}catch(l){if("cancel"!==l){console.error("创建订单失败:",l);const e=l instanceof Error?l.message:"未知错误";y.error(`创建订单失败: ${e}`)}}finally{e&&(e.creating_order=!1)}})(e)},{default:r((()=>l[22]||(l[22]=[d(" 创建新订单 ")]))),_:2},1032,["onClick"])):h("",!0),e.id&&"已确认"===e.status?(n(),s(w,{key:5,type:"primary",link:"",size:"small",onClick:l=>(e=>{"number"==typeof e.id?(W.value=e.id,M.value=!0):y.error("无效的报价单ID")})(e)},{default:r((()=>l[23]||(l[23]=[d(" 添加到现有订单 ")]))),_:2},1032,["onClick"])):h("",!0),u(w,{type:"info",link:"",size:"small",onClick:l=>(async e=>{if(!e.id&&"number"!=typeof e.id)return void y.error("报价单ID无效，无法导出。");e.exporting=!0,y.info({message:"正在导出报价单，可能需要1-3分钟，请耐心等待...",duration:0,showClose:!0,key:"exportingMsg"});try{const l=await j(e.id,{format:"xlsx"});if(y.closeAll(),!(l instanceof Blob))throw new Error("导出失败：服务器返回的不是有效的文件数据");const a=l.type||("xlsx"===K.format?"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":"application/pdf"),o=new Blob([l],{type:a}),t=window.URL.createObjectURL(o),n=document.createElement("a");n.href=t,n.download=`报价单_${e.quotation_number||e.id}.xlsx`,document.body.appendChild(n),n.click(),document.body.removeChild(n),setTimeout((()=>{window.URL.revokeObjectURL(t)}),100),y.success("导出成功")}catch(l){y.closeAll(),console.error("导出报价单失败:",l);const e=l instanceof Error?l.message.includes("timeout")?"导出超时，请减少导出数据量或联系管理员增加超时时间":l.message:"导出报价单失败";y.error(`导出报价单失败: ${e}`)}finally{e.exporting=!1}})(e),loading:e.exporting,disabled:!e.id},{default:r((()=>l[24]||(l[24]=[d(" 导出 ")]))),_:2},1032,["onClick","loading","disabled"]),e.id&&"string"==typeof e.status&&["草稿","已拒绝","已过期"].includes(e.status)?(n(),s(w,{key:6,type:"danger",link:"",size:"small",onClick:l=>(async e=>{var l;if(e.id||"number"==typeof e.id)try{await V.confirm("确认要删除此报价单吗？删除后无法恢复。","警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"}),await $(e.id),y.success("报价单删除成功"),oe()}catch(a){if("cancel"!==a){console.error("删除报价单失败:",a);const e=null==(l=null==a?void 0:a.response)?void 0:l.data;y.error((null==e?void 0:e.message)||(null==e?void 0:e.error)||`删除失败: ${a.message||"请重试"}`)}}else y.error("报价单ID无效，无法删除。")})(e)},{default:r((()=>l[25]||(l[25]=[d(" 删除 ")]))),_:2},1032,["onClick"])):h("",!0)])),_:1})])),_:1},8,["data"])),[[be,_.value]]),i("div",F,[u(me,{"current-page":I.value,"onUpdate:currentPage":l[3]||(l[3]=e=>I.value=e),"page-size":D.value,"onUpdate:pageSize":l[4]||(l[4]=e=>D.value=e),"page-sizes":[10,20,50,100],total:T.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ue,onCurrentChange:de},null,8,["current-page","page-size","total"])])])),_:1}),u(O,{modelValue:M.value,"onUpdate:modelValue":l[5]||(l[5]=e=>M.value=e),onConfirm:ie},null,8,["modelValue"]),u(ye,{modelValue:N.value,"onUpdate:modelValue":l[11]||(l[11]=e=>N.value=e),title:"批量导出报价单配置",width:"500px"},{footer:r((()=>[i("span",P,[u(w,{onClick:l[10]||(l[10]=e=>N.value=!1)},{default:r((()=>l[29]||(l[29]=[d("取消")]))),_:1}),u(w,{type:"primary",onClick:ae,loading:Q.value},{default:r((()=>l[30]||(l[30]=[d(" 确认导出 ")]))),_:1},8,["loading"])])])),default:r((()=>[u(A,{model:K,"label-width":"120px"},{default:r((()=>[u(x,{label:"导出格式"},{default:r((()=>[u(ve,{modelValue:K.format,"onUpdate:modelValue":l[6]||(l[6]=e=>K.format=e)},{default:r((()=>[u(fe,{label:"xlsx"},{default:r((()=>l[26]||(l[26]=[d("Excel (XLSX)")]))),_:1}),u(fe,{label:"pdf"},{default:r((()=>l[27]||(l[27]=[d("PDF")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),u(x,{label:"包含表头"},{default:r((()=>[u(ge,{modelValue:K.includeHeader,"onUpdate:modelValue":l[7]||(l[7]=e=>K.includeHeader=e)},null,8,["modelValue"])])),_:1}),u(x,{label:"导出列"},{default:r((()=>[u(_e,{modelValue:K.selectAll,"onUpdate:modelValue":l[8]||(l[8]=e=>K.selectAll=e),onChange:ee},{default:r((()=>l[28]||(l[28]=[d("全选")]))),_:1},8,["modelValue"]),u(he,{modelValue:K.selectedColumns,"onUpdate:modelValue":l[9]||(l[9]=e=>K.selectedColumns=e),class:"column-checkboxes"},{default:r((()=>[(n(),c(p,null,m(G,(e=>u(_e,{key:e.prop,label:e.prop,onChange:le},{default:r((()=>[d(g(e.label),1)])),_:2},1032,["label"]))),64))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-03960d3a"]]);export{W as default};
