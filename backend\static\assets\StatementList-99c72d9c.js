import{J as e,r as a,f as t,o as l,c as o,a as s,t as r,j as n,i as d,h as u,k as i,b as c,L as p,e as m,M as v,F as _,m as f,N as g,g as h,U as y,W as b}from"./index-3d4c440c.js";import{s as w}from"./customer-471ca075.js";import{g as k,c as C,d as V,e as x}from"./statement-2acbbb5b.js";import{b as U}from"./formatter-5775d610.js";import{_ as z}from"./_plugin-vue_export-helper-1b428a4d.js";const R={class:"table-empty-placeholder"},j={class:"text"},B={key:0,class:"description"},S={key:1,class:"action"},Y=z(e({__name:"TableEmptyPlaceholder",props:{message:{type:String,default:"暂无数据"},description:{type:String,default:""},showRefreshButton:{type:Boolean,default:!1}},emits:["refresh"],setup(e,{emit:c}){const p=c,m=a(!1),v=async()=>{try{m.value=!0,p("refresh"),await new Promise((e=>setTimeout(e,500)))}finally{m.value=!1}};return(a,c)=>{const p=t("el-button");return l(),o("div",R,[c[1]||(c[1]=s("div",{class:"icon-container"},[s("i",{class:"el-icon-empty"},[s("svg",{viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32"},[s("path",{fill:"currentColor",d:"M512 832c167.36 0 305.984-126.528 323.072-289.856H188.928C206.016 705.472 344.64 832 512 832zm0 64C299.936 896 128 724.064 128 512S299.936 128 512 128s384 171.936 384 384-171.936 384-384 384zm0-640c-159.04 0-288 128.96-288 288 0 148.832 113.36 271.392 258.688 286.336a381.44 381.44 0 0058.624 0C686.56 525.312 800 402.832 800 254c0-159.04-128.96-288-288-288zM320 384h384c25.6 0 48 19.84 48 45.44v108.16C752 563.2 730.24 640 565.76 640H458.24C293.76 640 272 563.2 272 537.6V429.44c0-25.6 19.84-45.44 48-45.44z"})])])],-1)),s("div",j,r(e.message||"暂无数据"),1),e.description?(l(),o("div",B,r(e.description),1)):n("",!0),e.showRefreshButton?(l(),o("div",S,[d(p,{onClick:v,type:"primary",loading:m.value,size:"small"},{default:u((()=>c[0]||(c[0]=[i("刷新")]))),_:1},8,["loading"])])):n("",!0)])}}}),[["__scopeId","data-v-59bcccbe"]]),M={class:"statement-list"},L={class:"operation-bar"},$={class:"pagination-container"},D={class:"dialog-footer"},T=z(e({__name:"StatementList",setup(e){const z=c(),R=p({customer_id:"",status:"",date_range:[]}),j=a([]),B=a(!1),S=a([]),T=a(1),A=a(10),O=a(0),P=a(!1),E=a(),F=p({statement_id:"",due_date:""}),H={due_date:[{required:!0,message:"请选择应付款日期",trigger:"change"}]},I=a([]),J=async e=>{if(e)try{const a=await w(e);j.value=a.list||[]}catch(a){console.error("搜索客户失败:",a)}else j.value=[]},N=async()=>{await q()},W=()=>{R.customer_id="",R.status="",R.date_range=[],N()},q=async()=>{var e,a;B.value=!0;try{const t={page:T.value,per_page:A.value,customer_id:R.customer_id,status:R.status,start_date:null==(e=R.date_range)?void 0:e[0],end_date:null==(a=R.date_range)?void 0:a[1]},l=await k(t);console.log("获取对账单列表原始响应:",JSON.stringify(l)),l.list&&Array.isArray(l.list)&&l.list.length>0?(S.value=l.list,console.log("成功获取并设置对账单数据:",S.value.length,"条")):(console.warn("获取到的对账单列表为空"),S.value=[]),l.pagination?void 0!==l.pagination.total_count?O.value=l.pagination.total_count:void 0!==l.pagination.total?O.value=l.pagination.total:O.value=S.value.length:O.value=S.value.length,console.log("对账单列表数据最终状态 - 显示:",S.value.length,"条, 总数:",O.value)}catch(t){console.error("获取对账单列表失败:",t),y.error("获取对账单列表失败"),S.value=[],O.value=0}finally{B.value=!1}},G=()=>{z.push("/statements/create")},K=async()=>{E.value&&await E.value.validate((async e=>{if(e)try{await C(F.statement_id,{due_date:F.due_date}),y.success("确认对账单成功"),P.value=!1,await q()}catch(a){console.error("确认对账单失败:",a),y.error("确认对账单失败")}}))},Q=e=>{I.value=e,console.log("选中的对账单:",e)},X=async()=>{try{if(0===I.value.length)return void y.warning("请先选择要导出的对账单");for(const a of I.value)try{const e=await x(a.id),t=window.URL.createObjectURL(new Blob([e.data])),l=document.createElement("a");l.href=t,l.setAttribute("download",`对账单_${a.statement_number}.xlsx`),document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(t)}catch(e){console.error(`导出对账单 ${a.statement_number} 失败:`,e),y.error(`导出对账单 ${a.statement_number} 失败`)}y.success("导出成功")}catch(e){console.error("导出失败:",e),y.error("导出失败")}},Z=e=>{A.value=e,q()},ee=e=>{T.value=e,q()},ae=e=>({draft:"待确认",confirmed:"已确认",paid:"已付款",cancelled:"已取消","待确认":"待确认","已确认":"已确认","已完成":"已完成","已取消":"已取消"}[e]||e),te=()=>{console.log("手动刷新对账单列表"),q()};return m((()=>{T.value=1,A.value=10,R.customer_id="",R.status="",R.date_range=[],console.log("组件挂载，开始获取对账单列表数据..."),q()})),(e,a)=>{const c=t("el-option"),p=t("el-select"),m=t("el-form-item"),w=t("el-date-picker"),k=t("el-button"),C=t("el-form"),x=t("el-card"),le=t("el-table-column"),oe=t("el-tag"),se=t("el-table"),re=t("el-pagination"),ne=t("el-dialog"),de=v("loading");return l(),o("div",M,[d(x,{class:"search-card"},{default:u((()=>[d(C,{model:R,inline:""},{default:u((()=>[d(m,{label:"客户"},{default:u((()=>[d(p,{modelValue:R.customer_id,"onUpdate:modelValue":a[0]||(a[0]=e=>R.customer_id=e),filterable:"",remote:"","remote-method":J,placeholder:"请输入客户名称",clearable:""},{default:u((()=>[(l(!0),o(_,null,f(j.value,(e=>(l(),h(c,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(m,{label:"状态"},{default:u((()=>[d(p,{modelValue:R.status,"onUpdate:modelValue":a[1]||(a[1]=e=>R.status=e),placeholder:"请选择状态",clearable:""},{default:u((()=>[d(c,{label:"待确认",value:"待确认"}),d(c,{label:"已确认",value:"已确认"}),d(c,{label:"已完成",value:"已完成"}),d(c,{label:"已取消",value:"已取消"})])),_:1},8,["modelValue"])])),_:1}),d(m,{label:"日期范围"},{default:u((()=>[d(w,{modelValue:R.date_range,"onUpdate:modelValue":a[2]||(a[2]=e=>R.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),d(m,null,{default:u((()=>[d(k,{type:"primary",onClick:N},{default:u((()=>a[8]||(a[8]=[i("搜索")]))),_:1}),d(k,{onClick:W},{default:u((()=>a[9]||(a[9]=[i("重置")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),s("div",L,[d(k,{type:"primary",onClick:G},{default:u((()=>a[10]||(a[10]=[i("新建对账单")]))),_:1}),d(k,{type:"success",disabled:0===I.value.length,onClick:X},{default:u((()=>[i(" 导出选中"+r(I.value.length?`(${I.value.length})`:""),1)])),_:1},8,["disabled"])]),d(x,null,{default:u((()=>[g((l(),h(se,{data:S.value,border:"",style:{width:"100%"},onSelectionChange:Q},{empty:u((()=>[d(Y,{message:"暂无对账单数据",description:"如果刚创建了新对账单，请尝试点击刷新按钮",showRefreshButton:!0,onRefresh:te})])),default:u((()=>[d(le,{type:"selection",width:"55"}),d(le,{prop:"statement_number",label:"对账单号",width:"180"}),d(le,{prop:"customer.name",label:"客户名称",width:"180"}),d(le,{prop:"statement_date",label:"对账日期",width:"120"}),d(le,{prop:"total_amount",label:"总金额",width:"120"},{default:u((({row:e})=>{return[i(r((a=e.total_amount,U(null==a?0:a))),1)];var a})),_:1}),d(le,{prop:"status",label:"状态",width:"100"},{default:u((({row:e})=>{return[d(oe,{type:(a=e.status,{draft:"warning",confirmed:"success",paid:"success",cancelled:"info","待确认":"warning","已确认":"success","已完成":"success","已取消":"info"}[a]||"info")},{default:u((()=>[i(r(ae(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),d(le,{prop:"due_date",label:"应付款日期",width:"120"}),d(le,{label:"操作",fixed:"right",width:"200"},{default:u((({row:e})=>[d(k,{link:"",type:"primary",onClick:a=>(e=>{z.push(`/statements/${e.id}`)})(e)},{default:u((()=>a[11]||(a[11]=[i("查看")]))),_:2},1032,["onClick"]),"待确认"===e.status?(l(),h(k,{key:0,link:"",type:"primary",onClick:a=>(async e=>{F.statement_id=e.id,e.due_date?F.due_date=e.due_date:F.due_date="",P.value=!0})(e)},{default:u((()=>a[12]||(a[12]=[i("确认")]))),_:2},1032,["onClick"])):n("",!0),"待确认"===e.status?(l(),h(k,{key:1,link:"",type:"danger",onClick:a=>(e=>{b.confirm("确定要删除该对账单吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await V(e.id),y.success("删除成功"),await q()}catch(a){console.error("删除对账单失败:",a),y.error("删除对账单失败")}}))})(e)},{default:u((()=>a[13]||(a[13]=[i("删除")]))),_:2},1032,["onClick"])):n("",!0)])),_:1})])),_:1},8,["data"])),[[de,B.value]]),s("div",$,[d(re,{"current-page":T.value,"onUpdate:currentPage":a[3]||(a[3]=e=>T.value=e),"page-size":A.value,"onUpdate:pageSize":a[4]||(a[4]=e=>A.value=e),"page-sizes":[10,20,50,100],total:O.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Z,onCurrentChange:ee},null,8,["current-page","page-size","total"])])])),_:1}),d(ne,{modelValue:P.value,"onUpdate:modelValue":a[7]||(a[7]=e=>P.value=e),title:"确认对账单",width:"500px"},{footer:u((()=>[s("span",D,[d(k,{onClick:a[6]||(a[6]=e=>P.value=!1)},{default:u((()=>a[14]||(a[14]=[i("取消")]))),_:1}),d(k,{type:"primary",onClick:K},{default:u((()=>a[15]||(a[15]=[i("确认")]))),_:1})])])),default:u((()=>[d(C,{ref_key:"confirmFormRef",ref:E,model:F,rules:H,"label-width":"100px"},{default:u((()=>[d(m,{label:"应付款日期",prop:"due_date"},{default:u((()=>[d(w,{modelValue:F.due_date,"onUpdate:modelValue":a[5]||(a[5]=e=>F.due_date=e),type:"date",placeholder:"请选择应付款日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-b7a4cc26"]]);export{T as default};
