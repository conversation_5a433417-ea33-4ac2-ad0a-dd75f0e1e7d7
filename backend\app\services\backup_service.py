"""
数据备份服务
提供数据库备份、恢复、文件管理等功能
"""
import os
import shutil
import hashlib
from datetime import datetime
from typing import List, Optional, Dict, Any
from flask import current_app
from sqlalchemy import desc

from app import db
from app.models.system import BackupRecord, BackupSettings


class BackupService:
    """备份服务类"""

    def __init__(self):
        self.backup_dir = None
        self._initialized = False
    
    def _ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            self._ensure_backup_directory()
            self._initialized = True

    def _ensure_backup_directory(self):
        """确保备份目录存在"""
        try:
            # 获取备份目录路径
            settings = self.get_backup_settings()
            if settings and settings.backup_path:
                self.backup_dir = settings.backup_path
            else:
                self.backup_dir = os.path.join(current_app.instance_path, 'backups')

            # 创建目录
            os.makedirs(self.backup_dir, exist_ok=True)
            current_app.logger.info(f"备份目录: {self.backup_dir}")

        except Exception as e:
            current_app.logger.error(f"创建备份目录失败: {str(e)}")
            raise
    
    def get_backup_settings(self) -> Optional[BackupSettings]:
        """获取备份设置"""
        try:
            settings = BackupSettings.query.first()
            if not settings:
                # 创建默认设置
                settings = BackupSettings(
                    auto_backup_enabled=False,
                    backup_frequency='daily',
                    backup_time='02:00:00',
                    keep_count=10,
                    backup_path=os.path.join(current_app.instance_path, 'backups')
                )
                db.session.add(settings)
                db.session.commit()
            return settings
        except Exception as e:
            current_app.logger.error(f"获取备份设置失败: {str(e)}")
            return None
    
    def update_backup_settings(self, data: Dict[str, Any]) -> BackupSettings:
        """更新备份设置"""
        try:
            settings = self.get_backup_settings()
            if not settings:
                settings = BackupSettings()
                db.session.add(settings)
            
            # 更新设置
            for key, value in data.items():
                if hasattr(settings, key):
                    setattr(settings, key, value)
            
            db.session.commit()

            # 更新备份目录
            if 'backup_path' in data:
                self.backup_dir = data['backup_path']
                self._ensure_backup_directory()

            # 更新调度器任务
            try:
                from app.services.scheduler_service import scheduler_service
                scheduler_service.schedule_backup_job(settings)
            except Exception as e:
                current_app.logger.warning(f"更新调度器任务失败: {str(e)}")

            return settings
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新备份设置失败: {str(e)}")
            raise
    
    def _find_database_file(self) -> str:
        """查找数据库文件"""
        # 可能的数据库文件位置
        db_files = [
            os.path.join(current_app.instance_path, 'project.db'),
            os.path.join(current_app.instance_path, 'app.db'),
            os.path.join(current_app.instance_path, 'dev_app.db'),
            os.path.join(current_app.instance_path, 'emb.sqlite'),
            'project.db',
            'app.db'
        ]
        
        for db_file in db_files:
            if os.path.exists(db_file):
                return db_file
        
        # 尝试从SQLAlchemy获取数据库路径
        try:
            db_uri = str(db.engine.url.database)
            if db_uri and os.path.exists(db_uri):
                return db_uri
        except:
            pass
        
        raise FileNotFoundError("找不到数据库文件")
    
    def _calculate_checksum(self, file_path: str) -> str:
        """计算文件MD5校验和"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            current_app.logger.error(f"计算文件校验和失败: {str(e)}")
            return ""
    
    def create_backup(self, name: str, description: str = None, backup_type: str = "manual") -> BackupRecord:
        """创建数据库备份"""
        self._ensure_initialized()
        backup_record = None
        try:
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"backup_{timestamp}.db"
            backup_path = os.path.join(self.backup_dir, filename)
            
            # 创建备份记录
            backup_record = BackupRecord(
                name=name,
                filename=filename,
                file_path=backup_path,
                file_size=0,
                backup_type=backup_type,
                status='running',
                description=description,
                started_at=datetime.now()
            )
            db.session.add(backup_record)
            db.session.commit()
            
            current_app.logger.info(f"开始创建备份: {name}")
            
            # 查找数据库文件
            source_db = self._find_database_file()
            current_app.logger.info(f"源数据库文件: {source_db}")
            
            # 创建备份
            shutil.copy2(source_db, backup_path)
            
            # 获取文件信息
            file_size = os.path.getsize(backup_path)
            checksum = self._calculate_checksum(backup_path)
            
            # 更新备份记录
            backup_record.file_size = file_size
            backup_record.checksum = checksum
            backup_record.status = 'completed'
            backup_record.completed_at = datetime.now()
            db.session.commit()
            
            current_app.logger.info(f"备份创建成功: {backup_path}, 大小: {file_size} bytes")
            
            # 清理旧备份
            self._cleanup_old_backups()
            
            return backup_record
            
        except Exception as e:
            current_app.logger.error(f"创建备份失败: {str(e)}")
            
            # 更新备份记录状态
            if backup_record:
                backup_record.status = 'failed'
                backup_record.error_message = str(e)
                backup_record.completed_at = datetime.now()
                db.session.commit()
            
            # 清理失败的备份文件
            if backup_record and backup_record.file_path and os.path.exists(backup_record.file_path):
                try:
                    os.remove(backup_record.file_path)
                except:
                    pass
            
            raise
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            settings = self.get_backup_settings()
            if not settings:
                return
            
            # 获取所有已完成的备份，按创建时间倒序
            backups = BackupRecord.query.filter_by(status='completed').order_by(desc(BackupRecord.created_at)).all()
            
            # 如果备份数量超过保留数量，删除多余的
            if len(backups) > settings.keep_count:
                old_backups = backups[settings.keep_count:]
                for backup in old_backups:
                    self.delete_backup(backup.id)
                    
        except Exception as e:
            current_app.logger.error(f"清理旧备份失败: {str(e)}")
    
    def get_backup_list(self, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """获取备份列表"""
        try:
            query = BackupRecord.query.order_by(desc(BackupRecord.created_at))
            pagination = query.paginate(page=page, per_page=per_page, error_out=False)
            
            return {
                'items': [backup.to_dict() for backup in pagination.items],
                'total': pagination.total,
                'page': page,
                'per_page': per_page,
                'pages': pagination.pages
            }
            
        except Exception as e:
            current_app.logger.error(f"获取备份列表失败: {str(e)}")
            raise
    
    def get_backup_by_id(self, backup_id: int) -> Optional[BackupRecord]:
        """根据ID获取备份记录"""
        try:
            return BackupRecord.query.get(backup_id)
        except Exception as e:
            current_app.logger.error(f"获取备份记录失败: {str(e)}")
            return None
    
    def delete_backup(self, backup_id: int) -> bool:
        """删除备份"""
        try:
            backup = BackupRecord.query.get(backup_id)
            if not backup:
                return False
            
            # 删除备份文件
            if backup.file_path and os.path.exists(backup.file_path):
                os.remove(backup.file_path)
                current_app.logger.info(f"删除备份文件: {backup.file_path}")
            
            # 删除数据库记录
            db.session.delete(backup)
            db.session.commit()
            
            current_app.logger.info(f"删除备份记录: {backup.name}")
            return True
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除备份失败: {str(e)}")
            raise
    
    def get_backup_statistics(self) -> Dict[str, Any]:
        """获取备份统计信息"""
        try:
            total_backups = BackupRecord.query.count()
            completed_backups = BackupRecord.query.filter_by(status='completed').count()
            manual_backups = BackupRecord.query.filter_by(backup_type='manual', status='completed').count()
            auto_backups = BackupRecord.query.filter_by(backup_type='auto', status='completed').count()
            
            # 计算总大小
            total_size = db.session.query(db.func.sum(BackupRecord.file_size)).filter_by(status='completed').scalar() or 0
            
            # 最近备份时间
            latest_backup = BackupRecord.query.filter_by(status='completed').order_by(desc(BackupRecord.completed_at)).first()
            latest_backup_time = latest_backup.completed_at if latest_backup else None
            
            return {
                'total_backups': total_backups,
                'completed_backups': completed_backups,
                'manual_backups': manual_backups,
                'auto_backups': auto_backups,
                'total_size': total_size,
                'latest_backup_time': latest_backup_time.strftime('%Y-%m-%d %H:%M:%S') if latest_backup_time else None
            }
            
        except Exception as e:
            current_app.logger.error(f"获取备份统计失败: {str(e)}")
            return {
                'total_backups': 0,
                'completed_backups': 0,
                'manual_backups': 0,
                'auto_backups': 0,
                'total_size': 0,
                'latest_backup_time': None
            }

    def restore_from_backup(self, backup_id: int) -> bool:
        """从备份恢复数据"""
        temp_backup = None
        try:
            # 获取备份记录
            backup = BackupRecord.query.get(backup_id)
            if not backup:
                raise ValueError("备份记录不存在")

            if backup.status != 'completed':
                raise ValueError("备份未完成，无法恢复")

            # 检查备份文件是否存在
            if not os.path.exists(backup.file_path):
                raise FileNotFoundError("备份文件不存在")

            current_app.logger.info(f"开始从备份恢复数据: {backup.name}")

            # 验证备份文件完整性
            if backup.checksum:
                current_checksum = self._calculate_checksum(backup.file_path)
                if current_checksum != backup.checksum:
                    raise ValueError("备份文件校验失败，文件可能已损坏")

            # 查找当前数据库文件
            current_db = self._find_database_file()
            current_app.logger.info(f"当前数据库文件: {current_db}")

            # 创建当前数据库的临时备份
            temp_backup = f"{current_db}.{datetime.now().strftime('%Y%m%d%H%M%S')}.temp"
            shutil.copy2(current_db, temp_backup)
            current_app.logger.info(f"创建临时备份: {temp_backup}")

            try:
                # 关闭数据库连接
                db.session.remove()

                # 恢复备份
                shutil.copy2(backup.file_path, current_db)
                current_app.logger.info(f"恢复数据库文件: {backup.file_path} -> {current_db}")

                # 重新连接数据库
                db.create_all()

                current_app.logger.info(f"数据恢复成功: {backup.name}")
                return True

            except Exception as e:
                # 恢复失败，还原临时备份
                current_app.logger.error(f"恢复失败，还原临时备份: {str(e)}")
                shutil.copy2(temp_backup, current_db)

                # 重新连接数据库
                db.create_all()

                raise e

        except Exception as e:
            current_app.logger.error(f"数据恢复失败: {str(e)}")
            raise

        finally:
            # 删除临时备份
            if temp_backup and os.path.exists(temp_backup):
                try:
                    os.remove(temp_backup)
                    current_app.logger.info(f"删除临时备份: {temp_backup}")
                except:
                    pass

    def upload_and_restore_backup(self, file, name: str, description: str = None) -> BackupRecord:
        """上传备份文件并恢复"""
        self._ensure_initialized()
        backup_record = None
        uploaded_file_path = None

        try:
            # 验证文件
            if not file or not file.filename:
                raise ValueError("请选择备份文件")

            if not file.filename.endswith('.db'):
                raise ValueError("只支持.db格式的备份文件")

            # 生成文件名和路径
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"uploaded_{timestamp}.db"
            uploaded_file_path = os.path.join(self.backup_dir, filename)

            # 保存上传的文件
            file.save(uploaded_file_path)
            current_app.logger.info(f"上传文件保存到: {uploaded_file_path}")

            # 获取文件信息
            file_size = os.path.getsize(uploaded_file_path)
            checksum = self._calculate_checksum(uploaded_file_path)

            # 创建备份记录
            backup_record = BackupRecord(
                name=name,
                filename=filename,
                file_path=uploaded_file_path,
                file_size=file_size,
                backup_type='manual',
                status='completed',
                description=description,
                checksum=checksum,
                started_at=datetime.now(),
                completed_at=datetime.now()
            )
            db.session.add(backup_record)
            db.session.commit()

            # 从上传的备份恢复数据
            self.restore_from_backup(backup_record.id)

            current_app.logger.info(f"上传备份并恢复成功: {name}")
            return backup_record

        except Exception as e:
            current_app.logger.error(f"上传备份并恢复失败: {str(e)}")

            # 清理上传的文件
            if uploaded_file_path and os.path.exists(uploaded_file_path):
                try:
                    os.remove(uploaded_file_path)
                except:
                    pass

            # 删除备份记录
            if backup_record:
                try:
                    db.session.delete(backup_record)
                    db.session.commit()
                except:
                    db.session.rollback()

            raise

    def validate_backup_file(self, file_path: str) -> Dict[str, Any]:
        """验证备份文件"""
        try:
            if not os.path.exists(file_path):
                return {'valid': False, 'error': '文件不存在'}

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return {'valid': False, 'error': '文件为空'}

            # 简单的SQLite文件格式检查
            with open(file_path, 'rb') as f:
                header = f.read(16)
                if not header.startswith(b'SQLite format 3'):
                    return {'valid': False, 'error': '不是有效的SQLite数据库文件'}

            return {
                'valid': True,
                'file_size': file_size,
                'checksum': self._calculate_checksum(file_path)
            }

        except Exception as e:
            return {'valid': False, 'error': f'文件验证失败: {str(e)}'}


# 创建全局备份服务实例
backup_service = BackupService()
