<template>
  <div class="data-backup-restore">
    <el-card class="header-card mb-20">
      <div class="card-header">
        <div class="left">
          <h2 class="page-title">数据备份与恢复</h2>
          <el-tag type="info">管理系统数据的备份与恢复</el-tag>
        </div>
        <div class="right">
          <el-button type="primary" @click="handleCreateBackup">
            <el-icon><Download /></el-icon> 立即备份
          </el-button>
          <el-button type="success" @click="handleUploadBackup">
            <el-icon><Upload /></el-icon> 上传备份
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 功能区域卡片 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :span="12">
        <el-card class="feature-card">
          <template #header>
            <div class="feature-header">
              <el-icon class="icon"><Upload /></el-icon>
              <span>数据恢复</span>
            </div>
          </template>
          <div class="feature-content">
            <p>从备份文件恢复系统数据</p>
            <el-alert
              title="恢复数据将覆盖当前系统数据，请谨慎操作！"
              type="warning"
              :closable="false"
              show-icon
              class="mb-20"
            />
            <p>请从下方备份历史中选择要恢复的备份，或上传本地备份文件进行恢复。</p>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="feature-card">
          <template #header>
            <div class="feature-header">
              <el-icon class="icon"><Timer /></el-icon>
              <span>备份统计</span>
            </div>
          </template>
          <div class="feature-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.totalBackups }}</div>
                  <div class="stat-label">总备份数</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ formatFileSize(statistics.totalSize) }}</div>
                  <div class="stat-label">总大小</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mt-10">
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.manualBackups }}</div>
                  <div class="stat-label">手动备份</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ statistics.autoBackups }}</div>
                  <div class="stat-label">自动备份</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 备份历史列表 -->
    <el-card>
      <template #header>
        <div class="table-header">
          <h3>备份历史记录</h3>
          <div class="filter-area">
            <el-input 
              v-model="searchKeyword" 
              placeholder="搜索备份名称" 
              clearable 
              style="width: 220px"
              @keyup.enter="fetchBackupHistory"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="fetchBackupHistory">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </div>
        </div>
      </template>

      <div v-loading="tableLoading">
        <el-table :data="backupList" border style="width: 100%">
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="filename" label="备份文件名" min-width="200" />
          <el-table-column prop="backup_type" label="备份类型" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.backup_type === 'manual' ? 'primary' : 'success'">
                {{ scope.row.backup_type === 'manual' ? '手动' : '自动' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="file_size" label="文件大小" width="120">
            <template #default="scope">
              {{ formatFileSize(scope.row.file_size) }}
            </template>
          </el-table-column>
          <el-table-column prop="description" label="备注" min-width="200" />
          <el-table-column prop="created_at" label="备份时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button link type="warning" size="small" @click="handleRestore(scope.row)">恢复</el-button>
              <el-button link type="primary" size="small" @click="handleDownload(scope.row)">下载</el-button>
              <el-button link type="info" size="small" @click="handleViewDetail(scope.row)">详情</el-button>
              <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 创建备份对话框 -->
    <el-dialog
      v-model="backupDialogVisible"
      title="创建备份"
      width="500px"
      destroy-on-close
    >
      <el-form :model="backupForm" label-width="100px">
        <el-form-item label="备份描述">
          <el-input 
            v-model="backupForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入备份描述，如：系统升级前备份、重要数据导入前备份等"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="backupDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCreateBackup" :loading="backupLoading">
          立即备份
        </el-button>
      </template>
    </el-dialog>

    <!-- 上传备份对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传备份文件"
      width="500px"
      destroy-on-close
    >
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :limit="1"
        accept=".sql,.zip,.tar.gz"
        drag
        :on-change="handleFileChange"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将备份文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 .sql, .zip, .tar.gz 格式的备份文件
          </div>
        </template>
      </el-upload>
      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUploadBackup" :loading="uploadLoading">
          上传并恢复
        </el-button>
      </template>
    </el-dialog>

    <!-- 备份详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="备份详情"
      width="600px"
      destroy-on-close
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="备份文件名">{{ selectedBackup.filename }}</el-descriptions-item>
        <el-descriptions-item label="备份类型">
          <el-tag :type="selectedBackup.backup_type === 'manual' ? 'primary' : 'success'">
            {{ selectedBackup.backup_type === 'manual' ? '手动' : '自动' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="文件大小">{{ formatFileSize(selectedBackup.file_size) }}</el-descriptions-item>
        <el-descriptions-item label="备份时间">{{ formatDateTime(selectedBackup.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ selectedBackup.description || '无' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="warning" @click="handleRestore(selectedBackup)">
          恢复数据
        </el-button>
      </template>
    </el-dialog>

    <!-- 恢复确认对话框 -->
    <el-dialog
      v-model="restoreDialogVisible"
      title="恢复确认"
      width="500px"
      destroy-on-close
    >
      <div class="restore-confirm-content">
        <el-alert
          title="恢复数据将覆盖当前系统数据，此操作不可逆！"
          type="error"
          :closable="false"
          show-icon
          class="mb-20"
        />
        <p>您确定要从以下备份恢复系统数据吗？</p>
        <el-descriptions :column="1" border class="mb-20">
          <el-descriptions-item label="备份文件名">{{ selectedBackup.filename }}</el-descriptions-item>
          <el-descriptions-item label="备份时间">{{ formatDateTime(selectedBackup.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="备份大小">{{ formatFileSize(selectedBackup.file_size) }}</el-descriptions-item>
        </el-descriptions>
        
        <el-form>
          <el-form-item label="请输入'CONFIRM'以确认操作" required>
            <el-input v-model="confirmText" placeholder="输入CONFIRM" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="restoreDialogVisible = false">取消</el-button>
        <el-button 
          type="danger" 
          @click="confirmRestore" 
          :disabled="confirmText !== 'CONFIRM'"
          :loading="restoreLoading"
        >
          确认恢复
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Download, Upload, Timer, Search, Refresh, UploadFilled
} from '@element-plus/icons-vue'
import { dataBackupApi } from '@/api/system'

const tableLoading = ref(false)
const backupLoading = ref(false)
const uploadLoading = ref(false)
const restoreLoading = ref(false)
const backupDialogVisible = ref(false)
const uploadDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const restoreDialogVisible = ref(false)
const searchKeyword = ref('')
const confirmText = ref('')
const uploadRef = ref(null)
const uploadFile = ref(null)

// 备份列表
const backupList = ref([])

// 统计数据
const statistics = reactive({
  totalBackups: 0,
  totalSize: 0,
  manualBackups: 0,
  autoBackups: 0
})

// 表单数据
const backupForm = reactive({
  description: `手动备份_${new Date().toISOString().split('T')[0]}`
})

// 选中的备份
const selectedBackup = reactive({
  id: null,
  filename: '',
  backup_type: 'manual',
  file_size: 0,
  description: '',
  created_at: ''
})

// 获取备份历史
const fetchBackupHistory = async () => {
  tableLoading.value = true
  try {
    const response = await dataBackupApi.getBackupList() as any
    
    // 处理响应数据
    let data = []
    if (Array.isArray(response)) {
      data = response
    } else if (response && response.data) {
      data = Array.isArray(response.data) ? response.data : []
    }
    
    // 过滤搜索结果
    if (searchKeyword.value && searchKeyword.value.trim() !== '') {
      backupList.value = data.filter((item: any) => 
        item.filename.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
      )
    } else {
      backupList.value = data
    }
    
    // 计算统计数据
    updateStatistics()
  } catch (error) {
    console.error('获取备份历史失败:', error)
    ElMessage.error('获取备份历史失败，使用模拟数据')
    
    // 使用模拟数据
    backupList.value = [
      {
        id: 1,
        filename: 'backup_2025_01_01_10_00_00.sql',
        backup_type: 'manual',
        file_size: 1024 * 1024 * 50, // 50MB
        description: '系统升级前备份',
        created_at: '2025-01-01 10:00:00'
      },
      {
        id: 2,
        filename: 'backup_2025_01_02_02_00_00.sql',
        backup_type: 'auto',
        file_size: 1024 * 1024 * 45, // 45MB
        description: '自动备份',
        created_at: '2025-01-02 02:00:00'
      },
      {
        id: 3,
        filename: 'backup_2025_01_03_14_30_00.sql',
        backup_type: 'manual',
        file_size: 1024 * 1024 * 52, // 52MB
        description: '重要数据导入前备份',
        created_at: '2025-01-03 14:30:00'
      }
    ]
    
    updateStatistics()
  } finally {
    tableLoading.value = false
  }
}

// 更新统计数据
const updateStatistics = () => {
  statistics.totalBackups = backupList.value.length
  statistics.totalSize = backupList.value.reduce((sum: number, item: any) => sum + (item.file_size || 0), 0)
  statistics.manualBackups = backupList.value.filter((item: any) => item.backup_type === 'manual').length
  statistics.autoBackups = backupList.value.filter((item: any) => item.backup_type === 'auto').length
}

// 重置搜索
const resetSearch = () => {
  searchKeyword.value = ''
  fetchBackupHistory()
}

// 创建备份
const handleCreateBackup = () => {
  backupForm.description = `手动备份_${new Date().toISOString().split('T')[0]}`
  backupDialogVisible.value = true
}

// 确认创建备份
const confirmCreateBackup = async () => {
  if (!backupForm.description.trim()) {
    ElMessage.warning('请输入备份描述')
    return
  }
  
  backupLoading.value = true
  try {
    await dataBackupApi.createBackup(backupForm)
    ElMessage.success('数据备份成功')
    backupDialogVisible.value = false
    fetchBackupHistory()
  } catch (error) {
    console.error('数据备份失败:', error)
    ElMessage.error('数据备份失败')
  } finally {
    backupLoading.value = false
  }
}

// 上传备份
const handleUploadBackup = () => {
  uploadDialogVisible.value = true
}

// 处理文件选择
const handleFileChange = (file: any) => {
  uploadFile.value = file.raw
}

// 确认上传备份
const confirmUploadBackup = async () => {
  if (!uploadFile.value) {
    ElMessage.warning('请选择备份文件')
    return
  }
  
  uploadLoading.value = true
  try {
    await dataBackupApi.uploadBackup(uploadFile.value)
    ElMessage.success('备份文件上传成功')
    uploadDialogVisible.value = false
    fetchBackupHistory()
  } catch (error) {
    console.error('上传备份失败:', error)
    ElMessage.error('上传备份失败')
  } finally {
    uploadLoading.value = false
  }
}

// 恢复数据
const handleRestore = (backup: any) => {
  Object.assign(selectedBackup, backup)
  detailDialogVisible.value = false
  confirmText.value = ''
  restoreDialogVisible.value = true
}

// 确认恢复
const confirmRestore = async () => {
  if (confirmText.value !== 'CONFIRM') {
    ElMessage.warning('请输入CONFIRM以确认操作')
    return
  }
  
  restoreLoading.value = true
  try {
    await dataBackupApi.restoreData(selectedBackup.id)
    ElMessage.success('数据恢复成功')
    restoreDialogVisible.value = false
  } catch (error) {
    console.error('数据恢复失败:', error)
    ElMessage.error('数据恢复失败')
  } finally {
    restoreLoading.value = false
  }
}

// 下载备份
const handleDownload = async (backup: any) => {
  try {
    const blob = await dataBackupApi.downloadBackup(backup.id)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = backup.filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 查看详情
const handleViewDetail = (backup: any) => {
  Object.assign(selectedBackup, backup)
  detailDialogVisible.value = true
}

// 删除备份
const handleDelete = async (backup: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备份 "${backup.filename}" 吗？此操作不可恢复！`,
      '删除备份',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await dataBackupApi.deleteBackup(backup.id)
    ElMessage.success('删除备份成功')
    fetchBackupHistory()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除备份失败:', error)
      ElMessage.error('删除备份失败')
    }
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

onMounted(() => {
  fetchBackupHistory()
})
</script>

<style lang="scss" scoped>
.data-backup-restore {
  .header-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .mt-10 {
    margin-top: 10px;
  }
  
  .feature-card {
    height: 100%;
    
    .feature-header {
      display: flex;
      align-items: center;
      
      .icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }
    
    .feature-content {
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #409EFF;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
  
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
    }
    
    .filter-area {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
  
  .restore-confirm-content {
    .mb-20 {
      margin-bottom: 20px;
    }
  }
}
</style>
