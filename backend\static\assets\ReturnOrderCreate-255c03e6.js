import{J as e,b as r,r as a,L as t,e as o,f as l,o as d,c as n,a as i,i as u,h as s,F as c,m,k as _,t as p,U as f,g as y,l as v}from"./index-3d4c440c.js";import{b as g,c as h,e as b}from"./return-614d16fd.js";import{s as q}from"./customer-471ca075.js";import{f as w}from"./format-552375ee.js";import{_ as V}from"./_plugin-vue_export-helper-1b428a4d.js";const j={class:"return-order-create"},U={class:"option-info"},k={class:"option-info"},x={class:"form-footer"},D=V(e({__name:"ReturnOrderCreate",setup(e){const V=r(),D=a(),N=t({order_id:0,return_date:w(new Date),reason:"业主无理由退货",notes:"",items:[],customer_id:void 0,delivery_note_id:void 0}),$={delivery_note_id:[{required:!0,message:"请选择关联发货单",trigger:"change"}],return_date:[{required:!0,message:"请选择退货日期",trigger:"change"}],reason:[{required:!0,message:"请输入退货原因",trigger:"blur"}]},C=a(!1),A=a([]),I=a(!1),Y=a([]),R=async e=>{try{C.value=!0;const r=await g(e?String(e):void 0,"已完成");console.log("可退货发货单响应:",r);let a=[];if(r&&Array.isArray(r))a=r;else if(r&&r.list)a=r.list;else if(r&&r.data){const e=r.data;e&&e.list?a=e.list:Array.isArray(e)&&(a=e)}A.value=a,0===a.length?f.warning(e?"该客户没有可退货的发货单":"没有找到可退货的发货单"):console.log(`已加载 ${a.length} 个可退货发货单`)}catch(r){console.error("获取可退货发货单失败:",r),f.error("获取可退货发货单失败")}finally{C.value=!1}},S=async e=>{I.value=!0;try{const r=await q(e);console.log("搜索客户响应:",r),r&&r.list?Y.value=r.list:r&&r.data?Y.value=r.data:Array.isArray(r)?Y.value=r:Y.value=[]}catch(r){console.error("搜索客户失败:",r),f.error("搜索客户失败"),Y.value=[]}finally{I.value=!1}},M=async e=>{if(!e)return N.delivery_note_id=void 0,N.items=[],void R();N.delivery_note_id=void 0,N.items=[],R(e)},F=async()=>{if(N.delivery_note_id){N.items=[];try{const e=A.value.find((e=>String(e.id)===String(N.delivery_note_id)));if(!e)return;if(e.order&&e.order.customer){if(N.customer_id=e.order.customer.id,e.order.customer.name){const r={id:e.order.customer.id,name:e.order.customer.name,contact:e.order.customer.contact||""};Y.value.some((e=>e.id===r.id))||Y.value.push(r)}console.log(`自动设置客户ID: ${N.customer_id}`)}if(e.order_id&&(N.order_id=Number(e.order_id),console.log(`关联订单ID: ${N.order_id}`)),e.items&&e.items.length>0){const r=e.items.map((e=>{const r=e,a=Number(e.quantity)||0;return{order_product_id:Number(e.order_product_id),product_name:e.product_name,product_model:e.product_model||"",specification:r.specification_description||e.specification||"",product_unit:e.unit||"",quantity:1,shipped_quantity:a,order_quantity:null,returned_quantity:0,notes:"",reason:N.reason}})).map((async e=>{try{if(e.order_product_id){const a=await h(e.order_product_id);console.log(`商品ID ${e.order_product_id} 已退货数量响应:`,a);let t=0;try{if(a&&"object"==typeof a&&"data"in a){const e=a.data;if(e&&"object"==typeof e){const r=e.quantity;void 0!==r&&(t=Number(r))}else"number"==typeof e&&(t=e)}}catch(r){console.error("处理已退货数量响应出错:",r)}return{...e,returned_quantity:t}}return e}catch(a){return console.error(`获取商品 ${e.order_product_id} 已退货数量失败:`,a),e}}));N.items=await Promise.all(r),console.log("已加载发货单商品(包含已退货数量):",N.items)}else f.warning("选择的发货单没有商品")}catch(e){console.error("处理发货单商品失败:",e),f.error("处理发货单商品失败")}}else N.items=[]};o((()=>{R()}));const J=a(!1),L=async()=>{if(D.value)try{await D.value.validate();const e=N.items.filter((e=>e.quantity>0));if(0===e.length)return void f.warning("请至少选择一个退货商品并设置大于0的退货数量");J.value=!0;const r=Number(N.order_id);if(!r||isNaN(r))return f.error("无效的订单ID"),void(J.value=!1);const a={order_id:r,return_date:N.return_date,reason:N.reason,notes:N.notes||"",items:e.map((e=>({order_product_id:Number(e.order_product_id),quantity:e.quantity,reason:e.reason||N.reason,notes:e.notes||""})))};console.log("提交退货单数据:",a);const t=await b(a);console.log("退货单创建响应:",t),f.success("退货单创建成功"),V.push("/return-orders")}catch(e){console.error("创建退货单失败:",e),f.error("object"==typeof e&&null!==e&&"message"in e?`创建退货单失败: ${e.message}`:"创建退货单失败")}finally{J.value=!1}},O=()=>{V.back()};return(e,r)=>{const a=l("el-option"),t=l("el-select"),o=l("el-form-item"),g=l("el-date-picker"),h=l("el-input"),b=l("el-card"),q=l("el-table-column"),V=l("el-input-number"),R=l("el-table"),P=l("el-button"),W=l("el-form");return d(),n("div",j,[r[9]||(r[9]=i("div",{class:"page-header"},[i("h2",null,"新建退货单")],-1)),u(W,{ref_key:"formRef",ref:D,model:N,rules:$,"label-width":"120px",class:"create-form"},{default:s((()=>[u(b,{class:"form-card"},{header:s((()=>r[5]||(r[5]=[i("div",{class:"card-header"},[i("span",null,"基本信息")],-1)]))),default:s((()=>[u(o,{label:"客户",prop:"customer_id"},{default:s((()=>[u(t,{modelValue:N.customer_id,"onUpdate:modelValue":r[0]||(r[0]=e=>N.customer_id=e),placeholder:"请选择客户",filterable:"",remote:"","remote-method":S,loading:I.value,onChange:M,clearable:""},{default:s((()=>[(d(!0),n(c,null,m(Y.value,(e=>(d(),y(a,{key:e.id,label:e.name,value:e.id},{default:s((()=>[i("span",null,p(e.name),1),i("span",U,"("+p(e.contact||"无联系人")+")",1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),u(o,{label:"关联发货单",prop:"delivery_note_id"},{default:s((()=>[u(t,{modelValue:N.delivery_note_id,"onUpdate:modelValue":r[1]||(r[1]=e=>N.delivery_note_id=e),placeholder:"请选择发货单",loading:C.value,onChange:F,clearable:""},{default:s((()=>[(d(!0),n(c,null,m(A.value,(e=>(d(),y(a,{key:e.id,label:e.delivery_number,value:e.id},{default:s((()=>{return[i("span",null,p(e.delivery_number),1),i("span",k," ("+p(v(w)(e.delivery_date))+" | "+p(e.status)+" | "+p((r=e,r.order&&r.order.project_name?r.order.project_name:r.order_number?`订单: ${r.order_number}`:r.order&&r.order.customer&&r.order.customer.name?r.order.customer.name:"无项目信息"))+") ",1)];var r})),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),u(o,{label:"退货日期",prop:"return_date"},{default:s((()=>[u(g,{modelValue:N.return_date,"onUpdate:modelValue":r[2]||(r[2]=e=>N.return_date=e),type:"date",placeholder:"请选择退货日期","value-format":"YYYY-MM-DD","default-value":new Date},null,8,["modelValue","default-value"])])),_:1}),u(o,{label:"退货原因",prop:"reason"},{default:s((()=>[u(h,{modelValue:N.reason,"onUpdate:modelValue":r[3]||(r[3]=e=>N.reason=e),type:"textarea",rows:3,placeholder:"请输入退货原因"},null,8,["modelValue"])])),_:1}),u(o,{label:"备注",prop:"notes"},{default:s((()=>[u(h,{modelValue:N.notes,"onUpdate:modelValue":r[4]||(r[4]=e=>N.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1}),u(b,{class:"form-card"},{header:s((()=>r[6]||(r[6]=[i("div",{class:"card-header"},[i("span",null,"退货商品")],-1)]))),default:s((()=>[u(R,{data:N.items,border:""},{default:s((()=>[u(q,{label:"商品名称","min-width":"200"},{default:s((({row:e})=>[_(p(e.product_name),1)])),_:1}),u(q,{label:"规格","min-width":"150"},{default:s((({row:e})=>[_(p(e.specification||"无规格"),1)])),_:1}),u(q,{label:"发货数量",width:"100"},{default:s((({row:e})=>[_(p(e.shipped_quantity||e.order_quantity||0),1)])),_:1}),u(q,{label:"已退数量",width:"100"},{default:s((({row:e})=>[_(p(e.returned_quantity||0),1)])),_:1}),u(q,{label:"可退数量",width:"100"},{default:s((({row:e})=>[_(p((e.shipped_quantity||e.order_quantity||0)-(e.returned_quantity||0)),1)])),_:1}),u(q,{label:"退货数量",width:"150"},{default:s((({row:e})=>[u(V,{modelValue:e.quantity,"onUpdate:modelValue":r=>e.quantity=r,min:0,max:(e.shipped_quantity||e.order_quantity||0)-(e.returned_quantity||0),onChange:r=>(e=>{const r=(e.shipped_quantity||0)-(e.returned_quantity||0);e.quantity<0?(e.quantity=0,f.warning("退货数量不能为负数")):e.quantity>r&&(e.quantity=r,f.warning(`退货数量不能超过可退数量（${r}）`))})(e)},null,8,["modelValue","onUpdate:modelValue","max","onChange"])])),_:1}),u(q,{label:"备注","min-width":"150"},{default:s((({row:e})=>[u(h,{modelValue:e.notes,"onUpdate:modelValue":r=>e.notes=r,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"])])),_:1}),i("div",x,[u(P,{onClick:O},{default:s((()=>r[7]||(r[7]=[_("取消")]))),_:1}),u(P,{type:"primary",loading:J.value,onClick:L},{default:s((()=>r[8]||(r[8]=[_("提交")]))),_:1},8,["loading"])])])),_:1},8,["model"])])}}}),[["__scopeId","data-v-c18ea2bc"]]);export{D as default};
