<template>
  <el-dialog
    v-model="visible"
    title="对账单收款"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <!-- 对账单信息 -->
    <div class="statement-info" v-if="statementInfo">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="对账单号">
          {{ statementInfo.statement_number }}
        </el-descriptions-item>
        <el-descriptions-item label="客户名称">
          {{ statementInfo.customer_name }}
        </el-descriptions-item>
        <el-descriptions-item label="对账日期">
          {{ statementInfo.statement_date }}
        </el-descriptions-item>
        <el-descriptions-item label="应付款日期">
          {{ statementInfo.due_date || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="原始金额">
          <span class="amount-text">{{ formatAmount(getOriginalAmount()) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="优惠金额">
          <span class="amount-text discount">{{ formatAmount(statementInfo.discount_amount || 0) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="实际金额">
          <span class="amount-text actual">{{ formatAmount(getActualAmount()) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已收金额">
          <span class="amount-text paid">{{ formatAmount(statementInfo.paid_amount || 0) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="未收金额" :span="2">
          <span class="amount-text unpaid">{{ formatAmount(unpaidAmount) }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 收款表单 -->
    <el-form
      ref="paymentFormRef"
      :model="paymentForm"
      :rules="paymentRules"
      label-width="100px"
      class="payment-form"
    >
      <el-form-item label="收款金额" prop="amount" required>
        <el-input-number
          v-model="paymentForm.amount"
          :min="0.01"
          :max="Math.max(0.01, unpaidAmount)"
          :precision="2"
          :disabled="unpaidAmount <= 0"
          placeholder="请输入收款金额"
          style="width: 100%"
        />
        <div class="amount-tips">
          <span>最大可收款金额：{{ formatAmount(unpaidAmount) }}</span>
          <el-button 
            type="primary" 
            link 
            size="small" 
            @click="setFullAmount"
          >
            全额收款
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="支付方式" prop="payment_method" required>
        <el-radio-group v-model="paymentForm.payment_method">
          <el-radio value="balance">账户余额</el-radio>
          <el-radio value="other">其他方式</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 其他支付方式详细信息 -->
      <template v-if="paymentForm.payment_method === 'other'">
        <el-form-item label="支付方式" prop="payment_type" required>
          <el-select 
            v-model="paymentForm.payment_type" 
            placeholder="请选择支付方式"
            style="width: 100%"
          >
            <el-option 
              v-for="method in paymentMethods" 
              :key="method.value" 
              :label="method.label" 
              :value="method.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="交易流水号" prop="reference_number">
          <el-input
            v-model="paymentForm.reference_number"
            placeholder="请输入交易流水号"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item label="收款账户" prop="bank_account">
          <el-input
            v-model="paymentForm.bank_account"
            placeholder="请输入收款账户信息"
            maxlength="200"
          />
        </el-form-item>
      </template>

      <el-form-item label="收款日期" prop="payment_date" required>
        <el-date-picker
          v-model="paymentForm.payment_date"
          type="date"
          placeholder="请选择收款日期"
          style="width: 100%"
          :disabled-date="disabledDate"
        />
      </el-form-item>

      <el-form-item label="收款凭证" prop="voucher">
        <el-upload
          ref="uploadRef"
          :file-list="fileList"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          :auto-upload="false"
          accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
          multiple
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 jpg/png/pdf/doc/docx 格式，单个文件不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="paymentForm.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入收款备注信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          确认收款
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile, UploadFiles } from 'element-plus'
import { statementApi } from '@/api/statement'

// Props
interface Props {
  modelValue: boolean
  statementId?: number
  statementInfo?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  statementId: undefined,
  statementInfo: undefined
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const paymentFormRef = ref<FormInstance>()
const uploadRef = ref()
const submitting = ref(false)
const fileList = ref<UploadFile[]>([])

// 支付方式选项（后续可以从系统设置中获取）
const paymentMethods = ref([
  { label: '银行转账', value: 'bank_transfer' },
  { label: '现金支付', value: 'cash' },
  { label: '微信', value: 'wechat' },
  { label: '支付宝', value: 'alipay' },
  { label: '其他', value: 'other' }
])

// 收款表单
const paymentForm = reactive({
  amount: 0.01, // 设置最小值避免ElInputNumber错误
  payment_method: 'other', // balance | other
  payment_type: '', // 具体的支付方式
  reference_number: '',
  bank_account: '',
  payment_date: new Date(),
  notes: '',
  voucher_files: [] as File[]
})

// 表单验证规则
const paymentRules: FormRules = {
  amount: [
    { required: true, message: '请输入收款金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '收款金额必须大于0', trigger: 'blur' }
  ],
  payment_method: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  payment_type: [
    { required: true, message: '请选择具体支付方式', trigger: 'change' }
  ],
  payment_date: [
    { required: true, message: '请选择收款日期', trigger: 'change' }
  ]
}

// 计算属性
const unpaidAmount = computed(() => {
  if (!props.statementInfo) return 0
  const actual = getActualAmount()
  const paid = Number(props.statementInfo.paid_amount || 0)
  const unpaid = actual - paid
  // 确保返回值始终为正数，如果是负数或退货单，则返回0
  return Math.max(0, unpaid)
})

// 方法
const formatAmount = (amount: number | string) => {
  const num = Number(amount || 0)
  return `¥${num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 获取原始金额（兼容不同的数据结构）
const getOriginalAmount = () => {
  if (!props.statementInfo) return 0
  // 如果有 original_amount 字段，使用它
  if (props.statementInfo.original_amount !== undefined) {
    return Number(props.statementInfo.original_amount || 0)
  }
  // 如果有 adjusted_total_amount 字段，说明是详情页数据
  if (props.statementInfo.adjusted_total_amount !== undefined) {
    return Number(props.statementInfo.total_amount || 0)
  }
  // 否则计算：实际金额 + 优惠金额
  const actual = Number(props.statementInfo.total_amount || 0)
  const discount = Number(props.statementInfo.discount_amount || 0)
  return actual + discount
}

// 获取实际金额（兼容不同的数据结构）
const getActualAmount = () => {
  if (!props.statementInfo) return 0
  // 如果有 adjusted_total_amount 字段，说明是详情页数据
  if (props.statementInfo.adjusted_total_amount !== undefined) {
    return Number(props.statementInfo.adjusted_total_amount || 0)
  }
  // 否则使用 total_amount（列表页数据，已经是实际金额）
  return Number(props.statementInfo.total_amount || 0)
}

const setFullAmount = () => {
  if (unpaidAmount.value > 0) {
    paymentForm.amount = unpaidAmount.value
  }
}

const disabledDate = (time: Date) => {
  // 不能选择未来的日期
  return time.getTime() > Date.now()
}

const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  // 更新文件列表
  paymentForm.voucher_files = files.map(f => f.raw).filter(Boolean) as File[]
}

const handleFileRemove = (file: UploadFile, files: UploadFiles) => {
  // 更新文件列表
  paymentForm.voucher_files = files.map(f => f.raw).filter(Boolean) as File[]
}

const beforeUpload = (file: File) => {
  // 文件大小限制
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return false // 阻止自动上传
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  paymentForm.amount = 0.01 // 设置最小值避免ElInputNumber错误
  paymentForm.payment_method = 'other'
  paymentForm.payment_type = ''
  paymentForm.reference_number = ''
  paymentForm.bank_account = ''
  paymentForm.payment_date = new Date()
  paymentForm.notes = ''
  paymentForm.voucher_files = []
  fileList.value = []

  if (paymentFormRef.value) {
    paymentFormRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  if (!paymentFormRef.value) return
  
  try {
    // 表单验证
    await paymentFormRef.value.validate()
    
    // 确认收款
    await ElMessageBox.confirm(
      `确认收款 ${formatAmount(paymentForm.amount)} 吗？`,
      '确认收款',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    submitting.value = true
    
    // 构建提交数据 - 使用FormData来支持文件上传
    const formData = new FormData()
    formData.append('statement_id', props.statementId.toString())
    formData.append('amount', paymentForm.amount.toString())
    formData.append('payment_method', paymentForm.payment_method === 'balance' ? 'balance' : paymentForm.payment_type)
    formData.append('payment_source', paymentForm.payment_method === 'balance' ? 'balance' : 'direct')
    formData.append('reference_number', paymentForm.reference_number || '')
    formData.append('bank_account', paymentForm.bank_account || '')
    formData.append('payment_date', paymentForm.payment_date instanceof Date
      ? paymentForm.payment_date.toISOString().split('T')[0]
      : paymentForm.payment_date)
    formData.append('notes', paymentForm.notes || '')

    // 添加文件
    paymentForm.voucher_files.forEach((file, index) => {
      formData.append(`voucher_file_${index}`, file)
    })
    formData.append('voucher_files_count', paymentForm.voucher_files.length.toString())

    // 调用收款API
    console.log('收款数据:', formData)

    const response = await statementApi.createDirectPaymentWithFiles(formData)

    // 由于HTTP拦截器的处理，成功的响应直接返回data部分
    if (response && response.payment_id) {
      ElMessage.success('收款成功')
      emit('success', {
        statement_id: props.statementId,
        amount: paymentForm.amount,
        payment_method: paymentForm.payment_method === 'balance' ? 'balance' : paymentForm.payment_type,
        payment_source: paymentForm.payment_method === 'balance' ? 'balance' : 'direct',
        payment_id: response.payment_id,
        statement_status: response.statement_status
      })
      handleClose()
    } else {
      throw new Error('收款失败：响应数据格式错误')
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('收款失败:', error)
      ElMessage.error('收款失败')
    }
  } finally {
    submitting.value = false
  }
}

// 监听支付方式变化，重置相关字段
watch(() => paymentForm.payment_method, (newValue) => {
  if (newValue === 'balance') {
    paymentForm.payment_type = ''
    paymentForm.reference_number = ''
    paymentForm.bank_account = ''
  }
})
</script>

<style lang="scss" scoped>
.statement-info {
  margin-bottom: 20px;
  
  .amount-text {
    font-weight: bold;

    &.discount {
      color: var(--el-color-info);
    }

    &.actual {
      color: var(--el-color-primary);
      font-size: 16px;
    }

    &.paid {
      color: var(--el-color-success);
    }

    &.unpaid {
      color: var(--el-color-warning);
      font-size: 16px;
    }
  }
}

.payment-form {
  .amount-tips {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-upload-dragger) {
  width: 100%;
}
</style>
