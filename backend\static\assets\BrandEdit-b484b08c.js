import{J as e,u as a,b as l,d as o,r as s,L as r,e as t,U as d,f as i,M as n,o as u,c,i as m,h as p,a as g,t as _,k as f,l as b,N as v,ak as h}from"./index-3d4c440c.js";import{a as w,b as y,c as V,u as x}from"./brand-81076433.js";import{_ as U}from"./_plugin-vue_export-helper-1b428a4d.js";const k={class:"brand-edit"},j={class:"card-header"},B={class:"left"},I={class:"page-title"},L={class:"right"},F={class:"form-container"},P={class:"form-actions"},R=U(e({__name:"BrandEdit",setup(e){const U=a(),R=l(),q=o((()=>!U.params.id)),A=s(!1),C=s(!1),E=s(),N=r({id:void 0,name:"",logo_url:null,description:"",website:"",sort_order:99}),D={name:[{required:!0,message:"请输入品牌名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],description:[{max:500,message:"不能超过500个字符",trigger:"blur"}],website:[{required:!1}],sort_order:[{type:"number",message:"排序必须为数字"}],logo_url:[{required:!1}]};t((async()=>{if(!q.value&&U.params.id)J(Number(U.params.id));else{A.value=!0;try{const e=await w();console.log("获取到的最大排序值:",e),N.sort_order=e+1}catch(e){console.error("获取最大排序值失败:",e)}finally{A.value=!1}}}));const J=async e=>{A.value=!0;try{const a=await y(e);console.log("获取到的品牌数据原始响应:",a);let l=a;if(a&&"object"==typeof a){const e=a;void 0!==e.code&&e.data?(console.log("使用标准API响应中的data字段"),l=e.data):a.id?(console.log("响应直接是数据对象"),l=a):(console.warn("无法识别的API响应格式",a),d.warning("API响应格式不符合预期，数据可能不完整"))}if(!l||!l.id)throw new Error("获取的品牌数据无效或不完整");console.log("处理后的品牌数据:",l),Object.assign(N,{id:l.id,name:l.name,logo_url:l.logo_url,description:l.description,website:l.website,sort_order:l.sort_order})}catch(a){console.error("Failed to fetch brand data:",a),d.error(a.message||"获取品牌信息失败")}finally{A.value=!1}},M=async()=>{E.value&&await E.value.validate((async e=>{if(e){C.value=!0;try{let e=N.website;e&&!e.match(/^https?:\/\//)&&(e="http://"+e);let a=N.logo_url;a&&!a.match(/^https?:\/\//)&&(a="http://"+a);const l={name:N.name,logo_url:a||null,description:N.description||null,website:e||null,sort_order:N.sort_order};if(console.log("--- Debug Save Brand ---"),console.log("isNew.value:",q.value),console.log("brandForm.id:",N.id),console.log("Payload:",l),q.value)await V(l),d.success("品牌创建成功");else{if(!N.id)throw new Error("无法更新品牌：缺少品牌 ID");await x(N.id,l),d.success("品牌更新成功")}R.push({name:"Brands"})}catch(a){a.message&&"string"==typeof a.message&&(a.message.includes("成功")||a.message.includes("已完成"))?(d.success(a.message),R.push({name:"Brands"})):(console.error("Failed to save brand:",a),d.error(a.message||"保存品牌失败"))}finally{C.value=!1}}else d.error("请检查表单填写是否正确")}))},O=()=>{R.push({name:"Brands"})};return(e,a)=>{const l=i("el-tag"),o=i("el-icon"),s=i("el-button"),r=i("el-card"),t=i("el-input"),d=i("el-form-item"),w=i("el-input-number"),y=i("el-form"),V=n("loading");return u(),c("div",k,[m(r,{class:"header-card mb-20"},{default:p((()=>[g("div",j,[g("div",B,[g("h2",I,_(q.value?"新增品牌":"编辑品牌"),1),m(l,{type:"info"},{default:p((()=>[f(_(q.value?"创建新的品牌":"修改品牌信息"),1)])),_:1})]),g("div",L,[m(s,{onClick:O},{default:p((()=>[m(o,null,{default:p((()=>[m(b(h))])),_:1}),a[5]||(a[5]=f(" 返回列表 "))])),_:1})])])])),_:1}),m(r,null,{default:p((()=>[v((u(),c("div",F,[m(y,{ref_key:"brandFormRef",ref:E,model:N,rules:D,"label-width":"100px","label-position":"right",class:"brand-form"},{default:p((()=>[m(d,{label:"品牌名称",prop:"name"},{default:p((()=>[m(t,{modelValue:N.name,"onUpdate:modelValue":a[0]||(a[0]=e=>N.name=e),placeholder:"请输入品牌名称"},null,8,["modelValue"])])),_:1}),m(d,{label:"品牌描述",prop:"description"},{default:p((()=>[m(t,{modelValue:N.description,"onUpdate:modelValue":a[1]||(a[1]=e=>N.description=e),type:"textarea",rows:3,placeholder:"请输入品牌描述"},null,8,["modelValue"])])),_:1}),m(d,{label:"官网",prop:"website"},{default:p((()=>[m(t,{modelValue:N.website,"onUpdate:modelValue":a[2]||(a[2]=e=>N.website=e),placeholder:"例如：www.example.com"},{prepend:p((()=>a[6]||(a[6]=[f("http(s)://")]))),_:1},8,["modelValue"]),a[7]||(a[7]=g("div",{class:"el-upload__tip"}," 请输入网址，如果没有添加http://前缀，系统会自动添加 ",-1))])),_:1}),m(d,{label:"排序",prop:"sort_order"},{default:p((()=>[m(w,{modelValue:N.sort_order,"onUpdate:modelValue":a[3]||(a[3]=e=>N.sort_order=e),min:0,max:999},null,8,["modelValue"])])),_:1}),m(d,{label:"品牌标志",prop:"logo_url"},{default:p((()=>[m(t,{modelValue:N.logo_url,"onUpdate:modelValue":a[4]||(a[4]=e=>N.logo_url=e),placeholder:"请输入 Logo URL (暂不支持上传)"},null,8,["modelValue"]),a[8]||(a[8]=g("div",{class:"el-upload__tip"}," 请输入 Logo 图片的 URL 地址。如果没有添加http://前缀，系统会自动添加 ",-1))])),_:1}),g("div",P,[m(s,{onClick:O},{default:p((()=>a[9]||(a[9]=[f("取消")]))),_:1}),m(s,{type:"primary",onClick:M,loading:C.value},{default:p((()=>a[10]||(a[10]=[f("保存")]))),_:1},8,["loading"])])])),_:1},8,["model"])])),[[V,A.value]])])),_:1})])}}}),[["__scopeId","data-v-5f9f4644"]]);export{R as default};
