"""
订单管理API
提供订单和退货单的CRUD操作，支持基于报价单创建订单、复杂状态流转和业务流程管理

重构说明：
- 已重构为双状态系统：order_status (物流状态) + payment_status (财务状态)
- 保留了status字段的兼容性支持，但建议使用新的双状态字段
- 移除了对账相关状态，对账功能由独立的对账单模块管理
- 状态流转规则已优化：部分发货后不可回退，不可取消

API端点：
- PUT /orders/{id}/status - 兼容性状态更新（映射到order_status）
- PUT /orders/{id}/order-status - 物流状态更新
- PUT /orders/{id}/payment-status - 财务状态更新（通常自动计算）
"""
from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from sqlalchemy import or_, desc, func
from sqlalchemy.orm import selectinload, joinedload
from typing import Dict, List, Optional, Any, Tuple
import uuid
from datetime import datetime, date
from decimal import Decimal
import os
from io import BytesIO

from app.models.order import Order, OrderProduct, OrderStatusHistory
from app.models.return_order import ReturnOrder, ReturnOrderItem
from app.models.customer import Customer
from app.models.quotation import Quotation
from app.models.quotation import Quotation, QuotationItem
from app.models.product import Product, ProductSpecification
from app.schemas.order import (
    OrderSchema, OrderSimpleSchema, OrderProductSchema, OrderStatusHistorySchema,
    ReturnOrderSchema, ReturnOrderSimpleSchema, ReturnOrderItemSchema
)
from app.utils.response import (
    success_response,
    error_response,
    paginated_response,
    validation_error_response,
    not_found_response
)
from app.utils.pagination import PaginationHelper
from app.utils.exceptions import ValidationError as CustomValidationError, NotFoundError
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('orders', description='订单管理API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 自动从Marshmallow Schema生成API模型
order_model = create_input_model(api, OrderSchema, 'OrderInput')
order_product_model = create_input_model(api, OrderProductSchema, 'OrderProductInput')
return_order_model = create_input_model(api, ReturnOrderSchema, 'ReturnOrderInput')


# 订单相关API
@api.route('')
class OrderList(Resource):
    @api.doc('get_orders')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('project_name', '项目名称搜索')
    @api.param('status', '订单状态')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取订单列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            customer_id = request.args.get('customer_id', type=int)
            project_name = request.args.get('project_name', '')
            # 双状态筛选参数
            order_status = request.args.get('order_status', '')
            payment_status = request.args.get('payment_status', '')
            # 兼容性参数（映射到order_status）
            status = request.args.get('status', '')
            start_date = request.args.get('start_date', '')
            end_date = request.args.get('end_date', '')

            query = Order.query.options(joinedload(Order.customer))

            # 应用过滤条件
            if customer_id:
                query = query.filter(Order.customer_id == customer_id)
            if project_name:
                query = query.filter(Order.project_name.ilike(f'%{project_name}%'))

            # 状态筛选：优先使用新的双状态字段
            if order_status:
                query = query.filter(Order.order_status == order_status)
            elif status:  # 兼容性：将status映射到order_status
                query = query.filter(Order.order_status == status)

            if payment_status:
                query = query.filter(Order.payment_status == payment_status)
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(Order.created_at >= start)
                except ValueError:
                    return error_response("开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            if end_date:
                try:
                    end = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(Order.created_at <= end)
                except ValueError:
                    return error_response("结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            # 排序
            query = query.order_by(Order.created_at.desc())
            
            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query,
                serializer_func=lambda item: OrderSimpleSchema().dump(item),
                page=page,
                per_page=per_page,
                message="获取订单列表成功"
            )
            
        except Exception as e:
            current_app.logger.error(f"获取订单列表失败: {str(e)}")
            return make_response(error_response, f"获取订单列表失败: {str(e)}")

    @api.doc('create_order',
             body=order_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建订单"""
        try:
            data = request.get_json() or {}
            products_data = data.pop('products', [])
            
            # 数据验证
            order_schema = OrderSchema(exclude=(
                'id', 'order_number', 'total_amount', 'paid_amount', 'customer', 'quotations',
                'products', 'status_history', 'created_at', 'updated_at'
            ))
            validated_data = order_schema.load(data)

            # 检查客户是否存在
            customer = Customer.query.get(validated_data['customer_id'])
            if not customer:
                return make_response(error_response, "客户不存在", errors={"customer_id": "指定的客户不存在"})

            # 检查报价单是否存在（如果提供了）
            if validated_data.get('quotation_ids'):
                import json
                try:
                    # 如果是字符串，解析为列表
                    if isinstance(validated_data['quotation_ids'], str):
                        quotation_ids = json.loads(validated_data['quotation_ids'])
                    else:
                        quotation_ids = validated_data['quotation_ids']

                    # 验证每个报价单是否存在且未被关联
                    for quotation_id in quotation_ids:
                        quotation = Quotation.query.get(quotation_id)
                        if not quotation:
                            return make_response(error_response, f"报价单ID {quotation_id} 不存在")

                        # 检查报价单是否已被其他订单关联
                        existing_order = Order.query.filter(
                            Order.quotation_ids.like(f'%{quotation_id}%')
                        ).first()

                        if existing_order:
                            return make_response(error_response, f"报价单 {quotation.quotation_number} 已被订单 {existing_order.order_number} 关联，不能重复使用")

                    # 将处理后的quotation_ids重新设置为JSON字符串
                    validated_data['quotation_ids'] = json.dumps(quotation_ids)

                except (json.JSONDecodeError, TypeError) as e:
                    return make_response(error_response, f"报价单ID格式错误: {str(e)}")

            # 生成订单编号
            order_number = f"ORD{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"
            validated_data['order_number'] = order_number

            # 创建订单对象
            order = Order(**validated_data)
            db.session.add(order)
            db.session.flush()  # 获取ID

            # 创建订单产品
            if products_data:
                product_schema = OrderProductSchema(many=True, exclude=(
                    'id', 'order_id', 'total_price', 'delivered_quantity', 'product_name', 'product_model',
                    'product_unit', 'specification_description', 'product', 'product_specification',
                    'created_at', 'updated_at'
                ))
                validated_products = product_schema.load(products_data)
                
                for product_data in validated_products:
                    # 检查产品规格是否存在
                    spec = ProductSpecification.query.get(product_data['product_specification_id'])
                    if not spec:
                        current_app.logger.warning(f"规格ID {product_data['product_specification_id']} 不存在，跳过此项目")
                        continue

                    # 通过规格获取产品信息
                    product = spec.product
                    if not product:
                        current_app.logger.warning(f"规格ID {product_data['product_specification_id']} 对应的产品不存在，跳过此项目")
                        continue

                    # 创建订单产品
                    order_product = OrderProduct(
                        order_id=order.id,
                        product_specification_id=product_data['product_specification_id'],
                        quantity=product_data['quantity'],
                        unit_price=product_data['unit_price'],
                        # 税率和折扣信息
                        tax_rate=product_data.get('tax_rate', 13.0),
                        discount=product_data.get('discount', 0.0),
                        # 产品来源信息
                        source_type=product_data.get('source_type', 'manual'),
                        source_id=product_data.get('source_id'),
                        source_display=product_data.get('source_display')
                    )
                    
                    # 填充快照字段
                    order_product.product_name = product.name
                    order_product.product_model = product.model
                    order_product.product_unit = product.unit
                    order_product.specification_description = spec.specification
                    
                    # 计算总价
                    order_product.total_price = order_product.calculate_total_price()
                    db.session.add(order_product)

            db.session.flush()
            
            # 更新订单总金额
            order.total_amount = order.calculate_total_amount()
            
            # 创建状态历史记录
            status_history = OrderStatusHistory(
                order_id=order.id,
                status=f"物流:{order.order_status}, 财务:{order.payment_status}",
                comment="订单创建"
            )
            db.session.add(status_history)
            
            db.session.commit()

            # 返回创建的订单信息
            created_order = Order.query.get(order.id)
            response_data = OrderSchema().dump(created_order)
            return make_response(success_response, response_data, "订单创建成功", 201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建订单失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建订单失败: {str(e)}")


@api.route('/<int:order_id>')
class OrderDetail(Resource):
    @api.doc('get_order')
    @api.param('exclude_delivery_note_id', '排除的发货单ID（用于编辑发货单时排除当前发货单的贡献）', type='integer')
    def get(self, order_id):
        """获取订单详情"""
        try:
            # 获取查询参数
            exclude_delivery_note_id = request.args.get('exclude_delivery_note_id', type=int)

            # 使用joinedload预加载关联数据
            from sqlalchemy.orm import joinedload
            order = Order.query.options(
                joinedload(Order.customer),
                joinedload(Order.products).joinedload(OrderProduct.product_specification).joinedload('product'),
                joinedload(Order.return_orders)
            ).get(order_id)

            if not order:
                return make_response(not_found_response, "订单不存在")

            # 如果需要排除特定发货单，重新计算已发货数量
            if exclude_delivery_note_id:
                from ...models.order import DeliveryNoteItem
                for order_product in order.products:
                    # 计算排除指定发货单后的已发货数量
                    excluded_quantity = db.session.query(func.sum(DeliveryNoteItem.quantity)).filter(
                        DeliveryNoteItem.order_product_id == order_product.id,
                        DeliveryNoteItem.delivery_note_id != exclude_delivery_note_id
                    ).scalar() or 0

                    # 临时设置已发货数量（不保存到数据库）
                    order_product.delivered_quantity = excluded_quantity

            # 调试：打印产品来源信息
            current_app.logger.info(f"订单 {order_id} 产品来源信息调试:")
            for product in order.products:
                current_app.logger.info(f"  产品ID {product.id}: source_type={product.source_type}, source_id={product.source_id}, source_display={product.source_display}")

            order_data = OrderSchema().dump(order)

            # 调试：打印序列化后的产品来源信息
            current_app.logger.info(f"序列化后的产品来源信息:")
            for product in order_data.get('products', []):
                current_app.logger.info(f"  产品ID {product.get('id')}: source_type={product.get('source_type')}, source_id={product.get('source_id')}, source_display={product.get('source_display')}")

            return make_response(success_response, order_data, "获取订单详情成功")

        except Exception as e:
            current_app.logger.error(f"获取订单详情失败: {str(e)}")
            return make_response(error_response, f"获取订单详情失败: {str(e)}")

    @api.doc('update_order',
             body=order_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Order Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, order_id):
        """更新订单"""
        try:
            print(f"🔧 订单更新开始 - 订单ID: {order_id}")

            order = Order.query.get(order_id)
            if not order:
                return make_response(not_found_response, "订单不存在")

            # 检查是否可以编辑
            if order.order_status not in ['待确认', '已确认']:
                return make_response(error_response, "当前状态的订单不可编辑")

            data = request.get_json() or {}
            print(f"🔧 原始请求数据: {data}")

            products_data = data.pop('products', None)
            print(f"🔧 产品数据: {products_data is not None}")
            print(f"🔧 quotation_ids在原始数据中: {'quotation_ids' in data}")
            if 'quotation_ids' in data:
                print(f"🔧 原始quotation_ids值: {repr(data['quotation_ids'])}")
                print(f"🔧 原始quotation_ids类型: {type(data['quotation_ids'])}")

            # 验证主表数据
            order_schema = OrderSchema(exclude=(
                'id', 'order_number', 'total_amount', 'paid_amount', 'customer', 'quotations',
                'products', 'status_history', 'created_at', 'updated_at'
            ))
            validated_data = order_schema.load(data, partial=True)

            print(f"🔧 验证后的数据: {validated_data}")
            print(f"🔧 quotation_ids在验证数据中: {'quotation_ids' in validated_data}")
            if 'quotation_ids' in validated_data:
                print(f"🔧 验证后quotation_ids值: {repr(validated_data['quotation_ids'])}")
                print(f"🔧 验证后quotation_ids类型: {type(validated_data['quotation_ids'])}")

            # 检查客户是否存在
            if 'customer_id' in validated_data:
                customer = Customer.query.get(validated_data['customer_id'])
                if not customer:
                    return make_response(error_response, "客户不存在", errors={"customer_id": "指定的客户不存在"})
            else:
                customer = order.customer

            # 检查客户是否有送货地址（保存时必须有）
            if not customer.get_default_delivery_address():
                return make_response(error_response, "客户没有设置送货地址，请先为客户添加送货地址后再保存订单")

            # 处理报价单关联状态的变更
            print(f"🔧 开始处理订单 {order.order_number} 的报价单关联状态变更")
            current_app.logger.info(f"开始处理订单 {order.order_number} 的报价单关联状态变更")

            old_quotation_ids = []
            if order.quotation_ids:
                try:
                    import json
                    old_quotation_ids = json.loads(order.quotation_ids)
                    current_app.logger.info(f"原有报价单ID列表: {old_quotation_ids}")
                except:
                    old_quotation_ids = []

            # 获取新的报价单ID列表（从请求数据中）
            new_quotation_ids = []
            if 'quotation_ids' in validated_data and validated_data['quotation_ids']:
                try:
                    import json
                    new_quotation_ids = json.loads(validated_data['quotation_ids'])
                    current_app.logger.info(f"新的报价单ID列表: {new_quotation_ids}")
                except:
                    new_quotation_ids = []
            else:
                current_app.logger.info("新的报价单ID列表为空")

            # 更新主表字段
            for key, value in validated_data.items():
                setattr(order, key, value)

            # 处理被移除的报价单（不需要修改状态，保持已确认）
            removed_quotation_ids = set(old_quotation_ids) - set(new_quotation_ids)
            for quotation_id in removed_quotation_ids:
                quotation = Quotation.query.get(quotation_id)
                if quotation:
                    current_app.logger.info(f"报价单 {quotation.quotation_number} 已从订单中移除，保持已确认状态")

            # 处理新增的报价单（保持已确认状态，不修改）
            added_quotation_ids = set(new_quotation_ids) - set(old_quotation_ids)
            for quotation_id in added_quotation_ids:
                quotation = Quotation.query.get(quotation_id)
                if quotation and quotation.status == '已确认':
                    current_app.logger.info(f"报价单 {quotation.quotation_number} 已关联到订单，保持已确认状态")

            # 处理产品数据
            if products_data is not None:
                # 获取现有产品ID
                existing_ids = {product.id for product in order.products}
                request_ids = {product.get('id') for product in products_data if product.get('id')}

                # 删除不在请求中的产品
                for product_id in existing_ids - request_ids:
                    product = OrderProduct.query.get(product_id)
                    if product:
                        db.session.delete(product)

                # 更新或创建产品
                for product_data in products_data:
                    product_data['order_id'] = order.id

                    if 'id' in product_data and product_data['id']:
                        # 更新现有产品
                        order_product = OrderProduct.query.get(product_data['id'])
                        if order_product and order_product.order_id == order.id:
                            # 验证产品数据
                            product_schema = OrderProductSchema(exclude=(
                                'total_price', 'delivered_quantity', 'product_name', 'product_model',
                                'product_unit', 'specification_description', 'product', 'product_specification',
                                'created_at', 'updated_at'
                            ))
                            validated_product_data = product_schema.load(product_data, partial=True)

                            # 检查产品规格（如果有）
                            spec = None
                            if 'product_specification_id' in validated_product_data and validated_product_data['product_specification_id']:
                                spec = ProductSpecification.query.get(validated_product_data['product_specification_id'])
                                if not spec:
                                    current_app.logger.warning(f"规格ID {validated_product_data['product_specification_id']} 无效")
                                    continue

                            # 更新产品属性
                            for k, v in validated_product_data.items():
                                setattr(order_product, k, v)

                            # 确保产品来源信息被正确更新
                            order_product.source_type = validated_product_data.get('source_type', order_product.source_type or 'manual')
                            order_product.source_id = validated_product_data.get('source_id', order_product.source_id)
                            order_product.source_display = validated_product_data.get('source_display', order_product.source_display)

                            # 更新总价
                            order_product.update_total_price()
                    else:
                        # 创建新产品
                        product_schema = OrderProductSchema(exclude=(
                            'id', 'total_price', 'delivered_quantity', 'product_name', 'product_model',
                            'product_unit', 'specification_description', 'product', 'product_specification',
                            'created_at', 'updated_at'
                        ))
                        validated_product_data = product_schema.load(product_data)

                        # 检查是否是手动添加的产品（没有product_specification_id）
                        spec = None

                        if validated_product_data.get('product_specification_id'):
                            # 从产品库选择的产品
                            spec = ProductSpecification.query.get(validated_product_data['product_specification_id'])
                            if not spec:
                                current_app.logger.warning(f"规格ID {validated_product_data['product_specification_id']} 无效，跳过")
                                continue

                        # 创建产品
                        order_product = OrderProduct(
                            order_id=validated_product_data['order_id'],
                            product_specification_id=spec.id if spec else None,
                            quantity=validated_product_data['quantity'],
                            unit_price=validated_product_data['unit_price'],
                            # 税率和折扣信息
                            tax_rate=validated_product_data.get('tax_rate', 13.0),
                            discount=validated_product_data.get('discount', 0.0),
                            # 产品来源信息
                            source_type=validated_product_data.get('source_type', 'manual'),
                            source_id=validated_product_data.get('source_id'),
                            source_display=validated_product_data.get('source_display')
                        )

                        # 计算总价
                        order_product.update_total_price()

                        order_product.total_price = order_product.calculate_total_price()
                        db.session.add(order_product)

                db.session.flush()

                # 更新订单总金额
                order.total_amount = order.calculate_total_amount()

            db.session.commit()

            # 返回更新后的数据
            updated_data = OrderSchema().dump(order)
            return make_response(success_response, updated_data, "订单更新成功")

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            db.session.rollback()
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新订单失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新订单失败: {str(e)}")

    @api.doc('delete_order')
    def delete(self, order_id):
        """删除订单"""
        try:
            order = Order.query.get(order_id)
            if not order:
                return make_response(not_found_response, "订单不存在")

            # 检查是否可以删除
            if order.order_status not in ['待确认', '已取消']:
                return make_response(error_response, "只有待确认和已取消状态的订单可以删除")

            db.session.delete(order)
            db.session.commit()
            return make_response(success_response, message="订单删除成功")

        except Exception as e:
            current_app.logger.error(f"删除订单失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除订单失败: {str(e)}")


@api.route('/<int:order_id>/status')
class OrderStatus(Resource):
    @api.doc('update_order_status')
    def put(self, order_id):
        """更新订单状态"""
        try:
            order = Order.query.get(order_id)
            if not order:
                return make_response(not_found_response, "订单不存在")

            data = request.get_json() or {}

            if 'status' not in data:
                return make_response(error_response, "缺少状态参数", code=400)

            new_status = data['status']
            comment = data.get('comment', '')

            # 兼容性处理：将旧的status更新映射到order_status更新
            # 过滤掉对账和收款相关状态，这些不再属于发货状态
            status_mapping = {
                # 标准发货状态（直接映射）
                '待确认': '待确认',
                '已确认': '已确认',
                '生产中': '生产中',
                '待发货': '待发货',
                '部分发货': '部分发货',
                '全部发货': '全部发货',
                '已完成': '已完成',
                '已取消': '已取消',

                # 对账相关状态（映射为全部发货）
                '待对账': '全部发货',
                '部分对账': '全部发货',
                '全部对账': '全部发货',

                # 收款相关状态（映射为已完成）
                '待收款': '已完成',
                '部分收款': '已完成',
            }

            if new_status not in status_mapping:
                return make_response(error_response, f"无效的状态值: {new_status}", code=400)

            # 映射到新的物流状态
            new_order_status = status_mapping[new_status]

            # 检查物流状态变更是否合法
            current_order_status = order.order_status
            valid_order_transitions = order.get_valid_next_order_statuses()

            if new_order_status not in valid_order_transitions:
                return make_response(error_response, f"不能从{current_order_status}状态变更为{new_order_status}状态", code=400)

            # 如果要确认订单，检查客户是否有送货地址
            if new_order_status == '已确认' and not order.customer.get_default_delivery_address():
                return make_response(error_response, "客户没有设置送货地址，无法确认订单。请先为客户添加送货地址。", code=400)

            # 使用新的物流状态更新方法
            order.update_order_status(new_order_status, f"兼容性更新: {new_status} -> {new_order_status}" + (f": {comment}" if comment else ""))

            db.session.commit()

            return make_response(success_response, {
                "status": new_status,  # 兼容性返回
                "order_status": new_order_status,
                "payment_status": order.payment_status,
                "combined_status": order.get_combined_status_display()
            }, "状态更新成功")

        except Exception as e:
            current_app.logger.error(f"更新订单状态失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新订单状态失败: {str(e)}")


@api.route('/<int:order_id>/order-status')
class OrderOrderStatus(Resource):
    @api.doc('update_order_order_status')
    def put(self, order_id):
        """更新订单发货状态"""
        try:
            order = Order.query.get(order_id)
            if not order:
                return make_response(not_found_response, "订单不存在")

            data = request.get_json() or {}

            if 'order_status' not in data:
                return make_response(error_response, "缺少发货状态参数", code=400)

            new_order_status = data['order_status']
            comment = data.get('comment', '')

            valid_order_statuses = [
                '待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消'
            ]

            if new_order_status not in valid_order_statuses:
                return make_response(error_response, f"无效的发货状态值: {new_order_status}", code=400)

            # 检查状态变更是否合法
            valid_next_statuses = order.get_valid_next_order_statuses()
            if new_order_status not in valid_next_statuses:
                return make_response(error_response, f"不能从{order.order_status}状态变更为{new_order_status}状态", code=400)

            # 如果要确认订单，检查客户是否有送货地址
            if new_order_status == '已确认' and not order.customer.get_default_delivery_address():
                return make_response(error_response, "客户没有设置送货地址，无法确认订单。请先为客户添加送货地址。", code=400)

            order.update_order_status(new_order_status, comment)
            db.session.commit()

            return make_response(success_response, {"order_status": new_order_status}, "发货状态更新成功")

        except Exception as e:
            current_app.logger.error(f"更新订单发货状态失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新订单发货状态失败: {str(e)}")


@api.route('/<int:order_id>/payment-status')
class OrderPaymentStatus(Resource):
    @api.doc('update_order_payment_status')
    def put(self, order_id):
        """更新订单财务状态"""
        try:
            order = Order.query.get(order_id)
            if not order:
                return make_response(not_found_response, "订单不存在")

            data = request.get_json() or {}

            if 'payment_status' not in data:
                return make_response(error_response, "缺少财务状态参数", code=400)

            new_payment_status = data['payment_status']
            comment = data.get('comment', '')

            valid_payment_statuses = ['未收款', '部分收款', '已收款']

            if new_payment_status not in valid_payment_statuses:
                return make_response(error_response, "无效的财务状态值", code=400)

            # 检查状态变更是否合法
            valid_next_statuses = order.get_valid_next_payment_statuses()
            if new_payment_status not in valid_next_statuses:
                return make_response(error_response, f"不能从{order.payment_status}状态变更为{new_payment_status}状态", code=400)

            order.update_payment_status(new_payment_status, comment)
            db.session.commit()

            return make_response(success_response, {"payment_status": new_payment_status}, "财务状态更新成功")

        except Exception as e:
            current_app.logger.error(f"更新订单财务状态失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新订单财务状态失败: {str(e)}")


@api.route('/quotations/<int:quotation_id>/create-order')
class CreateOrderFromQuotation(Resource):
    @api.doc('create_order_from_quotation')
    def post(self, quotation_id):
        """基于报价单创建订单"""
        try:
            quotation = Quotation.query.options(
                joinedload(Quotation.items),
                joinedload(Quotation.customer)
            ).get(quotation_id)

            if not quotation:
                return make_response(not_found_response, "报价单不存在")

            if quotation.status != '已确认':
                return make_response(error_response, "只有已确认状态的报价单可以创建订单")

            data = request.get_json() or {}

            # 生成订单编号
            order_number = f"ORD{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"

            # 创建订单
            import json
            order = Order(
                order_number=order_number,
                customer_id=quotation.customer_id,
                quotation_ids=json.dumps([quotation.id]),  # 使用多报价单格式
                project_name=quotation.project_name,
                project_address=quotation.project_address,
                expected_date=data.get('expected_date'),
                payment_terms=quotation.payment_terms,
                delivery_terms=quotation.delivery_terms,
                order_status='待确认',
                notes=data.get('notes', '')
            )

            db.session.add(order)
            db.session.flush()

            # 基于报价单项目创建订单产品
            for quotation_item in quotation.items:
                order_product = OrderProduct(
                    order_id=order.id,
                    product_specification_id=quotation_item.product_specification_id,
                    quantity=quotation_item.quantity,
                    unit_price=quotation_item.unit_price,
                    discount=float(quotation_item.discount or 0),
                    tax_rate=float(quotation_item.tax_rate or 13),
                    # 产品来源信息
                    source_type='quotation',
                    source_id=quotation.id,
                    source_display=quotation.quotation_number
                )

                # 计算总价
                order_product.total_price = order_product.calculate_total_price()
                db.session.add(order_product)

            db.session.flush()

            # 更新订单总金额
            order.total_amount = order.calculate_total_amount()

            # 创建状态历史记录
            status_history = OrderStatusHistory(
                order_id=order.id,
                status=order.order_status,  # 使用order_status替代已删除的status字段
                comment=f"基于报价单 {quotation.quotation_number} 创建订单"
            )
            db.session.add(status_history)

            # 报价单保持已确认状态，不需要修改
            current_app.logger.info(f"基于报价单 {quotation.quotation_number} 创建订单，报价单保持已确认状态")

            db.session.commit()

            # 返回生成的订单
            response_data = OrderSchema().dump(order)
            return make_response(success_response, response_data, "订单创建成功", 201)

        except Exception as e:
            current_app.logger.error(f"基于报价单创建订单失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"基于报价单创建订单失败: {str(e)}")


@api.route('/<int:order_id>/import-quotation')
class ImportQuotationToOrder(Resource):
    @api.doc('import_quotation_to_order')
    def post(self, order_id):
        """向现有订单导入报价单产品"""
        try:
            # 获取订单
            order = Order.query.get(order_id)
            if not order:
                return make_response(not_found_response, "订单不存在")

            # 获取请求数据
            data = request.get_json() or {}
            quotation_id = data.get('quotation_id')

            if not quotation_id:
                return make_response(error_response, "请提供报价单ID")

            # 获取报价单详情
            quotation = Quotation.query.options(
                joinedload(Quotation.items)
            ).get(quotation_id)

            if not quotation:
                return make_response(not_found_response, "报价单不存在")

            # 检查报价单状态
            if quotation.status != '已确认':
                return make_response(error_response, "只能导入已确认状态的报价单")

            # 检查报价单是否已被其他订单关联（排除当前订单）
            existing_order = Order.query.filter(
                db.and_(
                    Order.id != order_id,  # 排除当前订单
                    Order.quotation_ids.like(f'%{quotation_id}%')
                )
            ).first()

            if existing_order:
                return make_response(error_response, f"报价单 {quotation.quotation_number} 已被订单 {existing_order.order_number} 关联，不能重复使用")

            # 更新订单的关联报价单列表
            import json
            current_quotation_ids = []
            if order.quotation_ids:
                try:
                    current_quotation_ids = json.loads(order.quotation_ids)
                except:
                    current_quotation_ids = []

            # 如果报价单还没有关联，则添加
            if quotation_id not in current_quotation_ids:
                current_quotation_ids.append(quotation_id)
                order.quotation_ids = json.dumps(current_quotation_ids)

            # 导入报价单产品到订单
            imported_count = 0
            for quotation_item in quotation.items:
                if not quotation_item.product_specification_id:
                    continue

                order_product = OrderProduct(
                    order_id=order.id,
                    product_specification_id=quotation_item.product_specification_id,
                    quantity=float(quotation_item.quantity),
                    unit_price=float(quotation_item.unit_price),
                    total_price=float(quotation_item.total_price or (quotation_item.quantity * quotation_item.unit_price)),
                    source_type='quotation',
                    source_id=quotation.id,
                    source_display=quotation.quotation_number
                )
                db.session.add(order_product)
                imported_count += 1

            db.session.commit()

            return make_response(success_response, {
                'imported_count': imported_count,
                'quotation_number': quotation.quotation_number
            }, f"成功导入 {imported_count} 个产品")

        except Exception as e:
            current_app.logger.error(f"导入报价单到订单失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"导入报价单失败: {str(e)}")


