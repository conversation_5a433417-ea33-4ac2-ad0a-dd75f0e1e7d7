"""
客户相关的序列化模式
基于原项目的Schema定义，确保与现有数据库结构100%兼容
"""
from marshmallow import Schema, fields, validate, validates, ValidationError
from typing import Dict, List, Optional, Any


class CustomerBankAccountSchema(Schema):
    """客户银行账户序列化模式"""
    id = fields.Int(dump_only=True)
    customer_id = fields.Int(load_only=True)
    bank_name = fields.Str(required=True, validate=validate.Length(max=100))
    account_name = fields.Str(required=True, validate=validate.Length(max=100))
    account_number = fields.Str(required=True, validate=validate.Length(max=50))
    is_default = fields.Bool(load_default=False)
    notes = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class CustomerDeliveryAddressSchema(Schema):
    """客户送货地址序列化模式"""
    id = fields.Int(dump_only=True)
    customer_id = fields.Int(load_only=True)
    province = fields.Str(required=True, validate=validate.Length(max=50))
    city = fields.Str(required=True, validate=validate.Length(max=50))
    district = fields.Str(required=True, validate=validate.Length(max=50))
    detailed_address = fields.Str(required=True, validate=validate.Length(max=200))
    contact_person = fields.Str(required=True, validate=validate.Length(max=50))
    contact_phone = fields.Str(required=True, validate=validate.Length(max=20))
    is_default = fields.Bool(load_default=False)
    notes = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    
    # 添加计算字段用于显示完整地址
    full_address = fields.Method("get_full_address", dump_only=True)
    
    def get_full_address(self, obj):
        """获取完整地址"""
        if hasattr(obj, 'get_full_address'):
            return obj.get_full_address()
        return f"{obj.province}{obj.city}{obj.district}{obj.detailed_address}"


class CustomerSchema(Schema):
    """客户序列化模式"""
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True, validate=validate.Length(max=100))
    contact = fields.Str(required=True, validate=validate.Length(max=50))
    phone = fields.Str(allow_none=True, validate=validate.Length(max=20))
    email = fields.Str(
        allow_none=True, 
        validate=[
            validate.Length(max=100), 
            validate.Email(error="无效的电子邮件地址")
        ]
    )
    address = fields.Str(allow_none=True, validate=validate.Length(max=200))
    tax_id = fields.Str(allow_none=True, validate=validate.Length(max=50))
    source = fields.Str(allow_none=True, validate=validate.Length(max=50))
    level = fields.Str(allow_none=True, validate=validate.Length(max=20))
    status = fields.Str(load_default='正常', validate=validate.OneOf(['正常', '禁用']))
    notes = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    # 关联数据
    bank_accounts = fields.List(fields.Nested(CustomerBankAccountSchema), allow_none=True)
    delivery_addresses = fields.List(fields.Nested(CustomerDeliveryAddressSchema), allow_none=True)
    
    # 额外字段
    default_bank_account = fields.Method("get_default_bank_account", dump_only=True)
    default_delivery_address = fields.Method("get_default_delivery_address", dump_only=True)

    def get_default_bank_account(self, obj):
        """获取默认银行账户"""
        if hasattr(obj, 'get_default_bank_account'):
            default_account = obj.get_default_bank_account()
            if default_account:
                return CustomerBankAccountSchema().dump(default_account)
        return None

    def get_default_delivery_address(self, obj):
        """获取默认送货地址"""
        if hasattr(obj, 'get_default_delivery_address'):
            default_address = obj.get_default_delivery_address()
            if default_address:
                return CustomerDeliveryAddressSchema().dump(default_address)
        return None


class CustomerSimpleSchema(Schema):
    """简化版客户序列化模式，不包含关联数据"""
    id = fields.Int(dump_only=True)
    name = fields.Str(dump_only=True)
    contact = fields.Str(dump_only=True)
    phone = fields.Str(dump_only=True)
    level = fields.Str(dump_only=True)
    status = fields.Str(dump_only=True)
    created_at = fields.DateTime(dump_only=True)
