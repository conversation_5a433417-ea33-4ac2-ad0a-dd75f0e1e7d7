#!/usr/bin/env python3
"""
添加收款凭据字段到对账单收款记录表
"""
import sqlite3
import os
import sys

def add_voucher_files_column():
    """添加voucher_files字段到statement_payments表"""
    
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(statement_payments)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'voucher_files' in columns:
            print("voucher_files字段已存在，跳过添加")
            return True
        
        print("开始添加voucher_files字段...")
        
        # 添加voucher_files字段
        cursor.execute("""
            ALTER TABLE statement_payments 
            ADD COLUMN voucher_files TEXT
        """)
        
        # 提交更改
        conn.commit()
        print("✅ voucher_files字段添加成功")
        
        # 验证字段是否添加成功
        cursor.execute("PRAGMA table_info(statement_payments)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'voucher_files' in columns:
            print("✅ 字段验证成功")
            return True
        else:
            print("❌ 字段验证失败")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("=== 添加收款凭据字段迁移 ===")
    
    success = add_voucher_files_column()
    
    if success:
        print("🎉 迁移完成！")
        sys.exit(0)
    else:
        print("💥 迁移失败！")
        sys.exit(1)

if __name__ == '__main__':
    main()
