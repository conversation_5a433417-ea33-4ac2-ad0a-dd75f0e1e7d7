<template>
  <div class="test-payment">
    <el-container>
      <el-header>
        <h1>收款管理功能测试</h1>
      </el-header>
      <el-main>
        <PaymentManagement />
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import PaymentManagement from './finance/PaymentManagement.vue'
</script>

<style scoped>
.test-payment {
  height: 100vh;
  width: 100vw;
}

.el-header {
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.el-header h1 {
  margin: 0;
  font-size: 24px;
}

.el-main {
  padding: 0;
  background-color: #f5f5f5;
}
</style>
