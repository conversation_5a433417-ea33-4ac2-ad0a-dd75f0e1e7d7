import{J as e,b as a,u as t,r,L as d,e as o,f as s,M as i,o as n,c as l,a as u,g as c,h as _,k as p,j as f,i as m,N as y,t as b,l as w,U as h,W as g}from"./index-3d4c440c.js";import{g as v,c as k,u as q}from"./return-614d16fd.js";import{c as j}from"./order-d6035a15.js";import{f as C,a as I}from"./format-552375ee.js";import{_ as N}from"./_plugin-vue_export-helper-1b428a4d.js";const x={class:"return-order-detail"},A={class:"page-header"},D={class:"header-actions"},J={class:"detail-content"},O=N(e({__name:"ReturnOrderDetail",setup(e){const N=a(),O=t(),$=r(!1),P=r(""),R=d({id:0,order_id:0,return_number:"",return_date:"",status:"待确认",reason:"",notes:"",items:[],created_at:"",updated_at:""}),S=async()=>{var e;const a=Number(O.params.id);if(!a)return h.error("退货单ID无效"),void N.back();$.value=!0;try{console.log("获取退货单详情，ID:",a);const t=await v(a);console.log("退货单详情API响应:",t);const r=t.data;if(!r)throw new Error("获取退货单详情失败：数据为空");console.log("退货单详情数据:",JSON.stringify(r)),R.id=r.id,R.order_id=r.order_id,R.return_number=r.return_number||"",R.return_date=r.return_date,R.status=r.status||"待确认",R.reason=r.reason,R.notes=r.notes||"",R.created_at=r.created_at||"",R.updated_at=r.updated_at||"",r.items&&Array.isArray(r.items)&&(R.items=r.items.map((e=>({...e,id:e.id,return_order_id:e.return_order_id,order_product_id:e.order_product_id,quantity:e.quantity,reason:e.reason||"",notes:e.notes||"",product_name:e.product_name||"",product_model:e.product_model||"",product_unit:e.product_unit||"",specification_description:e.specification_description||"",order_product:e.order_product,order_quantity:0,returned_quantity:0}))),Promise.all(R.items.map((async(e,a)=>{try{const d=await k(e.order_product_id);d&&"object"==typeof d&&"data"in d&&"object"==typeof d.data&&d.data&&"quantity"in d.data&&(R.items[a].returned_quantity=d.data.quantity||0);const o=e.order_product_id;if(r.order_id)try{const e=await j(r.order_id);if(e&&e.data){const t=e.data,r=(t.products||[]).find((e=>e.id===o));r&&(R.items[a].order_quantity=r.quantity||0)}}catch(t){console.error("获取订单详情失败:",t)}}catch(t){console.error(`获取商品 ${e.product_name} 的数量信息失败:`,t)}})))),P.value=r.order_number||(null==(e=r.order)?void 0:e.order_number)||"",console.log("表单数据已更新:",JSON.stringify(R))}catch(t){console.error("获取退货单详情失败:",t),h.error("获取退货单详情失败")}finally{$.value=!1}},E=()=>{N.push(`/return/${R.id}/edit`)},L=async()=>{try{await g.confirm("确认要确认此退货单吗？","提示",{type:"warning"}),await q(R.id,"已确认"),h.success("退货单已确认"),S()}catch(e){"cancel"!==e&&(console.error("确认退货单失败:",e),h.error("确认退货单失败"))}},M=async()=>{try{await g.confirm("确认要完成此退货单吗？","提示",{type:"warning"}),await q(R.id,"已完成"),h.success("退货单已完成"),S()}catch(e){"cancel"!==e&&(console.error("完成退货单失败:",e),h.error("完成退货单失败"))}},U=async()=>{try{await g.confirm("确认要取消此退货单吗？","提示",{type:"warning"}),await q(R.id,"已取消"),h.success("退货单已取消"),S()}catch(e){"cancel"!==e&&(console.error("取消退货单失败:",e),h.error("取消退货单失败"))}},W=()=>{N.push(`/order/${R.order_id}`)},z=()=>{N.back()};return o((()=>{S()})),(e,a)=>{const t=s("el-button"),r=s("el-descriptions-item"),d=s("el-link"),o=s("el-tag"),h=s("el-descriptions"),g=s("el-card"),v=s("el-table-column"),k=s("el-table"),q=i("loading");return n(),l("div",x,[u("div",A,[a[5]||(a[5]=u("h2",null,"退货单详情",-1)),u("div",D,["待确认"===R.status?(n(),c(t,{key:0,type:"primary",onClick:E},{default:_((()=>a[0]||(a[0]=[p("编辑")]))),_:1})):f("",!0),"待确认"===R.status?(n(),c(t,{key:1,type:"primary",onClick:L},{default:_((()=>a[1]||(a[1]=[p("确认")]))),_:1})):f("",!0),"已确认"===R.status?(n(),c(t,{key:2,type:"primary",onClick:M},{default:_((()=>a[2]||(a[2]=[p("完成")]))),_:1})):f("",!0),["待确认","已确认"].includes(R.status??"待确认")?(n(),c(t,{key:3,type:"danger",onClick:U},{default:_((()=>a[3]||(a[3]=[p("取消")]))),_:1})):f("",!0),m(t,{onClick:z},{default:_((()=>a[4]||(a[4]=[p("返回")]))),_:1})])]),y((n(),l("div",J,[m(g,{class:"detail-card"},{header:_((()=>a[6]||(a[6]=[u("div",{class:"card-header"},[u("span",null,"基本信息")],-1)]))),default:_((()=>[m(h,{column:2,border:""},{default:_((()=>[m(r,{label:"退货单号"},{default:_((()=>[p(b(R.return_number),1)])),_:1}),m(r,{label:"关联订单"},{default:_((()=>[m(d,{type:"primary",onClick:W},{default:_((()=>[p(b(P.value),1)])),_:1})])),_:1}),m(r,{label:"退货日期"},{default:_((()=>[p(b(w(C)(R.return_date)),1)])),_:1}),m(r,{label:"状态"},{default:_((()=>{return[m(o,{type:(e=R.status,{"待确认":"warning","已确认":"primary","已完成":"success","已取消":"info"}[e||"待确认"])},{default:_((()=>[p(b(R.status),1)])),_:1},8,["type"])];var e})),_:1}),m(r,{label:"退货原因",span:2},{default:_((()=>[p(b(R.reason),1)])),_:1}),m(r,{label:"备注",span:2},{default:_((()=>[p(b(R.notes||"-"),1)])),_:1}),m(r,{label:"创建时间"},{default:_((()=>[p(b(w(I)(R.created_at)),1)])),_:1}),m(r,{label:"更新时间"},{default:_((()=>[p(b(w(I)(R.updated_at)),1)])),_:1})])),_:1})])),_:1}),m(g,{class:"detail-card"},{header:_((()=>a[7]||(a[7]=[u("div",{class:"card-header"},[u("span",null,"退货商品")],-1)]))),default:_((()=>[m(k,{data:R.items,border:""},{default:_((()=>[m(v,{label:"商品名称","min-width":"200"},{default:_((({row:e})=>[p(b(e.product_name),1)])),_:1}),m(v,{label:"规格","min-width":"150"},{default:_((({row:e})=>[p(b(e.specification_description),1)])),_:1}),m(v,{label:"订单数量",width:"100"},{default:_((({row:e})=>[p(b(e.order_quantity),1)])),_:1}),m(v,{label:"已退数量",width:"100"},{default:_((({row:e})=>[p(b(e.returned_quantity||0),1)])),_:1}),m(v,{label:"退货数量",width:"100"},{default:_((({row:e})=>[p(b(e.quantity),1)])),_:1}),m(v,{label:"备注","min-width":"150"},{default:_((({row:e})=>[p(b(e.notes||"-"),1)])),_:1})])),_:1},8,["data"])])),_:1})])),[[q,$.value]])])}}}),[["__scopeId","data-v-139f1fdd"]]);export{O as default};
