import{I as t}from"./index-3d4c440c.js";function e(e){return console.log("[API] searchCustomers - Requesting with params:",JSON.parse(JSON.stringify(e))),t.get("/api/v1/customers",{params:e})}function s(e){return console.log("[API] getCustomers - Requesting with params:",e?JSON.parse(JSON.stringify(e)):{}),t.get("/api/v1/customers",{params:e})}function r(e){return t.get(`/api/v1/customers/${e}`)}function a(e){return t.post("/api/v1/customers",e)}function o(e,s){return t.put(`/api/v1/customers/${e}`,s)}function u(e){return t.delete(`/api/v1/customers/${e}`)}function n(e){return t({url:"/api/v1/customers/batch",method:"delete",data:{ids:e}})}function i(e){const s=new FormData;return s.append("file",e),t({url:"/api/v1/customers/import",method:"post",data:s,headers:{"Content-Type":"multipart/form-data"}})}function c(e){return t({url:"/api/v1/customers/export",method:"get",params:e,responseType:"blob"})}function p(e){return t({url:"/api/v1/customers/batch/export",method:"post",data:{ids:e},responseType:"blob"})}function m(e,s){return t({url:`/api/v1/customers/${e}/bank-accounts`,method:"post",data:s})}function d(e,s){return t({url:`/api/v1/customers/bank-accounts/${e}`,method:"put",data:s})}function l(e){return t({url:`/api/v1/customers/bank-accounts/${e}`,method:"delete"})}function f(e,s){return t({url:`/api/v1/customers/${e}/delivery-addresses`,method:"post",data:s})}function v(e,s){return t({url:`/api/v1/customers/delivery-addresses/${e}`,method:"put",data:s})}function h(e){return t({url:`/api/v1/customers/delivery-addresses/${e}`,method:"delete"})}export{f as a,v as b,m as c,r as d,u as e,n as f,s as g,c as h,i,p as j,a as k,o as l,l as m,h as n,e as s,d as u};
