<template>
  <el-dialog
    v-model="dialogVisible"
    :title="type === 'add' ? '添加银行账户' : '编辑银行账户'"
    width="500px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="开户行" prop="bank_name">
        <el-input v-model="form.bank_name" placeholder="请输入开户行名称" />
      </el-form-item>
      
      <el-form-item label="账户名称" prop="account_name">
        <el-input v-model="form.account_name" placeholder="请输入账户名称" />
      </el-form-item>
      
      <el-form-item label="账号" prop="account_number">
        <el-input v-model="form.account_number" placeholder="请输入银行账号" />
      </el-form-item>
      
      <el-form-item label="是否默认">
        <el-switch v-model="form.is_default" />
      </el-form-item>
      
      <el-form-item label="备注" prop="notes">
        <el-input 
          v-model="form.notes" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息（可选）" 
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ type === 'add' ? '添加' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { customerApi } from '@/api/customer'

// Props
interface Props {
  visible: boolean
  type: 'add' | 'edit'
  data?: any
  customerId: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  type: 'add',
  data: () => ({}),
  customerId: 0
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'submit': [data?: any]
}>()

// 表单引用
const formRef = ref()

// 状态
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = reactive({
  bank_name: '',
  account_name: '',
  account_number: '',
  is_default: false,
  notes: ''
})

// 表单验证规则
const rules = {
  bank_name: [
    { required: true, message: '请输入开户行名称', trigger: 'blur' },
    { max: 100, message: '开户行名称长度不能超过 100 个字符', trigger: 'blur' }
  ],
  account_name: [
    { required: true, message: '请输入账户名称', trigger: 'blur' },
    { max: 100, message: '账户名称长度不能超过 100 个字符', trigger: 'blur' }
  ],
  account_number: [
    { required: true, message: '请输入银行账号', trigger: 'blur' },
    { max: 50, message: '银行账号长度不能超过 50 个字符', trigger: 'blur' }
  ],
  notes: [
    { max: 500, message: '备注长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 监听数据变化，填充表单
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, {
      bank_name: newData.bank_name || '',
      account_name: newData.account_name || '',
      account_number: newData.account_number || '',
      is_default: newData.is_default || false,
      notes: newData.notes || ''
    })
  }
}, { immediate: true, deep: true })

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    bank_name: '',
    account_name: '',
    account_number: '',
    is_default: false,
    notes: ''
  })
  formRef.value?.clearValidate()
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理关闭
const handleClosed = () => {
  resetForm()
}

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData: any = { ...form }

    // 处理空值 - 删除空字段而不是设置为null
    if (!submitData.notes || submitData.notes.trim() === '') {
      delete submitData.notes
    }

    if (props.customerId === 0) {
      // 新增客户时，不调用API，直接传递数据给父组件
      dialogVisible.value = false
      emit('submit', submitData)
    } else {
      // 编辑已存在客户时，调用API
      if (props.type === 'add') {
        // 添加银行账户
        await customerApi.addBankAccount(props.customerId, submitData)
        ElMessage.success('添加银行账户成功')
      } else {
        // 更新银行账户
        await customerApi.updateBankAccount(props.customerId, props.data.id, submitData)
        ElMessage.success('更新银行账户成功')
      }

      // 关闭对话框并触发刷新
      dialogVisible.value = false
      emit('submit')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(props.type === 'add' ? '添加银行账户失败' : '更新银行账户失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
