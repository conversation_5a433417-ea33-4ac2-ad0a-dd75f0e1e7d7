<template>
  <el-dialog
    v-model="dialogVisible"
    :title="type === 'add' ? '添加需求项目' : '编辑需求项目'"
    width="600px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="产品名称" prop="product_name">
        <el-input v-model="form.product_name" placeholder="请输入产品名称" />
      </el-form-item>
      
      <el-form-item label="产品型号" prop="product_model">
        <el-input v-model="form.product_model" placeholder="请输入产品型号" />
      </el-form-item>
      
      <el-form-item label="产品规格" prop="product_spec">
        <el-input 
          v-model="form.product_spec" 
          type="textarea"
          :rows="3"
          placeholder="请输入产品规格要求" 
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数量" prop="quantity">
            <el-input-number 
              v-model="form.quantity" 
              :min="1" 
              :precision="0"
              placeholder="请输入数量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="form.unit" placeholder="例如：个、台、米、公斤等" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注" prop="notes">
        <el-input 
          v-model="form.notes" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息（可选）" 
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ type === 'add' ? '添加' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  visible: boolean
  type: 'add' | 'edit'
  data?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  type: 'add',
  data: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'submit': [data: any]
}>()

// 表单引用
const formRef = ref()

// 状态
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = reactive({
  product_name: '',
  product_model: '',
  product_spec: '',
  quantity: 1,
  unit: '',
  notes: ''
})

// 表单验证规则
const rules = {
  product_name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请选择单位', trigger: 'change' }
  ],
  product_model: [
    { max: 50, message: '产品型号长度不能超过 50 个字符', trigger: 'blur' }
  ],
  product_spec: [
    { max: 500, message: '产品规格长度不能超过 500 个字符', trigger: 'blur' }
  ],
  notes: [
    { max: 500, message: '备注长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 监听数据变化，填充表单
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, {
      product_name: newData.product_name || '',
      product_model: newData.product_model || '',
      product_spec: newData.product_spec || '',
      quantity: newData.quantity || 1,
      unit: newData.unit || '个',
      notes: newData.notes || ''
    })
  }
}, { immediate: true, deep: true })

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    product_name: '',
    product_model: '',
    product_spec: '',
    quantity: 1,
    unit: '个',
    notes: ''
  })
  formRef.value?.clearValidate()
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理关闭
const handleClosed = () => {
  resetForm()
}

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData = { ...form }
    
    // 处理空值
    if (!submitData.notes || submitData.notes.trim() === '') {
      submitData.notes = ''
    }
    if (!submitData.product_model || submitData.product_model.trim() === '') {
      submitData.product_model = ''
    }
    if (!submitData.product_spec || submitData.product_spec.trim() === '') {
      submitData.product_spec = ''
    }
    
    // 触发提交事件
    emit('submit', submitData)
    
    // 关闭对话框
    dialogVisible.value = false
    
    ElMessage.success(props.type === 'add' ? '添加项目成功' : '更新项目成功')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(props.type === 'add' ? '添加项目失败' : '更新项目失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
