import{b as e,r as a,L as t,e as l,f as r,M as o,o as n,c as s,i,h as u,U as d,a as c,k as p,l as m,N as v,t as g,Y as f}from"./index-3d4c440c.js";import{g as _,u as y}from"./finance-789ce3e6.js";import{_ as h}from"./_plugin-vue_export-helper-1b428a4d.js";const w={class:"payment-pending"},b={class:"card-header"},C={class:"left"},k={class:"right"},z={class:"table-container"},A={class:"pagination-container"},S={class:"dialog-footer"},x={class:"dialog-footer"},V=h({__name:"PaymentPending",setup(h){const V=e(),j=a(!1),N=a(!1),U=a(!1),$=a(!1),P=a(!1),D=a(null),I=a([]),L=t({page:1,pageSize:20,total:0}),Y=async()=>{j.value=!0;try{const e={page:L.page,per_page:L.pageSize,status:"待确认"};console.log("获取待确认收款记录，参数:",e);const a=await _(e);if(console.log("待确认收款响应:",a),a){if(Array.isArray(a))I.value=a,L.total=a.length;else if(a.data)if(Array.isArray(a.data))I.value=a.data,L.total=a.data.length;else if(a.data.items&&Array.isArray(a.data.items))I.value=a.data.items,a.data.page_info?L.total=a.data.page_info.total||a.data.items.length:L.total=a.data.items.length;else{const e=Object.values(a.data).find((e=>Array.isArray(e)));e?(I.value=e,L.total=e.length):(I.value=[],L.total=0)}else a.items&&Array.isArray(a.items)?(I.value=a.items,L.total=a.total||a.items.length):(console.warn("无法识别的响应格式:",a),I.value=[],L.total=0);0===I.value.length&&d.info("没有待确认的收款记录"),console.log("处理后的待确认收款列表:",I.value)}else I.value=[],L.total=0,d.warning("未找到待确认的收款记录")}catch(e){console.error("获取待确认收款记录失败:",e),e.response&&console.error("错误响应:",e.response.status,e.response.data),d.error("获取待确认收款记录失败"),I.value=[]}finally{j.value=!1}},F=e=>{Y()},K=e=>{L.pageSize=e,Y()},M=e=>{L.page=e,Y()},O=e=>{if(!e)return"";try{return new Date(e).toLocaleDateString("zh-CN")}catch(a){return e}},R=async()=>{if(D.value){$.value=!0;try{await y(D.value.id,"已确认"),d.success("收款已确认"),N.value=!1,Y()}catch(e){console.error("确认收款失败:",e),d.error("确认收款失败")}finally{$.value=!1}}},q=async()=>{if(D.value){P.value=!0;try{await y(D.value.id,"已取消"),d.success("收款已取消"),U.value=!1,Y()}catch(e){console.error("取消收款失败:",e),d.error("取消收款失败")}finally{P.value=!1}}},B=()=>{Y()};return l((()=>{Y()})),(e,a)=>{const t=r("el-tag"),l=r("el-icon"),d=r("el-button"),_=r("el-card"),y=r("el-table-column"),h=r("el-table"),Y=r("el-pagination"),E=r("el-dialog"),G=o("loading");return n(),s("div",w,[i(_,{class:"header-card mb-20"},{default:u((()=>[c("div",b,[c("div",C,[a[7]||(a[7]=c("h2",{class:"page-title"},"待确认收款",-1)),i(t,{type:"warning"},{default:u((()=>a[6]||(a[6]=[p("需要处理的收款申请")]))),_:1})]),c("div",k,[i(d,{type:"primary",onClick:B},{default:u((()=>[i(l,null,{default:u((()=>[i(m(f))])),_:1}),a[8]||(a[8]=p(" 刷新数据 "))])),_:1})])])])),_:1}),i(_,null,{default:u((()=>[v((n(),s("div",z,[i(h,{data:I.value,border:"",stripe:"",style:{width:"100%"},onSortChange:F},{default:u((()=>[i(y,{prop:"id",label:"收款单号","min-width":"150",sortable:"custom",fixed:""},{default:u((e=>[i(d,{type:"primary",link:"",onClick:a=>{return t=e.row,void V.push(`/payment-records/detail/${t.id}`);var t}},{default:u((()=>[p(g(e.row.payment_number||`SK${e.row.id}`),1)])),_:2},1032,["onClick"])])),_:1}),i(y,{prop:"order_number",label:"关联订单","min-width":"150",sortable:"custom"},{default:u((e=>[i(d,{type:"success",link:"",onClick:a=>{return t=e.row.order_id,void V.push(`/orders/detail/${t}`);var t}},{default:u((()=>[p(g(e.row.order_number),1)])),_:2},1032,["onClick"])])),_:1}),i(y,{prop:"customer_name",label:"客户名称","min-width":"200"},{default:u((e=>[i(d,{type:"info",link:"",onClick:a=>{return t=e.row.customer_id,void V.push(`/customers/${t}`);var t}},{default:u((()=>[p(g(e.row.customer_name),1)])),_:2},1032,["onClick"])])),_:1}),i(y,{prop:"amount",label:"收款金额","min-width":"120",sortable:"custom"},{default:u((e=>{return[p(g((a=e.row.amount,null==a?"¥ 0.00":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY"}).format(a))),1)];var a})),_:1}),i(y,{prop:"payment_method",label:"收款方式",width:"120"},{default:u((e=>[i(t,null,{default:u((()=>{return[p(g((a=e.row.payment_method,{bank_transfer:"银行转账",cash:"现金",check:"支票",electronic:"电子支付",online_payment:"在线支付",other:"其他"}[a]||a)),1)];var a})),_:2},1024)])),_:1}),i(y,{prop:"payment_date",label:"收款日期",width:"120",sortable:"custom"},{default:u((e=>[p(g(O(e.row.payment_date)),1)])),_:1}),i(y,{label:"操作",width:"200",fixed:"right"},{default:u((e=>[i(d,{type:"success",size:"small",onClick:a=>{return t=e.row,D.value=t,void(N.value=!0);var t}},{default:u((()=>a[9]||(a[9]=[p("确认收款")]))),_:2},1032,["onClick"]),i(d,{type:"danger",size:"small",onClick:a=>{return t=e.row,D.value=t,void(U.value=!0);var t}},{default:u((()=>a[10]||(a[10]=[p("取消收款")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),c("div",A,[i(Y,{"current-page":L.page,"onUpdate:currentPage":a[0]||(a[0]=e=>L.page=e),"page-size":L.pageSize,"onUpdate:pageSize":a[1]||(a[1]=e=>L.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:L.total,onSizeChange:K,onCurrentChange:M},null,8,["current-page","page-size","total"])])])),[[G,j.value]])])),_:1}),i(E,{modelValue:N.value,"onUpdate:modelValue":a[3]||(a[3]=e=>N.value=e),title:"确认收款",width:"30%"},{footer:u((()=>[c("span",S,[i(d,{onClick:a[2]||(a[2]=e=>N.value=!1)},{default:u((()=>a[11]||(a[11]=[p("取消")]))),_:1}),i(d,{type:"primary",onClick:R,loading:$.value},{default:u((()=>a[12]||(a[12]=[p("确认")]))),_:1},8,["loading"])])])),default:u((()=>[a[13]||(a[13]=c("p",null,"确认处理此笔收款吗？确认后将更新订单的已付款金额。",-1))])),_:1},8,["modelValue"]),i(E,{modelValue:U.value,"onUpdate:modelValue":a[5]||(a[5]=e=>U.value=e),title:"取消收款",width:"30%"},{footer:u((()=>[c("span",x,[i(d,{onClick:a[4]||(a[4]=e=>U.value=!1)},{default:u((()=>a[14]||(a[14]=[p("关闭")]))),_:1}),i(d,{type:"danger",onClick:q,loading:P.value},{default:u((()=>a[15]||(a[15]=[p("确认取消")]))),_:1},8,["loading"])])])),default:u((()=>[a[16]||(a[16]=c("p",null,"确认取消此笔收款申请吗？",-1))])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-ed3ed39a"]]);export{V as default};
