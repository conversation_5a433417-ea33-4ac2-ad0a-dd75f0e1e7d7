import{J as e,r as l,d as a,e as o,f as t,o as r,c as n,i as d,h as s,a as u,k as i,g as c,t as p,a8 as v,j as m,F as _,m as g,U as f,V as y,W as h}from"./index-3d4c440c.js";import{g as k,l as w,c as V,u as b,d as A}from"./productCategory-3670b9eb.js";import{_ as C}from"./_plugin-vue_export-helper-1b428a4d.js";const x={class:"category-list"},I={class:"flex-between"},P={class:"custom-tree-node"},U={class:"tree-actions"},T={class:"card-header"},$={class:"dialog-footer"},j=C(e({__name:"ProductCategoryList",setup(e){console.log("当前运行环境:","production","是否为开发环境:",!1);const C=l(),j=l(),R=l(),z=l(!1),B=l(!1),F=l(!1),L=l("add"),M=l({}),E=l([]),N=l([]),S=l(0),q=l({name:"",parent_id:0,sort_order:1,description:"",notes:""}),D=l({name:"",parent_id:0,sort_order:1,description:"",notes:""}),J={children:"children",label:"name"},O=a((()=>{if(D.value.parent_id&&0!==D.value.parent_id){const e=E.value.find((e=>e.id===D.value.parent_id));return e?e.name:"未知分类"}return"无父级分类 (设为顶级)"})),W=async()=>{z.value=!0,console.log("=== 开始获取产品分类数据 ===");try{const a={}.VITE_API_URL||localStorage.getItem("apiBaseUrl")||window.location.origin;console.log("当前API基础URL:",a),console.log("尝试调用getProductCategoryTree API...");let o=await k();if(!o||0===o.length){console.log("getProductCategoryTree返回空数据，尝试备用API...");try{const e=await w({tree:!0,timestamp:(new Date).getTime(),all:!0});console.log("listProductCategories备用API响应:",e),e&&Array.isArray(e)?o=e:e&&e.items?o=e.items:console.log("备用API也未能返回有效数据")}catch(e){console.error("备用API调用失败:",e)}}if(!o||0===o.length){console.log("尝试第三种方案: 直接API调用...");try{const e=await fetch(`${a}/api/v1/product-categories?tree=true&timestamp=${Date.now()}`,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")||"demo-token"}`}});if(e.ok){const l=await e.json();console.log("直接API调用响应:",l),l&&Array.isArray(l)?o=l:l&&l.items&&Array.isArray(l.items)?o=l.items:l&&l.data&&(Array.isArray(l.data)?o=l.data:l.data.items&&Array.isArray(l.data.items)&&(o=l.data.items))}else console.error("直接API调用失败:",e.status,e.statusText)}catch(l){console.error("直接API调用出错:",l)}}if(console.log("最终获取到的分类数据:",o),o&&o.length>0){console.log(`共获取到 ${o.length} 个顶级分类`),N.value=o,console.log("使用后端返回的树状结构，顶级分类数:",N.value.length);const e=[],l=(e,a)=>{e.forEach((e=>{const o={...e,parent_id:null===e.parent_id?0:e.parent_id};a.push(o),e.children&&e.children.length>0&&l(e.children,a)}))};l(o,e),E.value=e,console.log(`扁平化后共有 ${E.value.length} 个分类`),S.value+=1,0===E.value.length&&(console.warn("未获取到真实分类数据，创建示例分类"),G())}else console.warn("未获取到任何分类数据"),E.value=[],N.value=[],G(),f({message:"未获取到产品分类数据，已创建示例分类供测试使用",type:"warning",duration:5e3})}catch(a){console.error("获取分类列表失败:",a),f.error("获取分类列表失败，请检查网络连接"),G()}finally{z.value=!1}},G=()=>{console.log("创建示例分类数据"),N.value=[{id:9999,name:"阀门类",parent_id:null,level:1,sort_order:1,description:"示例分类-阀门类产品",notes:"这是系统自动创建的示例分类，您可以编辑或删除它",children:[{id:9998,name:"闸阀",parent_id:9999,level:2,sort_order:1,description:"示例分类-闸阀产品",notes:"这是系统自动创建的示例分类",children:[]},{id:9997,name:"球阀",parent_id:9999,level:2,sort_order:2,description:"示例分类-球阀产品",notes:"这是系统自动创建的示例分类",children:[]}]},{id:9996,name:"管材管件",parent_id:null,level:1,sort_order:2,description:"示例分类-管材管件产品",notes:"这是系统自动创建的示例分类，您可以编辑或删除它",children:[]}];const e=[],l=(e,a)=>{e.forEach((e=>{a.push(e),e.children&&e.children.length>0&&l(e.children,a)}))};l(N.value,e),E.value=e,console.log("创建了示例分类数据:",E.value.length,"个分类"),S.value+=1},H=a((()=>(console.log("使用后端返回的树状结构，顶级分类数:",N.value.length),N.value))),K={name:[{required:!0,message:"请输入分类名称",trigger:"blur"}],parent_id:[{required:!1,message:"请选择父级分类",trigger:"change"}],sort_order:[{type:"number",message:"排序必须为数字"}]},Q=e=>{M.value={...e},q.value={...e},B.value=!1},X=()=>{L.value="add";const e=E.value.filter((e=>!e.parent_id||0===e.parent_id)).reduce(((e,l)=>Math.max(e,l.sort_order??0)),0)+1;D.value={name:"",parent_id:0,sort_order:e,description:"",notes:""},F.value=!0,y((()=>{R.value&&(R.value.resetFields(),D.value.parent_id=0,D.value.sort_order=e)}))},Y=async()=>{R.value&&await R.value.validate((async e=>{var l,a;if(e){const e={name:D.value.name||"",parent_id:D.value.parent_id??0,sort_order:D.value.sort_order??1,description:D.value.description||null,notes:D.value.notes||null};if(!e.name.trim())return void f.error("分类名称不能为空");console.log("准备添加分类:",e),z.value=!0;try{const l=await V(e);console.log("分类创建成功:",l),f.success("分类添加成功"),F.value=!1,await W()}catch(o){console.error("添加分类失败:",o);let e="添加分类失败";o.response?(console.log("错误响应:",o.response),(null==(l=o.response.data)?void 0:l.message)?e=o.response.data.message:(null==(a=o.response.data)?void 0:a.error)?e=o.response.data.error:"string"==typeof o.response.data&&(e=o.response.data)):o.message&&(e=o.message),f.error(e)}finally{z.value=!1}}}))},Z=async()=>{j.value&&M.value.id&&await j.value.validate((async e=>{var l,a;if(e){z.value=!0;try{const e={name:q.value.name,parent_id:q.value.parent_id,sort_order:q.value.sort_order,description:q.value.description,notes:q.value.notes};await b(M.value.id,e),f.success("分类更新成功"),B.value=!1,await W();const l=E.value.find((e=>e.id===M.value.id));l?Q(l):M.value={}}catch(o){console.error("更新分类失败:",o);const e=(null==(a=null==(l=null==o?void 0:o.response)?void 0:l.data)?void 0:a.message)||o.message||"更新分类失败";f.error(e)}finally{z.value=!1}}}))},ee=()=>{var e;B.value=!1,M.value.id&&(q.value={...M.value}),null==(e=j.value)||e.clearValidate()};return o((()=>{console.log("ProductCategoryList onMounted - 加载分类数据"),localStorage.getItem("token"),W().then((()=>{setTimeout((()=>{if(C.value){(()=>{try{if(C.value&&C.value.store){const e=C.value.store.nodesMap;if(e){const l=Object.keys(e);console.log(`尝试展开 ${l.length} 个节点`);for(const a of l){const l=e[a];l&&(l.expanded=!0)}}else console.log("树节点映射为空，无需展开")}}catch(e){console.error("展开节点失败:",e)}})()}}),300)}))})),(e,l)=>{const a=t("el-button"),o=t("el-card"),k=t("el-tree"),w=t("el-col"),V=t("el-input"),b=t("el-form-item"),N=t("el-option"),G=t("el-select"),le=t("el-input-number"),ae=t("el-form"),oe=t("el-empty"),te=t("el-row"),re=t("el-dialog");return r(),n("div",x,[d(o,{class:"header-card mb-20"},{default:s((()=>[u("div",I,[l[14]||(l[14]=u("h2",{class:"form-title"},"产品分类管理",-1)),u("div",null,[d(a,{type:"primary",onClick:X},{default:s((()=>l[13]||(l[13]=[i("新增顶级分类")]))),_:1})])])])),_:1}),d(te,{gutter:20},{default:s((()=>[d(w,{span:10},{default:s((()=>[d(o,null,{header:s((()=>l[15]||(l[15]=[u("div",{class:"card-header"},[u("span",null,"分类列表")],-1)]))),default:s((()=>[(r(),c(k,{ref_key:"treeRef",ref:C,data:H.value,props:J,"node-key":"id","default-expand-all":"","highlight-current":"",onNodeClick:Q,key:S.value,class:"category-tree"},{default:s((({node:e,data:o})=>[u("div",P,[u("span",null,p(o.name),1),u("span",U,[o.level<2?(r(),c(a,{key:0,type:"primary",link:"",size:"small",onClick:v((e=>(e=>{if(L.value="addChild",!e||!e.id)return void f.error("无效的父级分类数据");if((e.level||1)>=3)return void f.warning("不允许创建超过三级的分类");const l=E.value.filter((l=>l.parent_id===e.id)).reduce(((e,l)=>Math.max(e,l.sort_order??0)),0)+1;D.value={name:"",parent_id:e.id,sort_order:l,description:"",notes:""},F.value=!0,y((()=>{R.value&&(R.value.resetFields(),D.value.parent_id=e.id,D.value.sort_order=l)}))})(o)),["stop"])},{default:s((()=>l[16]||(l[16]=[i(" 添加子分类 ")]))),_:2},1032,["onClick"])):m("",!0),d(a,{type:"primary",link:"",size:"small",onClick:v((e=>(e=>{M.value={...e},q.value={...e},B.value=!0})(o)),["stop"])},{default:s((()=>l[17]||(l[17]=[i(" 编辑 ")]))),_:2},1032,["onClick"]),d(a,{type:"danger",link:"",size:"small",onClick:v((l=>(async(e,l)=>{l.id&&(e.childNodes&&e.childNodes.length>0?f.warning("该分类下有子分类，请先删除子分类！"):await h.confirm(`确定要删除分类 "${l.name}" 吗？此操作不可恢复。`,"警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{var e,a;z.value=!0;try{await A(l.id),f.success("分类删除成功"),M.value.id===l.id&&(M.value={},q.value={name:"",parent_id:0,level:1,sort_order:1,description:"",notes:""},B.value=!1),await W()}catch(o){console.error("删除分类失败:",o);const l=(null==(a=null==(e=null==o?void 0:o.response)?void 0:e.data)?void 0:a.message)||o.message||"删除分类失败";f.error(l)}finally{z.value=!1}})).catch((()=>{f.info("取消删除")})))})(e,o)),["stop"])},{default:s((()=>l[18]||(l[18]=[i(" 删除 ")]))),_:2},1032,["onClick"])])])])),_:1},8,["data"]))])),_:1})])),_:1}),d(w,{span:14},{default:s((()=>[M.value.id?(r(),c(o,{key:0},{header:s((()=>[u("div",T,[u("span",null,p(B.value?"编辑分类":"分类详情"),1),B.value?m("",!0):(r(),c(a,{key:0,type:"primary",link:"",onClick:l[0]||(l[0]=e=>B.value=!0)},{default:s((()=>l[19]||(l[19]=[i(" 编辑 ")]))),_:1}))])])),default:s((()=>[d(ae,{ref_key:"formRef",ref:j,model:q.value,rules:K,"label-width":"100px",disabled:!B.value},{default:s((()=>[d(b,{label:"分类名称",prop:"name"},{default:s((()=>[d(V,{modelValue:q.value.name,"onUpdate:modelValue":l[1]||(l[1]=e=>q.value.name=e),placeholder:"请输入分类名称"},null,8,["modelValue"])])),_:1}),d(b,{label:"父级分类",prop:"parent_id"},{default:s((()=>[d(G,{modelValue:q.value.parent_id,"onUpdate:modelValue":l[2]||(l[2]=e=>q.value.parent_id=e),placeholder:"请选择父级分类",style:{width:"100%"},disabled:!B.value,clearable:""},{default:s((()=>[d(N,{label:"无父级分类 (设为顶级)",value:0}),(r(!0),n(_,null,g(E.value,(e=>(r(),c(N,{key:e.id,label:e.name,value:e.id,disabled:e.id===M.value.id},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1}),d(b,{label:"排序",prop:"sort_order"},{default:s((()=>[d(le,{modelValue:q.value.sort_order,"onUpdate:modelValue":l[3]||(l[3]=e=>q.value.sort_order=e),min:1,max:99,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),d(b,{label:"描述",prop:"description"},{default:s((()=>[d(V,{modelValue:q.value.description,"onUpdate:modelValue":l[4]||(l[4]=e=>q.value.description=e),type:"textarea",rows:3,placeholder:"请输入分类描述"},null,8,["modelValue"])])),_:1}),d(b,{label:"备注",prop:"notes"},{default:s((()=>[d(V,{modelValue:q.value.notes,"onUpdate:modelValue":l[5]||(l[5]=e=>q.value.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),B.value?(r(),c(b,{key:0},{default:s((()=>[d(a,{type:"primary",onClick:Z},{default:s((()=>l[20]||(l[20]=[i("保存")]))),_:1}),d(a,{onClick:ee},{default:s((()=>l[21]||(l[21]=[i("取消")]))),_:1})])),_:1})):m("",!0)])),_:1},8,["model","disabled"])])),_:1})):(r(),c(o,{key:1},{header:s((()=>l[22]||(l[22]=[u("div",{class:"card-header"},[u("span",null,"分类详情")],-1)]))),default:s((()=>[d(oe,{description:"请选择左侧分类查看详情"})])),_:1}))])),_:1})])),_:1}),d(re,{modelValue:F.value,"onUpdate:modelValue":l[12]||(l[12]=e=>F.value=e),title:"add"===L.value?"新增分类":"添加子分类",width:"500px"},{footer:s((()=>[u("span",$,[d(a,{onClick:l[11]||(l[11]=e=>F.value=!1)},{default:s((()=>l[23]||(l[23]=[i("取消")]))),_:1}),d(a,{type:"primary",onClick:Y},{default:s((()=>l[24]||(l[24]=[i(" 确认 ")]))),_:1})])])),default:s((()=>[d(ae,{ref_key:"dialogFormRef",ref:R,model:D.value,rules:K,"label-width":"100px"},{default:s((()=>[d(b,{label:"分类名称",prop:"name"},{default:s((()=>[d(V,{modelValue:D.value.name,"onUpdate:modelValue":l[6]||(l[6]=e=>D.value.name=e),placeholder:"请输入分类名称"},null,8,["modelValue"])])),_:1}),d(b,{label:"父级分类",prop:"parent_id"},{default:s((()=>["addChild"===L.value?(r(),c(V,{key:0,value:O.value,readonly:""},null,8,["value"])):(r(),c(G,{key:1,modelValue:D.value.parent_id,"onUpdate:modelValue":l[7]||(l[7]=e=>D.value.parent_id=e),placeholder:"请选择父级分类 (可选)",style:{width:"100%"},clearable:""},{default:s((()=>[d(N,{label:"无父级分类 (设为顶级)",value:0}),(r(!0),n(_,null,g(E.value,(e=>(r(),c(N,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]))])),_:1}),d(b,{label:"排序",prop:"sort_order"},{default:s((()=>[d(le,{modelValue:D.value.sort_order,"onUpdate:modelValue":l[8]||(l[8]=e=>D.value.sort_order=e),min:1,max:99,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),d(b,{label:"描述",prop:"description"},{default:s((()=>[d(V,{modelValue:D.value.description,"onUpdate:modelValue":l[9]||(l[9]=e=>D.value.description=e),type:"textarea",rows:3,placeholder:"请输入分类描述"},null,8,["modelValue"])])),_:1}),d(b,{label:"备注",prop:"notes"},{default:s((()=>[d(V,{modelValue:D.value.notes,"onUpdate:modelValue":l[10]||(l[10]=e=>D.value.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-10a1c5b5"]]);export{j as default};
