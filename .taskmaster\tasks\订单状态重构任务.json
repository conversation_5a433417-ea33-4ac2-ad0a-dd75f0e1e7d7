{"master": {"tasks": [{"id": 1, "title": "项目启动与开发环境准备", "description": "初始化项目仓库，配置开发环境，并确保所有团队成员都能访问和使用。此步骤是所有后续开发工作的基础。", "details": "1. 创建新的Git仓库，并设置好分支策略（例如：main, develop, feature branches）。\n2. 配置Python后端开发环境：建议使用Python 3.9+，安装虚拟环境（如`venv`），并安装项目依赖（如Flask, SQLAlchemy, Marshmallow）。推荐使用`pip-tools`管理依赖，生成`requirements.txt`和`requirements.dev.txt`。\n3. 配置Vue.js前端开发环境：安装Node.js (LTS版本，如v20.x)，npm或yarn，并安装Vue CLI或Vite。\n4. 确保数据库服务（如PostgreSQL或MySQL）已启动并可连接，创建开发数据库。\n5. 验证所有工具链和依赖项是否正确安装并可运行。", "testStrategy": "1. 团队成员成功克隆仓库并设置本地开发环境。\n2. 后端服务能够成功启动并连接到数据库。\n3. 前端应用能够成功编译并运行在浏览器中。\n4. 运行简单的“Hello World”或健康检查API，确保基本功能正常。", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "数据库备份与迁移脚本开发", "description": "根据PRD要求，设计并编写数据库迁移脚本，用于将现有`status`字段的数据迁移到新的`order_status`字段，并为`order_status`和`payment_status`字段添加新的数据库列。", "details": "1. **数据库备份**: 在执行任何迁移前，务必对现有生产数据库进行完整备份，以防数据丢失或迁移失败。\n2. **迁移工具选择**: 鉴于后端使用Python和SQLAlchemy，推荐使用Alembic (版本1.13.1) 作为数据库迁移工具。它能更好地管理数据库模式变更。\n3. **创建迁移脚本**: 使用Alembic生成一个新的迁移文件。该脚本应包含以下SQL操作：\n   - **添加新列**: `ALTER TABLE orders ADD COLUMN order_status VARCHAR(20) NOT NULL DEFAULT '待确认';` 和 `ALTER TABLE orders ADD COLUMN payment_status VARCHAR(20) NOT NULL DEFAULT '未收款';`\n   - **数据迁移**: 按照PRD提供的逻辑，将旧的`status`数据映射到`order_status`。\n     ```sql\n     UPDATE orders\n     SET order_status = CASE\n         WHEN status IN ('待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消') THEN status\n         WHEN status IN ('待对账', '部分对账', '全部对账') THEN '全部发货'\n         WHEN status IN ('待收款', '部分收款') THEN '已完成'\n         ELSE '待确认'\n     END\n     WHERE order_status IS NULL OR order_status = '';\n     ```\n   - **保留旧字段**: 暂时不删除`status`字段，以备回滚或兼容性需求。\n4. **回滚脚本**: 准备相应的回滚脚本，以便在迁移出现问题时能够恢复到之前的状态。", "testStrategy": "1. 迁移脚本语法检查，确保无SQL错误。\n2. 在开发环境中运行迁移脚本，验证新字段是否成功添加。\n3. 验证数据迁移逻辑是否正确，抽样检查部分订单的`order_status`是否符合预期映射规则。\n4. 尝试运行回滚脚本，验证数据库能否恢复到迁移前的状态。", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "执行数据库迁移与数据验证", "description": "在开发环境中执行数据库迁移脚本，将新的`order_status`和`payment_status`字段添加到`orders`表，并完成现有数据的迁移。", "details": "1. **执行迁移**: 使用Alembic命令行工具执行上一步创建的迁移脚本，例如 `alembic upgrade head`。\n2. **数据验证**: 迁移完成后，通过SQL查询或ORM工具检查`orders`表：\n   - 确认`order_status`和`payment_status`两列已成功添加。\n   - 随机抽取大量订单数据，验证`order_status`字段的值是否按照PRD的迁移规则正确填充。\n   - 确认`payment_status`字段的默认值是否为'未收款'。\n3. **备份旧字段**: 确认`status`字段仍然存在，作为临时备份。", "testStrategy": "1. 检查数据库表结构，确认`orders`表包含`order_status`和`payment_status`字段。\n2. 编写SQL查询，验证`order_status`字段的数据迁移准确性，特别是针对旧的对账和收款状态的映射。\n3. 验证`payment_status`字段的默认值是否正确。\n4. 检查数据库日志，确保迁移过程中没有出现错误或警告。", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 4, "title": "添加数据库约束与索引优化", "description": "为新添加的`order_status`和`payment_status`字段添加数据库`CHECK`约束，以确保数据完整性，并考虑为这些字段添加索引以优化查询性能。", "details": "1. **添加CHECK约束**: 在Alembic迁移脚本中添加或修改现有脚本，为`order_status`和`payment_status`字段添加`CHECK`约束，限制其只能包含PRD中定义的状态值。这有助于在数据库层面强制数据有效性。\n   ```sql\n   ALTER TABLE orders ADD CONSTRAINT chk_order_status\n   CHECK (order_status IN ('待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消'));\n\n   ALTER TABLE orders ADD CONSTRAINT chk_payment_status\n   CHECK (payment_status IN ('未收款', '部分收款', '已收款'));\n   ```\n2. **添加索引**: 为`order_status`和`payment_status`字段添加数据库索引，以提高基于这些状态的查询和筛选性能。例如：\n   ```sql\n   CREATE INDEX idx_orders_order_status ON orders (order_status);\n   CREATE INDEX idx_orders_payment_status ON orders (payment_status);\n   ```\n3. **验证约束和索引**: 尝试插入或更新无效状态值，验证`CHECK`约束是否生效。通过数据库管理工具检查索引是否已创建。", "testStrategy": "1. 尝试通过SQL语句插入或更新一个无效的`order_status`或`payment_status`值，验证数据库是否拒绝该操作并抛出约束错误。\n2. 检查数据库的索引列表，确认`idx_orders_order_status`和`idx_orders_payment_status`索引已成功创建。\n3. 运行一些基于状态的查询，初步评估索引对查询性能的提升效果。", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 5, "title": "修改后端订单模型定义", "description": "修改后端`backend/app/models/order.py`中的订单模型定义，移除或标记废弃`status`字段，并确保`order_status`和`payment_status`成为主要字段。", "details": "1. **移除/废弃`status`字段**: 在`Order`模型中，将`status = db.Column(db.String(20), ...)`行注释掉或删除，并添加注释说明其已废弃。\n2. **定义新状态字段**: 确保`order_status`和`payment_status`字段已正确定义为`db.Column(db.String(20), nullable=False, default=...)`，并带有相应的注释。\n3. **添加兼容性属性**: 实现`@property`装饰器下的`status`方法，使其返回`self.order_status`，以提供对旧API的读取兼容性。\n   ```python\n   # backend/app/models/order.py\n   from app import db # 假设db是SQLAlchemy实例\n\n   class Order(db.Model):\n       # ... 其他字段\n       order_status = db.Column(db.String(20), nullable=False, default='待确认', comment='物流状态')\n       payment_status = db.Column(db.String(20), nullable=False, default='未收款', comment='财务状态')\n\n       # 兼容性属性\n       @property\n       def status(self):\n           return self.order_status\n\n       # ... 移除或标记废弃status相关的方法\n   ```\n4. **移除旧方法**: 删除或修改任何直接操作`status`字段的方法，确保所有状态逻辑都通过`order_status`和`payment_status`进行。", "testStrategy": "1. 启动后端服务，确保模型加载无错误。\n2. 编写单元测试，验证`Order`模型实例的`order_status`和`payment_status`字段能够正确存取。\n3. 验证`order.status`属性能够正确返回`order.order_status`的值。\n4. 检查旧的`status`相关方法是否已被移除或不再被调用。", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "更新后端订单序列化Schema", "description": "修改后端`backend/app/schemas/order.py`中的订单序列化Schema，完全移除`status`字段定义，并只保留`order_status`和`payment_status`字段。同时，添加一个计算字段`combined_status`用于前端显示。", "details": "1. **移除`status`字段**: 在`OrderSchema`中删除`status = fields.String(...)`的定义。\n2. **定义新状态字段**: 确保`order_status`和`payment_status`字段已正确定义，并使用`validate.OneOf`进行值验证。\n   ```python\n   # backend/app/schemas/order.py\n   from marshmallow import Schema, fields, validate\n\n   class OrderSchema(Schema):\n       # ... 其他字段\n       order_status = fields.String(\n           validate=validate.OneOf([\n               '待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消'\n           ]),\n           load_default='待确认'\n       )\n       payment_status = fields.String(\n           validate=validate.OneOf(['未收款', '部分收款', '已收款']),\n           load_default='未收款'\n       )\n\n       # 添加计算字段用于前端显示\n       combined_status = fields.Method('get_combined_status', dump_only=True)\n\n       def get_combined_status(self, obj):\n           return f'{obj.order_status} / {obj.payment_status}'\n   ```\n3. **添加`combined_status`计算字段**: 实现`get_combined_status`方法，用于组合物流和收款状态，方便前端显示。", "testStrategy": "1. 编写单元测试，使用`OrderSchema`序列化一个订单对象，验证输出JSON中不包含`status`字段，但包含`order_status`、`payment_status`和`combined_status`。\n2. 验证`order_status`和`payment_status`的`validate.OneOf`规则是否生效，尝试序列化包含无效状态值的对象，并检查是否抛出验证错误。\n3. 验证`combined_status`字段的输出格式是否正确，例如“待确认 / 未收款”。", "priority": "high", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 7, "title": "重构订单状态更新API", "description": "重构后端API层`backend/app/api/v1/orders.py`，移除所有对`status`字段的处理逻辑，并创建独立的API端点来更新`order_status`和`payment_status`。", "details": "1. **移除旧`status`处理**: 删除所有涉及`data.get('status')`或`order.status = new_status`的代码。\n2. **创建`OrderStatusResource`**: 实现`/api/v1/orders/<int:order_id>/order-status` PUT接口，专门用于更新订单的物流状态。\n   - 接收`order_status`参数。\n   - 实现PRD中定义的物流状态流转规则（`order_status_transitions`），进行严格的状态验证。\n   - 确保一旦进入'部分发货'及以后状态，不允许回退或取消。\n   ```python\n   # backend/app/api/v1/orders.py\n   from flask_restful import Resource\n   from flask import request\n   # ... 导入Order模型和Schema\n\n   order_status_transitions = {\n       '待确认': ['已确认', '生产中', '待发货', '已取消'],\n       '已确认': ['待确认', '生产中', '待发货', '已取消'],\n       '生产中': ['待确认', '已确认', '待发货', '部分发货', '已取消'],\n       '待发货': ['待确认', '已确认', '生产中', '部分发货', '已取消'],\n       '部分发货': ['全部发货'],\n       '全部发货': ['已完成'],\n       '已完成': [],\n       '已取消': []\n   }\n\n   class OrderStatusResource(Resource):\n       def put(self, order_id):\n           order = Order.query.get_or_404(order_id)\n           data = request.get_json()\n           new_status = data.get('order_status')\n\n           if not new_status or new_status not in VALID_ORDER_STATUSES:\n               return {'message': '无效的物流状态'}, 400\n\n           # 验证状态流转\n           if new_status not in order_status_transitions.get(order.order_status, []):\n               return {'message': f'不允许从 {order.order_status} 流转到 {new_status}'}, 400\n\n           order.order_status = new_status\n           db.session.commit()\n           return OrderSchema().dump(order), 200\n   ```\n3. **创建`PaymentStatusResource`**: 实现`/api/v1/orders/<int:order_id>/payment-status` PUT接口，用于更新订单的收款状态。此接口通常由系统内部调用，或在收款记录更新时触发，而不是直接由用户手动调用（详见任务8）。\n   - 接收`payment_status`参数。\n   - 验证`payment_status`是否在`VALID_PAYMENT_STATUSES`中。\n   - **注意**: PRD指出收款状态是自动计算的，因此此API可能主要用于内部系统或管理员强制更新，正常业务流程中应由收款逻辑触发。", "testStrategy": "1. **物流状态API测试**: \n   - 测试有效状态流转：例如从'待确认'到'已确认'，从'待发货'到'部分发货'。\n   - 测试无效状态流转：例如从'部分发货'到'待发货'，或从'已完成'到'已取消'。\n   - 测试取消逻辑：在允许取消的状态下取消订单，并验证不可取消状态下无法取消。\n   - 测试无效输入：传入不在`VALID_ORDER_STATUSES`列表中的状态值，验证API返回400错误。\n2. **收款状态API测试**: \n   - 测试有效状态更新：例如从'未收款'到'部分收款'。\n   - 测试无效输入：传入不在`VALID_PAYMENT_STATUSES`列表中的状态值，验证API返回400错误。\n   - 验证此API是否能被正确调用，但其主要逻辑应由收款计算触发。", "priority": "high", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "实现收款状态自动计算逻辑", "description": "实现订单收款状态的自动计算逻辑，根据`paid_amount`和`total_amount`自动更新`payment_status`字段，确保无需手动维护。", "details": "1. **确定触发点**: 收款状态的自动计算应在以下场景触发：\n   - 订单创建时（初始化为'未收款'）。\n   - 订单的`paid_amount`（已收款金额）发生变化时，例如新增收款记录或修改收款记录。\n   - 订单的`total_amount`（总金额）发生变化时。\n2. **实现计算逻辑**: 在`Order`模型中添加一个方法或在服务层中实现一个函数，根据`paid_amount`和`total_amount`计算`payment_status`。\n   ```python\n   # backend/app/models/order.py (或一个服务层文件)\n   class Order(db.Model):\n       # ... 其他字段\n       paid_amount = db.Column(db.Numeric(10, 2), default=0.00)\n       total_amount = db.Column(db.Numeric(10, 2), default=0.00)\n\n       def calculate_payment_status(self):\n           if self.paid_amount >= self.total_amount and self.total_amount > 0:\n               self.payment_status = '已收款'\n           elif self.paid_amount > 0 and self.paid_amount < self.total_amount:\n               self.payment_status = '部分收款'\n           else:\n               self.payment_status = '未收款'\n           db.session.add(self)\n           db.session.commit()\n   ```\n3. **集成到业务流程**: 在处理收款记录创建、更新或删除的API中，调用`order.calculate_payment_status()`方法，确保订单的`payment_status`始终保持最新。", "testStrategy": "1. **单元测试**: 编写测试用例，模拟不同`paid_amount`和`total_amount`组合，验证`calculate_payment_status`方法是否返回正确的`payment_status`（'未收款', '部分收款', '已收款'）。\n2. **集成测试**: \n   - 创建一个新订单，验证其`payment_status`默认为'未收款'。\n   - 模拟添加一笔收款记录，使`paid_amount`变为`total_amount`的50%，验证`payment_status`变为'部分收款'。\n   - 模拟添加另一笔收款记录，使`paid_amount`达到或超过`total_amount`，验证`payment_status`变为'已收款'。\n   - 模拟退款或修改收款记录，验证`payment_status`是否正确回退（例如从'已收款'变为'部分收款'）。", "priority": "high", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 9, "title": "完善订单查询与筛选API", "description": "修改后端API层，更新订单查询和筛选逻辑，使其能够根据新的`order_status`和`payment_status`字段进行筛选，并移除对旧`status`字段的筛选支持。", "details": "1. **修改查询参数**: 在订单列表查询API（例如`/api/v1/orders` GET）中，将`status`查询参数替换为`order_status`和`payment_status`。\n2. **更新筛选逻辑**: 根据新的查询参数构建SQLAlchemy查询过滤器。\n   ```python\n   # backend/app/api/v1/orders.py\n   # ...\n   class OrderListResource(Resource):\n       def get(self):\n           query = Order.query\n\n           # 按物流状态筛选\n           if order_status := request.args.get('order_status'):\n               if order_status not in VALID_ORDER_STATUSES:\n                   return {'message': '无效的物流状态筛选值'}, 400\n               query = query.filter(Order.order_status == order_status)\n\n           # 按收款状态筛选\n           if payment_status := request.args.get('payment_status'):\n               if payment_status not in VALID_PAYMENT_STATUSES:\n                   return {'message': '无效的收款状态筛选值'}, 400\n               query = query.filter(Order.payment_status == payment_status)\n\n           # 移除status相关的筛选\n           # if status := request.args.get('status'): # ❌ 删除\n\n           # ... 分页和序列化逻辑\n   ```\n3. **移除旧筛选**: 确保所有对旧`status`字段的筛选逻辑已被完全移除。", "testStrategy": "1. **API测试**: \n   - 调用订单列表API，不带任何状态筛选参数，验证返回所有订单。\n   - 调用订单列表API，带`order_status`参数（例如`?order_status=待确认`），验证只返回符合该物流状态的订单。\n   - 调用订单列表API，带`payment_status`参数（例如`?payment_status=已收款`），验证只返回符合该收款状态的订单。\n   - 调用订单列表API，同时带`order_status`和`payment_status`参数，验证组合筛选结果正确。\n   - 尝试传入无效的状态筛选值，验证API返回400错误。\n   - 尝试传入旧的`status`筛选参数，验证API不再支持或返回错误。", "priority": "medium", "dependencies": [8], "status": "pending", "subtasks": []}, {"id": 10, "title": "后端单元测试与集成测试", "description": "对后端所有修改过的模型、Schema和API进行全面的单元测试和集成测试，确保重构后的后端逻辑正确无误，且符合PRD要求。", "details": "1. **单元测试**: \n   - **模型测试**: 验证`Order`模型的新字段和兼容性属性。\n   - **Schema测试**: 验证`OrderSchema`的序列化、反序列化和验证逻辑。\n   - **状态流转规则测试**: 针对`order_status_transitions`定义的所有流转路径进行测试，包括合法和非法的流转。\n   - **收款状态计算测试**: 针对`calculate_payment_status`方法的所有边界条件进行测试。\n2. **集成测试**: \n   - **API端点测试**: 使用测试客户端模拟HTTP请求，测试所有新的和修改过的API端点（如订单创建、更新物流状态、更新收款状态、查询列表）。\n   - **数据库交互测试**: 验证API操作是否正确地持久化到数据库，并且数据一致性得到维护。\n   - **错误处理测试**: 验证API在接收无效输入或发生业务逻辑错误时是否返回正确的HTTP状态码和错误信息。\n3. **测试框架**: 推荐使用`pytest` (版本8.2.2) 进行单元和集成测试，结合`pytest-flask`和`pytest-mock`等插件。", "testStrategy": "1. 运行所有后端单元测试，确保所有测试用例通过。\n2. 运行所有后端集成测试，模拟真实API调用场景，验证业务逻辑和数据流转的正确性。\n3. 检查测试覆盖率报告，确保核心业务逻辑和状态管理代码得到充分测试。\n4. 针对关键业务场景编写端到端测试脚本，验证从请求到数据库持久化的完整流程。", "priority": "high", "dependencies": [9], "status": "pending", "subtasks": []}, {"id": 11, "title": "前端订单列表状态显示重构", "description": "修改前端`frontend/src/views/orders/OrderList.vue`组件，移除对旧`status`字段的依赖，并使用新的`order_status`和`payment_status`字段来显示订单状态。", "details": "1. **移除旧状态引用**: 在`OrderList.vue`中，删除所有直接引用`row.status`的代码。\n2. **使用双状态字段**: 将订单数据显示逻辑修改为使用`row.order_status`和`row.payment_status`。\n3. **更新状态显示映射**: 根据PRD提供的`orderStatusMap`和`paymentStatusMap`，更新前端的状态显示逻辑，可能使用Vue的计算属性或方法来渲染。\n   ```javascript\n   // frontend/src/views/orders/OrderList.vue\n   // ...\n   const orderStatusMap = {\n     '待确认': { type: 'info', text: '待确认' },\n     // ... 其他物流状态\n   }\n\n   const paymentStatusMap = {\n     '未收款': { type: 'danger', text: '未收款' },\n     // ... 其他收款状态\n   }\n\n   // 在模板中显示\n   // <el-tag :type=\"orderStatusMap[row.order_status].type\">{{ orderStatusMap[row.order_status].text }}</el-tag>\n   // <el-tag :type=\"paymentStatusMap[row.payment_status].type\">{{ paymentStatusMap[row.payment_status].text }}</el-tag>\n   ```\n4. **组合状态显示**: 如果需要，可以使用`combined_status`字段（由后端Schema提供）或在前端组合显示，例如`{{ row.order_status }} / {{ row.payment_status }}`。", "testStrategy": "1. 访问前端订单列表页面，目视检查每个订单的状态显示是否正确，是否同时显示物流状态和收款状态。\n2. 验证不同状态的订单（例如'待确认'/'未收款'，'全部发货'/'已收款'）是否能正确渲染其对应的样式和文本。\n3. 检查控制台是否有关于旧`status`字段的警告或错误。", "priority": "high", "dependencies": [10], "status": "pending", "subtasks": []}, {"id": 12, "title": "前端状态筛选与操作按钮逻辑调整", "description": "修改前端`frontend/src/views/orders/OrderList.vue`组件，更新状态筛选选项，并调整操作按钮（如取消、状态变更）的显示和可用性逻辑，使其基于新的双状态系统。", "details": "1. **更新状态筛选选项**: \n   - 移除旧的`status`筛选下拉框或选项。\n   - 添加独立的物流状态筛选选项（`orderStatusOptions`）和收款状态筛选选项（`paymentStatusOptions`）。\n   ```javascript\n   // frontend/src/views/orders/OrderList.vue\n   const orderStatusOptions = [\n     { label: '全部', value: '' },\n     { label: '待确认', value: '待确认' },\n     // ... 其他物流状态选项\n   ]\n\n   const paymentStatusOptions = [\n     { label: '全部', value: '' },\n     { label: '未收款', value: '未收款' },\n     // ... 其他收款状态选项\n   ]\n   ```\n2. **调整操作按钮逻辑**: \n   - **取消按钮**: 修改`canCancel`函数，使其只基于`order_status`判断，并符合PRD中'部分发货'及以后不可取消的规则。\n     ```javascript\n     const canCancel = (orderStatus) => {\n       return ['待确认', '已确认', '生产中', '待发货'].includes(orderStatus)\n     }\n     ```\n   - **状态变更按钮**: 调整其他状态变更按钮的显示逻辑，确保它们分别针对`order_status`或`payment_status`，并遵循各自的流转规则。\n3. **表单绑定**: 确保筛选表单元素正确绑定到新的数据模型，并在筛选条件变化时触发API调用。", "testStrategy": "1. **筛选功能测试**: \n   - 使用物流状态筛选器，选择不同状态，验证列表数据是否正确更新。\n   - 使用收款状态筛选器，选择不同状态，验证列表数据是否正确更新。\n   - 同时使用两个筛选器，验证组合筛选结果。\n2. **操作按钮测试**: \n   - 针对不同`order_status`的订单，验证“取消”按钮的显示/隐藏逻辑是否正确。\n   - 验证其他状态变更按钮（如果存在）的显示逻辑是否符合预期。\n   - 尝试点击“取消”按钮，验证其是否触发正确的API调用并更新订单状态。", "priority": "medium", "dependencies": [11], "status": "pending", "subtasks": []}, {"id": 13, "title": "前端API调用适配与功能测试", "description": "修改前端`frontend/src/views/orders/OrderList.vue`组件中的API调用逻辑，使其适配后端新的状态更新API，并移除对旧`status`字段的API调用。", "details": "1. **更新状态更新API调用**: \n   - 移除所有调用旧的`updateOrderStatus(orderId, status)`或类似API的代码。\n   - 修改为调用新的`/api/v1/orders/<order_id>/order-status` API来更新物流状态。\n   ```javascript\n   // frontend/src/api/order.js (或类似文件)\n   import request from '@/utils/request'\n\n   export function updateOrderStatus(orderId, newOrderStatus) {\n     return request({\n       url: `/api/v1/orders/${orderId}/order-status`,\n       method: 'put',\n       data: { order_status: newOrderStatus }\n     })\n   }\n   // 收款状态通常由后端自动计算，前端无需手动调用更新API\n   ```\n2. **更新订单列表查询API调用**: 确保订单列表的API调用（例如获取订单列表）已更新为使用`order_status`和`payment_status`作为查询参数。\n3. **前端功能测试**: \n   - 模拟用户操作，如修改订单物流状态，验证前端界面更新和后端数据持久化是否一致。\n   - 验证收款状态的自动更新（通过后端模拟收款操作，然后刷新前端查看）。", "testStrategy": "1. **物流状态更新**: 在前端界面上尝试修改订单的物流状态（例如从'待确认'到'已确认'），验证API调用是否成功，后端数据是否更新，并且前端界面是否实时反映了新的状态。\n2. **收款状态显示**: 模拟后端收款操作（例如通过Postman直接调用后端收款API），然后刷新前端订单列表，验证订单的收款状态是否正确显示。\n3. **筛选API调用**: 使用前端筛选器，验证发出的API请求参数是否正确（`order_status`和`payment_status`），并且返回的数据符合筛选条件。\n4. **错误处理**: 尝试在前端触发无效的状态流转，验证后端返回的错误信息是否能被前端正确捕获并提示用户。", "priority": "high", "dependencies": [12], "status": "pending", "subtasks": []}, {"id": 14, "title": "全链路集成测试与性能优化", "description": "执行全面的端到端集成测试，验证整个系统的状态流转、数据一致性和业务逻辑是否符合PRD要求。同时，进行性能测试和优化。", "details": "1. **端到端测试**: \n   - 使用Cypress (版本13.13.1) 或Playwright (版本1.44.1) 等工具编写自动化测试脚本，模拟真实用户操作。\n   - 覆盖所有关键业务流程：订单创建、物流状态变更（所有合法流转路径）、收款状态自动计算（通过模拟收款）、订单查询与筛选、订单取消等。\n   - 验证前端界面、后端API和数据库之间的数据一致性。\n2. **状态流转测试**: 重点测试PRD中定义的所有物流状态流转规则，包括前期状态的互相转换、发货状态的单向流转，以及不可取消的规则。\n3. **数据一致性测试**: 验证在各种操作后，`order_status`和`payment_status`在数据库、后端API响应和前端显示之间保持一致。\n4. **性能测试**: \n   - 使用JMeter (版本5.6.3) 或Locust (版本2.21.0) 对核心API进行负载测试，评估在并发用户下的响应时间、吞吐量和错误率。\n   - 识别性能瓶颈，并进行相应的优化（例如，数据库查询优化、缓存策略、代码重构）。", "testStrategy": "1. 运行所有端到端自动化测试脚本，确保所有业务流程都能顺畅执行，无功能性缺陷。\n2. 针对高并发场景进行性能测试，记录关键API的响应时间、吞吐量和资源利用率，并与基线进行对比。\n3. 随机抽样检查生产环境（或预生产环境）数据，验证状态字段的正确性和一致性。\n4. 组织内部用户进行UAT（用户验收测试），收集反馈并修复问题。", "priority": "high", "dependencies": [13], "status": "pending", "subtasks": []}, {"id": 15, "title": "部署与旧字段清理", "description": "将重构后的订单状态系统部署到生产环境，并进行用户验收测试。在确认系统稳定运行后，考虑清理数据库中废弃的`status`字段，并更新相关文档。", "details": "1. **生产环境部署**: \n   - 制定详细的部署计划，包括部署步骤、回滚策略和监控方案。\n   - 在非高峰期进行部署，并密切关注系统日志和性能指标。\n   - 确保所有新的API端点和前端功能在生产环境中正常工作。\n2. **用户验收测试 (UAT)**: 邀请业务用户对新系统进行全面测试，确保其符合业务需求和预期。\n3. **清理废弃字段 (可选)**: 在系统稳定运行一段时间，并确认不再需要旧`status`字段进行兼容性或回滚后，执行数据库迁移脚本删除该字段。\n   ```sql\n   -- ALTER TABLE orders DROP COLUMN status;\n   ```\n   **注意**: 此操作不可逆，务必谨慎。\n4. **文档更新**: \n   - 更新API文档，明确新的状态字段和API端点。\n   - 更新内部技术文档，记录重构的细节和设计决策。\n   - 更新用户手册或操作指南，培训用户了解新的状态流转规则和界面变化。", "testStrategy": "1. **部署验证**: 部署后立即进行冒烟测试，验证核心功能是否可用。\n2. **生产监控**: 持续监控系统日志、错误率、性能指标，确保系统稳定运行。\n3. **UAT反馈**: 收集并处理用户验收测试中的所有反馈和问题。\n4. **旧字段清理验证**: 如果执行了旧字段清理，验证数据库中`status`字段是否已成功删除，且系统功能不受影响。\n5. **文档审查**: 确保所有相关文档已更新并准确反映当前系统状态。", "priority": "medium", "dependencies": [14], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-06T10:26:03.349Z", "updated": "2025-07-06T10:26:03.349Z", "description": "Tasks for master context"}}}