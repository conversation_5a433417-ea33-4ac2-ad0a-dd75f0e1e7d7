#!/usr/bin/env python3
"""
修复客户删除问题 - 删除阻止删除的关联数据
"""
import sqlite3
import os

def fix_customer_delete():
    """修复客户删除问题"""
    db_path = r'D:\code\EMB-new\backend\instance\project.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 修复客户删除问题")
        print("=" * 50)
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 检查客户ID 13的应收账款记录
        customer_id = 13
        cursor.execute("SELECT * FROM receivables WHERE customer_id = ?", (customer_id,))
        receivables = cursor.fetchall()
        
        print(f"📋 客户ID {customer_id} 的应收账款记录:")
        for record in receivables:
            print(f"  应收账款ID: {record[0]}, 金额: {record[2]}")
        
        if receivables:
            print(f"\n🗑️  删除 {len(receivables)} 条应收账款记录...")
            cursor.execute("DELETE FROM receivables WHERE customer_id = ?", (customer_id,))
            print("✅ 应收账款记录删除成功")
        
        # 检查其他可能的关联数据
        tables_to_check = [
            'customer_balances',
            'balance_transactions', 
            'receivable_details',
            'statements',
            'statement_receivables'
        ]
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE customer_id = ?", (customer_id,))
                count = cursor.fetchone()[0]
                if count > 0:
                    print(f"🗑️  删除 {table} 中的 {count} 条记录...")
                    cursor.execute(f"DELETE FROM {table} WHERE customer_id = ?", (customer_id,))
                    print(f"✅ {table} 记录删除成功")
            except Exception as e:
                print(f"⚠️  检查 {table} 失败: {str(e)}")
        
        # 现在尝试删除客户
        print(f"\n🧪 尝试删除客户ID {customer_id}...")
        
        try:
            cursor.execute("DELETE FROM customers WHERE id = ?", (customer_id,))
            
            # 检查是否删除成功
            cursor.execute("SELECT COUNT(*) FROM customers WHERE id = ?", (customer_id,))
            customer_exists = cursor.fetchone()[0]
            
            if customer_exists == 0:
                print("✅ 客户删除成功！")
                conn.commit()
                return True
            else:
                print("❌ 客户删除失败")
                conn.rollback()
                return False
                
        except Exception as e:
            print(f"❌ 删除客户失败: {str(e)}")
            conn.rollback()
            return False
        
    except Exception as e:
        print(f"❌ 修复过程出错: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_api_delete():
    """测试API删除功能"""
    import requests
    
    print("\n🌐 测试API删除功能")
    print("=" * 50)
    
    try:
        # 获取客户列表
        response = requests.get('http://localhost:5001/api/v1/customers')
        if response.status_code != 200:
            print(f"❌ 获取客户列表失败: {response.status_code}")
            return False
        
        customers = response.json()['data']
        
        # 找一个测试客户
        test_customer = None
        for customer in customers:
            if customer['name'].startswith('测试') or customer['name'] in ['师傅丰富的撒', '第三方']:
                test_customer = customer
                break
        
        if not test_customer:
            print("⚠️  没有找到合适的测试客户")
            return False
        
        customer_id = test_customer['id']
        print(f"📋 测试删除客户: ID={customer_id}, 名称={test_customer['name']}")
        
        # 发送删除请求
        delete_response = requests.delete(f'http://localhost:5001/api/v1/customers/{customer_id}')
        
        print(f"API响应状态: {delete_response.status_code}")
        
        if delete_response.status_code == 200:
            result = delete_response.json()
            print(f"API响应: {result.get('message', '删除成功')}")
            print("✅ API删除成功！")
            return True
        else:
            error_info = delete_response.text
            print(f"API错误信息: {error_info}")
            print("❌ API删除失败")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 先修复数据库问题
    db_success = fix_customer_delete()
    
    if db_success:
        print("\n🎉 数据库层面修复成功！")
        
        # 测试API删除功能
        api_success = test_api_delete()
        
        if api_success:
            print("\n🎉 客户删除功能完全修复！")
        else:
            print("\n⚠️  数据库修复成功，但API可能还有其他问题")
    else:
        print("\n❌ 数据库层面修复失败")
