# Excel导入功能使用指南

## 功能概述

订单管理系统支持通过Excel文件批量导入产品信息，大大提高了订单创建的效率。该功能支持完整的数据验证、错误提示和格式检查。

## 使用步骤

### 1. 下载模板

1. 进入"新增订单"页面
2. 在"产品明细"卡片中，点击"Excel导入"按钮
3. 在弹出的对话框中，点击"下载模板"链接
4. 系统会自动下载"订单产品导入模板.xlsx"文件

### 2. 填写模板

下载的模板包含两个工作表：

#### 产品明细模板（主要工作表）
- **表头行**：包含所有字段名称，请勿修改
- **示例行**：提供了4行示例数据，可以参考格式
- **数据行**：在示例数据下方添加您的产品信息

#### 使用说明（参考工作表）
- 详细的字段说明
- 填写注意事项
- 常见问题解答

### 3. 字段说明

#### 必填字段（标有*号）
- **产品名称***：产品的完整名称，不能为空
- **单位***：计量单位（个、台、套、米、根、件、箱等）
- **数量***：订购数量，必须为正数
- **单价***：产品单价，必须为正数

#### 可选字段
- **型号**：产品型号或规格代码
- **规格**：产品的详细规格描述
- **折扣率(%)**：折扣百分比，如10表示10%折扣，默认为0
- **税率(%)**：税率百分比，如13表示13%税率，默认为13
- **备注**：产品相关的备注信息

### 4. 导入数据

1. 填写完成后保存Excel文件
2. 在"Excel导入"对话框中点击"选择文件"
3. 选择您填写好的Excel文件
4. 点击"导入"按钮
5. 系统会自动解析并验证数据

### 5. 结果处理

#### 成功导入
- 显示成功导入的产品数量
- 产品会自动添加到订单明细表格中
- 自动计算含税、折扣后的总价

#### 导入错误
- 显示具体的错误信息和行号
- 跳过有问题的行，导入有效数据
- 提供详细的错误报告

## 数据验证规则

### 必填字段验证
- 产品名称不能为空
- 单位不能为空
- 数量必须大于0
- 单价必须大于0

### 数据格式验证
- 数量和单价必须为有效数字
- 折扣率范围：0-100
- 税率范围：0-100

### 数据处理
- 空行自动跳过
- 数字字段自动转换格式
- 总价自动计算（含税、折扣）
- 金额保留两位小数

## 示例数据

```
产品名称*          | 型号        | 规格                    | 单位* | 数量* | 单价*   | 折扣率(%) | 税率(%) | 备注
工业控制器         | PLC-001     | 24V/16点输入输出        | 台    | 5     | 1200.00 | 5         | 13      | 主控制器，含配套软件
传感器            | SENSOR-002  | 温度传感器 -40~85℃      | 个    | 10    | 150.00  | 0         | 13      | 高精度温度检测
电缆              | CABLE-003   | 4芯屏蔽电缆 AWG18       | 米    | 100   | 8.50    | 10        | 13      | 工业级屏蔽电缆
开关电源          | PSU-004     | 24V/10A 开关电源        | 个    | 2     | 280.00  | 0         | 13      | 工业级开关电源
```

## 常见问题

### Q: 为什么我的Excel文件导入失败？
A: 请检查以下几点：
- 文件格式是否为.xlsx或.xls
- 表头是否与模板一致
- 必填字段是否都已填写
- 数字字段是否为有效数字

### Q: 可以修改模板的表头吗？
A: 不建议修改表头，系统根据表头名称识别字段。如需添加字段，请联系系统管理员。

### Q: 导入的数据可以修改吗？
A: 可以。导入后的数据会显示在产品明细表格中，您可以直接在表格中编辑。

### Q: 支持哪些单位？
A: 系统支持常见的计量单位：个、台、套、米、根、件、箱等。您也可以输入其他单位。

### Q: 如何计算含税价格？
A: 系统自动计算：总价 = (数量 × 单价 - 折扣金额) × (1 + 税率%)

## 技术支持

如果在使用过程中遇到问题，请联系技术支持团队或查看系统帮助文档。
