"""
仪表盘API单元测试
"""
import pytest
import json
from datetime import datetime, timedelta
from app.models.customer import Customer
from app.models.product import Product, ProductCategory
from app.models.quotation import Quotation, QuotationRequest
from app.models.order import Order, OrderProduct
from app.models.finance import PaymentRecord


class TestDashboardAPI:
    """仪表盘API测试类"""
    
    def test_get_dashboard_stats_empty(self, client, db_session):
        """测试获取空数据的统计信息"""
        response = client.get('/api/v1/dashboard/stats')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取统计数据成功'
        assert 'data' in data
        
        stats = data['data']
        assert stats['customers_total'] == 0
        assert stats['products_total'] == 0
        assert stats['quotations_total'] == 0
        assert stats['orders_total'] == 0
        assert stats['orders_pending'] == 0
        assert stats['orders_completed'] == 0
        assert stats['revenue_total'] == 0.0
        assert stats['revenue_month'] == 0.0
        assert 'last_updated' in stats
    
    def test_get_dashboard_stats_with_data(self, client, db_session, sample_customer, sample_product):
        """测试有数据时的统计信息"""
        # 创建送货地址
        from app.models.customer import CustomerDeliveryAddress
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()  # 获取ID

        # 创建一些测试数据
        quotation = Quotation(
            quotation_number='QUO20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='草稿',
            valid_until=datetime.now() + timedelta(days=30),
            total_amount=1000.00
        )
        db_session.add(quotation)

        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='已确认',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.commit()
        
        response = client.get('/api/v1/dashboard/stats')
        assert response.status_code == 200
        
        data = response.get_json()
        stats = data['data']
        assert stats['customers_total'] == 1
        assert stats['products_total'] == 1
        assert stats['quotations_total'] == 1
        assert stats['orders_total'] == 1
        assert stats['orders_pending'] == 1  # 已确认状态算待处理
        assert stats['orders_completed'] == 0
    
    def test_get_pending_quotations_empty(self, client, db_session):
        """测试获取空的待处理报价单"""
        response = client.get('/api/v1/dashboard/pending_quotations')
        if response.status_code != 200:
            print(f"Error response: {response.get_json()}")
        assert response.status_code == 200

        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取待处理报价单成功'
        assert data['data']['items'] == []
        assert 'pagination' in data['data']
    
    def test_get_pending_quotations_with_data(self, client, db_session, sample_customer):
        """测试获取有数据的待处理报价单"""
        # 创建待处理报价单
        valid_date = datetime.now() + timedelta(days=30)
        quotation1 = Quotation(
            quotation_number='QUO20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目1',
            status='草稿',
            valid_until=valid_date,
            total_amount=1000.00
        )
        quotation2 = Quotation(
            quotation_number='QUO20240101002',
            customer_id=sample_customer.id,
            project_name='测试项目2',
            status='已提交',
            valid_until=valid_date,
            total_amount=2000.00
        )
        # 创建已确认的报价单（不应该出现在待处理列表中）
        quotation3 = Quotation(
            quotation_number='QUO20240101003',
            customer_id=sample_customer.id,
            project_name='测试项目3',
            status='已确认',
            valid_until=valid_date,
            total_amount=3000.00
        )
        db_session.add_all([quotation1, quotation2, quotation3])
        db_session.commit()
        
        response = client.get('/api/v1/dashboard/pending_quotations')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']['items']) == 2  # 只有草稿和已提交的

        # 验证数据内容
        quotations = data['data']['items']
        assert quotations[0]['quotation_number'] in ['QUO20240101001', 'QUO20240101002']
        assert quotations[0]['customer_name'] == '测试公司'
        assert quotations[0]['status'] in ['草稿', '已提交']
    
    def test_get_pending_orders_empty(self, client, db_session):
        """测试获取空的待发货订单"""
        response = client.get('/api/v1/dashboard/pending_orders')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取待发货订单成功'
        assert data['data']['items'] == []
        assert 'pagination' in data['data']
    
    def test_get_pending_orders_with_data(self, client, db_session, sample_customer):
        """测试获取有数据的待发货订单"""
        # 创建送货地址
        from app.models.customer import CustomerDeliveryAddress
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()  # 获取ID

        # 创建待发货订单
        order1 = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目1',
            status='已确认',
            total_amount=1000.00,
            delivery_address_id=delivery_address.id
        )
        order2 = Order(
            order_number='ORD20240101002',
            customer_id=sample_customer.id,
            project_name='测试项目2',
            status='待发货',
            total_amount=2000.00,
            delivery_address_id=delivery_address.id
        )
        # 创建已完成的订单（不应该出现在待发货列表中）
        order3 = Order(
            order_number='ORD20240101003',
            customer_id=sample_customer.id,
            project_name='测试项目3',
            status='已完成',
            total_amount=3000.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add_all([order1, order2, order3])
        db_session.commit()
        
        response = client.get('/api/v1/dashboard/pending_orders')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']['items']) == 2  # 只有已确认和待发货的

        # 验证数据内容
        orders = data['data']['items']
        assert orders[0]['order_number'] in ['ORD20240101001', 'ORD20240101002']
        assert orders[0]['customer_name'] == '测试公司'
        assert orders[0]['status'] in ['已确认', '待发货']
    
    def test_get_sales_trends_empty(self, client, db_session):
        """测试获取空的销售趋势"""
        response = client.get('/api/v1/dashboard/sales_trends')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取销售趋势成功'
        assert isinstance(data['data'], list)
        assert len(data['data']) == 30  # 默认30天
        
        # 验证数据格式
        for item in data['data']:
            assert 'date' in item
            assert 'amount' in item
            assert 'orders_count' in item
            assert item['amount'] == 0.0
            assert item['orders_count'] == 0
    
    def test_get_sales_trends_with_custom_days(self, client, db_session):
        """测试获取自定义天数的销售趋势"""
        response = client.get('/api/v1/dashboard/sales_trends?days=7')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']) == 7  # 7天数据
    
    def test_get_product_sales_empty(self, client, db_session):
        """测试获取空的产品销售占比"""
        response = client.get('/api/v1/dashboard/product_sales')
        if response.status_code != 200:
            print(f"Error response: {response.get_json()}")
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取产品销售占比成功'
        assert data['data'] == []
    
    def test_get_weather_info(self, client, db_session):
        """测试获取天气信息"""
        response = client.get('/api/v1/dashboard/weather')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取天气信息成功'
        
        weather = data['data']
        assert 'city' in weather
        assert 'temperature' in weather
        assert 'weather' in weather
        assert 'humidity' in weather
        assert 'wind' in weather
        assert 'update_time' in weather
        assert weather['city'] == '北京'  # 默认城市
    
    def test_get_weather_info_with_city(self, client, db_session):
        """测试获取指定城市的天气信息"""
        response = client.get('/api/v1/dashboard/weather?city=上海')
        assert response.status_code == 200
        
        data = response.get_json()
        weather = data['data']
        assert weather['city'] == '上海'
    
    def test_get_user_info(self, client, db_session):
        """测试获取用户信息"""
        response = client.get('/api/v1/dashboard/user_info')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取用户信息成功'
        
        user_info = data['data']
        assert 'username' in user_info
        assert 'role' in user_info
        assert 'last_login' in user_info
        assert 'login_count' in user_info
        assert 'permissions' in user_info
        assert isinstance(user_info['permissions'], list)
    
    def test_pagination_parameters(self, client, db_session, sample_customer):
        """测试分页参数"""
        # 创建多个报价单
        valid_date = datetime.now() + timedelta(days=30)
        for i in range(15):
            quotation = Quotation(
                quotation_number=f'QUO2024010100{i:02d}',
                customer_id=sample_customer.id,
                project_name=f'测试项目{i}',
                status='草稿',
                valid_until=valid_date,
                total_amount=1000.00 * (i + 1)
            )
            db_session.add(quotation)
        db_session.commit()
        
        # 测试分页
        response = client.get('/api/v1/dashboard/pending_quotations?page=1&per_page=5')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']['items']) == 5
        assert data['data']['pagination']['page'] == 1
        assert data['data']['pagination']['per_page'] == 5
        assert data['data']['pagination']['total'] == 15
        assert data['data']['pagination']['pages'] == 3
