<template>
  <el-dialog v-model="visible" title="选择目标订单" width="600px" @close="handleClose">
    <div class="search-container">
      <el-select
        v-model="customerSearch"
        filterable
        remote
        placeholder="请输入客户名称搜索"
        :remote-method="searchCustomers"
        clearable
        style="width: 100%; margin-bottom: 15px;"
      >
        <el-option
          v-for="item in customerOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      
      <el-input 
        v-model="orderSearch" 
        placeholder="输入订单号搜索" 
        clearable 
        style="margin-bottom: 15px;"
      >
        <template #append>
          <el-button @click="handleOrderSearch">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
    </div>
    
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    
    <el-table 
      v-else 
      :data="orderList" 
      style="width: 100%;" 
      highlight-current-row
      @row-click="handleSelect"
      v-loading="loading"
    >
      <el-table-column prop="order_number" label="订单号" width="180" />
      <el-table-column prop="customer_name" label="客户" />
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small"
            @click.stop="handleSelect(row)"
          >
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div v-if="!loading && orderList.length === 0" class="empty-container">
      <el-empty description="暂无可用订单" />
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import { customerApi } from '@/api/customer'

const props = defineProps<{ modelValue: boolean }>()
const emit = defineEmits(['update:modelValue', 'confirm'])

const visible = ref(props.modelValue)
watch(() => props.modelValue, v => visible.value = v)
watch(visible, v => emit('update:modelValue', v))

const orderSearch = ref('')
const customerSearch = ref<number | null>(null)
const orderList = ref<any[]>([])
const customerOptions = ref<any[]>([])
const loading = ref(false)

// 状态类型映射函数
const getStatusType = (status: string | undefined): string => {
  if (!status) return 'info';
  
  const typeMap: Record<string, string> = {
    '草稿': 'info',
    '待确认': 'warning',
    '已确认': 'success',
    '生产中': 'primary',
    '待发货': 'success',
    '发货中': 'warning',
    '部分发货': 'warning',
    '已发货': 'success',
    '待对账': 'info',
    '部分对账': 'warning',
    '已对账': 'success',
    '待付款': 'info',
    '部分付款': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  };
  
  return typeMap[status] || 'info';
};

// 加载订单数据
const loadOrders = async () => {
  loading.value = true;
  
  try {
    const params: any = {
      page: 1,
      per_page: 50,
      status: '已确认,生产中,待发货,发货中,部分发货'
    };
    
    if (customerSearch.value) {
      params.customer_id = customerSearch.value;
    }
    
    if (orderSearch.value) {
      params.order_number = orderSearch.value;
    }
    
    const response = await orderApi.getList(params);
    
    // 处理响应数据
    if (Array.isArray(response)) {
      orderList.value = response;
    } else if (response && typeof response === 'object') {
      const data = response.data || response;
      if (Array.isArray(data)) {
        orderList.value = data;
      } else {
        orderList.value = [];
      }
    } else {
      orderList.value = [];
    }
    
    if (orderList.value.length === 0) {
      ElMessage.info('没有找到符合条件的订单');
    }
  } catch (error: any) {
    console.error('加载订单失败:', error);
    ElMessage.error('加载订单失败: ' + (error.message || '未知错误'));
    orderList.value = [];
  } finally {
    loading.value = false;
  }
};

// 搜索客户
const searchCustomers = async (query: string) => {
  if (!query) return;
  
  try {
    const response = await customerApi.getList({ name: query, per_page: 20 });
    customerOptions.value = Array.isArray(response) ? response : (response.data || response.items || []);
  } catch (error) {
    console.error('搜索客户失败:', error);
    customerOptions.value = [];
  }
};

// 处理订单搜索
const handleOrderSearch = () => {
  loadOrders();
};

// 监听客户选择变化
watch(customerSearch, () => {
  loadOrders();
});

// 选择订单
const handleSelect = (row: any) => {
  if (!row.id) {
    ElMessage.warning('无效的订单，请选择其他订单');
    return;
  }
  
  emit('confirm', row.id);
  visible.value = false;
};

// 关闭对话框
const handleClose = () => {
  orderSearch.value = '';
  customerSearch.value = null;
  orderList.value = [];
  emit('update:modelValue', false);
};

// 当对话框显示时加载初始数据
watch(visible, (val) => {
  if (val) {
    loadOrders();
  }
});
</script>

<style scoped>
.search-container {
  margin-bottom: 15px;
}

.loading-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
