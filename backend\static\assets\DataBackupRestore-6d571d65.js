import{J as e,r as l,L as a,b as t,e as o,f as d,M as s,o as r,c as n,i as u,h as i,a as c,k as p,l as f,g as m,j as _,a9 as y,N as b,t as v,U as w,W as h,$ as g,_ as k,a7 as C,am as V,X as x,Y as U,an as D,ao as O,a4 as j}from"./index-3d4c440c.js";import{c as I,d as B,e as F,r as M,s as R,f as N,h as $}from"./settings-1b359923.js";import{_ as z}from"./_plugin-vue_export-helper-1b428a4d.js";const L={class:"data-backup-restore"},A={class:"card-header"},S={class:"left"},q={class:"right"},H={class:"feature-header"},T={class:"feature-content"},E={class:"feature-header"},K={class:"feature-content"},G={class:"table-header"},J={class:"filter-area"},W={class:"dialog-footer"},X={class:"dialog-footer"},Y={class:"restore-confirm-content"},P={class:"restore-confirm-form mb-20 mt-20"},Q={class:"dialog-footer"},Z=z(e({__name:"DataBackupRestore",setup(e){const z=l(!1),Z=l(!1),ee=l(!1),le=l(!1),ae=l(!1),te=l(!1),oe=l(!1),de=l(""),se=l(""),re=l(),ne=a({description:`手动备份_${(new Date).toISOString().slice(0,10)}`}),ue=a({enabled:!1,frequency:"daily",time:new Date((new Date).setHours(2,0,0,0)),keepCount:7,path:"C:\\EMB\\Backup\\Auto"}),ie=l([]),ce=a({id:0,filename:"",backup_type:"手动",file_size:0,description:"",created_at:"",updated_at:""});t();const pe=async()=>{le.value=!0;try{const e=await I(),l=Array.isArray(e)?e:e.data||[];se.value&&""!==se.value.trim()?ie.value=l.filter((e=>e.filename.toLowerCase().includes(se.value.toLowerCase())||e.description&&e.description.toLowerCase().includes(se.value.toLowerCase()))):ie.value=l}catch(e){console.error("获取备份历史失败:",e),w.error("获取备份历史失败: "+(e.message||"未知错误")),ie.value=[]}finally{le.value=!1}},fe=async()=>{if(window.pywebview&&window.pywebview.api&&window.pywebview.api.openDirectoryDialog)try{const e=await window.pywebview.api.openDirectoryDialog();e&&e.length>0&&(ue.path=e[0])}catch(e){console.error("选择文件夹时发生错误:",e),w.error("选择文件夹失败。")}else w.error("无法调用文件选择器。该功能仅在桌面客户端中可用。")},me=async()=>{ne.description.trim()||(ne.description=`手动备份_${(new Date).toISOString().slice(0,10)}`),z.value=!0;try{await F(ne),w.success("数据备份成功"),te.value=!1,pe()}catch(e){console.error("数据备份失败:",e);let l="数据备份失败";if(e.response&&e.response.data){if(e.response.data.message&&(l+=": "+e.response.data.message),e.response.data.errors){l+=" - "+Object.entries(e.response.data.errors).map((([e,l])=>`${e}: ${Array.isArray(l)?l.join(", "):l}`)).join("; ")}}else e.message&&(l+=": "+e.message);w.error(l)}finally{z.value=!1}},_e=e=>{Object.assign(ce,e),ae.value=!1,de.value="",oe.value=!0},ye=async()=>{if("CONFIRM"===de.value){Z.value=!0;try{await M(ce.id),w.success("数据恢复成功，系统将在5秒后重启"),oe.value=!1,setTimeout((()=>{w.info("实际项目中，此时应该重启应用以完成数据恢复")}),5e3)}catch(e){console.error("数据恢复失败:",e),w.error("数据恢复失败: "+(e.message||"未知错误"))}finally{Z.value=!1}}else w.warning("请输入CONFIRM以确认操作")},be=async()=>{ee.value=!0;try{await R(ue),w.success("设置已保存")}catch(e){console.error("保存自动备份设置失败:",e),w.error("保存设置失败")}finally{ee.value=!1}},ve=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(2)+" KB":e<1073741824?(e/1048576).toFixed(2)+" MB":(e/1073741824).toFixed(2)+" GB",we=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN")};return o((()=>{pe(),(async()=>{try{const e=await B();if("string"==typeof e.time&&e.time){const[l,a]=e.time.split(":"),t=new Date;t.setHours(parseInt(l,10),parseInt(a,10),0,0),e.time=t}Object.assign(ue,e)}catch(e){console.error("获取自动备份设置失败:",e),w.error("获取自动备份设置失败: "+(e.message||"未知错误"))}})()})),(e,l)=>{const a=d("el-tag"),t=d("el-icon"),o=d("el-button"),I=d("el-card"),B=d("el-alert"),F=d("el-col"),M=d("el-switch"),R=d("el-form-item"),he=d("el-option"),ge=d("el-select"),ke=d("el-time-picker"),Ce=d("el-input-number"),Ve=d("el-input"),xe=d("el-form"),Ue=d("el-row"),De=d("el-table-column"),Oe=d("el-tooltip"),je=d("el-button-group"),Ie=d("el-table"),Be=d("el-descriptions-item"),Fe=d("el-descriptions"),Me=d("el-dialog"),Re=s("loading");return r(),n("div",L,[u(I,{class:"header-card mb-20"},{default:i((()=>[c("div",A,[c("div",S,[l[18]||(l[18]=c("h2",{class:"page-title"},"数据备份与恢复",-1)),u(a,{type:"info"},{default:i((()=>l[17]||(l[17]=[p("管理系统数据的备份与恢复")]))),_:1})]),c("div",q,[u(o,{type:"primary",onClick:l[0]||(l[0]=e=>te.value=!0)},{default:i((()=>[u(t,null,{default:i((()=>[u(f(g))])),_:1}),l[19]||(l[19]=p(" 立即备份 "))])),_:1})])])])),_:1}),u(Ue,{gutter:20,class:"mb-20"},{default:i((()=>[u(F,{span:12},{default:i((()=>[u(I,{class:"feature-card"},{header:i((()=>[c("div",H,[u(t,{class:"icon"},{default:i((()=>[u(f(k))])),_:1}),l[20]||(l[20]=c("span",null,"数据恢复",-1))])])),default:i((()=>[c("div",T,[l[21]||(l[21]=c("p",null,"从备份文件恢复系统数据",-1)),u(B,{title:"恢复数据将覆盖当前系统数据，请谨慎操作！",type:"warning",closable:!1,"show-icon":"",class:"mb-20"}),l[22]||(l[22]=c("p",null,"请从下方备份历史中选择要恢复的备份，或选择本地备份文件进行恢复。",-1))])])),_:1})])),_:1}),u(F,{span:12},{default:i((()=>[u(I,{class:"feature-card"},{header:i((()=>[c("div",E,[u(t,{class:"icon"},{default:i((()=>[u(f(C))])),_:1}),l[23]||(l[23]=c("span",null,"自动备份设置",-1))])])),default:i((()=>[c("div",K,[l[25]||(l[25]=c("p",null,"设置系统定期自动备份",-1)),u(xe,{model:ue,"label-position":"top"},{default:i((()=>[u(R,{label:"启用自动备份"},{default:i((()=>[u(M,{modelValue:ue.enabled,"onUpdate:modelValue":l[1]||(l[1]=e=>ue.enabled=e)},null,8,["modelValue"])])),_:1}),ue.enabled?(r(),m(R,{key:0,label:"备份频率"},{default:i((()=>[u(ge,{modelValue:ue.frequency,"onUpdate:modelValue":l[2]||(l[2]=e=>ue.frequency=e),class:"w-100"},{default:i((()=>[u(he,{label:"每天",value:"daily"}),u(he,{label:"每周",value:"weekly"}),u(he,{label:"每月",value:"monthly"})])),_:1},8,["modelValue"])])),_:1})):_("",!0),ue.enabled?(r(),m(R,{key:1,label:"备份时间"},{default:i((()=>[u(ke,{modelValue:ue.time,"onUpdate:modelValue":l[3]||(l[3]=e=>ue.time=e),format:"HH:mm",placeholder:"选择时间",class:"w-100"},null,8,["modelValue"])])),_:1})):_("",!0),ue.enabled?(r(),m(R,{key:2,label:"保留备份数量"},{default:i((()=>[u(Ce,{modelValue:ue.keepCount,"onUpdate:modelValue":l[4]||(l[4]=e=>ue.keepCount=e),min:1,max:50,class:"w-100"},null,8,["modelValue"])])),_:1})):_("",!0),ue.enabled?(r(),m(R,{key:3,label:"备份位置"},{default:i((()=>[u(Ve,{modelValue:ue.path,"onUpdate:modelValue":l[5]||(l[5]=e=>ue.path=e),placeholder:"请选择备份位置"},{append:i((()=>[u(o,{onClick:fe},{default:i((()=>[u(t,null,{default:i((()=>[u(f(V))])),_:1})])),_:1})])),_:1},8,["modelValue"])])),_:1})):_("",!0),u(R,null,{default:i((()=>[u(o,{type:"primary",onClick:be,loading:ee.value},{default:i((()=>l[24]||(l[24]=[p(" 保存设置 ")]))),_:1},8,["loading"])])),_:1})])),_:1},8,["model"])])])),_:1})])),_:1})])),_:1}),u(I,null,{header:i((()=>[c("div",G,[l[28]||(l[28]=c("h3",null,"备份历史记录",-1)),c("div",J,[u(Ve,{modelValue:se.value,"onUpdate:modelValue":l[6]||(l[6]=e=>se.value=e),placeholder:"搜索备份名称",clearable:"",style:{width:"220px"},onKeyup:y(pe,["enter"])},{prefix:i((()=>[u(t,null,{default:i((()=>[u(f(x))])),_:1})])),_:1},8,["modelValue"]),u(o,{type:"primary",onClick:pe},{default:i((()=>[u(t,null,{default:i((()=>[u(f(x))])),_:1}),l[26]||(l[26]=p(" 搜索 "))])),_:1}),u(o,{onClick:l[7]||(l[7]=e=>{se.value="",pe()})},{default:i((()=>[u(t,null,{default:i((()=>[u(f(U))])),_:1}),l[27]||(l[27]=p(" 重置 "))])),_:1})])])])),default:i((()=>[b((r(),n("div",null,[u(Ie,{data:ie.value,border:"",style:{width:"100%"},ref_key:"tableRef",ref:re},{default:i((()=>[u(De,{type:"index",label:"序号",width:"80"}),u(De,{prop:"filename",label:"备份文件名","min-width":"180"}),u(De,{prop:"backup_type",label:"备份类型",width:"100"},{default:i((e=>[u(a,{type:"手动"===e.row.backup_type?"primary":"success"},{default:i((()=>[p(v(e.row.backup_type),1)])),_:2},1032,["type"])])),_:1}),u(De,{prop:"file_size",label:"文件大小",width:"120"},{default:i((e=>[p(v(ve(e.row.file_size)),1)])),_:1}),u(De,{prop:"description",label:"备注","min-width":"200"}),u(De,{prop:"created_at",label:"备份时间",width:"180"},{default:i((e=>[p(v(we(e.row.created_at)),1)])),_:1}),u(De,{label:"操作",width:"220",fixed:"right"},{default:i((e=>[u(je,null,{default:i((()=>[u(Oe,{content:"恢复数据",placement:"top"},{default:i((()=>[u(o,{type:"warning",link:"",onClick:l=>_e(e.row)},{default:i((()=>[u(t,null,{default:i((()=>[u(f(D))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024),u(Oe,{content:"下载备份",placement:"top"},{default:i((()=>[u(o,{type:"primary",link:"",onClick:l=>(e=>{const l=document.createElement("a");l.href=N(e.id),l.target="_blank",l.download=e.filename,document.body.appendChild(l),l.click(),document.body.removeChild(l),w.success(`已开始下载备份：${e.filename}`)})(e.row)},{default:i((()=>[u(t,null,{default:i((()=>[u(f(g))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024),u(Oe,{content:"查看详情",placement:"top"},{default:i((()=>[u(o,{type:"info",link:"",onClick:l=>{return a=e.row,Object.assign(ce,a),void(ae.value=!0);var a}},{default:i((()=>[u(t,null,{default:i((()=>[u(f(O))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024),u(Oe,{content:"删除",placement:"top"},{default:i((()=>[u(o,{type:"danger",link:"",onClick:l=>{return a=e.row,void h.confirm(`确定要删除备份"${a.filename}"吗？此操作不可恢复！`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await $(a.id),w.success("备份删除成功"),pe()}catch(e){console.error("删除备份失败:",e),w.error("删除备份失败: "+(e.message||"未知错误"))}})).catch((()=>{}));var a}},{default:i((()=>[u(t,null,{default:i((()=>[u(f(j))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:1})])),_:1},8,["data"])])),[[Re,le.value]])])),_:1}),u(Me,{modelValue:ae.value,"onUpdate:modelValue":l[10]||(l[10]=e=>ae.value=e),title:"备份详情",width:"600px","destroy-on-close":""},{footer:i((()=>[c("span",W,[u(o,{onClick:l[8]||(l[8]=e=>ae.value=!1)},{default:i((()=>l[29]||(l[29]=[p("关闭")]))),_:1}),u(o,{type:"primary",onClick:l[9]||(l[9]=e=>_e(ce))},{default:i((()=>l[30]||(l[30]=[p(" 恢复数据 ")]))),_:1})])])),default:i((()=>[u(Fe,{column:1,border:""},{default:i((()=>[u(Be,{label:"备份文件名"},{default:i((()=>[p(v(ce.filename),1)])),_:1}),u(Be,{label:"备份类型"},{default:i((()=>[u(a,{type:"手动"===ce.backup_type?"primary":"success"},{default:i((()=>[p(v(ce.backup_type),1)])),_:1},8,["type"])])),_:1}),u(Be,{label:"文件大小"},{default:i((()=>[p(v(ve(ce.file_size)),1)])),_:1}),u(Be,{label:"备份时间"},{default:i((()=>[p(v(we(ce.created_at)),1)])),_:1}),u(Be,{label:"备注"},{default:i((()=>[p(v(ce.description||"无"),1)])),_:1})])),_:1})])),_:1},8,["modelValue"]),u(Me,{modelValue:te.value,"onUpdate:modelValue":l[13]||(l[13]=e=>te.value=e),title:"创建备份",width:"500px","destroy-on-close":""},{footer:i((()=>[c("span",X,[u(o,{onClick:l[12]||(l[12]=e=>te.value=!1)},{default:i((()=>l[31]||(l[31]=[p("取消")]))),_:1}),u(o,{type:"primary",onClick:me,loading:z.value},{default:i((()=>l[32]||(l[32]=[p(" 立即备份 ")]))),_:1},8,["loading"])])])),default:i((()=>[u(xe,{model:ne,"label-position":"top"},{default:i((()=>[u(R,{label:"备份描述"},{default:i((()=>[u(Ve,{modelValue:ne.description,"onUpdate:modelValue":l[11]||(l[11]=e=>ne.description=e),type:"textarea",rows:3,placeholder:"请输入备份描述，如：系统升级前备份、重要数据导入前备份等"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),u(Me,{modelValue:oe.value,"onUpdate:modelValue":l[16]||(l[16]=e=>oe.value=e),title:"恢复确认",width:"500px","destroy-on-close":""},{footer:i((()=>[c("span",Q,[u(o,{onClick:l[15]||(l[15]=e=>oe.value=!1)},{default:i((()=>l[34]||(l[34]=[p("取消")]))),_:1}),u(o,{type:"danger",onClick:ye,disabled:"CONFIRM"!==de.value,loading:Z.value},{default:i((()=>l[35]||(l[35]=[p(" 确认恢复 ")]))),_:1},8,["disabled","loading"])])])),default:i((()=>[c("div",Y,[u(B,{title:"恢复数据将覆盖当前系统数据，此操作不可逆！",type:"error",closable:!1,"show-icon":"",class:"mb-20"}),l[33]||(l[33]=c("p",null,"您确定要从以下备份恢复系统数据吗？",-1)),u(Fe,{column:1,border:""},{default:i((()=>[u(Be,{label:"备份文件名"},{default:i((()=>[p(v(ce.filename),1)])),_:1}),u(Be,{label:"备份时间"},{default:i((()=>[p(v(we(ce.created_at)),1)])),_:1}),u(Be,{label:"备份大小"},{default:i((()=>[p(v(ve(ce.file_size)),1)])),_:1})])),_:1}),c("div",P,[u(xe,null,{default:i((()=>[u(R,{label:"请输入'CONFIRM'以确认操作",required:""},{default:i((()=>[u(Ve,{modelValue:de.value,"onUpdate:modelValue":l[14]||(l[14]=e=>de.value=e),placeholder:"输入CONFIRM"},null,8,["modelValue"])])),_:1})])),_:1})])])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-6b50ac76"]]);export{Z as default};
