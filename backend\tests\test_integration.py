"""
集成测试
测试完整的业务流程：客户 -> 产品 -> 报价需求 -> 报价单 -> 订单 -> 发货单 -> 收款
"""
import pytest
import json
from datetime import datetime, date, timedelta


class TestBusinessWorkflow:
    """业务流程集成测试"""
    
    def test_complete_business_workflow(self, client, db_session, test_data_generator):
        """测试完整的业务流程"""
        
        # 1. 创建客户
        customer_data = test_data_generator.customer_data()
        response = client.post('/api/v1/customers', json=customer_data)
        assert response.status_code == 201
        customer = response.get_json()['data']
        customer_id = customer['id']
        
        # 2. 创建产品分类
        category_data = {
            'name': '测试分类',
            'description': '测试分类描述'
        }
        response = client.post('/api/v1/products/categories', json=category_data)
        assert response.status_code == 201
        category = response.get_json()['data']
        category_id = category['id']
        
        # 3. 创建产品
        product_data = test_data_generator.product_data(
            category_id=category_id
        )
        response = client.post('/api/v1/products', json=product_data)
        assert response.status_code == 201
        product = response.get_json()['data']
        product_id = product['id']
        spec_id = product['specifications'][0]['id']
        
        # 5. 创建报价需求
        request_data = {
            'customer_id': customer_id,
            'project_name': '测试项目',
            'project_address': '测试项目地址',
            'expected_date': (datetime.now() + timedelta(days=30)).isoformat(),
            'notes': '测试报价需求',
            'products': [{
                'product_id': product_id,
                'product_specification_id': spec_id,
                'quantity': 10,
                'notes': '测试需求产品'
            }]
        }
        response = client.post('/api/v1/quotations/requests', json=request_data)
        assert response.status_code == 201
        quotation_request = response.get_json()['data']
        request_id = quotation_request['id']
        
        # 6. 基于报价需求生成报价单
        quotation_data = {
            'valid_until': (datetime.now() + timedelta(days=30)).isoformat(),
            'payment_terms': '预付30%，货到付款70%',
            'delivery_terms': '7个工作日内发货',
            'notes': '测试报价单'
        }
        response = client.post(f'/api/v1/quotations/requests/{request_id}/generate-quotation', 
                             json=quotation_data)
        assert response.status_code == 201
        quotation = response.get_json()['data']
        quotation_id = quotation['id']
        
        # 7. 确认报价单
        status_data = {'status': '已确认', 'comment': '客户确认报价'}
        response = client.put(f'/api/v1/quotations/{quotation_id}/status', json=status_data)
        assert response.status_code == 200
        
        # 8. 基于报价单创建订单
        order_data = test_data_generator.order_data()
        response = client.post(f'/api/v1/orders/quotations/{quotation_id}/create-order', 
                             json=order_data)
        assert response.status_code == 201
        order = response.get_json()['data']
        order_id = order['id']
        
        # 9. 确认订单
        status_data = {'status': '已确认', 'comment': '订单确认'}
        response = client.put(f'/api/v1/orders/{order_id}/status', json=status_data)
        assert response.status_code == 200
        
        # 10. 更新订单状态为待发货
        status_data = {'status': '待发货', 'comment': '准备发货'}
        response = client.put(f'/api/v1/orders/{order_id}/status', json=status_data)
        assert response.status_code == 200
        
        # 11. 获取订单产品信息用于创建发货单
        response = client.get(f'/api/v1/orders/{order_id}')
        assert response.status_code == 200
        order_detail = response.get_json()['data']
        order_product_id = order_detail['products'][0]['id']
        
        # 12. 创建发货单
        delivery_note_data = {
            'order_id': order_id,
            'delivery_date': datetime.now().isoformat(),
            'logistics_company': '顺丰快递',
            'tracking_number': 'SF1234567890',
            'recipient_name': customer['contact'],
            'recipient_phone': customer['phone'],
            'delivery_address_snapshot': customer['address'],
            'notes': '测试发货单',
            'items': [{
                'order_product_id': order_product_id,
                'product_specification_id': spec_id,
                'quantity': 5,  # 部分发货
                'notes': '部分发货'
            }]
        }
        response = client.post('/api/v1/orders/delivery-notes', json=delivery_note_data)
        assert response.status_code == 201
        delivery_note = response.get_json()['data']
        delivery_note_id = delivery_note['id']
        
        # 13. 创建对账单
        statement_data = {
            'customer_id': customer_id,
            'statement_date': date.today().isoformat(),
            'due_date': (date.today() + timedelta(days=30)).isoformat(),
            'notes': '测试对账单',
            'delivery_note_ids': [delivery_note_id]
        }
        response = client.post('/api/v1/finance/statements', json=statement_data)
        assert response.status_code == 201
        statement = response.get_json()['data']
        
        # 14. 创建收款记录
        payment_data = {
            'order_id': order_id,
            'payment_date': date.today().isoformat(),
            'amount': 500.00,
            'payment_method': 'bank_transfer',
            'reference_number': 'TEST001',
            'notes': '测试收款记录',
            'created_by': '测试用户'
        }
        response = client.post('/api/v1/finance/payment-records', json=payment_data)
        assert response.status_code == 201
        payment_record = response.get_json()['data']
        
        # 15. 确认收款
        status_data = {'status': '已确认'}
        response = client.put(f'/api/v1/finance/payment-records/{payment_record["id"]}/status', 
                            json=status_data)
        assert response.status_code == 200
        
        # 验证整个流程的数据一致性
        
        # 验证客户数据
        response = client.get(f'/api/v1/customers/{customer_id}')
        assert response.status_code == 200
        final_customer = response.get_json()['data']
        assert final_customer['name'] == customer_data['name']
        
        # 验证产品数据
        response = client.get(f'/api/v1/products/{product_id}')
        assert response.status_code == 200
        final_product = response.get_json()['data']
        assert final_product['name'] == product_data['name']
        
        # 验证订单数据
        response = client.get(f'/api/v1/orders/{order_id}')
        assert response.status_code == 200
        final_order = response.get_json()['data']
        assert final_order['customer_id'] == customer_id
        assert final_order['quotation_id'] == quotation_id
        assert float(final_order['paid_amount']) == 500.00  # 收款金额
        
        # 验证发货单数据
        response = client.get(f'/api/v1/orders/delivery-notes/{delivery_note_id}')
        assert response.status_code == 200
        final_delivery_note = response.get_json()['data']
        assert final_delivery_note['order_id'] == order_id
        
        # 验证收款记录数据
        response = client.get(f'/api/v1/finance/payment-records/{payment_record["id"]}')
        assert response.status_code == 200
        final_payment = response.get_json()['data']
        assert final_payment['order_id'] == order_id
        assert final_payment['status'] == '已确认'
        
        print("✅ 完整业务流程测试通过")
    
    def test_quotation_to_order_workflow(self, client, db_session, sample_customer, sample_product):
        """测试报价单到订单的流程"""
        
        # 1. 创建报价需求
        request_data = {
            'customer_id': sample_customer.id,
            'project_name': '报价转订单测试项目',
            'project_address': '测试地址',
            'notes': '测试报价需求',
            'products': [{
                'product_id': sample_product.id,
                'product_specification_id': sample_product.specifications[0].id,
                'quantity': 5,
                'notes': '测试产品'
            }]
        }
        response = client.post('/api/v1/quotations/requests', json=request_data)
        assert response.status_code == 201
        request_id = response.get_json()['data']['id']
        
        # 2. 生成报价单
        quotation_data = {
            'valid_until': (datetime.now() + timedelta(days=30)).isoformat(),
            'payment_terms': '预付50%',
            'delivery_terms': '10个工作日',
            'notes': '测试报价单'
        }
        response = client.post(f'/api/v1/quotations/requests/{request_id}/generate-quotation', 
                             json=quotation_data)
        assert response.status_code == 201
        quotation_id = response.get_json()['data']['id']
        
        # 3. 确认报价单
        status_data = {'status': '已确认'}
        response = client.put(f'/api/v1/quotations/{quotation_id}/status', json=status_data)
        assert response.status_code == 200
        
        # 4. 基于报价单创建订单
        order_data = {
            'contact_person': '测试联系人',
            'contact_phone': '13800138000',
            'notes': '基于报价单创建的订单'
        }
        response = client.post(f'/api/v1/orders/quotations/{quotation_id}/create-order', 
                             json=order_data)
        assert response.status_code == 201
        order = response.get_json()['data']
        
        # 验证订单数据
        assert order['customer_id'] == sample_customer.id
        assert order['quotation_id'] == quotation_id
        assert order['project_name'] == '报价转订单测试项目'
        assert order['contact_person'] == '测试联系人'
        assert len(order['products']) == 1
        assert order['products'][0]['product_id'] == sample_product.id
        
        print("✅ 报价单到订单流程测试通过")
    
    def test_order_to_delivery_workflow(self, client, db_session, sample_order, sample_product):
        """测试订单到发货的流程"""
        
        # 1. 确认订单
        status_data = {'status': '已确认'}
        response = client.put(f'/api/v1/orders/{sample_order.id}/status', json=status_data)
        assert response.status_code == 200
        
        # 2. 更新为待发货状态
        status_data = {'status': '待发货'}
        response = client.put(f'/api/v1/orders/{sample_order.id}/status', json=status_data)
        assert response.status_code == 200
        
        # 3. 添加订单产品（因为sample_order可能没有产品）
        from app.models.order import OrderProduct
        order_product = OrderProduct(
            order_id=sample_order.id,
            product_id=sample_product.id,
            product_specification_id=sample_product.specifications[0].id,
            quantity=10,
            unit_price=150.00,
            product_name=sample_product.name,
            product_model=sample_product.model,
            product_unit=sample_product.unit,
            specification_description=sample_product.specifications[0].specification
        )
        order_product.total_price = order_product.calculate_total_price()
        db_session.add(order_product)
        db_session.commit()
        
        # 4. 创建发货单
        delivery_note_data = {
            'order_id': sample_order.id,
            'delivery_date': datetime.now().isoformat(),
            'recipient_name': '收货人',
            'delivery_address_snapshot': '收货地址',
            'items': [{
                'order_product_id': order_product.id,
                'product_specification_id': sample_product.specifications[0].id,
                'quantity': 8,  # 部分发货
                'notes': '部分发货'
            }]
        }
        response = client.post('/api/v1/orders/delivery-notes', json=delivery_note_data)
        assert response.status_code == 201
        delivery_note = response.get_json()['data']
        
        # 验证发货单数据
        assert delivery_note['order_id'] == sample_order.id
        assert len(delivery_note['items']) == 1
        assert delivery_note['items'][0]['quantity'] == 8
        
        # 验证订单产品的已发货数量更新
        response = client.get(f'/api/v1/orders/{sample_order.id}')
        assert response.status_code == 200
        updated_order = response.get_json()['data']
        assert updated_order['products'][0]['delivered_quantity'] == 8
        
        print("✅ 订单到发货流程测试通过")
