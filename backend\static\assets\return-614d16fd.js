import{I as t}from"./index-3d4c440c.js";function r(r){return t({url:"/api/v1/returns",method:"get",params:r})}function e(r){return t({url:`/api/v1/returns/${r}`,method:"get"})}function u(r,e="已完成"){return t({url:"/api/v1/returns/available-delivery-notes",method:"get",params:{customer_id:r,status:e}})}function a(r){return t({url:"/api/v1/returns",method:"post",data:r})}function n(r,e){return t({url:`/api/v1/returns/${r}`,method:"put",data:e})}function s(r,e,u=!1){return t({url:`/api/v1/returns/${r}/status`,method:"put",data:{status:e,force:u}})}function o(r){return t({url:`/api/v1/returns/${r}`,method:"delete"})}function i(r){return t({url:"/api/v1/returns/product-returned-quantity",method:"get",params:{order_product_id:r}})}export{r as a,u as b,i as c,o as d,a as e,n as f,e as g,s as u};
