"""
自定义异常类
定义业务逻辑中的各种异常情况
"""
from typing import Any, Dict, List, Optional, Union


class APIException(Exception):
    """
    API异常基类
    """
    def __init__(
        self, 
        message: str = '操作失败', 
        code: int = 400,
        errors: Optional[Union[Dict, List, str]] = None,
        data: Any = None
    ):
        self.message = message
        self.code = code
        self.errors = errors
        self.data = data
        super().__init__(self.message)


class ValidationError(APIException):
    """
    数据验证错误
    """
    def __init__(
        self, 
        message: str = '数据验证失败',
        errors: Optional[Dict[str, List[str]]] = None
    ):
        super().__init__(message=message, code=422, errors=errors)


class NotFoundError(APIException):
    """
    资源不存在错误
    """
    def __init__(
        self, 
        message: str = '资源不存在',
        resource: str = None
    ):
        if resource:
            message = f'{resource}不存在'
        super().__init__(message=message, code=404)


class UnauthorizedError(APIException):
    """
    未授权错误
    """
    def __init__(self, message: str = '未授权访问'):
        super().__init__(message=message, code=401)


class ForbiddenError(APIException):
    """
    禁止访问错误
    """
    def __init__(self, message: str = '禁止访问'):
        super().__init__(message=message, code=403)


class ConflictError(APIException):
    """
    资源冲突错误
    """
    def __init__(self, message: str = '资源冲突'):
        super().__init__(message=message, code=409)


class BusinessLogicError(APIException):
    """
    业务逻辑错误
    """
    def __init__(
        self, 
        message: str = '业务逻辑错误',
        errors: Optional[Union[Dict, List, str]] = None
    ):
        super().__init__(message=message, code=400, errors=errors)


class DatabaseError(APIException):
    """
    数据库操作错误
    """
    def __init__(self, message: str = '数据库操作失败'):
        super().__init__(message=message, code=500)


class ExternalServiceError(APIException):
    """
    外部服务错误
    """
    def __init__(self, message: str = '外部服务调用失败'):
        super().__init__(message=message, code=502)
