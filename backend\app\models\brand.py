"""
品牌数据模型
"""
from app import db
from app.models.base import BaseModel


class Brand(BaseModel):
    """品牌表模型"""
    __tablename__ = 'brands'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True, index=True, comment='品牌名称')
    description = db.Column(db.Text, nullable=True, comment='品牌描述')
    logo_url = db.Column(db.String(255), nullable=True, comment='品牌Logo URL')
    website = db.Column(db.String(255), nullable=True, comment='官方网站')
    sort_order = db.Column(db.Integer, default=0, comment='排序')

    def __repr__(self):
        return f'<Brand {self.name}>'
