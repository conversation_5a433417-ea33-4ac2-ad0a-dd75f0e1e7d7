import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// 缓存项接口
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiry?: number // 过期时间（毫秒）
}

// 缓存配置
export interface CacheConfig {
  defaultExpiry: number // 默认过期时间（毫秒）
  maxSize: number // 最大缓存数量
  enablePersist: boolean // 是否持久化
}

// 数据缓存状态管理
export const useCacheStore = defineStore(
  'cache',
  () => {
    // 默认配置
    const defaultConfig: CacheConfig = {
      defaultExpiry: 5 * 60 * 1000, // 5分钟
      maxSize: 100,
      enablePersist: false,
    }

    // 状态
    const config = ref<CacheConfig>({ ...defaultConfig })
    const cache = ref<Map<string, CacheItem>>(new Map())

    // 计算属性
    const cacheSize = computed(() => cache.value.size)
    const cacheKeys = computed(() => Array.from(cache.value.keys()))

    // 检查缓存项是否过期
    const isExpired = (item: CacheItem): boolean => {
      if (!item.expiry) return false
      return Date.now() > item.timestamp + item.expiry
    }

    // 清理过期缓存
    const clearExpired = () => {
      const now = Date.now()
      const expiredKeys: string[] = []

      cache.value.forEach((item, key) => {
        if (item.expiry && now > item.timestamp + item.expiry) {
          expiredKeys.push(key)
        }
      })

      expiredKeys.forEach(key => {
        cache.value.delete(key)
      })

      return expiredKeys.length
    }

    // 设置缓存
    const set = <T>(key: string, data: T, expiry?: number): void => {
      // 清理过期缓存
      clearExpired()

      // 如果超过最大缓存数量，删除最旧的缓存
      if (cache.value.size >= config.value.maxSize) {
        const oldestKey = cache.value.keys().next().value
        if (oldestKey) {
          cache.value.delete(oldestKey)
        }
      }

      const item: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiry: expiry || config.value.defaultExpiry,
      }

      cache.value.set(key, item)
    }

    // 获取缓存
    const get = <T>(key: string): T | null => {
      const item = cache.value.get(key)
      
      if (!item) {
        return null
      }

      // 检查是否过期
      if (isExpired(item)) {
        cache.value.delete(key)
        return null
      }

      return item.data as T
    }

    // 检查缓存是否存在且未过期
    const has = (key: string): boolean => {
      const item = cache.value.get(key)
      
      if (!item) {
        return false
      }

      if (isExpired(item)) {
        cache.value.delete(key)
        return false
      }

      return true
    }

    // 删除缓存
    const remove = (key: string): boolean => {
      return cache.value.delete(key)
    }

    // 清空所有缓存
    const clear = (): void => {
      cache.value.clear()
    }

    // 获取缓存信息
    const getInfo = (key: string) => {
      const item = cache.value.get(key)
      if (!item) return null

      return {
        key,
        size: JSON.stringify(item.data).length,
        timestamp: item.timestamp,
        expiry: item.expiry,
        isExpired: isExpired(item),
        age: Date.now() - item.timestamp,
      }
    }

    // 获取所有缓存信息
    const getAllInfo = () => {
      return cacheKeys.value.map(key => getInfo(key)).filter(Boolean)
    }

    // 更新配置
    const updateConfig = (newConfig: Partial<CacheConfig>) => {
      config.value = { ...config.value, ...newConfig }
    }

    // 缓存API响应数据
    const cacheApiResponse = <T>(
      url: string,
      params: any,
      data: T,
      expiry?: number
    ): void => {
      const key = `api:${url}:${JSON.stringify(params)}`
      set(key, data, expiry)
    }

    // 获取缓存的API响应数据
    const getCachedApiResponse = <T>(url: string, params: any): T | null => {
      const key = `api:${url}:${JSON.stringify(params)}`
      return get<T>(key)
    }

    // 缓存用户数据
    const cacheUserData = <T>(userId: string | number, data: T, expiry?: number): void => {
      const key = `user:${userId}`
      set(key, data, expiry)
    }

    // 获取缓存的用户数据
    const getCachedUserData = <T>(userId: string | number): T | null => {
      const key = `user:${userId}`
      return get<T>(key)
    }

    // 缓存列表数据
    const cacheListData = <T>(
      listType: string,
      filters: any,
      data: T,
      expiry?: number
    ): void => {
      const key = `list:${listType}:${JSON.stringify(filters)}`
      set(key, data, expiry)
    }

    // 获取缓存的列表数据
    const getCachedListData = <T>(listType: string, filters: any): T | null => {
      const key = `list:${listType}:${JSON.stringify(filters)}`
      return get<T>(key)
    }

    // 清除特定类型的缓存
    const clearByPattern = (pattern: string): number => {
      const keysToDelete = cacheKeys.value.filter(key => key.includes(pattern))
      keysToDelete.forEach(key => cache.value.delete(key))
      return keysToDelete.length
    }

    // 定期清理过期缓存
    const startAutoCleanup = (interval: number = 60000) => {
      setInterval(() => {
        const cleaned = clearExpired()
        if (cleaned > 0) {
          console.log(`Cleaned ${cleaned} expired cache items`)
        }
      }, interval)
    }

    return {
      // 状态
      config,
      cache,
      
      // 计算属性
      cacheSize,
      cacheKeys,
      
      // 基础方法
      set,
      get,
      has,
      remove,
      clear,
      clearExpired,
      
      // 信息方法
      getInfo,
      getAllInfo,
      
      // 配置方法
      updateConfig,
      
      // 专用缓存方法
      cacheApiResponse,
      getCachedApiResponse,
      cacheUserData,
      getCachedUserData,
      cacheListData,
      getCachedListData,
      
      // 清理方法
      clearByPattern,
      startAutoCleanup,
    }
  },
  {
    // 根据配置决定是否持久化
    persist: false, // 缓存数据通常不需要持久化
  }
)
