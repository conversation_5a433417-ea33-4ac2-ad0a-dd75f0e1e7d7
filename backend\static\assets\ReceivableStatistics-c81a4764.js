import{b as e,r as t,L as a,d as o,e as l,V as r,f as u,M as s,o as n,c as d,i,h as c,a as m,k as p,l as _,g as v,j as f,F as g,m as h,t as b,N as w,U as y,$ as C,Y as A,R as k,P as D}from"./index-3d4c440c.js";import{k as z,l as N,m as I}from"./finance-789ce3e6.js";import{s as R}from"./customer-471ca075.js";import{_ as V}from"./_plugin-vue_export-helper-1b428a4d.js";import{as as x,eo as P,en as S,ep as Y,em as j,ek as M,el as U,ej as G,eq as q}from"./install-028483b9.js";const E={class:"receivable-statistics"},L={class:"card-header"},F={class:"left"},O={class:"right"},$={class:"filter-container"},B={class:"statistic-value"},W={class:"statistic-footer"},X={class:"statistic-value"},H={class:"statistic-footer"},J={class:"progress-container"},K={class:"statistic-value"},Q={class:"statistic-footer"},T={class:"statistic-value"},Z={class:"statistic-footer"},ee={class:"card-header"},te={class:"right"},ae={class:"table-container"},oe={class:"pagination-container"},le=V({__name:"ReceivableStatistics",setup(V){x([P,S,Y,j,M,U,G]);const le=e(),re=t(null),ue=t(null),se=t(!1),ne=t(!1),de=t(!1),ie=t([]),ce=t("order"),me=a({period:"current_month",dateRange:[],customerId:null,status:""}),pe=a({page:1,pageSize:10,total:0}),_e=a({totalReceivable:0,totalReceivableGrowth:0,overdueAmount:0,overduePercentage:0,orderCount:0,customerCount:0}),ve=t([]),fe=t([]),ge=t([]),he=o((()=>{const e=_e.overduePercentage;return e<30?"#67C23A":e<70?"#E6A23C":"#F56C6C"})),be=async()=>{se.value=!0;try{const e={page:pe.page,per_page:pe.pageSize,customer_id:me.customerId,status:me.status};me.dateRange&&2===me.dateRange.length&&(e.due_date_start=me.dateRange[0],e.due_date_end=me.dateRange[1]);const t=await I(e);ve.value=t.data.map((e=>({id:e.id,orderNo:e.statement_number,customerId:e.customer_id,customerName:e.customer_name,orderDate:e.created_at,totalAmount:e.amount,paidAmount:e.paid_amount,receivableAmount:e.amount-e.paid_amount,dueDate:e.due_date,status:e.status}))),t.page_info&&(pe.total=t.page_info.total||0)}catch(e){console.error("获取应收款明细数据失败:",e),y.error("获取应收款明细数据失败，使用模拟数据");const t=[{id:1,orderNo:"DD202505010001",customerId:1,customerName:"北京某某科技有限公司",orderDate:"2025-05-01",totalAmount:5e4,paidAmount:3e4,receivableAmount:2e4,dueDate:"2025-06-01",status:"部分支付"},{id:2,orderNo:"DD202505010002",customerId:1,customerName:"北京某某科技有限公司",orderDate:"2025-05-03",totalAmount:35e3,paidAmount:0,receivableAmount:35e3,dueDate:"2025-06-03",status:"未支付"},{id:3,orderNo:"DD202505020001",customerId:2,customerName:"上海某某贸易有限公司",orderDate:"2025-05-02",totalAmount:68e3,paidAmount:38e3,receivableAmount:3e4,dueDate:"2025-05-15",status:"逾期"},{id:4,orderNo:"DD202505050001",customerId:3,customerName:"广州某某建设工程有限公司",orderDate:"2025-05-05",totalAmount:12e4,paidAmount:6e4,receivableAmount:6e4,dueDate:"2025-06-05",status:"部分支付"}];me.customerId?ve.value=t.filter((e=>e.customerId===me.customerId)):ve.value=t,pe.total=ve.value.length}finally{se.value=!1}},we=()=>{if(!re.value)return;const e=q(re.value),t={title:{text:"应收款账龄分析",left:"center"},tooltip:{trigger:"item",formatter:"{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:ge.value.map((e=>e.aging_range))},series:[{name:"账龄分布",type:"pie",radius:"55%",center:["50%","60%"],data:ge.value.map((e=>({name:e.aging_range,value:e.amount}))),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};e.setOption(t),window.addEventListener("resize",(()=>{e.resize()}))},ye=()=>{pe.page=1,je()},Ce=()=>{je()},Ae=e=>{pe.pageSize=e,be()},ke=e=>{pe.page=e,be()},De=async e=>{if(e){de.value=!0;try{const t=await R({name_like:e});ie.value=t.data||[]}catch(t){console.error("搜索客户失败:",t)}finally{de.value=!1}}else ie.value=[]},ze=()=>{je()},Ne=()=>{y.info("导出功能开发中")},Ie=()=>{le.push({path:"/orders",query:{status:"unpaid"}})},Re=()=>{le.push("/customers")},Ve=e=>{le.push(`/orders/detail/${e.id||1}`)},xe=e=>{le.push(`/customers/detail/${e}`)},Pe=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN")},Se=e=>null==e?"¥ 0.00":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY"}).format(e),Ye=({row:e})=>"逾期"===e.status?"overdue-row":"",je=()=>{(async()=>{try{const e=(await z(me.customerId)).data;let t=0,a=0,o=0,l=0;e&&e.length>0&&(o=e.length,e.forEach((e=>{t+=e.outstanding_amount||0,a+=e.overdue_amount||0,l+=e.order_count||1})),_e.totalReceivable=t,_e.overdueAmount=a,_e.overduePercentage=t>0?Math.round(a/t*100):0,_e.customerCount=o,_e.orderCount=l,_e.totalReceivableGrowth=5.8),fe.value=e.map((e=>({customerId:e.customer_id,customerName:e.customer_name,totalAmount:e.total_amount,paidAmount:e.paid_amount,outstandingAmount:e.outstanding_amount,overdueAmount:e.overdue_amount,orderCount:e.order_count||1,lastPaymentDate:null})))}catch(e){console.error("获取应收款统计数据失败:",e),y.error("获取应收款统计数据失败，使用模拟数据");const t=[{customer_id:1,customer_name:"北京某某科技有限公司",total_amount:15e4,paid_amount:9e4,outstanding_amount:6e4,overdue_amount:2e4,order_count:5},{customer_id:2,customer_name:"上海某某贸易有限公司",total_amount:22e4,paid_amount:12e4,outstanding_amount:1e5,overdue_amount:3e4,order_count:8},{customer_id:3,customer_name:"广州某某建设工程有限公司",total_amount:18e4,paid_amount:8e4,outstanding_amount:1e5,overdue_amount:5e4,order_count:6}];let a=0,o=0,l=t.length,r=0;t.forEach((e=>{a+=e.outstanding_amount,o+=e.overdue_amount,r+=e.order_count})),_e.totalReceivable=a,_e.overdueAmount=o,_e.overduePercentage=Math.round(o/a*100),_e.customerCount=l,_e.orderCount=r,_e.totalReceivableGrowth=8.2,fe.value=t.map((e=>({customerId:e.customer_id,customerName:e.customer_name,totalAmount:e.total_amount,paidAmount:e.paid_amount,outstandingAmount:e.outstanding_amount,overdueAmount:e.overdue_amount,orderCount:e.order_count,lastPaymentDate:"2025-05-15"})))}})(),(async()=>{ne.value=!0;try{const e=await N(me.customerId);ge.value=e.data,r((()=>{we()}))}catch(e){console.error("获取应收款账龄分析数据失败:",e),y.error("获取应收款账龄分析数据失败，使用模拟数据"),ge.value=[{aging_range:"30天内",amount:12e4},{aging_range:"31-60天",amount:85e3},{aging_range:"61-90天",amount:4e4},{aging_range:"91-180天",amount:15e3},{aging_range:"180天以上",amount:5e3}],r((()=>{we()}))}finally{ne.value=!1}})(),be()};return l((()=>{je(),r((()=>{(()=>{if(!ue.value)return;const e=q(ue.value),t={title:{text:"应收款趋势分析",left:"center"},tooltip:{trigger:"axis"},legend:{data:["应收金额","回款金额"],bottom:0},grid:{left:"3%",right:"4%",bottom:"10%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["1月","2月","3月","4月","5月","6月"]},yAxis:{type:"value"},series:[{name:"应收金额",type:"bar",data:[120,132,101,134,90,230]},{name:"回款金额",type:"bar",data:[220,182,191,234,290,330]}]};e.setOption(t),window.addEventListener("resize",(()=>{e.resize()}))})()}))})),(e,t)=>{const a=u("el-tag"),o=u("el-icon"),l=u("el-button"),r=u("el-card"),y=u("el-option"),z=u("el-select"),N=u("el-form-item"),I=u("el-date-picker"),R=u("el-form"),V=u("el-col"),x=u("el-progress"),P=u("el-row"),S=u("el-radio-button"),Y=u("el-radio-group"),j=u("el-table-column"),M=u("el-table"),U=u("el-pagination"),G=s("loading");return n(),d("div",E,[i(r,{class:"header-card mb-20"},{default:c((()=>[m("div",L,[m("div",F,[t[8]||(t[8]=m("h2",{class:"page-title"},"应收款统计",-1)),i(a,{type:"info"},{default:c((()=>t[7]||(t[7]=[p("订单应收款项统计分析")]))),_:1})]),m("div",O,[i(l,{type:"primary",onClick:Ne},{default:c((()=>[i(o,null,{default:c((()=>[i(_(C))])),_:1}),t[9]||(t[9]=p(" 导出统计 "))])),_:1}),i(l,{type:"success",onClick:ze},{default:c((()=>[i(o,null,{default:c((()=>[i(_(A))])),_:1}),t[10]||(t[10]=p(" 刷新数据 "))])),_:1})])])])),_:1}),i(r,{class:"mb-20"},{default:c((()=>[m("div",$,[i(R,{inline:!0,model:me,class:"filter-form"},{default:c((()=>[i(N,{label:"统计周期"},{default:c((()=>[i(z,{modelValue:me.period,"onUpdate:modelValue":t[0]||(t[0]=e=>me.period=e),placeholder:"选择统计周期",onChange:ye},{default:c((()=>[i(y,{label:"本月",value:"current_month"}),i(y,{label:"上月",value:"last_month"}),i(y,{label:"本季度",value:"current_quarter"}),i(y,{label:"本年度",value:"current_year"}),i(y,{label:"自定义",value:"custom"})])),_:1},8,["modelValue"])])),_:1}),"custom"===me.period?(n(),v(N,{key:0,label:"日期范围"},{default:c((()=>[i(I,{modelValue:me.dateRange,"onUpdate:modelValue":t[1]||(t[1]=e=>me.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:ye},null,8,["modelValue"])])),_:1})):f("",!0),i(N,{label:"客户"},{default:c((()=>[i(z,{modelValue:me.customerId,"onUpdate:modelValue":t[2]||(t[2]=e=>me.customerId=e),placeholder:"选择客户",clearable:"",filterable:"",remote:"","remote-method":De,loading:de.value,onChange:ye},{default:c((()=>[(n(!0),d(g,null,h(ie.value,(e=>(n(),v(y,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),i(N,{label:"状态"},{default:c((()=>[i(z,{modelValue:me.status,"onUpdate:modelValue":t[3]||(t[3]=e=>me.status=e),placeholder:"选择状态",clearable:"",onChange:ye},{default:c((()=>[i(y,{label:"全部",value:""}),i(y,{label:"已逾期",value:"逾期"}),i(y,{label:"即将到期",value:"未支付"}),i(y,{label:"正常",value:"部分支付"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])])),_:1}),i(P,{gutter:20,class:"mb-20"},{default:c((()=>[i(V,{span:6},{default:c((()=>[i(r,{class:"statistic-card"},{default:c((()=>[m("div",B,b(Se(_e.totalReceivable)),1),t[13]||(t[13]=m("div",{class:"statistic-title"},"应收款总额",-1)),m("div",W,[_e.totalReceivableGrowth>=0?(n(),v(a,{key:0,type:"danger"},{default:c((()=>[t[11]||(t[11]=p(" 较上期 ")),i(o,null,{default:c((()=>[i(_(k))])),_:1}),p(" "+b(_e.totalReceivableGrowth)+"% ",1)])),_:1})):(n(),v(a,{key:1,type:"success"},{default:c((()=>[t[12]||(t[12]=p(" 较上期 ")),i(o,null,{default:c((()=>[i(_(D))])),_:1}),p(" "+b(Math.abs(_e.totalReceivableGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),i(V,{span:6},{default:c((()=>[i(r,{class:"statistic-card"},{default:c((()=>[m("div",X,b(Se(_e.overdueAmount)),1),t[14]||(t[14]=m("div",{class:"statistic-title"},"逾期金额",-1)),m("div",H,[m("div",J,[i(x,{percentage:_e.overduePercentage,"stroke-width":8,color:he.value},null,8,["percentage","color"])])])])),_:1})])),_:1}),i(V,{span:6},{default:c((()=>[i(r,{class:"statistic-card"},{default:c((()=>[m("div",K,b(_e.orderCount),1),t[16]||(t[16]=m("div",{class:"statistic-title"},"未结清订单数",-1)),m("div",Q,[i(l,{size:"small",type:"primary",onClick:Ie},{default:c((()=>t[15]||(t[15]=[p(" 查看所有 ")]))),_:1})])])),_:1})])),_:1}),i(V,{span:6},{default:c((()=>[i(r,{class:"statistic-card"},{default:c((()=>[m("div",T,b(_e.customerCount),1),t[18]||(t[18]=m("div",{class:"statistic-title"},"欠款客户数",-1)),m("div",Z,[i(l,{size:"small",type:"primary",onClick:Re},{default:c((()=>t[17]||(t[17]=[p(" 查看所有 ")]))),_:1})])])),_:1})])),_:1})])),_:1}),i(P,{gutter:20,class:"mb-20"},{default:c((()=>[i(V,{span:12},{default:c((()=>[w((n(),v(r,{class:"chart-card"},{default:c((()=>[t[19]||(t[19]=m("div",{class:"chart-title"},"应收款账龄分析",-1)),m("div",{ref_key:"agingChart",ref:re,class:"chart-container"},null,512)])),_:1})),[[G,ne.value]])])),_:1}),i(V,{span:12},{default:c((()=>[i(r,{class:"chart-card"},{default:c((()=>[t[20]||(t[20]=m("div",{class:"chart-title"},"应收款趋势分析",-1)),m("div",{ref_key:"trendChart",ref:ue,class:"chart-container"},null,512)])),_:1})])),_:1})])),_:1}),i(r,null,{header:c((()=>[m("div",ee,[t[23]||(t[23]=m("div",{class:"left"},[m("h3",{class:"card-title"},"应收款明细")],-1)),m("div",te,[i(Y,{modelValue:ce.value,"onUpdate:modelValue":t[4]||(t[4]=e=>ce.value=e),onChange:Ce},{default:c((()=>[i(S,{value:"order"},{default:c((()=>t[21]||(t[21]=[p("按订单")]))),_:1}),i(S,{value:"customer"},{default:c((()=>t[22]||(t[22]=[p("按客户")]))),_:1})])),_:1},8,["modelValue"])])])])),default:c((()=>[w((n(),d("div",ae,["order"===ce.value?(n(),v(M,{key:0,data:ve.value,border:"",stripe:"",style:{width:"100%"},"row-class-name":Ye},{default:c((()=>[i(j,{prop:"orderNo",label:"订单编号","min-width":"150"},{default:c((e=>[i(l,{type:"primary",link:"",onClick:t=>Ve(e.row)},{default:c((()=>[p(b(e.row.orderNo),1)])),_:2},1032,["onClick"])])),_:1}),i(j,{prop:"customerName",label:"客户名称","min-width":"200"},{default:c((e=>[i(l,{type:"info",link:"",onClick:t=>xe(e.row.customerId)},{default:c((()=>[p(b(e.row.customerName),1)])),_:2},1032,["onClick"])])),_:1}),i(j,{prop:"orderDate",label:"订单日期",width:"120"},{default:c((e=>[p(b(Pe(e.row.orderDate)),1)])),_:1}),i(j,{prop:"totalAmount",label:"订单金额",width:"150"},{default:c((e=>[p(b(Se(e.row.totalAmount)),1)])),_:1}),i(j,{prop:"paidAmount",label:"已收金额",width:"150"},{default:c((e=>[p(b(Se(e.row.paidAmount)),1)])),_:1}),i(j,{prop:"receivableAmount",label:"应收金额",width:"150"},{default:c((e=>[p(b(Se(e.row.receivableAmount)),1)])),_:1}),i(j,{prop:"dueDate",label:"到期日",width:"120"},{default:c((e=>[p(b(Pe(e.row.dueDate)),1)])),_:1}),i(j,{prop:"status",label:"状态",width:"100"},{default:c((e=>{return[i(a,{type:(t=e.row.status,{"未支付":"warning","部分支付":"info","已支付":"success","逾期":"danger"}[t]||"info")},{default:c((()=>[p(b(e.row.status),1)])),_:2},1032,["type"])];var t})),_:1}),i(j,{label:"操作",width:"150",fixed:"right"},{default:c((e=>[i(l,{link:"",type:"primary",size:"small",onClick:t=>{return a=e.row,void le.push({path:"/finance/payment-record-edit",query:{order_id:a.id||1}});var a}},{default:c((()=>t[24]||(t[24]=[p(" 收款 ")]))),_:2},1032,["onClick"]),i(l,{link:"",type:"info",size:"small",onClick:t=>Ve(e.row)},{default:c((()=>t[25]||(t[25]=[p(" 详情 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])):(n(),v(M,{key:1,data:fe.value,border:"",stripe:"",style:{width:"100%"}},{default:c((()=>[i(j,{prop:"customerName",label:"客户名称","min-width":"200"},{default:c((e=>[i(l,{type:"info",link:"",onClick:t=>xe(e.row.customerId)},{default:c((()=>[p(b(e.row.customerName),1)])),_:2},1032,["onClick"])])),_:1}),i(j,{prop:"totalAmount",label:"应收总额",width:"150"},{default:c((e=>[p(b(Se(e.row.totalAmount)),1)])),_:1}),i(j,{prop:"paidAmount",label:"已收金额",width:"150"},{default:c((e=>[p(b(Se(e.row.paidAmount)),1)])),_:1}),i(j,{prop:"outstandingAmount",label:"未收金额",width:"150"},{default:c((e=>[p(b(Se(e.row.outstandingAmount)),1)])),_:1}),i(j,{prop:"overdueAmount",label:"逾期金额",width:"150"},{default:c((e=>[p(b(Se(e.row.overdueAmount)),1)])),_:1}),i(j,{prop:"orderCount",label:"订单数",width:"100"}),i(j,{prop:"lastPaymentDate",label:"最近收款日",width:"120"},{default:c((e=>[p(b(Pe(e.row.lastPaymentDate)),1)])),_:1}),i(j,{label:"操作",width:"150",fixed:"right"},{default:c((e=>[i(l,{link:"",type:"primary",size:"small",onClick:t=>{return a=e.row,void le.push({path:"/orders",query:{customer_id:a.customerId}});var a}},{default:c((()=>t[26]||(t[26]=[p(" 查看订单 ")]))),_:2},1032,["onClick"]),i(l,{link:"",type:"info",size:"small",onClick:t=>xe(e.row.customerId)},{default:c((()=>t[27]||(t[27]=[p(" 客户详情 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),m("div",oe,[i(U,{"current-page":pe.page,"onUpdate:currentPage":t[5]||(t[5]=e=>pe.page=e),"page-size":pe.pageSize,"onUpdate:pageSize":t[6]||(t[6]=e=>pe.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:pe.total,onSizeChange:Ae,onCurrentChange:ke},null,8,["current-page","page-size","total"])])])),[[G,se.value]])])),_:1})])}}},[["__scopeId","data-v-41cd62a3"]]);export{le as default};
