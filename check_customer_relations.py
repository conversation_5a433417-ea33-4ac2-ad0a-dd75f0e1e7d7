#!/usr/bin/env python3
"""
检查客户关联数据的脚本
查看客户ID为3和9是否真的有关联数据
"""
import sqlite3
import os

def check_customer_relations():
    """检查客户关联数据"""
    # 数据库文件路径
    db_path = r'D:\code\EMB-new\backend\instance\project.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查客户关联数据")
        print("=" * 50)
        
        # 检查的客户ID
        customer_ids = [3, 9]
        
        for customer_id in customer_ids:
            print(f"\n📋 检查客户ID: {customer_id}")
            
            # 1. 检查客户基本信息
            cursor.execute("SELECT id, name, contact FROM customers WHERE id = ?", (customer_id,))
            customer = cursor.fetchone()
            if customer:
                print(f"  客户信息: ID={customer[0]}, 名称={customer[1]}, 联系人={customer[2]}")
            else:
                print(f"  ❌ 客户ID {customer_id} 不存在")
                continue
            
            # 2. 检查报价单关联
            cursor.execute("SELECT COUNT(*) FROM quotations WHERE customer_id = ?", (customer_id,))
            quotation_count = cursor.fetchone()[0]
            print(f"  报价单数量: {quotation_count}")
            
            if quotation_count > 0:
                cursor.execute("SELECT id, quotation_number, status FROM quotations WHERE customer_id = ? LIMIT 5", (customer_id,))
                quotations = cursor.fetchall()
                for q in quotations:
                    print(f"    - 报价单ID: {q[0]}, 编号: {q[1]}, 状态: {q[2]}")
            
            # 3. 检查订单关联
            cursor.execute("SELECT COUNT(*) FROM orders WHERE customer_id = ?", (customer_id,))
            order_count = cursor.fetchone()[0]
            print(f"  订单数量: {order_count}")
            
            if order_count > 0:
                cursor.execute("SELECT id, order_number, status FROM orders WHERE customer_id = ? LIMIT 5", (customer_id,))
                orders = cursor.fetchall()
                for o in orders:
                    print(f"    - 订单ID: {o[0]}, 编号: {o[1]}, 状态: {o[2]}")
            
            # 4. 检查发货单关联（通过订单）
            cursor.execute("""
                SELECT COUNT(*) FROM delivery_notes dn 
                JOIN orders o ON dn.order_id = o.id 
                WHERE o.customer_id = ?
            """, (customer_id,))
            delivery_count = cursor.fetchone()[0]
            print(f"  发货单数量: {delivery_count}")
            
            if delivery_count > 0:
                cursor.execute("""
                    SELECT dn.id, dn.delivery_number, dn.status 
                    FROM delivery_notes dn 
                    JOIN orders o ON dn.order_id = o.id 
                    WHERE o.customer_id = ? LIMIT 5
                """, (customer_id,))
                deliveries = cursor.fetchall()
                for d in deliveries:
                    print(f"    - 发货单ID: {d[0]}, 编号: {d[1]}, 状态: {d[2]}")
            
            # 5. 检查报价请求关联
            cursor.execute("SELECT COUNT(*) FROM quotation_requests WHERE customer_id = ?", (customer_id,))
            request_count = cursor.fetchone()[0]
            print(f"  报价请求数量: {request_count}")
            
            # 6. 检查银行账户关联
            cursor.execute("SELECT COUNT(*) FROM customer_bank_accounts WHERE customer_id = ?", (customer_id,))
            bank_count = cursor.fetchone()[0]
            print(f"  银行账户数量: {bank_count}")
            
            # 7. 检查收货地址关联
            cursor.execute("SELECT COUNT(*) FROM customer_delivery_addresses WHERE customer_id = ?", (customer_id,))
            address_count = cursor.fetchone()[0]
            print(f"  收货地址数量: {address_count}")
            
            # 总结
            total_relations = quotation_count + order_count + delivery_count + request_count
            print(f"  📊 总关联数据: {total_relations} 条")
            
            if total_relations == 0:
                print(f"  ✅ 客户ID {customer_id} 没有业务关联数据，应该可以删除")
            else:
                print(f"  ❌ 客户ID {customer_id} 有 {total_relations} 条关联数据，无法删除")
        
        # 8. 检查所有表是否存在
        print(f"\n📋 检查相关表是否存在:")
        tables_to_check = [
            'customers', 'quotations', 'orders', 'delivery_notes', 
            'quotation_requests', 'customer_bank_accounts', 'customer_delivery_addresses'
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        for table in tables_to_check:
            if table in existing_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  ✅ {table}: {count} 条记录")
            else:
                print(f"  ❌ {table}: 表不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询错误: {str(e)}")

if __name__ == "__main__":
    check_customer_relations()
