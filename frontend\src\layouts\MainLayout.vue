<template>
  <el-container direction="horizontal" class="main-container">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '220px'" class="aside">
      <div class="logo" :class="{ 'collapsed-logo': isCollapse }">
        <img src="https://placehold.co/40x40/007bff/FFFFFF.svg?text=BC" alt="柏成物资" @error="handleImgError">
        <span v-if="!isCollapse">柏成物资配送管理系统</span>
      </div>
      <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical"
        :router="true"
        :collapse="isCollapse"
        :collapse-transition="false"
        unique-opened
        text-color="#bfcbd9"
        active-text-color="#ffffff"
      >
        <el-menu-item index="/">
          <el-icon><Monitor /></el-icon>
          <template #title>工作台</template>
        </el-menu-item>

        <el-sub-menu index="/customers">
          <template #title>
            <el-icon><User /></el-icon>
            <span>客户管理</span>
          </template>
          <el-menu-item index="/customers">客户列表</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/products">
          <template #title>
            <el-icon><Goods /></el-icon>
            <span>产品管理</span>
          </template>
          <el-menu-item index="/products">产品列表</el-menu-item>
          <el-menu-item index="/products/categories">产品分类</el-menu-item>
          <el-menu-item index="/products/brands">品牌管理</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/quotation">
          <template #title>
            <el-icon><Files /></el-icon>
            <span>报价管理</span>
          </template>
          <el-menu-item index="/quotation-requests">报价需求表</el-menu-item>
          <el-menu-item index="/quotations">报价单</el-menu-item>
          <el-menu-item index="/quotation-templates">报价模板</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/orders">
          <template #title>
            <el-icon><Document /></el-icon>
            <span>订单管理</span>
          </template>
          <el-menu-item index="/orders">订单</el-menu-item>
          <el-menu-item index="/delivery-notes">发货单</el-menu-item>
          <el-menu-item index="/return-orders">退货单</el-menu-item>
          <el-menu-item index="/statements">对账单</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/finance">
          <template #title>
            <el-icon><Money /></el-icon>
            <span>财务管理</span>
          </template>
          <el-menu-item index="/finance/payment-records">收款记录</el-menu-item>
          <el-menu-item index="/finance/refunds">退款记录</el-menu-item>
          <!-- <el-menu-item index="/finance/receivables">应收款项</el-menu-item> -->
        </el-sub-menu>

        <el-sub-menu index="/system">
          <template #title>
            <el-icon><Setting /></el-icon>
            <span>系统管理</span>
          </template>
          <el-menu-item index="/system/company-info">企业信息</el-menu-item>
          <el-menu-item index="/system/bank-accounts">银行账户</el-menu-item>
          <el-menu-item index="/system/backup">数据备份</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-aside>

    <!-- 主体内容 -->
    <el-container direction="vertical" class="right-container">
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button link @click="toggleCollapse" class="collapse-btn">
            <el-icon><Fold v-if="!isCollapse" /><Expand v-else /></el-icon>
          </el-button>

          <!-- 面包屑导航 -->
          <Breadcrumb />
        </div>

        <!-- 右上角用户信息区域已隐藏 -->
        <!-- <div class="header-right">
          <el-button link @click="toggleFullScreen">
            <el-icon><FullScreen /></el-icon>
          </el-button>

          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" :src="userAvatar">{{ userName.substring(0, 1) }}</el-avatar>
              <span class="username">{{ userName }}</span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><UserFilled /></el-icon>个人信息
                </el-dropdown-item>
                <el-dropdown-item command="password">
                  <el-icon><Lock /></el-icon>修改密码
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div> -->
      </el-header>

      <!-- 页面内容 -->
      <el-main class="main">
        <!-- 标签页导航 -->
        <div class="tabs-view-container">
          <el-tabs
            v-model="activeTab"
            type="card"
            class="tabs-view"
            @tab-remove="removeTab"
            @tab-click="clickTab"
          >
            <el-tab-pane
              v-for="item in visitedViews"
              :key="item.path"
              :label="item.title"
              :name="item.path"
              :closable="item.path !== '/'"
            />
          </el-tabs>
        </div>

        <!-- 路由视图 -->
        <div class="main-content">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <keep-alive :include="cachedViews">
                <component :is="Component" />
              </keep-alive>
            </transition>
          </router-view>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Monitor,
  User,
  Goods,
  Document,
  Money,
  Setting,
  Fold,
  Expand,
  FullScreen,
  UserFilled,
  Lock,
  SwitchButton,
  Files,
  OfficeBuilding
} from '@element-plus/icons-vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import { useNavigation } from '@/composables/useNavigation'
import { useApp, useUser } from '@/composables/useStore'

const route = useRoute()
const router = useRouter()
const { activeMenu } = useNavigation()
const { toggleSidebar, settings } = useApp()
const { userName, logout } = useUser()

// 用户头像
const userAvatar = ref('https://placehold.co/40x40/4A6FE3/FFFFFF.svg?text=EMB')

// 侧边栏折叠状态
const isCollapse = computed(() => settings.sidebarCollapsed)

// 处理图片加载错误
const handleImgError = (e: Event) => {
  const target = e.target as HTMLImageElement
  target.src = 'https://placehold.co/40x40/ff0000/FFFFFF.svg?text=EMB'
}

// 路由映射表
const routeMap: Record<string, { title: string; icon: string }> = {
  '/': { title: '工作台', icon: 'Monitor' },
  '/customers': { title: '客户管理', icon: 'User' },
  '/customers/new': { title: '新增客户', icon: 'User' },
  '/products': { title: '产品管理', icon: 'Goods' },
  '/products/new': { title: '新增产品', icon: 'Goods' },
  '/products/categories': { title: '产品分类管理', icon: 'Goods' },
  '/products/brands': { title: '品牌管理', icon: 'Star' },
  '/orders': { title: '订单', icon: 'Document' },
  '/orders/edit/:id': { title: '编辑订单', icon: 'Document' },
  '/orders/view/:id': { title: '订单详情', icon: 'Document' },
  '/delivery-notes': { title: '发货单管理', icon: 'Document' },
  '/delivery-notes/new': { title: '新建发货单', icon: 'Document' },
  '/delivery-notes/edit/:id': { title: '编辑发货单', icon: 'Document' },
  '/delivery-notes/view/:id': { title: '发货单详情', icon: 'Document' },
  '/return-orders': { title: '退货单', icon: 'Document' },
  '/statements': { title: '对账单', icon: 'Document' },
  '/quotation-requests': { title: '报价需求表', icon: 'Files' },
  '/quotation-requests/new': { title: '新增报价需求', icon: 'Files' },
  '/quotation-requests/edit/:id': { title: '编辑报价需求', icon: 'Files' },
  '/quotation-requests/view/:id': { title: '报价需求详情', icon: 'Files' },
  '/quotations': { title: '报价单', icon: 'Files' },
  '/quotations/new': { title: '新增报价单', icon: 'Files' },
  '/quotations/edit/:id': { title: '编辑报价单', icon: 'Files' },
  '/quotations/view/:id': { title: '报价单详情', icon: 'Files' },
  '/quotation-templates': { title: '报价模板', icon: 'Files' },
  '/finance/payment-records': { title: '收款记录', icon: 'Money' },
  '/finance/payment-records/new': { title: '新增收款记录', icon: 'Money' },
  '/finance/payment-records/edit/:id': { title: '编辑收款记录', icon: 'Money' },
  '/finance/payment-records/view/:id': { title: '收款记录详情', icon: 'Money' },
  '/finance/refunds': { title: '退款记录', icon: 'Money' },
  '/finance/receivables': { title: '应收款项', icon: 'Money' },
  '/enterprise/company-info': { title: '企业信息', icon: 'OfficeBuilding' },
  '/enterprise/bank-accounts': { title: '银行账户', icon: 'CreditCard' },
  '/enterprise/users': { title: '用户管理', icon: 'User' },
  '/system/settings': { title: '基本设置', icon: 'Setting' },
  '/system/backup': { title: '数据备份', icon: 'Download' }
}

// 标签页视图
interface VisitedView {
  title: string
  path: string
  icon: string
}

const visitedViews = ref<VisitedView[]>([
  { title: '工作台', path: '/', icon: 'Monitor' }
])
const cachedViews = ref<string[]>([])
const activeTab = ref('/')

// 添加访问的视图
const addVisitedView = (route: any) => {
  const { path } = route
  const routeInfo = routeMap[path]

  if (!routeInfo) return

  if (visitedViews.value.some(v => v.path === path)) return

  visitedViews.value.push({
    title: routeInfo.title,
    path: path,
    icon: routeInfo.icon
  })

  // 添加需要缓存的组件
  if (route.meta?.keepAlive) {
    cachedViews.value.push(route.name)
  }
}

// 监听路由变化，更新标签页
watch(
  () => route.path,
  (newPath) => {
    activeTab.value = newPath
    addVisitedView(route)
  },
  { immediate: true }
)

// 移除标签页
const removeTab = (targetPath: string) => {
  const tabs = visitedViews.value
  const activeTabPath = activeTab.value

  // 如果关闭的是当前激活的页签，需要激活其它页面
  if (activeTabPath === targetPath) {
    const index = tabs.findIndex(tab => tab.path === targetPath)
    // 激活前一个标签页，如果前一个不存在，激活后一个
    if (index === tabs.length - 1) {
      // 关闭的是最后一个，则激活前一个
      activeTab.value = tabs[index - 1].path
    } else {
      // 否则激活后一个
      activeTab.value = tabs[index + 1].path
    }
    router.push(activeTab.value)
  }

  // 从数组中移除
  visitedViews.value = tabs.filter(tab => tab.path !== targetPath)

  // 移除缓存
  const cacheIndex = cachedViews.value.indexOf(route.name as string)
  if (cacheIndex > -1) {
    cachedViews.value.splice(cacheIndex, 1)
  }
}

// 点击标签页
const clickTab = (tab: any) => {
  router.push(tab.props.name)
}

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  toggleSidebar()
}

// 切换全屏模式
const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }
}

// 处理用户下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'password':
      ElMessage.info('修改密码功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        // 使用store的logout方法
        await logout()

        // 跳转到登录页
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}

onMounted(() => {
  // 初始化访问标签页
  const { path } = route
  const routeInfo = routeMap[path]
  if (routeInfo && path !== '/') {
    visitedViews.value.push({
      title: routeInfo.title,
      path: path,
      icon: routeInfo.icon
    })
  }
})
</script>

<style lang="scss" scoped>
/* 添加到style部分的开头，确保全局应用 */
:deep(.el-menu) {
  --el-menu-hover-bg-color: #263445;
  --el-menu-active-color: #ffffff;
  --el-menu-text-color: #bfcbd9;
  --el-menu-hover-text-color: #ffffff;
}

.main-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  margin: 0;
  padding: 0;
  position: relative;
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  position: relative;
  z-index: 10;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #1f2d3d;
  box-shadow: 0 1px 4px rgba(0,0,0,.2);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  flex-shrink: 0;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .logo {
    display: flex;
    align-items: center;
    height: 60px;
    padding: 0 20px;
    background-color: #263445;
    color: #fff;
    position: sticky;
    top: 0;
    z-index: 2;
    flex-shrink: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    img {
      width: 30px;
      height: 30px;
      margin-right: 10px;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }

    span {
      font-size: 14px;
      font-weight: bold;
      white-space: nowrap;
      color: #ffffff;
      text-shadow: none;
      letter-spacing: 0.5px;
    }

    &.collapsed-logo {
      padding: 0;
      justify-content: center;

      img {
        margin-right: 0;
      }
    }
  }

  :deep(.el-menu) {
    border-right: none !important;
    background-color: transparent !important;
  }

  :deep(.el-menu-item) {
    color: #bfcbd9 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
    height: 50px !important;
    line-height: 50px !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    &.is-active {
      background-color: #1890ff !important;
      color: #ffffff !important;
      font-weight: 600 !important;
    }

    &:hover {
      background-color: #263445 !important;
      color: #ffffff !important;
    }
  }

  :deep(.el-sub-menu__title) {
    color: #bfcbd9 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;

    &:hover {
      background-color: #263445 !important;
      color: #ffffff !important;
    }
  }

  :deep(.el-menu--collapse) {
    width: 64px;
  }

  :deep(.el-sub-menu .el-menu) {
    background-color: #1f2d3d !important;
  }

  :deep(.el-sub-menu.is-active .el-sub-menu__title) {
    color: #409EFF !important;
  }

  :deep(.el-menu-item) i,
  :deep(.el-sub-menu__title) i {
    color: inherit;
    font-size: 18px !important;
    margin-right: 10px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 24px !important;
    height: 24px !important;
    transform: scale(1.1) !important;
  }

  .el-menu-vertical {
    border-right: none;
    flex: 1;

    &:not(.el-menu--collapse) {
      width: 220px;
    }
  }
}

.right-container {
  flex: 1;
  width: 0; /* 确保flex子元素可以正确收缩 */
  min-width: 0; /* 确保flex子元素可以正确收缩 */
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  position: relative;

  .header {
    height: 60px;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;

    .header-left {
      display: flex;
      align-items: center;

      .collapse-btn {
        font-size: 18px;
        margin-right: 15px;
        color: #606266;
      }
    }

    .header-right {
      display: flex;
      align-items: center;

      .el-button {
        color: #606266;
        margin-left: 15px;
        font-size: 18px;
      }

      .user-info {
        display: flex;
        align-items: center;
        margin-left: 15px;
        cursor: pointer;

        .username {
          margin-left: 8px;
          color: #606266;
        }
      }
    }
  }

  .main {
    background-color: #f0f2f5;
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .tabs-view-container {
      background: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

      .tabs-view {
        margin: 0;

        :deep(.el-tabs__header) {
          margin: 0;
          border-bottom: 1px solid #d8dce5;

          .el-tabs__nav {
            border: none;
          }

          .el-tabs__item {
            height: 40px;
            line-height: 40px;
            border: none !important;

            &.is-active {
              color: #409EFF;
              background-color: #f5f7fa;
            }
          }
        }
      }
    }

    .main-content {
      padding: 20px;
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      min-height: 0; /* 确保flex子元素可以正确收缩 */

      .fade-transform-enter-active,
      .fade-transform-leave-active {
        transition: all 0.3s;
      }

      .fade-transform-enter-from {
        opacity: 0;
        transform: translateX(-20px);
      }

      .fade-transform-leave-to {
        opacity: 0;
        transform: translateX(20px);
      }
    }
  }
}
</style>
