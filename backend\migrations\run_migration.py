#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单状态系统重构迁移执行器
提供多种迁移执行方式的统一入口

使用方法：
cd backend
python migrations/run_migration.py

功能：
1. 检查当前数据库状态
2. 选择迁移方式（Python脚本 或 SQL脚本）
3. 执行迁移
4. 验证结果
"""

import os
import sys
import subprocess
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 EMB订单状态系统重构迁移工具")
    print("=" * 60)
    print("功能：将单一status字段迁移到双状态系统")
    print("目标：order_status (物流状态) + payment_status (财务状态)")
    print("=" * 60)

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查数据库文件
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请先启动应用创建数据库")
        return False
    
    # 检查迁移脚本
    script_dir = os.path.dirname(__file__)
    python_script = os.path.join(script_dir, 'deprecate_status_field.py')
    sql_script = os.path.join(script_dir, 'deprecate_status_field.sql')
    
    if not os.path.exists(python_script):
        print(f"❌ Python迁移脚本不存在: {python_script}")
        return False
        
    if not os.path.exists(sql_script):
        print(f"❌ SQL迁移脚本不存在: {sql_script}")
        return False
    
    print("✅ 前置条件检查通过")
    return True

def get_database_info():
    """获取数据库信息"""
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查表结构
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        
        has_status = any(col[1] == 'status' for col in columns)
        has_order_status = any(col[1] == 'order_status' for col in columns)
        has_payment_status = any(col[1] == 'payment_status' for col in columns)
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM orders")
        total_orders = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM orders WHERE status IS NOT NULL AND status != ''")
        orders_with_status = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM orders WHERE order_status IS NULL OR order_status = ''")
        orders_need_migration = cursor.fetchone()[0]
        
        return {
            'has_status': has_status,
            'has_order_status': has_order_status,
            'has_payment_status': has_payment_status,
            'total_orders': total_orders,
            'orders_with_status': orders_with_status,
            'orders_need_migration': orders_need_migration
        }
        
    finally:
        conn.close()

def print_database_status(info):
    """打印数据库状态"""
    print("\n📊 当前数据库状态:")
    print(f"   status字段: {'✅ 存在' if info['has_status'] else '❌ 不存在'}")
    print(f"   order_status字段: {'✅ 存在' if info['has_order_status'] else '❌ 不存在'}")
    print(f"   payment_status字段: {'✅ 存在' if info['has_payment_status'] else '❌ 不存在'}")
    print(f"   总订单数: {info['total_orders']}")
    print(f"   有status数据的订单: {info['orders_with_status']}")
    print(f"   需要迁移的订单: {info['orders_need_migration']}")

def choose_migration_method():
    """选择迁移方式"""
    print("\n🛠️  请选择迁移方式:")
    print("1. Python脚本 (推荐) - 更安全，有详细日志")
    print("2. SQL脚本 - 直接执行，速度更快")
    print("3. 仅查看状态，不执行迁移")
    print("4. 退出")
    
    while True:
        choice = input("\n请输入选择 (1-4): ").strip()
        if choice in ['1', '2', '3', '4']:
            return choice
        print("❌ 无效选择，请输入 1-4")

def run_python_migration():
    """执行Python迁移脚本"""
    print("\n🐍 执行Python迁移脚本...")
    script_path = os.path.join(os.path.dirname(__file__), 'deprecate_status_field.py')
    
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, cwd=os.path.dirname(os.path.dirname(__file__)))
        
        print("📋 执行输出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ Python迁移脚本执行成功")
            return True
        else:
            print(f"❌ Python迁移脚本执行失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 执行Python迁移脚本时出错: {str(e)}")
        return False

def run_sql_migration():
    """执行SQL迁移脚本"""
    print("\n📄 执行SQL迁移脚本...")
    
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    sql_script = os.path.join(os.path.dirname(__file__), 'deprecate_status_field.sql')
    
    try:
        # 读取SQL脚本
        with open(sql_script, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 执行SQL脚本
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 分割SQL语句并执行
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip() and not stmt.strip().startswith('--')]
        
        for i, statement in enumerate(statements):
            if statement.upper().startswith('SELECT'):
                # 对于SELECT语句，显示结果
                cursor.execute(statement)
                results = cursor.fetchall()
                if results:
                    print(f"📊 查询结果 {i+1}:")
                    for row in results:
                        print(f"   {row}")
            else:
                # 对于其他语句，直接执行
                cursor.execute(statement)
                print(f"✅ 执行语句 {i+1}: {statement[:50]}...")
        
        conn.commit()
        conn.close()
        
        print("✅ SQL迁移脚本执行成功")
        return True
        
    except Exception as e:
        print(f"❌ 执行SQL迁移脚本时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查前置条件
    if not check_prerequisites():
        sys.exit(1)
    
    # 获取数据库信息
    try:
        db_info = get_database_info()
        print_database_status(db_info)
    except Exception as e:
        print(f"❌ 获取数据库信息失败: {str(e)}")
        sys.exit(1)
    
    # 检查是否需要迁移
    if not db_info['has_order_status'] or not db_info['has_payment_status']:
        print("❌ 错误: 双状态字段不存在")
        print("请先运行: backend/migrations/add_dual_status_fields.sql")
        sys.exit(1)
    
    if db_info['orders_need_migration'] == 0:
        print("✅ 所有订单已完成迁移，无需再次执行")
        return
    
    # 选择迁移方式
    choice = choose_migration_method()
    
    if choice == '1':
        # Python脚本迁移
        success = run_python_migration()
    elif choice == '2':
        # SQL脚本迁移
        print("⚠️  注意: SQL脚本迁移风险较高，建议先备份数据库")
        confirm = input("确认执行吗？(y/N): ").strip().lower()
        if confirm == 'y':
            success = run_sql_migration()
        else:
            print("❌ 用户取消操作")
            return
    elif choice == '3':
        # 仅查看状态
        print("ℹ️  仅查看状态，未执行迁移")
        return
    else:
        # 退出
        print("👋 退出程序")
        return
    
    # 验证迁移结果
    if success:
        print("\n🔍 验证迁移结果...")
        try:
            new_info = get_database_info()
            print_database_status(new_info)
            
            if new_info['orders_need_migration'] == 0:
                print("🎉 迁移完成！所有订单都已成功迁移到双状态系统")
            else:
                print(f"⚠️  仍有 {new_info['orders_need_migration']} 个订单需要迁移")
        except Exception as e:
            print(f"❌ 验证迁移结果失败: {str(e)}")
    
    print("\n📝 下一步操作:")
    print("1. 修改后端代码移除status字段依赖")
    print("2. 修改前端代码使用新的双状态字段")
    print("3. 进行全面测试")
    print("4. 部署到生产环境")

if __name__ == "__main__":
    main()
