import{J as e,u as t,b as a,r as l,L as o,e as u,U as r,f as s,o as d,c as i,i as n,h as c,a as m,t as p,k as v,F as _,m as f,g,j as y,l as h,W as b,ah as q,Z as w}from"./index-3d4c440c.js";import{C as V}from"./CustomerEditDialog-d8ba8163.js";import{s as j}from"./customer-471ca075.js";import{j as x,k as A,m as E}from"./quotation-219e97aa.js";import{_ as R}from"./_plugin-vue_export-helper-1b428a4d.js";import"./CustomerForm-dce303b8.js";import"./CustomerDeliveryAddresses-9519c02d.js";const k={class:"quotation-request-edit"},U={class:"flex-between"},Q={class:"page-title"},$={style:{display:"flex",width:"70%","align-items":"flex-start"}},C={key:0,class:"el-form-item__extra_info"},I={class:"table-operations mb-20"},N={class:"dialog-footer"},S=R(e({__name:"QuotationRequestEdit",setup(e){const R=t(),S=a(),D=l(!1),O=l(!1),P=l([]),Y=l(!1),L=l(),M=l(""),T=l(!1),B=l({}),J=l(!1),F=l(""),H=l({customer_id:void 0,project_name:"",project_address:"",expected_date:"",notes:"",items:[]}),W=o({customer_id:[{required:!0,message:"请选择客户",trigger:"change"}],project_name:[{required:!0,message:"请输入项目名称",trigger:"blur"}],expected_date:[{required:!0,message:"请选择预计采购时间",trigger:"change"}]}),Z=async e=>{var t;if(e){O.value=!0,console.log(`[QuotationRequestEdit] 开始搜索客户: ${e}`);try{const a=await j({name_like:e,per_page:50});let l;console.log("[QuotationRequestEdit] 收到API响应:",a),(null==(t=null==a?void 0:a.data)?void 0:t.items)&&Array.isArray(a.data.items)?(l=a.data.items,console.log("[QuotationRequestEdit] 从 response.data.items 解析列表")):(null==a?void 0:a.data)&&Array.isArray(a.data)?(l=a.data,console.log("[QuotationRequestEdit] 从 response.data 解析列表")):(null==a?void 0:a.items)&&Array.isArray(a.items)?(l=a.items,console.log("[QuotationRequestEdit] 从 response.items 解析列表")):Array.isArray(a)&&(l=a,console.log("[QuotationRequestEdit] 从 response (作为数组) 解析列表")),Array.isArray(l)?(console.log("[QuotationRequestEdit] 客户列表有效, 准备映射:",l),P.value=l.map((e=>({id:e.id,name:e.name||`客户ID: ${e.id}`}))),console.log("[QuotationRequestEdit] 客户选项更新完毕:",P.value)):(console.warn("[QuotationRequestEdit] 无法从响应中解析出有效的客户列表，清空选项。响应:",a),P.value=[])}catch(a){console.error("[QuotationRequestEdit] 搜索客户时发生严重错误:",a),P.value=[],r.error("搜索客户失败，请检查控制台日志。")}finally{O.value=!1,console.log("[QuotationRequestEdit] 搜索流程结束")}}else P.value=[]},X=()=>{M.value&&(B.value={name:M.value},T.value=!0)},z=e=>{e&&e.id&&(P.value.unshift({id:e.id,name:e.name}),H.value.customer_id=e.id,r.success(`新客户 "${e.name}" 已创建并选中`),M.value="",T.value=!1)},G=()=>{H.value.items.push({product_name:"",product_model:"",product_spec:"",quantity:0,unit:"",notes:""})},K=()=>{F.value="",J.value=!0},ee=()=>{},te=()=>{if(!F.value.trim())return void r.warning("请粘贴需要导入的产品数据");const e=F.value.trim().split("\n"),t=[],a=[];if(0===e.length)return void r.info("未粘贴任何数据。");const l=e[0].trim().split("\t").map((e=>e.trim().toLowerCase())),o={},u={product_name:["产品名称","名称","品名","物料名称"],product_model:["产品型号","型号"],product_spec:["产品规格","规格","技术参数"],quantity:["数量","需求数量","用量"],unit:["单位"],notes:["备注","说明"]};if(Object.keys(u).forEach((e=>{const t=e,a=u[t];for(let u=0;u<l.length;u++)if(a.some((e=>l[u].includes(e.toLowerCase())))){o[t]=u;break}})),void 0===o.product_name)return void r.error('无法识别表头中的"产品名称"列，请确保第一行是表头且包含相关关键字。');if(void 0===o.quantity)return void r.error('无法识别表头中的"数量"列，请确保第一行是表头且包含相关关键字。');const s=e.slice(1);s.forEach(((e,l)=>{if(!e.trim())return;const u=e.split("\t"),r={product_name:"",product_model:"",product_spec:"",quantity:1,unit:"",notes:""};let s=!1;Object.keys(o).forEach((e=>{const t=e,d=o[t];if(void 0!==d&&void 0!==u[d]){const e=u[d].trim();if("quantity"===t){if(e){const t=parseInt(e);!isNaN(t)&&t>0?r.quantity=t:a.push(`第 ${l+2} 行 (数据行 ${l+1}): "数量"列的值 "${e}" 无效或非正数，已使用默认值 1。`)}}else r[t]=e;"product_name"===t&&e&&(s=!0)}})),s?t.push(r):s||a.push(`第 ${l+2} 行 (数据行 ${l+1}): 未能解析到有效的产品名称，该行已跳过。`)})),a.length>0&&b.alert(a.join("<br>"),"部分数据导入错误",{dangerouslyUseHTMLString:!0,confirmButtonText:"好的"}),t.length>0?(H.value.items.push(...t),r.success(`成功添加 ${t.length} 条产品明细`),J.value=!1):0===a.length&&s.length>0&&s.some((e=>e.trim()))?r.info("没有解析到有效的产品数据行，请检查数据格式或表头是否正确。"):0===a.length&&r.info("未粘贴任何有效数据行。")},ae=async()=>{if(L.value)try{if(!(await L.value.validate()))return console.log("表单验证失败!"),!1;{D.value=!0;let e=H.value.expected_date;if(""===e&&(e=null),Array.isArray(e)&&(console.warn("[QuotationRequestEdit.vue] expected_date was an array, taking the first element or null:",e),e=e.length>0?String(e[0]):null),null!=e&&!/^\d{4}-\d{2}-\d{2}$/.test(e))return console.error("[QuotationRequestEdit.vue] Invalid expected_date format after processing:",e),r.error("预计采购日期格式无效，请重新选择。"),void(D.value=!1);const t=H.value.items.map((e=>{const t={product_name:e.product_name,product_model:e.product_model,product_spec:e.product_spec,quantity:"string"==typeof e.quantity?parseInt(e.quantity,10)||1:e.quantity||1,unit:e.unit,notes:e.notes};return void 0!==e.id&&null!==e.id&&(t.id=e.id),t.original_product_name=e.original_product_name||e.product_name,t.original_product_model=e.original_product_model||e.product_model,t.original_product_spec=e.original_product_spec||e.product_spec,t.original_unit=e.original_unit||e.unit,t})),a={customer_id:H.value.customer_id,project_name:H.value.project_name,project_address:H.value.project_address,expected_date:e,notes:H.value.notes,items:t};void 0===H.value.status||null===H.value.status||""===H.value.status?delete a.status:a.status=H.value.status;const l=Object.fromEntries(Object.entries(a).filter((([,e])=>void 0!==e)));if(null===e&&null===a.expected_date&&(l.expected_date=null),null===H.value.notes&&null===a.notes&&(l.notes=null),null===H.value.project_address&&null===a.project_address&&(l.project_address=null),console.log("[QuotationRequestEdit.vue] Data to be sent for update/create:",JSON.stringify(l,null,2)),Y.value&&R.params.id)await A(Number(R.params.id),l),r.success("报价需求更新成功");else{const e={...l};e.items&&(e.items=e.items.map((e=>{const{id:t,...a}=e;return a}))),delete e.id,delete e.status,await E(e),r.success("报价需求创建成功")}S.push("/quotation-requests")}}catch(e){console.error("[QuotationRequestEdit.vue] 保存报价需求原始错误:",e);let t="保存报价需求失败",a=null;if(e&&e.errors)a=e.errors,t=e.message||t;else if(e.response&&e.response.data){const l=e.response.data;t=l.message||t,l.errors&&(a=l.errors)}else e.message&&(t=e.message);if(a){console.error("[QuotationRequestEdit.vue] 后端验证错误详情:",a);let e=`${t}:`;if("object"==typeof a&&null!==a){a.expected_date&&Array.isArray(a.expected_date)&&(e+=`\n- 预计采购时间: ${a.expected_date.join(", ")}`,delete a.expected_date);for(const t in a)e+=`\n- ${t}: ${Array.isArray(a[t])?a[t].join(", "):a[t]}`}r.error({message:e.replace(/\n/g,"<br />"),dangerouslyUseHTMLString:!0,duration:7e3,showClose:!0})}else r.error(t)}finally{D.value=!1}},le=()=>{S.back()};return u((async()=>{var e;const t=sessionStorage.getItem("quotationRequestPrefill");if(t){try{const a=JSON.parse(t);if("number"!=typeof a.customer_id||isNaN(a.customer_id)){if(a.customer_name){const t=a.customer_name;M.value=t,H.value.customer_id=void 0,P.value=[];const l=await j({name_like:t});let o=[];if((null==(e=null==l?void 0:l.data)?void 0:e.items)?o=l.data.items:(null==l?void 0:l.data)&&Array.isArray(l.data)?o=l.data:(null==l?void 0:l.items)&&Array.isArray(l.items)?o=l.items:Array.isArray(l)&&(o=l),1===o.length){const e=o[0];H.value.customer_id=e.id,P.value=[{id:e.id,name:e.name}],M.value="",r.info(`已从导入的客户名称 "${t}" 匹配到现有客户。`)}else r.info(`导入的客户名称 "${t}" 在数据库中未精确匹配到唯一客户，您可以选择快速创建。`)}}else H.value.customer_id=a.customer_id,a.customer_name?(P.value=[{id:a.customer_id,name:a.customer_name}],M.value=""):(P.value=[{id:a.customer_id,name:`客户ID: ${a.customer_id}`}],M.value="");a.project_name&&(H.value.project_name=a.project_name),a.project_address&&(H.value.project_address=a.project_address),a.expected_date&&(H.value.expected_date=a.expected_date),a.notes&&(H.value.notes=a.notes),a.items&&Array.isArray(a.items)&&(H.value.items=a.items),r.info("已从导入数据预填表单。")}catch(a){console.error("解析预填数据失败:",a),r.error("加载预填数据失败。")}sessionStorage.removeItem("quotationRequestPrefill")}else(async()=>{const e=R.params.id;if(e){Y.value=!0,D.value=!0;try{const t=await x(Number(e));let a;if(console.log("[QuotationRequestEdit.vue] 获取到报价需求详情响应:",t),!t||"object"!=typeof t)throw new Error("无效的API响应");{const e=t;if(200!==e.code&&0!==e.code||!e.data)if(e.id)a=t,console.log("[QuotationRequestEdit.vue] 响应直接是数据对象");else{if(e.code&&200!==e.code&&0!==e.code)throw new Error(e.message||"获取报价需求失败");console.warn("[QuotationRequestEdit.vue] 尝试从未识别的API响应格式中提取数据",t),e.data&&e.data.id?(a=e.data,console.log("[QuotationRequestEdit.vue] 从非标准响应中成功提取数据")):(a=t,r.warning("API响应格式不符合预期，数据可能不完整"))}else a=e.data,console.log(`[QuotationRequestEdit.vue] 使用标准API响应中的data字段，code=${e.code}`)}if(!a||!a.id)throw new Error("获取报价需求详情失败：返回数据无效");Object.assign(H.value,a);const l=null==a?void 0:a.customer;l&&"number"==typeof l.id&&"string"==typeof l.name&&(P.value.find((e=>e.id===l.id))||(P.value=[{id:l.id,name:l.name}]))}catch(t){console.error("加载报价需求失败:",t),r.error("加载报价需求失败")}finally{D.value=!1}}})()})),(e,t)=>{const a=s("el-button"),l=s("el-card"),o=s("el-option"),u=s("el-select"),r=s("el-form-item"),b=s("el-input"),j=s("el-date-picker"),x=s("el-divider"),A=s("el-icon"),E=s("el-table-column"),R=s("el-input-number"),S=s("el-table"),oe=s("el-form"),ue=s("el-alert"),re=s("el-dialog");return d(),i("div",k,[n(l,{class:"header-card mb-20"},{default:c((()=>[m("div",U,[m("h2",Q,p(Y.value?"编辑报价需求":"新增报价需求"),1),m("div",null,[n(a,{onClick:le},{default:c((()=>t[11]||(t[11]=[v("取消")]))),_:1}),n(a,{type:"primary",loading:D.value,onClick:ae},{default:c((()=>t[12]||(t[12]=[v(" 保存 ")]))),_:1},8,["loading"])])])])),_:1}),n(l,null,{default:c((()=>[n(oe,{ref_key:"formRef",ref:L,model:H.value,rules:W,"label-width":"120px",class:"quotation-request-form"},{default:c((()=>[n(r,{label:"客户",prop:"customer_id"},{default:c((()=>[m("div",$,[n(u,{modelValue:H.value.customer_id,"onUpdate:modelValue":t[0]||(t[0]=e=>H.value.customer_id=e),filterable:"",remote:"",placeholder:M.value&&void 0===H.value.customer_id?`新客户 (待创建): ${M.value}`:"请选择或搜索客户","remote-method":Z,loading:O.value,clearable:"",style:{"flex-grow":"1","margin-right":"10px"},onClear:t[1]||(t[1]=()=>{H.value.customer_id=void 0,M.value=""}),onChange:t[2]||(t[2]=e=>{"number"==typeof e&&(M.value="")})},{default:c((()=>[(d(!0),i(_,null,f(P.value,(e=>(d(),g(o,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder","loading"]),M.value&&void 0===H.value.customer_id?(d(),g(a,{key:0,type:"success",onClick:X,title:"根据导入的客户名称快速创建新客户"},{default:c((()=>[v(" 创建: "+p(M.value),1)])),_:1})):y("",!0)]),M.value&&void 0===H.value.customer_id?(d(),i("div",C,' 当前客户 "'+p(M.value)+'" 来自导入，数据库中可能不存在。您可以直接创建或选择其他已有客户。 ',1)):y("",!0)])),_:1}),n(r,{label:"项目名称",prop:"project_name"},{default:c((()=>[n(b,{modelValue:H.value.project_name,"onUpdate:modelValue":t[3]||(t[3]=e=>H.value.project_name=e),placeholder:"请输入项目名称"},null,8,["modelValue"])])),_:1}),n(r,{label:"项目地址"},{default:c((()=>[n(b,{modelValue:H.value.project_address,"onUpdate:modelValue":t[4]||(t[4]=e=>H.value.project_address=e),type:"textarea",rows:2,placeholder:"请输入项目详细地址"},null,8,["modelValue"])])),_:1}),n(r,{label:"预计采购时间",prop:"expected_date"},{default:c((()=>[n(j,{modelValue:H.value.expected_date,"onUpdate:modelValue":t[5]||(t[5]=e=>H.value.expected_date=e),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),n(r,{label:"备注"},{default:c((()=>[n(b,{modelValue:H.value.notes,"onUpdate:modelValue":t[6]||(t[6]=e=>H.value.notes=e),type:"textarea",rows:2,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),n(x,{"content-position":"left"},{default:c((()=>t[13]||(t[13]=[v("产品明细")]))),_:1}),m("div",I,[n(a,{type:"success",onClick:K,style:{"margin-right":"10px"}},{default:c((()=>[n(A,null,{default:c((()=>[n(h(q))])),_:1}),t[14]||(t[14]=v(" 批量添加 "))])),_:1}),n(a,{type:"primary",onClick:G},{default:c((()=>[n(A,null,{default:c((()=>[n(h(w))])),_:1}),t[15]||(t[15]=v(" 添加产品 "))])),_:1})]),n(S,{data:H.value.items,border:"",style:{width:"100%"}},{default:c((()=>[n(E,{type:"index",width:"50"}),n(E,{label:"产品名称","min-width":"200"},{default:c((({row:e})=>[n(b,{modelValue:e.product_name,"onUpdate:modelValue":t=>e.product_name=t,placeholder:"请输入产品名称",onInput:e=>{}},null,8,["modelValue","onUpdate:modelValue","onInput"])])),_:1}),n(E,{label:"产品型号","min-width":"150"},{default:c((({row:e})=>[n(b,{modelValue:e.product_model,"onUpdate:modelValue":t=>e.product_model=t,placeholder:"请输入产品型号"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),n(E,{label:"规格","min-width":"150"},{default:c((({row:e})=>[n(b,{modelValue:e.product_spec,"onUpdate:modelValue":t=>e.product_spec=t,placeholder:"请输入规格"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),n(E,{label:"数量",width:"120"},{default:c((({row:e})=>[n(R,{modelValue:e.quantity,"onUpdate:modelValue":t=>e.quantity=t,min:1,precision:0,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),n(E,{label:"单位",width:"100"},{default:c((({row:e})=>[n(b,{modelValue:e.unit,"onUpdate:modelValue":t=>e.unit=t,placeholder:"单位"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),n(E,{label:"备注","min-width":"150"},{default:c((({row:e})=>[n(b,{modelValue:e.notes,"onUpdate:modelValue":t=>e.notes=t,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),n(E,{label:"操作",width:"80",fixed:"right"},{default:c((({$index:e})=>[n(a,{type:"danger",link:"",onClick:t=>{return a=e,void H.value.items.splice(a,1);var a}},{default:c((()=>t[16]||(t[16]=[v(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1},8,["model","rules"])])),_:1}),n(re,{modelValue:J.value,"onUpdate:modelValue":t[9]||(t[9]=e=>J.value=e),title:"批量添加产品",width:"60%",onClose:ee},{footer:c((()=>[m("span",N,[n(a,{onClick:t[8]||(t[8]=e=>J.value=!1)},{default:c((()=>t[18]||(t[18]=[v("取消")]))),_:1}),n(a,{type:"primary",onClick:te},{default:c((()=>t[19]||(t[19]=[v(" 确认添加 ")]))),_:1})])])),default:c((()=>[n(ue,{title:"请从Excel中复制数据（包括表头则会自动忽略第一行），按以下列顺序粘贴，并确保每列数据间使用制表符(Tab)分隔：",type:"info","show-icon":"",closable:!1,class:"mb-20"},{default:c((()=>t[17]||(t[17]=[m("p",null,"列顺序：产品名称 | 产品型号 | 规格 | 数量 | 单位 | 备注",-1),m("p",null,"示例：",-1),m("pre",null,"产品A  型号X  大号  10  个  这是备注A\n产品B  型号Y  中号  5   件  这是备注B\n        ",-1)]))),_:1}),n(b,{modelValue:F.value,"onUpdate:modelValue":t[7]||(t[7]=e=>F.value=e),type:"textarea",rows:10,placeholder:"请在此处粘贴Excel数据..."},null,8,["modelValue"])])),_:1},8,["modelValue"]),n(V,{visible:T.value,"onUpdate:visible":t[10]||(t[10]=e=>T.value=e),"initial-data":B.value,onSuccess:z},null,8,["visible","initial-data"])])}}}),[["__scopeId","data-v-1fca3131"]]);export{S as default};
