#!/usr/bin/env python3
"""
修复数据库外键约束的脚本
将客户相关表的外键约束改为 ON DELETE CASCADE
"""
import sqlite3
import os
import shutil
from datetime import datetime

def backup_database():
    """备份数据库"""
    db_path = r'D:\code\EMB-new\backend\instance\project.db'
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库备份完成: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ 数据库备份失败: {str(e)}")
        return None

def fix_foreign_keys():
    """修复外键约束"""
    db_path = r'D:\code\EMB-new\backend\instance\project.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    # 备份数据库
    backup_path = backup_database()
    if not backup_path:
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 开始修复外键约束")
        print("=" * 50)
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 1. 修复 customer_bank_accounts 表
        print("📋 修复 customer_bank_accounts 表...")
        
        # 获取原表数据
        cursor.execute("SELECT * FROM customer_bank_accounts")
        bank_data = cursor.fetchall()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(customer_bank_accounts)")
        bank_columns = cursor.fetchall()
        
        # 删除原表
        cursor.execute("DROP TABLE customer_bank_accounts")
        
        # 重建表（带正确的外键约束）
        cursor.execute("""
            CREATE TABLE customer_bank_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                bank_name VARCHAR(100) NOT NULL,
                account_name VARCHAR(100) NOT NULL,
                account_number VARCHAR(50) NOT NULL,
                is_default BOOLEAN NOT NULL DEFAULT 0,
                notes TEXT,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
            )
        """)
        
        # 恢复数据
        if bank_data:
            placeholders = ','.join(['?' for _ in range(len(bank_columns))])
            cursor.executemany(f"INSERT INTO customer_bank_accounts VALUES ({placeholders})", bank_data)
        
        print("✅ customer_bank_accounts 表修复完成")
        
        # 2. 修复 customer_delivery_addresses 表
        print("📋 修复 customer_delivery_addresses 表...")
        
        # 获取原表数据
        cursor.execute("SELECT * FROM customer_delivery_addresses")
        address_data = cursor.fetchall()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(customer_delivery_addresses)")
        address_columns = cursor.fetchall()
        
        # 删除原表
        cursor.execute("DROP TABLE customer_delivery_addresses")
        
        # 重建表（带正确的外键约束）
        cursor.execute("""
            CREATE TABLE customer_delivery_addresses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                province VARCHAR(50) NOT NULL,
                city VARCHAR(50) NOT NULL,
                district VARCHAR(50) NOT NULL,
                detailed_address VARCHAR(200) NOT NULL,
                contact_person VARCHAR(50) NOT NULL,
                contact_phone VARCHAR(20) NOT NULL,
                is_default BOOLEAN NOT NULL DEFAULT 0,
                notes TEXT,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
            )
        """)
        
        # 恢复数据
        if address_data:
            placeholders = ','.join(['?' for _ in range(len(address_columns))])
            cursor.executemany(f"INSERT INTO customer_delivery_addresses VALUES ({placeholders})", address_data)
        
        print("✅ customer_delivery_addresses 表修复完成")
        
        # 提交事务
        conn.commit()
        
        # 3. 验证修复结果
        print("\n🔍 验证修复结果:")
        
        # 检查外键约束
        cursor.execute("PRAGMA foreign_key_list(customer_bank_accounts)")
        bank_fks = cursor.fetchall()
        for fk in bank_fks:
            print(f"  customer_bank_accounts: {fk[3]} -> {fk[2]}.{fk[4]} (ON DELETE: {fk[6]})")
        
        cursor.execute("PRAGMA foreign_key_list(customer_delivery_addresses)")
        address_fks = cursor.fetchall()
        for fk in address_fks:
            print(f"  customer_delivery_addresses: {fk[3]} -> {fk[2]}.{fk[4]} (ON DELETE: {fk[6]})")
        
        # 4. 测试删除客户ID 9
        print("\n🧪 测试删除客户ID 9:")
        
        try:
            # 检查删除前的数据
            cursor.execute("SELECT COUNT(*) FROM customer_bank_accounts WHERE customer_id = 9")
            bank_count_before = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM customer_delivery_addresses WHERE customer_id = 9")
            address_count_before = cursor.fetchone()[0]
            
            print(f"  删除前: 银行账户 {bank_count_before} 条, 收货地址 {address_count_before} 条")
            
            # 尝试删除客户
            cursor.execute("DELETE FROM customers WHERE id = 9")
            
            # 检查删除后的数据
            cursor.execute("SELECT COUNT(*) FROM customer_bank_accounts WHERE customer_id = 9")
            bank_count_after = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM customer_delivery_addresses WHERE customer_id = 9")
            address_count_after = cursor.fetchone()[0]
            
            print(f"  删除后: 银行账户 {bank_count_after} 条, 收货地址 {address_count_after} 条")
            
            if bank_count_after == 0 and address_count_after == 0:
                print("✅ 级联删除成功！")
                conn.commit()
            else:
                print("❌ 级联删除失败")
                conn.rollback()
                return False
                
        except Exception as e:
            print(f"❌ 测试删除失败: {str(e)}")
            conn.rollback()
            return False
        
        conn.close()
        print("\n🎉 外键约束修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_foreign_keys()
    if success:
        print("\n✅ 修复成功！现在客户删除功能应该正常工作了。")
    else:
        print("\n❌ 修复失败！请检查错误信息。")
