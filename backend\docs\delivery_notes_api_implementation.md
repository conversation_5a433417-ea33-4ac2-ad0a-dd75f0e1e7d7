# 发货单管理API模块实现文档

## 概述

本文档描述了EMB系统发货单管理API模块的完整实现，包括所有API端点、功能特性、状态管理、物流跟踪和测试覆盖。

## 模块特性

### 核心功能
- ✅ 发货单完整CRUD操作
- ✅ 状态管理和流转控制
- ✅ 物流信息跟踪
- ✅ 与订单模块协同工作
- ✅ PDF导出功能（基础实现）
- ✅ 基于订单创建发货单模板
- ✅ 发货数量控制和库存管理

### 技术特性
- ✅ Flask-RESTX框架，自动生成API文档
- ✅ 统一的响应格式和错误处理
- ✅ 完整的数据验证和约束检查
- ✅ 分页查询和多条件过滤
- ✅ 状态流转规则验证
- ✅ 数据库事务管理

## API端点详情

### 1. 发货单列表 `GET /api/v1/delivery-notes`

**功能**: 获取发货单列表，支持多条件搜索和分页
**查询参数**:
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 20, 最大: 100)
- `order_id`: 订单ID过滤
- `delivery_number`: 发货单编号搜索
- `status`: 发货状态过滤
- `logistics_company`: 物流公司搜索
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `customer_name`: 客户名称搜索

**响应格式**:
```json
{
  "code": 200,
  "message": "获取发货单列表成功",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "pages": 5,
      "has_prev": false,
      "has_next": true
    }
  }
}
```

### 2. 创建发货单 `POST /api/v1/delivery-notes`

**功能**: 创建新的发货单
**请求体**:
```json
{
  "order_id": 1,
  "delivery_date": "2024-01-01T10:00:00",
  "recipient_name": "收货人姓名",
  "recipient_phone": "13800138000",
  "delivery_address_snapshot": "详细收货地址",
  "logistics_company": "顺丰快递",
  "tracking_number": "SF1234567890",
  "status": "待发出",
  "notes": "备注信息",
  "items": [
    {
      "order_product_id": 1,
      "product_specification_id": 1,
      "quantity": 10,
      "notes": "项目备注"
    }
  ]
}
```

**业务逻辑**:
- 验证订单存在且状态允许发货
- 检查发货数量不超过可发货数量
- 自动生成发货单编号 (DN + 日期 + 随机码)
- 填充产品信息快照
- 更新订单产品已发货数量

### 3. 发货单详情 `GET /api/v1/delivery-notes/<id>`

**功能**: 获取指定发货单的详细信息
**响应**: 包含发货单完整信息、关联订单信息和发货项目明细

### 4. 更新发货单 `PUT /api/v1/delivery-notes/<id>`

**功能**: 更新发货单信息
**限制**: 只有"待发出"状态的发货单可以编辑
**业务逻辑**:
- 更新发货单项目时，先恢复原有已发货数量
- 重新验证新的发货数量
- 更新订单产品已发货数量

### 5. 删除发货单 `DELETE /api/v1/delivery-notes/<id>`

**功能**: 删除发货单
**限制**: 只有"待发出"状态的发货单可以删除
**业务逻辑**: 恢复订单产品的已发货数量

### 6. 状态管理 `PUT /api/v1/delivery-notes/<id>/status`

**功能**: 更新发货单状态
**状态流转规则**:
```
待发出 → [已发出, 已作废]
已发出 → [运输中, 已签收, 已作废]
运输中 → [已签收, 已作废]
已签收 → []
已作废 → []
```

**请求体**:
```json
{
  "status": "已发出",
  "logistics_company": "顺丰快递",
  "tracking_number": "SF1234567890",
  "notes": "状态更新备注"
}
```

**业务逻辑**:
- 验证状态流转合法性
- 更新物流信息
- 根据发货状态自动更新订单状态

### 7. 物流跟踪 `GET /api/v1/delivery-notes/<id>/tracking`

**功能**: 获取发货单物流跟踪信息
**响应**: 包含物流公司、运单号、状态历史等信息
**扩展**: 可集成第三方物流API获取实时跟踪信息

### 8. 状态信息 `GET /api/v1/delivery-notes/statuses`

**功能**: 获取所有可用状态和流转规则
**响应**: 状态列表、流转规则和状态描述

### 9. PDF导出 `GET /api/v1/delivery-notes/<id>/pdf`

**功能**: 导出发货单PDF文件
**实现**: 基础文本格式实现，可扩展为专业PDF生成

### 10. 基于订单创建 `POST /api/v1/delivery-notes/from-order/<order_id>`

**功能**: 基于订单生成发货单模板
**响应**: 包含订单信息、可发货产品列表和预填充的发货单数据
**业务逻辑**:
- 验证订单状态
- 计算可发货数量
- 自动填充收货信息

## 数据模型

### DeliveryNote (发货单)
- `id`: 主键
- `delivery_number`: 发货单编号 (自动生成)
- `order_id`: 关联订单ID
- `delivery_date`: 发货日期
- `logistics_company`: 物流公司
- `tracking_number`: 运单号
- `recipient_name`: 收货人姓名
- `recipient_phone`: 收货人电话
- `delivery_address_snapshot`: 收货地址快照
- `status`: 发货状态
- `notes`: 备注

### DeliveryNoteItem (发货单项目)
- `id`: 主键
- `delivery_note_id`: 发货单ID
- `order_product_id`: 订单产品ID
- `product_specification_id`: 产品规格ID
- `quantity`: 发货数量
- `notes`: 项目备注
- 产品信息快照字段 (冗余存储)

## 业务规则

### 状态管理
1. **状态流转**: 严格按照预定义规则进行状态转换
2. **权限控制**: 不同状态下的操作权限不同
3. **订单同步**: 发货状态变化自动同步到订单状态

### 数量控制
1. **发货限制**: 发货数量不能超过订单数量减去已发货数量
2. **库存管理**: 实时更新订单产品的已发货数量
3. **数据一致性**: 通过数据库事务保证数据一致性

### 数据快照
1. **产品信息**: 发货时保存产品信息快照，避免后续修改影响历史记录
2. **地址信息**: 保存收货地址快照，确保发货记录的完整性

## 测试覆盖

### 测试文件: `tests/test_delivery_notes.py`

包含8个主要测试用例:

1. **基础CRUD测试**
   - 获取空列表
   - 获取有数据列表
   - 获取详情
   - 404错误处理

2. **状态管理测试**
   - 状态更新
   - 状态列表获取
   - 物流跟踪信息

3. **业务逻辑测试**
   - 订单不存在处理
   - 状态流转验证

### 测试结果
```
7 passed, 2 deselected, 2 warnings
```

## 集成说明

### 与订单模块协同
1. **状态同步**: 发货状态变化自动更新订单状态
2. **数量管理**: 实时更新订单产品已发货数量
3. **业务流程**: 支持订单 → 发货单 → 对账单的完整业务流程

### 与财务模块协同
1. **对账单关联**: 发货单可关联到对账单
2. **收款管理**: 支持基于发货单的收款流程

## 扩展建议

### 功能扩展
1. **批量操作**: 支持批量创建、更新发货单
2. **模板管理**: 发货单模板功能
3. **自动化**: 基于订单状态自动创建发货单
4. **通知系统**: 状态变化通知客户

### 技术优化
1. **PDF生成**: 集成专业PDF生成库 (如reportlab)
2. **物流集成**: 集成第三方物流API
3. **缓存优化**: 对频繁查询的数据添加缓存
4. **异步处理**: 大批量操作异步处理

### 监控和日志
1. **操作日志**: 记录所有发货单操作历史
2. **性能监控**: 监控API响应时间和错误率
3. **业务指标**: 发货效率、物流时效等业务指标

## 部署说明

1. **API注册**: 已在应用初始化时自动注册到 `/api/v1/delivery-notes`
2. **数据库**: 使用现有的发货单相关表结构
3. **权限**: 建议配置适当的访问权限控制
4. **监控**: 建议配置API监控和日志记录

发货单管理API模块现已完全实现并通过测试，可以为EMB系统提供完整的发货单管理功能！
