<template>
  <div class="data-backup-container">
    <!-- 页面头部 -->
    <el-card class="header-card">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">数据备份与恢复</h2>
          <el-tag type="info" size="large">安全管理您的系统数据</el-tag>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="large" @click="showCreateBackup" :loading="creating">
            <el-icon><Download /></el-icon>
            立即备份
          </el-button>
          <el-button type="success" size="large" @click="showUploadBackup">
            <el-icon><Upload /></el-icon>
            上传恢复
          </el-button>
          <el-button type="info" size="large" @click="showSettings">
            <el-icon><Setting /></el-icon>
            备份设置
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 统计信息卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.total_backups || 0 }}</div>
              <div class="stat-label">总备份数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon manual">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.manual_backups || 0 }}</div>
              <div class="stat-label">手动备份</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon auto">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.auto_backups || 0 }}</div>
              <div class="stat-label">自动备份</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon size">
              <el-icon><Coin /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatFileSize(statistics.total_size || 0) }}</div>
              <div class="stat-label">总大小</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 备份列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <h3>备份历史记录</h3>
          <div class="table-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索备份名称"
              style="width: 250px"
              clearable
              @keyup.enter="loadBackupList"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="loadBackupList">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="backupList" 
        v-loading="tableLoading" 
        stripe 
        border
        style="width: 100%"
        empty-text="暂无备份记录"
      >
        <el-table-column type="index" label="序号" width="80" />
        
        <el-table-column prop="name" label="备份名称" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="backup_type" label="备份类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.backup_type === 'manual' ? 'primary' : 'success'" size="small">
              {{ row.backup_type === 'manual' ? '手动' : '自动' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)" 
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="file_size" label="文件大小" width="120" align="right">
          <template #default="{ row }">
            {{ formatFileSize(row.file_size) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="备注" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="completed_at" label="完成时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.completed_at || row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="240" fixed="right" align="center">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewDetail(row)"
              :disabled="row.status !== 'completed'"
            >
              详情
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="restoreBackup(row)"
              :disabled="row.status !== 'completed'"
            >
              恢复
            </el-button>
            <el-button 
              type="info" 
              size="small" 
              @click="downloadBackup(row)"
              :disabled="row.status !== 'completed'"
            >
              下载
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteBackup(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建备份对话框 -->
    <CreateBackupDialog 
      v-model="createDialogVisible" 
      @success="handleCreateSuccess"
    />

    <!-- 上传备份对话框 -->
    <UploadBackupDialog 
      v-model="uploadDialogVisible" 
      @success="handleUploadSuccess"
    />

    <!-- 备份详情对话框 -->
    <BackupDetailDialog 
      v-model="detailDialogVisible" 
      :backup="selectedBackup"
      @restore="restoreBackup"
    />

    <!-- 恢复确认对话框 -->
    <RestoreConfirmDialog 
      v-model="restoreDialogVisible" 
      :backup="selectedBackup"
      @confirm="handleRestoreConfirm"
    />

    <!-- 备份设置对话框 -->
    <BackupSettingsDialog 
      v-model="settingsDialogVisible" 
      @success="handleSettingsSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Download, Upload, Setting, Search, Refresh, 
  FolderOpened, User, Timer, Coin
} from '@element-plus/icons-vue'
import { backupApi } from '@/api/system'

// 导入子组件
import CreateBackupDialog from '@/components/backup/CreateBackupDialog.vue'
import UploadBackupDialog from '@/components/backup/UploadBackupDialog.vue'
import BackupDetailDialog from '@/components/backup/BackupDetailDialog.vue'
import RestoreConfirmDialog from '@/components/backup/RestoreConfirmDialog.vue'
import BackupSettingsDialog from '@/components/backup/BackupSettingsDialog.vue'

// 响应式数据
const tableLoading = ref(false)
const creating = ref(false)
const searchKeyword = ref('')

// 对话框显示状态
const createDialogVisible = ref(false)
const uploadDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const restoreDialogVisible = ref(false)
const settingsDialogVisible = ref(false)

// 备份列表和分页
const backupList = ref([])
const pagination = reactive({
  page: 1,
  per_page: 20,
  total: 0,
  pages: 0
})

// 统计信息
const statistics = reactive({
  total_backups: 0,
  completed_backups: 0,
  manual_backups: 0,
  auto_backups: 0,
  total_size: 0,
  latest_backup_time: null
})

// 选中的备份
const selectedBackup = ref(null)

// 加载备份列表
const loadBackupList = async () => {
  tableLoading.value = true
  try {
    const response = await backupApi.getBackupList({
      page: pagination.page,
      per_page: pagination.per_page
    }) as any
    
    const data = response.data || response
    backupList.value = data.items || []
    pagination.total = data.total || 0
    pagination.pages = data.pages || 0
    
  } catch (error) {
    console.error('获取备份列表失败:', error)
    ElMessage.error('获取备份列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 加载统计信息
const loadStatistics = async () => {
  try {
    const response = await backupApi.getStatistics() as any
    const data = response.data || response
    Object.assign(statistics, data)
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 显示创建备份对话框
const showCreateBackup = () => {
  createDialogVisible.value = true
}

// 显示上传备份对话框
const showUploadBackup = () => {
  uploadDialogVisible.value = true
}

// 显示设置对话框
const showSettings = () => {
  settingsDialogVisible.value = true
}

// 查看备份详情
const viewDetail = (backup: any) => {
  selectedBackup.value = backup
  detailDialogVisible.value = true
}

// 恢复备份
const restoreBackup = (backup: any) => {
  selectedBackup.value = backup
  restoreDialogVisible.value = true
}

// 下载备份
const downloadBackup = async (backup: any) => {
  try {
    const blob = await backupApi.downloadBackup(backup.id)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = backup.filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 删除备份
const deleteBackup = async (backup: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备份 "${backup.name}" 吗？此操作不可恢复！`,
      '删除备份',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await backupApi.deleteBackup(backup.id)
    ElMessage.success('删除备份成功')
    loadBackupList()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除备份失败:', error)
      ElMessage.error('删除备份失败')
    }
  }
}

// 处理创建成功
const handleCreateSuccess = () => {
  loadBackupList()
  loadStatistics()
}

// 处理上传成功
const handleUploadSuccess = () => {
  loadBackupList()
  loadStatistics()
}

// 处理恢复确认
const handleRestoreConfirm = () => {
  loadBackupList()
  loadStatistics()
}

// 处理设置成功
const handleSettingsSuccess = () => {
  // 可以在这里刷新相关数据
}

// 重置搜索
const resetSearch = () => {
  searchKeyword.value = ''
  pagination.page = 1
  loadBackupList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.per_page = size
  pagination.page = 1
  loadBackupList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadBackupList()
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

const getStatusType = (status: string) => {
  const statusMap = {
    'pending': 'info',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '等待中',
    'running': '进行中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || status
}

// 组件挂载时加载数据
onMounted(() => {
  loadBackupList()
  loadStatistics()
})
</script>

<style scoped>
.data-backup-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.header-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.manual {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.auto {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.size {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .table-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}
</style>
