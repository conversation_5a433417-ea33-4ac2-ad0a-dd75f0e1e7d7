<template>
  <div class="receivable-statistics">
    <el-card class="header-card mb-20">
      <div class="card-header">
        <div class="left">
          <h2 class="page-title">应收款统计</h2>
          <el-tag type="info">订单应收款项统计分析</el-tag>
        </div>
        <div class="right">
          <el-button type="primary" @click="handleExport">
            <el-icon><Download /></el-icon> 导出统计
          </el-button>
          <el-button type="success" @click="handleRefresh">
            <el-icon><Refresh /></el-icon> 刷新数据
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 过滤条件 -->
    <el-card class="mb-20">
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="统计周期">
            <el-select v-model="filterForm.period" placeholder="选择统计周期" @change="handleFilterChange">
              <el-option label="本月" value="current_month" />
              <el-option label="上月" value="last_month" />
              <el-option label="本季度" value="current_quarter" />
              <el-option label="本年度" value="current_year" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </el-form-item>
          <el-form-item label="日期范围" v-if="filterForm.period === 'custom'">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleFilterChange"
            />
          </el-form-item>
          <el-form-item label="客户">
            <el-select 
              v-model="filterForm.customerId" 
              placeholder="选择客户" 
              clearable 
              filterable 
              remote
              :remote-method="searchCustomers"
              :loading="customerLoading"
              @change="handleFilterChange">
              <el-option 
                v-for="item in customerOptions" 
                :key="item.id" 
                :label="item.name" 
                :value="item.id" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" placeholder="选择状态" clearable @change="handleFilterChange">
              <el-option label="全部" value="" />
              <el-option label="已逾期" value="逾期" />
              <el-option label="即将到期" value="未支付" />
              <el-option label="正常" value="部分支付" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :span="6">
        <el-card class="statistic-card">
          <div class="statistic-value">{{ formatCurrency(statistics.totalReceivable) }}</div>
          <div class="statistic-title">应收款总额</div>
          <div class="statistic-footer">
            <el-tag v-if="statistics.totalReceivableGrowth >= 0" type="danger">
              较上期 <el-icon><ArrowUp /></el-icon> {{ statistics.totalReceivableGrowth }}%
            </el-tag>
            <el-tag v-else type="success">
              较上期 <el-icon><ArrowDown /></el-icon> {{ Math.abs(statistics.totalReceivableGrowth) }}%
            </el-tag>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistic-card">
          <div class="statistic-value">{{ formatCurrency(statistics.overdueAmount) }}</div>
          <div class="statistic-title">逾期金额</div>
          <div class="statistic-footer">
            <div class="progress-container">
              <el-progress 
                :percentage="statistics.overduePercentage" 
                :stroke-width="8" 
                :color="overdueProgressColor"
              />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistic-card">
          <div class="statistic-value">{{ statistics.statementCount }}</div>
          <div class="statistic-title">未结清对账单数</div>
          <div class="statistic-footer">
            <el-button size="small" type="primary" @click="viewAllStatements">
              查看所有
            </el-button>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistic-card">
          <div class="statistic-value">{{ statistics.customerCount }}</div>
          <div class="statistic-title">欠款客户数</div>
          <div class="statistic-footer">
            <el-button size="small" type="primary" @click="viewAllCustomers">
              查看所有
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :span="12">
        <el-card class="chart-card" v-loading="agingChartLoading">
          <div class="chart-title">应收款账龄分析</div>
          <div ref="agingChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <div class="chart-title">应收款趋势分析</div>
          <div ref="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 应收款明细表格 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="left">
            <h3 class="card-title">应收款明细</h3>
          </div>
          <div class="right">
            <el-radio-group v-model="viewType" @change="handleViewTypeChange">
              <el-radio-button value="statement">按对账单</el-radio-button>
              <el-radio-button value="customer">按客户</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      
      <div class="table-container" v-loading="tableLoading">
        <!-- 按对账单视图 -->
        <el-table
          v-if="viewType === 'statement'"
          :data="statementList"
          border
          stripe
          style="width: 100%"
          :row-class-name="getRowClass"
        >
          <el-table-column prop="statement_number" label="对账单编号" min-width="150">
            <template #default="scope">
              <el-button type="primary" link @click="viewStatementDetail(scope.row)">
                {{ scope.row.statement_number }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="customer_name" label="客户名称" min-width="200">
            <template #default="scope">
              <el-button type="info" link @click="viewCustomerDetail(scope.row.customer_id)">
                {{ scope.row.customer_name }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="statement_date" label="对账日期" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.statement_date) }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="应收金额" width="150">
            <template #default="scope">
              {{ formatCurrency(scope.row.amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="paid_amount" label="已收金额" width="150">
            <template #default="scope">
              {{ formatCurrency(scope.row.paid_amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="outstanding_amount" label="未收金额" width="150">
            <template #default="scope">
              {{ formatCurrency(scope.row.outstanding_amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="due_date" label="到期日期" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.due_date) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handleAddPayment(scope.row)">
                收款
              </el-button>
              <el-button link type="info" size="small" @click="viewStatementDetail(scope.row)">
                详情
              </el-button>
              <el-button link type="warning" size="small" @click="viewCustomerDetail(scope.row.customer_id)">
                客户
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 按客户视图 -->
        <el-table
          v-else
          :data="customerList"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column prop="customerName" label="客户名称" min-width="200">
            <template #default="scope">
              <el-button type="info" link @click="viewCustomerDetail(scope.row.customerId)">
                {{ scope.row.customerName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="totalAmount" label="应收总额" width="150">
            <template #default="scope">
              {{ formatCurrency(scope.row.totalAmount) }}
            </template>
          </el-table-column>
          <el-table-column prop="paidAmount" label="已收金额" width="150">
            <template #default="scope">
              {{ formatCurrency(scope.row.paidAmount) }}
            </template>
          </el-table-column>
          <el-table-column prop="outstandingAmount" label="未收金额" width="150">
            <template #default="scope">
              {{ formatCurrency(scope.row.outstandingAmount) }}
            </template>
          </el-table-column>
          <el-table-column prop="overdueAmount" label="逾期金额" width="150">
            <template #default="scope">
              {{ formatCurrency(scope.row.overdueAmount) }}
            </template>
          </el-table-column>
          <el-table-column prop="statement_count" label="对账单数" width="100" />
          <el-table-column prop="last_payment_date" label="最近收款日" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.last_payment_date) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="viewCustomerStatements(scope.row)">
                查看对账单
              </el-button>
              <el-button link type="info" size="small" @click="viewCustomerDetail(scope.row.customer_id)">
                客户详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Download, Refresh, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import * as echarts from 'echarts/core'
import { BarChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { receivableApi } from '@/api/finance'
import { customerApi } from '@/api/customer'

// 注册必要的 ECharts 组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  BarChart,
  PieChart,
  CanvasRenderer
])

const router = useRouter()
const agingChart = ref(null)
const trendChart = ref(null)
const tableLoading = ref(false)
const agingChartLoading = ref(false)
const customerLoading = ref(false)
const customerOptions = ref([])
const viewType = ref('statement')

// 过滤条件
const filterForm = reactive({
  period: 'current_month',
  dateRange: [],
  customerId: null,
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 统计数据
const statistics = reactive({
  totalReceivable: 0,
  totalReceivableGrowth: 0,
  overdueAmount: 0,
  overduePercentage: 0,
  statementCount: 0,
  customerCount: 0
})

// 对账单列表数据
const statementList = ref([])

// 客户列表数据
const customerList = ref([])

// 账龄分析数据
const agingData = ref([])

// 逾期进度条颜色
const overdueProgressColor = computed(() => {
  const percentage = statistics.overduePercentage
  if (percentage < 30) return '#67C23A'
  if (percentage < 70) return '#E6A23C'
  return '#F56C6C'
})

// 获取应收款统计数据
const fetchStatisticsData = async () => {
  try {
    const response = await receivableApi.getStats(filterForm) as any
    const data = response.data || response
    
    if (data) {
      statistics.totalReceivable = data.total_receivable || 0
      statistics.totalReceivableGrowth = data.total_receivable_growth || 0
      statistics.overdueAmount = data.overdue_amount || 0
      statistics.overduePercentage = data.overdue_percentage || 0
      statistics.statementCount = data.statement_count || 0
      statistics.customerCount = data.customer_count || 0
    }
  } catch (error) {
    console.error('获取应收款统计数据失败:', error)
    ElMessage.error('获取应收款统计数据失败')

    // 重置统计数据
    statistics.totalReceivable = 0
    statistics.totalReceivableGrowth = 0
    statistics.overdueAmount = 0
    statistics.overduePercentage = 0
    statistics.statementCount = 0
    statistics.customerCount = 0
  }
}

// 获取应收款列表数据
const fetchReceivableData = async () => {
  tableLoading.value = true
  try {
    const params = {
      ...filterForm,
      page: pagination.page,
      per_page: pagination.pageSize,
      view_type: viewType.value
    }
    
    const response = await receivableApi.getList(params) as any
    const data = response.data || response
    
    if (viewType.value === 'statement') {
      statementList.value = Array.isArray(data) ? data : (data.items || [])
    } else {
      customerList.value = Array.isArray(data) ? data : (data.items || [])
    }
    
    pagination.total = response.pagination?.total || 0
  } catch (error) {
    console.error('获取应收款列表数据失败:', error)
    ElMessage.error('获取应收款列表数据失败')

    // 清空数据
    statementList.value = []
    customerList.value = []
    pagination.total = 0
  } finally {
    tableLoading.value = false
  }
}

// 搜索客户
const searchCustomers = async (query: string) => {
  if (query && query.length >= 2) {
    customerLoading.value = true
    try {
      const response = await customerApi.getList({ name: query }) as any
      customerOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('搜索客户失败:', error)
    } finally {
      customerLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

// 初始化账龄分析图表
const initAgingChart = () => {
  if (!agingChart.value) return
  
  const chart = echarts.init(agingChart.value)
  const option = {
    title: {
      text: '账龄分析',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '账龄分析',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 80000, name: '30天内' },
          { value: 60000, name: '31-60天' },
          { value: 70000, name: '61-90天' },
          { value: 50000, name: '90天以上' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化趋势分析图表
const initTrendChart = () => {
  if (!trendChart.value) return
  
  const chart = echarts.init(trendChart.value)
  const option = {
    title: {
      text: '应收款趋势',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['应收款总额', '逾期金额']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '应收款总额',
        type: 'bar',
        data: [200000, 220000, 180000, 240000, 260000, 280000]
      },
      {
        name: '逾期金额',
        type: 'bar',
        data: [50000, 60000, 40000, 80000, 100000, 90000]
      }
    ]
  }
  chart.setOption(option)
}

// 处理过滤条件变化
const handleFilterChange = () => {
  pagination.page = 1
  fetchStatisticsData()
  fetchReceivableData()
}

// 处理视图类型变化
const handleViewTypeChange = () => {
  pagination.page = 1
  fetchReceivableData()
}

// 处理分页变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchReceivableData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchReceivableData()
}

// 刷新数据
const handleRefresh = () => {
  fetchStatisticsData()
  fetchReceivableData()
  nextTick(() => {
    initAgingChart()
    initTrendChart()
  })
}

// 导出统计
const handleExport = async () => {
  try {
    const blob = await receivableApi.export(filterForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `应收款统计_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 查看对账单详情
const viewStatementDetail = (row: any) => {
  router.push(`/statements/view/${row.statement_id || row.id}`)
}

// 查看客户详情
const viewCustomerDetail = (customerId: number) => {
  router.push(`/customers/view/${customerId}`)
}

// 查看客户对账单
const viewCustomerStatements = (row: any) => {
  router.push(`/statements?customer_id=${row.customer_id}`)
}

// 查看所有对账单
const viewAllStatements = () => {
  router.push('/statements')
}

// 查看所有客户
const viewAllCustomers = () => {
  router.push('/customers')
}

// 添加收款记录
const handleAddPayment = (row: any) => {
  router.push(`/statements/view/${row.statement_id || row.id}`)
}

// 获取行类名
const getRowClass = ({ row }: { row: any }) => {
  if (row.status === '逾期') {
    return 'overdue-row'
  }
  return ''
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '未支付': 'danger',
    '部分支付': 'warning',
    '已支付': 'success',
    '逾期': 'danger'
  }
  return statusMap[status] || 'info'
}

// 格式化货币
const formatCurrency = (value: number) => {
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

onMounted(() => {
  fetchStatisticsData()
  fetchReceivableData()
  nextTick(() => {
    initAgingChart()
    initTrendChart()
  })
})
</script>

<style lang="scss" scoped>
.receivable-statistics {
  .header-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .filter-container {
    .filter-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .statistic-card {
    text-align: center;
    
    .statistic-value {
      font-size: 24px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 8px;
    }
    
    .statistic-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 12px;
    }
    
    .statistic-footer {
      font-size: 12px;
      
      .progress-container {
        margin-top: 8px;
      }
    }
  }
  
  .chart-card {
    .chart-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
      text-align: center;
    }
    
    .chart-container {
      height: 300px;
    }
  }
  
  .table-container {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        margin: 0;
        font-size: 16px;
        font-weight: bold;
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

:deep(.overdue-row) {
  background-color: #fef0f0;
}
</style>
