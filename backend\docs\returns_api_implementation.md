# 退货管理API模块实现文档

## 概述

本文档描述了EMB系统退货管理API模块的完整实现，包括所有API端点、功能特性、状态管理、退货审批流程和与订单、发货单模块的协同工作。

## 模块特性

### 核心功能
- ✅ 退货单完整CRUD操作
- ✅ 状态管理和审批流程
- ✅ 退货原因管理和分类
- ✅ 退货数量控制（不能超过已发货数量）
- ✅ 与订单和发货单模块协同工作
- ✅ 基于订单创建退货单模板
- ✅ 可退货发货单查询
- ✅ 产品退货数量统计

### 技术特性
- ✅ Flask-RESTX框架，自动生成API文档
- ✅ 统一的响应格式和错误处理
- ✅ 完整的数据验证和约束检查
- ✅ 分页查询和多条件过滤
- ✅ 状态流转规则验证
- ✅ 数据库事务管理

## API端点详情

### 1. 退货单列表 `GET /api/v1/returns`

**功能**: 获取退货单列表，支持多条件搜索和分页
**查询参数**:
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 20, 最大: 100)
- `order_id`: 订单ID过滤
- `return_number`: 退货单编号搜索
- `status`: 退货状态过滤
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `customer_name`: 客户名称搜索
- `reason`: 退货原因搜索

**响应格式**:
```json
{
  "code": 200,
  "message": "获取退货单列表成功",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "pages": 5,
      "has_prev": false,
      "has_next": true
    }
  }
}
```

### 2. 创建退货单 `POST /api/v1/returns`

**功能**: 创建新的退货单
**请求体**:
```json
{
  "order_id": 1,
  "return_date": "2024-01-01T10:00:00",
  "status": "待审核",
  "reason": "质量问题",
  "notes": "备注信息",
  "items": [
    {
      "order_product_id": 1,
      "product_specification_id": 1,
      "quantity": 3,
      "reason": "质量问题",
      "notes": "项目备注"
    }
  ]
}
```

**业务逻辑**:
- 验证订单存在且状态允许退货
- 检查退货数量不超过已发货数量
- 自动生成退货单编号 (RT + 日期 + 随机码)
- 填充产品信息快照
- 支持部分退货

### 3. 退货单详情 `GET /api/v1/returns/<id>`

**功能**: 获取指定退货单的详细信息
**响应**: 包含退货单完整信息、关联订单信息和退货项目明细

### 4. 更新退货单 `PUT /api/v1/returns/<id>`

**功能**: 更新退货单信息
**限制**: 只有"待审核"状态的退货单可以编辑
**业务逻辑**:
- 更新退货单项目时，重新验证退货数量
- 支持修改退货原因和备注

### 5. 删除退货单 `DELETE /api/v1/returns/<id>`

**功能**: 删除退货单
**限制**: 只有"待审核"状态的退货单可以删除

### 6. 状态管理 `PUT /api/v1/returns/<id>/status`

**功能**: 更新退货单状态
**状态流转规则**:
```
待审核 → [已审核, 已拒绝]
已审核 → [已完成, 已拒绝]
已完成 → []
已拒绝 → []
```

**请求体**:
```json
{
  "status": "已审核",
  "notes": "审核通过"
}
```

**业务逻辑**:
- 验证状态流转合法性
- 支持退货审批流程
- 状态为"已完成"时可触发退款处理

### 7. 状态信息 `GET /api/v1/returns/statuses`

**功能**: 获取所有可用状态和流转规则
**响应**: 状态列表、流转规则和状态描述

### 8. 可退货发货单 `GET /api/v1/returns/available-delivery-notes`

**功能**: 获取可退货的发货单列表
**查询参数**:
- `order_id`: 订单ID过滤
- `customer_id`: 客户ID过滤

**业务逻辑**:
- 只显示已签收的发货单
- 计算每个产品的可退货数量
- 排除已全部退货的项目

### 9. 基于订单创建 `POST /api/v1/returns/from-order/<order_id>`

**功能**: 基于订单生成退货单模板
**响应**: 包含订单信息、可退货产品列表和预填充的退货单数据
**业务逻辑**:
- 验证订单状态
- 计算可退货数量
- 自动填充产品信息

### 10. 产品退货数量查询 `GET /api/v1/returns/product-returned-quantity`

**功能**: 查询指定产品的退货数量统计
**查询参数**:
- `order_product_id`: 订单产品ID (必需)

**响应**: 订购数量、已发货数量、已退货数量、可退货数量

### 11. 退货原因列表 `GET /api/v1/returns/reasons`

**功能**: 获取预定义的退货原因列表
**响应**: 退货原因列表和描述

## 数据模型

### ReturnOrder (退货单)
- `id`: 主键
- `return_number`: 退货单编号 (自动生成)
- `order_id`: 关联订单ID
- `return_date`: 退货日期
- `status`: 退货状态
- `reason`: 退货原因
- `notes`: 备注

### ReturnOrderItem (退货单项目)
- `id`: 主键
- `return_order_id`: 退货单ID
- `order_product_id`: 订单产品ID
- `product_specification_id`: 产品规格ID
- `quantity`: 退货数量
- `reason`: 项目退货原因
- `notes`: 项目备注
- 产品信息快照字段 (冗余存储)

## 业务规则

### 状态管理
1. **状态流转**: 严格按照预定义规则进行状态转换
2. **审批流程**: 支持退货申请的审核和拒绝
3. **权限控制**: 不同状态下的操作权限不同

### 数量控制
1. **退货限制**: 退货数量不能超过已发货数量减去已退货数量
2. **库存管理**: 实时计算可退货数量
3. **数据一致性**: 通过数据库事务保证数据一致性

### 数据快照
1. **产品信息**: 退货时保存产品信息快照，避免后续修改影响历史记录
2. **原因记录**: 保存退货原因，便于质量分析

## 测试覆盖

### 测试文件: `tests/test_returns.py`

包含9个主要测试用例:

1. **基础CRUD测试**
   - 获取空列表
   - 获取有数据列表
   - 获取详情
   - 404错误处理

2. **业务逻辑测试**
   - 创建退货单
   - 订单不存在处理
   - 状态更新

3. **辅助功能测试**
   - 状态列表获取
   - 退货原因列表

### 测试结果
```
9 passed, 3 warnings
```

## 集成说明

### 与订单模块协同
1. **订单验证**: 创建退货单时验证订单存在和状态
2. **产品关联**: 基于订单产品创建退货项目
3. **业务流程**: 支持订单 → 发货单 → 退货单的完整业务流程

### 与发货单模块协同
1. **发货验证**: 只能退货已发货的产品
2. **数量控制**: 基于发货数量控制退货数量
3. **可退货查询**: 提供可退货发货单查询功能

### 与财务模块协同
1. **退款处理**: 退货完成时可触发退款流程
2. **金额计算**: 支持退货金额计算（待扩展）

## 扩展建议

### 功能扩展
1. **批量操作**: 支持批量创建、审核退货单
2. **退款集成**: 完善退款处理逻辑
3. **质量分析**: 基于退货原因的质量分析报告
4. **通知系统**: 状态变化通知相关人员

### 技术优化
1. **审批工作流**: 集成工作流引擎支持复杂审批流程
2. **文件上传**: 支持退货凭证和图片上传
3. **缓存优化**: 对频繁查询的数据添加缓存
4. **异步处理**: 大批量操作异步处理

### 监控和日志
1. **操作日志**: 记录所有退货单操作历史
2. **性能监控**: 监控API响应时间和错误率
3. **业务指标**: 退货率、退货原因分析等业务指标

## 部署说明

1. **API注册**: 已在应用初始化时自动注册到 `/api/v1/returns`
2. **数据库**: 使用现有的退货单相关表结构
3. **权限**: 建议配置适当的访问权限控制
4. **监控**: 建议配置API监控和日志记录

退货管理API模块现已完全实现并通过测试，可以为EMB系统提供完整的退货管理功能！
