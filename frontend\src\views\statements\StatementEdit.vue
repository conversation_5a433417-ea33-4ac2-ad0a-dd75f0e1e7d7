<template>
  <div class="statement-edit">
    <div class="page-header">
      <el-page-header @back="handleBack" :content="pageTitle" />
    </div>

    <div v-loading="loading" class="content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="statement-form"
      >
        <el-card class="basic-info">
          <template #header>
            <span>基本信息</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户" prop="customer_id">
                <el-select
                  v-model="form.customer_id"
                  filterable
                  remote
                  :remote-method="handleSearchCustomers"
                  placeholder="请选择客户"
                  @change="handleCustomerChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in customerOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="对账日期" prop="statement_date">
                <el-date-picker
                  v-model="form.statement_date"
                  type="date"
                  placeholder="请选择对账日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="应付款日期" prop="due_date">
                <el-date-picker
                  v-model="form.due_date"
                  type="date"
                  placeholder="请选择应付款日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="总金额">
                <el-input
                  :value="formatAmount(totalAmount)"
                  readonly
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="优惠金额" prop="discount_amount">
                <el-input-number
                  v-model="form.discount_amount"
                  :min="0"
                  :max="totalAmount"
                  :precision="2"
                  placeholder="请输入优惠金额"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="调整后总金额">
                <el-input
                  :value="formatAmount(adjustedTotalAmount)"
                  readonly
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注">
            <el-input
              v-model="form.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-card>

        <el-card class="delivery-notes">
          <template #header>
            <div class="card-header">
              <span>选择发货单</span>
              <el-button type="primary" @click="showDeliveryNoteDialog">
                添加发货单
              </el-button>
            </div>
          </template>

          <el-table :data="selectedDeliveryNotes" border>
            <el-table-column prop="delivery_number" label="发货单号" width="180" />
            <el-table-column label="订单号" width="180">
              <template #default="{ row }">
                {{ row.order?.order_number || row.order_number || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="delivery_date" label="发货日期" width="120" />
            <el-table-column label="发货金额" width="120">
              <template #default="{ row }">
                {{ calculateDeliveryAmount(row) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row, $index }">
                <el-button
                  link
                  type="danger"
                  @click="removeDeliveryNote($index)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-if="selectedDeliveryNotes.length === 0" class="empty-placeholder">
            <el-empty description="请选择发货单" />
          </div>
        </el-card>

        <el-card class="return-orders">
          <template #header>
            <div class="card-header">
              <span>选择退货单</span>
              <el-button type="primary" @click="showReturnOrderDialog">
                添加退货单
              </el-button>
            </div>
          </template>

          <!-- 有数据时显示表格 -->
          <el-table v-if="selectedReturnOrders.length > 0" :data="selectedReturnOrders" border>
            <el-table-column prop="return_number" label="退货单号" width="180" />
            <el-table-column label="订单号" width="180">
              <template #default="{ row }">
                {{ row.order?.order_number || row.order_number || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="return_date" label="退货日期" width="120" />
            <el-table-column label="退货金额" width="120">
              <template #default="{ row }">
                {{ calculateReturnAmount(row) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row, $index }">
                <el-button
                  link
                  type="danger"
                  @click="removeReturnOrder($index)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 无数据时显示简洁提示 -->
          <div v-else class="empty-state">
            <el-icon class="empty-icon"><ShoppingCart /></el-icon>
            <p class="empty-text">暂无退货单，点击上方"添加退货单"按钮选择</p>
          </div>
        </el-card>

        <div class="form-actions">
          <el-button @click="handleBack">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            保存
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 发货单选择对话框 -->
    <el-dialog
      v-model="deliveryNoteDialogVisible"
      title="选择发货单"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="delivery-note-search">
        <el-form :model="deliveryNoteSearchForm" inline>
          <el-form-item label="发货单号">
            <el-input
              v-model="deliveryNoteSearchForm.delivery_number"
              placeholder="请输入发货单号"
              clearable
            />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input
              v-model="deliveryNoteSearchForm.order_number"
              placeholder="请输入订单号"
              clearable
            />
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="deliveryNoteSearchForm.date_range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchDeliveryNotes">搜索</el-button>
            <el-button @click="resetDeliveryNoteSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        ref="deliveryNoteTableRef"
        v-loading="deliveryNoteLoading"
        :data="availableDeliveryNotes"
        border
        @selection-change="handleDeliveryNoteSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="delivery_number" label="发货单号" width="180" />
        <el-table-column prop="order.order_number" label="订单号" width="180" />
        <el-table-column prop="delivery_date" label="发货日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getDeliveryStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发货金额" width="120">
          <template #default="{ row }">
            {{ calculateDeliveryAmount(row) }}
          </template>
        </el-table-column>
      </el-table>

      <div class="dialog-pagination">
        <el-pagination
          v-model:current-page="deliveryNotePagination.page"
          v-model:page-size="deliveryNotePagination.per_page"
          :page-sizes="[10, 20, 50]"
          :total="deliveryNotePagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="searchDeliveryNotes"
          @current-change="searchDeliveryNotes"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deliveryNoteDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelectDeliveryNotes">
            确定选择 ({{ tempSelectedDeliveryNotes.length }})
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 退货单选择对话框 -->
    <el-dialog
      v-model="returnOrderDialogVisible"
      title="选择退货单"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="return-order-search">
        <el-form :model="returnOrderSearchForm" inline>
          <el-form-item label="退货单号">
            <el-input
              v-model="returnOrderSearchForm.return_number"
              placeholder="请输入退货单号"
              clearable
            />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input
              v-model="returnOrderSearchForm.order_number"
              placeholder="请输入订单号"
              clearable
            />
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="returnOrderSearchForm.date_range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchReturnOrders">搜索</el-button>
            <el-button @click="resetReturnOrderSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        ref="returnOrderTableRef"
        v-loading="returnOrderLoading"
        :data="availableReturnOrders"
        border
        @selection-change="handleReturnOrderSelectionChange"
        @row-click="handleReturnOrderRowClick"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="return_number" label="退货单号" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-tag
                v-if="isReturnOrderSelected(row.id)"
                type="success"
                size="small"
                effect="plain"
                class="mr-1"
              >
                已选择
              </el-tag>
              <span>{{ row.return_number }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单号" width="180" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.order?.order_number || row.order_number || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="return_date" label="退货日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getReturnStatusType(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="退货金额" width="120">
          <template #default="{ row }">
            {{ calculateReturnAmount(row) }}
          </template>
        </el-table-column>
      </el-table>

      <div class="dialog-pagination">
        <el-pagination
          v-model:current-page="returnOrderPagination.page"
          v-model:page-size="returnOrderPagination.per_page"
          :page-sizes="[10, 20, 50]"
          :total="returnOrderPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="searchReturnOrders"
          @current-change="searchReturnOrders"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="returnOrderDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelectReturnOrders">
            确定选择 ({{ tempSelectedReturnOrders.length }})
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ShoppingCart } from '@element-plus/icons-vue'
import type { FormInstance, TableInstance } from 'element-plus'
import type { CustomerSimple } from '@/types/customer'
import type { Statement } from '@/types/statement'
import { searchCustomers } from '@/api/customer'
import { statementApi } from '@/api/statement'
import { formatCurrency } from '@/utils/formatter'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  customer_id: '',
  statement_date: '',
  due_date: '',
  notes: '',
  discount_amount: 0,
  delivery_note_ids: [] as string[]
})

// 表单验证规则
const rules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  statement_date: [
    { required: true, message: '请选择对账日期', trigger: 'change' }
  ]
}

// 客户选项
const customerOptions = ref<CustomerSimple[]>([])

// 选中的发货单
const selectedDeliveryNotes = ref<any[]>([])

// 发货单选择对话框
const deliveryNoteDialogVisible = ref(false)
const deliveryNoteLoading = ref(false)
const deliveryNoteTableRef = ref<TableInstance>()
const availableDeliveryNotes = ref<any[]>([])
const tempSelectedDeliveryNotes = ref<any[]>([])

// 发货单搜索表单
const deliveryNoteSearchForm = reactive({
  delivery_number: '',
  order_number: '',
  date_range: []
})

// 发货单分页
const deliveryNotePagination = reactive({
  page: 1,
  per_page: 10,
  total: 0
})

// 退货单选择对话框
const returnOrderDialogVisible = ref(false)
const returnOrderLoading = ref(false)
const returnOrderTableRef = ref<TableInstance>()
const availableReturnOrders = ref<any[]>([])
const tempSelectedReturnOrders = ref<any[]>([])
const selectedReturnOrders = ref<any[]>([])

// 退货单搜索表单
const returnOrderSearchForm = reactive({
  return_number: '',
  order_number: '',
  date_range: []
})

// 退货单分页
const returnOrderPagination = reactive({
  page: 1,
  per_page: 10,
  total: 0
})

// 计算属性
const pageTitle = computed(() => {
  return route.params.id ? '编辑对账单' : '新建对账单'
})

const totalAmount = computed(() => {
  let total = 0
  // 加上发货单金额
  selectedDeliveryNotes.value.forEach(deliveryNote => {
    // 直接使用数据库中的total_amount字段
    total += parseFloat(deliveryNote.total_amount || 0)
  })
  // 减去退货单金额
  selectedReturnOrders.value.forEach(returnOrder => {
    total -= parseFloat(returnOrder.total_amount || 0)
  })
  return total
})

const adjustedTotalAmount = computed(() => {
  return Math.max(0, totalAmount.value - (form.discount_amount || 0))
})

// 获取对账单详情（编辑模式）
const fetchStatement = async () => {
  const id = route.params.id as string
  if (!id) return

  loading.value = true
  try {
    const response = await statementApi.getById(id)
    const statement = response.data || response

    // 填充表单
    form.customer_id = statement.customer_id
    form.statement_date = statement.statement_date
    form.due_date = statement.due_date || ''
    form.notes = statement.notes || ''
    form.discount_amount = statement.discount_amount || 0

    // 设置选中的发货单
    selectedDeliveryNotes.value = statement.delivery_notes || []
    form.delivery_note_ids = selectedDeliveryNotes.value.map(dn => dn.id)

    // 设置选中的退货单
    selectedReturnOrders.value = statement.return_orders || []
    form.return_order_ids = selectedReturnOrders.value.map(ro => ro.id)

    // 设置客户选项
    if (statement.customer) {
      customerOptions.value = [statement.customer]
    }
  } catch (error) {
    console.error('获取对账单详情失败:', error)
    ElMessage.error('获取对账单详情失败')
    router.push('/statements')
  } finally {
    loading.value = false
  }
}

// 搜索客户
const handleSearchCustomers = async (query: string) => {
  if (query) {
    try {
      const response = await searchCustomers({ name: query, per_page: 20 })
      console.log('客户搜索响应:', response)

      // 处理不同的响应格式
      let customers = []
      if (Array.isArray(response)) {
        customers = response
      } else if (response.data && Array.isArray(response.data)) {
        customers = response.data
      } else if (response.list && Array.isArray(response.list)) {
        customers = response.list
      } else if (response.items && Array.isArray(response.items)) {
        customers = response.items
      }

      customerOptions.value = customers
      console.log('设置客户选项:', customers)
    } catch (error) {
      console.error('搜索客户失败:', error)
    }
  } else {
    customerOptions.value = []
  }
}

// 客户变化处理
const handleCustomerChange = () => {
  // 清空已选择的发货单
  selectedDeliveryNotes.value = []
  form.delivery_note_ids = []
}

// 显示发货单选择对话框
const showDeliveryNoteDialog = () => {
  if (!form.customer_id) {
    ElMessage.warning('请先选择客户')
    return
  }

  deliveryNoteDialogVisible.value = true
  tempSelectedDeliveryNotes.value = [...selectedDeliveryNotes.value]
  searchDeliveryNotes()
}

// 搜索可用发货单
const searchDeliveryNotes = async () => {
  if (!form.customer_id) return

  deliveryNoteLoading.value = true
  try {
    const params = {
      customer_id: form.customer_id,
      page: deliveryNotePagination.page,
      per_page: deliveryNotePagination.per_page,
      delivery_number: deliveryNoteSearchForm.delivery_number,
      order_number: deliveryNoteSearchForm.order_number,
      start_date: deliveryNoteSearchForm.date_range?.[0],
      end_date: deliveryNoteSearchForm.date_range?.[1]
    }

    const response = await statementApi.getAvailableDeliveryNotes(form.customer_id, undefined, params)
    availableDeliveryNotes.value = response.data?.list || response.list || []
    deliveryNotePagination.total = response.data?.pagination?.total || response.pagination?.total || 0

    // 设置已选中的发货单
    setTimeout(() => {
      if (deliveryNoteTableRef.value) {
        tempSelectedDeliveryNotes.value.forEach(selected => {
          const found = availableDeliveryNotes.value.find(item => item.id === selected.id)
          if (found) {
            deliveryNoteTableRef.value!.toggleRowSelection(found, true)
          }
        })
      }
    }, 100)
  } catch (error) {
    console.error('获取可用发货单失败:', error)
    ElMessage.error('获取可用发货单失败')
  } finally {
    deliveryNoteLoading.value = false
  }
}

// 重置发货单搜索
const resetDeliveryNoteSearch = () => {
  deliveryNoteSearchForm.delivery_number = ''
  deliveryNoteSearchForm.order_number = ''
  deliveryNoteSearchForm.date_range = []
  deliveryNotePagination.page = 1
  searchDeliveryNotes()
}

// 发货单选择变化
const handleDeliveryNoteSelectionChange = (selection: any[]) => {
  tempSelectedDeliveryNotes.value = selection
}

// 确认选择发货单
const confirmSelectDeliveryNotes = () => {
  selectedDeliveryNotes.value = [...tempSelectedDeliveryNotes.value]
  form.delivery_note_ids = selectedDeliveryNotes.value.map(dn => dn.id)
  deliveryNoteDialogVisible.value = false
}

// 显示退货单选择对话框
const showReturnOrderDialog = () => {
  if (!form.customer_id) {
    ElMessage.warning('请先选择客户')
    return
  }
  tempSelectedReturnOrders.value = [...selectedReturnOrders.value]
  returnOrderDialogVisible.value = true
  searchReturnOrders()
}

// 搜索退货单
const searchReturnOrders = async () => {
  if (!form.customer_id) return

  returnOrderLoading.value = true
  try {
    const params = {
      customer_id: form.customer_id,
      page: returnOrderPagination.page,
      per_page: returnOrderPagination.per_page,
      return_number: returnOrderSearchForm.return_number,
      order_number: returnOrderSearchForm.order_number,
      start_date: returnOrderSearchForm.date_range?.[0],
      end_date: returnOrderSearchForm.date_range?.[1]
    }

    const response = await statementApi.getAvailableReturnOrders(form.customer_id, undefined, params)
    availableReturnOrders.value = response.data?.list || response.list || []
    returnOrderPagination.total = response.data?.pagination?.total || response.pagination?.total || 0

    // 设置已选中的退货单
    setTimeout(() => {
      if (returnOrderTableRef.value) {
        tempSelectedReturnOrders.value.forEach(selected => {
          const found = availableReturnOrders.value.find(item => item.id === selected.id)
          if (found) {
            returnOrderTableRef.value!.toggleRowSelection(found, true)
          }
        })
      }
    }, 100)
  } catch (error) {
    console.error('获取可用退货单失败:', error)
    ElMessage.error('获取可用退货单失败')
  } finally {
    returnOrderLoading.value = false
  }
}

// 重置退货单搜索
const resetReturnOrderSearch = () => {
  returnOrderSearchForm.return_number = ''
  returnOrderSearchForm.order_number = ''
  returnOrderSearchForm.date_range = []
  returnOrderPagination.page = 1
  searchReturnOrders()
}

// 退货单选择变化
const handleReturnOrderSelectionChange = (selection: any[]) => {
  tempSelectedReturnOrders.value = selection
}

// 退货单行点击
const handleReturnOrderRowClick = (row: any, column: any, event: Event) => {
  // 检查是否点击了文本选择
  const selection = window.getSelection()
  if (selection && selection.toString().length > 0) {
    return
  }

  // 检查是否点击了按钮或链接
  const target = event.target as HTMLElement
  if (target.tagName === 'BUTTON' || target.closest('button') || target.closest('a')) {
    return
  }

  // 切换行选择状态
  if (returnOrderTableRef.value) {
    returnOrderTableRef.value.toggleRowSelection(row)
  }
}

// 确认选择退货单
const confirmSelectReturnOrders = () => {
  selectedReturnOrders.value = [...tempSelectedReturnOrders.value]
  form.return_order_ids = selectedReturnOrders.value.map(ro => ro.id)
  returnOrderDialogVisible.value = false
}

// 检查退货单是否已选择
const isReturnOrderSelected = (id: string) => {
  return tempSelectedReturnOrders.value.some(item => item.id === id)
}

// 移除发货单
const removeDeliveryNote = (index: number) => {
  selectedDeliveryNotes.value.splice(index, 1)
  form.delivery_note_ids = selectedDeliveryNotes.value.map(dn => dn.id)
}

// 计算发货单金额
const calculateDeliveryAmount = (deliveryNote: any) => {
  // 直接使用数据库中的total_amount字段
  return formatAmount(deliveryNote.total_amount || 0)
}

// 移除退货单
const removeReturnOrder = (index: number) => {
  selectedReturnOrders.value.splice(index, 1)
  form.return_order_ids = selectedReturnOrders.value.map(ro => ro.id)
}

// 计算退货单金额
const calculateReturnAmount = (returnOrder: any) => {
  // 直接使用数据库中的total_amount字段
  return formatAmount(returnOrder.total_amount || 0)
}

// 发货单状态类型
const getDeliveryStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待发货': 'warning',
    '已发货': 'primary',
    '已签收': 'success',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

// 退货单状态类型
const getReturnStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待确认': 'warning',
    '退货中': 'primary',
    '已签收': 'success',
    '已拒绝': 'danger',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

// 格式化金额
const formatAmount = (amount: number | string) => {
  if (amount === undefined || amount === null) return formatCurrency(0)
  return formatCurrency(amount)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (selectedDeliveryNotes.value.length === 0) {
        ElMessage.warning('请至少选择一个发货单')
        return
      }

      submitting.value = true
      try {
        const data = {
          customer_id: form.customer_id,
          statement_date: form.statement_date,
          due_date: form.due_date || undefined,
          notes: form.notes || undefined,
          delivery_note_ids: form.delivery_note_ids,
          return_order_ids: form.return_order_ids || [],
          discount_amount: form.discount_amount || 0
        }

        const id = route.params.id as string
        await statementApi.update(id, data)

        ElMessage.success('保存成功')
        router.push(`/statements/${id}`)
      } catch (error) {
        console.error('保存对账单失败:', error)
        ElMessage.error('保存对账单失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回
const handleBack = () => {
  const id = route.params.id as string
  if (id) {
    router.push(`/statements/${id}`)
  } else {
    router.push('/statements')
  }
}

onMounted(() => {
  if (route.params.id) {
    fetchStatement()
  }
})
</script>

<style lang="scss" scoped>
.statement-edit {
  .page-header {
    margin-bottom: 20px;
    padding: 16px 0;
    border-bottom: 1px solid #ebeef5;
  }

  .content {
    .statement-form {
      .basic-info {
        margin-bottom: 20px;
      }

      .delivery-notes {
        margin-bottom: 20px;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .empty-placeholder {
          padding: 40px 0;
        }
      }

      .return-orders {
        margin-bottom: 20px;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .empty-state {
          text-align: center;
          padding: 60px 20px;
          color: #909399;

          .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #c0c4cc;
          }

          .empty-text {
            font-size: 14px;
            margin: 0;
          }
        }
      }

      .form-actions {
        display: flex;
        justify-content: center;
        gap: 16px;
        padding: 20px 0;
      }
    }
  }

  .delivery-note-search {
    margin-bottom: 16px;
  }

  .return-order-search {
    margin-bottom: 16px;
  }

  .dialog-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
