import request from './request'
import type { DashboardStats, ChartData } from '@/types/api'

// 获取仪表板统计数据
export const getDashboardStats = () => {
  return request.get<DashboardStats>('/dashboard/stats', {}, { showError: false })
}

// 获取销售趋势数据
export const getSalesTrends = (period: 'week' | 'month' | 'quarter') => {
  // 根据period转换为后端API期望的days参数
  const daysMap = { week: 7, month: 30, quarter: 90 }
  return request.get<any>('/dashboard/sales_trends', { days: daysMap[period] }, { showError: false })
}

// 获取产品分类分布数据
export const getProductDistribution = (period: 'month' | 'quarter' | 'year') => {
  // 根据period转换为后端API期望的days参数
  const daysMap = { month: 30, quarter: 90, year: 365 }
  return request.get<any>('/dashboard/product_sales', { days: daysMap[period] }, { showError: false })
}

// 获取待处理报价单
export const getPendingQuotations = () => {
  return request.get<any>('/dashboard/pending_quotations', { per_page: 10 }, { showError: false })
}

// 获取待处理订单
export const getPendingOrders = () => {
  return request.get<any>('/dashboard/pending_orders', { per_page: 10 }, { showError: false })
}

// 获取天气信息
export const getWeatherInfo = (city = '深圳') => {
  return request.get<any>('/dashboard/weather', { city }, { showError: false })
}

// 获取用户信息
export const getUserInfo = () => {
  return request.get<any>('/dashboard/user_info', {}, { showError: false })
}
