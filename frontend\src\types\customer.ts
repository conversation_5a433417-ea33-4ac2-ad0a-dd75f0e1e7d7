// 客户相关类型定义

export interface Customer {
  id: number
  name: string
  contact: string
  phone?: string
  email?: string
  address?: string
  tax_id?: string
  source?: string
  level: string
  status: string
  notes?: string
  created_at: string
  updated_at: string
  bank_accounts?: CustomerBankAccount[]
  delivery_addresses?: CustomerDeliveryAddress[]
}

export interface CustomerSimple {
  id: number
  name: string
  contact?: string
  phone?: string
  email?: string
}

export interface CustomerBankAccount {
  id: number
  customer_id: number
  bank_name: string
  account_name: string
  account_number: string
  is_default: boolean
  notes?: string
  created_at: string
  updated_at: string
}

export interface CustomerDeliveryAddress {
  id: number
  customer_id: number
  province: string
  city: string
  district: string
  detailed_address: string
  contact_person: string
  contact_phone: string
  is_default: boolean
  notes?: string
  full_address?: string
  created_at: string
  updated_at: string
}

// 客户搜索响应
export interface CustomerSearchResponse {
  list: CustomerSimple[]
  pagination?: {
    total: number
    page: number
    per_page: number
  }
}
