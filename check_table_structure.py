#!/usr/bin/env python3
"""
检查表结构的脚本
"""
import sqlite3
import os

def check_table_structure():
    """检查表结构"""
    db_path = r'D:\code\EMB-new\backend\instance\project.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查表结构")
        print("=" * 50)
        
        # 检查 customer_bank_accounts 表结构
        print("📋 customer_bank_accounts 表结构:")
        cursor.execute("PRAGMA table_info(customer_bank_accounts)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        # 获取建表语句
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='customer_bank_accounts'")
        create_sql = cursor.fetchone()
        if create_sql:
            print(f"\n建表语句:\n{create_sql[0]}")
        
        print("\n" + "=" * 50)
        
        # 检查 customer_delivery_addresses 表结构
        print("📋 customer_delivery_addresses 表结构:")
        cursor.execute("PRAGMA table_info(customer_delivery_addresses)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        # 获取建表语句
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='customer_delivery_addresses'")
        create_sql = cursor.fetchone()
        if create_sql:
            print(f"\n建表语句:\n{create_sql[0]}")
        
        # 检查数据样本
        print("\n" + "=" * 50)
        print("📋 customer_bank_accounts 数据样本:")
        cursor.execute("SELECT * FROM customer_bank_accounts LIMIT 2")
        rows = cursor.fetchall()
        for row in rows:
            print(f"  {row}")
        
        print("\n📋 customer_delivery_addresses 数据样本:")
        cursor.execute("SELECT * FROM customer_delivery_addresses LIMIT 2")
        rows = cursor.fetchall()
        for row in rows:
            print(f"  {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")

if __name__ == "__main__":
    check_table_structure()
