import type { RouteRecordRaw, RouteMeta as VueRouteMeta } from 'vue-router'

// 扩展路由元信息类型
declare module 'vue-router' {
  interface RouteMeta {
    // 页面标题
    title?: string
    // 面包屑标题（如果与title不同）
    breadcrumbTitle?: string
    // 是否需要认证
    requiresAuth?: boolean
    // 权限角色
    roles?: string[]
    // 是否隐藏在菜单中
    hidden?: boolean
    // 菜单图标
    icon?: string
    // 是否新建页面
    isNew?: boolean
    // 是否查看页面
    isView?: boolean
    // 激活的菜单项（用于子页面）
    activeMenu?: string
    // 是否缓存页面
    keepAlive?: boolean
    // 页面描述
    description?: string
    // 自定义数据
    [key: string]: any
  }
}

// 导出RouteMeta类型
export type RouteMeta = VueRouteMeta

// 面包屑项目类型
export interface BreadcrumbItem {
  title: string
  path?: string
  disabled?: boolean
}

// 菜单项类型
export interface MenuItem {
  path: string
  name: string
  title: string
  icon?: string
  children?: MenuItem[]
  hidden?: boolean
  meta?: RouteMeta
}

// 路由配置类型
export interface AppRouteRecordRaw extends Omit<RouteRecordRaw, 'children' | 'meta'> {
  children?: AppRouteRecordRaw[]
  meta?: RouteMeta
}
