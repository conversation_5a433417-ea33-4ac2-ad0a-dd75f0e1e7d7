"""
发货单导出API模块
基于导出功能规范实现发货单的Excel和PDF导出功能
"""

import os
import tempfile
import platform
from datetime import datetime
from io import BytesIO

import pandas as pd
from flask import request, send_file, current_app
from flask_restx import Resource, Namespace

# 尝试导入reportlab，如果失败则使用简化版本
try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("Warning: reportlab not available, PDF export will be limited")

from ...models.order import DeliveryNote
from ...utils.response import success_response, error_response

# 创建命名空间
api = Namespace('delivery-export', description='发货单导出相关操作')

# 列名映射
COLUMN_MAPPING = {
    # 基本信息字段
    'delivery_number': '发货单号',
    'customer_name': '客户名称',
    'order_number': '关联订单号',
    'project_name': '项目名称',
    'delivery_date': '发货日期',
    'status': '发货状态',
    'settlement_status': '结清状态',
    'settlement_date': '结清日期',
    'total_amount': '总金额',
    'created_at': '创建时间',
    'notes': '发货单备注',
    
    # 物流信息字段
    'logistics_company': '物流公司',
    'tracking_number': '物流单号',
    'recipient_name': '收货人姓名',
    'recipient_phone': '收货人电话',
    'delivery_address_snapshot': '收货地址',
    
    # 产品明细字段
    'product_name': '产品名称',
    'product_model': '产品型号',
    'specification_description': '产品规格',
    'quantity': '发货数量',
    'unit_price': '单价',
    'amount': '金额',
    'item_notes': '明细备注'
}


def setup_chinese_font():
    """设置中文字体"""
    font_name = 'Helvetica'  # 默认字体
    
    try:
        system = platform.system()
        if system == 'Windows':
            # Windows系统字体路径
            font_paths = [
                'C:/Windows/Fonts/simhei.ttf',  # 黑体
                'C:/Windows/Fonts/simsun.ttc',  # 宋体
                'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑
                'C:/Windows/Fonts/simkai.ttf',  # 楷体
            ]
        elif system == 'Darwin':  # macOS
            font_paths = [
                '/System/Library/Fonts/PingFang.ttc',
                '/System/Library/Fonts/STHeiti Light.ttc',
            ]
        else:  # Linux
            font_paths = [
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
            ]

        # 尝试加载中文字体
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                    font_name = 'ChineseFont'
                    print(f"成功加载中文字体: {font_path}")
                    break
                except Exception as e:
                    print(f"加载字体失败 {font_path}: {str(e)}")
                    continue

        # 如果没有找到合适的字体，尝试使用reportlab的内置中文字体
        if font_name == 'Helvetica':
            try:
                from reportlab.pdfbase.cidfonts import UnicodeCIDFont
                pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                font_name = 'STSong-Light'
                print("使用内置中文字体: STSong-Light")
            except Exception as e:
                print(f"无法加载内置中文字体: {str(e)}")
                font_name = 'Helvetica'

    except Exception as e:
        print(f"字体加载异常: {str(e)}")
        font_name = 'Helvetica'

    return font_name


def prepare_export_data(delivery_note, columns=None):
    """准备导出数据 - 参考退货单实现分离布局"""
    print(f"准备导出数据，发货单ID: {delivery_note.id}, 列: {columns}")

    # 字段分类
    basic_fields = ['delivery_number', 'order_number', 'project_name', 'customer_name', 'delivery_date', 'status', 'settlement_status', 'settlement_date', 'total_amount', 'created_at', 'notes', 'logistics_company', 'tracking_number', 'recipient_name', 'recipient_phone', 'delivery_address_snapshot']
    item_fields = ['product_name', 'product_model', 'specification_description', 'quantity', 'unit_price', 'amount', 'item_notes']

    # 分离请求的字段
    requested_columns = columns or []
    basic_columns = [col for col in requested_columns if col in basic_fields]
    item_columns = [col for col in requested_columns if col in item_fields]

    # 基本信息数据
    basic_info = {
        'delivery_number': delivery_note.delivery_number or '',
        'customer_name': delivery_note.order.customer.name if delivery_note.order and delivery_note.order.customer else '',
        'order_number': delivery_note.order.order_number if delivery_note.order else '',
        'project_name': delivery_note.order.project_name if delivery_note.order else '',
        'delivery_date': delivery_note.delivery_date.strftime('%Y-%m-%d') if delivery_note.delivery_date else '',
        'status': delivery_note.status or '',
        'settlement_status': delivery_note.settlement_status or '未结清',
        'settlement_date': delivery_note.settlement_date.strftime('%Y-%m-%d') if delivery_note.settlement_date else '',
        'total_amount': f"{float(delivery_note.total_amount):.2f}" if delivery_note.total_amount else '0.00',
        'created_at': delivery_note.created_at.strftime('%Y-%m-%d %H:%M:%S') if delivery_note.created_at else '',
        'notes': delivery_note.notes or '',
        'logistics_company': delivery_note.logistics_company or '',
        'tracking_number': delivery_note.tracking_number or '',
        'recipient_name': delivery_note.recipient_name or '',
        'recipient_phone': delivery_note.recipient_phone or '',
        'delivery_address_snapshot': delivery_note.delivery_address_snapshot or ''
    }

    # 准备基本信息数据（只有一行）
    basic_data = []
    if basic_columns:
        basic_row = {}
        for col in basic_columns:
            if col in basic_info:
                basic_row[col] = basic_info[col]
        basic_data.append(basic_row)

    # 准备产品明细数据
    item_data = []
    for item in delivery_note.items:
        # 计算金额
        unit_price = 0
        if hasattr(item, 'order_product') and item.order_product:
            unit_price = float(item.order_product.unit_price) if item.order_product.unit_price else 0

        amount = unit_price * float(item.quantity) if item.quantity else 0

        item_row = {
            'product_name': item.product_name or '',
            'product_model': item.product_model or '',
            'specification_description': item.specification_description or '',
            'quantity': str(item.quantity) if item.quantity else '0',
            'unit_price': f"{unit_price:.2f}",
            'amount': f"{amount:.2f}",
            'item_notes': item.notes or ''
        }

        # 只导出请求的产品字段
        if item_columns:
            filtered_item_row = {}
            for col in item_columns:
                if col in item_row:
                    filtered_item_row[col] = item_row[col]
            item_data.append(filtered_item_row)
        else:
            item_data.append(item_row)

    print(f"基本信息数据: {basic_data}")
    print(f"产品明细数据: {item_data}")

    return basic_data, item_data, basic_info


def generate_excel_file(basic_data, item_data, basic_info, include_header=True):
    """生成Excel文件 - 参考退货单实现分离布局"""
    output = BytesIO()

    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        current_row = 0

        # 写入基本信息（如果有）
        if basic_data:
            basic_df = pd.DataFrame(basic_data)
            if not basic_df.empty:
                # 重命名基本信息列
                basic_df = basic_df.rename(columns=COLUMN_MAPPING)
                # 转置基本信息，使其垂直显示
                basic_transposed = basic_df.T.reset_index()
                basic_transposed.columns = ['项目', '内容']
                basic_transposed.to_excel(writer, sheet_name='发货单详情', index=False, startrow=current_row)
                current_row += len(basic_transposed) + 3  # 基本信息行数 + 空行

        # 写入产品明细（如果有）
        if item_data:
            item_df = pd.DataFrame(item_data)
            if not item_df.empty:
                # 重命名产品明细列
                item_df = item_df.rename(columns=COLUMN_MAPPING)

                # 添加产品明细标题
                if basic_data:  # 如果有基本信息，添加产品明细标题
                    title_df = pd.DataFrame([['产品明细']], columns=[''])
                    title_df.to_excel(writer, sheet_name='发货单详情', index=False, header=False, startrow=current_row)
                    current_row += 2

                # 写入产品明细表格
                item_df.to_excel(writer, sheet_name='发货单详情', index=False, startrow=current_row)

        # 如果没有任何数据，创建空表格
        if not basic_data and not item_data:
            empty_df = pd.DataFrame([['暂无数据']], columns=['提示'])
            empty_df.to_excel(writer, sheet_name='发货单详情', index=False)

    output.seek(0)
    return output


def generate_pdf_file(export_data, basic_info, include_header=True):
    """生成PDF文件"""
    # 设置中文字体
    font_name = setup_chinese_font()
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
    temp_file.close()
    
    # 创建PDF文档
    doc = SimpleDocTemplate(temp_file.name, pagesize=A4)
    
    # 创建样式
    styles = getSampleStyleSheet()
    section_style = ParagraphStyle(
        'SectionTitle',
        parent=styles['Heading2'],
        fontName=font_name,
        fontSize=12,
        alignment=0,  # 左对齐
        spaceAfter=10,
        spaceBefore=20
    )
    
    story = []
    
    # 分离基本信息和产品明细数据
    basic_data = []
    product_data = []
    
    basic_fields = set(basic_info.keys())
    
    for row in export_data:
        basic_row = {k: v for k, v in row.items() if k in basic_fields}
        product_row = {k: v for k, v in row.items() if k not in basic_fields}
        
        if basic_row:
            basic_data.append(basic_row)
        if product_row:
            product_data.append(product_row)
    
    # 添加基本信息部分
    if basic_data:
        basic_section = Paragraph("基本信息", section_style)
        story.append(basic_section)
        
        # 创建基本信息表格（两列：项目名称和内容）
        basic_table_data = []
        if include_header:
            basic_table_data.append(['项目', '内容'])
        
        for key, value in basic_data[0].items():  # 只取第一行基本信息
            display_key = COLUMN_MAPPING.get(key, key)
            basic_table_data.append([display_key, str(value)])
        
        if basic_table_data:
            basic_table = Table(basic_table_data, colWidths=[2*inch, 4*inch])
            basic_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey) if include_header else ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(basic_table)
            story.append(Spacer(1, 20))
    
    # 添加产品明细部分
    if product_data:
        product_section = Paragraph("产品明细", section_style)
        story.append(product_section)
        
        # 创建产品明细表格
        product_df = pd.DataFrame(product_data)
        if not product_df.empty:
            product_df = product_df.rename(columns=COLUMN_MAPPING)
            
            # 转换为表格数据
            product_table_data = [product_df.columns.tolist()] if include_header else []
            product_table_data.extend(product_df.values.tolist())
            
            # 创建产品明细表格
            product_table = Table(product_table_data)
            product_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(product_table)
    
    # 构建PDF
    doc.build(story)
    
    return temp_file.name


@api.route('/<int:delivery_note_id>/export')
class DeliveryNoteExport(Resource):
    @api.doc('export_delivery_note')
    def get(self, delivery_note_id):
        """导出发货单"""
        try:
            # 1. 获取请求参数
            format_type = request.args.get('format', 'xlsx')
            columns = request.args.get('columns', '').split(',') if request.args.get('columns') else None
            include_header = request.args.get('include_header', 'true').lower() == 'true'

            # 2. 查询数据
            delivery_note = DeliveryNote.query.get_or_404(delivery_note_id)

            # 3. 准备导出数据
            basic_data, item_data, basic_info = prepare_export_data(delivery_note, columns)

            # 4. 生成文件
            if format_type == 'xlsx':
                print(f"生成Excel文件，基本信息行数: {len(basic_data)}, 产品明细行数: {len(item_data)}")

                # 使用分离布局的Excel导出（参考退货单实现）
                output = generate_excel_file(basic_data, item_data, basic_info, include_header)
                output.seek(0)

                # 生成文件名
                filename = f"发货单_{delivery_note.delivery_number}_{datetime.now().strftime('%Y%m%d')}.xlsx"
                print(f"生成文件名: {filename}")

                return send_file(
                    output,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                
            elif format_type == 'pdf':
                print(f"生成PDF文件，基本信息行数: {len(basic_data)}, 产品明细行数: {len(item_data)}")

                # 生成文件名
                filename = f"发货单_{delivery_note.delivery_number}_{datetime.now().strftime('%Y%m%d')}.pdf"

                if REPORTLAB_AVAILABLE:
                    # 使用reportlab生成真正的PDF
                    output = BytesIO()

                    # 注册中文字体（使用系统默认字体）
                    try:
                        # 尝试注册SimHei字体（黑体）
                        pdfmetrics.registerFont(TTFont('SimHei', 'C:/Windows/Fonts/simhei.ttf'))
                        chinese_font = 'SimHei'
                    except:
                        try:
                            # 如果SimHei不可用，尝试使用SimSun（宋体）
                            pdfmetrics.registerFont(TTFont('SimSun', 'C:/Windows/Fonts/simsun.ttc'))
                            chinese_font = 'SimSun'
                        except:
                            # 如果都不可用，使用默认字体
                            chinese_font = 'Helvetica'
                            print("Warning: 无法加载中文字体，将使用默认字体")

                    # 创建PDF文档
                    doc = SimpleDocTemplate(output, pagesize=A4)
                    story = []

                    # 创建样式
                    styles = getSampleStyleSheet()
                    title_style = ParagraphStyle(
                        'CustomTitle',
                        parent=styles['Heading1'],
                        fontSize=16,
                        alignment=1,  # 居中
                        spaceAfter=20,
                        fontName=chinese_font
                    )

                    # 添加标题
                    title = Paragraph(f"发货单详情 - {delivery_note.delivery_number}", title_style)
                    story.append(title)
                    story.append(Spacer(1, 12))

                    # 创建基本信息表格
                    basic_data = [
                        ['发货单号', delivery_note.delivery_number or ''],
                        ['客户名称', delivery_note.order.customer.name if delivery_note.order and delivery_note.order.customer else ''],
                        ['关联订单号', delivery_note.order.order_number if delivery_note.order else ''],
                        ['项目名称', delivery_note.order.project_name if delivery_note.order else ''],
                        ['发货日期', delivery_note.delivery_date.strftime('%Y-%m-%d') if delivery_note.delivery_date else ''],
                        ['发货状态', delivery_note.status or ''],
                        ['结清状态', delivery_note.settlement_status or '未结清'],
                        ['结清日期', delivery_note.settlement_date.strftime('%Y-%m-%d') if delivery_note.settlement_date else ''],
                        ['总金额', f"{delivery_note.total_amount}" if delivery_note.total_amount else '0.00'],
                        ['创建时间', delivery_note.created_at.strftime('%Y-%m-%d %H:%M:%S') if delivery_note.created_at else ''],
                        ['物流公司', delivery_note.logistics_company or ''],
                        ['物流单号', delivery_note.tracking_number or ''],
                        ['收货人姓名', delivery_note.recipient_name or ''],
                        ['收货人电话', delivery_note.recipient_phone or ''],
                        ['收货地址', delivery_note.delivery_address_snapshot or ''],
                        ['发货单备注', delivery_note.notes or '']
                    ]

                    basic_table = Table(basic_data, colWidths=[2*inch, 4*inch])
                    basic_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(basic_table)
                    story.append(Spacer(1, 20))

                    # 如果有产品明细，添加产品明细表格
                    if delivery_note.items:
                        # 添加产品明细标题
                        product_title_style = ParagraphStyle(
                            'ProductTitle',
                            parent=styles['Heading2'],
                            fontName=chinese_font
                        )
                        product_title = Paragraph("产品明细", product_title_style)
                        story.append(product_title)
                        story.append(Spacer(1, 12))

                        # 创建产品明细表格
                        product_data = [['产品名称', '型号', '规格', '数量', '单价', '金额']]
                        for item in delivery_note.items:
                            product_data.append([
                                item.product_name or '',
                                item.product_model or '',
                                item.specification_description or '',
                                str(item.quantity) if item.quantity else '0',
                                f"{item.order_product.unit_price}" if item.order_product and item.order_product.unit_price else '0.00',
                                f"{item.quantity * item.order_product.unit_price}" if item.quantity and item.order_product and item.order_product.unit_price else '0.00'
                            ])

                        product_table = Table(product_data)
                        product_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, -1), chinese_font),
                            ('FONTSIZE', (0, 0), (-1, -1), 8),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        story.append(product_table)

                    # 构建PDF
                    doc.build(story)
                    output.seek(0)

                    return send_file(
                        output,
                        as_attachment=True,
                        download_name=filename,
                        mimetype='application/pdf'
                    )
                else:
                    # reportlab不可用时的备用方案：生成HTML格式的文件
                    html_content = f"""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <title>发货单详情</title>
                        <style>
                            body {{ font-family: Arial, sans-serif; margin: 20px; }}
                            h1 {{ text-align: center; color: #333; }}
                            table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                            th {{ background-color: #f2f2f2; }}
                        </style>
                    </head>
                    <body>
                        <h1>发货单详情 - {delivery_note.delivery_number}</h1>
                        <table>
                            <tr><th>发货单号</th><td>{delivery_note.delivery_number or ''}</td></tr>
                            <tr><th>客户名称</th><td>{delivery_note.order.customer.name if delivery_note.order and delivery_note.order.customer else ''}</td></tr>
                            <tr><th>发货日期</th><td>{delivery_note.delivery_date.strftime('%Y-%m-%d') if delivery_note.delivery_date else ''}</td></tr>
                            <tr><th>发货状态</th><td>{delivery_note.status or ''}</td></tr>
                            <tr><th>结清状态</th><td>{delivery_note.settlement_status or '未结清'}</td></tr>
                            <tr><th>总金额</th><td>{delivery_note.total_amount if delivery_note.total_amount else '0.00'}</td></tr>
                        </table>
                    """

                    if delivery_note.items:
                        html_content += """
                        <h2>产品明细</h2>
                        <table>
                            <tr><th>产品名称</th><th>型号</th><th>规格</th><th>数量</th><th>单价</th><th>金额</th></tr>
                        """
                        for item in delivery_note.items:
                            html_content += f"""
                            <tr>
                                <td>{item.product_name or ''}</td>
                                <td>{item.product_model or ''}</td>
                                <td>{item.specification_description or ''}</td>
                                <td>{item.quantity if item.quantity else '0'}</td>
                                <td>{item.order_product.unit_price if item.order_product and item.order_product.unit_price else '0.00'}</td>
                                <td>{item.quantity * item.order_product.unit_price if item.quantity and item.order_product and item.order_product.unit_price else '0.00'}</td>
                            </tr>
                            """
                        html_content += "</table>"

                    html_content += "</body></html>"

                    # 创建临时HTML文件
                    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8')
                    temp_file.write(html_content)
                    temp_file.close()

                    return send_file(
                        temp_file.name,
                        as_attachment=True,
                        download_name=filename.replace('.pdf', '.html'),
                        mimetype='text/html'
                    )
            else:
                return error_response("不支持的导出格式", code=400)

        except Exception as e:
            current_app.logger.error(f"导出发货单失败: {str(e)}")
            return error_response(f"导出失败: {str(e)}", code=500)
