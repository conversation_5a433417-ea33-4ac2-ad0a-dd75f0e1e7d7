"""
系统管理相关的序列化模式
基于原项目的Schema定义，确保与现有数据库结构100%兼容
"""
from marshmallow import Schema, fields, validate, validates, ValidationError
from typing import Dict, List, Optional, Any


class SystemSettingSchema(Schema):
    """系统设置序列化模式"""
    id = fields.Int(dump_only=True)
    key = fields.Str(required=True, validate=validate.Length(max=50))
    value = fields.Str(allow_none=True)
    description = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class CompanyInfoSchema(Schema):
    """企业信息序列化模式"""
    id = fields.Integer(dump_only=True)
    name = fields.String(
        required=True,
        validate=validate.Length(min=1, max=200),
        error_messages={"required": "企业名称不能为空"}
    )
    english_name = fields.String(validate=validate.Length(max=200), allow_none=True)
    tax_id = fields.String(
        required=True,
        validate=validate.Length(min=1, max=50),
        error_messages={"required": "统一社会信用代码不能为空"}
    )
    legal_representative = fields.String(validate=validate.Length(max=100), allow_none=True)
    registered_capital = fields.String(validate=validate.Length(max=50), allow_none=True)
    establishment_date = fields.Date(allow_none=True)
    business_scope = fields.String(allow_none=True)
    contact = fields.String(validate=validate.Length(max=50), allow_none=True)
    contact_title = fields.String(validate=validate.Length(max=50), allow_none=True)
    phone = fields.String(validate=validate.Length(max=20), allow_none=True)
    mobile = fields.String(validate=validate.Length(max=20), allow_none=True)
    email = fields.String(validate=validate.Length(max=100), allow_none=True)
    fax = fields.String(validate=validate.Length(max=20), allow_none=True)
    website = fields.String(validate=validate.Length(max=200), allow_none=True)
    registered_address = fields.String(validate=validate.Length(max=500), allow_none=True)
    office_address = fields.String(validate=validate.Length(max=500), allow_none=True)
    postal_code = fields.String(validate=validate.Length(max=10), allow_none=True)
    bank_name = fields.String(validate=validate.Length(max=100), allow_none=True)
    bank_account = fields.String(validate=validate.Length(max=50), allow_none=True)
    logo = fields.String(validate=validate.Length(max=255), allow_none=True)
    license_image = fields.String(validate=validate.Length(max=255), allow_none=True)
    industry = fields.String(validate=validate.Length(max=100), allow_none=True)
    company_type = fields.String(validate=validate.Length(max=50), allow_none=True)
    employee_count = fields.String(validate=validate.Length(max=50), allow_none=True)
    description = fields.String(allow_none=True)
    address = fields.String(validate=validate.Length(max=200), allow_none=True)
    created_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    updated_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")


class CompanyBankAccountSchema(Schema):
    """企业银行账户序列化模式"""
    id = fields.Integer(dump_only=True)
    bank_name = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=100), 
        error_messages={"required": "开户行名称不能为空"}
    )
    account_name = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=100), 
        error_messages={"required": "账户名称不能为空"}
    )
    account_number = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=50), 
        error_messages={"required": "账号不能为空"}
    )
    is_default = fields.Boolean(load_default=False)
    notes = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    updated_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")


class DocumentTemplateSchema(Schema):
    """单据模板序列化模式"""
    id = fields.Integer(dump_only=True)
    name = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=100), 
        error_messages={"required": "模板名称不能为空"}
    )
    type = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=50), 
        error_messages={"required": "模板类型不能为空"}
    )
    content = fields.String(allow_none=True)
    is_default = fields.Boolean(load_default=False)
    status = fields.String(
        validate=validate.OneOf(["启用", "禁用"]), 
        load_default="启用"
    )
    created_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    updated_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")

    @validates("type")
    def validate_type(self, value):
        """验证模板类型"""
        valid_types = ["报价单", "订单", "送货单", "对账单", "退货单"]
        if value not in valid_types:
            raise ValidationError(f"模板类型必须是以下之一: {', '.join(valid_types)}")


class BackupRecordSchema(Schema):
    """备份记录序列化模式"""
    id = fields.Integer(dump_only=True)
    name = fields.String(
        required=True,
        validate=validate.Length(min=1, max=200),
        error_messages={"required": "备份名称不能为空"}
    )
    filename = fields.String(
        required=True,
        validate=validate.Length(min=1, max=255),
        error_messages={"required": "备份文件名不能为空"}
    )
    file_path = fields.String(dump_only=True)
    file_size = fields.Integer(dump_only=True)
    backup_type = fields.String(
        required=True,
        validate=validate.OneOf(["manual", "auto"]),
        error_messages={"required": "备份类型不能为空"}
    )
    status = fields.String(
        validate=validate.OneOf(["pending", "running", "completed", "failed"]),
        missing="pending"
    )
    description = fields.String(validate=validate.Length(max=500), allow_none=True)
    checksum = fields.String(dump_only=True)
    error_message = fields.String(dump_only=True)
    started_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    completed_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    created_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    updated_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")


class BackupSettingsSchema(Schema):
    """备份设置序列化模式"""
    id = fields.Integer(dump_only=True)
    auto_backup_enabled = fields.Boolean(required=True)
    backup_frequency = fields.String(
        required=True,
        validate=validate.OneOf(["daily", "weekly", "monthly"]),
        error_messages={"required": "备份频率不能为空"}
    )
    backup_time = fields.String(
        required=True,
        validate=validate.Regexp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$'),
        error_messages={"required": "备份时间不能为空", "invalid": "时间格式错误，应为HH:MM:SS"}
    )
    backup_day = fields.Integer(
        allow_none=True,
        validate=validate.Range(min=1, max=31)
    )
    keep_count = fields.Integer(
        required=True,
        validate=validate.Range(min=1, max=100),
        error_messages={"required": "保留数量不能为空"}
    )
    backup_path = fields.String(
        required=True,
        validate=validate.Length(min=1, max=500),
        error_messages={"required": "备份路径不能为空"}
    )
    last_backup_time = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    created_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    updated_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")


class CreateBackupSchema(Schema):
    """创建备份请求模式"""
    name = fields.String(
        required=True,
        validate=validate.Length(min=1, max=200),
        error_messages={"required": "备份名称不能为空"}
    )
    description = fields.String(
        validate=validate.Length(max=500),
        allow_none=True
    )
    backup_type = fields.String(
        missing="manual",
        validate=validate.OneOf(["manual", "auto"])
    )


class ErrorLogSchema(Schema):
    """错误日志序列化模式"""
    id = fields.Int(dump_only=True)
    # 兼容旧版本的level字段
    level = fields.Str(allow_none=True)
    # 新版本使用log_type字段
    log_type = fields.Str(allow_none=True)
    message = fields.Str(required=True)
    # 旧版本字段
    details = fields.Str(allow_none=True)
    # 新版本用这个字段
    stack_trace = fields.Str(allow_none=True)
    # 额外信息
    additional_info = fields.Raw(allow_none=True)
    # 旧版本字段
    origin = fields.Str(allow_none=True)
    user_agent = fields.Str(allow_none=True)
    ip_address = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class AutoBackupSettingsSchema(Schema):
    """自动备份设置验证模式"""
    enabled = fields.Boolean(required=True)
    frequency = fields.String(
        required=True, 
        validate=validate.OneOf(['daily', 'weekly', 'monthly'])
    )
    time = fields.String(required=True)  # ISO格式的时间字符串
    keepCount = fields.Integer(
        required=True, 
        validate=validate.Range(min=1, max=50)
    )
    path = fields.String(required=True)
