"""
标准API响应模型
用于Flask-RESTX文档生成
"""
from flask_restx import fields, Model

# 基础响应模型
base_response_model = {
    'code': fields.Integer(required=True, description='响应状态码', example=200),
    'message': fields.String(required=True, description='响应消息', example='操作成功'),
    'timestamp': fields.DateTime(description='响应时间戳')
}

# 成功响应模型
success_response_model = {
    **base_response_model,
    'data': fields.Raw(description='响应数据')
}

# 分页响应模型
paginated_response_model = {
    **base_response_model,
    'data': fields.List(fields.Raw, description='数据列表'),
    'pagination': fields.Nested({
        'page': fields.Integer(description='当前页码'),
        'per_page': fields.Integer(description='每页数量'),
        'total': fields.Integer(description='总记录数'),
        'pages': fields.Integer(description='总页数'),
        'has_next': fields.<PERSON><PERSON><PERSON>(description='是否有下一页'),
        'has_prev': fields.<PERSON><PERSON>an(description='是否有上一页'),
        'next_num': fields.Integer(description='下一页页码'),
        'prev_num': fields.Integer(description='上一页页码')
    }, description='分页信息')
}

# 错误响应模型
error_response_model = {
    **base_response_model,
    'errors': fields.Raw(description='错误详情')
}

# 验证错误响应模型
validation_error_response_model = {
    **base_response_model,
    'errors': fields.Raw(description='验证错误详情，字段名为键，错误信息为值')
}

# 404错误响应模型
not_found_response_model = {
    **base_response_model,
    'code': fields.Integer(default=404, description='响应状态码'),
    'message': fields.String(default='资源不存在', description='响应消息')
}

# 权限错误响应模型
permission_error_response_model = {
    **base_response_model,
    'code': fields.Integer(default=403, description='响应状态码'),
    'message': fields.String(default='权限不足', description='响应消息')
}

# 服务器错误响应模型
server_error_response_model = {
    **base_response_model,
    'code': fields.Integer(default=500, description='响应状态码'),
    'message': fields.String(default='服务器内部错误', description='响应消息')
}
