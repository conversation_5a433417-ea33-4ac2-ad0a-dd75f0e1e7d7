// 发货单相关类型定义

export interface DeliveryNote {
  id: string
  delivery_number: string
  order_id: string
  customer_id: string
  delivery_date: string
  status: string
  tracking_number?: string
  notes?: string
  created_at: string
  updated_at: string
  
  // 关联数据
  order?: {
    id: string
    order_number: string
    customer_id: string
    project_name: string
  }
  customer?: {
    id: string
    name: string
  }
  items?: DeliveryNoteItem[]
}

export interface DeliveryNoteItem {
  id: string
  delivery_note_id: string
  order_product_id: string
  quantity: number
  notes?: string
  created_at: string
  updated_at: string
  
  // 关联数据
  order_product?: {
    id: string
    product_id: string
    specification: string
    unit_price: number
    product?: {
      id: string
      name: string
      model: string
      unit: string
    }
  }
}

// 发货单状态类型
export type DeliveryNoteStatus = '待发货' | '已发货' | '已签收' | '已取消'
