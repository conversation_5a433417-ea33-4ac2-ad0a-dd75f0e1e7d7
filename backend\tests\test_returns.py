"""
退货管理API单元测试
"""
import pytest
import json
from datetime import datetime, timedelta
from app.models.customer import Customer, CustomerDeliveryAddress
from app.models.product import Product, ProductCategory, ProductSpecification
from app.models.order import Order, OrderProduct, DeliveryNote, DeliveryNoteItem
from app.models.return_order import ReturnOrder, ReturnOrderItem


class TestReturnsAPI:
    """退货管理API测试类"""
    
    def test_get_return_orders_empty(self, client, db_session):
        """测试获取空的退货单列表"""
        response = client.get('/api/v1/returns')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取退货单列表成功'
        assert data['data']['items'] == []
        assert 'pagination' in data['data']
    
    def test_get_return_orders_with_data(self, client, db_session, sample_customer, sample_product):
        """测试获取有数据的退货单列表"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='已完成',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        # 创建退货单
        return_order = ReturnOrder(
            return_number='RT20240101001',
            order_id=order.id,
            return_date=datetime.now(),
            status='待审核',
            reason='质量问题'
        )
        db_session.add(return_order)
        db_session.commit()
        
        response = client.get('/api/v1/returns')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']['items']) == 1
        assert data['data']['items'][0]['return_number'] == 'RT20240101001'
        assert data['data']['items'][0]['status'] == '待审核'
    
    def test_get_return_order_detail(self, client, db_session, sample_customer, sample_product):
        """测试获取退货单详情"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='已完成',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        return_order = ReturnOrder(
            return_number='RT20240101001',
            order_id=order.id,
            return_date=datetime.now(),
            status='待审核',
            reason='质量问题'
        )
        db_session.add(return_order)
        db_session.commit()
        
        response = client.get(f'/api/v1/returns/{return_order.id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['return_number'] == 'RT20240101001'
        assert data['data']['reason'] == '质量问题'
    
    def test_get_return_order_not_found(self, client, db_session):
        """测试获取不存在的退货单"""
        response = client.get('/api/v1/returns/999')
        assert response.status_code == 404
        
        data = response.get_json()
        assert data['code'] == 404
        assert '退货单不存在' in data['message']
    
    def test_create_return_order(self, client, db_session, sample_customer, sample_product):
        """测试创建退货单"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='已完成',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        # 创建产品规格或使用现有的
        spec = ProductSpecification.query.filter_by(product_id=sample_product.id).first()
        if not spec:
            spec = ProductSpecification(
                product_id=sample_product.id,
                specification='标准规格',
                cost_price=80.00,
                suggested_price=100.00,
                tax_rate=13.0
            )
            db_session.add(spec)
            db_session.flush()
        
        # 创建订单产品
        order_product = OrderProduct(
            order_id=order.id,
            product_specification_id=spec.id,
            quantity=10,
            unit_price=100.00,
            total_price=1000.00,
            delivered_quantity=8.0  # 已发货8个
        )
        db_session.add(order_product)
        db_session.commit()
        
        # 创建退货单数据
        return_order_data = {
            'order_id': order.id,
            'return_date': datetime.now().isoformat(),
            'status': '待审核',
            'reason': '质量问题',
            'notes': '产品有质量缺陷',
            'items': [
                {
                    'order_product_id': order_product.id,
                    'product_specification_id': spec.id,
                    'quantity': 3,
                    'reason': '质量问题',
                    'notes': '部分退货'
                }
            ]
        }
        
        response = client.post('/api/v1/returns', 
                             data=json.dumps(return_order_data),
                             content_type='application/json')
        assert response.status_code == 201
        
        data = response.get_json()
        assert data['code'] == 201
        assert data['message'] == '退货单创建成功'
        assert 'RT' in data['data']['return_number']
        assert len(data['data']['items']) == 1
    
    def test_create_return_order_invalid_order(self, client, db_session):
        """测试创建退货单时订单不存在"""
        return_order_data = {
            'order_id': 999,
            'return_date': datetime.now().isoformat(),
            'status': '待审核',
            'reason': '质量问题'
        }
        
        response = client.post('/api/v1/returns',
                             data=json.dumps(return_order_data),
                             content_type='application/json')
        assert response.status_code == 404
        
        data = response.get_json()
        assert '订单不存在' in data['message']
    
    def test_update_return_order_status(self, client, db_session, sample_customer, sample_product):
        """测试更新退货单状态"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='已完成',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        return_order = ReturnOrder(
            return_number='RT20240101001',
            order_id=order.id,
            return_date=datetime.now(),
            status='待审核',
            reason='质量问题'
        )
        db_session.add(return_order)
        db_session.commit()
        
        # 更新状态
        status_data = {
            'status': '已审核',
            'notes': '审核通过'
        }
        
        response = client.put(f'/api/v1/returns/{return_order.id}/status',
                            data=json.dumps(status_data),
                            content_type='application/json')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['status'] == '已审核'
    
    def test_get_return_order_statuses(self, client, db_session):
        """测试获取退货单状态列表"""
        response = client.get('/api/v1/returns/statuses')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert 'statuses' in data['data']
        assert 'transitions' in data['data']
        assert 'descriptions' in data['data']
        assert '待审核' in data['data']['statuses']
        assert '已审核' in data['data']['statuses']
    
    def test_get_return_reasons(self, client, db_session):
        """测试获取退货原因列表"""
        response = client.get('/api/v1/returns/reasons')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert 'reasons' in data['data']
        assert 'descriptions' in data['data']
        assert '质量问题' in data['data']['reasons']
        assert '规格不符' in data['data']['reasons']
