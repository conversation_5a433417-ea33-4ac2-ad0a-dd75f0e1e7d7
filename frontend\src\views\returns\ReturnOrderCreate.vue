<template>
  <div class="return-order-create">
    <div class="page-header">
      <h2>新建退货单</h2>
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </div>
    </div>

    <div class="form-container">
      <el-card>
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关联订单" prop="order_id">
                <el-select
                  v-model="form.order_id"
                  placeholder="请选择订单"
                  filterable
                  :loading="loading"
                  clearable
                >
                  <el-option
                    v-for="order in orders"
                    :key="order.id"
                    :label="`${order.order_number} - ${order.project_name} (${order.customer?.name || ''})`"
                    :value="order.id"
                  >
                    <div style="display: flex; justify-content: space-between;">
                      <span>{{ order.order_number }} - {{ order.project_name }}</span>
                      <span style="color: #999; font-size: 12px;">{{ order.customer?.name }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退货日期" prop="return_date">
                <el-date-picker
                  v-model="form.return_date"
                  type="date"
                  placeholder="选择退货日期"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="退货原因" prop="reason">
                <el-input
                  v-model="form.reason"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入退货原因"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input
                  v-model="form.notes"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <el-card style="margin-top: 20px;">
        <template #header>
          <span>退货产品</span>
        </template>
        
        <div class="product-list">
          <div v-if="!form.order_id" class="empty-state">
            <p>请先选择订单，然后选择要退货的产品</p>
          </div>

          <div v-else-if="loadingProducts" class="loading-state">
            <el-icon class="is-loading"><Loading /></el-icon>
            <p>正在加载产品信息...</p>
          </div>

          <div v-else-if="orderProducts.length === 0" class="empty-state">
            <p>该订单没有可退货的产品</p>
          </div>

          <div v-else class="product-table">
            <el-table :data="orderProducts" border>
              <el-table-column type="selection" width="55" />
              <el-table-column prop="product_name" label="产品名称" min-width="120" />
              <el-table-column prop="product_model" label="型号" width="100" />
              <el-table-column prop="specification_description" label="规格" width="100" />
              <el-table-column prop="product_unit" label="单位" width="80" />
              <el-table-column prop="quantity" label="订购数量" width="100" />
              <el-table-column prop="delivered_quantity" label="已发货" width="100">
                <template #default="{ row }">
                  <span :class="{ 'text-success': row.delivered_quantity > 0 }">
                    {{ row.delivered_quantity || 0 }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="unit_price" label="单价" width="100">
                <template #default="{ row }">
                  ¥{{ calculateActualPrice(row).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="退货数量" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.return_quantity"
                    :min="0"
                    :max="row.delivered_quantity || 0"
                    :disabled="!row.delivered_quantity"
                    size="small"
                    @change="updateReturnItem(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="退货金额" width="120">
                <template #default="{ row }">
                  <span class="text-danger">
                    ¥{{ ((row.return_quantity || 0) * calculateActualPrice(row)).toFixed(2) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>

            <div class="product-summary">
              <div class="summary-item">
                <span>退货产品数量：</span>
                <span class="summary-value">{{ selectedProductsCount }} 种</span>
              </div>
              <div class="summary-item">
                <span>退货总金额：</span>
                <span class="summary-value text-danger">¥{{ totalReturnAmount.toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import type { CreateReturnOrderData } from '@/types/return'
import type { Order } from '@/types/order'
import { orderApi } from '@/api/order'
import { returnApi } from '@/api/return'

const router = useRouter()
const route = useRoute()

// 状态
const saving = ref(false)
const loading = ref(false)
const loadingProducts = ref(false)
const formRef = ref()
const orders = ref<Order[]>([])
const orderProducts = ref<any[]>([])
const selectedOrder = ref<Order | null>(null)

// 表单数据
const form = reactive<CreateReturnOrderData>({
  order_id: 0,
  return_date: '',
  reason: '',
  notes: '',
  items: []
})

// 验证规则
const rules = {
  order_id: [
    { required: true, message: '请选择订单', trigger: 'change' }
  ],
  return_date: [
    { required: true, message: '请选择退货日期', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入退货原因', trigger: 'blur' }
  ]
}

// 计算属性
const selectedProductsCount = computed(() => {
  return orderProducts.value.filter(item => (item.return_quantity || 0) > 0).length
})

// 计算实际单价（考虑税率和折扣）
const calculateActualPrice = (item: any) => {
  const basePrice = item.unit_price || 0
  const taxRate = (item.tax_rate || 0) / 100
  const discount = (item.discount || 0) / 100
  return basePrice * (1 - discount) * (1 + taxRate)
}

const totalReturnAmount = computed(() => {
  return orderProducts.value.reduce((total, item) => {
    const actualPrice = calculateActualPrice(item)
    return total + ((item.return_quantity || 0) * actualPrice)
  }, 0)
})

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const response = await orderApi.getList({
      // 获取所有可能需要退货的订单状态
      // status: '已发货,全部发货,部分发货', // 只获取已发货的订单，这些订单才能退货
      per_page: 100 // 获取更多订单供选择
    })

    const responseData = response.data || response
    if (responseData.items) {
      orders.value = responseData.items
    } else if (Array.isArray(responseData)) {
      orders.value = responseData
    } else {
      orders.value = []
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
    orders.value = []
  } finally {
    loading.value = false
  }
}

// 获取订单产品详情
const fetchOrderProducts = async (orderId: number) => {
  if (!orderId) {
    orderProducts.value = []
    return
  }

  loadingProducts.value = true
  try {
    const response = await orderApi.getById(orderId)
    const orderDetail = response.data || response

    if (orderDetail && orderDetail.products) {
      // 只显示已发货数量大于0的产品
      orderProducts.value = orderDetail.products
        .filter((item: any) => (item.delivered_quantity || 0) > 0)
        .map((item: any) => ({
          ...item,
          return_quantity: 0 // 初始化退货数量为0
        }))
    } else {
      orderProducts.value = []
    }

    // 保存选中的订单信息
    selectedOrder.value = orders.value.find(order => order.id === orderId) || null
  } catch (error) {
    console.error('获取订单产品失败:', error)
    ElMessage.error('获取订单产品失败')
    orderProducts.value = []
  } finally {
    loadingProducts.value = false
  }
}

// 更新退货项目
const updateReturnItem = (row: any) => {
  // 更新表单中的退货项目
  const existingIndex = form.items.findIndex(item => item.order_product_id === row.id)

  if (row.return_quantity > 0) {
    const returnItem = {
      order_product_id: row.id,
      product_id: row.product_id,
      quantity: row.return_quantity,
      unit_price: calculateActualPrice(row),
      reason: form.reason
    }

    if (existingIndex >= 0) {
      form.items[existingIndex] = returnItem
    } else {
      form.items.push(returnItem)
    }
  } else {
    // 如果退货数量为0，移除该项目
    if (existingIndex >= 0) {
      form.items.splice(existingIndex, 1)
    }
  }
}

// 监听订单选择变化
watch(() => form.order_id, (newOrderId) => {
  if (newOrderId) {
    fetchOrderProducts(newOrderId)
  } else {
    orderProducts.value = []
    form.items = []
  }
})

// 操作
const handleCancel = () => {
  router.back()
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()

    if (form.items.length === 0) {
      ElMessage.error('请至少选择一个退货产品')
      return
    }

    saving.value = true

    // 准备API数据
    const apiData = {
      order_id: form.order_id,
      return_date: new Date(form.return_date).toISOString(),
      reason: form.reason,
      notes: form.notes,
      items: form.items.map(item => ({
        order_product_id: item.order_product_id,
        quantity: item.quantity
      }))
    }

    console.log('创建退货单:', apiData)

    // 调用API创建退货单
    const response = await returnApi.create(apiData)

    ElMessage.success('退货单创建成功')
    router.push('/returns')
  } catch (error) {
    console.error('创建退货单失败:', error)
    ElMessage.error(error.response?.data?.message || '创建退货单失败')
  } finally {
    saving.value = false
  }
}

// 初始化
onMounted(async () => {
  await fetchOrders()

  // 处理从订单页面传递的参数
  const orderId = route.query.orderId
  if (orderId) {
    form.order_id = Number(orderId)

    // 设置默认退货日期为今天
    form.return_date = new Date().toISOString().split('T')[0]

    // 加载订单产品
    await fetchOrderProducts(Number(orderId))

    // 如果有订单号和项目名称，可以显示提示信息
    const orderNumber = route.query.orderNumber
    const projectName = route.query.projectName
    if (orderNumber && projectName) {
      ElMessage.success(`已自动选择订单：${orderNumber} - ${projectName}`)
    }
  }
})
</script>

<style scoped>
.return-order-create {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-container {
  max-width: 1200px;
}

.product-list {
  min-height: 200px;
}

.empty-state, .loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #999;
}

.loading-state .el-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.product-table {
  width: 100%;
}

.product-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-value {
  font-weight: bold;
  font-size: 16px;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}
</style>
