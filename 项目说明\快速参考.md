# EMB项目快速参考卡片

## 🚀 新AI对话快速上手

### 第一步：必读文档
```
📖 概况.md - 项目全貌和代码定位指南 (必读)
```

### 第二步：根据问题类型选择文档
```
🔧 后端问题 → 后端架构.md + API文档.md
🎨 前端问题 → 前端架构.md + 业务流程.md  
🗄️ 数据库问题 → 数据库设计.md
⚙️ 环境问题 → 开发指南.md + 部署运维.md
❓ 常见问题 → 问题解决.md
🧪 测试问题 → 测试文档.md
```

## 📁 核心代码定位

### 后端 (Flask + Flask-RESTX)
```
API路由:    backend/app/api/v1/
数据模型:    backend/app/models/
数据验证:    backend/app/schemas/
工具函数:    backend/app/utils/
配置文件:    backend/config.py
启动文件:    backend/run.py
```

### 前端 (Vue 3 + TypeScript)
```
页面组件:    frontend/src/views/
API调用:    frontend/src/api/
状态管理:    frontend/src/stores/
路由配置:    frontend/src/router/
公共组件:    frontend/src/components/
类型定义:    frontend/src/types/
```

## 🔗 重要端点
```
前端应用:    http://localhost:3001
后端API:    http://localhost:5001  
API文档:    http://localhost:5001/docs/
健康检查:    http://localhost:5001/test/health
```

## 📋 Bug修复流程

### 1. 分析问题 (5分钟)
- [ ] 读概况.md了解项目结构
- [ ] 查看问题解决.md寻找已知方案
- [ ] 选择对应的技术文档

### 2. 定位代码 (10分钟)  
- [ ] 使用代码定位指南找到相关文件
- [ ] 查看API文档.md了解接口规范
- [ ] 检查数据库设计.md了解数据结构

### 3. 修复实施 (30分钟)
- [ ] 遵循开发指南.md的代码规范
- [ ] 参考架构文档的设计模式
- [ ] 编写或更新测试用例

### 4. 更新文档 (10分钟) ⭐ 重要
- [ ] 更新相关技术文档
- [ ] 记录新的解决方案
- [ ] 验证修复效果

## 📚 文档更新规则

### 必须更新的情况
```
✅ API变更 → 更新 API文档.md
✅ 数据库变更 → 更新 数据库设计.md  
✅ 业务流程变更 → 更新 业务流程.md
✅ 配置变更 → 更新 开发指南.md
✅ 架构调整 → 更新对应架构文档
✅ 新问题解决 → 更新 问题解决.md
```

## 🎯 常见场景速查

### API错误
```
1. API文档.md → 确认接口规范
2. 后端架构.md → 了解设计模式  
3. backend/app/api/v1/ → 定位具体文件
4. 测试文档.md → 编写测试用例
```

### 前端界面问题
```
1. 前端架构.md → 了解组件结构
2. 业务流程.md → 了解业务逻辑
3. frontend/src/views/ → 定位页面组件
4. frontend/src/api/ → 检查API调用
```

### 数据库问题
```
1. 数据库设计.md → 了解表结构
2. 后端架构.md → 了解ORM使用
3. backend/app/models/ → 定位数据模型
```

### 环境配置问题
```
1. 开发指南.md → 环境配置章节
2. 部署运维.md → 部署说明
3. 问题解决.md → 常见环境问题
```

## 🔧 技术栈速查

### 后端技术栈
```
框架: Flask 2.3.3 + Flask-RESTX
数据库: SQLAlchemy + SQLite/PostgreSQL
验证: Marshmallow
文档: Swagger/OpenAPI 3.0
测试: pytest
```

### 前端技术栈  
```
框架: Vue 3 + Composition API
语言: TypeScript
UI库: Element Plus
状态: Pinia + 持久化
路由: Vue Router 4
构建: Vite
图表: ECharts
```

## 📊 项目状态
```
✅ 项目状态: 100%完成，生产就绪
✅ 核心模块: 8个主要业务模块全部完成
✅ 技术架构: 现代化前后端分离架构
✅ 文档完整: 12个专门技术文档
✅ 测试覆盖: 功能测试和集成测试完成
```

---

**💡 重要提醒**: 
1. 修复bug后务必更新相关文档
2. 遵循现有的代码规范和架构模式  
3. 使用概况.md的代码定位指南快速找到文件
4. 参考问题解决.md避免重复已知问题
