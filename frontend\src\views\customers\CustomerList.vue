<template>
  <div class="customer-list">
    <!-- 搜索栏 -->
    <el-card class="search-card mb-20">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="客户名称">
          <el-input v-model="searchForm.name" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item label="联系人">
          <el-input v-model="searchForm.contact" placeholder="请输入联系人" clearable />
        </el-form-item>
        <el-form-item label="客户等级">
          <el-select
            v-model="searchForm.level"
            placeholder="请选择客户等级"
            clearable
            style="width: 150px"
            @change="handleLevelChange"
          >
            <el-option label="普通客户" value="normal" />
            <el-option label="VIP客户" value="vip" />
            <el-option label="重要客户" value="important" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择客户状态"
            clearable
            style="width: 150px"
          >
            <el-option label="正常" value="正常" />
            <el-option label="禁用" value="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="mb-20">
      <div class="flex-between">
        <div>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增客户
          </el-button>
          <el-button type="success" @click="handleImport">
            <el-icon><Download /></el-icon>
            导入客户
          </el-button>
          <el-button type="warning" @click="handleExport">
            <el-icon><Upload /></el-icon>
            导出客户
          </el-button>
          <el-button
            type="info"
            @click="handleDownloadTemplate"
            :loading="templateLoading"
          >
            <el-icon><Document /></el-icon>
            下载模板
          </el-button>
        </div>
        <div v-if="selectedRows.length > 0" class="batch-actions">
          <el-button type="danger" @click="handleBatchDelete">
            <el-icon><Delete /></el-icon>
            批量删除 ({{ selectedRows.length }})
          </el-button>
          <el-button type="warning" @click="handleBatchExport">
            <el-icon><Upload /></el-icon>
            批量导出 ({{ selectedRows.length }})
          </el-button>
        </div>
        <div>
          <el-button-group>
            <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
              <el-icon><Menu /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 表格视图 -->
    <el-card v-if="viewMode === 'table'">
      <div v-loading="loading">
        <el-table :data="customerList" border stripe @selection-change="handleSelectionChange" @row-click="handleRowClick">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="客户编号" width="80" />
          <el-table-column label="客户名称" min-width="150">
            <template #default="{ row }">
              <span class="name-text">{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="contact" label="联系人" width="100" />
          <el-table-column prop="phone" label="联系电话" width="130" />
          <el-table-column prop="level" label="客户等级" width="100">
            <template #default="{ row }">
              <el-tag :type="getLevelType(row.level)">{{ getLevelLabel(row.level) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === '正常' ? 'success' : 'danger'">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template #default="{ row }">
              <div class="customer-actions">
                <el-button type="primary" link size="small" @click.stop="handleEdit(row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" link size="small" @click.stop="handleDelete(row)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex-between mt-20">
          <el-button-group>
            <el-button :disabled="!selectedCustomers.length" @click="handleBatchDelete">
              批量删除
            </el-button>
            <el-button :disabled="!selectedCustomers.length" @click="handleBatchExport">
              批量导出
            </el-button>
          </el-button-group>
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.per_page"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 卡片视图 -->
    <template v-else>
      <el-row :gutter="20">
        <el-col v-for="customer in customerList" :key="customer.id" :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="customer-card mb-20">
            <template #header>
              <div class="flex-between">
                <span class="customer-name">{{ customer.name }}</span>
                <el-tag :type="getLevelType(customer.level)">{{ getLevelLabel(customer.level) }}</el-tag>
              </div>
            </template>
            <div class="customer-info">
              <p><el-icon><User /></el-icon> 联系人：{{ customer.contact }}</p>
              <p><el-icon><Phone /></el-icon> 电话：{{ customer.phone }}</p>
              <p><el-icon><Location /></el-icon> 地址：{{ customer.address || '暂无' }}</p>
              <p><el-icon><Timer /></el-icon> 创建时间：{{ formatDate(customer.created_at) }}</p>
            </div>
            <div class="customer-actions mt-10">
              <el-button-group>
                <el-button type="primary" link @click="handleEdit(customer)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="info" link @click="handleView(customer)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button type="danger" link @click="handleDelete(customer)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </el-button-group>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 分页 -->
      <div class="flex-center mt-20">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[12, 24, 48, 96]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </template>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入客户数据"
      width="600px"
      destroy-on-close
    >
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :auto-upload="false"
        :limit="1"
        :on-change="handleFileChange"
        :on-exceed="handleExceed"
        accept=".xlsx,.xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将Excel文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 .xlsx/.xls 文件，且不超过 10MB
          </div>
        </template>
      </el-upload>

      <!-- 导入结果展示 -->
      <div v-if="importResult" class="import-result">
        <el-alert
          :title="importResult.message"
          :type="importResult.type"
          :closable="false"
          show-icon
        />

        <!-- 错误详情 -->
        <div v-if="importResult.errors && importResult.errors.length > 0" class="error-details">
          <el-divider content-position="left">
            <span class="error-title">错误详情 ({{ importResult.errors.length }}条)</span>
          </el-divider>
          <div class="error-list">
            <div
              v-for="(error, index) in importResult.errors"
              :key="index"
              class="error-item"
            >
              <el-icon class="error-icon"><Warning /></el-icon>
              <span>{{ error }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="handleCloseImportDialog">
          {{ importResult ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="!importResult"
          type="primary"
          @click="handleConfirmImport"
          :loading="importLoading"
          :disabled="!selectedFile"
        >
          确认导入
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Upload,
  Download,
  Document,
  Grid,
  Menu,
  Edit,
  View,
  Delete,
  User,
  Phone,
  Location,
  Timer,
  UploadFilled,
  Warning
} from '@element-plus/icons-vue'
import { customerApi } from '@/api/customer'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  name: '',
  contact: '',
  level: '',
  status: ''
})

// 视图模式
const viewMode = ref('table')

// 分页信息
const pagination = reactive({
  page: 1,
  per_page: 10,
  total: 0
})

// 客户列表数据
const customerList = ref([])
const selectedCustomers = ref([])
const selectedRows = ref([])
const loading = ref(false)

// 导入导出相关状态
const importDialogVisible = ref(false)
const importLoading = ref(false)
const templateLoading = ref(false)
const selectedFile = ref<File | null>(null)
const uploadRef = ref()
const importResult = ref<{
  message: string
  type: 'success' | 'warning' | 'error'
  errors?: string[]
} | null>(null)

// 获取客户列表
const getCustomerList = async () => {
  try {
    loading.value = true

    // 构建查询参数，适配后端API
    const params: any = {
      page: pagination.page,
      per_page: pagination.per_page,
      status: searchForm.status,
      level: searchForm.level
    }

    // 处理搜索逻辑：后端的name参数会同时搜索客户名称和联系人
    if (searchForm.name || searchForm.contact) {
      // 如果有客户名称搜索，优先使用客户名称
      if (searchForm.name) {
        params.name = searchForm.name
      } else if (searchForm.contact) {
        // 如果只有联系人搜索，使用联系人作为name参数
        params.name = searchForm.contact
      }
    }

    const response = await customerApi.getList(params) as any

    // 检查响应格式并处理数据
    if (Array.isArray(response)) {
      // 如果响应直接是数组（被request拦截器处理过）
      customerList.value = response
      pagination.total = response.length
    } else if (response && typeof response === 'object') {
      // 如果响应是对象格式
      customerList.value = response.data || response.items || []
      pagination.total = response.pagination?.total || response.total || 0
    } else {
      customerList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取客户列表失败:', error)
    ElMessage.error('获取客户列表失败')
    customerList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 客户等级选择变化
const handleLevelChange = (value: string) => {
  console.log('客户等级选择变化:', value)
  console.log('当前searchForm.level:', searchForm.level)
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getCustomerList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    contact: '',
    level: '',
    status: ''
  })
  pagination.page = 1
  getCustomerList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.per_page = size
  pagination.page = 1
  getCustomerList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  getCustomerList()
}

// 选择处理
const handleSelectionChange = (selection: any[]) => {
  selectedCustomers.value = selection
  selectedRows.value = selection
}

// 操作处理
const handleAdd = () => {
  router.push('/customers/new')
}

const handleEdit = (row: any) => {
  router.push(`/customers/edit/${row.id}`)
}

const handleView = (row: any) => {
  router.push(`/customers/view/${row.id}`)
}

// 处理表格行点击事件
const handleRowClick = (row: any, column: any, event: Event) => {
  // 检查是否点击了操作按钮或复选框
  const target = event.target as HTMLElement
  if (target.closest('.el-button') || target.closest('.el-checkbox')) {
    return
  }

  // 检查是否有文本选择（避免误触发）
  const selection = window.getSelection()
  if (selection && selection.toString().length > 0) {
    return
  }

  // 跳转到详情页
  handleView(row)
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除客户"${row.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await customerApi.delete(row.id)
    ElMessage.success('删除成功')
    getCustomerList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除客户失败:', error)
      ElMessage.error('删除客户失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedCustomers.value.length} 个客户吗？`, '确认批量删除', {
      type: 'warning'
    })

    // 使用批量删除API
    const ids = selectedCustomers.value.map((customer: any) => customer.id)
    await customerApi.batchDelete(ids)

    ElMessage.success('批量删除成功')
    getCustomerList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 导入功能
const handleImport = () => {
  importDialogVisible.value = true
  selectedFile.value = null
  importResult.value = null
}

const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
}

const handleExceed = () => {
  ElMessage.warning('只能选择一个文件')
}

const handleConfirmImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    importLoading.value = true
    const response = await customerApi.import(selectedFile.value)

    // 处理导入结果
    if (response.errors && response.errors.length > 0) {
      // 部分成功或完全失败
      const hasSuccess = response.message.includes('成功导入') && !response.message.includes('成功导入 0 条')
      importResult.value = {
        message: response.message,
        type: hasSuccess ? 'warning' : 'error',
        errors: response.errors
      }
    } else {
      // 完全成功
      importResult.value = {
        message: response.message || '导入成功',
        type: 'success'
      }
    }

    getCustomerList() // 刷新列表
  } catch (error: any) {
    console.error('导入失败:', error)
    const errorData = error.response?.data

    importResult.value = {
      message: errorData?.message || '导入失败',
      type: 'error',
      errors: errorData?.errors || []
    }
  } finally {
    importLoading.value = false
  }
}

// 关闭导入对话框
const handleCloseImportDialog = () => {
  importDialogVisible.value = false
  importResult.value = null
  selectedFile.value = null
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 导出功能
const handleExport = async () => {
  try {
    const params = {
      name: searchForm.name || searchForm.contact,
      status: searchForm.status,
      level: searchForm.level
    }

    const blob = await customerApi.export(params)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `客户列表_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 批量导出功能
const handleBatchExport = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要导出的客户')
    return
  }

  try {
    const ids = selectedRows.value.map((row: any) => row.id)
    const blob = await customerApi.batchExport(ids)

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `客户批量导出_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('批量导出成功')
  } catch (error) {
    console.error('批量导出失败:', error)
    ElMessage.error('批量导出失败')
  }
}

// 下载模板功能
const handleDownloadTemplate = async () => {
  try {
    templateLoading.value = true
    const blob = await customerApi.downloadTemplate()

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `客户导入模板_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  } finally {
    templateLoading.value = false
  }
}

// 工具函数
const getLevelType = (level: string) => {
  const typeMap: Record<string, string> = {
    'vip': 'danger',
    'important': 'warning',
    'normal': 'info'
  }
  return typeMap[level] || 'info'
}

const getLevelLabel = (level: string) => {
  const labelMap: Record<string, string> = {
    'vip': 'VIP客户',
    'important': '重要客户',
    'normal': '普通客户'
  }
  return labelMap[level] || level
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 初始化
onMounted(() => {
  getCustomerList()
})
</script>

<style lang="scss" scoped>
.customer-list {
  .search-card {
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .mt-20 {
    margin-top: 20px;
  }
  
  .mt-10 {
    margin-top: 10px;
  }
  
  .customer-card {
    height: 100%;

    .customer-name {
      font-weight: bold;
      font-size: 16px;
    }

    .customer-info {
      p {
        margin: 8px 0;
        display: flex;
        align-items: center;

        .el-icon {
          margin-right: 8px;
          color: #909399;
        }
      }
    }

    .customer-actions {
      text-align: center;
    }
  }

  // 客户名称文本样式
  .name-text {
    font-weight: 500;
    cursor: pointer;
  }

  // 表格行悬停效果
  :deep(.el-table__row) {
    cursor: pointer;

    &:hover {
      background-color: var(--el-table-row-hover-bg-color);
    }
  }

  // 操作按钮样式
  .customer-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;

    .el-button {
      margin: 0;
      padding: 4px 8px;
      font-size: 12px;

      .el-icon {
        font-size: 12px;
        margin-right: 2px;
      }
    }
  }

  .batch-actions {
    margin-left: 20px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }

  .upload-demo {
    .el-upload {
      width: 100%;
    }
  }

  .import-result {
    margin-top: 20px;

    .error-details {
      margin-top: 15px;

      .error-title {
        font-weight: bold;
        color: #e6a23c;
      }

      .error-list {
        max-height: 200px;
        overflow-y: auto;
        background-color: #fdf6ec;
        border: 1px solid #f5dab1;
        border-radius: 4px;
        padding: 10px;

        .error-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 1.4;

          &:last-child {
            margin-bottom: 0;
          }

          .error-icon {
            color: #e6a23c;
            margin-right: 8px;
            margin-top: 2px;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}
</style>
