<template>
  <div class="payment-record-list">
    <el-card class="header-card mb-20">
      <div class="card-header">
        <div class="left">
          <h2 class="page-title">收款记录</h2>
          <el-tag type="info">查看所有对账单的收款记录</el-tag>
        </div>
      </div>
    </el-card>

    <!-- 搜索过滤区域 -->
    <el-card class="mb-20">
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="对账单号">
            <el-input v-model="searchForm.statementNumber" placeholder="输入对账单号" clearable @keyup.enter="fetchData" />
          </el-form-item>
          <el-form-item label="客户名称">
            <el-select
              v-model="searchForm.customerId"
              placeholder="选择客户"
              clearable
              filterable
              remote
              :remote-method="searchCustomers"
              :loading="customerLoading"
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="支付方式">
            <el-select v-model="searchForm.paymentMethod" placeholder="选择支付方式" clearable>
              <el-option label="银行转账" value="bank_transfer" />
              <el-option label="现金支付" value="cash" />
              <el-option label="微信" value="wechat" />
              <el-option label="支付宝" value="alipay" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="收款日期">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :shortcuts="dateShortcuts"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchData">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!-- 收款记录表格 -->
    <el-card>
      <div class="table-container" v-loading="loading">
        <el-table
          :data="paymentList"
          border
          stripe
          style="width: 100%"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="id" label="收款ID" width="80" />
          <el-table-column prop="statement_number" label="对账单号" min-width="150" fixed>
            <template #default="scope">
              <el-button type="primary" link @click="viewStatement(scope.row.statement_id)">
                {{ scope.row.statement_number }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="customer_name" label="客户名称" min-width="200">
            <template #default="scope">
              <el-button type="info" link @click="viewCustomer(scope.row.customer_id)">
                {{ scope.row.customer_name }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="收款金额" min-width="120" sortable="custom">
            <template #default="scope">
              <span class="amount-text">{{ formatCurrency(scope.row.amount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="payment_method" label="支付方式" width="120">
            <template #default="scope">
              <el-tag>{{ formatPaymentMethod(scope.row.payment_method) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="payment_date" label="收款日期" width="120" sortable="custom">
            <template #default="scope">
              {{ formatDate(scope.row.payment_date) }}
            </template>
          </el-table-column>
          <el-table-column prop="bank_account" label="收款账户" min-width="200">
            <template #default="scope">
              {{ scope.row.bank_account || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="transaction_number" label="交易流水号" min-width="180">
            <template #default="scope">
              {{ scope.row.transaction_number || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" min-width="150">
            <template #default="scope">
              {{ scope.row.notes || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="收款时间" width="180" sortable="custom">
            <template #default="scope">
              {{ formatDateTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="120">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="viewStatement(scope.row.statement_id)">
                查看对账单
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { statementApi } from '@/api/statement'
import { customerApi } from '@/api/customer'

const router = useRouter()
const loading = ref(false)
const customerLoading = ref(false)
const customerOptions = ref([])

// 搜索表单
const searchForm = reactive({
  statementNumber: '',
  customerId: null,
  paymentMethod: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 收款记录列表
const paymentList = ref([])

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]

// 获取收款记录
const fetchData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params: any = {
      page: pagination.page,
      per_page: pagination.pageSize
    }

    if (searchForm.statementNumber) {
      params.statement_number = searchForm.statementNumber
    }
    if (searchForm.customerId) {
      params.customer_id = searchForm.customerId
    }
    if (searchForm.paymentMethod) {
      params.payment_method = searchForm.paymentMethod
    }

    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0]
      params.end_date = searchForm.dateRange[1]
    }

    const response = await statementApi.getPaymentRecords(params)

    if (response.data) {
      // 后端返回的数据格式：{ data: [...], pagination: {...} }
      if (Array.isArray(response.data)) {
        paymentList.value = response.data
        pagination.total = response.pagination?.total || 0
      } else {
        paymentList.value = response.data.items || []
        pagination.total = response.data.total || 0
      }
    } else {
      paymentList.value = []
      pagination.total = 0
    }

  } catch (error) {
    console.error('获取收款记录失败:', error)
    ElMessage.error('获取收款记录失败')
    paymentList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.statementNumber = ''
  searchForm.customerId = null
  searchForm.paymentMethod = ''
  searchForm.dateRange = []
  pagination.page = 1
  fetchData()
}

// 处理排序变化
const handleSortChange = (column: any) => {
  // 实现排序逻辑
  fetchData()
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchData()
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 搜索客户
const searchCustomers = async (query: string) => {
  if (query && query.length >= 2) {
    customerLoading.value = true
    try {
      const response = await customerApi.getList({ name: query }) as any
      customerOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('搜索客户失败:', error)
    } finally {
      customerLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

// 查看对账单
const viewStatement = (statementId: number) => {
  router.push(`/statements/${statementId}`)
}

// 查看客户
const viewCustomer = (customerId: number) => {
  router.push(`/customers/${customerId}`)
}
// 格式化支付方式
const formatPaymentMethod = (method: string) => {
  const methodMap: Record<string, string> = {
    'bank_transfer': '银行转账',
    'cash': '现金支付',
    'wechat': '微信',
    'alipay': '支付宝',
    'other': '其他'
  }
  return methodMap[method] || method
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

// 格式化货币
const formatCurrency = (value: number) => {
  return `¥${Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.payment-record-list {
  .header-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .search-bar {
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-container {
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }

    .amount-text {
      font-weight: bold;
      color: #67c23a;
    }
  }
}
</style>
