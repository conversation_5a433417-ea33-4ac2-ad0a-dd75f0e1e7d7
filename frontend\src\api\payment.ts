import request from './request'
import type {
  AddBalanceRequest,
  DirectPaymentRequest,
  BalancePaymentRequest
} from '@/types/payment'
import type { PaginationParams } from '@/types/api'

// 收款管理API
export const paymentApi = {
  // 获取客户余额列表
  getCustomerBalances: (params?: PaginationParams & {
    customer_id?: number
    search?: string
  }) => {
    return request.get('/payments/customer-balances', { params })
  },

  // 获取指定客户余额详情
  getCustomerBalance: (customerId: number) => {
    return request.get(`/payments/customer-balances/${customerId}`)
  },

  // 客户余额充值
  addBalance: (data: AddBalanceRequest) => {
    return request.post('/payments/add-balance', data)
  },

  // 获取余额交易记录列表
  getBalanceTransactions: (params?: PaginationParams & {
    customer_id?: number
    transaction_type?: string
    start_date?: string
    end_date?: string
  }) => {
    return request.get('/payments/balance-transactions', { params })
  },

  // 获取对账单收款记录列表
  getStatementPayments: (params?: PaginationParams & {
    statement_id?: number
    payment_source?: string
    start_date?: string
    end_date?: string
  }) => {
    return request.get('/payments/statement-payments', { params })
  },

  // 创建直接收款记录
  createDirectPayment: (data: DirectPaymentRequest) => {
    return request.post('/payments/direct-payment', data)
  },

  // 创建余额支付记录
  createBalancePayment: (data: BalancePaymentRequest) => {
    return request.post('/payments/balance-payment', data)
  },

  // 结清对账单
  settleStatement: (statementId: number) => {
    return request.post(`/payments/settle-statement/${statementId}`)
  },

  // 获取收款统计数据
  getPaymentStats: (params?: {
    start_date?: string
    end_date?: string
    customer_id?: number
  }) => {
    return request.get('/payments/payment-stats', { params })
  }
}
