import{b as e,r as a,e as l,f as t,M as r,o as n,c as u,i as o,h as d,U as i,a as s,l as c,k as p,g as m,j as v,a8 as _,a9 as f,F as b,m as y,N as g,t as h,W as w,Z as k,$ as C,ad as N,X as R,Y as U,P as S}from"./index-3d4c440c.js";import{g as V,p as x,e as $,a as z}from"./delivery-8900e898.js";import{_ as j}from"./_plugin-vue_export-helper-1b428a4d.js";const P={class:"delivery-note-list"},L={class:"flex-between"},D={class:"search-form"},B={class:"pagination-container"},Y=j({__name:"DeliveryNoteList",setup(j){const Y=e(),O=a(!1),T=a(null),A=a([]),M=a([]),I=[{value:"待发出",label:"待发出"},{value:"已发出",label:"已发出"},{value:"已完成",label:"已完成"},{value:"已作废",label:"已作废"}],K=a({currentPage:1,pageSize:10,total:0}),E=a({deliveryNumber:"",orderNumber:"",customerName:"",status:"",dateRange:[]}),F=async()=>{O.value=!0;try{const e={page:K.value.currentPage,per_page:K.value.pageSize,...W()},{data:a}=await V(e);console.log("获取到的送货单数据:",a),A.value=Array.isArray(null==a?void 0:a.list)?a.list:[],A.value=A.value.map((e=>{var a,l,t;return{...e,customer_name:e.customer_name||(null==(l=null==(a=e.order)?void 0:a.customer)?void 0:l.name)||"",order_number:e.order_number||(null==(t=e.order)?void 0:t.order_number)||""}}));const l="object"==typeof(null==a?void 0:a.pagination)&&null!==a.pagination?a.pagination:{};K.value.total=l.total_items||l.total||A.value.length}catch(e){console.error("获取送货单列表失败",e),i.error("获取送货单列表失败"),A.value=[],K.value.total=0}finally{O.value=!1}},W=()=>{const e={};return E.value.deliveryNumber&&(e.deliveryNumber=E.value.deliveryNumber),E.value.orderNumber&&(e.orderNumber=E.value.orderNumber),E.value.customerName&&(e.customerName=E.value.customerName),E.value.status&&(e.status=E.value.status),E.value.dateRange&&2===E.value.dateRange.length&&(e.startDate=E.value.dateRange[0],e.endDate=E.value.dateRange[1]),e},X=e=>{M.value=e},Z=()=>{K.value.currentPage=1,F()},q=()=>{E.value={deliveryNumber:"",orderNumber:"",customerName:"",status:"",dateRange:[]},K.value.currentPage=1,F()},G=e=>{K.value.currentPage=e,F()},H=e=>{K.value.pageSize=e,K.value.currentPage=1,F()},J=()=>{Y.push("/delivery/create")},Q=async e=>{try{const a=await x(e.id),l=window.URL.createObjectURL(new Blob([a.data])),t=document.createElement("a");t.href=l,t.setAttribute("download",`送货单_${e.delivery_number}.pdf`),document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(l),i.success("打印文件已生成")}catch(a){console.error("打印送货单失败",a),i.error("打印送货单失败")}},ee=async()=>{if(0!==M.value.length)try{for(const e of M.value)await Q(e)}catch(e){console.error("批量打印送货单失败",e),i.error("批量打印送货单失败")}else i.warning("请选择要打印的送货单")},ae=async()=>{if(0!==M.value.length)try{const e=M.value.map((e=>e.id)),a=await $(e),l=window.URL.createObjectURL(new Blob([a.data])),t=document.createElement("a");t.href=l,t.setAttribute("download",`送货单列表_${(new Date).toISOString().split("T")[0]}.xlsx`),document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(l),i.success("导出成功")}catch(e){console.error("导出送货单失败",e),i.error("导出送货单失败")}else i.warning("请选择要导出的送货单")},le=async(e,a)=>{const l={"已发出":"确认发货","已完成":"确认完成","已作废":"作废送货单"};try{"已发出"===a?await w.confirm(`确认发货后，如果在5天内未进行"确认完成"操作，系统将自动将状态更新为"已完成"。\n确定要将送货单 ${e.delivery_number}（关联订单：${e.order_number}）${l[a]}吗？`,"操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}):await w.confirm(`确定要将送货单 ${e.delivery_number}（关联订单：${e.order_number}）${l[a]}吗？`,"操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await z(e.id,a);if(200===t.code&&t.data){const l=A.value.findIndex((a=>a.id===e.id));-1!==l&&(A.value[l].status=a),i.success(`送货单状态已更新为：${te(a)}`)}else i.error("更新送货单状态失败")}catch(t){"cancel"!==t&&(console.error("更新送货单状态失败",t),i.error("更新送货单状态失败"))}},te=e=>({pending:"待发出",delivered:"已发出",completed:"已完成",cancelled:"已作废","待发出":"待发出","已发出":"已发出","已完成":"已完成","已作废":"已作废","":"未知状态"}[e]||"未知状态"),re=e=>{if(!e)return"未设置";try{const a=new Date(e);return a.getFullYear()+"/"+String(a.getMonth()+1).padStart(2,"0")+"/"+String(a.getDate()).padStart(2,"0")}catch(a){return console.error("日期格式化错误:",a),e}};return l((()=>{F()})),(e,a)=>{const l=t("el-icon"),i=t("el-button"),w=t("el-card"),V=t("el-input"),x=t("el-form-item"),$=t("el-option"),z=t("el-select"),j=t("el-date-picker"),F=t("el-form"),W=t("el-table-column"),ne=t("el-link"),ue=t("el-tag"),oe=t("el-dropdown-item"),de=t("el-dropdown-menu"),ie=t("el-dropdown"),se=t("el-table"),ce=t("el-pagination"),pe=r("loading");return n(),u("div",P,[o(w,{class:"header-card mb-20"},{default:d((()=>[s("div",L,[a[10]||(a[10]=s("h2",{class:"page-title"},"送货单管理",-1)),s("div",null,[o(i,{type:"primary",onClick:J},{default:d((()=>[o(l,null,{default:d((()=>[o(c(k))])),_:1}),a[7]||(a[7]=p(" 新建送货单 "))])),_:1}),M.value.length>0?(n(),m(i,{key:0,onClick:ae},{default:d((()=>[o(l,null,{default:d((()=>[o(c(C))])),_:1}),a[8]||(a[8]=p(" 导出送货单 "))])),_:1})):v("",!0),M.value.length>0?(n(),m(i,{key:1,onClick:ee},{default:d((()=>[o(l,null,{default:d((()=>[o(c(N))])),_:1}),a[9]||(a[9]=p(" 批量打印 "))])),_:1})):v("",!0)])])])),_:1}),o(w,{class:"mb-20"},{default:d((()=>[s("div",D,[o(F,{inline:!0,model:E.value,onSubmit:_(Z,["prevent"])},{default:d((()=>[o(x,{label:"送货单号"},{default:d((()=>[o(V,{modelValue:E.value.deliveryNumber,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value.deliveryNumber=e),placeholder:"请输入送货单号",clearable:"",onKeyup:f(Z,["enter"])},null,8,["modelValue"])])),_:1}),o(x,{label:"关联订单号"},{default:d((()=>[o(V,{modelValue:E.value.orderNumber,"onUpdate:modelValue":a[1]||(a[1]=e=>E.value.orderNumber=e),placeholder:"请输入订单号",clearable:"",onKeyup:f(Z,["enter"])},null,8,["modelValue"])])),_:1}),o(x,{label:"客户名称"},{default:d((()=>[o(V,{modelValue:E.value.customerName,"onUpdate:modelValue":a[2]||(a[2]=e=>E.value.customerName=e),placeholder:"请输入客户名称",clearable:"",onKeyup:f(Z,["enter"])},null,8,["modelValue"])])),_:1}),o(x,{label:"送货状态"},{default:d((()=>[o(z,{modelValue:E.value.status,"onUpdate:modelValue":a[3]||(a[3]=e=>E.value.status=e),placeholder:"请选择状态",clearable:"",style:{width:"130px"}},{default:d((()=>[(n(),u(b,null,y(I,(e=>o($,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),o(x,{label:"送货日期"},{default:d((()=>[o(j,{modelValue:E.value.dateRange,"onUpdate:modelValue":a[4]||(a[4]=e=>E.value.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),o(x,null,{default:d((()=>[o(i,{type:"primary",onClick:Z},{default:d((()=>[o(l,null,{default:d((()=>[o(c(R))])),_:1}),a[11]||(a[11]=p(" 搜索 "))])),_:1}),o(i,{onClick:q},{default:d((()=>[o(l,null,{default:d((()=>[o(c(U))])),_:1}),a[12]||(a[12]=p(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])])),_:1}),o(w,null,{default:d((()=>[g((n(),m(se,{ref_key:"tableRef",ref:T,data:A.value,border:"",stripe:"",style:{width:"100%"},onSelectionChange:X},{default:d((()=>[o(W,{type:"selection",width:"55"}),o(W,{prop:"delivery_number",label:"送货单号",width:"100"},{default:d((({row:e})=>[o(ne,{type:"primary",onClick:a=>(e=>{Y.push(`/delivery/${e.id}`)})(e)},{default:d((()=>[p(h(e.delivery_number),1)])),_:2},1032,["onClick"])])),_:1}),o(W,{prop:"order_number",label:"关联订单号",width:"100"},{default:d((({row:e})=>[o(ne,{type:"primary",onClick:a=>{return l=e.order_id,void Y.push(`/orders/${l}`);var l}},{default:d((()=>[p(h(e.order_number),1)])),_:2},1032,["onClick"])])),_:1}),o(W,{prop:"customer_name",label:"客户名称","min-width":"100","show-overflow-tooltip":""}),o(W,{prop:"delivery_date",label:"送货日期",width:"100"},{default:d((({row:e})=>[p(h(re(e.delivery_date)),1)])),_:1}),o(W,{prop:"recipient_name",label:"收货人",width:"80"},{default:d((({row:e})=>{var a;return[p(h(e.recipient_name||(null==(a=e.order)?void 0:a.contact_person)||"未设置"),1)]})),_:1}),o(W,{prop:"recipient_phone",label:"联系电话",width:"120"},{default:d((({row:e})=>{var a;return[p(h(e.recipient_phone||(null==(a=e.order)?void 0:a.contact_phone)||"未设置"),1)]})),_:1}),o(W,{prop:"status",label:"送货状态",width:"80"},{default:d((({row:e})=>{return[o(ue,{type:(a=e.status,{pending:"info",delivered:"warning",completed:"success",cancelled:"danger","待发出":"info","已发出":"warning","已完成":"success","已作废":"danger","":"info"}[a]||"info")},{default:d((()=>[p(h(te(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),o(W,{prop:"created_at",label:"创建时间",width:"100"},{default:d((({row:e})=>[p(h(re(e.created_at)),1)])),_:1}),o(W,{fixed:"right",label:"操作",width:"180"},{default:d((({row:e})=>["待发出"===e.status?(n(),m(i,{key:0,link:"",type:"primary",onClick:a=>(e=>{Y.push(`/delivery/${e.id}/edit`)})(e)},{default:d((()=>a[13]||(a[13]=[p("编辑")]))),_:2},1032,["onClick"])):v("",!0),o(i,{link:"",type:"primary",onClick:a=>Q(e)},{default:d((()=>a[14]||(a[14]=[p("打印")]))),_:2},1032,["onClick"]),"已作废"!==e.status?(n(),m(ie,{key:1},{dropdown:d((()=>[o(de,null,{default:d((()=>["待发出"===e.status?(n(),m(oe,{key:0,onClick:a=>le(e,"已发出")},{default:d((()=>a[16]||(a[16]=[p("发货")]))),_:2},1032,["onClick"])):v("",!0),"已发出"===e.status?(n(),m(oe,{key:1,onClick:a=>le(e,"已完成")},{default:d((()=>a[17]||(a[17]=[p("确认完成")]))),_:2},1032,["onClick"])):v("",!0),"待发出"===e.status?(n(),m(oe,{key:2,onClick:a=>le(e,"已作废")},{default:d((()=>a[18]||(a[18]=[p("作废")]))),_:2},1032,["onClick"])):v("",!0)])),_:2},1024)])),default:d((()=>[o(i,{link:"",type:"primary"},{default:d((()=>[a[15]||(a[15]=p(" 更多")),o(l,{class:"el-icon--right"},{default:d((()=>[o(c(S))])),_:1})])),_:1})])),_:2},1024)):v("",!0)])),_:1})])),_:1},8,["data"])),[[pe,O.value]]),s("div",B,[o(ce,{"current-page":K.value.currentPage,"onUpdate:currentPage":a[5]||(a[5]=e=>K.value.currentPage=e),"page-size":K.value.pageSize,"onUpdate:pageSize":a[6]||(a[6]=e=>K.value.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:K.value.total,onSizeChange:H,onCurrentChange:G},null,8,["current-page","page-size","total"])])])),_:1})])}}},[["__scopeId","data-v-a1a8e2c0"]]);export{Y as default};
