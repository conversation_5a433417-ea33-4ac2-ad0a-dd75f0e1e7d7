<template>
  <div class="payment-management">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <h2>收款管理</h2>
          <div class="header-actions">
            <el-button type="primary" @click="showAddBalanceDialog">
              <el-icon><Plus /></el-icon>
              余额充值
            </el-button>
            <el-button type="success" @click="showDirectPaymentDialog">
              <el-icon><Money /></el-icon>
              直接收款
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计卡片 -->
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value">{{ formatCurrency(stats.payment_stats?.total_payment_amount || 0) }}</div>
                <div class="stat-label">总收款金额</div>
                <div class="stat-count">{{ stats.payment_stats?.total_payment_count || 0 }}笔</div>
              </div>
              <el-icon class="stat-icon" color="#409EFF"><Money /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value">{{ formatCurrency(stats.balance_stats?.total_recharge_amount || 0) }}</div>
                <div class="stat-label">总充值金额</div>
                <div class="stat-count">{{ stats.balance_stats?.recharge_count || 0 }}笔</div>
              </div>
              <el-icon class="stat-icon" color="#67C23A"><CreditCard /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value">{{ formatCurrency(stats.balance_stats?.total_customer_balance || 0) }}</div>
                <div class="stat-label">客户总余额</div>
                <div class="stat-count">可用余额</div>
              </div>
              <el-icon class="stat-icon" color="#E6A23C"><Wallet /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value">{{ formatCurrency(stats.payment_stats?.balance_payment_amount || 0) }}</div>
                <div class="stat-label">余额支付金额</div>
                <div class="stat-count">{{ stats.payment_stats?.balance_payment_count || 0 }}笔</div>
              </div>
              <el-icon class="stat-icon" color="#F56C6C"><Coin /></el-icon>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 标签页 -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 客户余额管理 -->
        <el-tab-pane label="客户余额" name="balances">
          <CustomerBalanceList 
            ref="balanceListRef"
            @recharge="handleRecharge"
          />
        </el-tab-pane>

        <!-- 余额交易记录 -->
        <el-tab-pane label="交易记录" name="transactions">
          <BalanceTransactionList ref="transactionListRef" />
        </el-tab-pane>

        <!-- 对账单收款记录 -->
        <el-tab-pane label="收款记录" name="payments">
          <StatementPaymentList 
            ref="paymentListRef"
            @direct-payment="handleDirectPayment"
            @balance-payment="handleBalancePayment"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 余额充值对话框 -->
    <AddBalanceDialog
      v-model="addBalanceDialogVisible"
      @success="handleAddBalanceSuccess"
    />

    <!-- 直接收款对话框 -->
    <DirectPaymentDialog
      v-model="directPaymentDialogVisible"
      @success="handleDirectPaymentSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Money, Refresh, CreditCard, Wallet, Coin } from '@element-plus/icons-vue'
import { paymentApi } from '@/api/payment'
import type { PaymentStats } from '@/types/payment'
import CustomerBalanceList from './components/CustomerBalanceList.vue'
import BalanceTransactionList from './components/BalanceTransactionList.vue'
import StatementPaymentList from './components/StatementPaymentList.vue'
import AddBalanceDialog from './components/AddBalanceDialog.vue'
import DirectPaymentDialog from './components/DirectPaymentDialog.vue'

// 响应式数据
const activeTab = ref('balances')
const addBalanceDialogVisible = ref(false)
const directPaymentDialogVisible = ref(false)

// 统计数据
const stats = reactive<PaymentStats>({
  payment_stats: {
    direct_payment_count: 0,
    direct_payment_amount: 0,
    balance_payment_count: 0,
    balance_payment_amount: 0,
    total_payment_count: 0,
    total_payment_amount: 0
  },
  balance_stats: {
    recharge_count: 0,
    total_recharge_amount: 0,
    total_customer_balance: 0,
    total_frozen_balance: 0
  },
  period: {}
})

// 组件引用
const balanceListRef = ref()
const transactionListRef = ref()
const paymentListRef = ref()

// 格式化货币
const formatCurrency = (amount: number): string => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await paymentApi.getPaymentStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  await loadStats()
  
  // 刷新当前标签页的数据
  switch (activeTab.value) {
    case 'balances':
      balanceListRef.value?.refresh()
      break
    case 'transactions':
      transactionListRef.value?.refresh()
      break
    case 'payments':
      paymentListRef.value?.refresh()
      break
  }
  
  ElMessage.success('数据刷新成功')
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  console.log('切换到标签页:', tabName)
}

// 显示余额充值对话框
const showAddBalanceDialog = () => {
  addBalanceDialogVisible.value = true
}

// 显示直接收款对话框
const showDirectPaymentDialog = () => {
  directPaymentDialogVisible.value = true
}

// 处理余额充值
const handleRecharge = (customerId: number) => {
  // 可以传递客户ID到充值对话框
  showAddBalanceDialog()
}

// 处理直接收款
const handleDirectPayment = (statementId: number) => {
  // 可以传递对账单ID到收款对话框
  showDirectPaymentDialog()
}

// 处理余额支付
const handleBalancePayment = (data: any) => {
  // 处理余额支付逻辑
  console.log('余额支付:', data)
}

// 余额充值成功回调
const handleAddBalanceSuccess = () => {
  refreshData()
  ElMessage.success('余额充值成功')
}

// 直接收款成功回调
const handleDirectPaymentSuccess = () => {
  refreshData()
  ElMessage.success('收款记录创建成功')
}

// 组件挂载时加载数据
onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.payment-management {
  padding: 20px;
}

.page-card {
  min-height: calc(100vh - 40px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card :deep(.el-card__body) {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 3px;
}

.stat-count {
  font-size: 12px;
  color: #C0C4CC;
}

.stat-icon {
  font-size: 40px;
  opacity: 0.8;
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}
</style>
