<template>
  <div class="product-form" v-loading="loading">
    <!-- 页面头部 -->
    <el-card class="mb-20">
      <div class="flex-between">
        <div>
          <el-button @click="$router.back()">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
        <div>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            <el-icon><Check /></el-icon>
            {{ isEdit ? '更新' : '保存' }}
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 产品表单 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="product-form-content"
    >
      <el-tabs v-model="activeTab">
        <!-- 基本信息标签页 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-card>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="产品名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入产品名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品型号" prop="model">
                  <el-input v-model="form.model" placeholder="请输入产品型号" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计量单位" prop="unit">
                  <el-input v-model="form.unit" placeholder="例如：个、台、米、公斤等" />
                </el-form-item>
              </el-col>
          <el-col :span="12">
            <el-form-item label="产品分类" prop="category_id">
              <el-select v-model="form.category_id" placeholder="请选择产品分类" style="width: 100%">
                <el-option
                  v-for="category in categoryOptions"
                  :key="category.id"
                  :label="category.full_category_path || category.name"
                  :value="category.id"
                >
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span :style="{
                      paddingLeft: `${(category.level - 1) * 16}px`,
                      color: category.level > 1 ? '#606266' : '#303133'
                    }">
                      {{ category.level > 1 ? '└ ' : '' }}{{ category.name }}
                    </span>
                    <span style="color: #8492a6; font-size: 12px;">
                      {{ category.level ? `L${category.level}` : '' }}
                    </span>
                  </div>
                  <div v-if="category.full_category_path && category.level > 1"
                       style="font-size: 12px; color: #909399; margin-top: 2px;"
                       :style="{ paddingLeft: `${(category.level - 1) * 16}px` }">
                    {{ category.full_category_path }}
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品品牌" prop="brand_id">
              <el-select v-model="form.brand_id" placeholder="请选择产品品牌" clearable style="width: 100%">
                <el-option
                  v-for="brand in brandOptions"
                  :key="brand.id"
                  :label="brand.name"
                  :value="brand.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio value="正常">正常</el-radio>
                <el-radio value="禁用">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="产品图片">
              <div class="image-upload-container">
                <el-upload
                  ref="uploadRef"
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :file-list="imageList"
                  :on-success="handleImageSuccess"
                  :on-error="handleImageError"
                  :on-remove="handleImageRemove"
                  :on-preview="handleImagePreview"
                  :before-upload="beforeImageUpload"
                  :limit="5"
                  :multiple="true"
                  list-type="picture-card"
                  accept="image/*"
                  name="image"
                >
                  <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持jpg、png、gif格式，单张图片不超过10MB，最多上传5张
                    </div>
                  </template>
                </el-upload>

                <!-- 图片预览对话框 -->
                <el-dialog v-model="previewDialogVisible" title="图片预览" width="800px">
                  <img :src="previewImageUrl" alt="预览图片" style="width: 100%; height: auto;" />
                </el-dialog>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="产品描述" prop="description">
              <el-input 
                v-model="form.description" 
                type="textarea" 
                :rows="4"
                placeholder="请输入产品描述" 
              />
            </el-form-item>
          </el-col>
        </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="备注" prop="notes">
                  <el-input
                    v-model="form.notes"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入备注信息"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </el-tab-pane>

        <!-- 规格价格标签页 -->
        <el-tab-pane label="规格价格" name="specs">
          <el-card>
            <div class="toolbar mb-20">
              <el-button type="primary" @click="addSpecification">
                <el-icon><Plus /></el-icon>
                添加规格
              </el-button>
              <el-button type="success" @click="handleBatchImport">
                <el-icon><Upload /></el-icon>
                批量导入
              </el-button>
            </div>

            <div v-if="specifications.length === 0" class="text-center text-gray-500 py-8">
              暂无规格信息，点击上方"添加规格"按钮添加
            </div>

            <el-table v-else :data="specifications" border stripe>
              <el-table-column label="规格名称" prop="specification" min-width="180">
                <template #default="{ row }">
                  <el-input
                    v-model="row.specification"
                    placeholder="请输入规格名称"
                  />
                </template>
              </el-table-column>
              <el-table-column label="成本价(元)" prop="cost_price" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.cost_price"
                    :min="0"
                    :precision="2"
                    :step="0.1"
                    controls-position="right"
                    placeholder="成本价"
                  />
                </template>
              </el-table-column>
              <el-table-column label="最低售价(元)" prop="min_price" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.min_price"
                    :min="0"
                    :precision="2"
                    :step="0.1"
                    controls-position="right"
                    placeholder="最低售价"
                  />
                </template>
              </el-table-column>
              <el-table-column label="最高售价(元)" prop="max_price" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.max_price"
                    :min="0"
                    :precision="2"
                    :step="0.1"
                    controls-position="right"
                    placeholder="最高售价"
                  />
                </template>
              </el-table-column>
              <el-table-column label="建议售价(元)" prop="suggested_price" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.suggested_price"
                    :min="0"
                    :precision="2"
                    :step="0.1"
                    controls-position="right"
                    placeholder="建议售价"
                  />
                </template>
              </el-table-column>
              <el-table-column label="税率(%)" prop="tax_rate" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.tax_rate"
                    :min="0"
                    :max="100"
                    :precision="2"
                    :step="1"
                    controls-position="right"
                    placeholder="税率"
                  />
                </template>
              </el-table-column>
              <el-table-column label="默认规格" prop="is_default" width="100">
                <template #default="{ row, $index }">
                  <el-switch
                    v-model="row.is_default"
                    @change="(val: boolean) => handleDefaultChange(val, $index)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    icon="Delete"
                    circle
                    @click="removeSpecification($index)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>

        <!-- 产品属性标签页 -->
        <el-tab-pane label="产品属性" name="attributes">
          <el-card>
            <div class="toolbar mb-20">
              <el-button type="primary" @click="addAttribute">
                <el-icon><Plus /></el-icon>
                添加属性
              </el-button>
            </div>

            <div v-if="attributes.length === 0" class="text-center text-gray-500 py-8">
              暂无属性信息，点击上方"添加属性"按钮添加
            </div>

            <el-table v-else :data="attributes" border stripe>
              <el-table-column label="属性名称" prop="attribute_name" min-width="180">
                <template #default="{ row }">
                  <el-input v-model="row.attribute_name" placeholder="如：材质、颜色等" />
                </template>
              </el-table-column>
              <el-table-column label="属性值" prop="attribute_value" min-width="180">
                <template #default="{ row }">
                  <el-input v-model="row.attribute_value" placeholder="如：不锈钢、红色等" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    icon="Delete"
                    circle
                    @click="removeAttribute($index)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>


      </el-tabs>
    </el-form>

    <!-- 批量导入规格对话框 -->
    <el-dialog
      v-model="batchImportDialogVisible"
      title="批量导入规格"
      width="600px"
      destroy-on-close
    >
      <div class="batch-import-content">
        <div class="example-tip mb-10">
          <p><strong>示例数据格式：</strong></p>
          <p class="import-tip">直接从Excel中选择并复制数据（包含表头行），然后粘贴到下方文本框。系统将自动识别Tab、逗号或空格分隔符。</p>
          <div class="example-code">
            规格名称&nbsp;&nbsp;&nbsp;&nbsp;成本价&nbsp;&nbsp;&nbsp;&nbsp;最低售价&nbsp;&nbsp;&nbsp;&nbsp;最高售价&nbsp;&nbsp;&nbsp;&nbsp;建议售价&nbsp;&nbsp;&nbsp;&nbsp;税率<br>
            规格1&nbsp;&nbsp;&nbsp;&nbsp;100&nbsp;&nbsp;&nbsp;&nbsp;120&nbsp;&nbsp;&nbsp;&nbsp;150&nbsp;&nbsp;&nbsp;&nbsp;130&nbsp;&nbsp;&nbsp;&nbsp;13<br>
            规格2&nbsp;&nbsp;&nbsp;&nbsp;200&nbsp;&nbsp;&nbsp;&nbsp;220&nbsp;&nbsp;&nbsp;&nbsp;280&nbsp;&nbsp;&nbsp;&nbsp;250&nbsp;&nbsp;&nbsp;&nbsp;13<br>
            16GB 黑色&nbsp;&nbsp;&nbsp;&nbsp;300&nbsp;&nbsp;&nbsp;&nbsp;350&nbsp;&nbsp;&nbsp;&nbsp;400&nbsp;&nbsp;&nbsp;&nbsp;380&nbsp;&nbsp;&nbsp;&nbsp;13
          </div>
          <p class="import-tip">注意：表头需包含与"规格"、"成本价"、"最低售价"、"最高售价"、"建议售价"、"税率"相关的关键词，系统会自动匹配。</p>
          <p class="import-tip" style="color: #E6A23C;"><strong>特别提示：</strong> "市场价"、"单价"、"售价"、"价格"等字段都会被识别为<strong>建议售价</strong>。</p>
        </div>

        <el-divider content-position="center">在此粘贴</el-divider>

        <el-input
          v-model="batchImportText"
          type="textarea"
          :rows="10"
          placeholder="在此粘贴从Excel复制的内容（包含表头行）"
          :spellcheck="false"
        />
      </div>

      <template #footer>
        <el-button @click="batchImportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmBatchImport">确定导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Check,
  Plus
} from '@element-plus/icons-vue'
import { productApi, categoryApi, brandApi } from '@/api/product'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref()

// 状态
const loading = ref(false)
const submitting = ref(false)

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)

// 当前激活的标签页
const activeTab = ref('basic')

// 选项数据
const categoryOptions = ref([])
const brandOptions = ref([])

// 规格管理
const specifications = ref([])

// 属性管理
const attributes = ref([])

// 图片管理
const imageList = ref([])
const previewDialogVisible = ref(false)
const previewImageUrl = ref('')
const uploadAction = ref('/api/v1/products/upload-image')
const uploadHeaders = ref({
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
})
// 记录需要删除的图片文件（用于清理临时上传的文件）
const filesToDelete = ref([])

// 批量导入规格
const batchImportDialogVisible = ref(false)
const batchImportText = ref('')

// 表单数据
const form = reactive({
  name: '',
  model: '',
  unit: '',
  category_id: '',
  brand_id: '',
  images: [],
  description: '',
  notes: '',
  status: '正常'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  model: [
    { required: true, message: '请输入产品型号', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入计量单位', trigger: 'blur' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择产品分类', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择产品状态', trigger: 'change' }
  ],
  image: [
    { max: 255, message: '图片URL长度不能超过 255 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '产品描述长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  notes: [
    { max: 1000, message: '备注长度不能超过 1000 个字符', trigger: 'blur' }
  ]
}

// 获取分类选项
const getCategoryOptions = async () => {
  try {
    const response = await categoryApi.getList({ per_page: 999 }) as any
    let categories = Array.isArray(response) ? response : (response.data || response.items || [])

    // 构建层级结构，让子分类紧跟在父分类后面
    const buildHierarchicalList = (categories) => {
      const categoryMap = new Map()
      const rootCategories = []

      // 创建分类映射
      categories.forEach(cat => {
        categoryMap.set(cat.id, { ...cat, children: [] })
      })

      // 构建父子关系
      categories.forEach(cat => {
        if (cat.parent_id && categoryMap.has(cat.parent_id)) {
          categoryMap.get(cat.parent_id).children.push(categoryMap.get(cat.id))
        } else {
          rootCategories.push(categoryMap.get(cat.id))
        }
      })

      // 递归展开为平铺列表
      const flattenCategories = (cats) => {
        const result = []
        cats.sort((a, b) => a.name.localeCompare(b.name)) // 同级按名称排序

        cats.forEach(cat => {
          result.push(cat)
          if (cat.children && cat.children.length > 0) {
            result.push(...flattenCategories(cat.children))
          }
        })
        return result
      }

      return flattenCategories(rootCategories)
    }

    categoryOptions.value = buildHierarchicalList(categories)
  } catch (error) {
    console.error('获取分类选项失败:', error)
  }
}

// 获取品牌选项
const getBrandOptions = async () => {
  try {
    const response = await brandApi.getList({ per_page: 999 }) as any
    brandOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
  } catch (error) {
    console.error('获取品牌选项失败:', error)
  }
}

// 获取产品详情（编辑模式）
const getProductDetail = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const productId = Number(route.params.id)
    const response = await productApi.getById(productId) as any
    const product = response.data || response
    
    // 填充表单数据
    Object.assign(form, {
      name: product.name || '',
      model: product.model || '',
      unit: product.unit || '',
      category_id: product.category_id || '',
      brand_id: product.brand_id || '',
      images: product.images || [],
      description: product.description || '',
      notes: product.notes || '',
      status: product.status || '正常'
    })

    // 填充规格数据
    specifications.value = product.specifications || []

    // 填充属性数据
    attributes.value = product.attributes || []

    // 初始化图片列表
    imageList.value = (product.images || []).map((img: any, index: number) => ({
      uid: img.id || index,
      name: img.file_name || `image-${index}`,
      status: 'done',
      url: img.url.startsWith('http') ? img.url : `http://localhost:5001/api/v1${img.url}`
    }))
  } catch (error) {
    console.error('获取产品详情失败:', error)
    ElMessage.error('获取产品详情失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  if (!isEdit.value) {
    Object.assign(form, {
      name: '',
      model: '',
      unit: '',
      category_id: '',
      brand_id: '',
      images: [],
      description: '',
      notes: '',
      status: '正常'
    })
    specifications.value = []
    attributes.value = []
    imageList.value = []
    filesToDelete.value = []
  }
}

// 规格管理方法
const addSpecification = () => {
  const isFirstSpec = specifications.value.length === 0
  specifications.value.push({
    specification: '',
    cost_price: 0,
    min_price: 0,
    max_price: 0,
    suggested_price: 0,
    tax_rate: 13,
    is_default: isFirstSpec
  })
}

const removeSpecification = (index: number) => {
  specifications.value.splice(index, 1)
}

const handleDefaultChange = (val: boolean, index: number) => {
  if (val) {
    // 取消其他规格的默认状态
    specifications.value.forEach((spec: any, i: number) => {
      if (i !== index) {
        spec.is_default = false
      }
    })
  }
}

// 属性管理方法
const addAttribute = () => {
  attributes.value.push({
    attribute_name: '',
    attribute_value: ''
  })
}

const removeAttribute = (index: number) => {
  attributes.value.splice(index, 1)
}

// 图片管理方法
const handleImagePreview = (file: any) => {
  previewImageUrl.value = file.url
  previewDialogVisible.value = true
}

const handleImageSuccess = (response: any, file: any) => {
  if (response.success) {
    ElMessage.success('图片上传成功')
    // 将相对路径转换为完整URL用于显示
    const fullUrl = `http://localhost:5001/api/v1${response.data.url}`
    // 更新文件列表中的URL（用于显示）
    file.url = fullUrl
    // 将图片信息保存到表单数据中（用于提交）
    if (!form.images) {
      form.images = []
    }
    // 创建ProductImage格式的对象
    const imageData = {
      url: response.data.url,
      file_name: response.data.filename,
      is_main: form.images.length === 0, // 第一张图片设为主图
      sort_order: form.images.length
    }
    form.images.push(imageData)
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

const handleImageError = (error: any) => {
  console.error('图片上传失败:', error)
  ElMessage.error('图片上传失败')
}

const handleImageRemove = (file: any) => {
  // 从表单数据中移除图片
  if (form.images && file.url) {
    // 提取相对路径用于匹配
    const relativePath = file.url.replace('http://localhost:5001/api/v1', '')
    const index = form.images.findIndex((img: any) => img.url === relativePath)
    if (index > -1) {
      // 记录需要删除的文件（用于清理临时上传的文件）
      filesToDelete.value.push(relativePath)
      form.images.splice(index, 1)
    }
  }
  ElMessage.success('图片删除成功')
}

const beforeImageUpload = (file: any) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

// 批量导入规格方法
const handleBatchImport = () => {
  batchImportDialogVisible.value = true
  batchImportText.value = ''
}

// 确认批量导入
const handleConfirmBatchImport = () => {
  if (!batchImportText.value || !batchImportText.value.trim()) {
    ElMessage.warning('请粘贴Excel数据')
    return
  }

  // 分割行，兼容\r\n和\n
  const inputText = batchImportText.value.trim().replace(/\r\n/g, '\n')
  const lines = inputText.split('\n').filter(line => line.trim() !== '')

  if (lines.length === 0) {
    ElMessage.warning('未检测到有效数据行')
    return
  }

  if (lines.length < 2) {
    ElMessage.warning('至少需要表头行和一行数据')
    return
  }

  // 解析表头
  const headerLine = lines[0]
  const headers = parseRow(headerLine)

  // 识别列索引
  const columnIndexes: any = {}

  headers.forEach((header, index) => {
    const normalizedHeader = header.toLowerCase().trim()

    if (normalizedHeader.includes('规格') || normalizedHeader.includes('spec')) {
      columnIndexes.specification = index
    } else if (normalizedHeader.includes('成本') || normalizedHeader.includes('cost')) {
      columnIndexes.cost_price = index
    } else if (normalizedHeader.includes('最低') || normalizedHeader.includes('min')) {
      columnIndexes.min_price = index
    } else if (normalizedHeader.includes('最高') || normalizedHeader.includes('max')) {
      columnIndexes.max_price = index
    } else if (normalizedHeader.includes('建议') || normalizedHeader.includes('市场') ||
               normalizedHeader.includes('单价') || normalizedHeader.includes('售价') ||
               normalizedHeader.includes('价格') || normalizedHeader.includes('suggested')) {
      columnIndexes.suggested_price = index
    } else if (normalizedHeader.includes('税率') || normalizedHeader.includes('tax')) {
      columnIndexes.tax_rate = index
    }
  })

  if (columnIndexes.specification === undefined) {
    ElMessage.error('无法识别表头中的"规格"列，请确保表头包含规格相关的关键词')
    return
  }

  // 处理数据行
  const importedSpecs: any[] = []
  const errorRows: string[] = []
  const warnings: string[] = []

  for (let i = 1; i < lines.length; i++) {
    const dataLine = lines[i]
    const values = parseRow(dataLine)

    if (values.length === 0) continue

    const specName = values[columnIndexes.specification]?.trim()
    if (!specName) {
      errorRows.push(`第 ${i + 1} 行: 规格名称为空`)
      continue
    }

    // 检查规格名是否重复
    if (importedSpecs.some(s => s.specification === specName) ||
        specifications.value.some(s => s.specification === specName)) {
      warnings.push(`第 ${i + 1} 行: 规格名称 "${specName}" 已存在，可能导致重复`)
    }

    const spec: any = {
      specification: specName,
      cost_price: parseFloat(values[columnIndexes.cost_price] || '0') || 0,
      min_price: parseFloat(values[columnIndexes.min_price] || '0') || 0,
      max_price: parseFloat(values[columnIndexes.max_price] || '0') || 0,
      suggested_price: parseFloat(values[columnIndexes.suggested_price] || '0') || 0,
      tax_rate: parseFloat(values[columnIndexes.tax_rate] || '13') || 13,
      is_default: false
    }

    // 价格逻辑处理
    if (spec.suggested_price > 0) {
      if (spec.min_price === 0) {
        spec.min_price = spec.suggested_price * 0.9
      }
      if (spec.max_price === 0) {
        spec.max_price = spec.suggested_price * 1.2
      }
    }

    importedSpecs.push(spec)
  }

  if (importedSpecs.length === 0) {
    ElMessage.error('没有解析出有效的规格数据')
    return
  }

  // 将第一个导入的规格设为默认（如果没有其他默认规格）
  const hasDefaultSpec = specifications.value.some(spec => spec.is_default)
  if (!hasDefaultSpec && importedSpecs.length > 0) {
    importedSpecs[0].is_default = true
  }

  // 添加导入的规格到表单数据
  specifications.value = [...specifications.value, ...importedSpecs]

  // 显示导入结果
  let message = `成功导入 ${importedSpecs.length} 个规格`

  if (errorRows.length > 0) {
    message += `，${errorRows.length} 行有错误`
  }

  ElMessage.success(message)

  // 如果有警告，显示警告信息
  if (warnings.length > 0) {
    setTimeout(() => {
      ElMessage.warning({
        message: warnings.join('\n'),
        duration: 5000
      })
    }, 500)
  }

  // 如果有错误，显示错误信息
  if (errorRows.length > 0) {
    setTimeout(() => {
      ElMessage.error({
        message: errorRows.join('\n'),
        duration: 8000
      })
    }, 1000)
  }

  // 关闭对话框
  batchImportDialogVisible.value = false
}

// 解析行数据（支持Tab、逗号、空格分隔）
const parseRow = (line: string): string[] => {
  // 优先使用Tab分隔
  if (line.includes('\t')) {
    return line.split('\t')
  }

  // 其次使用逗号分隔
  if (line.includes(',')) {
    return line.split(',')
  }

  // 最后使用空格分隔（多个空格视为一个分隔符）
  return line.split(/\s+/)
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()

    // 规格验证：至少要有一个规格
    if (specifications.value.length === 0) {
      ElMessage.error('请至少添加一个产品规格')
      // 跳转到规格价格tab
      activeTab.value = 'specs'
      return
    }

    submitting.value = true
    
    // 准备提交数据
    const submitData = { ...form }

    // 添加规格数据
    if (specifications.value.length > 0) {
      submitData.specifications = specifications.value.map(spec => ({
        specification: spec.specification,
        cost_price: String(spec.cost_price),
        suggested_price: String(spec.suggested_price),
        min_price: spec.min_price ? String(spec.min_price) : null,
        max_price: spec.max_price ? String(spec.max_price) : null,
        tax_rate: spec.tax_rate || 13.0,
        is_default: spec.is_default || false,
        notes: spec.notes || null
      }))
    }

    // 添加属性数据
    if (attributes.value.length > 0) {
      submitData.attributes = attributes.value.filter(attr =>
        attr.attribute_name && attr.attribute_value
      ).map(attr => ({
        attribute_name: attr.attribute_name,
        attribute_value: attr.attribute_value
      }))
    }

    // 添加图片数据
    if (form.images && form.images.length > 0) {
      submitData.images = form.images
    }

    // 添加需要删除的文件列表
    if (filesToDelete.value.length > 0) {
      submitData.files_to_delete = filesToDelete.value
    }

    // 处理空值
    if (!submitData.brand_id || submitData.brand_id === '') {
      delete submitData.brand_id
    }
    if (!submitData.image || submitData.image.trim() === '') {
      delete submitData.image
    }
    if (!submitData.description || submitData.description.trim() === '') {
      delete submitData.description
    }
    if (!submitData.notes || submitData.notes.trim() === '') {
      delete submitData.notes
    }
    
    if (isEdit.value) {
      // 更新产品
      const productId = Number(route.params.id)
      await productApi.update(productId, submitData)
      ElMessage.success('更新产品成功')
    } else {
      // 创建产品
      await productApi.create(submitData)
      ElMessage.success('创建产品成功')
    }
    
    // 返回列表页
    router.push('/products')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(isEdit.value ? '更新产品失败' : '创建产品失败')
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  getCategoryOptions()
  getBrandOptions()
  if (isEdit.value) {
    getProductDetail()
  }
})
</script>

<style lang="scss" scoped>
.product-form {
  .card-header {
    font-size: 18px;
    font-weight: bold;
  }
  
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .product-form-content {
    .el-form-item {
      margin-bottom: 24px;
    }
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .upload-tip {
    color: #909399;
    font-size: 14px;
    margin: 0;
  }

  .upload-area {
    margin-top: 10px;
  }

  .text-center {
    text-align: center;
  }

  .text-gray-500 {
    color: #9CA3AF;
  }

  .py-8 {
    padding-top: 32px;
    padding-bottom: 32px;
  }

  :deep(.el-tabs__content) {
    padding-top: 20px;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  // 图片上传样式
  .image-upload-container {
    .el-upload--picture-card {
      width: 104px;
      height: 104px;
      border-radius: 6px;
    }

    .el-upload-list--picture-card .el-upload-list__item {
      width: 104px;
      height: 104px;
      border-radius: 6px;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 104px;
      height: 104px;
      text-align: center;
      line-height: 104px;
    }
  }

  // 批量导入对话框样式
  .batch-import-content {
    .example-tip {
      margin-bottom: 15px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border-left: 4px solid #67c23a;

      p {
        margin: 8px 0;
        color: #606266;
        line-height: 1.5;
      }

      .example-code {
        background-color: #f5f5f5;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        color: #303133;
        white-space: pre;
      }

      .import-tip {
        font-size: 12px;
        color: #909399;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
