import{_ as e}from"./_plugin-vue_export-helper-1b428a4d.js";import{o as n,c as o,a as t}from"./index-3d4c440c.js";const a={class:"login-container"};const s=e({methods:{handleLogin(){localStorage.setItem("token","demo-token-"+Date.now()),this.$router.push("/")}}},[["render",function(e,s,l,r,i,d){return n(),o("div",a,[s[1]||(s[1]=t("h1",null,"LOGIN",-1)),s[2]||(s[2]=t("p",null,"Username: admin",-1)),s[3]||(s[3]=t("p",null,"Password: 123456",-1)),t("button",{onClick:s[0]||(s[0]=(...e)=>d.handleLogin&&d.handleLogin(...e))},"Login")])}],["__scopeId","data-v-5f24ee33"]]);export{s as default};
