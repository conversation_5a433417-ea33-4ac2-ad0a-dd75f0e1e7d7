<template>
  <el-breadcrumb separator="/" class="app-breadcrumb">
    <el-breadcrumb-item
      v-for="(item, index) in breadcrumbs"
      :key="index"
      :to="item.path ? { path: item.path } : undefined"
    >
      <span :class="{ 'breadcrumb-disabled': item.disabled }">
        {{ item.title }}
      </span>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { useNavigation } from '@/composables/useNavigation'

// 使用导航组合函数
const { breadcrumbs } = useNavigation()
</script>

<style scoped>
.app-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;
}

.breadcrumb-disabled {
  color: var(--el-text-color-regular);
  cursor: default;
}

.app-breadcrumb :deep(.el-breadcrumb__inner) {
  font-weight: 400;
}

.app-breadcrumb :deep(.el-breadcrumb__inner:hover) {
  color: var(--el-color-primary);
}

.app-breadcrumb :deep(.el-breadcrumb__inner.is-link) {
  color: var(--el-text-color-regular);
}

.app-breadcrumb :deep(.el-breadcrumb__inner.is-link:hover) {
  color: var(--el-color-primary);
}
</style>
