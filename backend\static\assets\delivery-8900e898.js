import{I as t}from"./index-3d4c440c.js";const e=t,n="/api/v1/delivery-notes";function r(t){return e.get(n,{params:t})}function s(t){return e.get(`${n}/${t}`)}function o(t,n){return e.post(`/api/v1/orders/${t}/delivery-notes`,n)}function u(t,r){return e.put(`${n}/${t}`,r)}function a(t){return e.delete(`${n}/${t}`)}function p(t){return e.post(`${n}/export`,{ids:t},{responseType:"blob"})}const i=(t,r)=>e.put(`${n}/${t}/status`,{status:r});function $(t){return e.get(`${n}/${t}/export`,{responseType:"blob",params:{print:!0}})}export{i as a,s as b,o as c,a as d,p as e,r as g,$ as p,u};
