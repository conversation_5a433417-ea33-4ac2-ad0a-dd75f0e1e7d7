import{ar as e,r as a,e as s,U as t,f as l,o as u,c as r,a as n,g as o,h as d,i as c,j as i,t as v,k as p}from"./index-3d4c440c.js";import{_ as h}from"./_plugin-vue_export-helper-1b428a4d.js";const y="http://127.0.0.1:5001",f="http://127.0.0.1:5001",g="/api/v1/dashboard/stats";const k={class:"simple-test-container"},_={class:"status-bar"},m={class:"actions"},w={key:0,class:"loading"},$={key:1,class:"error"},C={key:2,class:"response"},A=h({__name:"SimpleTest",setup(h){const A=a(!1),O=a(null),I=a(null),S=a("unknown"),T=a(!1);s((()=>{b()}));const b=async()=>{T.value=!0,S.value="unknown";try{200===(await e.get(y,{timeout:2e3})).status?(S.value="running",t.success("服务器正常运行")):(S.value="error",t.error("服务器返回非200状态码"))}catch(a){console.error("服务器检查失败:",a),S.value="error",t.error("无法连接到服务器，请确保后端服务已启动")}finally{T.value=!1}},j=async()=>{A.value=!0,O.value=null,I.value=null;try{const a=await function(a=g){return e.get(`${y}${a}`)}();I.value={data:a.data,status:a.status,headers:a.headers}}catch(a){console.error("测试主服务器失败:",a),O.value=a.message,a.response&&(I.value={status:a.response.status,data:a.response.data,headers:a.response.headers})}finally{A.value=!1}},x=async()=>{A.value=!0,O.value=null,I.value=null;try{const a=await function(a=g){return e.get(`${y}${a}`,{headers:{Authorization:"Bearer test-token"}})}();I.value={data:a.data,status:a.status,headers:a.headers}}catch(a){console.error("测试带Auth头失败:",a),O.value=a.message,a.response&&(I.value={status:a.response.status,data:a.response.data,headers:a.response.headers})}finally{A.value=!1}},z=async()=>{A.value=!0,O.value=null,I.value=null;try{const a=await function(a=g){return e.options(`${y}${a}`,{headers:{"Access-Control-Request-Method":"GET","Access-Control-Request-Headers":"authorization"}})}();I.value={data:a.data,status:a.status,headers:a.headers}}catch(a){console.error("测试OPTIONS失败:",a),O.value=a.message,a.response&&(I.value={status:a.response.status,data:a.response.data,headers:a.response.headers})}finally{A.value=!1}},N=async()=>{A.value=!0,O.value=null,I.value=null;try{const a=await function(a="/api/test"){return e.get(`${f}${a}`)}();I.value={data:a.data,status:a.status,headers:a.headers}}catch(a){console.error("测试测试服务器失败:",a),O.value=a.message,a.response&&(I.value={status:a.response.status,data:a.response.data,headers:a.response.headers})}finally{A.value=!1}};return(e,a)=>{const s=l("el-tag"),t=l("el-button");return u(),r("div",k,[a[10]||(a[10]=n("h2",null,"简单API测试",-1)),n("div",_,["running"===S.value?(u(),o(s,{key:0,type:"success"},{default:d((()=>a[0]||(a[0]=[p("服务器正常运行")]))),_:1})):"error"===S.value?(u(),o(s,{key:1,type:"danger"},{default:d((()=>a[1]||(a[1]=[p("服务器未响应")]))),_:1})):(u(),o(s,{key:2,type:"info"},{default:d((()=>a[2]||(a[2]=[p("未知状态")]))),_:1})),c(t,{onClick:b,size:"small",loading:T.value},{default:d((()=>a[3]||(a[3]=[p("检查服务器状态")]))),_:1},8,["loading"])]),n("div",m,[c(t,{onClick:j,type:"primary"},{default:d((()=>a[4]||(a[4]=[p("测试主服务器")]))),_:1}),c(t,{onClick:x,type:"success"},{default:d((()=>a[5]||(a[5]=[p("测试带Auth头")]))),_:1}),c(t,{onClick:z,type:"warning"},{default:d((()=>a[6]||(a[6]=[p("测试OPTIONS")]))),_:1}),c(t,{onClick:N,type:"info"},{default:d((()=>a[7]||(a[7]=[p("测试5001端口服务器")]))),_:1})]),A.value?(u(),r("div",w,"正在加载...")):i("",!0),O.value?(u(),r("div",$,[a[8]||(a[8]=n("h3",null,"错误",-1)),n("p",null,v(O.value),1)])):i("",!0),I.value?(u(),r("div",C,[a[9]||(a[9]=n("h3",null,"响应",-1)),n("pre",null,v(JSON.stringify(I.value,null,2)),1)])):i("",!0)])}}},[["__scopeId","data-v-30258ed0"]]);export{A as default};
