import{I as t}from"./index-3d4c440c.js";const e="/api/v1/statements";async function n(n){console.log("调用getStatements API，参数:",n);try{const r=await t({url:e,method:"get",params:n});if(console.log("getStatements API原始响应:",r),r&&"object"==typeof r){if(Array.isArray(r.list))return console.log("从response.list获取数据, 长度:",r.list.length),{list:r.list,pagination:r.pagination||{}};if(200===r.code&&Array.isArray(r.list))return console.log("从response.code=200且response.list获取数据, 长度:",r.list.length),{list:r.list,pagination:r.pagination||{}};if(200===r.code&&Array.isArray(r.data))return console.log("从response.code=200且response.data获取数据, 长度:",r.data.length),{list:r.data,pagination:r.page||{}};if(console.warn("未匹配到预期的响应格式，尝试深入检查response结构:",r),Array.isArray(r))return console.log("response本身是数组，长度:",r.length),{list:r,pagination:{}};for(const t in r)if(Array.isArray(r[t]))return console.log(`从response.${t}获取到数组数据，长度:`,r[t].length),{list:r[t],pagination:{}}}return console.warn("getStatements: 未能识别的响应格式，返回空数组",r),{list:[],pagination:{}}}catch(r){throw console.error("getStatements API调用失败:",r),r}}function r(n){return t({url:`${e}/${n}`,method:"get"})}function o(n){return t({url:`${e}/${n}/return-orders`,method:"get"})}function s(n){return t({url:e,method:"post",data:n})}function a(n,r){return t({url:`${e}/${n}`,method:"put",data:r})}function i(n){return t({url:`${e}/${n}`,method:"delete"})}function l(n,r){return t({url:`${e}/${n}/confirm`,method:"put",data:r})}function u(n,r,o){return t({url:`${e}/available-delivery-notes`,method:"get",params:{...n?{customer_id:n}:{},...r?{status:r}:{},...o}})}function c(n){return t({url:`${e}/${n}/export`,method:"get",responseType:"blob"})}export{u as a,r as b,l as c,i as d,c as e,s as f,n as g,o as h,a as u};
