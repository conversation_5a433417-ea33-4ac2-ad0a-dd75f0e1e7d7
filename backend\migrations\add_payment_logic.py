#!/usr/bin/env python3
"""
添加收款逻辑相关的表和字段
包括客户余额管理、余额交易记录、对账单收款记录以及结清状态字段
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import *
from datetime import datetime
from decimal import Decimal

def add_payment_logic():
    """添加收款逻辑相关的表和字段"""
    app = create_app()
    
    with app.app_context():
        print("🔧 开始添加收款逻辑相关的表和字段...")
        
        try:
            # 1. 创建新表
            print("📊 创建新表...")
            
            # 检查表是否已存在
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            # 创建客户余额表
            if 'customer_balances' not in existing_tables:
                print("   ✅ 创建 customer_balances 表")
                db.session.execute(db.text("""
                    CREATE TABLE customer_balances (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_id INTEGER NOT NULL UNIQUE,
                        balance NUMERIC(12,2) NOT NULL DEFAULT 0.00,
                        frozen_balance NUMERIC(12,2) NOT NULL DEFAULT 0.00,
                        created_at DATETIME NOT NULL,
                        updated_at DATETIME NOT NULL,
                        FOREIGN KEY (customer_id) REFERENCES customers(id)
                    )
                """))
            else:
                print("   ⚠️  customer_balances 表已存在")
            
            # 创建余额交易记录表
            if 'balance_transactions' not in existing_tables:
                print("   ✅ 创建 balance_transactions 表")
                db.session.execute(db.text("""
                    CREATE TABLE balance_transactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_id INTEGER NOT NULL,
                        transaction_type VARCHAR(20) NOT NULL,
                        amount NUMERIC(12,2) NOT NULL,
                        balance_before NUMERIC(12,2) NOT NULL,
                        balance_after NUMERIC(12,2) NOT NULL,
                        reference_type VARCHAR(20),
                        reference_id INTEGER,
                        description VARCHAR(200),
                        created_at DATETIME NOT NULL,
                        updated_at DATETIME NOT NULL,
                        FOREIGN KEY (customer_id) REFERENCES customers(id)
                    )
                """))
            else:
                print("   ⚠️  balance_transactions 表已存在")
            
            # 创建对账单收款记录表
            if 'statement_payments' not in existing_tables:
                print("   ✅ 创建 statement_payments 表")
                db.session.execute(db.text("""
                    CREATE TABLE statement_payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        statement_id INTEGER NOT NULL,
                        payment_date DATE NOT NULL,
                        amount NUMERIC(12,2) NOT NULL,
                        payment_method VARCHAR(50) NOT NULL,
                        payment_source VARCHAR(20) NOT NULL DEFAULT 'direct',
                        reference_number VARCHAR(100),
                        bank_account VARCHAR(200),
                        balance_transaction_id INTEGER,
                        notes TEXT,
                        status VARCHAR(20) NOT NULL DEFAULT '已确认',
                        created_by VARCHAR(50),
                        created_at DATETIME NOT NULL,
                        updated_at DATETIME NOT NULL,
                        FOREIGN KEY (statement_id) REFERENCES statements(id),
                        FOREIGN KEY (balance_transaction_id) REFERENCES balance_transactions(id)
                    )
                """))
            else:
                print("   ⚠️  statement_payments 表已存在")
            
            # 2. 添加新字段到现有表
            print("🔧 添加新字段到现有表...")
            
            # 检查并添加字段到 delivery_notes 表
            delivery_notes_columns = [col['name'] for col in inspector.get_columns('delivery_notes')]
            
            if 'settlement_status' not in delivery_notes_columns:
                print("   ✅ 添加 settlement_status 字段到 delivery_notes 表")
                db.session.execute(db.text("ALTER TABLE delivery_notes ADD COLUMN settlement_status VARCHAR(20) NOT NULL DEFAULT '未结清'"))

            if 'settlement_date' not in delivery_notes_columns:
                print("   ✅ 添加 settlement_date 字段到 delivery_notes 表")
                db.session.execute(db.text("ALTER TABLE delivery_notes ADD COLUMN settlement_date DATE"))
            
            # 检查并添加字段到 return_orders 表
            return_orders_columns = [col['name'] for col in inspector.get_columns('return_orders')]
            
            if 'settlement_status' not in return_orders_columns:
                print("   ✅ 添加 settlement_status 字段到 return_orders 表")
                db.session.execute(db.text("ALTER TABLE return_orders ADD COLUMN settlement_status VARCHAR(20) NOT NULL DEFAULT '未结清'"))

            if 'settlement_date' not in return_orders_columns:
                print("   ✅ 添加 settlement_date 字段到 return_orders 表")
                db.session.execute(db.text("ALTER TABLE return_orders ADD COLUMN settlement_date DATE"))

            # 检查并添加字段到 statements 表
            statements_columns = [col['name'] for col in inspector.get_columns('statements')]

            if 'paid_amount' not in statements_columns:
                print("   ✅ 添加 paid_amount 字段到 statements 表")
                db.session.execute(db.text("ALTER TABLE statements ADD COLUMN paid_amount NUMERIC(12,2) NOT NULL DEFAULT 0.00"))

            if 'payment_status' not in statements_columns:
                print("   ✅ 添加 payment_status 字段到 statements 表")
                db.session.execute(db.text("ALTER TABLE statements ADD COLUMN payment_status VARCHAR(20) NOT NULL DEFAULT '未支付'"))

            if 'settlement_date' not in statements_columns:
                print("   ✅ 添加 settlement_date 字段到 statements 表")
                db.session.execute(db.text("ALTER TABLE statements ADD COLUMN settlement_date DATE"))
            
            # 3. 创建索引
            print("📈 创建索引...")
            
            try:
                db.session.execute(db.text("CREATE INDEX IF NOT EXISTS idx_customer_balances_customer ON customer_balances(customer_id)"))
                db.session.execute(db.text("CREATE INDEX IF NOT EXISTS idx_balance_transactions_customer ON balance_transactions(customer_id)"))
                db.session.execute(db.text("CREATE INDEX IF NOT EXISTS idx_balance_transactions_type ON balance_transactions(transaction_type)"))
                db.session.execute(db.text("CREATE INDEX IF NOT EXISTS idx_balance_transactions_reference ON balance_transactions(reference_type, reference_id)"))
                db.session.execute(db.text("CREATE INDEX IF NOT EXISTS idx_statement_payments_statement ON statement_payments(statement_id)"))
                db.session.execute(db.text("CREATE INDEX IF NOT EXISTS idx_statement_payments_date ON statement_payments(payment_date)"))
                db.session.execute(db.text("CREATE INDEX IF NOT EXISTS idx_delivery_notes_settlement ON delivery_notes(settlement_status)"))
                db.session.execute(db.text("CREATE INDEX IF NOT EXISTS idx_return_orders_settlement ON return_orders(settlement_status)"))
                print("   ✅ 索引创建完成")
            except Exception as e:
                print(f"   ⚠️  索引创建警告: {str(e)}")
            
            # 4. 为现有客户创建余额记录
            print("👥 为现有客户创建余额记录...")

            customers = db.session.execute(db.text("SELECT id FROM customers")).fetchall()
            for customer in customers:
                customer_id = customer[0]

                # 检查是否已有余额记录
                existing = db.session.execute(
                    db.text("SELECT id FROM customer_balances WHERE customer_id = :customer_id"),
                    {"customer_id": customer_id}
                ).fetchone()

                if not existing:
                    now = datetime.now()
                    db.session.execute(db.text("""
                        INSERT INTO customer_balances (customer_id, balance, frozen_balance, created_at, updated_at)
                        VALUES (:customer_id, 0.00, 0.00, :created_at, :updated_at)
                    """), {"customer_id": customer_id, "created_at": now, "updated_at": now})

            db.session.commit()
            print(f"   ✅ 为 {len(customers)} 个客户创建了余额记录")
            
            print("🎉 收款逻辑相关的表和字段添加完成！")
            return True
            
        except Exception as e:
            print(f"❌ 迁移失败: {str(e)}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    success = add_payment_logic()
    if success:
        print("\n✅ 迁移成功完成！")
        print("\n📋 新增功能:")
        print("  - 客户余额管理 (预付款功能)")
        print("  - 余额交易记录追踪")
        print("  - 对账单收款记录")
        print("  - 发货单和退货单结清状态")
        print("  - 对账单付款状态管理")
    else:
        print("\n❌ 迁移失败，请检查错误信息")
        sys.exit(1)
