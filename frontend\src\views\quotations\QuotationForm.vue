<template>
  <div class="quotation-edit">
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="form-title">
          {{ isEdit ? '编辑报价单' : (requestId ? '从报价需求表创建报价单' : '新增报价单') }}
        </h2>
        <div>
          <el-button @click="goBack">返回</el-button>
          <el-button type="primary" @click="handleSave(false)" :loading="submitting">保存</el-button>
        </div>
      </div>
    </el-card>

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="quotation-form"
    >
      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="报价单号" prop="quotation_number">
              <el-input v-model="form.quotation_number" placeholder="系统自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="当前状态">
              <el-tag :type="getStatusType(form.status)" size="large">
                {{ form.status }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户" prop="customer_id">
              <el-select
                v-model="form.customer_id"
                placeholder="请选择客户"
                filterable
                remote
                :remote-method="handleSearchCustomers"
                :loading="customerLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in customerOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="有效期至" prop="valid_until">
              <el-date-picker
                v-model="form.valid_until"
                type="date"
                placeholder="请选择有效期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目名称" prop="project_name">
              <el-input v-model="form.project_name" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="项目地址" prop="project_address">
              <el-input v-model="form.project_address" placeholder="请输入项目地址" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="付款条件" prop="payment_terms">
              <el-input v-model="form.payment_terms" placeholder="请输入付款条件" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交货条件" prop="delivery_terms">
              <el-input v-model="form.delivery_terms" placeholder="请输入交货条件" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注" prop="notes">
          <el-input 
            v-model="form.notes" 
            type="textarea" 
            :rows="2"
            placeholder="请输入备注信息" 
          />
        </el-form-item>
      </el-card>

      <el-card>
        <template #header>
          <div class="card-header flex-between">
            <span>报价项目</span>
            <div>
              <el-button type="primary" @click="openProductSelectDialog">
                <el-icon><Plus /></el-icon>
                选择产品规格
              </el-button>
              <el-button type="danger" :disabled="!selectedItems.length" @click="handleDeleteItems">
                <el-icon><Delete /></el-icon>
                删除选中
              </el-button>
            </div>
          </div>
        </template>

        <!-- 统一设置工具栏 -->
        <div class="batch-settings-toolbar" style="margin-bottom: 16px; padding: 12px; background-color: #f5f7fa; border-radius: 4px;">
          <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
            <span style="font-weight: 500; color: #606266;">统一设置：</span>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="font-size: 14px; color: #606266;">税率：</span>
              <el-input-number
                v-model="batchSettings.taxRate"
                :min="0"
                :max="100"
                :precision="2"
                controls-position="right"
                style="width: 120px;"
                placeholder="税率"
              />
              <span style="font-size: 12px; color: #909399;">%</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="font-size: 14px; color: #606266;">折扣：</span>
              <el-input-number
                v-model="batchSettings.discount"
                :min="0"
                :max="100"
                :precision="0"
                controls-position="right"
                style="width: 120px;"
                placeholder="折扣"
              />
              <span style="font-size: 12px; color: #909399;">%</span>
            </div>
            <el-button
              type="primary"
              size="small"
              @click="applyBatchSettings"
              :disabled="displayedItems.length === 0"
            >
              <el-icon><Setting /></el-icon>
              应用到所有产品
            </el-button>
          </div>
        </div>

        <el-table
          ref="itemsTable"
          :data="displayedItems"
          border
          show-summary
          :summary-method="getSummaries"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="name" label="产品名称" min-width="150" show-overflow-tooltip />
          <el-table-column prop="model" label="型号" width="120" />
          <el-table-column prop="specification" label="规格" width="150" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="quantity" label="数量" width="100">
            <template #default="{ row }">
              <el-input-number
                v-model="row.quantity"
                :min="1"
                :precision="0"
                size="small"
                controls-position="right"
                style="width: 100%"
                @change="() => updateItemTotal(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="unit_price" label="单价" width="120">
            <template #default="{ row }">
              <el-input-number
                v-model="row.unit_price"
                :min="0"
                :precision="2"
                :step="1"
                size="small"
                controls-position="right"
                style="width: 100%"
                @change="() => updateItemTotal(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="discount" label="折扣(%)" width="100">
            <template #default="{ row }">
              <el-input-number
                v-model="row.discount"
                :min="0"
                :max="100"
                :precision="2"
                :step="1"
                size="small"
                controls-position="right"
                style="width: 100%"
                @change="() => updateItemTotal(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="tax_rate" label="税率(%)" width="100">
            <template #default="{ row }">
              <el-input-number
                v-model="row.tax_rate"
                :min="0"
                :max="100"
                :precision="2"
                :step="1"
                size="small"
                controls-position="right"
                style="width: 100%"
                @change="() => updateItemTotal(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="total_price" label="金额" width="120">
            <template #default="{ row }">
              ¥{{ formatNumber(row.total_price) }}
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" min-width="150">
            <template #default="{ row }">
              <el-input
                v-model="row.notes"
                placeholder="添加备注"
                size="small"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" link @click="handleDeleteItem($index)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-form>

    <!-- 选择产品对话框 -->
    <el-dialog
      v-model="productSelectDialogVisible"
      title="选择产品"
      width="800px"
      destroy-on-close
    >
      <div class="product-search mb-20">
        <el-input
          v-model="productSearchKeyword"
          placeholder="搜索产品名称或型号"
          clearable
          @input="searchProductsForSelection"
        >
          <template #append>
            <el-button :icon="Search" @click="searchProductsForSelection"></el-button>
          </template>
        </el-input>
      </div>

      <el-table
        :data="productSearchResults"
        border
        style="width: 100%"
        @row-click="handleProductRowClick"
        row-key="id"
      >
        <el-table-column prop="name" label="产品名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="model" label="型号" width="120" show-overflow-tooltip />
        <el-table-column label="规格" min-width="180">
          <template #default="{ row }">
            <div v-if="row.specifications && row.specifications.length > 0">
              <el-checkbox-group v-model="row.selectedSpecIds">
                <div v-for="spec in row.specifications" :key="spec.id" style="margin-bottom: 5px;">
                  <el-checkbox :label="spec.id">
                    <span :class="{ 'existing-spec-text': isExistingSpecification(spec.id) }">
                      {{ spec.specification }} (¥{{ spec.suggested_price || spec.price || 0 }})
                    </span>
                    <el-tag
                      v-if="isExistingSpecification(spec.id)"
                      type="info"
                      size="small"
                      class="existing-spec-tag ml-5"
                      effect="plain"
                    >
                      已选择
                    </el-tag>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
            <el-empty v-else description="无规格信息" :image-size="40" />
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
      </el-table>

      <div class="flex-between mt-20">
        <span></span>
        <el-pagination
          v-model:current-page="productPagination.currentPage"
          v-model:page-size="productPagination.pageSize"
          :total="productPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleProductSizeChange"
          @current-change="handleProductCurrentChange"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="productSelectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmProductSelection">确定添加</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Search, Warning, Setting } from '@element-plus/icons-vue'
import { quotationApi, quotationRequestApi } from '@/api/quotation'
import { customerApi } from '@/api/customer'
import { productApi } from '@/api/product'
import { formatCurrency, formatNumber } from '@/utils/format'
import type { FormInstance, FormRules, TableColumnCtx } from 'element-plus'

// 类型定义
interface SelectableProduct {
  productId: number;
  name: string;
  model: string;
  unit: string;
  specId: number;
  specification: string;
  suggested_price: number;
  tax_rate: number;
}

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance | null>(null)
const itemsTable = ref<any>(null)
const productSelectionTableRef = ref<any>(null)

const isEdit = computed(() => !!route.params.id)
const quotationId = computed(() => route.params.id ? parseInt(route.params.id as string, 10) : null)
const requestId = computed(() => route.query.requestId ? parseInt(route.query.requestId as string, 10) : null)

const loading = ref(false)
const submitting = ref(false)
const customerLoading = ref(false)
const loadingFromRequest = ref(false)

// 表单数据
const form = reactive({
  id: null,
  quotation_number: '',
  customer_id: null,
  customer_name: '',
  project_name: '',
  project_address: '',
  valid_until: '',
  payment_terms: '',
  delivery_terms: '',
  status: '待确认',
  notes: '',
  total_amount: 0,
  items: []
})

// 批量设置数据
const batchSettings = reactive({
  taxRate: 13.0,  // 默认税率13%
  discount: 0.0   // 默认折扣0%
})

const displayedItems = ref([])
const selectedItems = ref([])



// 客户选项
const customerOptions = ref([])

// 产品选择相关
const productSelectDialogVisible = ref(false)
const productSearchKeyword = ref('')
const productSearchResults = ref([])
const productPagination = reactive({ currentPage: 1, pageSize: 10, total: 0 })

// 表单验证规则
const rules: FormRules = {
  customer_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
  valid_until: [{ required: true, message: '请选择有效期至', trigger: 'change' }],
  project_name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  project_address: [{ required: true, message: '请输入项目地址', trigger: 'blur' }],
}

// 返回上一页
const goBack = () => {
  router.push('/quotations')
}



// 搜索客户
const handleSearchCustomers = async (query: string) => {
  if (query) {
    try {
      customerLoading.value = true
      const response = await customerApi.getList({ name: query, per_page: 20 }) as any
      customerOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('搜索客户失败:', error)
    } finally {
      customerLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

// 计算项目总价
const updateItemTotal = (row: any) => {
  const quantity = Number(row.quantity) || 0
  const unitPrice = Number(row.unit_price) || 0
  const discount = Number(row.discount) || 0  // 默认为0，即没有折扣
  const taxRate = Number(row.tax_rate) || 0

  // 计算基础价格
  let totalPrice = quantity * unitPrice

  // 应用折扣（扣减逻辑）
  if (discount >= 0 && discount <= 100) {
    totalPrice = totalPrice * (1 - discount / 100)  // 改为扣减逻辑
  }

  // 应用税率
  if (taxRate > 0) {
    totalPrice = totalPrice * (1 + taxRate / 100)
  }

  row.total_price = totalPrice
  updateTotalAmount()
}

// 更新总金额
const updateTotalAmount = () => {
  form.total_amount = displayedItems.value.reduce((total: number, item: any) => {
    return total + (Number(item.total_price) || 0);
  }, 0);
}

// 批量应用税率和折扣设置
const applyBatchSettings = () => {
  if (displayedItems.value.length === 0) {
    ElMessage.warning('暂无产品数据')
    return
  }

  // 应用到所有产品
  displayedItems.value.forEach(item => {
    item.tax_rate = batchSettings.taxRate
    item.discount = batchSettings.discount
    // 重新计算总价
    updateItemTotal(item)
  })

  ElMessage.success(`已为 ${displayedItems.value.length} 个产品设置税率 ${batchSettings.taxRate}% 和折扣 ${batchSettings.discount}%`)
}

// 保存报价单
const handleSave = async (skipRedirect = false) => {
  if (!formRef.value) return

  try {
    submitting.value = true

    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) {
      ElMessage.error('表单验证失败')
      submitting.value = false
      return
    }

    // 检查是否有报价项目
    if (displayedItems.value.length === 0) {
      ElMessage.warning('请至少添加一个报价项目')
      submitting.value = false
      return
    }

    // 准备报价项目数据
    const itemsToSave = displayedItems.value.map((item: any) => ({
      id: item.id || undefined, // 编辑模式下保留项目ID
      product_id: item.matched_product_id,
      product_specification_id: item.matched_specification_id,
      quantity: Number(item.quantity),
      unit_price: Number(item.unit_price),
      discount: Number(item.discount),
      tax_rate: Number(item.tax_rate),
      notes: item.notes || undefined,
      // 快照字段 - 使用后端模型的字段名
      product_name: item.name,
      product_model: item.model,
      product_specification: item.specification,
      product_unit: item.unit
    }))

    // 准备表单数据
    const dataToSave: any = {
      customer_id: form.customer_id,
      project_name: form.project_name,
      project_address: form.project_address,
      valid_until: form.valid_until,
      payment_terms: form.payment_terms,
      delivery_terms: form.delivery_terms,
      status: form.status,
      notes: form.notes,
      items: itemsToSave
    }

    // 如果是从报价需求表创建，添加request_id
    if (requestId.value) {
      dataToSave.request_id = requestId.value
    }

    // 确保不包含id字段（编辑模式下form可能包含id）
    delete dataToSave.id

    console.log('保存数据:', JSON.stringify(dataToSave, null, 2))

    if (isEdit.value) {
      // 更新报价单
      await quotationApi.update(quotationId.value!, dataToSave)
      ElMessage.success('报价单更新成功')
    } else {
      // 创建报价单
      await quotationApi.create(dataToSave)
      ElMessage.success('报价单创建成功')
    }

    // 保存成功后返回列表页（除非指定跳过）
    if (!skipRedirect) {
      router.push('/quotations')
    }

  } catch (error: any) {
    console.error('保存报价单失败:', error)
    const errorMessage = error.response?.data?.message || error.message || '未知错误'
    ElMessage.error(`保存报价单失败: ${errorMessage}`)
  } finally {
    submitting.value = false
  }
}



// 打开产品选择对话框
const openProductSelectDialog = () => {
  productSearchKeyword.value = ''
  productPagination.currentPage = 1
  productSearchResults.value = []
  searchProductsForSelection()
  productSelectDialogVisible.value = true
}

const handleDeleteItems = async () => {
  if (!selectedItems.value.length) {
    ElMessage.warning('请选择要删除的项目')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedItems.value.length} 个项目吗？`, '确认删除', {
      type: 'warning'
    })

    // 从displayedItems中移除选中的项目
    selectedItems.value.forEach(selectedItem => {
      const index = displayedItems.value.findIndex(item => item === selectedItem)
      if (index > -1) {
        displayedItems.value.splice(index, 1)
      }
    })

    selectedItems.value = []
    updateTotalAmount()
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedItems.value = selection
}

const handleDeleteItem = (index: number) => {
  displayedItems.value.splice(index, 1)
  updateTotalAmount()
  ElMessage.success('删除成功')
}

const getSummaries = (param: { columns: TableColumnCtx<any>[]; data: any[] }) => {
  const { columns, data } = param
  const sums: string[] = []
  
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    const prop = column.property
    if (prop && ['quantity', 'total_price'].includes(prop)) {
      const values = data.map(item => Number(item[prop]))
      const total = values.reduce((prev, curr) => prev + curr, 0)
      
      if (prop === 'total_price') {
        sums[index] = formatCurrency(total)
      } else {
        sums[index] = total.toString()
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

const searchProductsForSelection = async () => {
  try {
    const params: any = {
      page: productPagination.currentPage,
      per_page: productPagination.pageSize,
      with_specifications: true
    }

    // 如果有搜索关键词，添加到参数中
    if (productSearchKeyword.value && productSearchKeyword.value.trim()) {
      const keyword = productSearchKeyword.value.trim()
      params.search = keyword
    }

    const response = await productApi.getList(params) as any
    const products = Array.isArray(response) ? response : (response.data || response.items || [])

    if (products && Array.isArray(products)) {
      // 为每个产品初始化selectedSpecIds数组
      productSearchResults.value = products.map((product: any) => ({
        ...product,
        selectedSpecIds: []
      }))
      productPagination.total = response.pagination?.total || response.total || 0
    } else {
      productSearchResults.value = []
      productPagination.total = 0
    }
  } catch (error) {
    console.error('搜索产品失败:', error)
    productSearchResults.value = []
    productPagination.total = 0
    ElMessage.error('搜索产品失败')
  }
}

// 分页处理
const handleProductSizeChange = (size: number) => {
  productPagination.pageSize = size
  productPagination.currentPage = 1
  searchProductsForSelection()
}

const handleProductCurrentChange = (page: number) => {
  productPagination.currentPage = page
  searchProductsForSelection()
}



// 判断规格是否已存在于报价单中
const isExistingSpecification = (specId: number) => {
  return displayedItems.value.some((item: any) => item.matched_specification_id === specId)
}

// 处理产品行点击事件
const handleProductRowClick = (row: any) => {
  // 可以在这里添加行点击逻辑
}

// 处理行点击事件
const handleRowClick = (row: SelectableProduct) => {
  // 获取表格引用
  const table = productSelectionTableRef.value
  if (!table) {
    return
  }

  // 检查当前行是否已被选中
  const isSelected = selectedSpecifications.value.some(spec => spec.specId === row.specId)

  if (isSelected) {
    // 如果已选中，则取消选择
    table.toggleRowSelection(row, false)
  } else {
    // 如果未选中，则选择该行
    table.toggleRowSelection(row, true)
  }
}

const confirmProductSelection = () => {
  const selectedProducts = productSearchResults.value.filter(product =>
    product.selectedSpecIds && product.selectedSpecIds.length > 0
  )

  if (selectedProducts.length === 0) {
    ElMessage.warning('请至少选择一个产品规格')
    return
  }

  let addedCount = 0

  for (const product of selectedProducts) {
    for (const specId of product.selectedSpecIds) {
      const spec = product.specifications.find((s: any) => s.id === specId)
      if (spec) {
        const newItem = {
          name: product.name,
          model: product.model,
          specification: spec.specification,
          unit: product.unit,
          quantity: 1,
          unit_price: spec.suggested_price || 0,
          discount: 0,
          tax_rate: spec.tax_rate || 13,
          total_price: 0,
          notes: '',
          // 保存匹配信息
          matched_product_id: product.id,
          matched_specification_id: spec.id
        }
        updateItemTotal(newItem)
        displayedItems.value.unshift(newItem) // 添加到顶部
        addedCount++
      }
    }
  }

  updateTotalAmount()

  productSelectDialogVisible.value = false
  // 清空选择状态
  productSearchResults.value.forEach(product => {
    product.selectedSpecIds = []
  })

  ElMessage.success(`已添加 ${addedCount} 个产品规格`)
}

// 从报价需求表加载数据
const loadFromRequest = async (requestId: number) => {
  try {
    loadingFromRequest.value = true
    const response = await quotationRequestApi.getById(requestId) as any
    const requestData = response.data || response

    if (requestData) {
      // 填充基本信息
      form.customer_id = requestData.customer_id
      form.project_name = requestData.project_name || ''
      form.project_address = requestData.project_address || ''
      form.notes = `基于报价需求表 ${requestData.request_number} 创建`

      // 如果有客户信息，添加到客户选项中
      if (requestData.customer) {
        customerOptions.value = [requestData.customer]
      }

      // 设置默认有效期（30天后）
      const validDate = new Date()
      validDate.setDate(validDate.getDate() + 30)
      form.valid_until = validDate.toISOString().split('T')[0]

      // 如果有产品明细，转换为报价单项目
      if (requestData.items && requestData.items.length > 0) {
        displayedItems.value = requestData.items
          .filter((item: any) => item.matched_product_id) // 只处理已匹配的产品
          .map((item: any) => ({
            // 使用匹配后的产品信息
            name: item.matched_product?.name || item.product_name,
            model: item.matched_product?.model || item.product_model,
            specification: item.matched_specification?.specification || item.product_spec,
            unit: item.matched_product?.unit || item.unit, // 单位从产品表获取
            quantity: item.quantity || 1,
            unit_price: parseFloat(item.matched_specification?.suggested_price || 0),
            discount: 0,
            tax_rate: item.matched_specification?.tax_rate || 13, // 使用产品规格的税率或默认13%
            total_price: (item.quantity || 1) * parseFloat(item.matched_specification?.suggested_price || 0),
            notes: item.notes || '',
            // 保存匹配信息用于后续处理
            matched_product_id: item.matched_product_id,
            matched_specification_id: item.matched_product_specification_id
          }))

        // 更新总金额
        updateTotalAmount()

        const matchedCount = displayedItems.value.length
        const totalCount = requestData.items.length
        if (matchedCount < totalCount) {
          ElMessage.warning(`已加载 ${matchedCount}/${totalCount} 个已匹配的产品项目`)
        } else {
          ElMessage.success(`已加载 ${matchedCount} 个产品项目`)
        }
      } else {
        ElMessage.success('已从报价需求表加载基本信息')
      }
    }
  } catch (error) {
    console.error('加载报价需求表数据失败:', error)
    ElMessage.error('加载报价需求表数据失败')
  } finally {
    loadingFromRequest.value = false
  }
}

// 初始化
// 加载报价单数据
const loadQuotationData = async () => {
  if (!isEdit.value || !quotationId.value) return

  try {
    loading.value = true
    const quotationData = await quotationApi.getById(quotationId.value)
    console.log('获取到报价单数据:', quotationData)

    // 兼容不同的API响应格式
    let responseData: any
    if (quotationData && typeof quotationData === 'object') {
      const respAny = quotationData as any
      if ((respAny.code === 200 || respAny.code === 0) && respAny.data) {
        // 标准API响应格式
        responseData = respAny.data
      } else if (respAny.id) {
        // 直接返回对象的格式
        responseData = quotationData
      } else {
        console.warn('无法识别的API响应格式', quotationData)
        responseData = quotationData
        ElMessage.warning('API响应格式不符合预期，数据可能不完整')
      }
    } else {
      throw new Error('无效的API响应')
    }

    // 处理报价项目数据
    const processedItems = (responseData.items || []).map((item: any) => ({
      name: item.product_name,
      model: item.product_model || '',
      specification: item.product_specification,
      unit: item.product_unit,
      quantity: Number(item.quantity || 0),
      unit_price: Number(item.unit_price || 0),
      discount: Number(item.discount || 0),
      tax_rate: Number(item.tax_rate || 0),
      total_price: 0,
      notes: item.notes || '',
      // 保存匹配信息
      matched_product_id: item.product_id,
      matched_specification_id: item.product_specification_id,
      // 保存项目ID用于更新
      id: item.id
    }))

    // 计算每个项目的总价
    processedItems.forEach((item: any) => updateItemTotal(item))
    displayedItems.value = processedItems

    // 填充表单数据
    Object.assign(form, {
      quotation_number: responseData.quotation_number || '',
      customer_id: responseData.customer_id,
      project_name: responseData.project_name || '',
      project_address: responseData.project_address || '',
      valid_until: responseData.valid_until,
      payment_terms: responseData.payment_terms || '',
      delivery_terms: responseData.delivery_terms || '',
      status: responseData.status,
      notes: responseData.notes || ''
    })

    // 确保form中不包含id字段（避免在保存时意外发送）
    if ('id' in form) {
      delete form.id
    }

    updateTotalAmount()

    // 如果有客户信息，添加到选项中
    if (responseData.customer && !customerOptions.value.some((opt: any) => opt.id === responseData.customer_id)) {
      customerOptions.value.unshift({
        id: responseData.customer_id,
        name: responseData.customer.name || `客户ID: ${responseData.customer_id}`
      })
    }

    ElMessage.success('报价单数据加载成功')

  } catch (error) {
    console.error('加载报价单失败:', error)
    ElMessage.error('加载报价单失败')
  } finally {
    loading.value = false
  }
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  const statusMap = {
    '待确认': 'warning',
    '已确认': 'success',
    '已拒绝': 'danger',
    '已过期': 'info'
  }
  return statusMap[status] || 'info'
}

// 初始化
onMounted(async () => {
  if (isEdit.value) {
    await loadQuotationData()
  } else {
    form.status = '待确认'

    // 检查是否从报价需求表创建
    if (requestId.value) {
      await loadFromRequest(requestId.value)
    }
  }
})
</script>

<style scoped>
.mb-20 {
  margin-bottom: 20px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-title {
  margin: 0;
  font-size: 18px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-10 {
  margin-top: 10px;
}

.flex-center {
  display: flex;
  justify-content: center;
}

/* 表格样式优化 */
.quotation-form :deep(.el-table) {
  .el-table__cell {
    padding: 8px 0;
  }
}

.quotation-form :deep(.el-input-number) {
  .el-input__inner {
    text-align: center;
  }
}

.quotation-form :deep(.el-input-number--small) {
  width: 100% !important;
}

.quotation-form :deep(.el-table .el-input) {
  .el-input__inner {
    border: 1px solid transparent;
    background: transparent;
  }

  .el-input__inner:hover {
    border-color: #c0c4cc;
  }

  .el-input__inner:focus {
    border-color: #409eff;
  }
}

/* 表格行点击样式 */
.quotation-form :deep(.el-table__body tr) {
  cursor: pointer;
}

.quotation-form :deep(.el-table__body tr:hover) {
  background-color: #f5f7fa;
}

.ml-5 {
  margin-left: 5px;
}

/* 已存在规格样式 */
.existing-spec-text {
  color: #909399;
}

.existing-spec-tag {
  font-size: 10px;
  height: 16px;
  line-height: 14px;
}

/* 产品搜索样式 */
.product-search {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-20 {
  margin-top: 20px;
}
</style>
