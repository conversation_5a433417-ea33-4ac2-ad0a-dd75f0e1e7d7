// 通用类型定义

// 通用API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean
  data: T
  message?: string
  code?: number
}

// 列表响应类型
export interface ListResponse<T> {
  list: T[]
  pagination?: {
    total: number
    page: number
    per_page: number
    pages?: number
    has_prev?: boolean
    has_next?: boolean
  }
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  per_page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 选项类型
export interface Option {
  label: string
  value: string | number
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
}

// 表单规则
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

// 搜索表单基础类型
export interface BaseSearchForm {
  [key: string]: any
}

// 状态类型
export type Status = 'active' | 'inactive' | 'pending' | 'completed' | 'cancelled'

// 操作类型
export type Action = 'create' | 'edit' | 'view' | 'delete'
