#!/usr/bin/env python3
"""
测试收款逻辑功能
验证新添加的客户余额管理、余额交易记录、对账单收款等功能
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import *
from datetime import datetime, date
from decimal import Decimal

def test_payment_logic():
    """测试收款逻辑功能"""
    app = create_app()
    
    with app.app_context():
        print("🧪 开始测试收款逻辑功能...")
        
        try:
            # 1. 测试客户余额管理
            print("\n1️⃣ 测试客户余额管理...")
            
            # 获取第一个客户
            customer = Customer.query.first()
            if not customer:
                print("   ❌ 没有找到客户数据")
                return False
            
            print(f"   📋 测试客户: {customer.name}")
            
            # 获取客户余额
            balance = CustomerBalance.query.filter_by(customer_id=customer.id).first()
            if not balance:
                print("   ❌ 客户余额记录不存在")
                return False
            
            print(f"   💰 初始余额: {balance.balance}")
            
            # 测试充值
            print("   💳 测试充值功能...")
            transaction = balance.add_balance(
                amount=Decimal('1000.00'),
                description='测试充值',
                reference_type='test',
                reference_id=1
            )
            db.session.commit()
            
            print(f"   ✅ 充值成功，新余额: {balance.balance}")
            print(f"   📝 交易记录ID: {transaction.id}")
            
            # 测试扣减
            print("   💸 测试扣减功能...")
            deduct_transaction = balance.deduct_balance(
                amount=Decimal('300.00'),
                description='测试扣减',
                reference_type='test',
                reference_id=2
            )
            db.session.commit()
            
            print(f"   ✅ 扣减成功，新余额: {balance.balance}")
            print(f"   📝 交易记录ID: {deduct_transaction.id}")
            
            # 2. 测试余额交易记录
            print("\n2️⃣ 测试余额交易记录...")
            
            transactions = BalanceTransaction.query.filter_by(customer_id=customer.id).all()
            print(f"   📊 客户交易记录数量: {len(transactions)}")
            
            for trans in transactions[-2:]:  # 显示最后两条记录
                print(f"   📝 {trans.transaction_type}: {trans.amount}, 余额: {trans.balance_before} → {trans.balance_after}")
            
            # 3. 测试对账单收款记录
            print("\n3️⃣ 测试对账单收款记录...")
            
            # 获取第一个对账单
            statement = Statement.query.first()
            if not statement:
                print("   ⚠️  没有找到对账单数据，跳过对账单收款测试")
            else:
                print(f"   📋 测试对账单: {statement.statement_number}")
                print(f"   💰 对账单金额: {statement.adjusted_total_amount}")
                print(f"   💳 已付金额: {statement.paid_amount}")
                print(f"   📊 付款状态: {statement.payment_status}")
                
                # 测试直接收款
                print("   💳 测试直接收款...")
                payment = StatementPayment.create_direct_payment(
                    statement_id=statement.id,
                    amount=Decimal('500.00'),
                    payment_method='bank_transfer',
                    reference_number='TEST001',
                    notes='测试直接收款'
                )
                db.session.add(payment)
                
                # 更新对账单付款状态
                statement.add_payment(500.00)
                db.session.commit()
                
                print(f"   ✅ 直接收款成功，收款记录ID: {payment.id}")
                print(f"   📊 对账单新状态: {statement.payment_status}")
                
                # 测试余额支付
                if balance.balance >= 200:
                    print("   💰 测试余额支付...")
                    balance_payment = StatementPayment.create_balance_payment(
                        statement_id=statement.id,
                        amount=Decimal('200.00'),
                        customer_id=customer.id,
                        notes='测试余额支付'
                    )
                    db.session.add(balance_payment)
                    
                    # 更新对账单付款状态
                    statement.add_payment(200.00)
                    db.session.commit()
                    
                    print(f"   ✅ 余额支付成功，收款记录ID: {balance_payment.id}")
                    print(f"   💰 客户新余额: {balance.balance}")
                    print(f"   📊 对账单新状态: {statement.payment_status}")
            
            # 4. 测试结清状态
            print("\n4️⃣ 测试结清状态...")
            
            # 获取第一个发货单
            delivery_note = DeliveryNote.query.first()
            if delivery_note:
                print(f"   📋 测试发货单: {delivery_note.delivery_number}")
                print(f"   📊 结清状态: {delivery_note.settlement_status}")
                
                # 测试结清
                delivery_note.settle()
                db.session.commit()
                
                print(f"   ✅ 发货单结清成功")
                print(f"   📊 新结清状态: {delivery_note.settlement_status}")
                print(f"   📅 结清日期: {delivery_note.settlement_date}")
            
            # 获取第一个退货单
            return_order = ReturnOrder.query.first()
            if return_order:
                print(f"   📋 测试退货单: {return_order.return_number}")
                print(f"   📊 结清状态: {return_order.settlement_status}")
                
                # 测试结清
                return_order.settle()
                db.session.commit()
                
                print(f"   ✅ 退货单结清成功")
                print(f"   📊 新结清状态: {return_order.settlement_status}")
                print(f"   📅 结清日期: {return_order.settlement_date}")
            
            # 5. 验证数据完整性
            print("\n5️⃣ 验证数据完整性...")
            
            # 检查客户余额表
            balance_count = CustomerBalance.query.count()
            customer_count = Customer.query.count()
            print(f"   👥 客户数量: {customer_count}")
            print(f"   💰 余额记录数量: {balance_count}")
            
            if balance_count == customer_count:
                print("   ✅ 每个客户都有对应的余额记录")
            else:
                print("   ⚠️  客户余额记录数量不匹配")
            
            # 检查交易记录
            transaction_count = BalanceTransaction.query.count()
            print(f"   📝 余额交易记录数量: {transaction_count}")
            
            # 检查收款记录
            payment_count = StatementPayment.query.count()
            print(f"   💳 对账单收款记录数量: {payment_count}")
            
            print("\n🎉 收款逻辑功能测试完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()
            return False

def show_summary():
    """显示功能总结"""
    print("\n" + "="*60)
    print("📋 新增收款逻辑功能总结")
    print("="*60)
    print("✅ 客户余额管理 (CustomerBalance)")
    print("   - 支持预付款充值")
    print("   - 支持余额扣减")
    print("   - 支持余额冻结/解冻")
    print("")
    print("✅ 余额交易记录 (BalanceTransaction)")
    print("   - 记录所有余额变动")
    print("   - 支持关联业务记录")
    print("   - 完整的交易追踪")
    print("")
    print("✅ 对账单收款记录 (StatementPayment)")
    print("   - 支持直接收款")
    print("   - 支持余额支付")
    print("   - 完整的收款追踪")
    print("")
    print("✅ 结清状态管理")
    print("   - 发货单结清状态")
    print("   - 退货单结清状态")
    print("   - 对账单付款状态")
    print("")
    print("🔄 完整的收款流程支持")
    print("   - 预付款 → 客户余额")
    print("   - 对账单收款 → 直接/余额支付")
    print("   - 对账单完结 → 自动结清相关单据")
    print("="*60)

if __name__ == '__main__':
    success = test_payment_logic()
    show_summary()
    
    if success:
        print("\n✅ 所有测试通过！收款逻辑功能已成功实现。")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
        sys.exit(1)
