<template>
  <div class="company-info-container">
    <el-card class="header-card">
      <div class="flex-between">
        <h2 class="form-title">企业信息</h2>
        <div>
          <el-button v-if="!isCompanyInfoEditing" type="primary" @click="isCompanyInfoEditing = true">编辑</el-button>
          <el-button v-if="!isCompanyInfoEditing" type="info" @click="fetchCompanyInfo">刷新</el-button>
          <el-button v-if="isCompanyInfoEditing" type="success" @click="saveCompanyInfo" :loading="saving.company">保存</el-button>
          <el-button v-if="isCompanyInfoEditing" @click="cancelCompanyInfoEdit">取消</el-button>
        </div>
      </div>
    </el-card>

    <el-card v-loading="loading.company" class="form-card">
      <template #header>
        <span>企业详细信息</span>
      </template>
          <el-form
            ref="companyFormRef"
            :model="companyForm"
            :rules="companyRules"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="企业名称" prop="name">
                  <el-input v-model="companyForm.name" :disabled="!isCompanyInfoEditing" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码" prop="tax_id">
                  <el-input v-model="companyForm.tax_id" :disabled="!isCompanyInfoEditing" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系人" prop="contact">
                  <el-input v-model="companyForm.contact" :disabled="!isCompanyInfoEditing" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="companyForm.phone" :disabled="!isCompanyInfoEditing" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="电子邮箱" prop="email">
                  <el-input v-model="companyForm.email" :disabled="!isCompanyInfoEditing" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="传真" prop="fax">
                  <el-input v-model="companyForm.fax" :disabled="!isCompanyInfoEditing" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="公司地址" prop="address">
                  <el-input v-model="companyForm.address" :disabled="!isCompanyInfoEditing" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="公司Logo" prop="logo">
                  <!-- 编辑模式：显示上传组件 -->
                  <el-upload
                    v-if="isCompanyInfoEditing"
                    class="avatar-uploader"
                    action="#"
                    :show-file-list="false"
                    :http-request="handleLogoUpload"
                    :before-upload="beforeImageUpload"
                  >
                    <el-tooltip content="点击更换Logo" placement="top">
                      <img v-if="companyForm.logo" :src="getImageUrl(companyForm.logo)" class="avatar" />
                      <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                    </el-tooltip>
                  </el-upload>
                  <!-- 查看模式：显示可预览的图片 -->
                  <div v-else class="image-preview-container">
                    <el-tooltip content="点击查看大图" placement="top" v-if="companyForm.logo">
                      <img
                        :src="getImageUrl(companyForm.logo)"
                        class="avatar preview-image"
                        @click="previewImage(getImageUrl(companyForm.logo), '公司Logo')"
                      />
                    </el-tooltip>
                    <div v-else class="no-image">
                      <el-icon class="avatar-uploader-icon"><Picture /></el-icon>
                      <div class="no-image-text">暂无Logo</div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="营业执照" prop="license_image">
                  <!-- 编辑模式：显示上传组件 -->
                  <el-upload
                    v-if="isCompanyInfoEditing"
                    class="avatar-uploader"
                    action="#"
                    :show-file-list="false"
                    :http-request="handleLicenseUpload"
                    :before-upload="beforeImageUpload"
                  >
                    <el-tooltip content="点击上传或更换营业执照" placement="top">
                      <img v-if="companyForm.license_image" :src="getImageUrl(companyForm.license_image)" class="avatar" />
                      <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                    </el-tooltip>
                  </el-upload>
                  <!-- 查看模式：显示可预览的图片 -->
                  <div v-else class="image-preview-container">
                    <el-tooltip content="点击查看大图" placement="top" v-if="companyForm.license_image">
                      <img
                        :src="getImageUrl(companyForm.license_image)"
                        class="avatar preview-image"
                        @click="previewImage(getImageUrl(companyForm.license_image), '营业执照')"
                      />
                    </el-tooltip>
                    <div v-else class="no-image">
                      <el-icon class="avatar-uploader-icon"><Picture /></el-icon>
                      <div class="no-image-text">暂无营业执照</div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
      </el-form>
    </el-card>
  </div>

  <!-- 图片预览模态框 -->
  <el-dialog
    v-model="imagePreviewVisible"
    :title="imagePreviewTitle"
    width="80%"
    center
    :close-on-click-modal="true"
    :close-on-press-escape="true"
  >
    <div class="image-preview-dialog">
      <img :src="imagePreviewUrl" alt="预览图片" class="preview-dialog-image" />
    </div>
    <template #footer>
      <el-button @click="imagePreviewVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Picture } from '@element-plus/icons-vue'
import { systemSettingsApi } from '@/api/system'


const loading = reactive({ company: false })
const saving = reactive({ company: false })

// --- 企业信息 ---
const isCompanyInfoEditing = ref(false)
const companyFormRef = ref(null)
const companyForm = reactive({
  id: null,
  name: '',
  address: '',
  contact: '',
  phone: '',
  email: '',
  fax: '',
  tax_id: '',
  logo: null,
  license_image: null
})
const companyInfoBackup = ref(null)

// 图片预览相关状态
const imagePreviewVisible = ref(false)
const imagePreviewUrl = ref('')
const imagePreviewTitle = ref('')

const companyRules = {
  name: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  address: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ],
  contact: [
    { max: 50, message: '长度不能超过 50 个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/, message: '请输入有效的电话号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  tax_id: [
    { pattern: /^[A-Z0-9]{15,20}$/, message: '请输入有效的统一社会信用代码', trigger: 'blur' }
  ]
}

const getImageUrl = (url: string) => {
  if (!url) return ''
  const u = String(url)
  // 绝对地址或本地blob，直接使用
  if (u.startsWith('http') || u.startsWith('blob')) return u
  // 已经是API路径，直接用，交给前端代理
  if (u.startsWith('/api/')) return u
  // 旧数据若是以 /uploads 开头，补上 /api/v1 前缀以走代理
  if (u.startsWith('/uploads/')) return `/api/v1${u}`
  // 其他相对路径，归一到 /api/v1/uploads
  return u.startsWith('/') ? `/api/v1${u}` : `/api/v1/${u}`
}

const fetchCompanyInfo = async () => {
  loading.company = true
  try {
    const response = await systemSettingsApi.getCompanyInfo() as any
    console.log('获取到的企业信息:', response)
    
    // API response might be wrapped in data property
    const companyData = response.data || response
    
    // 将API返回的数据合并到表单中
    Object.keys(companyForm).forEach(key => {
      if (key in companyData) {
        companyForm[key] = companyData[key]
      }
    })
    
    // 备份初始数据，用于取消编辑
    companyInfoBackup.value = JSON.parse(JSON.stringify(companyForm))
    
    // 保存到localStorage作为备份
    localStorage.setItem('companyInfo', JSON.stringify(companyForm))
    console.log('企业信息已保存到localStorage')
    
    // 加载成功，不重复打扰用户
  } catch (error) {
    console.error('获取企业信息失败:', error)
    
    // 尝试从localStorage恢复
    const savedInfo = localStorage.getItem('companyInfo')
    if (savedInfo) {
      try {
        const parsedInfo = JSON.parse(savedInfo)
        Object.keys(companyForm).forEach(key => {
          if (key in parsedInfo) {
            companyForm[key] = parsedInfo[key]
          }
        })
        companyInfoBackup.value = JSON.parse(JSON.stringify(companyForm))
        ElMessage.info('已从本地缓存恢复企业信息')
      } catch (e) {
        console.error('解析本地缓存的企业信息失败:', e)
        ElMessage.error('获取企业信息失败，且无法从本地缓存恢复')
      }
    } else {
      // 使用模拟数据
      Object.assign(companyForm, {
        name: '某某工程物资有限公司',
        address: '北京市海淀区中关村大街1号',
        contact: '张经理',
        phone: '010-12345678',
        email: '<EMAIL>',
        fax: '010-12345679',
        tax_id: '91110000123456789X'
      })
      companyInfoBackup.value = JSON.parse(JSON.stringify(companyForm))
      ElMessage.warning('获取企业信息失败，使用模拟数据')
    }
  } finally {
    loading.company = false
  }
}

const saveCompanyInfo = async () => {
  if (!companyFormRef.value) return
  
  await companyFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      saving.company = true
      try {
        // 注意：图片上传已通过 handleUpload 单独完成，这里仅提交URL字符串
        const payload: any = {}
        Object.keys(companyForm).forEach(key => {
          // 过滤掉只读字段（id、created_at、updated_at）
          if (['id', 'created_at', 'updated_at'].includes(key)) return

          const val = (companyForm as any)[key]
          if (val !== undefined && val !== null && val !== '') {
            // 若误传了File对象，忽略（应先通过上传接口换成URL）
            if (val instanceof File) return
            payload[key] = val
          }
        })

        const response = await systemSettingsApi.updateCompanyInfo(payload)
        console.log('保存企业信息成功:', response)

        // 更新本地备份
        companyInfoBackup.value = JSON.parse(JSON.stringify(companyForm))

        // 更新localStorage备份
        localStorage.setItem('companyInfo', JSON.stringify(companyForm))

        ElMessage.success('企业信息保存成功')
        isCompanyInfoEditing.value = false
      } catch (error) {
        console.error('保存企业信息失败:', error)
        ElMessage.error('保存企业信息失败')
      } finally {
        saving.company = false
      }
    } else {
      ElMessage.warning('请正确填写企业信息')
      return false
    }
  })
}

const cancelCompanyInfoEdit = () => {
  if (companyInfoBackup.value) {
    Object.keys(companyForm).forEach(key => {
      if (key in companyInfoBackup.value) {
        companyForm[key] = companyInfoBackup.value[key]
      }
    })
  }
  isCompanyInfoEditing.value = false
}

// --- 图片上传 ---
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const handleUpload = async (options: any, field: string) => {
  const { file } = options
  saving.company = true
  try {
    const response = await systemSettingsApi.uploadFile(file) as any
    // 响应拦截器已经提取了 data.data，所以直接访问 response.url
    if (response && response.url) {
      companyForm[field] = response.url
      ElMessage.success('上传成功')
    } else {
      console.error('上传响应格式:', response)
      ElMessage.error('上传失败: 服务器响应格式不正确')
    }
  } catch (error: any) {
    console.error('上传错误:', error)
    ElMessage.error(`上传失败: ${error.message || '未知错误'}`)
  } finally {
    saving.company = false
  }
}

const handleLogoUpload = (options: any) => {
  handleUpload(options, 'logo')
}

const handleLicenseUpload = (options: any) => {
  handleUpload(options, 'license_image')
}

// 图片预览函数
const previewImage = (imageUrl: string, title: string) => {
  if (!imageUrl) return
  imagePreviewUrl.value = imageUrl
  imagePreviewTitle.value = title
  imagePreviewVisible.value = true
}

onMounted(() => {
  fetchCompanyInfo()
})
</script>

<style scoped>
.company-info-container {
  padding: 20px;
}
.header-card {
  margin-bottom: 20px;
}
.form-card {
  margin-top: 0;
}
.form-title {
  margin: 0;
  font-size: 20px;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

/* 图片预览相关样式 */
.image-preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 178px;
  height: 178px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
}

.preview-image {
  width: 178px;
  height: 178px;
  object-fit: cover;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 6px;
}

.preview-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #8c939d;
}

.no-image-text {
  margin-top: 8px;
  font-size: 12px;
}

.image-preview-dialog {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.preview-dialog-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
</style>
