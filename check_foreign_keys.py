#!/usr/bin/env python3
"""
检查数据库外键约束的脚本
"""
import sqlite3
import os

def check_foreign_keys():
    """检查数据库外键约束"""
    # 数据库文件路径
    db_path = r'D:\code\EMB-new\backend\instance\project.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查数据库外键约束")
        print("=" * 50)
        
        # 1. 检查外键是否启用
        cursor.execute("PRAGMA foreign_keys")
        fk_enabled = cursor.fetchone()[0]
        print(f"外键约束状态: {'启用' if fk_enabled else '禁用'}")
        
        # 2. 检查客户相关表的外键约束
        tables_to_check = [
            'customer_bank_accounts',
            'customer_delivery_addresses', 
            'quotations',
            'orders',
            'quotation_requests'
        ]
        
        for table in tables_to_check:
            print(f"\n📋 表: {table}")
            try:
                cursor.execute(f"PRAGMA foreign_key_list({table})")
                foreign_keys = cursor.fetchall()
                
                if foreign_keys:
                    for fk in foreign_keys:
                        print(f"  外键: {fk[3]} -> {fk[2]}.{fk[4]} (ON DELETE: {fk[6]}, ON UPDATE: {fk[5]})")
                else:
                    print("  ❌ 没有外键约束")
            except Exception as e:
                print(f"  ❌ 检查失败: {str(e)}")
        
        # 3. 检查客户ID 9的具体关联数据
        print(f"\n🔍 客户ID 9的详细关联数据:")
        
        # 银行账户
        cursor.execute("SELECT id, bank_name, account_name FROM customer_bank_accounts WHERE customer_id = 9")
        bank_accounts = cursor.fetchall()
        print(f"银行账户 ({len(bank_accounts)} 条):")
        for account in bank_accounts:
            print(f"  - ID: {account[0]}, 银行: {account[1]}, 账户名: {account[2]}")
        
        # 收货地址
        cursor.execute("SELECT id, province, city, contact_person FROM customer_delivery_addresses WHERE customer_id = 9")
        addresses = cursor.fetchall()
        print(f"收货地址 ({len(addresses)} 条):")
        for addr in addresses:
            print(f"  - ID: {addr[0]}, 地址: {addr[1]}{addr[2]}, 联系人: {addr[3]}")
        
        # 4. 尝试手动删除测试
        print(f"\n🧪 尝试手动删除客户ID 9:")
        
        try:
            # 开启外键约束
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # 尝试删除客户
            cursor.execute("DELETE FROM customers WHERE id = 9")
            print("✅ 删除成功（但未提交）")
            
            # 回滚事务
            conn.rollback()
            print("🔄 已回滚事务")
            
        except Exception as e:
            print(f"❌ 删除失败: {str(e)}")
            conn.rollback()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询错误: {str(e)}")

if __name__ == "__main__":
    check_foreign_keys()
