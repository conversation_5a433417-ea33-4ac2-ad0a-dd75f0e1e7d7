#!/usr/bin/env python3
"""
添加对账单退款表 (statement_refunds)
创建时间: 2025-07-17
"""

import sqlite3
import os
import sys
from datetime import datetime

def add_statement_refunds_table():
    """添加对账单退款表"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("开始添加对账单退款表...")
        
        # 1. 检查表是否已存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='statement_refunds'
        """)
        
        if cursor.fetchone():
            print("对账单退款表已存在，跳过创建")
            return True
        
        # 2. 创建对账单退款表
        print("创建 statement_refunds 表...")
        cursor.execute("""
            CREATE TABLE statement_refunds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                statement_id INTEGER NOT NULL,
                refund_number VARCHAR(50) NOT NULL UNIQUE,
                refund_date DATE NOT NULL,
                amount DECIMAL(12,2) NOT NULL,
                refund_method VARCHAR(50) NOT NULL,
                refund_target VARCHAR(20) NOT NULL DEFAULT 'direct',
                reference_number VARCHAR(100),
                bank_account VARCHAR(200),
                balance_transaction_id INTEGER,
                notes TEXT,
                voucher_files TEXT,
                status VARCHAR(20) NOT NULL DEFAULT '待处理',
                created_by VARCHAR(50),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (statement_id) REFERENCES statements (id),
                FOREIGN KEY (balance_transaction_id) REFERENCES balance_transactions (id),
                CHECK (refund_target IN ('direct', 'balance')),
                CHECK (status IN ('待处理', '已退款', '已取消')),
                CHECK (amount > 0)
            )
        """)
        
        # 3. 创建索引
        print("创建索引...")
        
        # 对账单ID索引
        cursor.execute("""
            CREATE INDEX idx_statement_refunds_statement_id 
            ON statement_refunds(statement_id)
        """)
        
        # 退款单号索引
        cursor.execute("""
            CREATE INDEX idx_statement_refunds_refund_number 
            ON statement_refunds(refund_number)
        """)
        
        # 状态索引
        cursor.execute("""
            CREATE INDEX idx_statement_refunds_status 
            ON statement_refunds(status)
        """)
        
        # 退款日期索引
        cursor.execute("""
            CREATE INDEX idx_statement_refunds_refund_date 
            ON statement_refunds(refund_date)
        """)
        
        # 创建时间索引
        cursor.execute("""
            CREATE INDEX idx_statement_refunds_created_at 
            ON statement_refunds(created_at)
        """)
        
        # 4. 提交更改
        conn.commit()
        print("对账单退款表创建成功！")
        
        # 5. 验证表结构
        cursor.execute("PRAGMA table_info(statement_refunds)")
        columns = cursor.fetchall()
        print(f"表结构验证: statement_refunds 表包含 {len(columns)} 个字段")
        
        return True
        
    except sqlite3.Error as e:
        print(f"数据库操作失败: {e}")
        if conn:
            conn.rollback()
        return False
    except Exception as e:
        print(f"执行失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("=" * 50)
    print("EMB系统 - 添加对账单退款表")
    print("=" * 50)
    
    success = add_statement_refunds_table()
    
    if success:
        print("\n✅ 对账单退款表添加完成！")
        print("\n新增功能:")
        print("- 对账单退款记录管理")
        print("- 支持直接退款和余额退款")
        print("- 退款状态跟踪")
        print("- 退款凭据管理")
    else:
        print("\n❌ 对账单退款表添加失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
