import { http } from './request'
import request from './request'
import type {
  Product,
  ProductCategory,
  Brand,
  PaginatedResponse,
  PaginationParams,
} from '@/types/api'

// 产品管理API
export const productApi = {
  // 获取产品列表（支持可选的请求配置，如 { showLoading: false } 以避免全局loading闪烁）
  getList: (params?: PaginationParams, config?: any) => {
    return http.get<PaginatedResponse<Product>>('/products', params, config)
  },

  // 获取产品详情
  getById: (id: number) => {
    return http.get<Product>(`/products/${id}`)
  },

  // 创建产品
  create: (data: Partial<Product>) => {
    return http.post<Product>('/products', data)
  },

  // 更新产品
  update: (id: number, data: Partial<Product>) => {
    return http.put<Product>(`/products/${id}`, data)
  },

  // 删除产品
  delete: (id: number) => {
    return http.delete(`/products/${id}`)
  },

  // 批量删除产品
  batchDelete: (ids: number[]) => {
    return http.post('/products/batch-delete', { ids })
  },

  // 产品规格管理（通过产品更新API）
  addSpecification: async (productId: number, data: any) => {
    // 先获取产品详情
    const product = await http.get(`/products/${productId}`)
    const specifications = product.specifications || []

    // 处理新规格数据格式
    const newSpec = {
      specification: data.specification,
      cost_price: String(data.cost_price || 0), // 转换为字符串
      suggested_price: String(data.suggested_price || 0), // 转换为字符串
      min_price: data.min_price ? String(data.min_price) : null,
      max_price: data.max_price ? String(data.max_price) : null,
      tax_rate: data.tax_rate || 13.0,
      is_default: data.is_default || false,
      notes: data.notes || null
    }

    // 清理现有规格的只读字段
    const cleanedSpecs = specifications.map(spec => ({
      specification: spec.specification,
      cost_price: spec.cost_price,
      suggested_price: spec.suggested_price,
      min_price: spec.min_price,
      max_price: spec.max_price,
      tax_rate: spec.tax_rate,
      is_default: spec.is_default,
      notes: spec.notes
    }))

    // 添加新规格
    cleanedSpecs.push(newSpec)

    // 更新产品（只更新规格字段）
    const updateData: any = {
      name: product.name,
      model: product.model,
      unit: product.unit,
      category_id: product.category_id,
      status: product.status,
      specifications: cleanedSpecs
    }

    // 只添加非空字段
    if (product.brand_id) updateData.brand_id = product.brand_id
    if (product.image) updateData.image = product.image
    if (product.description) updateData.description = product.description
    if (product.notes) updateData.notes = product.notes

    console.log('发送的更新数据:', updateData)
    return http.put<Product>(`/products/${productId}`, updateData)
  },

  updateSpecification: async (productId: number, specId: number, data: any) => {
    // 先获取产品详情
    const product = await http.get(`/products/${productId}`)
    const specifications = product.specifications || []

    // 清理现有规格的只读字段
    const cleanedSpecs = specifications.map(spec => ({
      specification: spec.specification,
      cost_price: spec.cost_price,
      suggested_price: spec.suggested_price,
      min_price: spec.min_price,
      max_price: spec.max_price,
      tax_rate: spec.tax_rate,
      is_default: spec.is_default,
      notes: spec.notes
    }))

    // 更新指定规格
    const specIndex = cleanedSpecs.findIndex((spec: any, index: number) => {
      // 如果规格有ID，按ID匹配；否则按索引匹配
      return specifications[index].id === specId
    })
    if (specIndex !== -1) {
      // 处理更新数据格式
      cleanedSpecs[specIndex] = {
        specification: data.specification !== undefined ? data.specification : cleanedSpecs[specIndex].specification,
        cost_price: data.cost_price !== undefined ? (data.cost_price ? String(data.cost_price) : null) : cleanedSpecs[specIndex].cost_price,
        suggested_price: data.suggested_price !== undefined ? (data.suggested_price ? String(data.suggested_price) : null) : cleanedSpecs[specIndex].suggested_price,
        min_price: data.min_price !== undefined ? (data.min_price ? String(data.min_price) : null) : cleanedSpecs[specIndex].min_price,
        max_price: data.max_price !== undefined ? (data.max_price ? String(data.max_price) : null) : cleanedSpecs[specIndex].max_price,
        tax_rate: data.tax_rate !== undefined ? data.tax_rate : cleanedSpecs[specIndex].tax_rate,
        is_default: data.is_default !== undefined ? data.is_default : cleanedSpecs[specIndex].is_default,
        notes: data.notes !== undefined ? data.notes : cleanedSpecs[specIndex].notes
      }
    }

    // 更新产品（只更新规格字段）
    const updateData: any = {
      name: product.name,
      model: product.model,
      unit: product.unit,
      category_id: product.category_id,
      status: product.status,
      specifications: cleanedSpecs
    }

    // 只添加非空字段
    if (product.brand_id) updateData.brand_id = product.brand_id
    if (product.image) updateData.image = product.image
    if (product.description) updateData.description = product.description
    if (product.notes) updateData.notes = product.notes

    return http.put<Product>(`/products/${productId}`, updateData)
  },

  deleteSpecification: async (productId: number, specId: number) => {
    // 先获取产品详情
    const product = await http.get(`/products/${productId}`)
    const specifications = product.specifications || []

    // 清理现有规格的只读字段并删除指定规格
    const filteredSpecs = specifications
      .filter((spec: any) => spec.id !== specId)
      .map(spec => ({
        specification: spec.specification,
        cost_price: spec.cost_price,
        suggested_price: spec.suggested_price,
        min_price: spec.min_price,
        max_price: spec.max_price,
        tax_rate: spec.tax_rate,
        is_default: spec.is_default,
        notes: spec.notes
      }))

    // 更新产品（只更新规格字段）
    const updateData: any = {
      name: product.name,
      model: product.model,
      unit: product.unit,
      category_id: product.category_id,
      status: product.status,
      specifications: filteredSpecs
    }

    // 只添加非空字段
    if (product.brand_id) updateData.brand_id = product.brand_id
    if (product.image) updateData.image = product.image
    if (product.description) updateData.description = product.description
    if (product.notes) updateData.notes = product.notes

    return http.put<Product>(`/products/${productId}`, updateData)
  },

  // 上传产品图片
  uploadImage: (file: File) => {
    const formData = new FormData()
    formData.append('image', file)
    return http.upload<{ url: string }>('/products/upload-image', formData)
  },

  // 导出产品数据
  export: (params?: PaginationParams) => {
    return request.get('/products/export', {
      params,
      responseType: 'blob'
    })
  },

  // 导入产品
  import: (formData: FormData) => {
    return http.post('/products/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 预览导入产品
  previewImport: (formData: FormData) => {
    return http.post('/products/preview-import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 从预览数据导入产品
  importFromPreview: (data: any) => {
    return http.post('/products/import', data, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
  },

  // 下载导入模板
  downloadTemplate: () => {
    return request.get('/products/export-template', {
      responseType: 'blob'
    })
  },

  // 批量导出产品
  batchExport: (ids: number[]) => {
    return request.post('/products/batch-export', { ids }, {
      responseType: 'blob'
    })
  },


}

// 产品分类API
export const categoryApi = {
  // 获取分类列表
  getList: (params?: PaginationParams) => {
    return http.get<PaginatedResponse<ProductCategory>>('/products/categories', params)
  },

  // 获取所有分类（不分页）
  getAll: () => {
    return http.get<ProductCategory[]>('/products/categories/all')
  },

  // 获取分类详情
  getById: (id: number) => {
    return http.get<ProductCategory>(`/products/categories/${id}`)
  },

  // 创建分类
  create: (data: Partial<ProductCategory>) => {
    return http.post<ProductCategory>('/products/categories', data)
  },

  // 更新分类
  update: (id: number, data: Partial<ProductCategory>) => {
    return http.put<ProductCategory>(`/products/categories/${id}`, data)
  },

  // 删除分类
  delete: (id: number) => {
    return http.delete(`/products/categories/${id}`)
  },
}

// 品牌管理API
export const brandApi = {
  // 获取品牌列表
  getList: (params?: PaginationParams) => {
    return http.get<PaginatedResponse<Brand>>('/products/brands', params)
  },

  // 获取所有品牌（不分页）
  getAll: () => {
    return http.get<Brand[]>('/products/brands/all')
  },

  // 获取品牌详情
  getById: (id: number) => {
    return http.get<Brand>(`/products/brands/${id}`)
  },

  // 创建品牌
  create: (data: Partial<Brand>) => {
    return http.post<Brand>('/products/brands', data)
  },

  // 更新品牌
  update: (id: number, data: Partial<Brand>) => {
    return http.put<Brand>(`/products/brands/${id}`, data)
  },

  // 删除品牌
  delete: (id: number) => {
    return http.delete(`/products/brands/${id}`)
  },
}
