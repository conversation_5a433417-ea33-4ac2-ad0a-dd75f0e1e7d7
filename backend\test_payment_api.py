#!/usr/bin/env python3
"""
测试收款API接口
验证新添加的收款逻辑API功能
"""
import os
import sys
import requests
import json
from datetime import datetime, date
from decimal import Decimal

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# API基础URL
BASE_URL = 'http://localhost:5001/api/v1'

def test_api_endpoints():
    """测试收款API接口"""
    print("🧪 开始测试收款API接口...")
    
    try:
        # 1. 测试获取客户余额列表
        print("\n1️⃣ 测试获取客户余额列表...")
        response = requests.get(f'{BASE_URL}/payments/customer-balances')
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 获取客户余额列表成功")
            print(f"   📊 返回数据数量: {len(data.get('data', []))}")
            
            if data.get('data'):
                first_balance = data['data'][0]
                customer_id = first_balance['customer_id']
                print(f"   👤 第一个客户: {first_balance['customer_name']}")
                print(f"   💰 余额: {first_balance['balance']}")
                
                # 2. 测试获取指定客户余额详情
                print(f"\n2️⃣ 测试获取客户 {customer_id} 余额详情...")
                response = requests.get(f'{BASE_URL}/payments/customer-balances/{customer_id}')
                
                if response.status_code == 200:
                    detail_data = response.json()
                    print(f"   ✅ 获取客户余额详情成功")
                    print(f"   💰 余额: {detail_data['data']['balance']}")
                    print(f"   🔒 冻结余额: {detail_data['data']['frozen_balance']}")
                else:
                    print(f"   ❌ 获取客户余额详情失败: {response.status_code}")
                
                # 3. 测试余额充值
                print(f"\n3️⃣ 测试客户 {customer_id} 余额充值...")
                charge_data = {
                    'customer_id': customer_id,
                    'amount': 500.00,
                    'description': 'API测试充值',
                    'reference_type': 'test'
                }
                
                response = requests.post(
                    f'{BASE_URL}/payments/add-balance',
                    json=charge_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    charge_result = response.json()
                    print(f"   ✅ 余额充值成功")
                    print(f"   💰 新余额: {charge_result['data']['new_balance']}")
                    print(f"   📝 交易ID: {charge_result['data']['transaction_id']}")
                else:
                    print(f"   ❌ 余额充值失败: {response.status_code}")
                    print(f"   📄 错误信息: {response.text}")
            
        else:
            print(f"   ❌ 获取客户余额列表失败: {response.status_code}")
            print(f"   📄 错误信息: {response.text}")
        
        # 4. 测试获取余额交易记录列表
        print("\n4️⃣ 测试获取余额交易记录列表...")
        response = requests.get(f'{BASE_URL}/payments/balance-transactions')
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 获取余额交易记录列表成功")
            print(f"   📊 返回数据数量: {len(data.get('data', []))}")
            
            if data.get('data'):
                latest_transaction = data['data'][0]
                print(f"   📝 最新交易: {latest_transaction['transaction_type']} {latest_transaction['amount']}")
                print(f"   👤 客户: {latest_transaction['customer_name']}")
        else:
            print(f"   ❌ 获取余额交易记录列表失败: {response.status_code}")
        
        # 5. 测试获取对账单收款记录列表
        print("\n5️⃣ 测试获取对账单收款记录列表...")
        response = requests.get(f'{BASE_URL}/payments/statement-payments')
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 获取对账单收款记录列表成功")
            print(f"   📊 返回数据数量: {len(data.get('data', []))}")
            
            if data.get('data'):
                latest_payment = data['data'][0]
                print(f"   💳 最新收款: {latest_payment['amount']} ({latest_payment['payment_source']})")
                print(f"   📋 对账单: {latest_payment['statement_number']}")
        else:
            print(f"   ❌ 获取对账单收款记录列表失败: {response.status_code}")
        
        # 6. 测试获取收款统计数据
        print("\n6️⃣ 测试获取收款统计数据...")
        response = requests.get(f'{BASE_URL}/payments/payment-stats')
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 获取收款统计数据成功")
            stats = data['data']
            print(f"   💳 直接收款: {stats['payment_stats']['direct_payment_count']}笔, {stats['payment_stats']['direct_payment_amount']}元")
            print(f"   💰 余额支付: {stats['payment_stats']['balance_payment_count']}笔, {stats['payment_stats']['balance_payment_amount']}元")
            print(f"   🔄 余额充值: {stats['balance_stats']['recharge_count']}笔, {stats['balance_stats']['total_recharge_amount']}元")
            print(f"   💼 客户总余额: {stats['balance_stats']['total_customer_balance']}元")
        else:
            print(f"   ❌ 获取收款统计数据失败: {response.status_code}")
        
        print("\n🎉 收款API接口测试完成！")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保后端服务正在运行")
        print("   启动命令: cd backend && python run.py")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_api_summary():
    """显示API接口总结"""
    print("\n" + "="*60)
    print("📋 收款管理API接口总结")
    print("="*60)
    print("✅ 客户余额管理")
    print("   GET  /api/v1/payments/customer-balances - 获取客户余额列表")
    print("   GET  /api/v1/payments/customer-balances/{id} - 获取客户余额详情")
    print("   POST /api/v1/payments/add-balance - 客户余额充值")
    print("")
    print("✅ 余额交易记录")
    print("   GET  /api/v1/payments/balance-transactions - 获取余额交易记录列表")
    print("")
    print("✅ 对账单收款管理")
    print("   GET  /api/v1/payments/statement-payments - 获取对账单收款记录列表")
    print("   POST /api/v1/payments/direct-payment - 创建直接收款记录")
    print("   POST /api/v1/payments/balance-payment - 创建余额支付记录")
    print("")
    print("✅ 对账单结清管理")
    print("   POST /api/v1/payments/settle-statement/{id} - 结清对账单")
    print("")
    print("✅ 统计分析")
    print("   GET  /api/v1/payments/payment-stats - 获取收款统计数据")
    print("")
    print("🔗 API文档地址: http://localhost:5001/docs/")
    print("🔧 API基础路径: /api/v1/payments")
    print("="*60)

if __name__ == '__main__':
    success = test_api_endpoints()
    show_api_summary()
    
    if success:
        print("\n✅ API接口测试完成！收款管理API已成功实现。")
        print("\n💡 提示:")
        print("   - 可以通过 http://localhost:5001/docs/ 查看完整的API文档")
        print("   - 所有API都支持分页、筛选和排序功能")
        print("   - API返回标准化的JSON响应格式")
    else:
        print("\n❌ API接口测试失败，请检查服务器状态。")
        sys.exit(1)
