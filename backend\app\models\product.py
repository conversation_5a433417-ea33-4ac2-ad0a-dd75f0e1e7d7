"""
产品相关模型
包括产品基本信息、分类、规格、属性、图片
"""
from app.models.base import BaseModel
from app import db
from typing import Optional
from sqlalchemy import UniqueConstraint


class Product(BaseModel):
    """产品表模型"""
    __tablename__ = 'products'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False, comment='产品名称')
    model = db.Column(db.String(50), nullable=False, comment='产品型号')
    unit = db.Column(db.String(20), nullable=False, comment='单位')
    category_id = db.Column(db.Integer, db.ForeignKey('product_categories.id'), nullable=False, comment='类别ID')
    brand_id = db.Column(db.Integer, db.ForeignKey('brands.id'), nullable=True, comment='品牌ID')
    image = db.Column(db.String(255), nullable=True, comment='产品图片URL')
    description = db.Column(db.Text, nullable=True, comment='产品描述')
    notes = db.Column(db.Text, nullable=True, comment='备注')
    status = db.Column(db.String(20), nullable=False, default='正常', comment='状态')

    # 关联关系
    category = db.relationship('ProductCategory', back_populates='products')
    brand = db.relationship('Brand', backref='products')
    specifications = db.relationship('ProductSpecification', back_populates='product', cascade='all, delete-orphan')
    attributes = db.relationship('ProductAttribute', back_populates='product', cascade='all, delete-orphan', lazy='dynamic')
    quotation_items = db.relationship('QuotationItem', back_populates='product')
    images = db.relationship('ProductImage', back_populates='product', cascade='all, delete-orphan', lazy='dynamic')

    # 创建联合唯一约束
    __table_args__ = (
        UniqueConstraint('name', 'model', name='uix_product_name_model'),
    )

    def __repr__(self) -> str:
        return f"<Product {self.name} {self.model}>"
    
    def get_default_specification(self) -> Optional['ProductSpecification']:
        """获取默认产品规格 (例如第一个或标记为默认的)"""
        if self.specifications:
            return self.specifications[0]
        return None
    
    def get_price_range(self):
        """计算并返回产品的价格范围 (基于其规格)"""
        if not self.specifications:
            return "-"
        
        prices = [spec.suggested_price for spec in self.specifications if spec.suggested_price is not None]
        
        if not prices:
            return "-"
        
        min_price = min(prices)
        max_price = max(prices)
        
        if min_price == max_price:
            return f"¥{min_price:.2f}"
        else:
            return f"¥{min_price:.2f} - ¥{max_price:.2f}"
    
    def get_main_image(self) -> Optional[str]:
        """获取产品主图url"""
        main_image = self.images.filter_by(is_main=True).first()
        if main_image:
            return main_image.url
        return self.image  # 如果没有主图，返回旧的image字段值


class ProductCategory(BaseModel):
    """产品类别表模型"""
    __tablename__ = 'product_categories'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(50), nullable=False, comment='类别名称')
    parent_id = db.Column(db.Integer, db.ForeignKey('product_categories.id'), nullable=True, comment='父类别ID')
    level = db.Column(db.Integer, nullable=False, default=1, comment='层级')
    sort_order = db.Column(db.Integer, nullable=False, default=0, comment='排序')
    description = db.Column(db.Text, nullable=True, comment='描述')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 关联关系
    parent = db.relationship('ProductCategory', remote_side=[id], backref=db.backref('children', lazy='dynamic'))
    products = db.relationship('Product', back_populates='category')

    def __repr__(self) -> str:
        return f"<ProductCategory {self.name}>"
    
    def get_full_category_path(self) -> str:
        """获取完整分类路径"""
        if not self.parent:
            return self.name
        
        path = self.parent.get_full_category_path()
        return f"{path} > {self.name}"


class ProductSpecification(BaseModel):
    """产品规格、价格和税率表模型"""
    __tablename__ = 'product_specifications'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False, comment='产品ID')
    specification = db.Column(db.String(100), nullable=False, comment='产品规格描述')
    cost_price = db.Column(db.Numeric(10, 2), nullable=False, comment='成本价')
    suggested_price = db.Column(db.Numeric(10, 2), nullable=False, comment='建议售价')
    min_price = db.Column(db.Numeric(10, 2), nullable=True, comment='最低售价')
    max_price = db.Column(db.Numeric(10, 2), nullable=True, comment='最高售价')
    tax_rate = db.Column(db.Float, nullable=False, default=13.0, comment='税率(%)')
    is_default = db.Column(db.Boolean, nullable=False, default=False, comment='是否默认规格')
    notes = db.Column(db.Text, nullable=True, comment='规格备注')

    # 关联关系
    product = db.relationship('Product', back_populates='specifications')

    # 创建联合唯一约束，确保同一产品下规格描述唯一
    __table_args__ = (
        UniqueConstraint('product_id', 'specification', name='uix_product_specification'),
    )

    def __repr__(self) -> str:
        return f"<ProductSpecification {self.specification} for Product {self.product_id}>"
    
    def get_taxed_price(self) -> Optional[float]:
        """获取含税价格"""
        if self.suggested_price is None:
            return None
        return float(self.suggested_price) * (1 + self.tax_rate / 100)
    
    def get_untaxed_price(self) -> Optional[float]:
        """获取不含税价格"""
        if self.suggested_price is None:
            return None
        return float(self.suggested_price)


class ProductAttribute(BaseModel):
    """产品自定义属性表模型"""
    __tablename__ = 'product_attributes'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False, comment='产品ID')
    attribute_name = db.Column(db.String(100), nullable=False, comment='属性名称')
    attribute_value = db.Column(db.String(255), nullable=False, comment='属性值')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 关联关系
    product = db.relationship('Product', back_populates='attributes')

    # 创建联合唯一约束，确保同一产品下属性名称唯一
    __table_args__ = (
        UniqueConstraint('product_id', 'attribute_name', name='uix_product_attribute_name'),
    )

    def __repr__(self) -> str:
        return f"<ProductAttribute {self.attribute_name}={self.attribute_value} for Product {self.product_id}>"


class ProductImage(BaseModel):
    """产品图片表模型"""
    __tablename__ = 'product_images'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False, comment='产品ID')
    url = db.Column(db.String(255), nullable=False, comment='图片URL')
    file_name = db.Column(db.String(100), nullable=True, comment='文件名')
    alt_text = db.Column(db.String(255), nullable=True, comment='图片描述')
    is_main = db.Column(db.Boolean, nullable=False, default=False, comment='是否主图')
    sort_order = db.Column(db.Integer, nullable=False, default=0, comment='排序')
    file_size = db.Column(db.Integer, nullable=True, comment='文件大小(字节)')
    width = db.Column(db.Integer, nullable=True, comment='图片宽度')
    height = db.Column(db.Integer, nullable=True, comment='图片高度')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 关联关系
    product = db.relationship('Product', back_populates='images')

    def __repr__(self) -> str:
        return f"<ProductImage {self.id} for Product {self.product_id}>"
