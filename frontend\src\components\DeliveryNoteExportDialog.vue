<template>
  <el-dialog
    v-model="dialogVisible"
    title="导出发货单配置"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form :model="exportConfig" label-width="100px">
      <!-- 导出格式选择 -->
      <el-form-item label="导出格式">
        <el-radio-group v-model="exportConfig.format">
          <el-radio value="xlsx">Excel (XLSX)</el-radio>
          <el-radio value="pdf">PDF</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 表头控制 -->
      <el-form-item label="包含表头">
        <el-switch v-model="exportConfig.includeHeader" />
      </el-form-item>

      <!-- 导出列选择 -->
      <el-form-item label="导出列">
        <div class="export-columns-container">
          <!-- 总全选控制 -->
          <div class="select-all-container">
            <el-checkbox v-model="selectAll" class="select-all-checkbox">
              全选
            </el-checkbox>
          </div>

          <!-- 分类显示 -->
          <div class="columns-grid">
            <!-- 基本信息字段 -->
            <div class="column-category">
              <div class="category-header">
                <div class="category-title">基本信息</div>
                <el-checkbox v-model="basicSelectAll" @change="handleBasicSelectAllChange" class="category-select-all">
                  全选
                </el-checkbox>
              </div>
              <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
                <el-checkbox v-for="column in basicColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                  {{ column.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 物流信息字段 -->
            <div class="column-category">
              <div class="category-header">
                <div class="category-title">物流信息</div>
                <el-checkbox v-model="logisticsSelectAll" @change="handleLogisticsSelectAllChange" class="category-select-all">
                  全选
                </el-checkbox>
              </div>
              <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
                <el-checkbox v-for="column in logisticsColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                  {{ column.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 产品明细字段 -->
            <div class="column-category">
              <div class="category-header">
                <div class="category-title">产品明细</div>
                <el-checkbox v-model="itemSelectAll" @change="handleItemSelectAllChange" class="category-select-all">
                  全选
                </el-checkbox>
              </div>
              <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
                <el-checkbox v-for="column in itemColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                  {{ column.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleExport" :loading="exporting">确认导出</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { deliveryNoteApi } from '@/api/delivery'

// Props定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  deliveryNoteId: {
    type: [String, Number],
    required: true
  },
  deliveryNoteName: {
    type: String,
    default: ''
  }
})

// Emits定义
const emit = defineEmits(['update:modelValue', 'export-success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const exporting = ref(false)

// 导出配置
const exportConfig = reactive({
  format: 'xlsx',
  includeHeader: true,
  selectedColumns: [] as string[]
})

// 总全选状态
const selectAll = computed({
  get: () => exportConfig.selectedColumns.length === availableColumns.length,
  set: (val) => handleSelectAllChange(val)
})

// 可用列定义
const availableColumns = [
  // 基本信息字段
  { prop: 'delivery_number', label: '发货单号', category: 'basic' },
  { prop: 'customer_name', label: '客户名称', category: 'basic' },
  { prop: 'order_number', label: '关联订单号', category: 'basic' },
  { prop: 'project_name', label: '项目名称', category: 'basic' },
  { prop: 'delivery_date', label: '发货日期', category: 'basic' },
  { prop: 'status', label: '发货状态', category: 'basic' },
  { prop: 'settlement_status', label: '结清状态', category: 'basic' },
  { prop: 'settlement_date', label: '结清日期', category: 'basic' },
  { prop: 'total_amount', label: '总金额', category: 'basic' },
  { prop: 'created_at', label: '创建时间', category: 'basic' },

  // 物流信息字段
  { prop: 'logistics_company', label: '物流公司', category: 'logistics' },
  { prop: 'tracking_number', label: '物流单号', category: 'logistics' },
  { prop: 'recipient_name', label: '收货人姓名', category: 'logistics' },
  { prop: 'recipient_phone', label: '收货人电话', category: 'logistics' },
  { prop: 'delivery_address_snapshot', label: '收货地址', category: 'logistics' },

  // 产品明细字段
  { prop: 'product_name', label: '产品名称', category: 'items' },
  { prop: 'product_model', label: '产品型号', category: 'items' },
  { prop: 'specification_description', label: '产品规格', category: 'items' },
  { prop: 'quantity', label: '发货数量', category: 'items' },
  { prop: 'unit_price', label: '单价', category: 'items' },
  { prop: 'amount', label: '金额', category: 'items' },
  { prop: 'item_notes', label: '明细备注', category: 'items' },

  // 备注信息
  { prop: 'notes', label: '发货单备注', category: 'basic' }
]

// 分类计算属性
const basicColumns = computed(() => availableColumns.filter(col => col.category === 'basic'))
const logisticsColumns = computed(() => availableColumns.filter(col => col.category === 'logistics'))
const itemColumns = computed(() => availableColumns.filter(col => col.category === 'items'))

// 分类全选状态
const basicSelectAll = ref(true)
const logisticsSelectAll = ref(true)
const itemSelectAll = ref(true)

// 分类配置
const columnCategories = computed(() => [
  {
    name: 'basic',
    title: '基本信息',
    columns: basicColumns.value,
    selectAll: basicSelectAll
  },
  {
    name: 'logistics',
    title: '物流信息',
    columns: logisticsColumns.value,
    selectAll: logisticsSelectAll
  },
  {
    name: 'items',
    title: '产品明细',
    columns: itemColumns.value,
    selectAll: itemSelectAll
  }
])

// 初始化选中所有列
const initializeSelectedColumns = () => {
  exportConfig.selectedColumns = availableColumns.map(col => col.prop)
  basicSelectAll.value = true
  logisticsSelectAll.value = true
  itemSelectAll.value = true
}

// 总全选处理
const handleSelectAllChange = (val: boolean) => {
  if (val) {
    exportConfig.selectedColumns = availableColumns.map(col => col.prop)
    basicSelectAll.value = true
    logisticsSelectAll.value = true
    itemSelectAll.value = true
  } else {
    exportConfig.selectedColumns = []
    basicSelectAll.value = false
    logisticsSelectAll.value = false
    itemSelectAll.value = false
  }
}

// 基本信息分类全选处理
const handleBasicSelectAllChange = (val: boolean) => {
  const basicProps = basicColumns.value.map(col => col.prop)
  if (val) {
    // 添加基本信息字段到选中列表
    const newSelectedColumns = [...exportConfig.selectedColumns]
    basicProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    // 从选中列表中移除基本信息字段
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !basicProps.includes(col))
  }
  updateSelectAllStatus()
}

// 物流信息分类全选处理
const handleLogisticsSelectAllChange = (val: boolean) => {
  const logisticsProps = logisticsColumns.value.map(col => col.prop)
  if (val) {
    // 添加物流信息字段到选中列表
    const newSelectedColumns = [...exportConfig.selectedColumns]
    logisticsProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    // 从选中列表中移除物流信息字段
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !logisticsProps.includes(col))
  }
  updateSelectAllStatus()
}

// 产品明细分类全选处理
const handleItemSelectAllChange = (val: boolean) => {
  const itemProps = itemColumns.value.map(col => col.prop)
  if (val) {
    // 添加产品明细字段到选中列表
    const newSelectedColumns = [...exportConfig.selectedColumns]
    itemProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    // 从选中列表中移除产品明细字段
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !itemProps.includes(col))
  }
  updateSelectAllStatus()
}

// 更新全选状态
const updateSelectAllStatus = () => {
  const basicProps = basicColumns.value.map(col => col.prop)
  const logisticsProps = logisticsColumns.value.map(col => col.prop)
  const itemProps = itemColumns.value.map(col => col.prop)

  // 更新分类全选状态
  basicSelectAll.value = basicProps.every(prop => exportConfig.selectedColumns.includes(prop))
  logisticsSelectAll.value = logisticsProps.every(prop => exportConfig.selectedColumns.includes(prop))
  itemSelectAll.value = itemProps.every(prop => exportConfig.selectedColumns.includes(prop))
}

// 列变化处理
const handleColumnChange = () => {
  updateSelectAllStatus()
}

// 导出处理
const handleExport = async () => {
  if (exportConfig.selectedColumns.length === 0) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }

  try {
    exporting.value = true

    // 参考退货单的实现方式，直接使用fetch调用API
    const queryParams = new URLSearchParams()
    queryParams.append('format', exportConfig.format)
    if (exportConfig.selectedColumns && exportConfig.selectedColumns.length > 0) {
      queryParams.append('columns', exportConfig.selectedColumns.join(','))
    }
    if (exportConfig.includeHeader !== undefined) {
      queryParams.append('include_header', exportConfig.includeHeader.toString())
    }

    const response = await fetch(`/api/v1/delivery-export/${props.deliveryNoteId}/export?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`导出失败: ${response.statusText}`)
    }

    const blob = await response.blob()

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `发货单_${props.deliveryNoteName || props.deliveryNoteId}.${exportConfig.format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
    dialogVisible.value = false
    emit('export-success')

  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exporting.value = false
  }
}

// 监听对话框打开，初始化选中列
watch(dialogVisible, (newVal) => {
  if (newVal) {
    initializeSelectedColumns()
  }
})
</script>

<style scoped>
.export-columns-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.select-all-container {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.select-all-checkbox {
  margin-bottom: 0;
}

.columns-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.column-category {
  margin-bottom: 16px;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.category-title {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.category-select-all {
  font-size: 12px;
  color: #409eff;
}

.column-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 20px;
}

.column-checkboxes.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 16px;
  padding-left: 20px;
}

.column-checkboxes .el-checkbox {
  margin-right: 0;
}
</style>
