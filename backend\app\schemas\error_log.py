"""
错误日志相关Schema
用于序列化和反序列化错误日志数据
"""
from marshmallow import Schema, fields, validate, post_load
from app.models.error_log import ErrorLog


class ErrorLogSchema(Schema):
    """错误日志Schema"""
    id = fields.Integer(dump_only=True)
    log_type = fields.String(required=True, validate=validate.Length(min=1, max=50))
    level = fields.String(allow_none=True, validate=validate.Length(max=50))
    message = fields.String(required=True, validate=validate.Length(min=1))
    stack_trace = fields.String(allow_none=True)
    details = fields.String(allow_none=True)
    additional_info = fields.Raw(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def make_error_log(self, data, **kwargs):
        return data


class ErrorLogSimpleSchema(Schema):
    """错误日志简化Schema"""
    id = fields.Integer()
    log_type = fields.String()
    level = fields.String()
    message = fields.String()
    created_at = fields.DateTime()
