import{J as e,b as a,u as r,r as t,L as l,e as n,f as d,M as o,o as u,c as s,a as i,N as m,g as _,h as c,i as p,k as f,t as y,U as b}from"./index-3d4c440c.js";import{g as h,f as q}from"./return-614d16fd.js";import{_ as v}from"./_plugin-vue_export-helper-1b428a4d.js";const w={class:"return-order-edit"},V={class:"form-footer"},g=v(e({__name:"ReturnOrderEdit",setup(e){const v=a(),g=r(),U=t(),k=t(!1),x=t(!1),C=t(""),Y=l({id:0,order_id:0,return_number:"",return_date:"",status:"待确认",reason:"",notes:"",items:[]}),$={return_date:[{required:!0,message:"请选择退货日期",trigger:"change"}],reason:[{required:!0,message:"请输入退货原因",trigger:"blur"}]},j=async()=>{if(U.value)try{await U.value.validate();const e=Y.items.filter((e=>e.quantity>0));if(0===e.length)return void b.warning("请至少选择一个退货商品");let a=!1;if(e.forEach((e=>{const r=e,t=r.order_quantity-(r.returned_quantity||0);e.quantity>t&&(a=!0,b.error(`商品"${e.product_name}"的退货数量(${e.quantity})超过了可退数量(${t})`))})),a)return;x.value=!0;const r={return_date:Y.return_date,reason:Y.reason,notes:Y.notes,items:e.map((e=>({order_product_id:e.order_product_id,quantity:e.quantity,reason:e.reason,notes:e.notes})))};await q(Y.id,r),b.success("退货单更新成功"),v.push("/return")}catch(e){console.error("更新退货单失败:",e),b.error("更新退货单失败")}finally{x.value=!1}},D=()=>{v.back()};return n((()=>{(async()=>{var e;const a=Number(g.params.id);if(!a)return b.error("退货单ID无效"),void v.back();k.value=!0;try{const r=(await h(a)).data;if(!r)throw new Error("获取退货单详情失败：数据为空");Y.id=r.id,Y.order_id=r.order_id,Y.return_number=r.return_number||"",Y.return_date=r.return_date,Y.status=r.status||"待确认",Y.reason=r.reason,Y.notes=r.notes||"",Y.items=(r.items||[]).map((e=>{var a;return{...e,order_quantity:(null==(a=e.order_product)?void 0:a.quantity)||0,returned_quantity:0}})),C.value=(null==(e=r.order)?void 0:e.order_number)||""}catch(r){console.error("获取退货单详情失败:",r),b.error("获取退货单详情失败"),v.back()}finally{k.value=!1}})()})),(e,a)=>{const r=d("el-input"),t=d("el-form-item"),l=d("el-date-picker"),n=d("el-card"),h=d("el-table-column"),q=d("el-input-number"),v=d("el-table"),g=d("el-button"),E=d("el-form"),M=o("loading");return u(),s("div",w,[a[9]||(a[9]=i("div",{class:"page-header"},[i("h2",null,"编辑退货单")],-1)),m((u(),_(E,{ref_key:"formRef",ref:U,model:Y,rules:$,"label-width":"120px",class:"edit-form"},{default:c((()=>[p(n,{class:"form-card"},{header:c((()=>a[5]||(a[5]=[i("div",{class:"card-header"},[i("span",null,"基本信息")],-1)]))),default:c((()=>[p(t,{label:"退货单号"},{default:c((()=>[p(r,{modelValue:Y.return_number,"onUpdate:modelValue":a[0]||(a[0]=e=>Y.return_number=e),disabled:""},null,8,["modelValue"])])),_:1}),p(t,{label:"关联订单"},{default:c((()=>[p(r,{modelValue:C.value,"onUpdate:modelValue":a[1]||(a[1]=e=>C.value=e),disabled:""},null,8,["modelValue"])])),_:1}),p(t,{label:"退货日期",prop:"return_date"},{default:c((()=>[p(l,{modelValue:Y.return_date,"onUpdate:modelValue":a[2]||(a[2]=e=>Y.return_date=e),type:"date",placeholder:"请选择退货日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),p(t,{label:"退货原因",prop:"reason"},{default:c((()=>[p(r,{modelValue:Y.reason,"onUpdate:modelValue":a[3]||(a[3]=e=>Y.reason=e),type:"textarea",rows:3,placeholder:"请输入退货原因"},null,8,["modelValue"])])),_:1}),p(t,{label:"备注",prop:"notes"},{default:c((()=>[p(r,{modelValue:Y.notes,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1}),p(n,{class:"form-card"},{header:c((()=>a[6]||(a[6]=[i("div",{class:"card-header"},[i("span",null,"退货商品")],-1)]))),default:c((()=>[p(v,{data:Y.items,border:""},{default:c((()=>[p(h,{label:"商品名称","min-width":"200"},{default:c((({row:e})=>[f(y(e.product_name),1)])),_:1}),p(h,{label:"规格","min-width":"150"},{default:c((({row:e})=>[f(y(e.specification_description),1)])),_:1}),p(h,{label:"订单数量",width:"100"},{default:c((({row:e})=>[f(y(e.order_quantity),1)])),_:1}),p(h,{label:"已退数量",width:"100"},{default:c((({row:e})=>[f(y(e.returned_quantity||0),1)])),_:1}),p(h,{label:"可退数量",width:"100"},{default:c((({row:e})=>[f(y(e.order_quantity-(e.returned_quantity||0)),1)])),_:1}),p(h,{label:"退货数量",width:"150"},{default:c((({row:e})=>[p(q,{modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,min:1,max:e.order_quantity-(e.returned_quantity||0),onChange:a=>(e=>{const a=e.order_quantity-(e.returned_quantity||0);e.quantity>a&&(e.quantity=a,b.warning(`退货数量不能超过可退数量(${a})，已自动调整`)),e.quantity<=0&&(e.quantity=1,b.warning("退货数量必须大于0"))})(e)},null,8,["modelValue","onUpdate:modelValue","max","onChange"])])),_:1}),p(h,{label:"备注","min-width":"150"},{default:c((({row:e})=>[p(r,{modelValue:e.notes,"onUpdate:modelValue":a=>e.notes=a,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"])])),_:1}),i("div",V,[p(g,{onClick:D},{default:c((()=>a[7]||(a[7]=[f("取消")]))),_:1}),p(g,{type:"primary",loading:x.value,onClick:j},{default:c((()=>a[8]||(a[8]=[f("保存")]))),_:1},8,["loading"])])])),_:1},8,["model"])),[[M,k.value]])])}}}),[["__scopeId","data-v-a4c5b8f1"]]);export{g as default};
