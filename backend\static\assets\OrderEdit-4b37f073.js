import{J as e,u as a,b as t,r as l,d as o,L as r,e as d,U as n,f as i,o as u,c as s,i as c,h as p,a as _,t as m,k as v,F as f,m as g,l as h,a9 as y,g as b,ae as w,_ as V,a4 as x,X as q,Y as j}from"./index-3d4c440c.js";import{u as U,w as C,r as k}from"./xlsx-3e8bf635.js";import{c as S,e as N,f as A}from"./order-d6035a15.js";import{d as E,s as I}from"./customer-471ca075.js";import{g as P,l as $}from"./quotation-219e97aa.js";import{l as z}from"./product-ab10366a.js";import{b as O}from"./formatter-5775d610.js";import{_ as D}from"./_plugin-vue_export-helper-1b428a4d.js";const Y={class:"order-edit"},T={class:"flex-between"},M={class:"form-title"},R={class:"flex-between"},B={class:"card-header flex-between"},F={key:0,class:"empty-data"},H={key:1,class:"order-summary"},K={class:"summary-item"},J={class:"summary-item"},L={class:"highlight"},Q={class:"mb-10"},W={class:"dialog-footer"},X={class:"product-search mb-20"},G={key:0},Z={class:"flex-between mt-20"},ee={class:"dialog-footer"},ae={class:"excel-upload-tip mb-20"},te={class:"el-upload__tip"},le={class:"dialog-footer"},oe=D(e({__name:"OrderEdit",setup(e,{expose:D}){const oe=a(),re=t(),de=l(null),ne=o((()=>!!oe.params.id)),ie=o((()=>oe.params.id?parseInt(oe.params.id,10):null)),ue=o((()=>oe.query.quotation_id?Number(oe.query.quotation_id):null)),se=l({id:null,order_number:"",order_date:(new Date).toISOString().split("T")[0],customer_id:null,customer_name:"",contact_person:"",contact_phone:"",project_name:"",project_address:"",delivery_address_id:null,expected_date:"",payment_terms:"",delivery_terms:"",notes:"",status:"draft",total_amount:0,products:[]}),ce={order_date:[{required:!0,message:"请选择订单日期",trigger:"change"}],customer_id:[{required:!0,message:"请选择客户",trigger:"change"}],project_name:[{required:!0,message:"请输入项目名称",trigger:"blur"}],project_address:[{required:!0,message:"请输入项目地址",trigger:"blur"}],expected_date:[{required:!0,message:"请选择预计交期",trigger:"change"}],delivery_terms:[{required:!0,message:"请选择交货条件",trigger:"change"}],payment_terms:[{required:!0,message:"请输入付款条件",trigger:"blur"}],products:[{required:!0,validator:(e,a,t)=>{if(a&&0!==a.length){a.filter((e=>!(e.product_name&&e.unit&&e.quantity&&e.unit_price))).length>0?t(new Error("请完整填写所有产品信息")):t()}else t(new Error("请至少添加一个产品"))},trigger:"change"}]},pe=l(!1),_e=l([]),me=async e=>{var a;if(e){pe.value=!0;try{const t=await I({name_like:e});let l=(null==(a=null==t?void 0:t.data)?void 0:a.items)||(null==t?void 0:t.data)||(null==t?void 0:t.items)||t||[];Array.isArray(l)||(l=[]),_e.value=l,console.log("客户搜索响应:",t),console.log("处理后的客户选项:",_e.value)}catch(t){console.error("搜索客户失败:",t),_e.value=[]}finally{pe.value=!1}}else _e.value=[]},ve=async e=>{var a;if(!e)return se.value.contact_person="",void(se.value.contact_phone="");console.log(`[OrderEdit] 客户已选择, ID: ${e}, 准备获取详细信息...`),pe.value=!0;try{const t=await E(e);console.log(`[OrderEdit] 获取到客户 ${e} 的详细信息:`,t);const l=(null==t?void 0:t.data)||t;if(l&&"object"==typeof l){se.value.contact_person=l.contact||"",se.value.contact_phone=l.phone||"";const e=null==(a=l.delivery_addresses)?void 0:a.find((e=>e.is_default));e&&(se.value.project_address=[e.province,e.city,e.district,e.address].filter(Boolean).join(" "),console.log(`[OrderEdit] 已自动填充默认地址: ${se.value.project_address}`)),n.success("已自动填充客户信息")}else console.error(`[OrderEdit] 未能从API响应中获取到客户 ${e} 的有效数据对象:`,t),n.error("获取客户详细信息失败")}catch(t){console.error(`[OrderEdit] 获取客户 ${e} 详细信息时出错:`,t),n.error("加载客户详情失败")}finally{pe.value=!1,console.log(`[OrderEdit] 客户信息处理流程结束, ID: ${e}`)}},fe=l(!1),ge=r({quotation_number:"",customer_name:""}),he=l([]),ye=r({currentPage:1,pageSize:10,total:0}),be=l(null),we=l(null),Ve=()=>{ge.quotation_number="",ge.customer_name="",be.value=null,fe.value=!0,xe()},xe=()=>{ye.currentPage=1,(async()=>{var e,a;try{const t={page:ye.currentPage,per_page:ye.pageSize,status:"已确认",...ge.quotation_number&&{quotation_number:ge.quotation_number},...ge.customer_name&&{customer_name:ge.customer_name}};console.log("搜索报价单参数:",t);const l=await $(t);console.log("报价单搜索结果:",l),l?"data"in l&&l.data?(Array.isArray(l.data)?he.value=l.data:"list"in l.data&&Array.isArray(l.data.list)?(he.value=l.data.list,ye.total=(null==(e=l.data.pagination)?void 0:e.total_items)||0):he.value=[],"page"in l&&l.page?ye.total=l.page.total||0:"page_info"in l&&l.page_info?ye.total=l.page_info.total||0:"data"in l&&"page"in l.data&&l.data.page&&(ye.total=l.data.page.total||0)):"list"in l?(he.value=l.list,ye.total=(null==(a=l.pagination)?void 0:a.total_items)||0):Array.isArray(l)?(he.value=l,ye.total=l.length):(he.value=[],ye.total=0):(he.value=[],ye.total=0),console.log("处理后的报价单列表:",he.value)}catch(t){console.error("搜索报价单失败:",t),n.error("搜索报价单失败"),he.value=[],ye.total=0}})()},qe=e=>{e&&(be.value=e.id)},je=async()=>{var e;const a=he.value.find((e=>e.id===be.value));if(a)try{const l=await P(a.id);console.log("报价单详情响应:",l);const o=l.data||l;if(console.log("处理后的报价单详情数据:",o),!o)throw new Error("未能获取报价单详情数据");if(se.value={...se.value,quotation_id:o.id,quotation_number:o.quotation_number||"",customer_id:o.customer_id,customer_name:(null==(e=o.customer)?void 0:e.name)||a.customer_name||"",project_name:o.project_name||"",project_address:o.project_address||"",payment_terms:o.payment_terms||"",delivery_terms:o.delivery_terms||"送货上门",notes:o.notes||""},console.log("已更新表单基本信息:",{quotation_id:se.value.quotation_id,quotation_number:se.value.quotation_number,customer_id:se.value.customer_id,customer_name:se.value.customer_name}),o.customer_id)try{const e=await E(o.customer_id);console.log("客户信息响应:",e);const a=e.data||e;if(a)if(console.log("客户详情数据:",a),se.value={...se.value,customer_id:a.id,customer_name:a.name||"",contact_person:a.contact||"",contact_phone:a.phone||"",_selectedCustomer:a},a.delivery_addresses&&a.delivery_addresses.length>0){const e=a.delivery_addresses.find((e=>e.is_default))||a.delivery_addresses[0];se.value.delivery_address_id=e.id,se.value.project_address||(se.value.project_address=e.address)}else n.warning("该客户没有设置送货地址，请手动填写项目地址")}catch(t){console.error("获取客户信息失败:",t),n.warning("获取客户信息失败，请手动完善客户相关信息")}o.items&&Array.isArray(o.items)?(console.log("报价单产品项数量:",o.items.length),se.value.products=[],se.value.products=o.items.map((e=>{var a,t,l,o;return console.log("处理产品项:",e),{product_id:e.product_id,product_specification_id:e.product_specification_id,product_name:e.product_name||e.product_name_snapshot||"",product_model:e.product_model||e.product_model_snapshot||"",specification:(null==(a=e.product_specification)?void 0:a.specification)||(null==(t=e.specification)?void 0:t.specification)||e.product_spec_snapshot||"",specification_name:(null==(l=e.product_specification)?void 0:l.specification)||(null==(o=e.specification)?void 0:o.specification)||e.product_spec_snapshot||"",unit:e.product_unit||e.product_unit_snapshot||"个",quantity:Number(e.quantity)||0,unit_price:Number(e.unit_price)||0,discount_rate:Number(e.discount||0),tax_rate:Number(e.tax_rate||13),total_price:Number(e.total_price)||0,notes:e.notes||""}})),console.log("已更新产品列表，共计",se.value.products.length,"项")):(console.warn("报价单中没有产品项或产品项格式不正确"),se.value.products=[]),De(),fe.value=!1,n.success("已成功加载报价单信息")}catch(l){console.error("加载报价单详情失败:",l),n.error("加载报价单详情失败")}else n.warning("请选择一个报价单")},Ue=l(!1),Ce=l(""),ke=l([]);l(null);const Se=l([]),Ne=r({currentPage:1,pageSize:10,total:0}),Ae=()=>{Ce.value="",Ne.currentPage=1,ke.value=[],Se.value=[],Ee(),Ue.value=!0},Ee=async()=>{try{const e={page:Ne.currentPage,per_page:Ne.pageSize,search:Ce.value||void 0,with_specifications:!0};console.log("产品搜索参数:",e);const a=await z(e);if(console.log("产品搜索响应:",a),a){let e=[];Array.isArray(a)?e=a:"object"==typeof a&&"list"in a&&Array.isArray(a.list)?e=a.list:"object"==typeof a&&"data"in a&&Array.isArray(a.data)?e=a.data:"object"==typeof a&&"data"in a&&"object"==typeof a.data&&a.data&&"list"in a.data&&Array.isArray(a.data.list)&&(e=a.data.list),ke.value=e.map((e=>({...e,selectedSpecIds:[],specifications:e.specifications||[]})));let t=null;"object"==typeof a&&"pagination"in a&&a.pagination?t=a.pagination:"object"==typeof a&&"data"in a&&"object"==typeof a.data&&a.data&&"pagination"in a.data?t=a.data.pagination:"object"==typeof a&&"page_info"in a?t=a.page_info:"object"==typeof a&&"data"in a&&"object"==typeof a.data&&a.data&&"page_info"in a.data&&(t=a.data.page_info),t&&(Ne.total=t.total_items||t.total||0)}}catch(e){console.error("搜索产品失败:",e),n.error("搜索产品失败"),ke.value=[],Ne.total=0}},Ie=e=>{Ne.pageSize=e,Ne.currentPage=1,Ee()},Pe=e=>{Ne.currentPage=e,Ee()},$e=e=>{Se.value=e},ze=async()=>{const e=ke.value.filter((e=>e.selectedSpecIds&&e.selectedSpecIds.length>0));for(const a of e)for(const e of a.selectedSpecIds){const t=a.specifications.find((a=>a.id===e));t&&se.value.products.push({product_id:a.id,product_specification_id:e,product_name:a.name,product_model:a.model,specification:t.specification,specification_name:t.specification,unit:a.unit,quantity:1,unit_price:Number(t.suggested_price)||0,discount_rate:0,tax_rate:13,total_price:Number(t.suggested_price)||0,notes:""})}Ue.value=!1,De()},Oe=e=>{const a=Number(e.quantity)||0,t=Number(e.unit_price)||0,l=Number(e.discount)||0,o=Number(e.tax_rate)||13,r=a*t*(1-l/100);e.total_price=r*(1+o/100),De()},De=()=>{se.value.total_amount=se.value.products.reduce(((e,a)=>e+(Number(a.total_price)||0)),0)},Ye=l(!1),Te=l([]),Me=l(null),Re=()=>{Te.value=[],Me.value=null,Ye.value=!0},Be=(e,a)=>{e.raw&&(Me.value=e.raw),Te.value=a.slice(-1)},Fe=(e,a)=>{Me.value=null,Te.value=[]},He=()=>{const e=U.aoa_to_sheet([["产品名称*","型号","规格","单位*","数量*","单价*","折扣率(0-1)","税率(0-1)","备注"]]);e["!cols"]=[{wch:20},{wch:15},{wch:20},{wch:8},{wch:8},{wch:10},{wch:15},{wch:15},{wch:30}];const a=U.book_new();U.book_append_sheet(a,e,"产品明细模板"),C(a,"订单产品导入模板.xlsx"),n.success("模板文件已开始下载")},Ke=()=>{if(!Me.value)return void n.warning("请先选择一个Excel文件");const e=new FileReader;e.onload=e=>{var a;try{const t=null==(a=e.target)?void 0:a.result,l=k(t,{type:"binary"}),o=l.SheetNames[0],r=l.Sheets[o],d=U.sheet_to_json(r,{header:1});if(d.length<2)return void n.error("Excel文件内容为空或表头不正确");const i=d[0].map(String),u={"产品名称*":"product_name","型号":"product_model","规格":"specification_name","单位*":"unit","数量*":"quantity","单价*":"unit_price","折扣率(0-1)":"discount_rate","税率(0-1)":"tax_rate","备注":"notes"},s=[];for(let e=1;e<d.length;e++){const a=d[e];if(!a||a.every((e=>null==e||""===String(e).trim())))continue;const t={product_id:0,product_specification_id:0};let l=!0;if(i.forEach(((e,o)=>{const r=u[e];if(r){let e=a[o];["quantity","unit_price","discount_rate","tax_rate"].includes(r)&&(e=parseFloat(String(e)),isNaN(e)&&(["quantity","unit_price"].includes(r)&&(l=!1),e=void 0)),t[r]=e}})),!t.product_name||!t.unit||void 0===t.quantity||void 0===t.unit_price){n.warning(`第 ${e+1} 行数据不完整 (产品名称, 单位, 数量, 单价为必填)，已跳过。`);continue}t.product_model=t.product_model||"",t.specification_name=t.specification_name||"",t.unit=t.unit||"个",t.discount_rate=t.discount_rate||0,t.tax_rate=t.tax_rate||13,t.notes=t.notes||"";const o=Number(t.quantity)||0,r=Number(t.unit_price)||0,c=Number(t.discount_rate)||0;t.total_price=o*r*(1-c),s.push(t)}s.length>0?(se.value.products.push(...s),De(),n.success(`成功导入 ${s.length} 条产品数据`)):n.info("未能从Excel文件中导入有效的产品数据")}catch(t){console.error("解析Excel文件失败:",t),n.error("解析Excel文件失败，请确保文件格式正确。")}finally{Ye.value=!1,Me.value=null,Te.value=[]}},e.readAsBinaryString(Me.value)},Je=async(e="draft")=>{if(de.value)try{if("draft"!==e){if(await de.value.validate(),!se.value.customer_id)return void n.error("请选择客户");if(!se.value.project_name)return void n.error("请输入项目名称");if(!se.value.project_address)return void n.error("请输入项目地址");if(!se.value.order_date)return void n.error("请选择订单日期");if(!se.value.expected_date)return void n.error("请选择预计交期");if(!se.value.payment_terms)return void n.error("请输入付款条件");if(!se.value.delivery_terms)return void n.error("请选择交货条件");if("draft"!==e&&!(se.value.delivery_address_id||se.value.project_address&&""!==se.value.project_address.trim()))return void n.error("请为订单设置送货地址 (选择客户档案地址或填写项目地址)");if(0===se.value.products.length)return void n.warning("请至少添加一个产品明细");if(se.value.products.filter((e=>!e.product_name||!e.unit||!e.quantity||e.quantity<=0||!e.unit_price||e.unit_price<0)).length>0)return void n.warning("请完整填写所有产品信息，包括产品名称、单位、数量和单价");const t=se.value.products.map((e=>({product_specification_id:Number(e.product_specification_id)||0,quantity:Number(e.quantity)||0,unit_price:Number(e.unit_price)||0,notes:e.notes||""}))),l={customer_id:Number(se.value.customer_id),project_name:se.value.project_name,project_address:se.value.project_address,delivery_address_id:Number(se.value.delivery_address_id),payment_terms:se.value.payment_terms,delivery_terms:se.value.delivery_terms,notes:se.value.notes||"",status:"draft"===e?"pending":e,products:t};se.value.quotation_id&&(l.quotation_id=se.value.quotation_id),se.value.expected_date&&(l.expected_date=se.value.expected_date),console.log("提交的订单数据:",l);try{let e;ne.value&&se.value.id?(e=await N(se.value.id,l),console.log("更新订单响应:",e)):(e=await A(l),console.log("创建订单响应:",e)),n.success(ne.value?"订单更新成功":"订单创建成功"),Le()}catch(a){if(console.error("API请求失败详情:",a),a.response)if(console.error("HTTP状态码:",a.response.status),console.error("响应头:",a.response.headers),console.error("响应体:",a.response.data),a.response.data&&a.response.data.errors){const e=a.response.data.errors;console.error("表单验证错误:",e);const t={payment_terms:"付款条件",delivery_terms:"交货条件",customer_id:"客户",project_name:"项目名称",project_address:"项目地址",order_date:"订单日期",expected_date:"预计交期",products:"产品明细",contact_person:"联系人",contact_phone:"联系电话",delivery_address_id:"送货地址"},l=Object.entries(e).map((([e,a])=>`${t[e]||e}: ${Array.isArray(a)?a.join(", "):String(a)}`)).join("\n");n({type:"error",message:`表单验证失败:\n${l}`,duration:5e3,showClose:!0})}else a.response.data&&a.response.data.message?n.error(`保存失败: ${a.response.data.message}`):n.error(`请求失败，HTTP错误码: ${a.response.status}`);else a.request?n.error("服务器无响应，请稍后重试"):n.error(`请求错误: ${a.message}`)}}}catch(t){console.error("保存订单失败:",t),n.error("表单验证失败，请检查必填字段")}},Le=()=>{re.push("/orders")};d((async()=>{var e;if(ne.value&&Qe(),ue.value)try{const a=await P(ue.value);if(a){const t=a.items.map((e=>({product_id:0,product_specification_id:0,product_name:"",product_model:"",specification:"",specification_name:"",unit:"",quantity:1,unit_price:0,discount_rate:0,tax_rate:13,total_price:0,notes:"",product_id:e.product_id,product_specification_id:e.product_specification_id,product_name:e.product_name_snapshot||"",product_model:e.product_model_snapshot||"",specification_name:e.product_spec_snapshot||"",unit:e.product_unit_snapshot||"",quantity:e.quantity||0,unit_price:e.unit_price||0,discount_rate:(e.discount||0)/100,tax_rate:e.tax_rate||13,total_price:Xe(e)||0,notes:e.notes||""})));se.value={...se.value,quotation_id:a.id,quotation_number:a.quotation_number||"",customer_id:a.customer_id,customer_name:(null==(e=a.customer)?void 0:e.name)||"",project_name:a.project_name||"",project_address:a.project_address||"",notes:a.notes||"",products:t},a.customer_id&&await We(a.customer_id),De()}}catch(a){console.error("从报价单加载数据失败:",a),n.error("从报价单加载数据失败")}}));const Qe=async()=>{var e,a,t,l;if(ie.value)try{const o=(await S(String(ie.value))).data;console.log("订单数据:",o),se.value={...se.value,id:o.id,order_number:o.order_number,order_date:o.order_date||(new Date).toISOString().split("T")[0],customer_id:o.customer_id,customer_name:(null==(e=o.customer)?void 0:e.name)||o.customer_name||"",contact_person:(null==(a=o.customer)?void 0:a.contact)||"",contact_phone:(null==(t=o.customer)?void 0:t.phone)||"",project_name:o.project_name||"",project_address:o.project_address||"",expected_date:o.expected_date||(new Date).toISOString().split("T")[0],notes:o.notes||"",quotation_id:o.quotation_id,quotation_number:(null==(l=o.quotation)?void 0:l.quotation_number)||"",status:o.status,delivery_terms:o.delivery_terms||"送货上门",payment_terms:o.payment_terms||"",products:(o.products||[]).map((e=>{var a,t,l,o,r;return{id:e.id,product_id:e.product_id,product_specification_id:e.product_specification_id,product_name:e.product_name||"",product_model:e.product_model||"",specification:(null==(a=e.product_specification)?void 0:a.specification)||(null==(t=e.specification)?void 0:t.specification)||e.specification_name||"",specification_name:(null==(l=e.product_specification)?void 0:l.specification)||(null==(o=e.specification)?void 0:o.specification)||e.specification_name||"",unit:e.product_unit||"个",quantity:e.quantity||0,unit_price:e.unit_price||0,discount_rate:e.discount_rate||0,tax_rate:(null==(r=e.product_specification)?void 0:r.tax_rate)||e.tax_rate||13,total_price:e.total_price||0,notes:e.notes||"",discount:e.discount_rate?100*e.discount_rate:0}}))},console.log("表单数据已填充:",se.value),o.customer_id&&await We(o.customer_id),De(),n.success("订单数据加载成功")}catch(o){console.error("加载订单数据失败:",o),n.error("加载订单数据失败，请重试。")}},We=async e=>{try{const a=await E(e);if(console.log("加载到客户详情:",a),a&&200===a.code&&a.data){const e=a.data;if(se.value={...se.value,customer_id:e.id,customer_name:e.name,contact_person:e.contact||se.value.contact_person||"",contact_phone:e.phone||se.value.contact_phone||"",_selectedCustomer:e},console.log("客户信息已更新到表单:",se.value.customer_name),!se.value.project_address&&e.delivery_addresses&&e.delivery_addresses.length>0){const a=e.delivery_addresses.find((e=>e.is_default))||e.delivery_addresses[0];se.value.delivery_address_id=a.id,se.value.project_address=a.address}}else console.warn("客户API响应格式不符合预期:",a)}catch(a){console.error("加载客户详情失败:",a),n.warning("加载客户详情失败，部分客户信息可能不完整")}},Xe=e=>{const a=(e.quantity||0)*(e.unit_price||0),t=a*((e.discount||0)/100);return a-t+(e.tax_rate||13)/100*(a-t)},Ge=()=>{ge.quotation_number="",ge.customer_name="",xe()};return D({goBack:Le,resetQuotationSearch:Ge}),(e,a)=>{const t=i("el-button"),l=i("el-card"),o=i("el-input"),r=i("el-form-item"),d=i("el-col"),n=i("el-date-picker"),U=i("el-row"),C=i("el-option"),k=i("el-select"),S=i("el-icon"),N=i("el-table-column"),A=i("el-input-number"),E=i("el-table"),I=i("el-empty"),P=i("el-form"),$=i("el-alert"),z=i("el-radio"),D=i("el-tag"),oe=i("el-dialog"),re=i("el-checkbox"),ie=i("el-checkbox-group"),ue=i("el-pagination"),ye=i("el-link"),Se=i("el-upload");return u(),s("div",Y,[c(l,{class:"header-card mb-20"},{default:p((()=>[_("div",T,[_("h2",M,m(ne.value?"编辑订单":"新增订单"),1),_("div",null,[c(t,{onClick:Le},{default:p((()=>a[26]||(a[26]=[v("取消")]))),_:1}),c(t,{type:"primary",onClick:a[0]||(a[0]=e=>Je("draft"))},{default:p((()=>a[27]||(a[27]=[v("保存草稿")]))),_:1}),c(t,{type:"success",onClick:a[1]||(a[1]=e=>Je("confirmed"))},{default:p((()=>a[28]||(a[28]=[v("确认订单")]))),_:1})])])])),_:1}),c(P,{ref_key:"formRef",ref:de,model:se.value,rules:ce,"label-width":"120px",class:"order-form"},{default:p((()=>[c(l,{class:"mb-20"},{header:p((()=>a[29]||(a[29]=[_("div",{class:"card-header"},[_("h3",null,"基本信息")],-1)]))),default:p((()=>[c(U,{gutter:20},{default:p((()=>[c(d,{span:12},{default:p((()=>[c(r,{label:"订单号",prop:"order_number"},{default:p((()=>[c(o,{modelValue:se.value.order_number,"onUpdate:modelValue":a[2]||(a[2]=e=>se.value.order_number=e),placeholder:"自动生成",disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),c(d,{span:12},{default:p((()=>[c(r,{label:"订单日期",prop:"order_date"},{default:p((()=>[c(n,{modelValue:se.value.order_date,"onUpdate:modelValue":a[3]||(a[3]=e=>se.value.order_date=e),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(U,{gutter:20},{default:p((()=>[c(d,{span:12},{default:p((()=>[c(r,{label:"关联报价单"},{default:p((()=>[_("div",R,[c(o,{modelValue:se.value.quotation_number,"onUpdate:modelValue":a[4]||(a[4]=e=>se.value.quotation_number=e),placeholder:"无关联报价单",disabled:""},null,8,["modelValue"]),c(t,{type:"primary",onClick:Ve},{default:p((()=>a[30]||(a[30]=[v("选择报价单")]))),_:1})])])),_:1})])),_:1}),c(d,{span:12},{default:p((()=>[c(r,{label:"预计交期",prop:"expected_date"},{default:p((()=>[c(n,{modelValue:se.value.expected_date,"onUpdate:modelValue":a[5]||(a[5]=e=>se.value.expected_date=e),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),c(l,{class:"mb-20"},{header:p((()=>a[31]||(a[31]=[_("div",{class:"card-header"},[_("h3",null,"客户信息")],-1)]))),default:p((()=>[c(U,{gutter:20},{default:p((()=>[c(d,{span:12},{default:p((()=>[c(r,{label:"客户名称",prop:"customer_id"},{default:p((()=>[c(k,{modelValue:se.value.customer_id,"onUpdate:modelValue":a[6]||(a[6]=e=>se.value.customer_id=e),filterable:"",remote:"",placeholder:"请选择客户","remote-method":me,loading:pe.value,style:{width:"100%"},onChange:ve},{default:p((()=>[(u(!0),s(f,null,g(_e.value,(e=>(u(),b(C,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})])),_:1}),c(d,{span:12},{default:p((()=>[c(r,{label:"联系人"},{default:p((()=>[c(o,{modelValue:se.value.contact_person,"onUpdate:modelValue":a[7]||(a[7]=e=>se.value.contact_person=e),placeholder:"请输入联系人"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(U,{gutter:20},{default:p((()=>[c(d,{span:12},{default:p((()=>[c(r,{label:"项目名称",prop:"project_name"},{default:p((()=>[c(o,{modelValue:se.value.project_name,"onUpdate:modelValue":a[8]||(a[8]=e=>se.value.project_name=e),placeholder:"请输入项目名称"},null,8,["modelValue"])])),_:1})])),_:1}),c(d,{span:12},{default:p((()=>[c(r,{label:"联系电话"},{default:p((()=>[c(o,{modelValue:se.value.contact_phone,"onUpdate:modelValue":a[9]||(a[9]=e=>se.value.contact_phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(r,{label:"项目地址",prop:"project_address"},{default:p((()=>[c(o,{modelValue:se.value.project_address,"onUpdate:modelValue":a[10]||(a[10]=e=>se.value.project_address=e),placeholder:"请输入项目地址"},null,8,["modelValue"])])),_:1})])),_:1}),c(l,{class:"mb-20"},{header:p((()=>a[32]||(a[32]=[_("div",{class:"card-header"},[_("h3",null,"交易条款")],-1)]))),default:p((()=>[c(r,{label:"付款条件",prop:"payment_terms"},{default:p((()=>[c(o,{modelValue:se.value.payment_terms,"onUpdate:modelValue":a[11]||(a[11]=e=>se.value.payment_terms=e),placeholder:"例如：预付30%，发货前付款40%，验收合格后30天内付款30%"},null,8,["modelValue"])])),_:1}),c(r,{label:"交货条件",prop:"delivery_terms"},{default:p((()=>[c(k,{modelValue:se.value.delivery_terms,"onUpdate:modelValue":a[12]||(a[12]=e=>se.value.delivery_terms=e),placeholder:"请选择交货条件",style:{width:"100%"}},{default:p((()=>[c(C,{label:"送货上门",value:"送货上门"}),c(C,{label:"客户自提",value:"客户自提"}),c(C,{label:"物流配送",value:"物流配送"})])),_:1},8,["modelValue"])])),_:1}),c(r,{label:"备注"},{default:p((()=>[c(o,{modelValue:se.value.notes,"onUpdate:modelValue":a[13]||(a[13]=e=>se.value.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1}),c(l,null,{header:p((()=>[_("div",B,[a[35]||(a[35]=_("h3",null,"产品明细",-1)),_("div",null,[c(t,{type:"success",onClick:Ae},{default:p((()=>[c(S,null,{default:p((()=>[c(h(w))])),_:1}),a[33]||(a[33]=v(" 选择产品 "))])),_:1}),c(t,{type:"warning",onClick:Re},{default:p((()=>[c(S,null,{default:p((()=>[c(h(V))])),_:1}),a[34]||(a[34]=v(" Excel导入 "))])),_:1})])])])),default:p((()=>[c(E,{data:se.value.products,border:"",style:{width:"100%"}},{default:p((()=>[c(N,{type:"index",label:"序号",width:"50"}),c(N,{label:"产品名称","min-width":"180"},{default:p((({row:e})=>[c(o,{modelValue:e.product_name,"onUpdate:modelValue":a=>e.product_name=a,placeholder:"请输入产品名称"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),c(N,{label:"型号",width:"120"},{default:p((({row:e})=>[c(o,{modelValue:e.product_model,"onUpdate:modelValue":a=>e.product_model=a,placeholder:"请输入型号"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),c(N,{label:"规格","min-width":"100"},{default:p((({row:e})=>[c(o,{modelValue:e.specification,"onUpdate:modelValue":a=>e.specification=a,placeholder:"请输入规格"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),c(N,{label:"单位",width:"90"},{default:p((({row:e})=>[c(k,{modelValue:e.unit,"onUpdate:modelValue":a=>e.unit=a,placeholder:"单位",style:{width:"100%"}},{default:p((()=>[c(C,{label:"个",value:"个"}),c(C,{label:"台",value:"台"}),c(C,{label:"套",value:"套"}),c(C,{label:"米",value:"米"}),c(C,{label:"根",value:"根"}),c(C,{label:"件",value:"件"}),c(C,{label:"箱",value:"箱"})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),c(N,{label:"数量",width:"100"},{default:p((({row:e})=>[c(A,{modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,min:1,"controls-position":"right",style:{width:"100%"},onChange:a=>Oe(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),c(N,{label:"单价",width:"100"},{default:p((({row:e})=>[c(A,{modelValue:e.unit_price,"onUpdate:modelValue":a=>e.unit_price=a,min:0,precision:2,"controls-position":"right",onChange:a=>Oe(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),c(N,{label:"税率(%)",width:"80"},{default:p((({row:e})=>[c(A,{modelValue:e.tax_rate,"onUpdate:modelValue":a=>e.tax_rate=a,min:0,max:100,precision:2,"controls-position":"right",style:{width:"200%"},onChange:a=>Oe(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),c(N,{label:"折扣(%)",width:"80"},{default:p((({row:e})=>[c(A,{modelValue:e.discount,"onUpdate:modelValue":a=>e.discount=a,min:0,max:100,precision:0,"controls-position":"right",onChange:a=>Oe(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),c(N,{label:"总价",width:"120"},{default:p((({row:e})=>[v(m(h(O)(e.total_price)),1)])),_:1}),c(N,{label:"操作",width:"120",fixed:"right"},{default:p((({$index:e})=>[c(t,{type:"danger",link:"",onClick:a=>{return t=e,se.value.products.splice(t,1),void De();var t}},{default:p((()=>[c(S,null,{default:p((()=>[c(h(x))])),_:1}),a[36]||(a[36]=v(" 删除 "))])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),0===se.value.products.length?(u(),s("div",F,[c(I,{description:"暂无产品数据"})])):(u(),s("div",H,[_("div",K,[a[37]||(a[37]=_("span",null,"产品数量:",-1)),_("span",null,m(se.value.products.reduce(((e,a)=>e+(Number(a.quantity)||0)),0))+" 件",1)]),_("div",J,[a[38]||(a[38]=_("span",null,"订单总金额:",-1)),_("span",L,m(h(O)((De(),se.value.total_amount||0))),1)])]))])),_:1})])),_:1},8,["model"]),c(oe,{modelValue:fe.value,"onUpdate:modelValue":a[18]||(a[18]=e=>fe.value=e),title:"选择报价单",width:"800px","destroy-on-close":""},{footer:p((()=>[_("div",W,[c(t,{onClick:a[17]||(a[17]=e=>fe.value=!1)},{default:p((()=>a[42]||(a[42]=[v("取消")]))),_:1}),c(t,{type:"primary",onClick:je},{default:p((()=>a[43]||(a[43]=[v("确定")]))),_:1})])])),default:p((()=>[_("div",Q,[c($,{title:"请选择一个已确认状态的报价单，一个订单只能关联一个报价单",type:"info","show-icon":"",closable:!1})]),c(P,{inline:!0,class:"mb-20"},{default:p((()=>[c(r,{label:"报价单号"},{default:p((()=>[c(o,{modelValue:ge.quotation_number,"onUpdate:modelValue":a[14]||(a[14]=e=>ge.quotation_number=e),placeholder:"请输入报价单号",clearable:"",onKeyup:y(xe,["enter"])},null,8,["modelValue"])])),_:1}),c(r,{label:"客户名称"},{default:p((()=>[c(o,{modelValue:ge.customer_name,"onUpdate:modelValue":a[15]||(a[15]=e=>ge.customer_name=e),placeholder:"请输入客户名称",clearable:"",onKeyup:y(xe,["enter"])},null,8,["modelValue"])])),_:1}),c(r,null,{default:p((()=>[c(t,{type:"primary",onClick:xe},{default:p((()=>[c(S,null,{default:p((()=>[c(h(q))])),_:1}),a[39]||(a[39]=v(" 搜索 "))])),_:1}),c(t,{onClick:Ge},{default:p((()=>[c(S,null,{default:p((()=>[c(h(j))])),_:1}),a[40]||(a[40]=v(" 重置 "))])),_:1})])),_:1})])),_:1}),c(E,{ref_key:"quotationTableRef",ref:we,data:he.value,border:"","highlight-current-row":"",onCurrentChange:qe},{default:p((()=>[c(N,{width:"55"},{default:p((({row:e})=>[c(z,{modelValue:be.value,"onUpdate:modelValue":a[16]||(a[16]=e=>be.value=e),label:e.id,onChange:()=>qe(e)},{default:p((()=>a[41]||(a[41]=[v(" ")]))),_:2},1032,["modelValue","label","onChange"])])),_:1}),c(N,{prop:"quotation_number",label:"报价单号",width:"150"}),c(N,{label:"客户名称","min-width":"180"},{default:p((({row:e})=>[v(m(e.customer_name||""),1)])),_:1}),c(N,{prop:"project_name",label:"项目名称","min-width":"180"}),c(N,{prop:"total_amount",label:"总金额",width:"120"},{default:p((({row:e})=>[v(m(h(O)(e.total_amount||0)),1)])),_:1}),c(N,{prop:"valid_until",label:"有效期至",width:"120"}),c(N,{prop:"status",label:"状态",width:"100"},{default:p((({row:e})=>[c(D,{type:"已确认"===e.status?"success":"info"},{default:p((()=>[v(m(e.status),1)])),_:2},1032,["type"])])),_:1})])),_:1},8,["data"])])),_:1},8,["modelValue"]),c(oe,{modelValue:Ue.value,"onUpdate:modelValue":a[23]||(a[23]=e=>Ue.value=e),title:"选择产品",width:"800px","destroy-on-close":""},{footer:p((()=>[_("div",ee,[c(t,{onClick:a[22]||(a[22]=e=>Ue.value=!1)},{default:p((()=>a[45]||(a[45]=[v("取消")]))),_:1}),c(t,{type:"primary",onClick:ze},{default:p((()=>a[46]||(a[46]=[v("确定添加")]))),_:1})])])),default:p((()=>[_("div",X,[c(o,{modelValue:Ce.value,"onUpdate:modelValue":a[19]||(a[19]=e=>Ce.value=e),placeholder:"搜索产品名称或型号",clearable:"",onInput:Ee},{append:p((()=>[c(t,{icon:h(q),onClick:Ee},null,8,["icon"])])),_:1},8,["modelValue"])]),c(E,{data:ke.value,border:"",style:{width:"100%"},onSelectionChange:$e},{default:p((()=>[c(N,{type:"selection",width:"55"}),c(N,{prop:"name",label:"产品名称","min-width":"150","show-overflow-tooltip":""}),c(N,{prop:"model",label:"型号",width:"120","show-overflow-tooltip":""}),c(N,{label:"规格","min-width":"180"},{default:p((({row:e})=>[e.specifications&&e.specifications.length>0?(u(),s("div",G,[c(ie,{modelValue:e.selectedSpecIds,"onUpdate:modelValue":a=>e.selectedSpecIds=a},{default:p((()=>[(u(!0),s(f,null,g(e.specifications,(e=>(u(),s("div",{key:e.id,style:{"margin-bottom":"5px"}},[c(re,{label:e.id},{default:p((()=>[v(m(e.specification)+" (¥"+m(e.suggested_price||e.price||0)+")",1)])),_:2},1032,["label"])])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])):(u(),b(I,{key:1,description:"无规格信息","image-size":40}))])),_:1}),c(N,{prop:"unit",label:"单位",width:"80"})])),_:1},8,["data"]),_("div",Z,[a[44]||(a[44]=_("span",null,null,-1)),c(ue,{"current-page":Ne.currentPage,"onUpdate:currentPage":a[20]||(a[20]=e=>Ne.currentPage=e),"page-size":Ne.pageSize,"onUpdate:pageSize":a[21]||(a[21]=e=>Ne.pageSize=e),total:Ne.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next",onSizeChange:Ie,onCurrentChange:Pe},null,8,["current-page","page-size","total"])])])),_:1},8,["modelValue"]),c(oe,{modelValue:Ye.value,"onUpdate:modelValue":a[25]||(a[25]=e=>Ye.value=e),title:"Excel导入产品",width:"500px","destroy-on-close":""},{footer:p((()=>[_("div",le,[c(t,{onClick:a[24]||(a[24]=e=>Ye.value=!1)},{default:p((()=>a[50]||(a[50]=[v("取消")]))),_:1}),c(t,{type:"primary",onClick:Ke,disabled:!Me.value},{default:p((()=>a[51]||(a[51]=[v("导入")]))),_:1},8,["disabled"])])])),default:p((()=>[_("div",ae,[c($,{title:"请上传按照模板格式的Excel文件，文件中必须包含产品名称、型号、规格、单位、数量和单价字段",type:"info","show-icon":"",closable:!1})]),c(Se,{class:"excel-uploader",action:"#","auto-upload":!1,"on-change":Be,"on-remove":Fe,"file-list":Te.value,limit:1,accept:".xlsx,.xls"},{tip:p((()=>[_("div",te,[a[49]||(a[49]=v(" 仅支持 .xlsx, .xls 格式的Excel文件，")),c(ye,{type:"primary",onClick:He},{default:p((()=>a[48]||(a[48]=[v("下载模板")]))),_:1})])])),default:p((()=>[c(t,{type:"primary"},{default:p((()=>a[47]||(a[47]=[v("选择文件")]))),_:1})])),_:1},8,["file-list"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-7bf19b31"]]);export{oe as default};
