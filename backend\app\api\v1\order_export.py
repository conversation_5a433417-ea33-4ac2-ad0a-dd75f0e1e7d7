"""
订单导出功能API
提供订单导出、批量导出等功能
基于原项目API实现，确保与现有数据库结构100%兼容
"""
from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
from sqlalchemy.orm import selectinload
from datetime import datetime
import pandas as pd
from io import BytesIO

from app.models.order import Order, OrderProduct
from app.models.customer import Customer
from app.models.product import Product
from app.utils.response import (
    success_response, 
    error_response, 
    not_found_response
)
from app import db

# 创建命名空间
api = Namespace('order-export', description='订单导出功能API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 定义API模型用于Swagger文档
export_config_model = api.model('ExportConfig', {
    'format': fields.String(description='导出格式', enum=['excel', 'csv'], default='excel'),
    'include_products': fields.Boolean(description='是否包含产品明细', default=True),
    'customer_ids': fields.List(fields.Integer, description='客户ID列表'),
    'start_date': fields.String(description='开始日期 (YYYY-MM-DD)'),
    'end_date': fields.String(description='结束日期 (YYYY-MM-DD)')
})

batch_export_model = api.model('BatchExport', {
    'order_ids': fields.List(fields.Integer, description='订单ID列表', required=True),
    'format': fields.String(description='导出格式', enum=['excel', 'csv'], default='excel'),
    'include_products': fields.Boolean(description='是否包含产品明细', default=True)
})


@api.route('/export')
class OrderExport(Resource):
    @api.doc('export_orders',
             body=export_config_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """导出订单列表"""
        try:
            data = request.get_json() or {}
            
            # 获取导出配置
            export_format = data.get('format', 'excel').lower()
            include_products = data.get('include_products', True)
            customer_ids = data.get('customer_ids', [])
            start_date = data.get('start_date', '')
            end_date = data.get('end_date', '')

            # 构建查询
            query = Order.query.options(
                selectinload(Order.customer),
                selectinload(Order.order_products).selectinload(OrderProduct.product)
            )

            if customer_ids:
                query = query.filter(Order.customer_id.in_(customer_ids))
            
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(Order.order_date >= start)
                except ValueError:
                    return make_response(error_response, "开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            if end_date:
                try:
                    end = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(Order.order_date <= end)
                except ValueError:
                    return make_response(error_response, "结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            orders = query.order_by(Order.order_date.desc()).all()

            if not orders:
                return make_response(error_response, "没有符合条件的订单可导出", code=404)

            # 准备导出数据
            export_data = []
            
            if include_products:
                # 包含产品明细的导出
                for order in orders:
                    base_data = {
                        '订单编号': order.order_number,
                        '客户名称': order.customer.name if order.customer else '',
                        '订单日期': order.order_date.strftime('%Y-%m-%d %H:%M:%S') if order.order_date else '',
                        '交货日期': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else '',
                        '订单状态': order.order_status,
                        '订单总额': float(order.total_amount) if order.total_amount else 0,
                        '已付金额': float(order.paid_amount) if order.paid_amount else 0,
                        '备注': order.notes or '',
                        '创建时间': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else ''
                    }
                    
                    if order.order_products:
                        for order_product in order.order_products:
                            product_data = base_data.copy()
                            product_data.update({
                                '产品名称': order_product.product.name if order_product.product else '',
                                '产品型号': order_product.product.model if order_product.product else '',
                                '规格': order_product.specification or '',
                                '数量': order_product.quantity,
                                '单价': float(order_product.unit_price) if order_product.unit_price else 0,
                                '小计': float(order_product.subtotal) if order_product.subtotal else 0
                            })
                            export_data.append(product_data)
                    else:
                        # 没有产品明细的订单
                        base_data.update({
                            '产品名称': '',
                            '产品型号': '',
                            '规格': '',
                            '数量': 0,
                            '单价': 0,
                            '小计': 0
                        })
                        export_data.append(base_data)
            else:
                # 只导出订单基本信息
                for order in orders:
                    order_data = {
                        '订单编号': order.order_number,
                        '客户名称': order.customer.name if order.customer else '',
                        '订单日期': order.order_date.strftime('%Y-%m-%d %H:%M:%S') if order.order_date else '',
                        '交货日期': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else '',
                        '订单状态': order.order_status,
                        '订单总额': float(order.total_amount) if order.total_amount else 0,
                        '已付金额': float(order.paid_amount) if order.paid_amount else 0,
                        '产品数量': len(order.order_products) if order.order_products else 0,
                        '备注': order.notes or '',
                        '创建时间': order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else ''
                    }
                    export_data.append(order_data)

            # 创建DataFrame
            df = pd.DataFrame(export_data)

            # 根据格式导出
            if export_format == 'csv':
                output = BytesIO()
                df.to_csv(output, index=False, encoding='utf-8-sig')
                output.seek(0)
                
                return send_file(
                    output,
                    mimetype='text/csv',
                    as_attachment=True,
                    download_name=f'订单导出_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
                )
            else:
                # Excel格式
                output = BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='订单列表')
                output.seek(0)

                return send_file(
                    output,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f'订单导出_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                )

        except Exception as e:
            current_app.logger.error(f"导出订单失败: {str(e)}")
            return make_response(error_response, f"导出订单失败: {str(e)}")


@api.route('/<int:order_id>/export')
class OrderDetailExport(Resource):
    @api.doc('export_order_detail')
    @api.param('format', '导出格式', enum=['xlsx', 'pdf'], default='xlsx')
    @api.param('columns', '导出列（逗号分隔）')
    @api.param('include_header', '是否包含表头', type='boolean', default=True)
    def get(self, order_id):
        """导出单个订单详情"""
        try:
            order = Order.query.options(
                selectinload(Order.customer),
                selectinload(Order.products).selectinload(OrderProduct.product_specification).selectinload('product')
            ).get(order_id)

            if not order:
                return make_response(not_found_response, "订单不存在")

            # 获取请求参数
            format_type = request.args.get('format', 'xlsx')
            columns = request.args.get('columns', '').split(',') if request.args.get('columns') else None
            include_header = request.args.get('include_header', 'true').lower() == 'true'

            # 列名映射
            column_mapping = {
                # 基本信息字段
                'order_number': '订单编号',
                'project_name': '项目名称',
                'project_address': '项目地址',
                'customer_name': '客户名称',
                'expected_date': '预期时间',
                'order_status': '订单状态',
                'payment_status': '付款状态',
                'total_amount': '订单总额',
                'paid_amount': '已付金额',
                'payment_terms': '付款条件',
                'delivery_terms': '交货条件',
                'order_notes': '订单备注',
                # 产品明细字段
                'product_name': '产品名称',
                'product_model': '产品型号',
                'specification_name': '规格名称',
                'quantity': '数量',
                'unit': '单位',
                'unit_price': '单价',
                'discount': '折扣',
                'tax_rate': '税率',
                'total_price': '小计',
                'product_notes': '产品备注'
            }

            # 准备基本信息数据
            basic_info = {
                'order_number': order.order_number,
                'project_name': order.project_name or '',
                'project_address': order.project_address or '',
                'customer_name': order.customer.name if order.customer else '',
                'expected_date': str(order.expected_date) if order.expected_date else '',
                'order_status': order.order_status or '',
                'payment_status': order.payment_status or '',
                'total_amount': f"{float(order.total_amount):.2f}" if order.total_amount else '0.00',
                'paid_amount': f"{float(order.paid_amount):.2f}" if order.paid_amount else '0.00',
                'payment_terms': order.payment_terms or '',
                'delivery_terms': order.delivery_terms or '',
                'order_notes': order.notes or ''
            }

            # 字段分类
            basic_fields = ['order_number', 'project_name', 'project_address', 'customer_name', 'expected_date', 'order_status', 'payment_status', 'total_amount', 'paid_amount', 'payment_terms', 'delivery_terms', 'order_notes']
            product_fields = ['product_name', 'product_model', 'specification_name', 'quantity', 'unit', 'unit_price', 'discount', 'tax_rate', 'total_price', 'product_notes']

            # 分离请求的字段
            requested_columns = columns or []
            basic_columns = [col for col in requested_columns if col in basic_fields]
            product_columns = [col for col in requested_columns if col in product_fields]

            # 准备基本信息数据（只有一行）
            basic_data = []
            if basic_columns:
                basic_row = {}
                for col in basic_columns:
                    if col in basic_info:
                        basic_row[col] = basic_info[col]
                basic_data.append(basic_row)

            # 准备产品明细数据
            product_data = []
            for product in order.products:
                # 获取产品信息
                product_name = ''
                product_model = ''
                product_unit = ''
                specification_name = ''

                if product.product_specification:
                    specification_name = product.product_specification.specification or ''
                    if product.product_specification.product:
                        product_name = product.product_specification.product.name or ''
                        product_model = product.product_specification.product.model or ''
                        product_unit = product.product_specification.product.unit or ''

                product_row = {
                    'product_name': product_name,
                    'product_model': product_model,
                    'specification_name': specification_name,
                    'quantity': product.quantity or 0,
                    'unit': product_unit,
                    'unit_price': f"{float(product.unit_price):.2f}" if product.unit_price else '0.00',
                    'discount': f"{float(product.discount):.1f}%" if product.discount else '0.0%',
                    'tax_rate': f"{float(product.tax_rate):.1f}%" if product.tax_rate else '0.0%',
                    'total_price': f"{float(product.total_price):.2f}" if product.total_price else '0.00',
                    'product_notes': ''
                }

                # 只导出请求的产品字段
                if product_columns:
                    filtered_product_row = {}
                    for col in product_columns:
                        if col in product_row:
                            filtered_product_row[col] = product_row[col]
                    product_data.append(filtered_product_row)
                else:
                    product_data.append(product_row)

            # 导入BytesIO
            from io import BytesIO

            if format_type.lower() == 'xlsx':
                # Excel导出 - 分离布局
                output = BytesIO()

                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    current_row = 0

                    # 写入基本信息（如果有）
                    if basic_data:
                        basic_df = pd.DataFrame(basic_data)
                        if not basic_df.empty:
                            # 重命名基本信息列
                            basic_df = basic_df.rename(columns=column_mapping)
                            # 转置基本信息，使其垂直显示
                            basic_transposed = basic_df.T.reset_index()
                            basic_transposed.columns = ['项目', '内容']
                            basic_transposed.to_excel(writer, sheet_name='订单详情', index=False, startrow=current_row)
                            current_row += len(basic_transposed) + 3  # 基本信息行数 + 空行

                    # 写入产品明细（如果有）
                    if product_data:
                        product_df = pd.DataFrame(product_data)
                        if not product_df.empty:
                            # 重命名产品明细列
                            product_df = product_df.rename(columns=column_mapping)

                            # 添加产品明细标题
                            if basic_data:  # 如果有基本信息，添加产品明细标题
                                title_df = pd.DataFrame([['产品明细']], columns=[''])
                                title_df.to_excel(writer, sheet_name='订单详情', index=False, header=False, startrow=current_row)
                                current_row += 2

                            # 写入产品明细表格
                            product_df.to_excel(writer, sheet_name='订单详情', index=False, startrow=current_row)

                    # 如果没有任何数据，创建空表格
                    if not basic_data and not product_data:
                        empty_df = pd.DataFrame([['暂无数据']], columns=['提示'])
                        empty_df.to_excel(writer, sheet_name='订单详情', index=False)

                output.seek(0)

                filename = f"订单_{order.project_name or order.order_number}_{datetime.now().strftime('%Y%m%d')}.xlsx"

                return send_file(
                    output,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )

            elif format_type.lower() == 'pdf':
                # PDF导出
                from reportlab.lib.pagesizes import letter, A4
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib import colors
                from reportlab.lib.units import inch
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                import os
                import platform

                # 注册中文字体 - 使用系统字体
                font_name = 'Helvetica'  # 默认字体

                try:
                    system = platform.system()
                    if system == 'Windows':
                        # Windows系统字体路径
                        font_paths = [
                            'C:/Windows/Fonts/simhei.ttf',  # 黑体
                            'C:/Windows/Fonts/simsun.ttc',  # 宋体
                            'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑
                            'C:/Windows/Fonts/simkai.ttf',  # 楷体
                        ]
                    elif system == 'Darwin':  # macOS
                        font_paths = [
                            '/System/Library/Fonts/PingFang.ttc',
                            '/System/Library/Fonts/STHeiti Light.ttc',
                        ]
                    else:  # Linux
                        font_paths = [
                            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                            '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
                        ]

                    # 尝试加载中文字体
                    for font_path in font_paths:
                        if os.path.exists(font_path):
                            try:
                                pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                                font_name = 'ChineseFont'
                                print(f"成功加载中文字体: {font_path}")
                                break
                            except Exception as e:
                                print(f"加载字体失败 {font_path}: {str(e)}")
                                continue

                    # 如果没有找到合适的字体，尝试使用reportlab的内置中文字体
                    if font_name == 'Helvetica':
                        try:
                            from reportlab.pdfbase.cidfonts import UnicodeCIDFont
                            pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                            font_name = 'STSong-Light'
                            print("使用内置中文字体: STSong-Light")
                        except Exception as e:
                            print(f"无法加载内置中文字体: {str(e)}")
                            # 最后的备选方案：使用Helvetica但转换中文为拼音或英文
                            font_name = 'Helvetica'

                except Exception as e:
                    print(f"字体加载异常: {str(e)}")
                    # 如果无法加载中文字体，使用默认字体
                    font_name = 'Helvetica'

                output = BytesIO()
                doc = SimpleDocTemplate(output, pagesize=A4)

                # 创建样式
                styles = getSampleStyleSheet()
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontName=font_name,
                    fontSize=16,
                    alignment=1,  # 居中
                    spaceAfter=20
                )

                section_style = ParagraphStyle(
                    'SectionTitle',
                    parent=styles['Heading2'],
                    fontName=font_name,
                    fontSize=12,
                    alignment=0,  # 左对齐
                    spaceAfter=10,
                    spaceBefore=20
                )

                story = []

                # 添加标题
                title = Paragraph(f"订单详情 - {order.order_number}", title_style)
                story.append(title)
                story.append(Spacer(1, 20))

                # 添加基本信息部分
                if basic_data:
                    basic_section = Paragraph("基本信息", section_style)
                    story.append(basic_section)

                    # 创建基本信息表格（两列：项目名称和内容）
                    basic_table_data = []
                    if include_header:
                        basic_table_data.append(['项目', '内容'])

                    for item in basic_data:
                        for key, value in item.items():
                            display_key = column_mapping.get(key, key)
                            basic_table_data.append([display_key, str(value)])

                    if basic_table_data:
                        basic_table = Table(basic_table_data, colWidths=[2*inch, 4*inch])
                        basic_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey) if include_header else ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                            ('FONTNAME', (0, 0), (-1, -1), font_name),
                            ('FONTSIZE', (0, 0), (-1, -1), 10),
                            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                            ('TOPPADDING', (0, 0), (-1, -1), 6),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        story.append(basic_table)
                        story.append(Spacer(1, 20))

                # 添加产品明细部分
                if product_data:
                    product_section = Paragraph("产品明细", section_style)
                    story.append(product_section)

                    # 创建产品明细表格
                    product_df = pd.DataFrame(product_data)
                    if not product_df.empty:
                        product_df = product_df.rename(columns=column_mapping)

                        # 转换为表格数据
                        product_table_data = [product_df.columns.tolist()] if include_header else []
                        product_table_data.extend(product_df.values.tolist())

                        # 创建产品明细表格
                        product_table = Table(product_table_data)
                        product_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, -1), font_name),
                            ('FONTSIZE', (0, 0), (-1, -1), 8),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))

                        story.append(product_table)

                doc.build(story)
                output.seek(0)

                filename = f"订单_{order.project_name or order.order_number}_{datetime.now().strftime('%Y%m%d')}.pdf"

                return send_file(
                    output,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/pdf'
                )

            else:
                return make_response(error_response, "不支持的导出格式", code=400)

        except Exception as e:
            current_app.logger.error(f"导出订单详情失败: {str(e)}")
            return make_response(error_response, f"导出订单详情失败: {str(e)}")


@api.route('/batch/export')
class BatchOrderExport(Resource):
    @api.doc('batch_export_orders',
             body=batch_export_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """批量导出订单"""
        try:
            data = request.get_json() or {}
            order_ids = data.get('order_ids', [])
            export_format = data.get('format', 'excel').lower()
            include_products = data.get('include_products', True)

            if not order_ids:
                return make_response(error_response, "请提供要导出的订单ID列表", code=400)

            # 获取订单
            orders = Order.query.options(
                selectinload(Order.customer),
                selectinload(Order.order_products).selectinload(OrderProduct.product)
            ).filter(Order.id.in_(order_ids)).all()

            if not orders:
                return make_response(error_response, "没有找到指定的订单", code=404)

            # 准备导出数据（与export方法相同的逻辑）
            export_data = []
            
            if include_products:
                for order in orders:
                    base_data = {
                        '订单编号': order.order_number,
                        '客户名称': order.customer.name if order.customer else '',
                        '订单日期': order.order_date.strftime('%Y-%m-%d %H:%M:%S') if order.order_date else '',
                        '交货日期': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else '',
                        '订单状态': order.order_status,
                        '订单总额': float(order.total_amount) if order.total_amount else 0,
                        '已付金额': float(order.paid_amount) if order.paid_amount else 0,
                        '备注': order.notes or ''
                    }
                    
                    if order.order_products:
                        for order_product in order.order_products:
                            product_data = base_data.copy()
                            product_data.update({
                                '产品名称': order_product.product.name if order_product.product else '',
                                '产品型号': order_product.product.model if order_product.product else '',
                                '规格': order_product.specification or '',
                                '数量': order_product.quantity,
                                '单价': float(order_product.unit_price) if order_product.unit_price else 0,
                                '小计': float(order_product.subtotal) if order_product.subtotal else 0
                            })
                            export_data.append(product_data)
                    else:
                        base_data.update({
                            '产品名称': '', '产品型号': '', '规格': '',
                            '数量': 0, '单价': 0, '小计': 0
                        })
                        export_data.append(base_data)
            else:
                for order in orders:
                    order_data = {
                        '订单编号': order.order_number,
                        '客户名称': order.customer.name if order.customer else '',
                        '订单日期': order.order_date.strftime('%Y-%m-%d %H:%M:%S') if order.order_date else '',
                        '交货日期': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else '',
                        '订单状态': order.order_status,
                        '订单总额': float(order.total_amount) if order.total_amount else 0,
                        '已付金额': float(order.paid_amount) if order.paid_amount else 0,
                        '产品数量': len(order.order_products) if order.order_products else 0,
                        '备注': order.notes or ''
                    }
                    export_data.append(order_data)

            df = pd.DataFrame(export_data)

            # 根据格式导出
            if export_format == 'csv':
                output = BytesIO()
                df.to_csv(output, index=False, encoding='utf-8-sig')
                output.seek(0)
                
                return send_file(
                    output,
                    mimetype='text/csv',
                    as_attachment=True,
                    download_name=f'批量订单导出_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
                )
            else:
                output = BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='订单列表')
                output.seek(0)

                return send_file(
                    output,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f'批量订单导出_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                )

        except Exception as e:
            current_app.logger.error(f"批量导出订单失败: {str(e)}")
            return make_response(error_response, f"批量导出订单失败: {str(e)}")
