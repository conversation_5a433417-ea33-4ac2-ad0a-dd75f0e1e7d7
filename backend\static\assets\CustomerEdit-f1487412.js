import{J as a,u as e,b as s,r as l,d as r,e as t,f as u,o,c as i,i as n,h as c,a as d,t as m,k as v,U as f}from"./index-3d4c440c.js";import{C as p}from"./CustomerForm-dce303b8.js";import{d as _,l as y,k as g}from"./customer-471ca075.js";import{_ as C}from"./_plugin-vue_export-helper-1b428a4d.js";import"./CustomerDeliveryAddresses-9519c02d.js";const b={class:"customer-edit"},h={class:"flex-between"},k={class:"page-title"},w={class:"action-buttons mt-20"},j=C(a({__name:"CustomerEdit",setup(a){const C=e(),j=s(),x=l(null),F=l(!1),D=l(!1),R=r((()=>C.params.id?Number(C.params.id):null)),A=r((()=>!!R.value)),E=r((()=>A.value?"编辑客户":"新增客户")),I=l(null),J=async()=>{if(!x.value)return;if(!(await x.value.validate()))return void f.warning("表单填写有误，请检查");D.value=!0;const a=x.value.getFormData();try{A.value&&R.value?(await y(R.value,a),f.success("客户更新成功")):(await g(a),f.success("客户创建成功")),L()}catch(e){console.error("保存客户失败:",e),f.error(e.message||"保存客户失败")}finally{D.value=!1}},L=()=>{j.push({name:"CustomerList"})};return t((()=>{(async()=>{if(A.value&&R.value){F.value=!0;try{I.value=await _(R.value)}catch(a){console.error("获取客户信息失败:",a),f.error("获取客户信息失败")}finally{F.value=!1}}})()})),(a,e)=>{const s=u("el-button"),l=u("el-card");return o(),i("div",b,[n(l,{class:"header-card mb-20"},{default:c((()=>[d("div",h,[d("h2",k,m(E.value),1),d("div",null,[n(s,{onClick:L},{default:c((()=>e[0]||(e[0]=[v("返回")]))),_:1}),n(s,{type:"primary",onClick:J,loading:D.value},{default:c((()=>e[1]||(e[1]=[v("保存")]))),_:1},8,["loading"])])])])),_:1}),n(p,{ref_key:"customerFormRef",ref:x,"customer-data":I.value,loading:F.value},null,8,["customer-data","loading"]),d("div",w,[n(s,{type:"primary",onClick:J,loading:D.value},{default:c((()=>e[2]||(e[2]=[v("保存")]))),_:1},8,["loading"]),n(s,{onClick:L},{default:c((()=>e[3]||(e[3]=[v("取消")]))),_:1})])])}}}),[["__scopeId","data-v-5be6ebc1"]]);export{j as default};
