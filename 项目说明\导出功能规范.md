# EMB项目导出功能开发规范

## 概述

本规范基于报价需求表导出功能的开发经验，为EMB项目中其他模块的导出功能开发提供标准化指导。通过遵循此规范，可以确保所有导出功能具有一致的用户体验、代码结构和技术实现。

## 1. 功能设计原则

### 1.1 用户体验原则
- **一致性**：所有模块的导出功能应保持界面和交互的一致性
- **灵活性**：支持多种导出格式和自定义配置选项
- **易用性**：提供直观的操作界面和清晰的用户反馈
- **响应性**：支持不同的页面布局模式（详细模式、简化模式等）

### 1.2 技术实现原则
- **组件化**：创建可复用的导出组件，避免重复开发
- **模块化**：前后端分离，API设计规范化
- **可扩展性**：支持新增导出格式和配置选项
- **性能优化**：合理处理大数据量导出

## 2. 组件架构设计

### 2.1 前端组件结构
```
components/
├── [ModuleName]ExportDialog.vue     # 主导出对话框组件
├── ExportFormatSelector.vue         # 导出格式选择器（可选，复用）
└── ExportColumnSelector.vue         # 导出列选择器（可选，复用）
```

### 2.2 组件命名规范
- 主组件：`[ModuleName]ExportDialog.vue`
- 例如：`QuotationRequestExportDialog.vue`、`OrderExportDialog.vue`

### 2.3 组件Props设计
```javascript
// 必需Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
    description: '对话框显示状态'
  },
  [entityId]: {
    type: [String, Number],
    required: true,
    description: '要导出的实体ID'
  },
  [entityName]: {
    type: String,
    default: '',
    description: '实体名称，用于文件命名'
  }
})

// 可选Props
const optionalProps = {
  batchMode: {
    type: Boolean,
    default: false,
    description: '是否为批量导出模式'
  },
  selectedIds: {
    type: Array,
    default: () => [],
    description: '批量导出时的ID列表'
  }
}
```

## 3. 界面设计规范

### 3.1 对话框布局
```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    title="导出[模块名称]配置"
    width="600px"
    :close-on-click-modal="false"
  >
    <!-- 导出格式选择 -->
    <el-form-item label="导出格式">
      <el-radio-group v-model="exportConfig.format">
        <el-radio value="xlsx">Excel (XLSX)</el-radio>
        <el-radio value="pdf">PDF</el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 表头控制 -->
    <el-form-item label="包含表头">
      <el-switch v-model="exportConfig.includeHeader" />
    </el-form-item>

    <!-- 导出列选择 -->
    <el-form-item label="导出列">
      <div class="export-columns-container">
        <!-- 总全选控制 -->
        <div class="select-all-container">
          <el-checkbox v-model="exportConfig.selectAll" @change="handleSelectAllChange" class="select-all-checkbox">
            全选
          </el-checkbox>
        </div>

        <!-- 分类显示 -->
        <div class="columns-grid">
          <div class="column-category" v-for="category in columnCategories" :key="category.name">
            <!-- 分类标题和分类全选 -->
            <div class="category-header">
              <div class="category-title">{{ category.title }}</div>
              <el-checkbox v-model="category.selectAll" @change="handleCategorySelectAllChange(category)" class="category-select-all">
                全选
              </el-checkbox>
            </div>
            <!-- 分类字段列表 -->
            <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
              <el-checkbox v-for="column in category.columns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                {{ column.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </el-form-item>

    <!-- 操作按钮 -->
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleExport">确认导出</el-button>
    </template>
  </el-dialog>
</template>
```

### 3.2 样式规范
```scss
<style scoped>
.export-columns-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.select-all-container {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.select-all-checkbox {
  margin-bottom: 0;
}

.columns-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.column-category {
  margin-bottom: 16px;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.category-title {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.category-select-all {
  font-size: 12px;
  color: #409eff;
}

.column-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 20px;
}

.column-checkboxes.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 16px;
  padding-left: 20px;
}

.column-checkboxes .el-checkbox {
  margin-right: 0;
}
</style>
```

## 4. 数据结构规范

### 4.1 导出配置数据结构
```javascript
const exportConfig = reactive({
  format: 'xlsx',                    // 导出格式：'xlsx' | 'pdf'
  includeHeader: true,               // 是否包含表头
  selectedColumns: [],               // 选中的列
  selectAll: true                    // 总全选状态
})

// 分类全选状态
const basicSelectAll = ref(true)     // 基本信息分类全选状态
const itemSelectAll = ref(true)      // 产品明细分类全选状态
```

### 4.2 列定义数据结构
```javascript
const availableColumns = [
  // 基本信息字段
  { prop: 'field1', label: '字段1', category: 'basic' },
  { prop: 'field2', label: '字段2', category: 'basic' },

  // 详细信息字段
  { prop: 'field3', label: '字段3', category: 'details' },
  { prop: 'field4', label: '字段4', category: 'details' }
]

// 分类计算属性
const basicColumns = computed(() => availableColumns.filter(col => col.category === 'basic'))
const detailColumns = computed(() => availableColumns.filter(col => col.category === 'details'))
```

## 5. 后端API设计规范

### 5.1 API端点命名
```
GET /api/v1/[module]/[entity-id]/export
```
例如：`GET /api/v1/quotations/requests/1/export`

### 5.2 请求参数
```javascript
// Query Parameters
{
  format: 'xlsx',                    // 导出格式
  columns: 'field1,field2,field3',   // 导出列（逗号分隔）
  include_header: true               // 是否包含表头
}
```

### 5.3 响应格式
```python
# 成功响应
return send_file(
    file_path,
    as_attachment=True,
    download_name=filename,
    mimetype=mimetype
)

# 错误响应
{
    "error": "错误信息",
    "code": "ERROR_CODE"
}
```

### 5.4 后端实现模板
```python
@bp.route('/<int:entity_id>/export')
def export_entity(entity_id):
    try:
        # 1. 获取请求参数
        format_type = request.args.get('format', 'xlsx')
        columns = request.args.get('columns', '').split(',') if request.args.get('columns') else None
        include_header = request.args.get('include_header', 'true').lower() == 'true'

        # 2. 查询数据
        entity = EntityModel.query.get_or_404(entity_id)

        # 3. 准备导出数据
        export_data = prepare_export_data(entity, columns)

        # 4. 生成文件
        if format_type == 'xlsx':
            file_path = generate_excel_file(export_data, include_header)
            mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif format_type == 'pdf':
            file_path = generate_pdf_file(export_data, include_header)
            mimetype = 'application/pdf'

        # 5. 返回文件
        filename = f"{entity.name}_{entity.id}.{format_type}"
        return send_file(file_path, as_attachment=True, download_name=filename, mimetype=mimetype)

    except Exception as e:
        return jsonify({'error': str(e)}), 500
```

### 5.5 列名映射规范
```python
# 列名映射
column_mapping = {
    # 基本信息字段
    'field1': '字段1显示名',
    'field2': '字段2显示名',

    # 详细信息字段
    'field3': '字段3显示名',
    'field4': '字段4显示名'
}
```

### 5.6 数据准备函数模板
```python
def prepare_export_data(entity, columns=None):
    """准备导出数据"""
    # 基本信息数据
    basic_info = {
        'field1': entity.field1 or '',
        'field2': entity.field2 or '',
    }

    # 检查是否需要导出基本信息字段
    basic_fields = ['field1', 'field2']
    has_basic_fields = columns and any(col in basic_fields for col in columns)

    # 准备导出数据
    export_data = []

    for item in entity.items:
        row_data = {
            'field3': item.field3 or '',
            'field4': item.field4 or '',
        }

        # 如果需要基本信息字段，添加到每行数据中
        if has_basic_fields:
            row_data.update(basic_info)

        # 如果指定了列，只导出指定的列
        if columns and columns != ['']:
            filtered_data = {}
            for col in columns:
                if col in row_data:
                    filtered_data[col] = row_data[col]
            export_data.append(filtered_data)
        else:
            export_data.append(row_data)

    return export_data
```

## 6. 列表页面集成规范

### 6.1 导入组件
```javascript
import [ModuleName]ExportDialog from '@/components/[ModuleName]ExportDialog.vue'
```

### 6.2 状态管理
```javascript
// 导出相关状态
const exportDialogVisible = ref(false)
const currentExportEntity = ref(null)
const batchExportDialogVisible = ref(false)
```

### 6.3 详细模式按钮集成
```vue
<!-- 在操作按钮区域添加导出按钮 -->
<el-button type="info" size="small" @click="handleExport(entity)" title="导出">
  <el-icon><Download /></el-icon>
  导出
</el-button>
```

### 6.4 简化模式下拉菜单集成
```vue
<el-dropdown-item @click="handleExport(entity)" divided>
  <el-icon><Download /></el-icon>
  导出[模块名称]
</el-dropdown-item>
```

### 6.5 批量导出功能
```javascript
// 批量导出函数
const handleBatchExport = () => {
  if (selectedEntities.value.length === 0) {
    ElMessage.warning('请先选择要导出的项目')
    return
  }

  if (selectedEntities.value.length === 1) {
    handleExport(selectedEntities.value[0])
  } else {
    // 处理多选导出逻辑
    ElMessage.info(`已选择${selectedEntities.value.length}个项目，当前版本仅支持单个导出`)
    handleExport(selectedEntities.value[0])
  }
}
```

### 6.6 导出函数实现
```javascript
// 单个导出函数
const handleExport = (entity) => {
  currentExportEntity.value = entity
  exportDialogVisible.value = true
}

// 导出成功处理
const handleExportSuccess = () => {
  currentExportEntity.value = null
}

// 批量导出成功处理
const handleBatchExportSuccess = () => {
  clearSelection()
}

// 总全选处理
const handleSelectAllChange = (val) => {
  if (val) {
    exportConfig.selectedColumns = availableColumns.map(col => col.prop)
    basicSelectAll.value = true
    itemSelectAll.value = true
  } else {
    exportConfig.selectedColumns = []
    basicSelectAll.value = false
    itemSelectAll.value = false
  }
}

// 基本信息分类全选处理
const handleBasicSelectAllChange = (val) => {
  const basicProps = basicColumns.value.map(col => col.prop)
  if (val) {
    // 添加基本信息字段到选中列表
    const newSelectedColumns = [...exportConfig.selectedColumns]
    basicProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    // 从选中列表中移除基本信息字段
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !basicProps.includes(col))
  }
  updateSelectAllStatus()
}

// 产品明细分类全选处理
const handleItemSelectAllChange = (val) => {
  const itemProps = itemColumns.value.map(col => col.prop)
  if (val) {
    // 添加产品明细字段到选中列表
    const newSelectedColumns = [...exportConfig.selectedColumns]
    itemProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    // 从选中列表中移除产品明细字段
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !itemProps.includes(col))
  }
  updateSelectAllStatus()
}

// 更新全选状态
const updateSelectAllStatus = () => {
  const basicProps = basicColumns.value.map(col => col.prop)
  const itemProps = itemColumns.value.map(col => col.prop)

  // 更新分类全选状态
  basicSelectAll.value = basicProps.every(prop => exportConfig.selectedColumns.includes(prop))
  itemSelectAll.value = itemProps.every(prop => exportConfig.selectedColumns.includes(prop))

  // 更新总全选状态
  exportConfig.selectAll = exportConfig.selectedColumns.length === availableColumns.length
}

// 列变化处理
const handleColumnChange = () => {
  updateSelectAllStatus()
}
```

## 7. 文件命名规范

### 7.1 文件名格式
```
[模块名称]_[实体标识].[扩展名]
```

### 7.2 示例
- 报价需求表：`报价需求表_都是.xlsx`
- 订单：`订单_ORD20250101001.pdf`
- 客户信息：`客户信息_华为技术有限公司.xlsx`

### 7.3 文件名生成函数
```javascript
// 前端文件名生成
const generateFileName = (entityName, entityId, format) => {
  const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '')
  return `${moduleName}_${entityName || entityId}_${timestamp}.${format}`
}
```

```python
# 后端文件名生成
def generate_filename(entity_name, entity_id, format_type):
    """生成导出文件名"""
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d')
    safe_name = entity_name.replace('/', '_').replace('\\', '_') if entity_name else str(entity_id)
    return f"{MODULE_NAME}_{safe_name}_{timestamp}.{format_type}"
```

## 8. 错误处理规范

### 8.1 前端错误处理
```javascript
const handleExport = async () => {
  try {
    loading.value = true
    const response = await api.exportEntity(entityId, exportConfig)

    // 处理文件下载
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.click()

    ElMessage.success('导出成功')
    dialogVisible.value = false

  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    loading.value = false
  }
}
```

### 8.2 后端错误处理
```python
try:
    # 导出逻辑
    pass
except FileNotFoundError:
    return jsonify({'error': '文件未找到'}), 404
except PermissionError:
    return jsonify({'error': '权限不足'}), 403
except Exception as e:
    logger.error(f"导出失败: {str(e)}")
    return jsonify({'error': '导出失败，请联系管理员'}), 500
```

### 8.3 常见错误类型
- **数据不存在**：实体ID无效或已删除
- **权限不足**：用户无权限导出该数据
- **格式不支持**：请求的导出格式不支持
- **数据过大**：导出数据量超过限制
- **系统错误**：服务器内部错误

### 8.4 PDF中文字体问题处理

**问题描述**：PDF导出时中文字符显示为黑色方块（乱码）

**解决方案**：实现智能中文字体检测和加载机制

```python
# PDF中文字体处理
import platform
import os
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

def setup_chinese_font():
    """设置中文字体"""
    font_name = 'Helvetica'  # 默认字体

    try:
        system = platform.system()
        if system == 'Windows':
            # Windows系统字体路径
            font_paths = [
                'C:/Windows/Fonts/simhei.ttf',  # 黑体
                'C:/Windows/Fonts/simsun.ttc',  # 宋体
                'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑
                'C:/Windows/Fonts/simkai.ttf',  # 楷体
            ]
        elif system == 'Darwin':  # macOS
            font_paths = [
                '/System/Library/Fonts/PingFang.ttc',
                '/System/Library/Fonts/STHeiti Light.ttc',
            ]
        else:  # Linux
            font_paths = [
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
            ]

        # 尝试加载中文字体
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                    font_name = 'ChineseFont'
                    print(f"成功加载中文字体: {font_path}")
                    break
                except Exception as e:
                    print(f"加载字体失败 {font_path}: {str(e)}")
                    continue

        # 如果没有找到合适的字体，尝试使用reportlab的内置中文字体
        if font_name == 'Helvetica':
            try:
                from reportlab.pdfbase.cidfonts import UnicodeCIDFont
                pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                font_name = 'STSong-Light'
                print("使用内置中文字体: STSong-Light")
            except Exception as e:
                print(f"无法加载内置中文字体: {str(e)}")
                font_name = 'Helvetica'

    except Exception as e:
        print(f"字体加载异常: {str(e)}")
        font_name = 'Helvetica'

    return font_name
```

**关键要点**：
1. **多系统支持**：Windows、macOS、Linux系统的字体路径检测
2. **多字体备选**：按优先级尝试多种中文字体
3. **异常处理**：完善的字体加载异常处理机制
4. **调试信息**：添加字体加载成功/失败的日志输出
5. **降级策略**：系统字体 → 内置字体 → 默认字体

### 8.5 导出布局优化

**问题描述**：用户希望基本信息和产品明细分离显示，而不是混合在一个表格中

**解决方案**：实现分离式导出布局

#### Excel分离布局实现
```python
def generate_excel_with_separated_layout(basic_data, product_data, include_header=True):
    """生成分离布局的Excel文件"""
    output = BytesIO()

    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        current_row = 0

        # 写入基本信息（如果有）
        if basic_data:
            basic_df = pd.DataFrame(basic_data)
            if not basic_df.empty:
                # 重命名基本信息列
                basic_df = basic_df.rename(columns=column_mapping)
                # 转置基本信息，使其垂直显示
                basic_transposed = basic_df.T.reset_index()
                basic_transposed.columns = ['项目', '内容']
                basic_transposed.to_excel(writer, sheet_name='订单详情', index=False, startrow=current_row)
                current_row += len(basic_transposed) + 3  # 基本信息行数 + 空行

        # 写入产品明细（如果有）
        if product_data:
            product_df = pd.DataFrame(product_data)
            if not product_df.empty:
                # 重命名产品明细列
                product_df = product_df.rename(columns=column_mapping)

                # 添加产品明细标题
                if basic_data:  # 如果有基本信息，添加产品明细标题
                    title_df = pd.DataFrame([['产品明细']], columns=[''])
                    title_df.to_excel(writer, sheet_name='订单详情', index=False, header=False, startrow=current_row)
                    current_row += 2

                # 写入产品明细表格
                product_df.to_excel(writer, sheet_name='订单详情', index=False, startrow=current_row)

    return output
```

#### PDF分离布局实现
```python
def generate_pdf_with_separated_layout(basic_data, product_data, include_header=True):
    """生成分离布局的PDF文件"""
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib import colors
    from reportlab.lib.units import inch

    # 创建样式
    styles = getSampleStyleSheet()
    section_style = ParagraphStyle(
        'SectionTitle',
        parent=styles['Heading2'],
        fontName=font_name,
        fontSize=12,
        alignment=0,  # 左对齐
        spaceAfter=10,
        spaceBefore=20
    )

    story = []

    # 添加基本信息部分
    if basic_data:
        basic_section = Paragraph("基本信息", section_style)
        story.append(basic_section)

        # 创建基本信息表格（两列：项目名称和内容）
        basic_table_data = []
        if include_header:
            basic_table_data.append(['项目', '内容'])

        for item in basic_data:
            for key, value in item.items():
                display_key = column_mapping.get(key, key)
                basic_table_data.append([display_key, str(value)])

        if basic_table_data:
            basic_table = Table(basic_table_data, colWidths=[2*inch, 4*inch])
            basic_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey) if include_header else ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(basic_table)
            story.append(Spacer(1, 20))

    # 添加产品明细部分
    if product_data:
        product_section = Paragraph("产品明细", section_style)
        story.append(product_section)

        # 创建产品明细表格
        product_df = pd.DataFrame(product_data)
        if not product_df.empty:
            product_df = product_df.rename(columns=column_mapping)

            # 转换为表格数据
            product_table_data = [product_df.columns.tolist()] if include_header else []
            product_table_data.extend(product_df.values.tolist())

            # 创建产品明细表格
            product_table = Table(product_table_data)
            product_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(product_table)

    return story
```

**布局优化要点**：
1. **基本信息区域**：放在上方，采用键值对形式显示
2. **产品明细区域**：放在下方，采用表格形式显示
3. **分区标题**：添加明确的区域标题
4. **间距控制**：合理的区域间距
5. **格式一致**：Excel和PDF保持相同的布局逻辑

## 9. 性能优化建议

### 9.1 大数据量处理
- 实现分页导出或流式导出
- 添加导出进度提示
- 设置合理的超时时间

### 9.2 缓存策略
- 对频繁导出的数据进行缓存
- 实现导出任务队列

### 9.3 用户体验优化
- 添加导出进度条
- 支持后台导出和邮件通知

### 9.4 性能监控
```python
import time
from functools import wraps

def monitor_export_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(f"导出完成，耗时: {duration:.2f}秒")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"导出失败，耗时: {duration:.2f}秒，错误: {str(e)}")
            raise
    return wrapper
```

## 10. 测试规范

### 10.1 功能测试清单
- [ ] 单个导出功能
- [ ] 批量导出功能
- [ ] Excel格式导出
- [ ] PDF格式导出
- [ ] 列选择功能
- [ ] 表头控制功能
- [ ] 错误处理
- [ ] 文件下载
- [ ] 中文字符显示（PDF）
- [ ] 分离布局显示
- [ ] 字体加载验证

### 10.2 兼容性测试
- [ ] 不同浏览器兼容性
- [ ] 移动端适配
- [ ] 大文件导出性能
- [ ] 不同操作系统字体支持

### 10.3 测试用例模板
```javascript
// 前端测试用例
describe('导出功能测试', () => {
  test('应该能够打开导出对话框', () => {
    // 测试逻辑
  })

  test('应该能够选择导出格式', () => {
    // 测试逻辑
  })

  test('应该能够自定义导出列', () => {
    // 测试逻辑
  })

  test('应该能够正确处理分离布局', () => {
    // 测试基本信息和产品明细分离显示
  })
})
```

```python
# 后端测试用例
class TestExportAPI(unittest.TestCase):
    def test_export_xlsx(self):
        """测试Excel导出"""
        response = self.client.get('/api/v1/module/1/export?format=xlsx')
        self.assertEqual(response.status_code, 200)

    def test_export_pdf(self):
        """测试PDF导出"""
        response = self.client.get('/api/v1/module/1/export?format=pdf')
        self.assertEqual(response.status_code, 200)

    def test_pdf_chinese_font(self):
        """测试PDF中文字体加载"""
        response = self.client.get('/api/v1/module/1/export?format=pdf')
        self.assertEqual(response.status_code, 200)
        # 验证返回的PDF文件包含正确的中文字符

    def test_separated_layout(self):
        """测试分离布局导出"""
        response = self.client.get('/api/v1/module/1/export?format=xlsx&columns=order_number,product_name')
        self.assertEqual(response.status_code, 200)
        # 验证基本信息和产品明细分离显示
```

### 10.4 实际测试验证步骤

#### PDF中文字体测试
1. **测试步骤**：
   - 打开导出对话框
   - 选择PDF格式
   - 确认导出
   - 检查后端日志中的字体加载信息
   - 打开生成的PDF文件验证中文字符显示

2. **预期结果**：
   - 后端日志显示：`成功加载中文字体: C:/Windows/Fonts/simhei.ttf`
   - PDF文件中中文字符正常显示，无黑色方块
   - 导出成功提示正常显示

#### 分离布局测试
1. **测试步骤**：
   - 选择包含基本信息和产品明细的字段
   - 分别测试Excel和PDF导出
   - 验证文件内容布局

2. **预期结果**：
   - Excel：基本信息在上方以键值对形式显示，产品明细在下方以表格形式显示
   - PDF：基本信息和产品明细有明确的区域标题和分离
   - 布局清晰，符合业务单据格式

## 11. 部署和维护

### 11.1 依赖管理
确保安装必要的依赖包：

**前端依赖：**
```json
{
  "dependencies": {
    "element-plus": "^2.x.x",
    "vue": "^3.x.x"
  }
}
```

**后端依赖：**
```python
# requirements.txt
openpyxl>=3.0.0          # Excel文件处理
reportlab>=3.6.0         # PDF文件生成
flask>=2.0.0             # Web框架
```

### 11.2 配置管理
```python
# config.py
class Config:
    # 导出文件存储路径
    EXPORT_TEMP_DIR = os.environ.get('EXPORT_TEMP_DIR', '/tmp/exports')

    # 文件大小限制（MB）
    MAX_EXPORT_FILE_SIZE = int(os.environ.get('MAX_EXPORT_FILE_SIZE', 50))

    # 导出超时时间（秒）
    EXPORT_TIMEOUT = int(os.environ.get('EXPORT_TIMEOUT', 300))

    # 支持的导出格式
    SUPPORTED_EXPORT_FORMATS = ['xlsx', 'pdf']
```

### 11.3 日志配置
```python
import logging

# 配置导出专用日志
export_logger = logging.getLogger('export')
export_handler = logging.FileHandler('logs/export.log')
export_formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
export_handler.setFormatter(export_formatter)
export_logger.addHandler(export_handler)
export_logger.setLevel(logging.INFO)
```

### 11.4 定期清理
```python
import os
import time
from datetime import datetime, timedelta

def cleanup_export_files():
    """清理过期的导出文件"""
    export_dir = current_app.config['EXPORT_TEMP_DIR']
    cutoff_time = time.time() - (24 * 60 * 60)  # 24小时前

    for filename in os.listdir(export_dir):
        file_path = os.path.join(export_dir, filename)
        if os.path.getctime(file_path) < cutoff_time:
            try:
                os.remove(file_path)
                export_logger.info(f"清理过期文件: {filename}")
            except Exception as e:
                export_logger.error(f"清理文件失败: {filename}, 错误: {str(e)}")
```

## 12. 扩展指南

### 12.1 新增导出格式
1. **前端修改**：
```javascript
// 在导出格式选项中添加新格式
<el-radio value="csv">CSV</el-radio>
```

2. **后端实现**：
```python
elif format_type == 'csv':
    file_path = generate_csv_file(export_data, include_header)
    mimetype = 'text/csv'
```

3. **生成函数**：
```python
def generate_csv_file(data, include_header=True):
    import csv
    import tempfile

    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
    writer = csv.DictWriter(temp_file, fieldnames=data[0].keys())

    if include_header:
        writer.writeheader()

    writer.writerows(data)
    temp_file.close()

    return temp_file.name
```

### 12.2 新增导出字段
1. **前端字段定义**：
```javascript
const availableColumns = [
  // 添加新字段
  { prop: 'new_field', label: '新字段', category: 'basic' }
]
```

2. **后端映射更新**：
```python
column_mapping = {
    'new_field': '新字段显示名'
}
```

3. **数据准备逻辑**：
```python
row_data = {
    'new_field': item.new_field or '',
    # 其他字段...
}
```

### 12.3 自定义导出模板
```python
class ExportTemplate:
    def __init__(self, template_name):
        self.template_name = template_name
        self.columns = []
        self.format_rules = {}

    def add_column(self, prop, label, formatter=None):
        self.columns.append({
            'prop': prop,
            'label': label,
            'formatter': formatter
        })

    def apply_format(self, data):
        formatted_data = []
        for row in data:
            formatted_row = {}
            for col in self.columns:
                value = row.get(col['prop'], '')
                if col.get('formatter'):
                    value = col['formatter'](value)
                formatted_row[col['prop']] = value
            formatted_data.append(formatted_row)
        return formatted_data
```

## 13. 常见问题和解决方案

### 13.1 PDF中文字体问题

**问题现象**：PDF导出后中文字符显示为黑色方块

**原因分析**：
- PDF生成时未正确加载中文字体
- 系统中缺少合适的中文字体文件
- 字体路径配置错误

**解决步骤**：
1. 检查后端日志中的字体加载信息
2. 确认系统中存在中文字体文件
3. 按照8.4节的方案实现智能字体检测
4. 测试验证字体加载成功

**预防措施**：
- 在部署时确认目标系统的字体支持
- 添加字体加载状态的监控日志
- 提供字体文件的备选方案

### 13.2 导出布局混乱问题

**问题现象**：基本信息和产品明细混合显示，布局不清晰

**原因分析**：
- 数据结构设计不合理
- 前后端数据处理逻辑不一致
- 缺少明确的区域分离

**解决步骤**：
1. 重新设计数据结构，分离基本信息和明细数据
2. 按照8.5节的方案实现分离布局
3. 添加区域标题和合理间距
4. 统一Excel和PDF的布局逻辑

**预防措施**：
- 在设计阶段明确布局需求
- 建立统一的布局模板
- 定期进行布局效果验证

## 14. 最佳实践总结

### 14.1 开发最佳实践
1. **组件复用**：优先复用现有导出组件，保持一致性
2. **渐进增强**：先实现基础功能，再逐步添加高级特性
3. **用户反馈**：及时提供操作反馈和错误提示
4. **性能考虑**：合理处理大数据量导出
5. **布局优化**：基本信息和明细数据分离显示
6. **字体处理**：实现智能中文字体检测机制

### 14.2 代码质量
1. **命名规范**：遵循项目命名约定
2. **注释完整**：关键逻辑添加详细注释
3. **错误处理**：完善的异常处理机制
4. **测试覆盖**：编写充分的单元测试
5. **日志记录**：添加关键操作的日志输出
6. **监控告警**：建立导出功能的监控机制

### 14.3 用户体验
1. **界面一致**：保持与项目整体风格一致
2. **操作直观**：提供清晰的操作指引
3. **响应及时**：快速响应用户操作
4. **容错性强**：优雅处理各种异常情况
5. **布局清晰**：分离式布局提升可读性
6. **格式支持**：确保各种格式的正确显示
7. **分类全选**：提供总全选和分类全选功能，提升操作效率

### 14.4 测试验证
1. **功能测试**：全面测试各项导出功能
2. **兼容性测试**：验证不同系统和浏览器的兼容性
3. **性能测试**：测试大数据量导出的性能表现
4. **字体测试**：验证PDF中文字符的正确显示
5. **布局测试**：确认分离布局的正确实现
6. **回归测试**：确保新功能不影响现有功能

---

## 附录A：报价需求表导出功能实现参考

本规范基于以下实际实现：

- **组件文件**：`frontend/src/components/QuotationRequestExportDialog.vue`
- **API端点**：`GET /api/v1/quotations/requests/<id>/export`
- **列表集成**：`frontend/src/views/quotations/QuotationRequestList.vue`
- **详情集成**：`frontend/src/views/quotations/QuotationRequestDetail.vue`

## 附录B：订单导出功能实现参考

基于本规范完成的订单导出功能实现：

- **组件文件**：`frontend/src/components/OrderExportDialog.vue`
- **API端点**：`GET /api/v1/order-export/<id>/export`
- **详情集成**：`frontend/src/views/orders/OrderDetail.vue`
- **后端实现**：`backend/app/api/v1/order_export.py`

### 关键改进点

#### 1. PDF中文字体支持
- **问题**：PDF导出中文字符显示为黑色方块
- **解决**：实现智能中文字体检测机制
- **效果**：成功加载Windows系统黑体字体，中文字符正常显示

#### 2. 分离式布局设计
- **问题**：基本信息和产品明细混合显示，布局混乱
- **解决**：实现分离式布局，基本信息在上方，产品明细在下方
- **效果**：Excel和PDF都采用清晰的分区显示，符合业务单据格式

#### 3. 数据结构优化
- **改进**：将基本信息和产品明细数据分别处理
- **优势**：提高代码可维护性，支持灵活的布局配置

### 测试验证结果

#### PDF中文字体测试
- ✅ 后端日志显示：`成功加载中文字体: C:/Windows/Fonts/simhei.ttf`
- ✅ PDF文件中中文字符正常显示，无黑色方块
- ✅ 导出成功提示正常显示

#### 分离布局测试
- ✅ Excel：基本信息以键值对形式在上方显示，产品明细表格在下方
- ✅ PDF：基本信息和产品明细有明确的区域标题和分离
- ✅ 布局清晰，符合用户期望的业务单据格式

#### 功能完整性测试
- ✅ 导出对话框正常打开和关闭
- ✅ Excel (XLSX) 格式导出成功
- ✅ PDF格式导出成功
- ✅ 列选择功能正常工作
- ✅ 全选/取消全选功能正常
- ✅ 文件下载和命名正确

## 附录C：退货单导出功能实现参考

基于本规范完成的退货单导出功能实现：

- **组件文件**：`frontend/src/components/ReturnOrderExportDialog.vue`
- **API端点**：`GET /api/v1/return-export/<id>/export`
- **详情集成**：`frontend/src/views/returns/ReturnOrderDetail.vue`
- **后端实现**：`backend/app/api/v1/return_export.py`

## 附录D：对账单导出功能实现参考

基于本规范完成的对账单导出功能实现：

- **组件文件**：`frontend/src/components/StatementExportDialog.vue`
- **API端点**：`GET /api/v1/statement-export/<id>/export`
- **详情集成**：`frontend/src/views/statements/StatementDetail.vue`
- **后端实现**：`backend/app/api/v1/statement_export.py`

### 对账单导出功能特点

1. **三类数据分离导出**：
   - 基本信息：对账单号、客户名称、状态、金额等
   - 发货单明细：关联的发货单信息
   - 收款记录：收款历史记录

2. **字段值中文映射**：
   - 支付方式：`bank_transfer` → `银行转账`
   - 付款来源：`direct` → `直接付款`
   - 对账单状态：`partial_paid` → `部分收款`
   - 发货状态：`delivered` → `已签收`

3. **分类全选功能**：
   - 基本信息分类独立全选
   - 发货单明细分类独立全选
   - 收款记录分类独立全选
   - 总全选控制所有字段

4. **分离式布局**：
   - Excel：基本信息垂直显示，发货单明细和收款记录表格形式
   - PDF：分区域显示，标题分隔，专业格式

5. **中文字体支持**：
   - 智能检测Windows系统字体（simhei.ttf等）
   - 自动加载中文字体确保PDF正确显示

### 技术实现要点

1. **字段值映射函数**：
```python
def get_display_value(field_name, value):
    """获取字段值的中文显示"""
    if not value:
        return ''

    if field_name in value_mapping:
        return value_mapping[field_name].get(value, value)
    return value
```

2. **分类全选状态管理**：
```javascript
const updateSelectAllStatus = () => {
  const basicProps = basicColumns.value.map(col => col.prop)
  const deliveryProps = deliveryColumns.value.map(col => col.prop)
  const paymentProps = paymentColumns.value.map(col => col.prop)

  // 更新分类全选状态
  basicSelectAll.value = basicProps.every(prop => exportConfig.selectedColumns.includes(prop))
  deliverySelectAll.value = deliveryProps.every(prop => exportConfig.selectedColumns.includes(prop))
  paymentSelectAll.value = paymentProps.every(prop => exportConfig.selectedColumns.includes(prop))

  // 更新总全选状态
  exportConfig.selectAll = exportConfig.selectedColumns.length === availableColumns.length
}
```

3. **Excel分离布局实现**：
```python
# 基本信息垂直显示
basic_transposed = basic_df.T.reset_index()
basic_transposed.columns = ['项目', '内容']
basic_transposed.to_excel(writer, sheet_name='对账单详情', index=False, startrow=current_row)

# 发货单明细表格显示
delivery_df.to_excel(writer, sheet_name='对账单详情', index=False, startrow=current_row)

# 收款记录表格显示
payment_df.to_excel(writer, sheet_name='对账单详情', index=False, startrow=current_row)
```

### 测试验证结果

- **✅ Excel导出**：字段值正确显示中文，分离布局清晰
- **✅ PDF导出**：中文字体正确加载，格式专业美观
- **✅ 字段选择**：分类全选功能正常，用户体验良好
- **✅ 错误处理**：API错误处理完善，用户提示友好

### 关键功能特性

#### 1. 分类全选功能
- **功能**：在"基本信息"和"产品明细"分类旁边添加分类全选复选框
- **实现**：提供总全选、基本信息全选、产品明细全选三级选择控制
- **用户体验**：用户可以快速选择或取消选择某个分类下的所有字段

#### 2. 智能状态同步
- **总全选状态**：根据所有字段的选择状态自动更新
- **分类全选状态**：根据分类内字段的选择状态自动更新
- **字段变化响应**：单个字段变化时自动更新相关的全选状态

#### 3. 完整的数据支持
- **基本信息字段**：退货单号、关联订单号、项目名称、客户名称等12个字段
- **产品明细字段**：产品名称、型号、规格、数量、单价等9个字段
- **数据处理**：支持退货金额计算、状态转换、日期格式化等

### 技术实现要点

#### 1. 响应式状态管理
```javascript
// 分类全选状态
const basicSelectAll = ref(true)
const itemSelectAll = ref(true)

// 状态更新函数
const updateSelectAllStatus = () => {
  const basicProps = basicColumns.value.map(col => col.prop)
  const itemProps = itemColumns.value.map(col => col.prop)

  basicSelectAll.value = basicProps.every(prop => exportConfig.selectedColumns.includes(prop))
  itemSelectAll.value = itemProps.every(prop => exportConfig.selectedColumns.includes(prop))
  exportConfig.selectAll = exportConfig.selectedColumns.length === availableColumns.length
}
```

#### 2. 分类全选处理逻辑
- **添加字段**：将分类字段添加到选中列表，避免重复
- **移除字段**：从选中列表中过滤掉分类字段
- **状态同步**：每次操作后更新所有相关的全选状态

#### 3. 用户界面优化
- **分类标题布局**：使用flex布局，标题左对齐，全选复选框右对齐
- **视觉层次**：分类全选复选框使用较小字体和主题色
- **操作反馈**：所有操作都有即时的视觉反馈

### 测试验证结果

#### 分类全选功能测试
- ✅ 基本信息全选：正确选择/取消选择所有基本信息字段
- ✅ 产品明细全选：正确选择/取消选择所有产品明细字段
- ✅ 总全选同步：分类全选状态正确影响总全选状态
- ✅ 字段变化响应：单个字段变化正确更新分类全选状态

#### 导出功能测试
- ✅ Excel导出成功：文件正确下载，数据完整
- ✅ PDF导出成功：中文字体正常显示，布局清晰
- ✅ 字段过滤正确：只导出选中的字段
- ✅ 文件命名规范：使用退货单号作为文件名

**注意**：本规范基于EMB项目报价需求表、订单和退货单导出功能的实际开发经验制定，经过完整的测试验证。在实施过程中可根据具体模块需求进行适当调整，但应保持核心架构和用户体验的一致性。

---

*最后更新时间：2025年7月16日*
*更新内容：添加退货单导出功能实现参考和分类全选功能规范*