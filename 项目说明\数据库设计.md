# EMB项目数据库设计详解

## 📋 数据库概览

EMB系统采用关系型数据库设计，支持SQLite(开发)、PostgreSQL(生产)、MySQL等多种数据库。数据库设计遵循第三范式，确保数据一致性和完整性。

### 设计特点
- **规范化设计**: 遵循数据库设计范式
- **外键约束**: 完整的引用完整性
- **索引优化**: 关键字段建立索引
- **时间戳**: 统一的创建和更新时间
- **软删除**: 重要数据支持软删除

## 🗄️ 数据库架构

### 基础配置
```python
# 数据库连接配置
SQLALCHEMY_DATABASE_URI = 'sqlite:///project.db'  # 开发环境
SQLALCHEMY_DATABASE_URI = '******************************'  # 生产环境
SQLALCHEMY_TRACK_MODIFICATIONS = False
```

### 基础模型类
```python
class BaseModel(db.Model):
    """所有模型的基类"""
    __abstract__ = True
    
    # 通用时间戳字段
    created_at = db.Column(db.DateTime, default=datetime.now, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    
    def to_dict(self, exclude=None):
        """模型转字典"""
        
    def from_dict(self, data):
        """字典更新模型"""
        
    def save(self):
        """保存到数据库"""
        
    def delete(self):
        """从数据库删除"""
```

## 📊 核心业务表设计

### 1. 客户管理模块

#### customers (客户表)
```sql
CREATE TABLE customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,           -- 公司名称
    contact VARCHAR(50) NOT NULL,                -- 联系人
    phone VARCHAR(20),                           -- 联系电话
    email VARCHAR(120),                          -- 邮箱地址
    address VARCHAR(255),                        -- 公司地址
    tax_id VARCHAR(50) UNIQUE,                   -- 税号
    source VARCHAR(50),                          -- 客户来源
    level VARCHAR(50) DEFAULT 'normal',          -- 客户等级
    status VARCHAR(20) DEFAULT '正常',           -- 状态
    notes TEXT,                                  -- 备注
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

#### customer_bank_accounts (客户银行账户表)
```sql
CREATE TABLE customer_bank_accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,               -- 客户ID
    bank_name VARCHAR(100) NOT NULL,            -- 银行名称
    account_number VARCHAR(50) NOT NULL,        -- 账户号码
    account_name VARCHAR(100) NOT NULL,         -- 账户名称
    is_default BOOLEAN DEFAULT FALSE,           -- 是否默认账户
    status VARCHAR(20) DEFAULT '正常',          -- 状态
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);
```

#### customer_delivery_addresses (客户送货地址表)
```sql
CREATE TABLE customer_delivery_addresses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,               -- 客户ID
    contact_name VARCHAR(50) NOT NULL,          -- 联系人姓名
    contact_phone VARCHAR(20) NOT NULL,         -- 联系电话
    address VARCHAR(255) NOT NULL,              -- 详细地址
    is_default BOOLEAN DEFAULT FALSE,           -- 是否默认地址
    notes TEXT,                                 -- 备注
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);
```

### 2. 产品管理模块

#### product_categories (产品分类表)
```sql
CREATE TABLE product_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,          -- 分类名称
    parent_id INTEGER,                          -- 父分类ID
    description TEXT,                           -- 分类描述
    sort_order INTEGER DEFAULT 0,              -- 排序
    status VARCHAR(20) DEFAULT '正常',          -- 状态
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (parent_id) REFERENCES product_categories(id)
);
```

#### brands (品牌表)
```sql
CREATE TABLE brands (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,          -- 品牌名称
    logo VARCHAR(255),                          -- 品牌Logo
    description TEXT,                           -- 品牌描述
    website VARCHAR(255),                       -- 官网地址
    sort_order INTEGER DEFAULT 0,              -- 排序
    status VARCHAR(20) DEFAULT '正常',          -- 状态
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

#### products (产品表)
```sql
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,                 -- 产品名称
    model VARCHAR(50) NOT NULL,                 -- 产品型号
    unit VARCHAR(20) NOT NULL,                  -- 单位
    category_id INTEGER NOT NULL,               -- 类别ID
    brand_id INTEGER,                           -- 品牌ID
    image VARCHAR(255),                         -- 产品图片URL
    description TEXT,                           -- 产品描述
    notes TEXT,                                 -- 备注
    status VARCHAR(20) DEFAULT '正常',          -- 状态
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (category_id) REFERENCES product_categories(id),
    FOREIGN KEY (brand_id) REFERENCES brands(id),
    UNIQUE(name, model)
);
```

#### product_specifications (产品规格表)
```sql
CREATE TABLE product_specifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,                -- 产品ID
    specification VARCHAR(100) NOT NULL,        -- 规格
    price DECIMAL(10,2) NOT NULL,              -- 价格
    notes TEXT,                                 -- 备注
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

### 3. 报价管理模块

#### quotation_requests (报价需求表)
```sql
CREATE TABLE quotation_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_number VARCHAR(50) NOT NULL UNIQUE, -- 需求编号
    customer_id INTEGER NOT NULL,               -- 客户ID
    project_name VARCHAR(100) NOT NULL,         -- 项目名称
    project_address VARCHAR(200),               -- 项目地址
    expected_date DATETIME,                     -- 预计采购时间
    status VARCHAR(20) DEFAULT '待处理',        -- 状态
    notes TEXT,                                 -- 备注
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);
```

#### quotations (报价单表)
```sql
CREATE TABLE quotations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    quotation_number VARCHAR(50) NOT NULL UNIQUE, -- 报价单编号
    request_id INTEGER,                         -- 关联报价需求表ID
    customer_id INTEGER NOT NULL,               -- 客户ID
    project_name VARCHAR(100) NOT NULL,         -- 项目名称
    project_address VARCHAR(200),               -- 项目地址
    valid_until DATETIME NOT NULL,              -- 有效期至
    payment_terms VARCHAR(100),                 -- 付款条件
    delivery_terms VARCHAR(100),                -- 交货条件
    status VARCHAR(20) DEFAULT '草稿',          -- 状态
    total_amount DECIMAL(12,2) DEFAULT 0,       -- 总金额
    notes TEXT,                                 -- 备注
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (request_id) REFERENCES quotation_requests(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);
```

#### quotation_items (报价项表)
```sql
CREATE TABLE quotation_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    quotation_id INTEGER NOT NULL,              -- 报价单ID
    product_id INTEGER NOT NULL,                -- 产品ID
    specification VARCHAR(100),                 -- 规格
    quantity DECIMAL(10,2) NOT NULL,           -- 数量
    unit_price DECIMAL(10,2) NOT NULL,         -- 单价
    total_price DECIMAL(12,2) NOT NULL,        -- 总价
    notes TEXT,                                 -- 备注
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (quotation_id) REFERENCES quotations(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

### 4. 订单管理模块

#### orders (订单表)
```sql
CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number VARCHAR(50) NOT NULL UNIQUE,   -- 订单编号
    customer_id INTEGER NOT NULL,               -- 客户ID
    quotation_id INTEGER,                       -- 关联报价单ID
    project_name VARCHAR(100) NOT NULL,         -- 项目名称
    project_address VARCHAR(200),               -- 项目地址
    expected_date DATETIME,                     -- 预计采购时间
    payment_terms VARCHAR(100),                 -- 付款条件
    delivery_terms VARCHAR(100),                -- 交货条件
    status VARCHAR(20) DEFAULT '待确认',        -- 订单状态
    total_amount DECIMAL(12,2) DEFAULT 0,       -- 总金额
    paid_amount DECIMAL(12,2) DEFAULT 0,        -- 已付金额
    notes TEXT,                                 -- 备注
    delivery_address_id INTEGER NOT NULL,       -- 送货地址ID
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (quotation_id) REFERENCES quotations(id),
    FOREIGN KEY (delivery_address_id) REFERENCES customer_delivery_addresses(id)
);
```

#### order_products (订单产品表)
```sql
CREATE TABLE order_products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL,                  -- 订单ID
    product_id INTEGER NOT NULL,                -- 产品ID
    specification VARCHAR(100),                 -- 规格
    quantity DECIMAL(10,2) NOT NULL,           -- 数量
    unit_price DECIMAL(10,2) NOT NULL,         -- 单价
    total_price DECIMAL(12,2) NOT NULL,        -- 总价
    delivered_quantity DECIMAL(10,2) DEFAULT 0, -- 已发货数量
    notes TEXT,                                 -- 备注
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

#### delivery_notes (发货单表)
```sql
CREATE TABLE delivery_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    delivery_number VARCHAR(50) NOT NULL UNIQUE, -- 发货单编号
    order_id INTEGER NOT NULL,                  -- 订单ID
    delivery_date DATE NOT NULL,                -- 发货日期
    logistics_company VARCHAR(100),             -- 物流公司
    tracking_number VARCHAR(100),               -- 物流单号
    delivery_address VARCHAR(255) NOT NULL,     -- 送货地址
    contact_name VARCHAR(50) NOT NULL,          -- 联系人
    contact_phone VARCHAR(20) NOT NULL,         -- 联系电话
    status VARCHAR(20) DEFAULT '待发货',        -- 状态
    notes TEXT,                                 -- 备注
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

### 5. 财务管理模块

#### payment_records (收款记录表)
```sql
CREATE TABLE payment_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    payment_number VARCHAR(50) NOT NULL UNIQUE, -- 收款单号
    order_id INTEGER NOT NULL,                  -- 订单ID
    payment_date DATE NOT NULL,                 -- 收款日期
    amount FLOAT NOT NULL,                      -- 收款金额
    payment_method VARCHAR(50) NOT NULL,        -- 支付方式
    reference_number VARCHAR(100),              -- 交易流水号
    notes TEXT,                                 -- 备注
    created_by VARCHAR(50),                     -- 创建人
    bank_account VARCHAR(200),                  -- 收款账户
    status VARCHAR(20) DEFAULT '待确认',        -- 状态
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

#### receivables (应收款表)
```sql
CREATE TABLE receivables (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,               -- 客户ID
    order_id INTEGER NOT NULL,                  -- 订单ID
    total_amount DECIMAL(12,2) NOT NULL,        -- 应收总额
    paid_amount DECIMAL(12,2) DEFAULT 0,        -- 已收金额
    remaining_amount DECIMAL(12,2) NOT NULL,    -- 剩余金额
    due_date DATE,                              -- 到期日期
    overdue_days INTEGER DEFAULT 0,             -- 逾期天数
    status VARCHAR(20) DEFAULT '未收款',        -- 状态
    notes TEXT,                                 -- 备注
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

### 6. 系统管理模块

#### system_settings (系统设置表)
```sql
CREATE TABLE system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key VARCHAR(100) NOT NULL UNIQUE,           -- 设置键
    value TEXT,                                 -- 设置值
    description VARCHAR(255),                   -- 描述
    category VARCHAR(50),                       -- 分类
    data_type VARCHAR(20) DEFAULT 'string',     -- 数据类型
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

#### error_logs (错误日志表)
```sql
CREATE TABLE error_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level VARCHAR(20) NOT NULL,                 -- 日志级别
    message TEXT NOT NULL,                      -- 错误消息
    traceback TEXT,                             -- 错误堆栈
    request_url VARCHAR(255),                   -- 请求URL
    request_method VARCHAR(10),                 -- 请求方法
    user_agent VARCHAR(255),                    -- 用户代理
    ip_address VARCHAR(45),                     -- IP地址
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## 🔗 表关系设计

### 主要外键关系
```
客户管理:
customers 1:N customer_bank_accounts
customers 1:N customer_delivery_addresses

产品管理:
product_categories 1:N products
brands 1:N products
products 1:N product_specifications
products 1:N product_attributes
products 1:N product_images

报价管理:
customers 1:N quotation_requests
customers 1:N quotations
quotation_requests 1:N quotations
quotations 1:N quotation_items
products 1:N quotation_items

订单管理:
customers 1:N orders
quotations 1:N orders
orders 1:N order_products
products 1:N order_products
orders 1:N delivery_notes
delivery_notes 1:N delivery_note_items

财务管理:
orders 1:N payment_records
customers 1:N receivables
orders 1:N receivables
return_orders 1:N refund_records
```

## 📈 索引设计

### 主要索引
```sql
-- 客户表索引
CREATE INDEX idx_customers_name ON customers(name);
CREATE INDEX idx_customers_status ON customers(status);

-- 产品表索引
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_brand ON products(brand_id);
CREATE INDEX idx_products_status ON products(status);

-- 订单表索引
CREATE INDEX idx_orders_customer ON orders(customer_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_date ON orders(created_at);

-- 报价表索引
CREATE INDEX idx_quotations_customer ON quotations(customer_id);
CREATE INDEX idx_quotations_status ON quotations(status);

-- 财务表索引
CREATE INDEX idx_payment_records_order ON payment_records(order_id);
CREATE INDEX idx_receivables_customer ON receivables(customer_id);
```

## 🔧 数据库优化

### 性能优化
1. **索引优化**: 关键查询字段建立索引
2. **查询优化**: 使用ORM查询优化
3. **连接池**: 数据库连接池配置
4. **分页查询**: 大数据量分页处理
5. **缓存策略**: 查询结果缓存

### 数据完整性
1. **外键约束**: 保证引用完整性
2. **唯一约束**: 防止重复数据
3. **检查约束**: 数据格式验证
4. **非空约束**: 必填字段控制
5. **默认值**: 合理的默认值设置

---

**数据库设计版本**: v1.0  
**最后更新**: 2025年1月  
**数据库类型**: SQLite/PostgreSQL/MySQL  
**维护状态**: 🔄 持续优化中
