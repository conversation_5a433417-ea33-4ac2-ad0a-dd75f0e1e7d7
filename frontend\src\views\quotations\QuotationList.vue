<template>
  <div class="quotation-list">
    <!-- 头部卡片 -->
    <el-card class="header-card">
      <template #header>
        <div class="card-header">
          <span>报价单列表</span>
          <div>
            <el-button type="primary" @click="handleAdd">新增报价单</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="客户">
          <el-select
            v-model="searchForm.customer_id"
            filterable
            remote
            :remote-method="handleSearchCustomers"
            placeholder="请输入客户名称"
            clearable
          >
            <el-option
              v-for="item in customerOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="searchForm.project_name" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="待确认" value="待确认" />
            <el-option label="已确认" value="已确认" />
            <el-option label="已拒绝" value="已拒绝" />
            <el-option label="已过期" value="已过期" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区域 -->
      <div class="operator-content mb-20">
        <div class="left-actions">
          <span>共 {{ total }} 条记录</span>
        </div>
        <div class="right-actions">
          <!-- 卡片详细程度切换 -->
          <el-button-group>
            <el-button
              :type="cardMode === 'detailed' ? 'primary' : ''"
              @click="cardMode = 'detailed'"
              title="详细模式"
            >
              详细
            </el-button>
            <el-button
              :type="cardMode === 'simple' ? 'primary' : ''"
              @click="cardMode = 'simple'"
              title="简化模式"
            >
              简化
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 卡片视图 -->
      <div v-loading="loading">
        <!-- 卡片列表 -->
        <div class="quotation-cards-list">
          <div
            v-for="quotation in tableData"
            :key="quotation.id"
            class="quotation-card"
            @click="handleView(quotation)"
          >
            <!-- 详细模式卡片内容 -->
            <div v-if="cardMode === 'detailed'" class="card-content detailed">
              <!-- 第一行：报价单号、客户、项目、状态 -->
              <div class="card-row-1">
                <div class="left-info">
                  <div class="quotation-number">{{ quotation.quotation_number }}</div>
                  <div class="customer-project-status">
                    <span class="customer">{{ quotation.customer_name || 'N/A' }}</span>
                    <span class="separator">|</span>
                    <span class="project" :title="quotation.project_name">{{ quotation.project_name }}</span>
                    <div class="status-tags">
                      <el-tag :type="getStatusType(quotation.status)" size="default" class="status-tag-prominent quotation-status">
                        {{ quotation.status }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第二行：金额信息、时间信息和关联订单 -->
              <div class="card-row-2">
                <div class="left-details">
                  <div class="amounts-info">
                    <div class="amount-group">
                      <span class="amount-label">报价金额</span>
                      <span class="amount-value total">{{ formatCurrency(quotation.total_amount) }}</span>
                    </div>
                  </div>
                  <div class="dates-info">
                    <div class="date-group">
                      <span class="date-label">有效期</span>
                      <span class="date-value">{{ quotation.valid_until ? formatDate(quotation.valid_until) : 'N/A' }}</span>
                    </div>
                    <div class="date-group">
                      <span class="date-label">创建日期</span>
                      <span class="date-value">{{ quotation.created_at ? formatDateTime(quotation.created_at) : 'N/A' }}</span>
                    </div>
                  </div>
                  <div class="linked-order-info">
                    <div class="linked-order-group">
                      <span class="linked-order-label">关联订单</span>
                      <div v-if="quotation.is_linked_to_order" class="linked-order-content">
                        <el-tag type="success" size="small" class="linked-order-tag" @click.stop="handleViewLinkedOrder(quotation)" style="cursor: pointer;">
                          {{ quotation.linked_order_number || 'N/A' }}
                        </el-tag>
                      </div>
                      <span v-else class="no-linked-order">暂无关联</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第三行：操作按钮 -->
              <div class="card-actions detailed" @click.stop>
                <!-- 待确认状态：编辑、确认、拒绝、删除、导出 -->
                <template v-if="quotation.id && quotation.status === '待确认'">
                  <el-button type="primary" size="small" @click="handleEdit(quotation)">
                    编辑
                  </el-button>
                  <el-button type="success" size="small" @click="handleStatusChange(quotation, '已确认')">
                    确认
                  </el-button>
                  <el-button type="warning" size="small" @click="handleStatusChange(quotation, '已拒绝')">
                    拒绝
                  </el-button>
                  <el-button type="danger" size="small" @click="handleDeleteConfirmation(quotation)">
                    删除
                  </el-button>
                  <el-button type="info" size="small" @click="handleExport(quotation)">
                    导出
                  </el-button>
                </template>

                <!-- 已确认且无关联订单：创建订单、添加到订单、取消确认、导出 -->
                <template v-else-if="quotation.id && quotation.status === '已确认' && !quotation.is_linked_to_order">
                  <el-button type="success" size="small" @click="handleCreateOrder(quotation)">
                    创建订单
                  </el-button>
                  <el-button type="primary" size="small" @click="openSelectOrder(quotation)">
                    添加到订单
                  </el-button>
                  <el-button type="warning" size="small" @click="handleStatusChange(quotation, '待确认')">
                    取消确认
                  </el-button>
                  <el-button type="info" size="small" @click="handleExport(quotation)">
                    导出
                  </el-button>
                </template>

                <!-- 其他状态：导出 -->
                <template v-else-if="!quotation.is_linked_to_order">
                  <el-button type="info" size="small" @click="handleExport(quotation)">
                    导出
                  </el-button>
                </template>

                <!-- 已关联订单：导出 -->
                <template v-else>
                  <el-button type="info" size="small" @click="handleExport(quotation)">
                    导出
                  </el-button>
                </template>
              </div>
            </div>

            <!-- 简化模式卡片内容 -->
            <div v-else class="card-content simple">
              <div class="card-main simple">
                <div class="quotation-number">{{ quotation.quotation_number }}</div>
                <div class="customer-project">
                  <span class="customer">{{ quotation.customer_name || 'N/A' }}</span>
                  <span class="project">{{ quotation.project_name }}</span>
                </div>
                <div class="amount-info">
                  <span class="total-amount">{{ formatCurrency(quotation.total_amount) }}</span>
                  <span class="valid-info">{{ quotation.valid_until ? formatDate(quotation.valid_until) : '无有效期' }}</span>
                </div>
                <div class="status-tags">
                  <el-tag :type="getStatusType(quotation.status)" size="default" class="status-tag-prominent quotation-status">
                    {{ quotation.status }}
                  </el-tag>
                </div>
                <div class="card-actions simple" @click.stop>
                  <!-- 根据状态显示相应操作 -->
                  <template v-if="quotation.status === '待确认'">
                    <el-button type="primary" size="small" @click="handleEdit(quotation)">编辑</el-button>
                    <el-button type="success" size="small" @click="handleStatusChange(quotation, '已确认')">确认</el-button>
                    <el-button type="info" size="small" @click="handleExport(quotation)">导出</el-button>
                  </template>
                  <template v-else-if="quotation.status === '已确认' && !quotation.is_linked_to_order">
                    <el-button type="success" size="small" @click="handleCreateOrder(quotation)">创建订单</el-button>
                    <el-button type="info" size="small" @click="handleExport(quotation)">导出</el-button>
                  </template>
                  <template v-else>
                    <el-button type="info" size="small" @click="handleExport(quotation)">导出</el-button>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <SelectOrderDialog v-model="selectOrderDialogVisible" @confirm="handleAddToOrder" />

    <!-- 导出对话框 -->
    <QuotationExportDialog
      v-model="exportDialogVisible"
      :quotation-id="currentExportQuotation?.id"
      :quotation-number="currentExportQuotation?.quotation_number"
      @export-success="handleExportSuccess"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Edit, View, Delete, Download, DocumentAdd } from '@element-plus/icons-vue'
import { quotationApi } from '@/api/quotation'
import { customerApi } from '@/api/customer'
import { orderApi } from '@/api/order'
import SelectOrderDialog from '@/components/SelectOrderDialog.vue'
import QuotationExportDialog from '@/components/QuotationExportDialog.vue'

const router = useRouter()
const loading = ref(false)
const tableData = ref([])
const customerOptions = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const multipleSelection = ref([]);

const selectOrderDialogVisible = ref(false)
const currentQuotationId = ref(null)

// 导出相关
const exportDialogVisible = ref(false)
const currentExportQuotation = ref<any>(null)

// 卡片模式（详细/简化）
const cardMode = ref('detailed')

// 搜索表单
const searchForm = reactive({
  customer_id: undefined,
  project_name: '',
  status: ''
})



// 处理表格选中变化
const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val;
}



// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 清理空参数以减少后端处理
    const params: any = {
      page: currentPage.value,
      per_page: pageSize.value
    }

    // 只添加有值的参数
    if (searchForm.customer_id) params.customer_id = searchForm.customer_id
    if (searchForm.project_name?.trim()) params.project_name = searchForm.project_name.trim()
    if (searchForm.status) params.status = searchForm.status

    const response = await quotationApi.getList(params) as any

    // 简化数据处理逻辑
    if (Array.isArray(response)) {
      tableData.value = response
      total.value = response.length
    } else if (response?.items) {
      tableData.value = Array.isArray(response.items) ? response.items : []
      total.value = response.total || 0
    } else if (response?.data?.items) {
      tableData.value = Array.isArray(response.data.items) ? response.data.items : []
      total.value = response.data.total || 0
    } else if (response?.data) {
      tableData.value = Array.isArray(response.data) ? response.data : []
      total.value = response.pagination?.total || 0
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载报价单列表失败:', error)
    ElMessage.error('加载报价单列表失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索客户
const handleSearchCustomers = async (query: string) => {
  if (query) {
    try {
      const response = await customerApi.getList({ name: query, per_page: 20 }) as any
      customerOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('搜索客户失败:', error)
    }
  } else {
    customerOptions.value = []
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.customer_id = undefined
  searchForm.project_name = ''
  searchForm.status = ''
  handleSearch()
}

// 分页
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

// 新增
const handleAdd = () => {
  router.push('/quotations/new')
}

// 编辑
const handleEdit = (row: any) => {
  if (!row.id) {
    ElMessage.warning('无效的报价单ID');
    return;
  }
  if (typeof row.status !== 'string') {
    ElMessage.warning('报价单状态无效或未定义，无法编辑。');
    return;
  }
  if (row.status !== '待确认') {
    ElMessage.warning('只有待确认状态的报价单可以编辑。');
    return;
  }
  router.push(`/quotations/edit/${row.id}`)
}

// 行点击查看详情
const handleRowClick = (row: any) => {
  // 检查是否有文本被选中，如果有则不触发跳转
  const selection = window.getSelection();
  if (selection && selection.toString().length > 0) {
    return;
  }

  if (!row.id) {
    ElMessage.warning('无效的报价单ID');
    return;
  }
  router.push(`/quotations/view/${row.id}`)
}

// 导出报价单
const handleExport = (quotation: any) => {
  if (!quotation.id) {
    ElMessage.warning('无效的报价单ID');
    return;
  }
  currentExportQuotation.value = quotation
  exportDialogVisible.value = true
}

// 导出成功回调
const handleExportSuccess = () => {
  // 可以在这里添加导出成功后的处理逻辑
}

// 更新状态的通用处理函数
const handleUpdateStatusWrapper = async (row: any, newStatus: string, confirmMessage: string) => {
  if (!row.id && typeof row.id !== 'number') {
    ElMessage.error('报价单ID无效，无法更新状态。');
    return;
  }
  try {
    await ElMessageBox.confirm(confirmMessage, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await quotationApi.updateStatus(row.id, newStatus)
    ElMessage.success('状态更新成功')
    loadData() // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`更新状态到 ${newStatus} 失败:`, error)
      const errorData = (error as any)?.response?.data;
      ElMessage.error(errorData?.message || errorData?.error || `状态更新失败: ${(error as any).message || '请重试'}`)
    }
  }
}

// 检查是否可以变更到指定状态
const canChangeToStatus = (currentStatus: string, targetStatus: string) => {
  const validTransitions = {
    '待确认': ['已确认', '已拒绝'],
    '已确认': ['待确认', '已拒绝', '已过期'],
    '已拒绝': ['待确认', '已确认'],
    '已过期': []
  }
  return validTransitions[currentStatus]?.includes(targetStatus) || false
}

// 处理状态变更
const handleStatusChange = async (row: any, newStatus: string) => {
  if (!row.id || typeof row.id !== 'number') {
    ElMessage.error('报价单ID无效，无法更新状态。')
    return
  }

  // 检查状态变更是否合法
  if (!canChangeToStatus(row.status, newStatus)) {
    ElMessage.warning(`不能从${row.status}状态变更为${newStatus}状态`)
    return
  }

  const statusMessages = {
    '待确认': '确认要将此报价单标记为待确认吗？',
    '已确认': '确认要将此报价单标记为已确认吗？',
    '已拒绝': '确认要将此报价单标记为已拒绝吗？',
    '已过期': '确认要将此报价单标记为已过期吗？'
  }

  try {
    await ElMessageBox.confirm(statusMessages[newStatus] || '确认要更改状态吗？', '状态变更确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await quotationApi.updateStatus(row.id, newStatus)
    ElMessage.success(`状态已更新为${newStatus}`)
    loadData() // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`更新状态到 ${newStatus} 失败:`, error)
      const errorData = (error as any)?.response?.data
      ElMessage.error(errorData?.message || errorData?.error || `状态更新失败: ${(error as any).message || '请重试'}`)
    }
  }
}

// 删除处理函数
const handleDeleteConfirmation = async (row: any) => {
  if (!row.id && typeof row.id !== 'number') {
    ElMessage.error('报价单ID无效，无法删除。');
    return;
  }
  try {
    await ElMessageBox.confirm('确认要删除此报价单吗？删除后无法恢复。', '警告', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error'
    })
    await quotationApi.delete(row.id)
    ElMessage.success('报价单删除成功')
    loadData() // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除报价单失败:', error)
      const errorData = (error as any)?.response?.data;
      ElMessage.error(errorData?.message || errorData?.error || `删除失败: ${(error as any).message || '请重试'}`)
    }
  }
}

// 创建订单
const handleCreateOrder = async (row: any) => {
  if (!row.id) return;

  try {
    // 确保报价单状态为"已确认"
    if (row.status !== '已确认') {
      ElMessage.warning('只能从已确认状态的报价单创建订单，请先确认报价单。');
      return;
    }

    // 检查报价单是否已关联订单
    if (row.is_linked_to_order) {
      ElMessage.warning(`报价单 ${row.quotation_number} 已被订单 ${row.linked_order_number} 关联，不能重复使用`);
      return;
    }

    await ElMessageBox.confirm(
      `确定要基于报价单 ${row.quotation_number} 创建新订单吗？\n\n将跳转到新建订单页面，您可以填写完整信息后再保存。`,
      '创建订单确认',
      {
        type: 'question',
        confirmButtonText: '创建订单',
        cancelButtonText: '取消'
      }
    );

    // 直接跳转到新建订单页面，通过URL参数传递报价单ID
    router.push(`/orders/new?quotationId=${row.id}`);

  } catch (error) {
    if (error !== 'cancel') {
      console.error('跳转失败:', error);
    }
  }
};

// 打开选择订单对话框 (此函数已存在，用于"添加到现有订单")
const openSelectOrder = (row: any) => {
  if (typeof row.id === 'number') {
    currentQuotationId.value = row.id
    selectOrderDialogVisible.value = true
  } else {
    ElMessage.error('无效的报价单ID');
  }
}

// 处理添加到现有订单（这个函数是 SelectOrderDialog 的 @confirm 事件触发的）
const handleAddToOrder = async (orderId: number) => {
  if (!currentQuotationId.value || !orderId) {
    ElMessage.warning('未选择报价单或订单。')
    return
  }
  try {
    await orderApi.addItemsFromQuotation(orderId, currentQuotationId.value)
    ElMessage.success('成功将报价单项目添加到订单！')
    selectOrderDialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('添加到现有订单失败:', error)
    const errorData = (error as any)?.response?.data;
    ElMessage.error(errorData?.message || errorData?.error || `添加失败: ${(error as any).message || '请重试'}`)
  }
}

// 查看报价单详情
const handleView = (row: any) => {
  if (!row.id) {
    ElMessage.warning('无效的报价单ID');
    return;
  }
  router.push(`/quotations/view/${row.id}`)
}

// 查看关联订单详情
const handleViewLinkedOrder = (quotation: any) => {
  if (!quotation.linked_order_id) {
    ElMessage.warning('该报价单未关联订单');
    return;
  }
  router.push(`/orders/view/${quotation.linked_order_id}`)
}





// 获取状态标签类型
const getStatusType = (status: string) => {
  const statusMap = {
    '待确认': 'warning',
    '已确认': 'success',
    '已拒绝': 'danger',
    '已过期': 'info'
  }
  return statusMap[status] || 'info'
}

// 优化的日期格式化函数，减少重复计算
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    return new Date(dateStr).toLocaleDateString('zh-CN')
  } catch {
    return dateStr
  }
}

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    return new Date(dateStr).toLocaleString('zh-CN')
  } catch {
    return dateStr
  }
}

// 格式化金额
const formatCurrency = (value: number | string) => {
  if (value === undefined || value === null) return 'N/A'
  const numericValue = Number(value)
  if (isNaN(numericValue)) return 'N/A'
  return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(numericValue)
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.quotation-list {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operator-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-actions {
  display: flex;
  gap: 10px;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

/* 卡片列表布局 */
.quotation-cards-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加卡片间距 */
  margin-bottom: 20px;
}

/* 报价单卡片样式 */
.quotation-card {
  position: relative;
  width: 100%;
  border: 2px solid #e8eaed; /* 增加边框宽度，使用更柔和的颜色 */
  border-radius: 12px; /* 增加圆角 */
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 4px; /* 额外的底部间距 */
}

.quotation-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15); /* 增强悬停阴影 */
  transform: translateY(-1px); /* 轻微上移效果 */
}

.card-content {
  padding: 12px 16px;
}

.card-content.detailed {
  padding: 14px 16px;
}

.card-content.simple {
  padding: 10px 16px;
}

/* 详细模式样式 */
.card-content.detailed .card-row-1 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.card-content.detailed .left-info {
  flex: 1;
  min-width: 0;
}

.card-content.detailed .quotation-number {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.card-content.detailed .customer-project-status {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.card-content.detailed .customer-project-status .customer {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  flex-shrink: 0;
}

.card-content.detailed .customer-project-status .separator {
  color: #dcdfe6;
  font-size: 12px;
}

.card-content.detailed .customer-project-status .project {
  font-size: 13px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
  margin-right: 12px;
}

.card-content.detailed .status-tags {
  display: flex;
  gap: 8px;
  flex-shrink: 0;

  .status-tag-prominent {
    font-weight: 600;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    border: 2px solid transparent;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* 报价单状态 - 统一大小 */
    &.quotation-status {
      font-size: 13px;
      padding: 5px 12px;
      font-weight: 600;
      letter-spacing: 0.3px;

      &.el-tag--success {
        background: linear-gradient(135deg, #67c23a, #85ce61);
        border-color: #67c23a;
        color: #fff;
        box-shadow: 0 2px 6px rgba(103, 194, 58, 0.25);
      }

      &.el-tag--warning {
        background: linear-gradient(135deg, #e6a23c, #ebb563);
        border-color: #e6a23c;
        color: #fff;
        box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25);
      }

      &.el-tag--danger {
        background: linear-gradient(135deg, #f56c6c, #f78989);
        border-color: #f56c6c;
        color: #fff;
        box-shadow: 0 2px 6px rgba(245, 108, 108, 0.25);
      }

      &.el-tag--info {
        background: linear-gradient(135deg, #909399, #a6a9ad);
        border-color: #909399;
        color: #fff;
        box-shadow: 0 2px 6px rgba(144, 147, 153, 0.25);
      }

      &.el-tag--primary {
        background: linear-gradient(135deg, #409eff, #66b1ff);
        border-color: #409eff;
        color: #fff;
        box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
      }
    }

    /* 关联订单标签 - 相对较小 */
    &.linked-order {
      font-size: 12px;
      padding: 4px 10px;
      font-weight: 600;

      &.el-tag--success {
        background-color: #67c23a;
        border-color: #67c23a;
        color: #fff;
      }
    }
  }
}

.card-content.detailed .card-row-2 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
}

.card-content.detailed .left-details {
  flex: 1;
  display: flex;
  gap: 32px;
}

.card-content.detailed .amounts-info {
  display: flex;
  gap: 20px;
}

.card-content.detailed .amount-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.card-content.detailed .amount-label {
  font-size: 12px;
  color: #909399;
}

.card-content.detailed .amount-value {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
}

.card-content.detailed .dates-info {
  display: flex;
  gap: 24px;
}

.card-content.detailed .date-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.card-content.detailed .date-label {
  font-size: 12px;
  color: #909399;
}

.card-content.detailed .date-value {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.card-content.detailed .linked-order-info {
  .linked-order-group {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .linked-order-label {
      font-size: 12px;
      color: #909399;
    }

    .linked-order-content {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    .linked-order-tag {
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }

    .no-linked-order {
      color: #999;
      font-size: 12px;
    }
  }
}

.card-content.detailed .date-label {
  display: block;
  font-size: 11px;
  color: #909399;
  margin-bottom: 2px;
}

.card-content.detailed .date-value {
  font-size: 12px;
  color: #606266;
}

.card-content.detailed .card-actions.detailed {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  border-top: 1px solid #ebeef5;
  padding-top: 8px;
}

.card-content.detailed .card-actions.detailed .el-button {
  font-size: 12px;
  padding: 3px 6px;
  height: 24px;
}

/* 简化模式样式 */
.card-content.simple .card-main.simple {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.card-content.simple .quotation-number {
  flex: 0 0 140px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.card-content.simple .customer-project {
  flex: 1;
  min-width: 0;
}

.card-content.simple .customer-project .customer {
  color: #303133;
  font-weight: 500;
  font-size: 13px;
  margin-right: 8px;
}

.card-content.simple .customer-project .project {
  color: #606266;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-content.simple .amount-info {
  flex: 0 0 160px;
  text-align: center;
}

.card-content.simple .amount-info .total-amount {
  display: block;
  color: #303133;
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 1px;
}

.card-content.simple .amount-info .valid-info {
  color: #909399;
  font-size: 11px;
}

.card-content.simple .status-tags {
  flex: 0 0 120px;
  display: flex;
  gap: 4px;
  justify-content: center;
}

.card-content.simple .card-actions.simple {
  flex: 0 0 auto;
  display: flex;
  gap: 6px;
  align-items: center;
}

.card-content.simple .card-actions.simple .el-button {
  font-size: 11px;
  padding: 3px 6px;
  height: 24px;
}

/* 无操作状态样式 */
.no-operation {
  color: #c0c4cc;
  font-size: 12px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 操作列按钮对齐 */
.el-table .el-table__body .el-table__row .el-table__cell {
  .el-dropdown {
    display: inline-block;
    vertical-align: middle;
  }

  .el-button {
    vertical-align: middle;
  }
}

/* 确保操作列中的所有元素都在同一基线上 */
.el-table__cell .el-dropdown,
.el-table__cell .el-button {
  vertical-align: baseline;
}

/* 无操作状态样式 */
.no-operation {
  color: #c0c4cc;
  font-size: 12px;
}

</style>
