<template>
  <div class="quotation-request-edit">
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="page-title">{{ isEdit ? '编辑报价需求' : '新增报价需求' }}</h2>
        <div>
          <el-button @click="handleCancel">返回</el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            保存
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="quotation-request-form"
      >
        <!-- 基本信息 -->
        <el-form-item label="客户" prop="customer_id">
          <div style="display: flex; width: 70%; align-items: flex-start;">
            <el-select
              v-model="form.customer_id"
              filterable
              remote
              :placeholder="customerNameFromImport && form.customer_id === undefined ? `新客户 (待创建): ${customerNameFromImport}` : '请选择或搜索客户'"
              :remote-method="handleSearchCustomers"
              :loading="customerLoading"
              clearable
              style="flex-grow: 1; margin-right: 10px;"
              @clear="() => { form.customer_id = undefined; customerNameFromImport = '' }"
              @change="(selectedId: number | undefined) => { if (typeof selectedId === 'number') customerNameFromImport = '' }"
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button
              v-if="customerNameFromImport && form.customer_id === undefined"
              type="success"
              @click="handleQuickCreateCustomer"
              title="根据导入的客户名称快速创建新客户"
            >
              创建: {{ customerNameFromImport }}
            </el-button>
          </div>
          <div v-if="customerNameFromImport && form.customer_id === undefined" class="el-form-item__extra_info">
            当前客户 "{{ customerNameFromImport }}" 来自导入，数据库中可能不存在。您可以直接创建或选择其他已有客户。
          </div>
        </el-form-item>

        <el-form-item label="项目名称" prop="project_name">
          <el-input v-model="form.project_name" placeholder="请输入项目名称" />
        </el-form-item>

        <el-form-item label="项目地址">
          <el-input
            v-model="form.project_address"
            type="textarea"
            :rows="2"
            placeholder="请输入项目详细地址"
          />
        </el-form-item>

        <el-form-item label="预计采购时间" prop="expected_date">
          <el-date-picker
            v-model="form.expected_date"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 当前状态 -->
        <el-form-item label="当前状态">
          <div style="display: flex; align-items: center; gap: 10px;">
            <el-tag
              :type="form.status === '已确认' ? 'success' : 'warning'"
              size="large"
            >
              {{ form.status || '待确认' }}
            </el-tag>

            <!-- 待确认状态显示确认按钮 -->
            <el-button
              v-if="form.status !== '已确认' && isEdit"
              type="primary"
              size="small"
              :loading="confirmStatusLoading"
              @click="handleConfirmStatus"
            >
              确认
            </el-button>

            <!-- 已确认状态显示创建报价单和转为未确认按钮 -->
            <template v-if="form.status === '已确认' && isEdit">
              <el-button
                type="success"
                size="small"
                @click="handleGenerateQuotation"
              >
                创建报价单
              </el-button>
              <el-button
                type="warning"
                size="small"
                :loading="unconfirmStatusLoading"
                @click="handleUnconfirmStatus"
              >
                转为未确认
              </el-button>
            </template>

            <!-- 导出需求表按钮 - 无论状态如何都显示 -->
            <el-button
              v-if="isEdit"
              type="info"
              size="small"
              :loading="exportLoading"
              @click="handleExportRequest"
            >
              <el-icon><Download /></el-icon>
              导出需求表
            </el-button>
          </div>
        </el-form-item>

      <!-- 产品明细 -->
        <el-divider content-position="left">
          <span>产品明细</span>
          <span v-if="isConfirmed" style="color: #909399; font-size: 12px; margin-left: 10px;">
            （编辑产品明细需要转为未确认状态）
          </span>
        </el-divider>

        <div class="table-operations mb-20">
          <div class="operations-left">
            <el-button
              type="success"
              :disabled="isConfirmed"
              @click="handleOpenBatchAddDialog"
              style="margin-right: 10px;"
            >
              <el-icon><Finished /></el-icon>
              批量添加
            </el-button>
            <el-button
              type="warning"
              :disabled="isConfirmed"
              @click="importDialogVisible = true"
              style="margin-right: 10px;"
            >
              <el-icon><DocumentAdd /></el-icon>
              导入Excel
            </el-button>
            <el-button
              type="info"
              :disabled="isConfirmed"
              @click="handleDownloadTemplate"
              style="margin-right: 10px;"
            >
              <el-icon><Download /></el-icon>
              下载模板
            </el-button>
            <el-button
              type="primary"
              :disabled="isConfirmed"
              @click="handleAddItem"
              style="margin-right: 10px;"
            >
              <el-icon><Plus /></el-icon>
              添加产品
            </el-button>
            <el-button
              type="warning"
              :disabled="isConfirmed"
              :loading="autoStandardizeLoading"
              @click="handleAutoStandardizeItems"
              style="margin-right: 10px;"
            >
              <el-icon><Refresh /></el-icon>
              自动匹配(待匹配条目)
            </el-button>
            <el-button
              type="info"
              :disabled="isConfirmed"
              @click="openStandardizeDialog"
            >
              <el-icon><Finished /></el-icon>
              手动匹配
            </el-button>
          </div>


        </div>

        <!-- 产品明细表格 -->
        <el-table :data="form.items" border style="width: 100%">
          <el-table-column type="index" width="50" />
          <el-table-column label="产品名称" min-width="200">
            <template #default="{ row }">
              <div>
                <el-input
                  v-model="row.product_name"
                  placeholder="请输入产品名称"
                  :disabled="isConfirmed"
                  @input="handleProductNameInput(row)"
                  style="width: 100%;"
                />
                <div v-if="row.matched_product_id" class="matched-info">
                  <el-icon><Link /></el-icon>
                  <span>{{ getMatchedProductName(row) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="产品型号" min-width="150">
            <template #default="{ row }">
              <div>
                <el-input
                  v-model="row.product_model"
                  placeholder="请输入产品型号"
                  :disabled="isConfirmed"
                  style="width: 100%;"
                />
                <div v-if="row.matched_product_id" class="matched-info">
                  <el-icon><Link /></el-icon>
                  <span>{{ getMatchedProductModel(row) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="规格" min-width="150">
            <template #default="{ row }">
              <div>
                <el-input
                  v-model="row.product_spec"
                  placeholder="请输入规格"
                  :disabled="isConfirmed"
                  style="width: 100%;"
                />
                <div v-if="row.matched_product_specification_id" class="matched-info">
                  <el-icon><Link /></el-icon>
                  <span>{{ getMatchedSpecificationName(row) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="数量" width="140">
            <template #default="{ row }">
              <el-input-number
                v-model="row.quantity"
                :min="1"
                :precision="0"
                :disabled="isConfirmed"
                class="quantity-input"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="单位" width="100">
            <template #default="{ row }">
              <div>
                <el-input v-model="row.unit" placeholder="单位" :disabled="isConfirmed" style="width: 100%;" />
                <div v-if="row.matched_product_id" class="matched-info">
                  <el-icon><Link /></el-icon>
                  <span>{{ getMatchedProductUnit(row) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="匹配状态" width="140">
            <template #default="{ row }">
              <div>
                <div v-if="row.matched_product_id">
                  <el-tag
                    :type="row.matched_product_specification_id ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ row.matched_product_specification_id ? '已匹配' : '部分匹配' }}
                  </el-tag>
                  <el-tag
                    v-if="row.match_type === 'auto'"
                    type="info"
                    size="small"
                    style="margin-top: 2px;"
                  >
                    自动
                  </el-tag>
                  <el-tag
                    v-else-if="row.match_type === 'manual'"
                    type="primary"
                    size="small"
                    style="margin-top: 2px;"
                  >
                    手动
                  </el-tag>
                </div>
                <el-tag v-else type="warning" size="small">
                  待匹配
                </el-tag>
                <div style="margin-top: 5px;">
                  <el-button
                    :type="row.matched_product_id ? '' : 'primary'"
                    :class="{ 'light-blue-btn': row.matched_product_id }"
                    :disabled="isConfirmed"
                    size="small"
                    @click="openSingleStandardizeDialog(row)"
                  >
                    调整匹配
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150">
            <template #default="{ row }">
              <el-input
                v-model="row.notes"
                type="textarea"
                placeholder="请输入备注"
                :disabled="isConfirmed"
                :rows="3"
                resize="vertical"
                style="width: 100%;"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button
                type="danger"
                size="small"
                :disabled="isConfirmed"
                @click="handleRemoveItem($index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>


      </el-form>
      </el-card>

    <!-- 批量添加产品对话框 -->
    <el-dialog
      v-model="batchAddDialogVisible"
      title="批量添加产品"
      width="60%"
      @close="handleBatchDialogClose"
    >
      <el-alert
        title="请从Excel中复制数据（包括表头则会自动忽略第一行），按以下列顺序粘贴，并确保每列数据间使用制表符(Tab)分隔："
        type="info"
        show-icon
        :closable="false"
        class="mb-20"
      >
        <p>列顺序：产品名称 | 产品型号 | 规格 | 数量 | 单位 | 备注</p>
        <p>示例：</p>
        <pre>
产品A  型号X  大号  10  个  这是备注A
产品B  型号Y  中号  5   件  这是备注B
        </pre>
      </el-alert>
      <el-input
        v-model="batchAddText"
        type="textarea"
        :rows="10"
        placeholder="请在此处粘贴Excel数据..."
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchAddDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmBatchAdd">
            确认添加
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入Excel对话框 -->
    <el-dialog v-model="importDialogVisible" title="导入产品明细" width="500px" @close="resetImportForm">
      <el-form ref="importFormRef" label-width="100px">
        <el-form-item label="Excel文件">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            :show-file-list="true"
            :limit="1"
            :on-exceed="handleUploadExceed"
            :on-change="handleFileChange"
            :auto-upload="false"
            accept=".xlsx,.xls"
          >
            <el-icon class="el-icon--upload"><DocumentAdd /></el-icon>
            <div class="el-upload__text">
              将Excel文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 .xlsx/.xls 格式，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-alert
            title="智能导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p><strong>✨ 智能表头识别：</strong>系统会自动识别Excel表头，支持多种列名格式</p>
            <p><strong>📋 支持的列名：</strong></p>
            <ul style="margin: 5px 0; padding-left: 20px;">
              <li><strong>产品名称：</strong>产品名称、物料名称、名称、品名</li>
              <li><strong>产品型号：</strong>型号、产品型号、规格型号</li>
              <li><strong>产品规格：</strong>规格、产品规格、技术参数、规格描述</li>
              <li><strong>数量：</strong>数量、需求数量、用量、采购数量（必填）</li>
              <li><strong>单位：</strong>单位、计量单位</li>
              <li><strong>备注：</strong>备注、产品备注、说明</li>
            </ul>
            <p><strong>📝 使用提示：</strong></p>
            <ul style="margin: 5px 0; padding-left: 20px;">
              <li>Excel文件必须包含产品名称和数量列</li>
              <li>系统会自动跳过空行和无效数据</li>
              <li>导入的产品将添加到当前产品列表顶部</li>
              <li>如有格式问题，系统会提供详细的错误提示</li>
            </ul>
          </el-alert>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitImport" :loading="importLoading">确认导入</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 标准化处理/手动匹配调整 对话框 -->
    <el-dialog
      v-model="standardizeDialogVisible"
      title="需求条目匹配调整"
      width="95%"
      destroy-on-close
      top="3vh"
    >
      <el-table
        :data="standardizeItems"
        border
        stripe
        max-height="calc(90vh - 150px)"
      >
        <el-table-column type="index" label="#" width="45" fixed />
        <el-table-column prop="product_name" label="原始名称" min-width="150" show-overflow-tooltip fixed>
          <template #default="{row}">{{ row.product_name }}</template>
        </el-table-column>
        <el-table-column prop="product_model" label="原始型号" min-width="120" show-overflow-tooltip>
           <template #default="{row}">{{ row.product_model || '---' }}</template>
        </el-table-column>
        <el-table-column prop="product_spec" label="原始规格" min-width="150" show-overflow-tooltip>
          <template #default="{row}">{{ row.product_spec || '---' }}</template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />

        <el-table-column label="匹配产品" min-width="200">
          <template #default="{ row }">
            <el-select
              v-model="row.form_matched_product_id"
              filterable
              clearable
              placeholder="选择或搜索系统产品"
              style="width: 100%"
              @change="(productId) => handleDialogProductChange(row, productId)"
              remote
              :remote-method="searchProductsForDialog"
            >
              <el-option
                v-for="product in productOptionsForDialog"
                :key="product.id"
                :label="`${product.name}${product.model ? ` (${product.model})` : ''}`"
                :value="product.id"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="匹配规格" min-width="200">
          <template #default="{ row }">
            <el-select
              v-model="row.form_matched_specification_id"
              filterable
              clearable
              placeholder="选择产品规格"
              style="width: 100%"
              :disabled="!row.form_matched_product_id"
            >
              <el-option
                v-for="spec in getSpecificationsForProduct(row.form_matched_product_id)"
                :key="spec.id"
                :label="`${spec.specification} - ¥${spec.suggested_price || '0.00'}`"
                :value="spec.id"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="当前匹配状态" min-width="120">
          <template #default="{ row }">
            <el-tag v-if="row.form_matched_specification_id" type="success" size="small">
              已匹配
            </el-tag>
            <el-tag v-else-if="row.form_matched_product_id && !row.form_matched_specification_id" type="danger" size="small">
              需选择规格
            </el-tag>
            <el-tag v-else type="warning" size="small">
              待匹配
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="standardizeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStandardize" :loading="isSubmittingMatch">确认更新匹配</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Document,
  Check,
  Plus,
  Finished,
  DocumentAdd,
  Download,
  Connection,
  Link
} from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { quotationRequestApi } from '@/api/quotation'
import { customerApi, searchCustomers } from '@/api/customer'
import { productApi } from '@/api/product'
import type { UploadInstance, UploadProps, UploadRawFile, UploadFile, FormInstance } from 'element-plus'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref()

// 状态
const loading = ref(false)
const submitting = ref(false)
const customerLoading = ref(false)

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)
// 是否已确认状态
const isConfirmed = computed(() => form.status === '已确认')

// 选项数据
const customerOptions = ref<Array<any>>([])

// 批量添加对话框
const batchAddDialogVisible = ref(false)
const batchAddText = ref('')

// 导入相关
const importDialogVisible = ref(false)
const importLoading = ref(false)
const uploadRef = ref<UploadInstance>()
const importFormRef = ref<FormInstance>()
const fileToUpload = ref<UploadFile | null>(null)

// 客户创建相关
const customerNameFromImport = ref('')

// 匹配相关
const autoStandardizeLoading = ref(false)
const standardizeDialogVisible = ref(false)
const standardizeItems = ref<any[]>([])
const productOptionsForDialog = ref<any[]>([])
const productSpecificationsMap = ref<any>({})

const confirmStatusLoading = ref(false)
const unconfirmStatusLoading = ref(false)
const exportLoading = ref(false)
const isSubmittingMatch = ref(false)


// 表单数据
const form = reactive({
  customer_id: '',
  project_name: '',
  project_address: '',
  expected_date: '',
  notes: '',
  status: '待确认',
  items: [] as Array<{
    product_name: string
    product_model: string
    product_spec: string
    quantity: number
    unit: string
    notes: string
    matched_product_id?: number
    matched_product_specification_id?: number
    matched_specification?: any
    matched_product?: any
    [key: string]: any
  }>
})

// 表单验证规则
const rules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  project_name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  project_address: [
    { max: 200, message: '项目地址长度不能超过 200 个字符', trigger: 'blur' }
  ],
  notes: [
    { max: 1000, message: '备注长度不能超过 1000 个字符', trigger: 'blur' }
  ]
}

// 搜索客户
const handleSearchCustomers = async (query: string) => {
  if (query) {
    customerLoading.value = true
    try {
      const response = await searchCustomers({ name: query, per_page: 20 }) as any
      customerOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('搜索客户失败:', error)
    } finally {
      customerLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

// 快速创建客户
const handleQuickCreateCustomer = () => {
  // TODO: 实现快速创建客户功能
  ElMessage.info('快速创建客户功能开发中...')
}

// 获取报价需求详情（编辑模式）
const getRequestDetail = async () => {
  if (!isEdit.value) return

  try {
    loading.value = true
    const requestId = Number(route.params.id)
    const response = await quotationRequestApi.getById(requestId, { showLoading: false }) as any
    const request = response.data || response

    // 填充表单数据
    Object.assign(form, {
      customer_id: request.customer_id || '',
      project_name: request.project_name || '',
      project_address: request.project_address || '',
      expected_date: request.expected_date || '',
      notes: request.notes || '',
      status: request.status || '待确认',
      items: request.items || []
    })

    // 如果有客户ID，加载客户信息
    if (request.customer_id && request.customer) {
      customerOptions.value = [request.customer]
    }
  } catch (error) {
    console.error('获取报价需求详情失败:', error)
    ElMessage.error('获取报价需求详情失败')
  } finally {
    loading.value = false
  }
}

// 产品项目操作
const handleAddItem = () => {
  form.items.unshift({
    product_name: '',
    product_model: '',
    product_spec: '',
    quantity: 1,
    unit: '',
    notes: ''
  })
}

const handleRemoveItem = (index: number) => {
  form.items.splice(index, 1)
  ElMessage.success('删除成功')
}

// 产品名称输入处理（可以添加自动匹配逻辑）
const handleProductNameInput = (row: any) => {
  // TODO: 可以在这里添加产品名称自动匹配逻辑
  console.log('产品名称输入:', row.product_name)
}

// 批量添加相关
const handleOpenBatchAddDialog = () => {
  batchAddDialogVisible.value = true
  batchAddText.value = ''
}

const handleBatchDialogClose = () => {
  batchAddText.value = ''
}

const handleConfirmBatchAdd = () => {
  if (!batchAddText.value.trim()) {
    ElMessage.warning('请输入要添加的产品数据')
    return
  }

  try {
    const lines = batchAddText.value.trim().split('\n')
    const newItems: any[] = []

    lines.forEach((line, index) => {
      const trimmedLine = line.trim()
      if (!trimmedLine) return

      // 如果第一行包含"产品名称"等关键词，跳过（表头）
      if (index === 0 && (trimmedLine.includes('产品名称') || trimmedLine.includes('名称'))) {
        return
      }

      const columns = trimmedLine.split('\t') // 使用制表符分割
      if (columns.length >= 4) { // 至少需要产品名称、型号、规格、数量
        newItems.push({
          product_name: columns[0]?.trim() || '',
          product_model: columns[1]?.trim() || '',
          product_spec: columns[2]?.trim() || '',
          quantity: parseInt(columns[3]?.trim()) || 1,
          unit: columns[4]?.trim() || '',
          notes: columns[5]?.trim() || ''
        })
      }
    })

    // 将新项目添加到表格顶部
    if (newItems.length > 0) {
      form.items.unshift(...newItems)
    }

    if (newItems.length > 0) {
      ElMessage.success(`成功添加 ${newItems.length} 个产品`)
      batchAddDialogVisible.value = false
      batchAddText.value = ''
    } else {
      ElMessage.warning('没有识别到有效的产品数据，请检查格式')
    }
  } catch (error) {
    console.error('批量添加失败:', error)
    ElMessage.error('批量添加失败，请检查数据格式')
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  if (!isEdit.value) {
    Object.assign(form, {
      customer_id: '',
      project_name: '',
      project_address: '',
      expected_date: '',
      notes: '',
      items: []
    })
  }
}

// 返回操作
const handleCancel = async () => {
  // 检查是否有未保存的数据
  const hasUnsavedData = form.customer_id || form.project_name || form.project_address ||
                        form.expected_date || form.notes || form.items.length > 0

  if (hasUnsavedData) {
    try {
      await ElMessageBox.confirm(
        '当前页面有未保存的数据，返回后将会丢失。确定要返回吗？',
        '确认返回',
        {
          confirmButtonText: '确定返回',
          cancelButtonText: '继续编辑',
          type: 'warning',
        }
      )
    } catch {
      // 用户点击取消，不执行返回操作
      return
    }
  }

  // 返回列表页
  router.push('/quotation-requests')
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()

    // 检查是否有产品明细
    if (!form.items.length) {
      ElMessage.warning('请至少添加一个产品')
      return
    }

    loading.value = true

    // 准备提交数据
    const submitData = { ...form }

    // 清理items中的只读字段，但保留匹配信息
    if (submitData.items && Array.isArray(submitData.items)) {
      submitData.items = submitData.items.map(item => {
        const cleanItem = { ...item }
        // 移除只读字段
        delete cleanItem.id
        delete cleanItem.created_at
        delete cleanItem.updated_at
        // 保留匹配信息，这些需要保存到后端
        // delete cleanItem.matched_product_id
        // delete cleanItem.matched_product_specification_id
        // 移除前端临时字段
        delete cleanItem.matched_product
        delete cleanItem.matched_specification
        delete cleanItem.original_product_name
        delete cleanItem.original_product_model
        delete cleanItem.original_product_spec
        delete cleanItem.original_unit
        // 移除表单临时字段
        delete cleanItem.form_matched_product_id
        delete cleanItem.form_matched_specification_id

        // 确保单位字段不为空
        if (!cleanItem.unit || cleanItem.unit.trim() === '') {
          // 尝试从匹配的产品获取单位
          if (item.matched_product?.unit) {
            cleanItem.unit = item.matched_product.unit
          } else {
            // 提供默认单位
            cleanItem.unit = '个'
          }
        }

        return cleanItem
      })
    }

    // 处理空值
    Object.keys(submitData).forEach(key => {
      if ((submitData as any)[key] === '' || (submitData as any)[key] === null) {
        delete (submitData as any)[key]
      }
    })

    if (isEdit.value) {
      // 更新报价需求
      const requestId = Number(route.params.id)
      await quotationRequestApi.update(requestId, submitData)
      ElMessage.success('更新报价需求成功')

      // 编辑模式下跳转到详情页
      router.push(`/quotation-requests/view/${requestId}`)
    } else {
      // 创建报价需求
      const response = await quotationRequestApi.create(submitData)
      ElMessage.success('创建报价需求成功')

      // 新建模式下跳转到列表页
      router.push('/quotation-requests')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(isEdit.value ? '更新报价需求失败' : '创建报价需求失败')
  } finally {
    loading.value = false
  }
}

// 导入相关函数
const resetImportForm = () => {
  fileToUpload.value = null
  uploadRef.value?.clearFiles()
  if (importFormRef.value) {
    importFormRef.value.resetFields()
  }
}

const handleFileChange: UploadProps['onChange'] = async (uploadFile) => {
  if (uploadFile.raw) {
    const isExcel = uploadFile.name.endsWith('.xlsx') || uploadFile.name.endsWith('.xls')
    const isLt10M = (uploadFile.size || 0) / 1024 / 1024 < 10

    if (!isExcel) {
      ElMessage.error('只能上传 Excel 文件 (.xlsx, .xls)!')
      uploadRef.value?.clearFiles()
      fileToUpload.value = null
      return
    }

    if (!isLt10M) {
      ElMessage.error('上传文件大小不能超过 10MB!')
      uploadRef.value?.clearFiles()
      fileToUpload.value = null
      return
    }

    fileToUpload.value = uploadFile

    // 增强的Excel文件预验证
    try {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const data = e.target?.result
          const workbook = XLSX.read(data, { type: 'binary', cellDates: true })
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          const sheetData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' })

          if (!sheetData || sheetData.length === 0) {
            ElMessage.error('Excel文件内容为空')
            uploadRef.value?.clearFiles()
            fileToUpload.value = null
            return
          }

          // 使用智能解析进行预验证 + 解析头部信息并尝试填充
          try {
            const { items, errors } = parseExcelWithIntelligentMapping(sheetData)

            // 解析头部信息并应用到表单
            const header = parseHeaderInfoFromSheet(sheetData)
            const applied = applyHeaderInfoToForm(header)
            // 预验证阶段不弹提示，避免多次消息；仅控制台记录
            if (applied.length) {
              console.log(`预验证：已从Excel填充 -> ${applied.join('、')}`)
            }

            // 自动匹配客户（静默，不弹窗）
            if (header.customer_name) {
              await autoMatchCustomerByName(header.customer_name, { notify: false })
            }

            // 预验证阶段不弹“格式正确”提示，避免重复弹窗
            if (items.length > 0) {
              console.log('预验证结果:', { items: items.length, errors: errors.length, header })
            } else {
              ElMessage.warning('Excel文件中未检测到有效的产品数据')
            }
          } catch (validationError: any) {
            ElMessage.error(`文件格式验证失败: ${validationError.message}`)
            uploadRef.value?.clearFiles()
            fileToUpload.value = null
            return
          }
        } catch (parseError) {
          console.error('解析Excel文件失败:', parseError)
          ElMessage.error('解析Excel文件失败，请检查文件格式')
          uploadRef.value?.clearFiles()
          fileToUpload.value = null
        }
      }
      reader.readAsBinaryString(uploadFile.raw)
    } catch (error) {
      console.error('读取Excel文件失败:', error)
      ElMessage.error('读取Excel文件失败')
    }
  } else {
    fileToUpload.value = null
  }
}

const handleUploadExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  uploadRef.value!.handleStart(file)

  const newUploadFile: UploadFile = {
    name: file.name,
    size: file.size,
    raw: file,
    uid: file.uid,
    status: 'ready'
  }
  handleFileChange(newUploadFile, [newUploadFile])
}

// 头部信息解析：从前若干行识别项目名称/地址/日期/备注/客户名称
const parseHeaderInfoFromSheet = (sheetData: any[][]) => {
  const headerInfo: any = {
    project_name: '',
    project_address: '',
    expected_date: '',
    notes: '',
    customer_name: ''
  }
  const projectKeywords = ['项目名称', '项目名', '项目', '工程名称']
  const addressKeywords = ['项目地址', '地址', '送货地址']
  const dateKeywords = ['预计采购时间', '期望日期', '日期']
  const overallNotesKeywords = ['备注', '说明']
  const customerKeywords = ['客户名称', '客户', '公司名称', '询价单位']

  const maxRows = Math.min(sheetData.length, 15)
  for (let i = 0; i < maxRows; i++) {
    const row = sheetData[i]
    if (!row || !Array.isArray(row)) continue
    for (let j = 0; j < row.length; j++) {
      const cellValue = String(row[j] ?? '').trim()
      const nextCell = String((j + 1 < row.length ? (row[j + 1] ?? '') : '')).trim()
      if (!cellValue) continue

      if (!headerInfo.project_name && projectKeywords.some(kw => cellValue.includes(kw))) {
        headerInfo.project_name = nextCell
      }
      if (!headerInfo.project_address && addressKeywords.some(kw => cellValue.includes(kw))) {
        headerInfo.project_address = nextCell
      }
      if (!headerInfo.expected_date && dateKeywords.some(kw => cellValue.includes(kw))) {
        headerInfo.expected_date = nextCell
      }
      if (!headerInfo.notes && overallNotesKeywords.some(kw => cellValue.includes(kw))) {
        headerInfo.notes = nextCell
      }
      if (!headerInfo.customer_name && customerKeywords.some(kw => cellValue.includes(kw))) {
        headerInfo.customer_name = nextCell
      }
    }
  }
  return headerInfo
}

// 将多种日期文本格式规范化为 YYYY-MM-DD
const formatToYYYYMMDD = (val: string): string => {
  if (!val) return ''
  const s = String(val).trim()
  // 2025-08-08 或 2025/8/8 或 2025.8.8
  const m = s.match(/^(\d{4})[-\/.](\d{1,2})[-\/.](\d{1,2})/)
  if (m) {
    const y = m[1]
    const mm = m[2].padStart(2, '0')
    const dd = m[3].padStart(2, '0')
    return `${y}-${mm}-${dd}`
  }
  return s // 兜底返回原值
}

// 将头部信息自动填入表单（仅在目标字段为空时）
const applyHeaderInfoToForm = (headerInfo: any) => {
  const applied: string[] = []
  if (headerInfo.project_name && !form.project_name) {
    form.project_name = headerInfo.project_name
    applied.push('项目名称')
  }
  if (headerInfo.project_address && !form.project_address) {
    form.project_address = headerInfo.project_address
    applied.push('项目地址')
  }
  if (headerInfo.expected_date && !form.expected_date) {
    const d = formatToYYYYMMDD(headerInfo.expected_date)
    if (d) {
      form.expected_date = d
      applied.push('预计采购时间')
    }
  }
  if (headerInfo.notes && !form.notes) {
    form.notes = headerInfo.notes
    applied.push('备注')
  }
  return applied
}

// 按客户名称自动匹配：唯一匹配则直接选择，否则保留占位以便创建
const autoMatchCustomerByName = async (name: string, options: { notify?: boolean } = {}) => {
  const { notify = true } = options
  try {
    if (!name) return
    if (form.customer_id) return
    const resp: any = await searchCustomers({ name, per_page: 50 })
    const list = Array.isArray(resp) ? resp : (resp.data || resp.items || [])
    if (Array.isArray(list)) {
      const exact = list.filter((c: any) => c && (c.name === name))
      if (exact.length === 1) {
        form.customer_id = exact[0].id
        customerOptions.value = [exact[0]]
        customerNameFromImport.value = ''
        if (notify) ElMessage.success(`已自动选择客户：${exact[0].name}`)
      } else if (list.length === 1) {
        form.customer_id = list[0].id
        customerOptions.value = [list[0]]
        customerNameFromImport.value = ''
        if (notify) ElMessage.success(`已自动选择客户：${list[0].name}`)
      } else {
        // 多个或没有，保留占位，等待用户选择/创建
        customerNameFromImport.value = name
        if (notify) {
          if (list.length > 1) {
            ElMessage.info(`找到多个同名客户，请手动选择`)
          } else {
            ElMessage.info(`未找到客户“${name}”，可点击右侧按钮创建`)
          }
        }
      }
    }
  } catch (e) {
    console.warn('自动匹配客户失败：', e)
  }
}


// 智能Excel解析函数
const parseExcelWithIntelligentMapping = (sheetData: any[][]) => {
  // 定义列映射关键词（优化优先级和匹配逻辑）
  const columnMapping = {
    'product_name': ['产品名称', '物料名称', '材料名称', '名称', '品名', '产品'],
    'product_model': ['型号', '产品型号', '规格型号', '模型', '编号'],
    'product_spec': ['产品规格', '技术参数', '规格描述', '参数', '规格'],
    'quantity': ['数量', '需求数量', '用量', '采购数量', 'qty'],
    'unit': ['单位', '计量单位'],
    'notes': ['备注', '产品备注', '说明', '描述']
  }

  // 1. 智能识别表头行
  let headerRowIndex = -1
  const headerKeywords = Object.values(columnMapping).flat()

  for (let i = 0; i < Math.min(sheetData.length, 10); i++) {
    const row = sheetData[i]
    if (!row || !Array.isArray(row)) continue

    // 检查这一行是否包含表头关键词
    const matchCount = row.filter(cell => {
      const cellText = String(cell || '').trim()
      // 修复：空字符串不应该匹配任何关键词
      if (!cellText) return false

      return headerKeywords.some(keyword => {
        // 修复：只有当单元格文本包含关键词时才匹配，避免空字符串问题
        return cellText.includes(keyword) && keyword.length > 0
      })
    }).length

    // 如果匹配到3个或以上关键词，认为是表头行
    if (matchCount >= 3) {
      headerRowIndex = i
      console.log(`智能识别到表头行: 第${i + 1}行, 匹配关键词数: ${matchCount}`)
      break
    }
  }

  if (headerRowIndex === -1) {
    throw new Error('未能识别到有效的表头行，请确保Excel文件包含产品名称、规格、数量等列')
  }

  // 2. 动态列映射
  const headerRow = sheetData[headerRowIndex]
  const columnMap: Record<string, number> = {}

  headerRow.forEach((cell, index) => {
    const cellText = String(cell || '').trim().toLowerCase()

    // 为每个字段找到最匹配的列（优化匹配策略）
    Object.entries(columnMapping).forEach(([field, keywords]) => {
      if (!columnMap[field] && cellText) { // 如果该字段还没有映射且单元格不为空
        // 按优先级匹配：完全匹配 > 包含匹配
        let matchScore = 0
        let matchedKeyword = ''

        for (const keyword of keywords) {
          const keywordLower = keyword.toLowerCase()
          if (keywordLower.length === 0) continue

          // 完全匹配（最高优先级）
          if (cellText === keywordLower) {
            matchScore = 100
            matchedKeyword = keyword
            break
          }
          // 单元格文本包含关键词
          else if (cellText.includes(keywordLower)) {
            const score = 50 + (keywordLower.length / cellText.length) * 30
            if (score > matchScore) {
              matchScore = score
              matchedKeyword = keyword
            }
          }
          // 关键词包含单元格文本（较低优先级）
          else if (keywordLower.includes(cellText)) {
            const score = 20 + (cellText.length / keywordLower.length) * 20
            if (score > matchScore) {
              matchScore = score
              matchedKeyword = keyword
            }
          }
        }

        // 只有匹配分数足够高才进行映射
        if (matchScore >= 20) {
          columnMap[field] = index
          console.log(`映射字段 ${field} 到第 ${index + 1} 列: ${cell} (匹配: ${matchedKeyword}, 分数: ${matchScore})`)
        }
      }
    })
  })

  // 3. 验证必要字段
  const requiredFields = ['product_name', 'quantity']
  const missingFields = requiredFields.filter(field => columnMap[field] === undefined)
  if (missingFields.length > 0) {
    throw new Error(`缺少必要的列: ${missingFields.map(f =>
      columnMapping[f as keyof typeof columnMapping][0]
    ).join(', ')}`)
  }

  // 4. 解析数据行
  const items: any[] = []
  const errors: string[] = []

  for (let i = headerRowIndex + 1; i < sheetData.length; i++) {
    const row = sheetData[i]
    if (!row || !Array.isArray(row)) continue

    // 检查是否为空行
    if (row.every(cell => !String(cell || '').trim())) continue

    const productName = String(row[columnMap.product_name] || '').trim()
    const quantityStr = String(row[columnMap.quantity] || '').trim()

    // 验证必要字段
    if (!productName) {
      errors.push(`第${i + 1}行: 产品名称不能为空`)
      continue
    }

    // 解析数量
    let quantity = 1
    if (quantityStr) {
      const parsedQty = parseFloat(quantityStr)
      if (isNaN(parsedQty) || parsedQty <= 0) {
        errors.push(`第${i + 1}行: 数量"${quantityStr}"无效，已设为默认值1`)
      } else {
        quantity = parsedQty
      }
    }

    const item = {
      product_name: productName,
      product_model: columnMap.product_model !== undefined ?
        String(row[columnMap.product_model] || '').trim() : '',
      product_spec: columnMap.product_spec !== undefined ?
        String(row[columnMap.product_spec] || '').trim() : '',
      quantity: quantity,
      unit: columnMap.unit !== undefined ?
        String(row[columnMap.unit] || '').trim() : '',
      notes: columnMap.notes !== undefined ?
        String(row[columnMap.notes] || '').trim() : ''
    }

    items.push(item)
  }

  return { items, errors, columnMap }
}

const submitImport = async () => {
  if (!fileToUpload.value) {
    ElMessage.warning('请先选择要导入的Excel文件')
    return
  }

  importLoading.value = true
  try {
    // 解析Excel文件并添加到产品列表
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const data = e.target?.result
        const workbook = XLSX.read(data, { type: 'binary', cellDates: true })
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        const sheetData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' })

        // 使用智能解析（包括列映射）
        const { items: newItems, errors, columnMap } = parseExcelWithIntelligentMapping(sheetData)

        // 解析头部信息并填充（导入时再执行一次，确保填充生效）
        const header = parseHeaderInfoFromSheet(sheetData)
        const applied = applyHeaderInfoToForm(header)
        // 导入阶段仅在有填充时给一个合并提示
        if (applied.length) {
          ElMessage.info(`已从Excel填充 ${applied.join('、')}`)
        }
        if (header.customer_name) {
          await autoMatchCustomerByName(header.customer_name, { notify: true })
        }

        // 显示解析结果（有错误才提示）
        if (errors.length > 0) {
          console.warn('导入过程中的警告:', errors)
          ElMessage.warning(`导入完成，但有 ${errors.length} 个警告，请检查控制台`)
        }

        // 将新项目添加到表格顶部
        if (newItems.length > 0) {
          form.items.unshift(...newItems)
          ElMessage.success(`已导入 ${newItems.length} 个产品`)
          console.log('列映射结果:', columnMap)
          console.log('导入的产品:', newItems)
          importDialogVisible.value = false
          resetImportForm()
        } else {
          ElMessage.warning('没有识别到有效的产品数据，请检查文件格式')
        }
      } catch (parseError: any) {
        console.error('解析Excel文件失败:', parseError)
        ElMessage.error(parseError.message || '解析Excel文件失败，请检查文件格式')
      } finally {
        importLoading.value = false
      }
    }
    reader.readAsBinaryString(fileToUpload.value.raw!)
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
    importLoading.value = false
  }
}

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const response = await quotationRequestApi.downloadTemplate()

    const blob = new Blob([response as any], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `询价单模板_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  }
}

// 未保存状态下的本地自动匹配（不依赖后端ID）
const localAutoMatchItems = async () => {
  autoStandardizeLoading.value = true
  try {
    if (!form.items.length) {
      ElMessage.warning('没有需要匹配的产品项目')
      return
    }

    let processed = 0
    let matched = 0

    for (const item of form.items) {
      if (item.matched_product_id) continue // 已有匹配则跳过

      const nameToMatch = String(item.product_name || '').trim()
      const modelToMatch = String(item.product_model || '').trim()
      const specToMatch = String(item.product_spec || '').trim()
      if (!nameToMatch) continue

      try {
        const params: any = {
          search: nameToMatch,
          per_page: 20,
          page: 1,
          with_specifications: true
        }
        const resp: any = await productApi.getList(params, { showLoading: false })
        // 兼容多种响应结构
        const list = (resp && (resp.items || resp.data || resp.records || resp.results)) || []

        let candidates = Array.isArray(list) ? list : []
        if (candidates.length > 1 && modelToMatch) {
          const mm = modelToMatch.toLowerCase()
          candidates = candidates.filter((p: any) => (p.model || '').toLowerCase() === mm)
        }
        if (candidates.length > 1) {
          // 再次用名称做精确匹配
          candidates = candidates.filter((p: any) => (p.name || '') === nameToMatch)
        }
        if (candidates.length === 1) {
          const p = candidates[0]
          item.matched_product_id = p.id
          // 单位联动（前端展示友好，不改变后端逻辑）
          if (p.unit && !item.unit) item.unit = p.unit

          // 规格匹配
          const specs: any[] = (p.specifications || [])
          if (specs.length === 1 && !specToMatch) {
            item.matched_product_specification_id = specs[0].id
            item.product_spec = specs[0].specification || item.product_spec
          } else if (specToMatch && specs.length) {
            const target = specs.find(s => String(s.specification || '').toLowerCase().includes(specToMatch.toLowerCase()))
            if (target) {
              item.matched_product_specification_id = target.id
              item.product_spec = target.specification || item.product_spec
            } else {
              item.matched_product_specification_id = null
            }
          }

          matched += 1
        }
        processed += 1
      } catch (e) {
        console.warn('本地自动匹配失败(单项)：', e)
      }
    }

    if (processed === 0) {
      ElMessage.info('未找到可匹配的条目（可能均已匹配或缺少产品名称）')
    } else {
      ElMessage.success(`本地自动匹配完成：处理 ${processed} 条，成功匹配 ${matched} 条。保存后可持久化匹配结果。`)
    }
  } finally {
    autoStandardizeLoading.value = false
  }
}


// 单个条目调整匹配
const openSingleStandardizeDialog = async (item: any) => {
  await openStandardizeDialogForItem(item)
}

const handleAutoStandardizeItems = async () => {
  if (!form.items.length) {
    ElMessage.warning('没有需要规整的产品项目')
    return
  }

  // 检查是否有待匹配的条目
  const unmatchedItems = form.items.filter(item => !item.matched_product_id)
  if (unmatchedItems.length === 0) {
    ElMessage.info('所有产品都已匹配，无需进行自动匹配')
    return
  }

  if (!isEdit.value) {
    // 未保存：走前端本地匹配，不要求保存
    await localAutoMatchItems()
    return
  }

  autoStandardizeLoading.value = true
  try {
    // 先保存当前状态（包括删除操作），但不显示成功消息
    await saveCurrentState()

    const requestId = Number(route.params.id)
    const response = await quotationRequestApi.autoMatch(requestId, { showLoading: false }) as any
    const result = response.data || response

    // 重新加载数据以显示匹配结果
    await getRequestDetail()

    ElMessage.success(`自动匹配完成！处理了 ${unmatchedItems.length} 个待匹配条目，成功匹配 ${result.matched_count || 0} 个产品`)
  } catch (error) {
    console.error('自动匹配失败:', error)
    ElMessage.error('自动匹配失败')
  } finally {
    autoStandardizeLoading.value = false
  }
}

// 内部保存函数，不显示成功消息
const saveCurrentState = async () => {
  // 表单验证
  await formRef.value?.validate()

  // 检查是否有产品明细
  if (!form.items.length) {
    throw new Error('请至少添加一个产品')
  }

  // 准备提交数据
  const submitData = { ...form }

  // 清理items中的只读字段，但保留匹配信息
  if (submitData.items && Array.isArray(submitData.items)) {
    submitData.items = submitData.items.map(item => {
      const cleanItem = { ...item }
      // 移除只读字段
      delete cleanItem.id
      delete cleanItem.created_at
      delete cleanItem.updated_at
      // 移除前端临时字段
      delete cleanItem.matched_product
      delete cleanItem.matched_specification
      delete cleanItem.original_product_name
      delete cleanItem.original_product_model
      delete cleanItem.original_product_spec
      delete cleanItem.original_unit
      delete cleanItem.form_matched_product_id
      delete cleanItem.form_matched_specification_id

      // 确保单位字段不为空
      if (!cleanItem.unit || cleanItem.unit.trim() === '') {
        // 尝试从匹配的产品获取单位
        if (item.matched_product?.unit) {
          cleanItem.unit = item.matched_product.unit
        } else {
          // 提供默认单位
          cleanItem.unit = '个'
        }
      }

      return cleanItem
    })
  }

  // 处理空值
  Object.keys(submitData).forEach(key => {
    if ((submitData as any)[key] === '' || (submitData as any)[key] === null) {
      delete (submitData as any)[key]
    }
  })

  if (isEdit.value) {
    // 更新报价需求
    const requestId = Number(route.params.id)
    await quotationRequestApi.update(requestId, submitData, { showLoading: false })
  } else {
    // 创建报价需求
    await quotationRequestApi.create(submitData, { showLoading: false })
  }
}





// 确认状态
const handleConfirmStatus = async () => {
  if (!isEdit.value) {
    ElMessage.warning('请先保存报价需求')
    return
  }

  // 检查是否有产品明细
  if (!form.items.length) {
    ElMessage.warning('请先添加产品明细')
    return
  }

  confirmStatusLoading.value = true
  try {
    // 先保存产品明细，确保最新的匹配状态被保存
    await saveCurrentState()

    // 重新获取最新数据，确保检查的是后端保存的状态
    await getRequestDetail()

    // 检查所有产品是否都已匹配
    const unmatchedItems = form.items.filter(item => !item.matched_product_id)
    if (unmatchedItems.length > 0) {
      const unmatchedNames = unmatchedItems.map(item => item.product_name || '未命名产品').join('、')
      ElMessage.warning(`以下产品尚未匹配，请先完成匹配：${unmatchedNames}`)
      return
    }

    // 更新状态为已确认
    const requestId = Number(route.params.id)
    await quotationRequestApi.updateStatus(requestId, { status: '已确认' })

    // 更新本地状态
    form.status = '已确认'

    ElMessage.success('确认成功')
  } catch (error) {
    console.error('确认失败:', error)
    ElMessage.error('确认失败')
  } finally {
    confirmStatusLoading.value = false
  }
}

// 创建报价单
const handleGenerateQuotation = async () => {
  try {
    await ElMessageBox.confirm('确定要从此报价需求创建报价单吗？', '创建报价单', {
      type: 'warning'
    })

    // 跳转到新建报价单页面，传递报价需求表ID作为参数
    const requestId = route.params.id
    router.push(`/quotations/new?requestId=${requestId}`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('跳转失败:', error)
    }
  }
}

// 转为未确认
const handleUnconfirmStatus = async () => {
  if (!isEdit.value) {
    ElMessage.warning('请先保存报价需求')
    return
  }

  unconfirmStatusLoading.value = true
  try {
    const requestId = Number(route.params.id)
    await quotationRequestApi.updateStatus(requestId, { status: '待确认' })

    // 更新本地状态
    form.status = '待确认'

    ElMessage.success('已转为未确认状态，可以继续编辑')
  } catch (error) {
    console.error('转为未确认失败:', error)
    ElMessage.error('转为未确认失败')
  } finally {
    unconfirmStatusLoading.value = false
  }
}

// 导出需求表
const handleExportRequest = async () => {
  if (!isEdit.value) {
    ElMessage.warning('请先保存报价需求')
    return
  }

  exportLoading.value = true
  try {
    const requestId = Number(route.params.id)
    const response = await quotationRequestApi.export(requestId)

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `报价需求表_${form.project_name || '未命名'}_${new Date().toLocaleDateString()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const openStandardizeDialog = async () => {
  if (!form.items.length) {
    ElMessage.warning('没有需要匹配的产品项目')
    return
  }

  // 先预加载产品选项，避免对话框打开后的闪烁
  await ensureProductOptionsLoaded()

  // 复制数据并添加表单字段，同时添加原始索引用于关联
  standardizeItems.value = form.items.map((item, index) => ({
    ...item,
    form_matched_product_id: item.matched_product_id || null,
    form_matched_specification_id: item.matched_product_specification_id || null,
    _originalIndex: index  // 添加原始索引用于关联
  }))

  // 预加载所有已匹配产品的规格选项
  const matchedProductIds = standardizeItems.value
    .map(item => item.matched_product_id)
    .filter(id => id)

  for (const productId of matchedProductIds) {
    await loadSpecificationsForProduct(productId)
  }

  standardizeDialogVisible.value = true
}

const openStandardizeDialogForItem = async (item: any) => {
  // 先预加载产品选项，避免对话框打开后的闪烁
  await ensureProductOptionsLoaded()

  // 复制数据并添加表单字段，同时添加原始索引用于关联
  const originalIndex = form.items.findIndex(formItem => formItem === item)
  standardizeItems.value = [{
    ...item,
    form_matched_product_id: item.matched_product_id || null,
    form_matched_specification_id: item.matched_product_specification_id || null,
    _originalIndex: originalIndex  // 添加原始索引用于关联
  }]

  // 如果已经匹配了产品但没有规格，预加载该产品的规格选项
  if (item.matched_product_id && !item.matched_product_specification_id) {
    await loadSpecificationsForProduct(item.matched_product_id)
  }

  standardizeDialogVisible.value = true
}

const handleDialogProductChange = (row: any, productId: number) => {
  // 立即更新UI状态，避免闪烁
  row.form_matched_product_id = productId
  row.form_matched_specification_id = null

  // 如果选择了产品，预加载规格数据
  if (productId && !productSpecificationsMap.value[productId]) {
    loadSpecificationsForProduct(productId)
  }
}

const loadSpecificationsForProduct = async (productId: number) => {
  if (!productId) return

  // 如果已经加载过，直接返回
  if (productSpecificationsMap.value[productId]) {
    return
  }

  try {
    // 通过获取产品详情来获取规格信息
    const response = await productApi.getById(productId) as any
    const product = response.data || response
    const specifications = product.specifications || []

    // 使用Vue的响应式更新，避免直接赋值导致的闪烁
    productSpecificationsMap.value = {
      ...productSpecificationsMap.value,
      [productId]: specifications
    }

    console.log(`加载产品${productId}的规格:`, specifications)
  } catch (error) {
    console.error('获取产品规格失败:', error)
    // 即使失败也要设置空数组，避免重复请求
    productSpecificationsMap.value = {
      ...productSpecificationsMap.value,
      [productId]: []
    }
    ElMessage.error('获取产品规格失败')
  }
}

const searchProductsForDialog = async (query: string) => {
  try {
    if (query && query.trim()) {
      // 使用综合搜索参数，同时搜索产品名称和型号
      const response = await productApi.getList({
        search: query.trim()
      } as any) as any
      productOptionsForDialog.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } else {
      // 清空搜索词时，显示默认产品列表
      if (productOptionsForDialog.value.length === 0) {
        ensureProductOptionsLoaded()
      }
    }
  } catch (error) {
    console.error('搜索产品失败:', error)
  }
}

const ensureProductOptionsLoaded = async (forceReload = false) => {
  // 如果没有产品选项或强制重新加载，加载默认产品列表
  if (productOptionsForDialog.value.length === 0 || forceReload) {
    try {
      const response = await productApi.getList({} as any) as any
      productOptionsForDialog.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('加载产品选项失败:', error)
    }
  }
}

const submitStandardize = () => {
  // 验证匹配完整性
  const incompleteItems = standardizeItems.value.filter(item =>
    item.form_matched_product_id && !item.form_matched_specification_id
  )

  if (incompleteItems.length > 0) {
    const itemNames = incompleteItems.map(item => item.product_name).join('、')
    ElMessage.warning(`以下产品已选择匹配产品但未选择规格：${itemNames}，请完善匹配信息后再确认`)
    return
  }

  // 将匹配结果应用到原始数据
  standardizeItems.value.forEach(dialogItem => {
    // 使用原始索引直接定位到对应的项目
    const originalItem = form.items[dialogItem._originalIndex]

    if (originalItem) {
      originalItem.matched_product_id = dialogItem.form_matched_product_id
      originalItem.matched_product_specification_id = dialogItem.form_matched_specification_id

      // 设置为手动匹配
      if (dialogItem.form_matched_product_id) {
        originalItem.match_type = 'manual'
      } else {
        originalItem.match_type = null
      }

      // 如果有匹配的规格，更新匹配信息
      if (dialogItem.form_matched_specification_id) {
        const spec = productSpecificationsMap.value[dialogItem.form_matched_product_id]?.find(
          (s: any) => s.id === dialogItem.form_matched_specification_id
        )
        if (spec) {
          originalItem.matched_specification = spec
          // 同时保存匹配的产品信息
          const product = productOptionsForDialog.value.find(p => p.id === dialogItem.form_matched_product_id)
          if (product) {
            originalItem.matched_product = product
          }
        }
      } else {
        // 清空匹配信息
        originalItem.matched_specification = null
        originalItem.matched_product = null
      }
    }
  })

  ElMessage.success('匹配更新成功')
  standardizeDialogVisible.value = false
}

// 获取匹配产品名称
const getMatchedProductName = (row: any) => {
  // 优先从matched_product获取
  if (row.matched_product?.name) {
    return row.matched_product.name
  }

  // 从matched_specification.product获取
  if (row.matched_specification?.product?.name) {
    return row.matched_specification.product.name
  }

  // 从productOptionsForDialog中查找
  if (row.matched_product_id) {
    const product = productOptionsForDialog.value.find(p => p.id === row.matched_product_id)
    if (product?.name) {
      return product.name
    }
  }

  return '未知产品'
}

// 获取匹配产品型号
const getMatchedProductModel = (row: any) => {
  // 优先从matched_product获取
  if (row.matched_product?.model) {
    return row.matched_product.model
  }

  // 从matched_specification.product获取
  if (row.matched_specification?.product?.model) {
    return row.matched_specification.product.model
  }

  // 从productOptionsForDialog中查找
  if (row.matched_product_id) {
    const product = productOptionsForDialog.value.find(p => p.id === row.matched_product_id)
    if (product?.model) {
      return product.model
    }
  }

  return '-'
}

// 获取匹配产品单位
const getMatchedProductUnit = (row: any) => {
  // 优先从matched_product获取
  if (row.matched_product?.unit) {
    return row.matched_product.unit
  }

  // 从matched_specification.product获取
  if (row.matched_specification?.product?.unit) {
    return row.matched_specification.product.unit
  }

  // 从productOptionsForDialog中查找
  if (row.matched_product_id) {
    const product = productOptionsForDialog.value.find(p => p.id === row.matched_product_id)
    if (product?.unit) {
      return product.unit
    }
  }

  return '-'
}

// 安全获取产品规格列表
const getSpecificationsForProduct = (productId: number) => {
  if (!productId) return []
  return productSpecificationsMap.value[productId] || []
}

// 获取匹配规格名称
const getMatchedSpecificationName = (row: any) => {
  // 优先从matched_specification获取
  if (row.matched_specification?.specification) {
    return row.matched_specification.specification
  }

  // 从productSpecificationsMap中查找
  if (row.matched_product_id && row.matched_product_specification_id) {
    const specs = productSpecificationsMap.value[row.matched_product_id] || []
    const spec = specs.find((s: any) => s.id === row.matched_product_specification_id)
    if (spec?.specification) {
      return spec.specification
    }
  }

  return '无规格'
}

// 获取匹配规格价格
const getMatchedSpecificationPrice = (row: any) => {
  // 优先从matched_specification获取
  if (row.matched_specification?.suggested_price) {
    return row.matched_specification.suggested_price
  }

  // 从productSpecificationsMap中查找
  if (row.matched_product_id && row.matched_product_specification_id) {
    const specs = productSpecificationsMap.value[row.matched_product_id] || []
    const spec = specs.find((s: any) => s.id === row.matched_product_specification_id)
    if (spec?.suggested_price) {
      return spec.suggested_price
    }
  }

  return '0.00'
}



// 初始化
onMounted(() => {
  if (isEdit.value) {
    getRequestDetail()
  }
})
</script>

<style lang="scss" scoped>
.quotation-request-edit {
  padding: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
}

.quotation-request-form {
  .table-operations {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .operations-left {
      display: flex;
      align-items: center;
    }

    .operations-right {
      display: flex;
      align-items: center;
    }
  }
}

.el-form-item__extra_info {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}

.upload-demo {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }

  :deep(.el-icon--upload) {
    font-size: 67px;
    color: var(--el-text-color-placeholder);
    margin: 40px 0 16px;
    line-height: 50px;
  }

  :deep(.el-upload__text) {
    color: var(--el-text-color-regular);
    font-size: 14px;
    text-align: center;

    em {
      color: var(--el-color-primary);
      font-style: normal;
    }
  }
}

.matching-active {
  background-color: var(--el-color-danger) !important;
  border-color: var(--el-color-danger) !important;
  color: white !important;
}

.matching-mode {
  .matching-header {
    margin-bottom: 20px;
  }

  .matching-operations {
    margin-bottom: 20px;
  }

  .matched-product {
    font-weight: bold;
    color: var(--el-color-primary);
  }

  .matched-spec {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-top: 2px;
  }

  .matched-price {
    font-size: 12px;
    color: var(--el-color-success);
    margin-top: 2px;
  }

  .text-muted {
    color: var(--el-text-color-placeholder);
  }

  .unmatched-section {
    .match-actions {
      margin-top: 8px;
    }

    .match-notes {
      margin: 4px 0;
      padding: 4px 8px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
      border-left: 3px solid var(--el-text-color-placeholder);
      font-size: 12px;
    }
  }

  .original-product {
    margin-bottom: 6px;
    padding: 4px 8px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
    font-size: 12px;
  }

  .matched-product {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 4px;

    .el-icon {
      color: var(--el-color-success);
    }
  }

  .matched-info {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
    font-size: 12px;
    color: var(--el-color-success);

    .el-icon {
      font-size: 12px;
    }

    span {
      color: var(--el-color-success);
    }
  }

  .table-operations {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .operations-left {
      display: flex;
      align-items: center;
    }

    .operations-right {
      display: flex;
      align-items: center;
    }

    .matching-active {
      background-color: var(--el-color-danger);
      border-color: var(--el-color-danger);
      color: white;
    }
  }

  .quantity-input {
    :deep(.el-input__inner) {
      font-weight: bold;
      font-size: 16px;
      text-align: center;
      background-color: #f8f9fa;
      border: 2px solid #e9ecef;
    }

    :deep(.el-input__inner:focus) {
      background-color: #fff;
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }

    :deep(.el-input-number__decrease),
    :deep(.el-input-number__increase) {
      background-color: #f8f9fa;
      border-color: #e9ecef;
      font-weight: bold;
    }

    :deep(.el-input-number__decrease:hover),
    :deep(.el-input-number__increase:hover) {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: white;
    }
  }
}

.light-blue-btn {
  background-color: #e1f5fe !important;
  border-color: #81d4fa !important;
  color: #0277bd !important;
}

.light-blue-btn:hover {
  background-color: #b3e5fc !important;
  border-color: #4fc3f7 !important;
  color: #01579b !important;
}
</style>
