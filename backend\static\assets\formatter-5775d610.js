function t(t,r="¥",n=2){if(null==t||""===t)return`${r}0.00`;const e="string"==typeof t?parseFloat(t):t;if(isNaN(e))return`${r}0.00`;const i=e<0?"-":"",a=Math.abs(e).toFixed(n).split(".");return a[0]=a[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),`${i}${r}${a.join(".")}`}function r(t,r=2){const n=Number(t);if(null==t||isNaN(n))return"0.00";const e=n.toFixed(r).split(".");return e[0]=e[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),e.join(".")}const n=t=>{if(!t)return"";try{const r=new Date(t);if(isNaN(r.getTime()))return t||"";const n=r.getFullYear(),e=(r.getMonth()+1).toString().padStart(2,"0");return`${n}-${e}-${r.getDate().toString().padStart(2,"0")}`}catch(r){return console.error("Error formatting date:",t,r),t||""}},e=(t,r=2)=>{if(null==t||""===t)return"";const n="string"==typeof t?parseFloat(t):t;if(isNaN(n))return"";return n.toLocaleString("zh-CN",{minimumFractionDigits:r,maximumFractionDigits:r})};export{n as a,e as b,r as c,t as f};
