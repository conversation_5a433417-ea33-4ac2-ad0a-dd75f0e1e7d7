"""
发货单管理API模块
提供发货单的完整CRUD操作、状态管理、物流跟踪和PDF导出功能
确保与订单模块的协同工作
"""
from flask import request, current_app, send_file
from flask_restx import Resource, Namespace, fields
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, or_, desc
from marshmallow import ValidationError
from datetime import datetime, timedelta
import uuid
import os
from io import BytesIO

from app.models.order import Order, OrderProduct, DeliveryNote, DeliveryNoteItem
from app.models.customer import Customer, CustomerDeliveryAddress
from app.models.product import Product, ProductSpecification
from app.schemas.order import (
    DeliveryNoteSchema, DeliveryNoteSimpleSchema, DeliveryNoteItemSchema
)
from app.utils.response import success_response, error_response
from app.utils.pagination import PaginationHelper
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('delivery-notes', description='发货单管理API')

# 自动从Marshmallow Schema生成API模型
delivery_note_model = create_input_model(api, DeliveryNoteSchema, 'DeliveryNoteInput')
delivery_note_item_model = create_input_model(api, DeliveryNoteItemSchema, 'DeliveryNoteItemInput')

# 状态更新模型（手动定义，因为没有对应的Schema）
delivery_note_status_model = api.model('DeliveryNoteStatus', {
    'status': fields.String(required=True, description='新状态'),
    'logistics_company': fields.String(description='物流公司'),
    'tracking_number': fields.String(description='运单号'),
    'notes': fields.String(description='备注')
})

# 发货单状态定义
DELIVERY_NOTE_STATUSES = ['待发出', '已发出', '运输中', '已签收', '已作废']

# 状态流转规则
STATUS_TRANSITIONS = {
    '待发出': ['已发出', '已作废'],
    '已发出': ['运输中', '已签收', '已作废'],
    '运输中': ['已签收', '已作废'],
    '已签收': [],
    '已作废': []
}


@api.route('')
class DeliveryNoteList(Resource):
    @api.doc('get_delivery_notes')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('order_id', '订单ID', type='integer')
    @api.param('delivery_number', '发货单编号搜索')
    @api.param('status', '发货状态')
    @api.param('logistics_company', '物流公司')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    @api.param('customer_name', '客户名称搜索')
    def get(self):
        """获取发货单列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 20, type=int), 100)
            order_id = request.args.get('order_id', type=int)
            delivery_number = request.args.get('delivery_number', '').strip()
            status = request.args.get('status', '').strip()
            logistics_company = request.args.get('logistics_company', '').strip()
            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()
            customer_name = request.args.get('customer_name', '').strip()

            # 构建查询
            query = DeliveryNote.query.options(
                joinedload(DeliveryNote.order).joinedload(Order.customer),
                joinedload(DeliveryNote.items).joinedload(DeliveryNoteItem.order_product)
            )

            # 应用过滤条件
            if order_id:
                query = query.filter(DeliveryNote.order_id == order_id)
            
            if delivery_number:
                query = query.filter(DeliveryNote.delivery_number.ilike(f'%{delivery_number}%'))
            
            if status:
                query = query.filter(DeliveryNote.status == status)
            
            if logistics_company:
                query = query.filter(DeliveryNote.logistics_company.ilike(f'%{logistics_company}%'))
            
            if customer_name:
                query = query.join(Order).join(Customer).filter(
                    Customer.name.ilike(f'%{customer_name}%')
                )
            
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(DeliveryNote.delivery_date >= start)
                except ValueError:
                    return error_response("开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            if end_date:
                try:
                    end = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(DeliveryNote.delivery_date <= end)
                except ValueError:
                    return error_response("结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            # 排序
            query = query.order_by(desc(DeliveryNote.created_at))

            # 分页
            pagination = query.paginate(
                page=page, 
                per_page=per_page, 
                error_out=False
            )

            # 序列化数据
            delivery_notes = []
            for delivery_note in pagination.items:
                data = DeliveryNoteSimpleSchema().dump(delivery_note)
                # 添加客户信息
                if delivery_note.order and delivery_note.order.customer:
                    data['customer_name'] = delivery_note.order.customer.name
                delivery_notes.append(data)

            return success_response({
                'items': delivery_notes,
                'pagination': {
                    'page': pagination.page,
                    'per_page': pagination.per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                }
            }, '获取发货单列表成功')

        except Exception as e:
            current_app.logger.error(f"获取发货单列表失败: {str(e)}")
            return error_response(f"获取发货单列表失败: {str(e)}", code=500)

    @api.doc('create_delivery_note',
             body=delivery_note_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 404: 'Order Not Found',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建发货单"""
        try:
            data = request.get_json() or {}
            items_data = data.pop('items', [])

            # 数据验证
            delivery_note_schema = DeliveryNoteSchema(exclude=(
                'id', 'delivery_number', 'order', 'items', 'created_at', 'updated_at'
            ))
            validated_data = delivery_note_schema.load(data)

            # 检查订单是否存在
            order = Order.query.get(validated_data['order_id'])
            if not order:
                return error_response("订单不存在", code=404)

            # 检查订单状态（使用order_status字段）
            # 允许已确认、生产中、待发货、部分发货状态的订单创建发货单
            if order.order_status not in ['已确认', '生产中', '待发货', '部分发货']:
                return error_response(f"订单状态为'{order.order_status}'时不允许创建发货单，只有已确认、生产中、待发货、部分发货状态的订单可以创建发货单", code=400)

            # 生成发货单编号
            delivery_number = f"DN{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"
            validated_data['delivery_number'] = delivery_number

            # 创建发货单对象
            delivery_note = DeliveryNote(**validated_data)
            db.session.add(delivery_note)
            db.session.flush()  # 获取ID

            # 创建发货单项目
            if items_data:
                for item_data in items_data:
                    # 验证订单产品
                    order_product = OrderProduct.query.filter_by(
                        id=item_data.get('order_product_id'),
                        order_id=order.id
                    ).first()
                    
                    if not order_product:
                        db.session.rollback()
                        return error_response(f"订单产品ID {item_data.get('order_product_id')} 不存在或不属于该订单", code=400)

                    # 检查发货数量
                    remaining_quantity = order_product.quantity - (order_product.delivered_quantity or 0)
                    if item_data['quantity'] > remaining_quantity:
                        db.session.rollback()
                        return error_response(f"发货数量超过可发货数量，可发货数量为 {remaining_quantity}", code=400)

                    # 获取产品信息
                    product_spec = order_product.product_specification
                    product = product_spec.product if product_spec else None

                    # 创建发货单项目
                    delivery_item = DeliveryNoteItem(
                        delivery_note_id=delivery_note.id,
                        order_product_id=item_data['order_product_id'],
                        product_specification_id=order_product.product_specification_id,
                        quantity=item_data['quantity'],
                        notes=item_data.get('notes', '')
                    )

                    # 填充快照字段
                    if product:
                        delivery_item.product_name = product.name
                        delivery_item.product_model = product.model
                        delivery_item.product_unit = product.unit
                    if product_spec:
                        delivery_item.specification_description = product_spec.specification

                    db.session.add(delivery_item)

                    # 更新订单产品的已发货数量
                    order_product.delivered_quantity = (order_product.delivered_quantity or 0) + item_data['quantity']

            # 计算并更新发货单总金额
            delivery_note.update_total_amount()

            db.session.commit()

            # 返回创建的发货单信息
            created_delivery_note = DeliveryNote.query.get(delivery_note.id)
            response_data = DeliveryNoteSchema().dump(created_delivery_note)
            return success_response(response_data, "发货单创建成功", code=201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            db.session.rollback()
            return error_response("数据验证失败", errors=e.messages, code=400)
        except Exception as e:
            current_app.logger.error(f"创建发货单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"创建发货单失败: {str(e)}", code=500)


@api.route('/<int:delivery_note_id>')
class DeliveryNoteDetail(Resource):
    @api.doc('get_delivery_note')
    def get(self, delivery_note_id):
        """获取发货单详情"""
        try:
            delivery_note = DeliveryNote.query.options(
                joinedload(DeliveryNote.order).joinedload(Order.customer),
                joinedload(DeliveryNote.items).joinedload(DeliveryNoteItem.product_specification).joinedload('product'),
                joinedload(DeliveryNote.items).joinedload(DeliveryNoteItem.order_product)
            ).get(delivery_note_id)

            if not delivery_note:
                return error_response("发货单不存在", code=404)

            delivery_note_data = DeliveryNoteSchema().dump(delivery_note)
            return success_response(delivery_note_data, "获取发货单详情成功")

        except Exception as e:
            current_app.logger.error(f"获取发货单详情失败: {str(e)}")
            return error_response(f"获取发货单详情失败: {str(e)}", code=500)

    @api.doc('update_delivery_note',
             body=delivery_note_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Delivery Note Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, delivery_note_id):
        """更新发货单"""
        try:
            delivery_note = DeliveryNote.query.get(delivery_note_id)
            if not delivery_note:
                return error_response("发货单不存在", code=404)

            # 检查是否可以编辑
            if delivery_note.status not in ['待发出']:
                return error_response("当前状态的发货单不可编辑", code=400)

            data = request.get_json() or {}
            items_data = data.pop('items', None)

            # 验证主表数据
            delivery_note_schema = DeliveryNoteSchema(exclude=(
                'id', 'delivery_number', 'order', 'items', 'created_at', 'updated_at'
            ))
            validated_data = delivery_note_schema.load(data, partial=True)

            # 更新主表字段
            for key, value in validated_data.items():
                setattr(delivery_note, key, value)

            # 处理项目数据
            if items_data is not None:
                # 恢复订单产品的已发货数量
                for item in delivery_note.items:
                    if item.order_product:
                        item.order_product.delivered_quantity = (item.order_product.delivered_quantity or 0) - item.quantity

                # 删除现有项目
                for item in delivery_note.items:
                    db.session.delete(item)

                # 添加新项目
                for item_data in items_data:
                    order_product = OrderProduct.query.filter_by(
                        id=item_data.get('order_product_id'),
                        order_id=delivery_note.order_id
                    ).first()

                    if not order_product:
                        db.session.rollback()
                        return error_response(f"订单产品ID {item_data.get('order_product_id')} 无效", code=400)

                    # 检查发货数量
                    remaining_quantity = order_product.quantity - (order_product.delivered_quantity or 0)
                    if item_data['quantity'] > remaining_quantity:
                        db.session.rollback()
                        return error_response(f"发货数量超过可发货数量，可发货数量为 {remaining_quantity}", code=400)

                    # 获取产品信息
                    product_spec = order_product.product_specification
                    product = product_spec.product if product_spec else None

                    # 创建发货单项目
                    delivery_item = DeliveryNoteItem(
                        delivery_note_id=delivery_note.id,
                        order_product_id=item_data['order_product_id'],
                        product_specification_id=order_product.product_specification_id,
                        quantity=item_data['quantity'],
                        notes=item_data.get('notes', '')
                    )

                    # 填充快照字段
                    if product:
                        delivery_item.product_name = product.name
                        delivery_item.product_model = product.model
                        delivery_item.product_unit = product.unit
                    if product_spec:
                        delivery_item.specification_description = product_spec.specification

                    db.session.add(delivery_item)

                    # 更新订单产品的已发货数量
                    order_product.delivered_quantity = (order_product.delivered_quantity or 0) + item_data['quantity']

            # 计算并更新发货单总金额
            delivery_note.update_total_amount()

            db.session.commit()

            # 返回更新后的数据
            updated_data = DeliveryNoteSchema().dump(delivery_note)
            return success_response(updated_data, "发货单更新成功")

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            db.session.rollback()
            return error_response("数据验证失败", errors=e.messages, code=400)
        except Exception as e:
            current_app.logger.error(f"更新发货单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新发货单失败: {str(e)}", code=500)

    @api.doc('delete_delivery_note')
    def delete(self, delivery_note_id):
        """删除发货单"""
        try:
            delivery_note = DeliveryNote.query.get(delivery_note_id)
            if not delivery_note:
                return error_response("发货单不存在", code=404)

            # 检查是否可以删除
            if delivery_note.status not in ['待发出']:
                return error_response("只有待发出状态的发货单可以删除", code=400)

            # 恢复订单产品的已发货数量
            for item in delivery_note.items:
                if item.order_product:
                    item.order_product.delivered_quantity = (item.order_product.delivered_quantity or 0) - item.quantity

            db.session.delete(delivery_note)
            db.session.commit()
            return success_response(message="发货单删除成功")

        except Exception as e:
            current_app.logger.error(f"删除发货单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"删除发货单失败: {str(e)}", code=500)


@api.route('/<int:delivery_note_id>/status')
class DeliveryNoteStatus(Resource):
    @api.doc('update_delivery_note_status',
             body=delivery_note_status_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Delivery Note Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, delivery_note_id):
        """更新发货单状态"""
        try:
            delivery_note = DeliveryNote.query.get(delivery_note_id)
            if not delivery_note:
                return error_response("发货单不存在", code=404)

            data = request.get_json() or {}
            new_status = data.get('status', '').strip()

            if not new_status:
                return error_response("状态不能为空", code=400)

            if new_status not in DELIVERY_NOTE_STATUSES:
                return error_response(f"无效的状态值，允许的状态: {', '.join(DELIVERY_NOTE_STATUSES)}", code=400)

            # 检查状态流转是否合法
            current_status = delivery_note.status
            if new_status not in STATUS_TRANSITIONS.get(current_status, []):
                return error_response(f"不能从 '{current_status}' 状态转换到 '{new_status}' 状态", code=400)

            # 更新状态（使用模型方法，会自动处理应收账款）
            delivery_note.update_status(new_status)

            # 更新物流信息
            if 'logistics_company' in data:
                delivery_note.logistics_company = data['logistics_company']
            if 'tracking_number' in data:
                delivery_note.tracking_number = data['tracking_number']
            if 'notes' in data:
                delivery_note.notes = data['notes']

            # 根据状态更新订单状态
            if new_status == '已发出':
                # 检查订单是否所有产品都已发货
                order = delivery_note.order
                all_delivered = True
                for order_product in order.products:
                    if (order_product.delivered_quantity or 0) < order_product.quantity:
                        all_delivered = False
                        break

                if all_delivered:
                    order.update_order_status_by_delivery('全部发货', '所有产品已发货完成')
                elif order.order_status in ['待发货', '已确认', '生产中']:
                    order.update_order_status_by_delivery('部分发货', '部分产品已发货')

            db.session.commit()

            # 返回更新后的数据
            updated_data = DeliveryNoteSchema().dump(delivery_note)
            return success_response(updated_data, f"发货单状态已更新为 '{new_status}'")

        except Exception as e:
            current_app.logger.error(f"更新发货单状态失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新发货单状态失败: {str(e)}", code=500)


@api.route('/<int:delivery_note_id>/tracking')
class DeliveryNoteTracking(Resource):
    @api.doc('get_delivery_note_tracking')
    def get(self, delivery_note_id):
        """获取发货单物流跟踪信息"""
        try:
            delivery_note = DeliveryNote.query.get(delivery_note_id)
            if not delivery_note:
                return error_response("发货单不存在", code=404)

            tracking_info = {
                'delivery_number': delivery_note.delivery_number,
                'status': delivery_note.status,
                'logistics_company': delivery_note.logistics_company,
                'tracking_number': delivery_note.tracking_number,
                'delivery_date': delivery_note.delivery_date.isoformat() if delivery_note.delivery_date else None,
                'recipient_name': delivery_note.recipient_name,
                'recipient_phone': delivery_note.recipient_phone,
                'delivery_address': delivery_note.delivery_address_snapshot,
                'notes': delivery_note.notes,
                'created_at': delivery_note.created_at.isoformat() if delivery_note.created_at else None,
                'updated_at': delivery_note.updated_at.isoformat() if delivery_note.updated_at else None
            }

            # 如果有物流公司和运单号，可以在这里集成第三方物流API获取实时跟踪信息
            # 示例：
            # if delivery_note.logistics_company and delivery_note.tracking_number:
            #     tracking_info['logistics_details'] = get_logistics_tracking(
            #         delivery_note.logistics_company,
            #         delivery_note.tracking_number
            #     )

            return success_response(tracking_info, "获取物流跟踪信息成功")

        except Exception as e:
            current_app.logger.error(f"获取物流跟踪信息失败: {str(e)}")
            return error_response(f"获取物流跟踪信息失败: {str(e)}", code=500)


@api.route('/statuses')
class DeliveryNoteStatuses(Resource):
    @api.doc('get_delivery_note_statuses')
    def get(self):
        """获取发货单状态列表和流转规则"""
        try:
            status_info = {
                'statuses': DELIVERY_NOTE_STATUSES,
                'transitions': STATUS_TRANSITIONS,
                'descriptions': {
                    '待发出': '发货单已创建，等待发出',
                    '已发出': '货物已发出，等待物流公司接收',
                    '运输中': '货物正在运输途中',
                    '已签收': '货物已被收货人签收',
                    '已作废': '发货单已作废'
                }
            }
            return success_response(status_info, "获取状态信息成功")

        except Exception as e:
            current_app.logger.error(f"获取状态信息失败: {str(e)}")
            return error_response(f"获取状态信息失败: {str(e)}", code=500)


@api.route('/<int:delivery_note_id>/pdf')
class DeliveryNotePDF(Resource):
    @api.doc('export_delivery_note_pdf')
    def get(self, delivery_note_id):
        """导出发货单PDF"""
        try:
            delivery_note = DeliveryNote.query.options(
                joinedload(DeliveryNote.order).joinedload(Order.customer),
                joinedload(DeliveryNote.items)
            ).get(delivery_note_id)

            if not delivery_note:
                return error_response("发货单不存在", code=404)

            # 这里应该使用PDF生成库（如reportlab）生成PDF
            # 为了演示，我们返回一个简单的文本响应
            pdf_content = self._generate_delivery_note_pdf(delivery_note)

            # 创建内存中的文件对象
            pdf_buffer = BytesIO()
            pdf_buffer.write(pdf_content.encode('utf-8'))
            pdf_buffer.seek(0)

            filename = f"delivery_note_{delivery_note.delivery_number}.txt"

            return send_file(
                pdf_buffer,
                as_attachment=True,
                download_name=filename,
                mimetype='text/plain'
            )

        except Exception as e:
            current_app.logger.error(f"导出发货单PDF失败: {str(e)}")
            return error_response(f"导出发货单PDF失败: {str(e)}", code=500)

    def _generate_delivery_note_pdf(self, delivery_note):
        """生成发货单PDF内容（示例实现）"""
        content = f"""
发货单

发货单号: {delivery_note.delivery_number}
订单号: {delivery_note.order.order_number if delivery_note.order else ''}
客户名称: {delivery_note.order.customer.name if delivery_note.order and delivery_note.order.customer else ''}
项目名称: {delivery_note.order.project_name if delivery_note.order else ''}

发货信息:
发货日期: {delivery_note.delivery_date.strftime('%Y-%m-%d %H:%M:%S') if delivery_note.delivery_date else ''}
收货人: {delivery_note.recipient_name}
收货电话: {delivery_note.recipient_phone or ''}
收货地址: {delivery_note.delivery_address_snapshot}

物流信息:
物流公司: {delivery_note.logistics_company or ''}
运单号: {delivery_note.tracking_number or ''}
状态: {delivery_note.status}

发货明细:
"""
        for item in delivery_note.items:
            content += f"- {item.product_name} ({item.product_model}) x {item.quantity} {item.product_unit}\n"
            if item.specification_description:
                content += f"  规格: {item.specification_description}\n"
            if item.notes:
                content += f"  备注: {item.notes}\n"

        if delivery_note.notes:
            content += f"\n备注: {delivery_note.notes}"

        return content


@api.route('/from-order/<int:order_id>')
class DeliveryNoteFromOrder(Resource):
    @api.doc('create_delivery_note_from_order')
    def post(self, order_id):
        """基于订单创建发货单"""
        try:
            order = Order.query.options(
                joinedload(Order.customer),
                joinedload(Order.products),
                joinedload(Order.delivery_address)
            ).get(order_id)

            if not order:
                return error_response("订单不存在", code=404)

            # 检查订单状态（使用order_status字段）
            # 允许已确认、生产中、待发货、部分发货状态的订单创建发货单
            if order.order_status not in ['已确认', '生产中', '待发货', '部分发货']:
                return error_response(f"订单状态为'{order.order_status}'时不允许创建发货单，只有已确认、生产中、待发货、部分发货状态的订单可以创建发货单", code=400)

            # 获取可发货的产品
            available_products = []
            for order_product in order.products:
                remaining_quantity = order_product.quantity - (order_product.delivered_quantity or 0)
                if remaining_quantity > 0:
                    # 获取产品信息
                    product_spec = order_product.product_specification
                    product = product_spec.product if product_spec else None

                    available_products.append({
                        'order_product_id': order_product.id,
                        'product_name': product.name if product else '未知产品',
                        'product_model': product.model if product else '',
                        'product_unit': product.unit if product else '',
                        'specification_description': product_spec.specification if product_spec else '',
                        'ordered_quantity': order_product.quantity,
                        'delivered_quantity': order_product.delivered_quantity or 0,
                        'remaining_quantity': remaining_quantity
                    })

            if not available_products:
                return error_response("该订单没有可发货的产品", code=400)

            # 准备发货单数据模板
            delivery_address = order.delivery_address
            delivery_note_template = {
                'order_id': order.id,
                'delivery_date': datetime.now().isoformat(),
                'recipient_name': delivery_address.contact_person if delivery_address else order.contact_person or '',
                'recipient_phone': delivery_address.contact_phone if delivery_address else order.contact_phone or '',
                'delivery_address_snapshot': self._format_delivery_address(delivery_address) if delivery_address else '',
                'status': '待发出',
                'available_products': available_products
            }

            return success_response(delivery_note_template, "获取发货单模板成功")

        except Exception as e:
            current_app.logger.error(f"基于订单创建发货单模板失败: {str(e)}")
            return error_response(f"基于订单创建发货单模板失败: {str(e)}", code=500)

    def _format_delivery_address(self, delivery_address):
        """格式化送货地址"""
        if not delivery_address:
            return ''

        address_parts = []
        if delivery_address.province:
            address_parts.append(delivery_address.province)
        if delivery_address.city:
            address_parts.append(delivery_address.city)
        if delivery_address.district:
            address_parts.append(delivery_address.district)
        if delivery_address.detailed_address:
            address_parts.append(delivery_address.detailed_address)

        return ''.join(address_parts)


@api.route('/<int:delivery_note_id>/export')
class DeliveryNoteExport(Resource):
    @api.doc('export_delivery_note')
    def get(self, delivery_note_id):
        """导出发货单"""
        from .delivery_export import DeliveryNoteExport as ExportResource
        export_resource = ExportResource()
        return export_resource.get(delivery_note_id)
