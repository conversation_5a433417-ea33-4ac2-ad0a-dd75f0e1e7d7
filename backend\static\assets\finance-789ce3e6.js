import{I as r}from"./index-3d4c440c.js";function t(t){return r({url:"/api/v1/payment-records",method:"get",params:t})}function e(t){return r({url:"/api/v1/payment-records",method:"post",data:t})}function a(t,e){return r({url:`/api/v1/payment-records/${t}/status`,method:"put",data:{status:e}})}function n(t){return r({url:`/api/v1/payment-records/${t}`,method:"get"})}function s(t){return r({url:"/api/v1/refund-records",method:"get",params:t})}function u(t){return r({url:"/api/v1/refund-records",method:"post",data:t})}function o(t,e){return r({url:`/api/v1/refund-records/${t}/status`,method:"put",data:{status:e}})}function i(t){return r({url:`/api/v1/refund-records/${t}`,method:"get"})}function d(t){return r({url:"/api/v1/refund-records/available-return-orders",method:"get",params:t})}function c(t){return r({url:"/api/v1/receivables/list",method:"get",params:t})}function p(t){return r({url:"/api/v1/receivables/statistics",method:"get",params:t?{customer_id:t}:{}})}function m(t){return r({url:"/api/v1/receivables/aging-analysis",method:"get",params:t?{customer_id:t}:{}})}function f(){return r({url:"/api/v1/finance/payment-statistics",method:"get"})}function l(t){return r({url:"/api/v1/payment-records",method:"get",params:{order_id:t}})}export{f as a,n as b,l as c,e as d,s as e,o as f,t as g,i as h,d as i,u as j,p as k,m as l,c as m,a as u};
