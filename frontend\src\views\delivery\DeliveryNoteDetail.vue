<template>
  <div class="delivery-detail">
    <!-- 头部区域 -->
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="form-title">发货单详情</h2>
        <div class="header-actions">
          <el-button @click="goBack">返回</el-button>
          <el-button
            v-if="deliveryNote?.status === '待发出'"
            type="primary"
            @click="handleEdit"
          >
            编辑
          </el-button>
          <el-button
            v-if="deliveryNote?.status === '待发出'"
            type="success"
            @click="handleConfirm"
          >
            确认发货
          </el-button>
          <el-button
            v-if="deliveryNote?.status === '待发出'"
            type="danger"
            @click="handleCancel"
          >
            取消
          </el-button>
          <el-button type="primary" @click="handlePrint">打印</el-button>
          <el-button type="info" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 发货单状态管理 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>发货单状态管理</span>
          <span class="status-tip">点击状态可直接切换</span>
        </div>
      </template>

      <div class="status-section">
        <div class="status-selector">
          <div class="status-grid">
            <div
              v-for="status in availableStatuses"
              :key="status.value"
              :class="[
                'status-item',
                { 'active': deliveryNote?.status === status.value },
                { 'clickable': deliveryNote?.status !== status.value && canTransitionTo(status.value) }
              ]"
              @click="handleStatusChange(status.value)"
            >
              <div class="status-icon">
                <el-icon v-if="deliveryNote?.status === status.value">
                  <Check />
                </el-icon>
                <span v-else class="status-number">{{ status.step }}</span>
              </div>
              <div class="status-text">{{ status.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 发货单信息 -->
    <el-card class="mb-20">
      <el-descriptions
        v-loading="loading"
        :column="3"
        border
      >
        <el-descriptions-item label="发货单号">
          {{ deliveryNote?.delivery_number || 'N/A' }}
        </el-descriptions-item>
        <el-descriptions-item label="客户名称">
          {{ deliveryNote?.customer?.name || deliveryNote?.customer_name || '未知客户' }}
        </el-descriptions-item>
        <el-descriptions-item label="发货日期">
          {{ formatDate(deliveryNote?.delivery_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="发货状态">
          <el-tag :type="getStatusType(deliveryNote?.status)">
            {{ getStatusText(deliveryNote?.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="结清状态">
          <el-tag :type="getSettlementStatusType(deliveryNote?.settlement_status)">
            {{ deliveryNote?.settlement_status || '未结清' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="结清日期" v-if="deliveryNote?.settlement_status === '已结清'">
          {{ formatDate(deliveryNote?.settlement_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="物流公司" v-if="deliveryNote?.settlement_status !== '已结清'">
          {{ deliveryNote?.logistics_company || '暂无' }}
        </el-descriptions-item>
        <el-descriptions-item label="物流单号">
          {{ deliveryNote?.tracking_number || '暂无' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="3">
          {{ deliveryNote?.notes || '暂无' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 产品明细 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>产品明细</span>
        </div>
      </template>

      <el-table
        :data="deliveryNote?.items || deliveryNote?.products || []"
        border
        style="width: 100%"
      >
        <el-table-column prop="product_name" label="产品名称" min-width="180" />
        <el-table-column prop="product_model" label="型号" width="120" />
        <el-table-column label="规格" width="120">
          <template #default="{ row }">
            {{ row.specification_description || row.product_specification?.specification || '' }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.quantity ?? 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="unit_price" label="单价" width="120">
          <template #default="{ row }">
            {{ formatCurrency(row.unit_price ?? 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="120">
          <template #default="{ row }">
            {{ formatCurrency(row.amount ?? 0) }}
          </template>
        </el-table-column>
      </el-table>

      <div class="total-amount">
        <span>总金额：</span>
        <span class="amount">{{ formatCurrency(deliveryNote?.total_amount ?? 0) }}</span>
      </div>
    </el-card>

    <!-- 导出对话框 -->
    <DeliveryNoteExportDialog
      v-model="exportDialogVisible"
      :delivery-note-id="deliveryNote?.id"
      :delivery-note-name="deliveryNote?.delivery_number"
      @export-success="handleExportSuccess"
    />
  </div>


</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Download } from '@element-plus/icons-vue'
import { deliveryNoteApi } from '@/api/delivery'
import DeliveryNoteExportDialog from '@/components/DeliveryNoteExportDialog.vue'

const route = useRoute()
const router = useRouter()

// 数据
const loading = ref(false)
const deliveryNote = ref<any>({})

// 导出相关状态
const exportDialogVisible = ref(false)

// 发货单状态定义
const availableStatuses = ref([
  { value: '待发出', label: '待发出', step: 1, type: 'warning' },
  { value: '已发出', label: '已发出', step: 2, type: 'primary' },
  { value: '运输中', label: '运输中', step: 3, type: 'info' },
  { value: '已签收', label: '已签收', step: 4, type: 'success' },
  { value: '已作废', label: '已作废', step: 5, type: 'danger' }
])

// 状态流转规则
const statusTransitions = {
  '待发出': ['已发出', '已作废'],
  '已发出': ['运输中', '已签收', '已作废'],
  '运输中': ['已签收', '已作废'],
  '已签收': [],
  '已作废': []
}

// 获取发货单详情
const fetchDeliveryNote = async () => {
  loading.value = true
  try {
    const id = route.params.id as string
    const response = await deliveryNoteApi.getById(Number(id))

    if (response && typeof response === 'object') {
      // API直接返回发货单对象，不需要额外处理
      deliveryNote.value = response
    } else {
      deliveryNote.value = {}
    }
  } catch (error) {
    console.error('获取发货单详情失败:', error)
    ElMessage.error('获取发货单详情失败')
    deliveryNote.value = {}
  } finally {
    loading.value = false
  }
}

// 状态处理
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待发出': 'warning',
    '已发出': 'primary',
    '运输中': 'primary',
    '已签收': 'success',
    '已作废': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  // 直接返回中文状态，不需要映射
  return status || '未知状态'
}

// 结清状态样式
const getSettlementStatusType = (settlementStatus: string) => {
  const statusMap: Record<string, string> = {
    '未结清': 'warning',
    '已结清': 'success'
  }
  return statusMap[settlementStatus] || 'warning'
}

// 操作处理
const goBack = () => {
  router.back()
}

const handleEdit = () => {
  router.push(`/delivery-notes/edit/${deliveryNote.value.id}`)
}

const handleConfirm = async () => {
  try {
    await ElMessageBox.confirm('确认要标记该发货单为已发货吗？这将更新订单状态和产品发货数量', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    loading.value = true
    await deliveryNoteApi.updateStatus(deliveryNote.value.id, '已发出')
    ElMessage.success('发货单已确认发货')

    // 刷新数据
    await fetchDeliveryNote()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('确认发货失败:', error)
      ElMessage.error('确认发货失败')
    }
  } finally {
    loading.value = false
  }
}

const handleCancel = async () => {
  try {
    await ElMessageBox.confirm('确认要取消该发货单吗？', '提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    loading.value = true
    await deliveryNoteApi.updateStatus(deliveryNote.value.id, '已作废')
    ElMessage.success('发货单已取消')

    // 刷新数据
    await fetchDeliveryNote()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('取消发货单失败:', error)
      ElMessage.error('取消发货单失败')
    }
  } finally {
    loading.value = false
  }
}

const handlePrint = async () => {
  try {
    const blob = await deliveryNoteApi.print(deliveryNote.value.id)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `发货单_${deliveryNote.value.delivery_number}.pdf`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('打印文件已生成')
  } catch (error) {
    console.error('打印发货单失败', error)
    ElMessage.error('打印发货单失败')
  }
}

// 格式化函数
const formatDate = (dateString: string | Date) => {
  if (!dateString) return '未设置'
  try {
    const date = new Date(dateString)
    return date.getFullYear() + '/' +
           String(date.getMonth() + 1).padStart(2, '0') + '/' +
           String(date.getDate()).padStart(2, '0')
  } catch (error) {
    console.error('日期格式化错误:', error)
    return String(dateString)
  }
}

const formatNumber = (num: number) => {
  return num?.toFixed(0) || '0'
}

const formatCurrency = (amount: number | string) => {
  if (typeof amount === 'string') {
    return amount ? `¥${amount}` : '¥0.00'
  }
  return amount ? `¥${amount.toFixed(2)}` : '¥0.00'
}

// 初始化
// 检查是否可以转换到指定状态
const canTransitionTo = (targetStatus: string) => {
  const currentStatus = deliveryNote.value?.status
  if (!currentStatus) return false

  const allowedTransitions = statusTransitions[currentStatus] || []
  return allowedTransitions.includes(targetStatus)
}

// 处理状态变更
const handleStatusChange = async (newStatus: string) => {
  if (newStatus === deliveryNote.value?.status) {
    return // 相同状态不需要更新
  }

  if (!canTransitionTo(newStatus)) {
    ElMessage.warning('当前状态不能直接转换到该状态')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要将发货单状态更改为"${newStatus}"吗？`,
      '确认状态变更',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true

    // 调用后端API更新状态
    await deliveryNoteApi.updateStatus(deliveryNote.value.id, newStatus)

    // 更新本地状态
    deliveryNote.value.status = newStatus

    ElMessage.success(`发货单状态已更新为"${newStatus}"`)
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('更新发货单状态失败:', error)
      ElMessage.error(error?.message || '更新发货单状态失败')
    }
  } finally {
    loading.value = false
  }
}

// 导出处理
const handleExport = () => {
  exportDialogVisible.value = true
}

// 导出成功处理
const handleExportSuccess = () => {
  // 可以在这里添加导出成功后的处理逻辑
}

onMounted(() => {
  fetchDeliveryNote()
})
</script>

<style scoped>
.delivery-detail {
  padding: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}

.status-section {
  padding: 10px 0;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  max-width: 800px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;
  min-height: 80px;
  justify-content: center;
}

.status-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
  color: #409eff;
}

.status-item.clickable {
  cursor: pointer;
}

.status-item.clickable:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}

.status-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #e4e7ed;
  color: #909399;
  font-size: 12px;
  font-weight: bold;
}

.status-item.active .status-number {
  background-color: #409eff;
  color: white;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.products-table {
  margin-top: 20px;
}

.products-table h3 {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}

.total-amount {
  margin-top: 20px;
  text-align: right;
  font-size: 16px;
}

.amount {
  font-weight: bold;
  color: #e6a23c;
  font-size: 18px;
}
</style>
