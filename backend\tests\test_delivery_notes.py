"""
发货单API单元测试
"""
import pytest
import json
from datetime import datetime, timedelta
from app.models.customer import Customer, CustomerDeliveryAddress
from app.models.product import Product, ProductCategory, ProductSpecification
from app.models.order import Order, OrderProduct, DeliveryNote, DeliveryNoteItem
from app.models.quotation import Quotation


class TestDeliveryNotesAPI:
    """发货单API测试类"""
    
    def test_get_delivery_notes_empty(self, client, db_session):
        """测试获取空的发货单列表"""
        response = client.get('/api/v1/delivery-notes')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取发货单列表成功'
        assert data['data']['items'] == []
        assert 'pagination' in data['data']
    
    def test_get_delivery_notes_with_data(self, client, db_session, sample_customer, sample_product):
        """测试获取有数据的发货单列表"""
        # 创建送货地址
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        # 创建订单
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='待发货',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        # 创建发货单
        delivery_note = DeliveryNote(
            delivery_number='DN20240101001',
            order_id=order.id,
            delivery_date=datetime.now(),
            recipient_name='测试收货人',
            recipient_phone='13800138000',
            delivery_address_snapshot='福建省泉州市丰泽区测试送货地址',
            status='待发出'
        )
        db_session.add(delivery_note)
        db_session.commit()
        
        response = client.get('/api/v1/delivery-notes')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']['items']) == 1
        assert data['data']['items'][0]['delivery_number'] == 'DN20240101001'
        assert data['data']['items'][0]['status'] == '待发出'
    
    def test_get_delivery_note_detail(self, client, db_session, sample_customer, sample_product):
        """测试获取发货单详情"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='待发货',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        delivery_note = DeliveryNote(
            delivery_number='DN20240101001',
            order_id=order.id,
            delivery_date=datetime.now(),
            recipient_name='测试收货人',
            recipient_phone='13800138000',
            delivery_address_snapshot='福建省泉州市丰泽区测试送货地址',
            status='待发出'
        )
        db_session.add(delivery_note)
        db_session.commit()
        
        response = client.get(f'/api/v1/delivery-notes/{delivery_note.id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['delivery_number'] == 'DN20240101001'
        assert data['data']['recipient_name'] == '测试收货人'
    
    def test_get_delivery_note_not_found(self, client, db_session):
        """测试获取不存在的发货单"""
        response = client.get('/api/v1/delivery-notes/999')
        assert response.status_code == 404
        
        data = response.get_json()
        assert data['code'] == 404
        assert '发货单不存在' in data['message']
    
    def test_create_delivery_note(self, client, db_session, sample_customer, sample_product):
        """测试创建发货单"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='待发货',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        # 创建产品规格或使用现有的
        spec = ProductSpecification.query.filter_by(product_id=sample_product.id).first()
        if not spec:
            spec = ProductSpecification(
                product_id=sample_product.id,
                specification='标准规格',
                cost_price=80.00,
                suggested_price=100.00,
                tax_rate=13.0
            )
            db_session.add(spec)
            db_session.flush()
        
        # 创建订单产品
        order_product = OrderProduct(
            order_id=order.id,
            product_specification_id=spec.id,
            quantity=10,
            unit_price=100.00,
            total_price=1000.00
        )
        db_session.add(order_product)
        db_session.commit()
        
        # 创建发货单数据
        delivery_note_data = {
            'order_id': order.id,
            'delivery_date': datetime.now().isoformat(),
            'recipient_name': '测试收货人',
            'recipient_phone': '13800138000',
            'delivery_address_snapshot': '福建省泉州市丰泽区测试送货地址',
            'status': '待发出',
            'items': [
                {
                    'order_product_id': order_product.id,
                    'product_specification_id': spec.id,
                    'quantity': 5,
                    'notes': '部分发货'
                }
            ]
        }
        
        response = client.post('/api/v1/delivery-notes', 
                             data=json.dumps(delivery_note_data),
                             content_type='application/json')
        assert response.status_code == 201
        
        data = response.get_json()
        assert data['code'] == 201
        assert data['message'] == '发货单创建成功'
        assert 'DN' in data['data']['delivery_number']
        assert len(data['data']['items']) == 1
    
    def test_create_delivery_note_invalid_order(self, client, db_session):
        """测试创建发货单时订单不存在"""
        delivery_note_data = {
            'order_id': 999,
            'delivery_date': datetime.now().isoformat(),
            'recipient_name': '测试收货人',
            'delivery_address_snapshot': '测试地址',
            'status': '待发出'
        }
        
        response = client.post('/api/v1/delivery-notes',
                             data=json.dumps(delivery_note_data),
                             content_type='application/json')
        assert response.status_code == 404
        
        data = response.get_json()
        assert '订单不存在' in data['message']
    
    def test_update_delivery_note_status(self, client, db_session, sample_customer, sample_product):
        """测试更新发货单状态"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='待发货',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        delivery_note = DeliveryNote(
            delivery_number='DN20240101001',
            order_id=order.id,
            delivery_date=datetime.now(),
            recipient_name='测试收货人',
            recipient_phone='13800138000',
            delivery_address_snapshot='福建省泉州市丰泽区测试送货地址',
            status='待发出'
        )
        db_session.add(delivery_note)
        db_session.commit()
        
        # 更新状态
        status_data = {
            'status': '已发出',
            'logistics_company': '顺丰快递',
            'tracking_number': 'SF1234567890',
            'notes': '已发出备注'
        }
        
        response = client.put(f'/api/v1/delivery-notes/{delivery_note.id}/status',
                            data=json.dumps(status_data),
                            content_type='application/json')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['status'] == '已发出'
        assert data['data']['logistics_company'] == '顺丰快递'
        assert data['data']['tracking_number'] == 'SF1234567890'
    
    def test_get_delivery_note_statuses(self, client, db_session):
        """测试获取发货单状态列表"""
        response = client.get('/api/v1/delivery-notes/statuses')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert 'statuses' in data['data']
        assert 'transitions' in data['data']
        assert 'descriptions' in data['data']
        assert '待发出' in data['data']['statuses']
        assert '已发出' in data['data']['statuses']
    
    def test_get_delivery_note_tracking(self, client, db_session, sample_customer, sample_product):
        """测试获取发货单物流跟踪信息"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='待发货',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        delivery_note = DeliveryNote(
            delivery_number='DN20240101001',
            order_id=order.id,
            delivery_date=datetime.now(),
            recipient_name='测试收货人',
            recipient_phone='13800138000',
            delivery_address_snapshot='福建省泉州市丰泽区测试送货地址',
            status='运输中',
            logistics_company='顺丰快递',
            tracking_number='SF1234567890'
        )
        db_session.add(delivery_note)
        db_session.commit()
        
        response = client.get(f'/api/v1/delivery-notes/{delivery_note.id}/tracking')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['delivery_number'] == 'DN20240101001'
        assert data['data']['status'] == '运输中'
        assert data['data']['logistics_company'] == '顺丰快递'
        assert data['data']['tracking_number'] == 'SF1234567890'
