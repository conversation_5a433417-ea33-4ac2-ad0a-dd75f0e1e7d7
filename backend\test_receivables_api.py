#!/usr/bin/env python3
"""
测试应收账款API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json

def test_receivables_api():
    """测试应收账款API"""
    base_url = "http://localhost:5001/api/v1/finance"
    
    print("🧪 测试应收账款API")
    print("=" * 60)
    
    try:
        # 1. 测试获取应收账款列表（按对账单）
        print("📋 测试获取应收账款列表（按对账单）...")
        response = requests.get(f"{base_url}/receivables", params={
            'view_type': 'statement',
            'page': 1,
            'per_page': 10
        })
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取数据")
            print(f"   数据结构: {list(data.keys()) if isinstance(data, dict) else 'List'}")
            if isinstance(data, dict) and 'data' in data:
                items = data['data'].get('items', [])
                print(f"   记录数量: {len(items)}")
                if items:
                    print(f"   第一条记录: {json.dumps(items[0], indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
        
        print()
        
        # 2. 测试获取应收账款列表（按客户）
        print("👥 测试获取应收账款列表（按客户）...")
        response = requests.get(f"{base_url}/receivables", params={
            'view_type': 'customer',
            'page': 1,
            'per_page': 10
        })
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取数据")
            print(f"   数据结构: {list(data.keys()) if isinstance(data, dict) else 'List'}")
            if isinstance(data, dict) and 'data' in data:
                items = data['data'].get('items', [])
                print(f"   记录数量: {len(items)}")
                if items:
                    print(f"   第一条记录: {json.dumps(items[0], indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
        
        print()
        
        # 3. 测试获取应收账款统计
        print("📊 测试获取应收账款统计...")
        response = requests.get(f"{base_url}/receivables/stats")
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取统计数据")
            print(f"   统计数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ 请求失败: {response.text}")
        
        print()
        print("🎉 API测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_receivables_api()
