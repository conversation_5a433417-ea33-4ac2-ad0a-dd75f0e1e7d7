"""
订单管理相关的序列化模式
基于原项目的Schema定义，确保与现有数据库结构100%兼容
"""
from marshmallow import Schema, fields, validate, validates, ValidationError
from typing import Dict, List, Optional, Any
from decimal import Decimal


class OrderProductSchema(Schema):
    """订单产品序列化模式"""
    id = fields.Integer(dump_only=True)
    order_id = fields.Integer(required=True, load_only=True)
    product_specification_id = fields.Integer(allow_none=True)  # 允许为空，用于手动添加的产品
    quantity = fields.Float(required=True, validate=validate.Range(min=0.000001))
    unit_price = fields.Float(required=True, validate=validate.Range(min=0))
    total_price = fields.Float(dump_only=True)
    delivered_quantity = fields.Float(dump_only=True, load_default=0.0)

    # 税率和折扣信息
    tax_rate = fields.Float(load_default=13.0, validate=validate.Range(min=0, max=100))
    discount = fields.Float(load_default=0.0, validate=validate.Range(min=0, max=100))

    # 发货状态计算字段
    remaining_quantity = fields.Method('calculate_remaining_quantity', dump_only=True)
    delivery_status = fields.Method('get_delivery_status', dump_only=True)
    delivery_progress = fields.Method('calculate_delivery_progress', dump_only=True)

    # 产品来源信息
    source_type = fields.String(validate=validate.OneOf(['quotation', 'manual']), load_default='manual')
    source_id = fields.Integer(allow_none=True)
    source_display = fields.String(allow_none=True)

    # 通过关联关系获取产品信息（用于输出）
    product_name = fields.Method("get_product_name", dump_only=True)
    product_model = fields.Method("get_product_model", dump_only=True)
    product_unit = fields.Method("get_product_unit", dump_only=True)
    specification_description = fields.Method("get_specification_description", dump_only=True)

    # 关联信息
    product = fields.Nested('ProductSimpleSchema', only=('id', 'name', 'model', 'unit'), dump_only=True)
    product_specification = fields.Nested('ProductSpecificationSchema', only=('id', 'specification'), dump_only=True)

    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    def get_product_name(self, obj):
        """获取产品名称"""
        if hasattr(obj, 'product_specification') and obj.product_specification and obj.product_specification.product:
            return obj.product_specification.product.name
        return ""

    def get_product_model(self, obj):
        """获取产品型号"""
        if hasattr(obj, 'product_specification') and obj.product_specification and obj.product_specification.product:
            return obj.product_specification.product.model
        return ""

    def get_product_unit(self, obj):
        """获取产品单位"""
        if hasattr(obj, 'product_specification') and obj.product_specification and obj.product_specification.product:
            return obj.product_specification.product.unit
        return ""

    def get_specification_description(self, obj):
        """获取规格描述"""
        if hasattr(obj, 'product_specification') and obj.product_specification:
            return obj.product_specification.specification
        return ""

    def calculate_remaining_quantity(self, obj):
        """计算剩余待发货数量"""
        return float(obj.quantity) - float(obj.delivered_quantity or 0)

    def get_delivery_status(self, obj):
        """获取发货状态"""
        remaining = self.calculate_remaining_quantity(obj)
        delivered = float(obj.delivered_quantity or 0)

        if delivered == 0:
            return "未发货"
        elif remaining <= 0:
            return "已完成"
        else:
            return "部分发货"

    def calculate_delivery_progress(self, obj):
        """计算发货进度百分比"""
        total = float(obj.quantity)
        delivered = float(obj.delivered_quantity or 0)

        if total <= 0:
            return 0.0

        progress = (delivered / total) * 100
        return round(progress, 2)


class OrderStatusHistorySchema(Schema):
    """订单状态历史序列化模式"""
    id = fields.Integer(dump_only=True)
    order_id = fields.Integer(required=True, load_only=True)
    status = fields.String(required=True)
    comment = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)


class OrderSchema(Schema):
    """
    订单序列化模式

    重构说明：
    - 移除了status字段，改用双状态系统
    - order_status: 发货状态，手动管理
    - payment_status: 财务状态，根据收款金额自动计算
    - 添加了计算字段用于前端显示和兼容性
    """
    id = fields.Integer(dump_only=True)
    order_number = fields.String(dump_only=True)
    customer_id = fields.Integer(required=True)
    quotation_ids = fields.String(allow_none=True)  # JSON字符串格式
    project_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    project_address = fields.String(validate=validate.Length(max=200), allow_none=True)
    delivery_address_id = fields.Integer(allow_none=True)
    expected_date = fields.DateTime(allow_none=True)
    payment_terms = fields.String(validate=validate.Length(max=100), allow_none=True)
    delivery_terms = fields.String(validate=validate.Length(max=100), allow_none=True)

    # 主要状态字段：双状态系统
    order_status = fields.String(
        validate=validate.OneOf([
            '待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消'
        ]),
        missing='待确认',
        metadata={'description': '发货状态'}
    )
    payment_status = fields.String(
        validate=validate.OneOf(['未收款', '部分收款', '已收款']),
        missing='未收款',
        metadata={'description': '财务状态（根据收款金额自动计算）'}
    )

    # 计算字段用于前端显示和兼容性
    combined_status = fields.Method("get_combined_status", dump_only=True,
                                   metadata={'description': '组合状态显示：发货状态 / 财务状态'})
    status_info = fields.Method("get_status_info", dump_only=True,
                               metadata={'description': '完整状态信息，包含可用操作'})
    can_cancel = fields.Method("get_can_cancel", dump_only=True,
                              metadata={'description': '是否可以取消订单'})
    can_edit = fields.Method("get_can_edit", dump_only=True,
                            metadata={'description': '是否可以编辑订单'})
    total_amount = fields.Decimal(dump_only=True, as_string=True)
    paid_amount = fields.Decimal(dump_only=True, as_string=True, load_default=Decimal('0.00'))
    notes = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    
    # 关联信息
    customer = fields.Nested('CustomerSimpleSchema', only=('id', 'name', 'contact', 'phone'), dump_only=True)
    quotations = fields.Method("get_quotations", dump_only=True)  # 获取关联的报价单列表
    products = fields.List(fields.Nested(OrderProductSchema), dump_only=True)
    status_history = fields.List(fields.Nested(OrderStatusHistorySchema), dump_only=True)
    delivery_notes = fields.Method("get_delivery_notes", dump_only=True)
    return_orders = fields.Method("get_return_orders", dump_only=True)

    def get_quotations(self, obj):
        """获取关联的报价单信息"""
        quotations = obj.get_quotations()
        from .quotation import QuotationSimpleSchema
        return QuotationSimpleSchema(many=True).dump(quotations)

    def get_delivery_notes(self, obj):
        """获取关联的发货单信息"""
        try:
            # delivery_notes是动态关系，需要调用.all()来获取数据
            delivery_notes_query = obj.delivery_notes.all() if hasattr(obj, 'delivery_notes') else []
            delivery_notes = []

            for dn in delivery_notes_query:
                # 计算发货单总金额
                total_amount = 0.0
                try:
                    if hasattr(dn, 'items') and dn.items:
                        for item in dn.items:
                            if hasattr(item, 'order_product') and item.order_product and item.order_product.unit_price:
                                # 计算考虑税率和折扣的实际单价
                                from decimal import Decimal
                                base_price = Decimal(str(item.order_product.unit_price))
                                tax_rate = Decimal(str(item.order_product.tax_rate or 0)) / Decimal('100')
                                discount = Decimal(str(item.order_product.discount or 0)) / Decimal('100')
                                actual_price = base_price * (Decimal('1') - discount) * (Decimal('1') + tax_rate)
                                item_amount = actual_price * Decimal(str(item.quantity))
                                total_amount += float(item_amount)
                except Exception:
                    total_amount = 0.0

                delivery_notes.append({
                    'id': dn.id,
                    'delivery_number': dn.delivery_number,
                    'delivery_date': dn.delivery_date.isoformat() if dn.delivery_date else None,
                    'status': dn.status,
                    'settlement_status': dn.settlement_status,
                    'settlement_date': dn.settlement_date.isoformat() if dn.settlement_date else None,
                    'total_amount': total_amount
                })
            return delivery_notes
        except Exception:
            return []

    def get_return_orders(self, obj):
        """获取关联的退货单信息"""
        try:
            # return_orders是backref关系，直接访问即可
            return_orders_query = list(obj.return_orders) if hasattr(obj, 'return_orders') else []
            return_orders = []

            for ro in return_orders_query:
                # 计算退货单总金额
                total_amount = 0.0
                try:
                    if hasattr(ro, 'items') and ro.items:
                        for item in ro.items:
                            if item.order_product:
                                # 计算含税金额：单价 × (1 - 折扣率) × (1 + 税率) × 数量
                                unit_price = Decimal(str(item.order_product.unit_price or 0))
                                discount_rate = Decimal(str(item.order_product.discount or 0)) / Decimal('100')
                                tax_rate = Decimal(str(item.order_product.tax_rate or 0)) / Decimal('100')
                                quantity = Decimal(str(item.quantity or 0))

                                actual_price = unit_price * (Decimal('1') - discount_rate) * (Decimal('1') + tax_rate)
                                item_amount = actual_price * quantity
                                total_amount += float(item_amount)
                except Exception:
                    total_amount = 0.0

                return_orders.append({
                    'id': ro.id,
                    'return_number': ro.return_number,
                    'return_date': ro.return_date.isoformat() if ro.return_date else None,
                    'status': ro.status,
                    'settlement_status': ro.settlement_status,
                    'settlement_date': ro.settlement_date.isoformat() if ro.settlement_date else None,
                    'reason': ro.reason,
                    'total_amount': total_amount
                })
            return return_orders
        except Exception:
            return []

    def get_combined_status(self, obj):
        """获取组合状态用于显示"""
        return f"{obj.order_status} / {obj.payment_status}"

    def get_status_info(self, obj):
        """获取完整状态信息"""
        return obj.get_status_info()

    def get_can_cancel(self, obj):
        """获取是否可以取消"""
        return obj.can_cancel()

    def get_can_edit(self, obj):
        """获取是否可以编辑"""
        return obj.can_edit()


class OrderSimpleSchema(Schema):
    """简化版订单序列化模式，用于列表展示"""
    id = fields.Integer(dump_only=True)
    order_number = fields.String(dump_only=True)
    customer_id = fields.Integer(dump_only=True)
    customer_name = fields.String(dump_only=True, attribute='customer.name')
    project_name = fields.String(dump_only=True)
    expected_date = fields.DateTime(dump_only=True)

    # 双状态字段
    order_status = fields.String(dump_only=True)
    payment_status = fields.String(dump_only=True)
    combined_status = fields.Method("get_combined_status", dump_only=True)
    can_cancel = fields.Method("get_can_cancel", dump_only=True)

    total_amount = fields.Decimal(dump_only=True, as_string=True)
    paid_amount = fields.Decimal(dump_only=True, as_string=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    def get_combined_status(self, obj):
        """获取组合状态用于显示"""
        return f"{obj.order_status} / {obj.payment_status}"

    def get_can_cancel(self, obj):
        """获取是否可以取消"""
        return obj.can_cancel()


class DeliveryNoteItemSchema(Schema):
    """发货单项目序列化模式"""
    id = fields.Integer(dump_only=True)
    delivery_note_id = fields.Integer(required=True, load_only=True)
    order_product_id = fields.Integer(required=True)
    product_specification_id = fields.Integer(required=True)
    quantity = fields.Float(required=True, validate=validate.Range(min=0.000001))
    notes = fields.String(allow_none=True)
    
    # 快照字段
    product_name = fields.String(dump_only=True)
    product_model = fields.String(dump_only=True)
    product_unit = fields.String(dump_only=True)
    specification_description = fields.String(dump_only=True)
    
    # 计算字段
    unit_price = fields.Method('get_unit_price', dump_only=True)
    amount = fields.Method('calculate_amount', dump_only=True)

    # 关联信息
    order_product = fields.Nested(OrderProductSchema, only=('id', 'quantity', 'delivered_quantity'), dump_only=True)
    product_specification = fields.Nested('ProductSpecificationSchema', only=('id', 'specification'), dump_only=True)

    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    def get_unit_price(self, obj):
        """获取单价（考虑税率和折扣）"""
        if hasattr(obj, 'order_product') and obj.order_product and obj.order_product.unit_price:
            from decimal import Decimal
            base_price = Decimal(str(obj.order_product.unit_price))
            tax_rate = Decimal(str(obj.order_product.tax_rate or 0)) / Decimal('100')
            discount = Decimal(str(obj.order_product.discount or 0)) / Decimal('100')
            actual_price = base_price * (Decimal('1') - discount) * (Decimal('1') + tax_rate)
            return float(actual_price)
        return 0.0

    def calculate_amount(self, obj):
        """计算金额（使用考虑税率和折扣的单价）"""
        from decimal import Decimal
        if hasattr(obj, 'order_product') and obj.order_product and obj.order_product.unit_price:
            # 使用get_unit_price方法获取计算后的单价
            actual_unit_price = Decimal(str(self.get_unit_price(obj)))
            quantity = Decimal(str(obj.quantity))
            return float(actual_unit_price * quantity)
        return 0.0


class DeliveryNoteSchema(Schema):
    """发货单序列化模式"""
    id = fields.Integer(dump_only=True)
    delivery_number = fields.String(dump_only=True)
    order_id = fields.Integer(required=True)
    delivery_date = fields.DateTime(required=True)
    logistics_company = fields.String(validate=validate.Length(max=100), allow_none=True)
    tracking_number = fields.String(validate=validate.Length(max=100), allow_none=True)
    recipient_name = fields.String(required=True, validate=validate.Length(min=1, max=50))
    recipient_phone = fields.String(validate=validate.Length(max=20), allow_none=True)
    delivery_address_snapshot = fields.String(required=True, validate=validate.Length(min=1, max=200))
    status = fields.String(
        validate=validate.OneOf(['待发出', '已发出', '运输中', '已签收', '已作废']),
        load_default='待发出'
    )
    settlement_status = fields.String(dump_only=True)
    settlement_date = fields.Date(dump_only=True)
    notes = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    # 总金额字段（从数据库读取）
    total_amount = fields.Decimal(places=2, dump_only=True, as_string=True)

    # 关联信息
    order = fields.Nested(OrderSimpleSchema, only=('id', 'order_number', 'project_name', 'customer_name'), dump_only=True)
    items = fields.List(fields.Nested(DeliveryNoteItemSchema), required=False)




class DeliveryNoteSimpleSchema(Schema):
    """简化版发货单序列化模式"""
    id = fields.Integer(dump_only=True)
    delivery_number = fields.String(dump_only=True)
    order_id = fields.Integer(dump_only=True)
    order_number = fields.String(dump_only=True, attribute='order.order_number')
    delivery_date = fields.DateTime(dump_only=True)
    recipient_name = fields.String(dump_only=True)
    status = fields.String(dump_only=True)
    settlement_status = fields.String(dump_only=True)
    settlement_date = fields.Date(dump_only=True)
    created_at = fields.DateTime(dump_only=True)

    # 总金额字段（从数据库读取）
    total_amount = fields.Decimal(places=2, dump_only=True, as_string=True)


class ReturnOrderItemSchema(Schema):
    """退货单项目序列化模式"""
    id = fields.Integer(dump_only=True)
    return_order_id = fields.Integer(required=True, load_only=True)
    order_product_id = fields.Integer(required=True)
    product_specification_id = fields.Integer(required=True)
    quantity = fields.Float(required=True, validate=validate.Range(min=0.000001))
    reason = fields.String(validate=validate.Length(max=200), allow_none=True)
    notes = fields.String(allow_none=True)

    # 快照字段
    product_name = fields.String(dump_only=True)
    product_model = fields.String(dump_only=True)
    product_unit = fields.String(dump_only=True)
    specification_description = fields.String(dump_only=True)

    # 计算字段
    unit_price = fields.Method('get_unit_price', dump_only=True)
    amount = fields.Method('calculate_amount', dump_only=True)

    # 关联信息
    order_product = fields.Nested(OrderProductSchema, only=('id', 'quantity', 'unit_price', 'tax_rate', 'discount'), dump_only=True)
    product_specification = fields.Nested('ProductSpecificationSchema', only=('id', 'specification'), dump_only=True)

    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    def get_unit_price(self, obj):
        """获取单价（考虑税率和折扣）"""
        if hasattr(obj, 'order_product') and obj.order_product and obj.order_product.unit_price:
            from decimal import Decimal
            base_price = Decimal(str(obj.order_product.unit_price))
            tax_rate = Decimal(str(obj.order_product.tax_rate or 0)) / Decimal('100')
            discount = Decimal(str(obj.order_product.discount or 0)) / Decimal('100')
            actual_price = base_price * (Decimal('1') - discount) * (Decimal('1') + tax_rate)
            return float(actual_price)
        return 0.0

    def calculate_amount(self, obj):
        """计算金额（使用考虑税率和折扣的单价）"""
        from decimal import Decimal
        if hasattr(obj, 'order_product') and obj.order_product and obj.order_product.unit_price:
            # 使用get_unit_price方法获取计算后的单价
            actual_unit_price = Decimal(str(self.get_unit_price(obj)))
            quantity = Decimal(str(obj.quantity))
            return float(actual_unit_price * quantity)
        return 0.0


class ReturnOrderSchema(Schema):
    """退货单序列化模式"""
    id = fields.Integer(dump_only=True)
    return_number = fields.String(dump_only=True)
    order_id = fields.Integer(required=True)
    return_date = fields.DateTime(required=True)
    status = fields.String(
        validate=validate.OneOf(['待确认', '退货中', '已签收', '已拒绝', '已取消']),
        load_default='待确认'
    )
    settlement_status = fields.String(dump_only=True)
    settlement_date = fields.Date(dump_only=True)
    reason = fields.String(required=True, validate=validate.Length(min=1, max=200))
    notes = fields.String(allow_none=True)
    total_amount = fields.Decimal(places=2, dump_only=True, as_string=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    
    # 关联信息
    order = fields.Nested(OrderSimpleSchema, only=('id', 'order_number', 'project_name', 'customer_name'), dump_only=True)
    items = fields.List(fields.Nested(ReturnOrderItemSchema), required=False)


class ReturnOrderSimpleSchema(Schema):
    """简化版退货单序列化模式"""
    id = fields.Integer(dump_only=True)
    return_number = fields.String(dump_only=True)
    order_id = fields.Integer(dump_only=True)
    order_number = fields.String(dump_only=True, attribute='order.order_number')
    project_name = fields.String(dump_only=True, attribute='order.project_name')
    return_date = fields.DateTime(dump_only=True)
    status = fields.String(dump_only=True)
    settlement_status = fields.String(dump_only=True)
    settlement_date = fields.Date(dump_only=True)
    reason = fields.String(dump_only=True)
    total_amount = fields.Decimal(places=2, dump_only=True, as_string=True)
    created_at = fields.DateTime(dump_only=True)

    # 添加简化的订单对象，只包含必要的字段
    order = fields.Nested(OrderSimpleSchema, only=('id', 'order_number'), dump_only=True)
