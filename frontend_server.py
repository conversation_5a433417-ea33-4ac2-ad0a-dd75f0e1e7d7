#!/usr/bin/env python3
"""
EMB前端静态文件服务器
提供前端静态文件的HTTP服务，支持SPA路由
"""
import os
import sys
import mimetypes
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, unquote
import threading
import webbrowser
import time
import urllib.request
import urllib.error
import json
import urllib.request
import urllib.error
import json

class SPAHandler(SimpleHTTPRequestHandler):
    """支持SPA的静态文件处理器，包含API代理功能"""

    def __init__(self, *args, **kwargs):
        # 设置静态文件目录
        self.static_dir = os.path.join(os.path.dirname(__file__), 'backend', 'static')
        # 后端API服务器地址
        self.backend_url = 'http://localhost:5001'
        super().__init__(*args, directory=self.static_dir, **kwargs)

    def map_api_path(self, path):
        """映射API路径，处理前端和后端路径不一致的情况"""
        # 定义路径映射规则
        path_mappings = {
            '/api/v1/product-categories': '/api/v1/products/categories',
            '/api/v1/brands': '/api/v1/products/brands',
        }

        # 检查是否需要映射
        for frontend_path, backend_path in path_mappings.items():
            if path.startswith(frontend_path):
                # 替换路径前缀，保留查询参数
                return path.replace(frontend_path, backend_path, 1)

        # 如果没有匹配的映射，返回原路径
        return path
    
    def end_headers(self):
        """添加CORS头部"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        super().end_headers()

    def proxy_api_request(self, method='GET'):
        """代理API请求到后端服务器"""
        try:
            # 处理特殊的路径映射
            mapped_path = self.map_api_path(self.path)

            # 构建后端URL
            backend_url = self.backend_url + mapped_path

            # 准备请求头
            headers = {}
            for header_name, header_value in self.headers.items():
                # 跳过一些不需要的头部
                if header_name.lower() not in ['host', 'connection', 'accept-encoding']:
                    headers[header_name] = header_value

            # 读取请求体（对于POST、PUT等请求）
            content_length = int(self.headers.get('Content-Length', 0))
            request_body = None
            if content_length > 0:
                request_body = self.rfile.read(content_length)

            # 创建请求
            req = urllib.request.Request(
                backend_url,
                data=request_body,
                headers=headers,
                method=method
            )

            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                # 获取响应数据
                response_data = response.read()
                response_headers = dict(response.headers)

                # 发送响应
                self.send_response(response.status)

                # 转发响应头
                for header_name, header_value in response_headers.items():
                    if header_name.lower() not in ['connection', 'transfer-encoding']:
                        self.send_header(header_name, header_value)

                self.end_headers()
                self.wfile.write(response_data)

                return True

        except urllib.error.HTTPError as e:
            # HTTP错误
            error_data = e.read()
            self.send_response(e.code)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(error_data)
            return True

        except urllib.error.URLError as e:
            # 连接错误
            self.send_response(502)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = json.dumps({
                'error': 'Backend server unavailable',
                'message': f'Cannot connect to backend server at {self.backend_url}',
                'details': str(e)
            }).encode('utf-8')
            self.wfile.write(error_response)
            return True

        except Exception as e:
            # 其他错误
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = json.dumps({
                'error': 'Proxy error',
                'message': str(e)
            }).encode('utf-8')
            self.wfile.write(error_response)
            return True
    
    def do_GET(self):
        """处理GET请求"""
        # 检查是否是API请求
        if self.path.startswith('/api/'):
            return self.proxy_api_request('GET')

        # 解析URL路径
        parsed_path = urlparse(self.path)
        path = unquote(parsed_path.path)

        # 移除开头的斜杠
        if path.startswith('/'):
            path = path[1:]
        
        # 构建完整文件路径
        file_path = os.path.join(self.static_dir, path)
        
        # 如果是根路径或者文件不存在且不是静态资源，返回index.html（SPA路由支持）
        if (path == '' or 
            (not os.path.exists(file_path) and 
             not path.startswith('assets/') and 
             not path.startswith('img/') and 
             not path.startswith('static/') and
             not path.endswith(('.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg')))):
            path = 'index.html'
            file_path = os.path.join(self.static_dir, path)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            self.send_error(404, f"File not found: {path}")
            return
        
        # 检查是否是目录
        if os.path.isdir(file_path):
            # 尝试查找index.html
            index_path = os.path.join(file_path, 'index.html')
            if os.path.exists(index_path):
                file_path = index_path
            else:
                self.send_error(404, "Directory listing not allowed")
                return
        
        try:
            # 获取文件MIME类型
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type is None:
                mime_type = 'application/octet-stream'
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # 发送响应
            self.send_response(200)
            self.send_header('Content-Type', mime_type)
            self.send_header('Content-Length', str(len(content)))
            
            # 为静态资源添加缓存头
            if path.startswith('assets/') or path.startswith('img/') or path.startswith('static/'):
                self.send_header('Cache-Control', 'public, max-age=31536000')  # 1年缓存
            else:
                self.send_header('Cache-Control', 'no-cache')
            
            self.end_headers()
            self.wfile.write(content)
            
        except Exception as e:
            print(f"Error serving file {file_path}: {e}")
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def do_POST(self):
        """处理POST请求"""
        if self.path.startswith('/api/'):
            return self.proxy_api_request('POST')
        else:
            self.send_error(405, "Method not allowed for static files")

    def do_PUT(self):
        """处理PUT请求"""
        if self.path.startswith('/api/'):
            return self.proxy_api_request('PUT')
        else:
            self.send_error(405, "Method not allowed for static files")

    def do_DELETE(self):
        """处理DELETE请求"""
        if self.path.startswith('/api/'):
            return self.proxy_api_request('DELETE')
        else:
            self.send_error(405, "Method not allowed for static files")

    def do_PATCH(self):
        """处理PATCH请求"""
        if self.path.startswith('/api/'):
            return self.proxy_api_request('PATCH')
        else:
            self.send_error(405, "Method not allowed for static files")

    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        if self.path.startswith('/api/'):
            return self.proxy_api_request('OPTIONS')
        else:
            self.send_response(200)
            self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.log_date_time_string()}] {format % args}")


def open_browser(url, delay=2):
    """延迟打开浏览器"""
    time.sleep(delay)
    print(f"🌐 正在打开浏览器: {url}")
    webbrowser.open(url)


def start_server(host='localhost', port=3000):
    """启动前端服务器"""
    # 检查静态文件目录是否存在
    static_dir = os.path.join(os.path.dirname(__file__), 'backend', 'static')
    if not os.path.exists(static_dir):
        print(f"❌ 错误: 静态文件目录不存在: {static_dir}")
        print("请确保前端构建文件已复制到 backend/static 目录")
        return False
    
    # 检查index.html是否存在
    index_path = os.path.join(static_dir, 'index.html')
    if not os.path.exists(index_path):
        print(f"❌ 错误: index.html 文件不存在: {index_path}")
        print("请确保前端构建文件已正确复制")
        return False
    
    try:
        # 创建HTTP服务器
        server = HTTPServer((host, port), SPAHandler)
        server_url = f"http://{host}:{port}"
        
        print("🚀 EMB前端服务器启动中...")
        print(f"📁 静态文件目录: {static_dir}")
        print(f"🌍 前端服务器地址: {server_url}")
        print(f"📱 前端应用: {server_url}")
        print(f"🔗 API代理: {server_url}/api/* -> http://localhost:5001/api/*")
        print("🔧 支持SPA路由和API代理")
        print("⚡ 按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        # 在新线程中延迟打开浏览器
        browser_thread = threading.Thread(target=open_browser, args=(server_url,))
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动服务器
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        return True
    except OSError as e:
        if e.errno == 10048:  # Windows: Address already in use
            print(f"❌ 错误: 端口 {port} 已被占用")
            print(f"请尝试使用其他端口: python frontend_server.py --port 3001")
        else:
            print(f"❌ 服务器启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 意外错误: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='EMB前端静态文件服务器')
    parser.add_argument('--host', default='localhost', help='服务器主机地址 (默认: localhost)')
    parser.add_argument('--port', type=int, default=3000, help='服务器端口 (默认: 3000)')
    parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    
    args = parser.parse_args()
    
    # 如果指定不打开浏览器，则禁用自动打开功能
    if args.no_browser:
        global open_browser
        open_browser = lambda *args, **kwargs: None
    
    # 启动服务器
    success = start_server(args.host, args.port)
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
