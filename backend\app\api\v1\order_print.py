"""
订单打印功能API
提供订单打印数据、批量打印功能等
基于原项目API实现，确保与现有数据库结构100%兼容
"""
from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from sqlalchemy.orm import selectinload
from datetime import datetime

from app.models.order import Order, OrderProduct
from app.models.customer import Customer
from app.models.product import Product
from app.utils.response import (
    success_response, 
    error_response, 
    not_found_response
)
from app import db

# 创建命名空间
api = Namespace('order-print', description='订单打印功能API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 定义API模型用于Swagger文档
batch_print_model = api.model('BatchPrint', {
    'order_ids': fields.List(fields.Integer, description='订单ID列表', required=True),
    'include_logo': fields.Boolean(description='是否包含Logo', default=True),
    'include_signature': fields.Boolean(description='是否包含签名区域', default=True)
})


@api.route('/<int:order_id>/print')
class OrderPrintData(Resource):
    @api.doc('get_order_print_data')
    @api.param('include_company_info', '是否包含企业信息', type='boolean', default=True)
    def get(self, order_id):
        """获取订单打印数据"""
        try:
            order = Order.query.options(
                selectinload(Order.customer),
                selectinload(Order.order_products).selectinload(OrderProduct.product)
            ).get(order_id)
            
            if not order:
                return make_response(not_found_response, "订单不存在")

            include_company_info = request.args.get('include_company_info', 'true').lower() == 'true'

            # 获取企业信息（模拟数据，实际应从数据库获取）
            company_info = None
            if include_company_info:
                company_info = {
                    'company_name': 'EMB企业管理系统',
                    'contact_phone': '************',
                    'contact_email': '<EMAIL>',
                    'office_address': '北京市朝阳区xxx路xxx号',
                    'website': 'www.emb.com',
                    'logo_url': '/static/logo.png'
                }

            # 准备订单数据
            order_data = {
                'order_number': order.order_number,
                'order_date': order.order_date.strftime('%Y-%m-%d') if order.order_date else '',
                'delivery_date': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else '',
                'status': order.order_status,
                'total_amount': float(order.total_amount) if order.total_amount else 0,
                'paid_amount': float(order.paid_amount) if order.paid_amount else 0,
                'unpaid_amount': float(order.total_amount - order.paid_amount) if order.total_amount and order.paid_amount else 0,
                'notes': order.notes or ''
            }

            # 客户信息
            customer_data = {}
            if order.customer:
                customer_data = {
                    'name': order.customer.name,
                    'contact': order.customer.contact,
                    'phone': order.customer.phone,
                    'email': order.customer.email,
                    'address': order.customer.address,
                    'tax_id': order.customer.tax_id
                }

            # 产品明细
            products_data = []
            if order.order_products:
                for i, order_product in enumerate(order.order_products, 1):
                    product_data = {
                        'sequence': i,
                        'product_name': order_product.product.name if order_product.product else '',
                        'product_model': order_product.product.model if order_product.product else '',
                        'specification': order_product.specification or '',
                        'quantity': order_product.quantity,
                        'unit_price': float(order_product.unit_price) if order_product.unit_price else 0,
                        'subtotal': float(order_product.subtotal) if order_product.subtotal else 0
                    }
                    products_data.append(product_data)

            # 统计信息
            statistics = {
                'total_quantity': sum(p['quantity'] for p in products_data),
                'total_amount': order_data['total_amount'],
                'product_count': len(products_data)
            }

            # 打印时间
            print_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            print_data = {
                'company_info': company_info,
                'order': order_data,
                'customer': customer_data,
                'products': products_data,
                'statistics': statistics,
                'print_time': print_time,
                'print_config': {
                    'paper_size': 'A4',
                    'orientation': 'portrait',
                    'margins': {
                        'top': 20,
                        'bottom': 20,
                        'left': 20,
                        'right': 20
                    }
                }
            }

            return make_response(success_response, print_data, "获取订单打印数据成功")

        except Exception as e:
            current_app.logger.error(f"获取订单打印数据失败: {str(e)}")
            return make_response(error_response, f"获取订单打印数据失败: {str(e)}")


@api.route('/batch/print')
class BatchOrderPrint(Resource):
    @api.doc('batch_get_order_print_data',
             body=batch_print_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Orders Not Found',
                 500: 'Internal Server Error'
             })
    def post(self):
        """批量获取订单打印数据"""
        try:
            data = request.get_json() or {}
            order_ids = data.get('order_ids', [])
            include_logo = data.get('include_logo', True)
            include_signature = data.get('include_signature', True)

            if not order_ids:
                return make_response(error_response, "请提供要打印的订单ID列表", code=400)

            # 验证订单是否存在
            orders = Order.query.options(
                selectinload(Order.customer),
                selectinload(Order.order_products).selectinload(OrderProduct.product)
            ).filter(Order.id.in_(order_ids)).all()

            if len(orders) != len(order_ids):
                found_ids = [order.id for order in orders]
                missing_ids = [oid for oid in order_ids if oid not in found_ids]
                return make_response(error_response, f"以下订单不存在: {missing_ids}", code=404)

            # 获取企业信息（模拟数据）
            company_info = {
                'company_name': 'EMB企业管理系统',
                'contact_phone': '************',
                'contact_email': '<EMAIL>',
                'office_address': '北京市朝阳区xxx路xxx号',
                'website': 'www.emb.com',
                'logo_url': '/static/logo.png' if include_logo else None
            }

            # 准备批量打印数据
            batch_print_data = []
            
            for order in orders:
                # 订单数据
                order_data = {
                    'id': order.id,
                    'order_number': order.order_number,
                    'order_date': order.order_date.strftime('%Y-%m-%d') if order.order_date else '',
                    'delivery_date': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else '',
                    'status': order.order_status,
                    'total_amount': float(order.total_amount) if order.total_amount else 0,
                    'paid_amount': float(order.paid_amount) if order.paid_amount else 0,
                    'notes': order.notes or ''
                }

                # 客户信息
                customer_data = {}
                if order.customer:
                    customer_data = {
                        'name': order.customer.name,
                        'contact': order.customer.contact,
                        'phone': order.customer.phone,
                        'email': order.customer.email,
                        'address': order.customer.address
                    }

                # 产品明细
                products_data = []
                if order.order_products:
                    for i, order_product in enumerate(order.order_products, 1):
                        product_data = {
                            'sequence': i,
                            'product_name': order_product.product.name if order_product.product else '',
                            'product_model': order_product.product.model if order_product.product else '',
                            'specification': order_product.specification or '',
                            'quantity': order_product.quantity,
                            'unit_price': float(order_product.unit_price) if order_product.unit_price else 0,
                            'subtotal': float(order_product.subtotal) if order_product.subtotal else 0
                        }
                        products_data.append(product_data)

                batch_print_data.append({
                    'order': order_data,
                    'customer': customer_data,
                    'products': products_data
                })

            result = {
                'company_info': company_info,
                'orders': batch_print_data,
                'print_config': {
                    'include_logo': include_logo,
                    'include_signature': include_signature,
                    'paper_size': 'A4',
                    'orientation': 'portrait'
                },
                'print_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_orders': len(batch_print_data)
            }

            return make_response(success_response, result, f"成功准备 {len(batch_print_data)} 个订单的打印数据")

        except Exception as e:
            current_app.logger.error(f"批量获取订单打印数据失败: {str(e)}")
            return make_response(error_response, f"批量获取订单打印数据失败: {str(e)}")
