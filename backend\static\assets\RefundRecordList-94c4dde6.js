import{b as e,r as t,L as a,e as l,f as r,M as o,o as n,c as s,i as d,h as u,U as i,a as c,k as f,l as p,a9 as m,F as _,m as g,t as v,g as h,N as w,j as b,W as y,Z as k,$ as C,X as N,Y as R,R as D,P as M,a3 as V,a2 as T,al as I,a4 as O,ad as S}from"./index-3d4c440c.js";import{e as z,f as G}from"./finance-789ce3e6.js";import{s as x}from"./customer-471ca075.js";import{_ as A}from"./_plugin-vue_export-helper-1b428a4d.js";const U={class:"refund-record-list"},Y={class:"card-header"},F={class:"left"},$={class:"right"},j={class:"search-bar"},B={class:"statistic-value"},L={class:"statistic-footer"},K={class:"statistic-value"},P={class:"statistic-footer"},W={class:"statistic-value"},E={class:"statistic-footer"},X={class:"statistic-value"},Z={class:"statistic-footer"},q={class:"table-container"},H={class:"pagination-container"},J={class:"confirm-content"},Q={class:"dialog-footer"},ee=A({__name:"RefundRecordList",setup(A){const ee=e(),te=t(!1),ae=t(!1),le=t(!1),re=t(!1),oe=a({refundNo:"",returnOrderNo:"",customerId:"",refundMethod:"",status:"",dateRange:[new Date((new Date).setMonth((new Date).getMonth()-1)).toISOString().split("T")[0],(new Date).toISOString().split("T")[0]]}),ne=[{text:"最近一周",value:()=>{const e=new Date,t=new Date;return t.setTime(t.getTime()-6048e5),[t,e]}},{text:"最近一个月",value:()=>{const e=new Date,t=new Date;return t.setTime(t.getTime()-2592e6),[t,e]}},{text:"最近三个月",value:()=>{const e=new Date,t=new Date;return t.setTime(t.getTime()-7776e6),[t,e]}}],se=a({page:1,pageSize:10,total:0}),de=a({prop:"",order:""}),ue=t([]),ie=a({totalRefund:0,totalRefundGrowth:0,refundCount:0,refundCountGrowth:0,averageRefund:0,averageRefundGrowth:0,pendingCount:0}),ce=t([]),fe=a({id:"",refundNo:"",returnOrderNo:"",returnOrderId:"",customerId:"",customerName:"",amount:0,refundMethod:"",refundDate:"",bankAccount:"",status:"",createBy:"",createTime:""}),pe=async()=>{te.value=!0;try{const e={page:se.page,per_page:se.pageSize,refund_number:oe.refundNo||void 0,return_order_number:oe.returnOrderNo||void 0,customer_id:oe.customerId||void 0,refund_method:oe.refundMethod||void 0,status:oe.status||void 0};if(oe.dateRange&&2===oe.dateRange.length&&(e.start_date=oe.dateRange[0],e.end_date=oe.dateRange[1]),de.prop&&de.order){const t={refundNo:"refund_number",returnOrderNo:"return_order_number",customerName:"customer_name",amount:"amount",refundDate:"refund_date",createTime:"created_at"}[de.prop]||de.prop;e.sort="descending"===de.order?`-${t}`:t}console.log("获取退款记录列表，参数:",e);const t=await z(e);if(console.log("退款记录列表响应:",t),t&&t.data){if(Array.isArray(t.data)?ce.value=t.data.map(me):t.data.items&&Array.isArray(t.data.items)?ce.value=t.data.items.map(me):t.data.list&&Array.isArray(t.data.list)?ce.value=t.data.list.map(me):(console.error("无法识别的API响应数据结构:",t.data),ce.value=[]),console.log("解析后的退款记录列表:",ce.value),t.data.page_info?se.total=t.data.page_info.total||0:t.page&&(se.total=t.page.total||0),t.data.statistics){const e=t.data.statistics;ie.totalRefund=parseFloat(e.total_refund)||0,ie.totalRefundGrowth=parseFloat(e.total_refund_growth)||0,ie.refundCount=e.refund_count||0,ie.refundCountGrowth=parseFloat(e.refund_count_growth)||0,ie.averageRefund=parseFloat(e.average_refund)||0,ie.averageRefundGrowth=parseFloat(e.average_refund_growth)||0,ie.pendingCount=e.pending_count||0}}else ce.value=[],se.total=0,i.warning("未找到退款记录")}catch(e){console.error("获取退款记录失败:",e),e.response&&(console.error("错误响应状态:",e.response.status),console.error("错误响应数据:",e.response.data)),i.error("获取退款记录失败"),ce.value=[],se.total=0}finally{te.value=!1}},me=e=>({id:e.id,refundNo:e.refund_no||e.refund_number||`TK${e.id}`,returnOrderNo:e.return_order_number||"",returnOrderId:e.return_order_id||"",customerId:e.customer_id||"",customerName:e.customer_name||"",amount:parseFloat(e.amount)||0,refundMethod:e.refund_method||"",refundDate:e.refund_date||"",bankAccount:e.bank_account_info||e.bank_account||"",status:e.status||"",createBy:e.created_by||"",createTime:e.created_at||""}),_e=async e=>{if(!(e.length<2)){ae.value=!0;try{console.log("搜索客户，关键词:",e);const t=await x(e);console.log("客户搜索响应:",t),t&&t.items&&Array.isArray(t.items)?(ue.value=t.items.map((e=>({id:e.id,name:e.name}))),console.log("客户选项已更新:",ue.value)):(ue.value=[],console.warn("客户搜索响应中没有找到items数组或为空"))}catch(t){console.error("搜索客户失败:",t),t.response&&(console.error("错误响应状态:",t.response.status),console.error("错误响应数据:",t.response.data)),i.error("搜索客户失败"),ue.value=[]}finally{ae.value=!1}}},ge=()=>{Object.keys(oe).forEach((e=>{oe[e]="dateRange"===e?[new Date((new Date).setMonth((new Date).getMonth()-1)).toISOString().split("T")[0],(new Date).toISOString().split("T")[0]]:""})),pe()},ve=({prop:e,order:t})=>{console.log("排序变化:",e,t),de.prop=e,de.order=t,pe()},he=e=>({bank_transfer:"银行转账",cash:"现金",original_channel:"原支付渠道",other:"其他"}[e]||e),we=e=>({pending:"待处理",refunded:"已退款",canceled:"已取消"}[e]||e),be=({row:e})=>"canceled"===e.status?"canceled-row":"",ye=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN")},ke=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN")},Ce=e=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(e),Ne=()=>{ee.push("/refund-record/add")},Re=e=>{ee.push(`/refund-record/detail/${e.id}`)},De=async()=>{le.value=!0;try{console.log("确认退款，ID:",fe.id);const e=await G(fe.id,"refunded");console.log("确认退款响应:",e);const t=ce.value.findIndex((e=>e.id===fe.id));-1!==t&&(ce.value[t].status="refunded"),i.success("退款确认成功"),re.value=!1,ie.pendingCount-=1,pe()}catch(e){console.error("确认退款失败:",e),e.response&&(console.error("错误响应状态:",e.response.status),console.error("错误响应数据:",e.response.data)),i.error("确认退款失败: "+(e.message||"未知错误"))}finally{le.value=!1}},Me=()=>{i.success("开始导出退款记录，请稍候..."),setTimeout((()=>{i.success("退款记录导出成功")}),1500)},Ve=e=>{console.log("分页大小变化:",e),se.pageSize=e,pe()},Te=e=>{console.log("当前页变化:",e),se.page=e,pe()};return l((()=>{console.log("RefundRecordList组件挂载"),pe()})),(e,t)=>{const a=r("el-tag"),l=r("el-icon"),z=r("el-button"),G=r("el-card"),x=r("el-input"),A=r("el-form-item"),de=r("el-option"),me=r("el-select"),Ie=r("el-date-picker"),Oe=r("el-form"),Se=r("el-col"),ze=r("router-link"),Ge=r("el-row"),xe=r("el-table-column"),Ae=r("el-tooltip"),Ue=r("el-button-group"),Ye=r("el-table"),Fe=r("el-pagination"),$e=r("el-descriptions-item"),je=r("el-descriptions"),Be=r("el-alert"),Le=r("el-dialog"),Ke=o("loading");return n(),s("div",U,[d(G,{class:"header-card mb-20"},{default:u((()=>[c("div",Y,[c("div",F,[t[11]||(t[11]=c("h2",{class:"page-title"},"退款管理",-1)),d(a,{type:"info"},{default:u((()=>t[10]||(t[10]=[f("管理退货订单的退款记录")]))),_:1})]),c("div",$,[d(z,{type:"primary",onClick:Ne},{default:u((()=>[d(l,null,{default:u((()=>[d(p(k))])),_:1}),t[12]||(t[12]=f(" 新增退款记录 "))])),_:1}),d(z,{type:"success",onClick:Me},{default:u((()=>[d(l,null,{default:u((()=>[d(p(C))])),_:1}),t[13]||(t[13]=f(" 导出记录 "))])),_:1})])])])),_:1}),d(G,{class:"mb-20"},{default:u((()=>[c("div",j,[d(Oe,{inline:!0,model:oe,class:"search-form"},{default:u((()=>[d(A,{label:"退款单号"},{default:u((()=>[d(x,{modelValue:oe.refundNo,"onUpdate:modelValue":t[0]||(t[0]=e=>oe.refundNo=e),placeholder:"输入退款单号",clearable:"",onKeyup:m(pe,["enter"])},null,8,["modelValue"])])),_:1}),d(A,{label:"关联退货单"},{default:u((()=>[d(x,{modelValue:oe.returnOrderNo,"onUpdate:modelValue":t[1]||(t[1]=e=>oe.returnOrderNo=e),placeholder:"输入退货单号",clearable:"",onKeyup:m(pe,["enter"])},null,8,["modelValue"])])),_:1}),d(A,{label:"客户名称"},{default:u((()=>[d(me,{modelValue:oe.customerId,"onUpdate:modelValue":t[2]||(t[2]=e=>oe.customerId=e),placeholder:"选择客户",clearable:"",filterable:"",remote:"","remote-method":_e,loading:ae.value},{default:u((()=>[(n(!0),s(_,null,g(ue.value,(e=>(n(),h(de,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),d(A,{label:"退款方式"},{default:u((()=>[d(me,{modelValue:oe.refundMethod,"onUpdate:modelValue":t[3]||(t[3]=e=>oe.refundMethod=e),placeholder:"选择退款方式",clearable:""},{default:u((()=>[d(de,{label:"银行转账",value:"bank_transfer"}),d(de,{label:"现金",value:"cash"}),d(de,{label:"原支付渠道",value:"original_channel"}),d(de,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1}),d(A,{label:"退款状态"},{default:u((()=>[d(me,{modelValue:oe.status,"onUpdate:modelValue":t[4]||(t[4]=e=>oe.status=e),placeholder:"选择状态",clearable:""},{default:u((()=>[d(de,{label:"待处理",value:"pending"}),d(de,{label:"已退款",value:"refunded"}),d(de,{label:"已取消",value:"canceled"})])),_:1},8,["modelValue"])])),_:1}),d(A,{label:"退款日期"},{default:u((()=>[d(Ie,{modelValue:oe.dateRange,"onUpdate:modelValue":t[5]||(t[5]=e=>oe.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",shortcuts:ne},null,8,["modelValue"])])),_:1}),d(A,null,{default:u((()=>[d(z,{type:"primary",onClick:pe},{default:u((()=>[d(l,null,{default:u((()=>[d(p(N))])),_:1}),t[14]||(t[14]=f(" 搜索 "))])),_:1}),d(z,{onClick:ge},{default:u((()=>[d(l,null,{default:u((()=>[d(p(R))])),_:1}),t[15]||(t[15]=f(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])])),_:1}),d(Ge,{gutter:20,class:"mb-20"},{default:u((()=>[d(Se,{span:6},{default:u((()=>[d(G,{class:"statistic-card"},{default:u((()=>[c("div",B,v(Ce(ie.totalRefund)),1),t[18]||(t[18]=c("div",{class:"statistic-title"},"总退款金额",-1)),c("div",L,[ie.totalRefundGrowth>=0?(n(),h(a,{key:0,type:"danger"},{default:u((()=>[t[16]||(t[16]=f(" 较上月 ")),d(l,null,{default:u((()=>[d(p(D))])),_:1}),f(" "+v(ie.totalRefundGrowth)+"% ",1)])),_:1})):(n(),h(a,{key:1,type:"success"},{default:u((()=>[t[17]||(t[17]=f(" 较上月 ")),d(l,null,{default:u((()=>[d(p(M))])),_:1}),f(" "+v(Math.abs(ie.totalRefundGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),d(Se,{span:6},{default:u((()=>[d(G,{class:"statistic-card"},{default:u((()=>[c("div",K,v(ie.refundCount),1),t[21]||(t[21]=c("div",{class:"statistic-title"},"退款记录总数",-1)),c("div",P,[ie.refundCountGrowth>=0?(n(),h(a,{key:0,type:"danger"},{default:u((()=>[t[19]||(t[19]=f(" 较上月 ")),d(l,null,{default:u((()=>[d(p(D))])),_:1}),f(" "+v(ie.refundCountGrowth)+"% ",1)])),_:1})):(n(),h(a,{key:1,type:"success"},{default:u((()=>[t[20]||(t[20]=f(" 较上月 ")),d(l,null,{default:u((()=>[d(p(M))])),_:1}),f(" "+v(Math.abs(ie.refundCountGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),d(Se,{span:6},{default:u((()=>[d(G,{class:"statistic-card"},{default:u((()=>[c("div",W,v(Ce(ie.averageRefund)),1),t[24]||(t[24]=c("div",{class:"statistic-title"},"平均退款金额",-1)),c("div",E,[ie.averageRefundGrowth>=0?(n(),h(a,{key:0,type:"danger"},{default:u((()=>[t[22]||(t[22]=f(" 较上月 ")),d(l,null,{default:u((()=>[d(p(D))])),_:1}),f(" "+v(ie.averageRefundGrowth)+"% ",1)])),_:1})):(n(),h(a,{key:1,type:"success"},{default:u((()=>[t[23]||(t[23]=f(" 较上月 ")),d(l,null,{default:u((()=>[d(p(M))])),_:1}),f(" "+v(Math.abs(ie.averageRefundGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),d(Se,{span:6},{default:u((()=>[d(G,{class:"statistic-card"},{default:u((()=>[c("div",X,v(ie.pendingCount),1),t[26]||(t[26]=c("div",{class:"statistic-title"},"待处理退款数",-1)),c("div",Z,[d(ze,{to:"/refund-pending"},{default:u((()=>[d(z,{type:"primary",size:"small"},{default:u((()=>t[25]||(t[25]=[f("查看详情")]))),_:1})])),_:1})])])),_:1})])),_:1})])),_:1}),d(G,null,{default:u((()=>[w((n(),s("div",q,[d(Ye,{data:ce.value,border:"",stripe:"",style:{width:"100%"},"row-class-name":be,onSortChange:ve},{default:u((()=>[d(xe,{type:"selection",width:"55"}),d(xe,{prop:"refundNo",label:"退款单号","min-width":"150",sortable:"custom",fixed:""},{default:u((e=>[d(z,{type:"primary",link:"",onClick:t=>Re(e.row)},{default:u((()=>[f(v(e.row.refundNo),1)])),_:2},1032,["onClick"])])),_:1}),d(xe,{prop:"returnOrderNo",label:"退货单号","min-width":"150",sortable:"custom"},{default:u((e=>[d(z,{type:"success",link:"",onClick:t=>{return a=e.row.returnOrderId,console.log("查看退货单详情:",a),void ee.push(`/return-orders/${a}`);var a}},{default:u((()=>[f(v(e.row.returnOrderNo),1)])),_:2},1032,["onClick"])])),_:1}),d(xe,{prop:"customerName",label:"客户名称","min-width":"200",sortable:"custom"},{default:u((e=>[d(z,{type:"info",link:"",onClick:t=>{return a=e.row.customerId,console.log("查看客户详情:",a),void ee.push(`/customers/${a}`);var a}},{default:u((()=>[f(v(e.row.customerName),1)])),_:2},1032,["onClick"])])),_:1}),d(xe,{prop:"amount",label:"退款金额","min-width":"120",sortable:"custom"},{default:u((e=>[f(v(Ce(e.row.amount)),1)])),_:1}),d(xe,{prop:"refundMethod",label:"退款方式",width:"120"},{default:u((e=>[d(a,null,{default:u((()=>[f(v(he(e.row.refundMethod)),1)])),_:2},1024)])),_:1}),d(xe,{prop:"refundDate",label:"退款日期",width:"120",sortable:"custom"},{default:u((e=>[f(v(ye(e.row.refundDate)),1)])),_:1}),d(xe,{prop:"bankAccount",label:"退款账户","min-width":"200"}),d(xe,{prop:"status",label:"状态",width:"100"},{default:u((e=>{return[d(a,{type:(t=e.row.status,{pending:"warning",refunded:"success",canceled:"danger"}[t]||"info")},{default:u((()=>[f(v(we(e.row.status)),1)])),_:2},1032,["type"])];var t})),_:1}),d(xe,{prop:"createBy",label:"录入人",width:"120"}),d(xe,{prop:"createTime",label:"录入时间",width:"180",sortable:"custom"},{default:u((e=>[f(v(ke(e.row.createTime)),1)])),_:1}),d(xe,{label:"操作",width:"150",fixed:"right"},{default:u((e=>[d(Ue,null,{default:u((()=>[d(Ae,{content:"查看详情",placement:"top"},{default:u((()=>[d(z,{type:"primary",link:"",onClick:t=>Re(e.row)},{default:u((()=>[d(l,null,{default:u((()=>[d(p(V))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024),"pending"===e.row.status?(n(),h(Ae,{key:0,content:"编辑",placement:"top"},{default:u((()=>[d(z,{type:"success",link:"",onClick:t=>{return a=e.row,void ee.push(`/refund-record/edit/${a.id}`);var a}},{default:u((()=>[d(l,null,{default:u((()=>[d(p(T))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)):b("",!0),"pending"===e.row.status?(n(),h(Ae,{key:1,content:"确认退款",placement:"top"},{default:u((()=>[d(z,{type:"warning",link:"",onClick:t=>{return a=e.row,console.log("确认退款记录:",a),Object.assign(fe,a),void(re.value=!0);var a}},{default:u((()=>[d(l,null,{default:u((()=>[d(p(I))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)):b("",!0),"pending"===e.row.status?(n(),h(Ae,{key:2,content:"删除",placement:"top"},{default:u((()=>[d(z,{type:"danger",link:"",onClick:t=>{return a=e.row,void y.confirm(`确定要删除退款单号为${a.refundNo}的退款记录吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{console.log("删除退款记录:",a),ce.value=ce.value.filter((e=>e.id!==a.id)),"pending"===a.status&&(ie.pendingCount-=1),ie.refundCount-=1,i.success("删除成功"),pe()}catch(e){console.error("删除失败:",e),e.response&&(console.error("错误响应状态:",e.response.status),console.error("错误响应数据:",e.response.data)),i.error("删除失败: "+(e.message||"未知错误"))}})).catch((()=>{console.log("用户取消删除操作")}));var a}},{default:u((()=>[d(l,null,{default:u((()=>[d(p(O))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)):b("",!0),d(Ae,{content:"打印凭证",placement:"top"},{default:u((()=>[d(z,{type:"info",link:"",onClick:t=>{return a=e.row,console.log("打印退款凭证:",a),i.success(`正在准备打印退款凭证：${a.refundNo}`),void setTimeout((()=>{i.success("打印准备完成，请检查打印队列")}),1e3);var a}},{default:u((()=>[d(l,null,{default:u((()=>[d(p(S))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:1})])),_:1},8,["data"]),c("div",H,[d(Fe,{"current-page":se.page,"onUpdate:currentPage":t[6]||(t[6]=e=>se.page=e),"page-size":se.pageSize,"onUpdate:pageSize":t[7]||(t[7]=e=>se.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:se.total,onSizeChange:Ve,onCurrentChange:Te},null,8,["current-page","page-size","total"])])])),[[Ke,te.value]])])),_:1}),d(Le,{modelValue:re.value,"onUpdate:modelValue":t[9]||(t[9]=e=>re.value=e),title:"确认退款",width:"500px","destroy-on-close":""},{footer:u((()=>[c("span",Q,[d(z,{onClick:t[8]||(t[8]=e=>re.value=!1)},{default:u((()=>t[28]||(t[28]=[f("取消")]))),_:1}),d(z,{type:"primary",onClick:De,loading:le.value},{default:u((()=>t[29]||(t[29]=[f(" 确认退款 ")]))),_:1},8,["loading"])])])),default:u((()=>[c("div",J,[t[27]||(t[27]=c("p",null,"您确定要确认以下退款记录吗？",-1)),d(je,{border:"",column:1},{default:u((()=>[d($e,{label:"退款单号"},{default:u((()=>[f(v(fe.refundNo),1)])),_:1}),d($e,{label:"退货单号"},{default:u((()=>[f(v(fe.returnOrderNo),1)])),_:1}),d($e,{label:"客户名称"},{default:u((()=>[f(v(fe.customerName),1)])),_:1}),d($e,{label:"退款金额"},{default:u((()=>[f(v(Ce(fe.amount)),1)])),_:1}),d($e,{label:"退款方式"},{default:u((()=>[f(v(he(fe.refundMethod)),1)])),_:1}),d($e,{label:"退款日期"},{default:u((()=>[f(v(ye(fe.refundDate)),1)])),_:1})])),_:1}),d(Be,{type:"warning",closable:!1,"show-icon":"",title:"确认后将无法修改该退款记录！",class:"mt-20"})])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-84b09c7e"]]);export{ee as default};
