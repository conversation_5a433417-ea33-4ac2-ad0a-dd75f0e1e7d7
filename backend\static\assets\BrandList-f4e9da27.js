import{J as e,b as a,r as l,L as r,d as t,e as o,f as s,M as n,o as i,c as d,i as p,h as c,a as u,k as g,l as m,a9 as f,N as _,g as h,t as v,U as w,W as y,ap as b,Z as k,X as x,Y as z,a2 as C,a4 as S}from"./index-3d4c440c.js";import{l as j,d as B}from"./brand-81076433.js";import{_ as A}from"./_plugin-vue_export-helper-1b428a4d.js";const U={class:"brand-list"},E={class:"card-header"},L={class:"left"},V={class:"right"},$={class:"search-bar"},N={class:"table-container"},T={key:1,class:"default-logo"},I={key:1},J={class:"pagination-container"},K=A(e({__name:"BrandList",setup(e){const A=a(),K=l(!1),M=r({name:""}),O=r({page:1,pageSize:10,total:0}),P=r({prop:"sort_order",order:"ascending"}),R=l([]),W=t((()=>P.prop?"descending"===P.order?`-${P.prop}`:P.prop:null)),X=async()=>{K.value=!0;try{const e={page:O.page,per_page:O.pageSize,name:M.name||void 0,sort:W.value||void 0};Object.keys(e).forEach((a=>void 0===e[a]&&delete e[a])),console.log("正在请求品牌数据，参数:",e);const a=await j(e);console.log("品牌数据响应:",a),a?(Array.isArray(a.items)?(R.value=a.items,a.page_info&&(O.total=a.page_info.total_items||0)):Array.isArray(a)?(R.value=a,O.total=a.length):(console.error("无法识别的响应格式:",a),R.value=[],O.total=0),console.log("解析后的品牌列表:",R.value),console.log("解析后的分页信息:",O)):(console.error("响应为空"),R.value=[],O.total=0)}catch(e){console.error("获取品牌列表失败:",e),console.error("错误详情:",e.response||e.message||e),w.error(e.message||"获取品牌列表失败"),R.value=[],O.total=0}finally{K.value=!1}},Y=()=>{M.name="",X()},Z=({prop:e,order:a})=>{P.prop=e,P.order=a??"ascending",X()},q=e=>{O.pageSize=e,O.page=1,X()},D=e=>{O.page=e,X()},F=()=>{A.push({name:"BrandNew"})},G=e=>{const a=e.target.src||"";b.log(`品牌标志加载失败: ${a}`,{level:"warn",origin:"brand_list_logo_error",immediate:!1}),console.warn("Logo load error:",a)};return o((()=>{X()})),(e,a)=>{const l=s("el-tag"),r=s("el-icon"),t=s("el-button"),o=s("el-card"),b=s("el-input"),j=s("el-form-item"),P=s("el-form"),W=s("el-table-column"),H=s("el-image"),Q=s("el-link"),ee=s("el-tooltip"),ae=s("el-table"),le=s("el-pagination"),re=n("loading");return i(),d("div",U,[p(o,{class:"header-card mb-20"},{default:c((()=>[u("div",E,[u("div",L,[a[4]||(a[4]=u("h2",{class:"page-title"},"品牌管理",-1)),p(l,{type:"info"},{default:c((()=>a[3]||(a[3]=[g("管理产品品牌信息")]))),_:1})]),u("div",V,[p(t,{type:"primary",onClick:F},{default:c((()=>[p(r,null,{default:c((()=>[p(m(k))])),_:1}),a[5]||(a[5]=g(" 添加品牌 "))])),_:1})])])])),_:1}),p(o,{class:"mb-20"},{default:c((()=>[u("div",$,[p(P,{inline:!0,model:M,class:"search-form"},{default:c((()=>[p(j,{label:"品牌名称"},{default:c((()=>[p(b,{modelValue:M.name,"onUpdate:modelValue":a[0]||(a[0]=e=>M.name=e),placeholder:"输入品牌名称",clearable:"",onKeyup:f(X,["enter"])},null,8,["modelValue"])])),_:1}),p(j,null,{default:c((()=>[p(t,{type:"primary",onClick:X},{default:c((()=>[p(r,null,{default:c((()=>[p(m(x))])),_:1}),a[6]||(a[6]=g(" 搜索 "))])),_:1}),p(t,{onClick:Y},{default:c((()=>[p(r,null,{default:c((()=>[p(m(z))])),_:1}),a[7]||(a[7]=g(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])])),_:1}),p(o,null,{default:c((()=>[_((i(),d("div",N,[p(ae,{data:R.value,border:"",stripe:"",style:{width:"100%"},onSortChange:Z,"default-sort":{prop:"sort_order",order:"ascending"}},{default:c((()=>[p(W,{type:"index",width:"60",label:"序号"}),p(W,{prop:"name",label:"品牌名称","min-width":"110",sortable:"custom"}),p(W,{prop:"logo_url",label:"品牌标志",width:"100"},{default:c((e=>[e.row.logo_url?(i(),h(H,{key:0,src:e.row.logo_url,fit:"contain",style:{width:"80px",height:"40px"},"preview-src-list":[e.row.logo_url],"preview-teleported":"",onError:G},{error:c((()=>[u("div",{class:"image-error"},[u("img",{src:"/img/placeholders/brand-error.svg",alt:"加载失败",style:{width:"80px",height:"40px"}})])])),_:2},1032,["src","preview-src-list"])):(i(),d("div",T,[u("img",{src:"/img/placeholders/brand-placeholder.svg",alt:"暂无图标",style:{width:"80px",height:"40px"}})]))])),_:1}),p(W,{prop:"description",label:"品牌描述","min-width":"200","show-overflow-tooltip":""}),p(W,{prop:"website",label:"官网","min-width":"150"},{default:c((e=>[e.row.website?(i(),h(Q,{key:0,type:"primary",href:e.row.website,target:"_blank"},{default:c((()=>[g(v(e.row.website),1)])),_:2},1032,["href"])):(i(),d("span",I,"-"))])),_:1}),p(W,{prop:"sort_order",label:"排序",width:"100",sortable:"custom"}),p(W,{fixed:"right",label:"操作",width:"100"},{default:c((e=>[p(ee,{content:"编辑",placement:"top"},{default:c((()=>[p(t,{type:"primary",link:"",onClick:a=>{return l=e.row,void A.push({name:"BrandEdit",params:{id:l.id}});var l}},{default:c((()=>[p(r,null,{default:c((()=>[p(m(C))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024),p(ee,{content:"删除",placement:"top"},{default:c((()=>[p(t,{type:"danger",link:"",onClick:a=>(async e=>{await y.confirm(`确定要删除品牌 "${e.name}" 吗？此操作不可恢复。`,"警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{K.value=!0;try{await B(e.id),w.success("品牌删除成功"),1===R.value.length&&O.page>1&&(O.page-=1),await X()}catch(a){console.error("删除品牌失败:",a),w.error(a.message||"删除品牌失败")}finally{K.value=!1}})).catch((()=>{w.info("取消删除")}))})(e.row)},{default:c((()=>[p(r,null,{default:c((()=>[p(m(S))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["data"]),u("div",J,[p(le,{"current-page":O.page,"onUpdate:currentPage":a[1]||(a[1]=e=>O.page=e),"page-size":O.pageSize,"onUpdate:pageSize":a[2]||(a[2]=e=>O.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:O.total,onSizeChange:q,onCurrentChange:D},null,8,["current-page","page-size","total"])])])),[[re,K.value]])])),_:1})])}}}),[["__scopeId","data-v-984dceee"]]);export{K as default};
