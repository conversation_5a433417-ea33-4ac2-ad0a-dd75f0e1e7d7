"""
测试Flask-RESTx字段的allow_none属性
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from flask_restx import Api, fields

def test_restx_field_properties():
    """测试Flask-RESTx字段属性"""
    app = Flask(__name__)
    api = Api(app)
    
    print("=== 测试Flask-RESTx字段属性 ===\n")
    
    # 测试不同的字段定义方式
    print("1. 测试Integer字段的allow_none参数:")
    
    try:
        # 方式1：在构造函数中设置
        field1 = fields.Integer(allow_none=True, description='测试字段1')
        print(f"✅ 构造函数设置: allow_none={getattr(field1, 'allow_none', '未设置')}")
    except Exception as e:
        print(f"❌ 构造函数设置失败: {e}")
    
    try:
        # 方式2：后设置属性
        field2 = fields.Integer(description='测试字段2')
        field2.allow_none = True
        print(f"✅ 后设置属性: allow_none={getattr(field2, 'allow_none', '未设置')}")
    except Exception as e:
        print(f"❌ 后设置属性失败: {e}")
    
    # 测试required属性
    print("\n2. 测试required属性:")
    field3 = fields.Integer(required=True)
    field4 = fields.Integer(required=False)
    field5 = fields.Integer()  # 默认值
    
    print(f"required=True: {field3.required}")
    print(f"required=False: {field4.required}")
    print(f"默认值: {field5.required}")
    
    # 测试String字段
    print("\n3. 测试String字段:")
    try:
        str_field = fields.String(allow_none=True)
        print(f"✅ String allow_none: {getattr(str_field, 'allow_none', '未设置')}")
    except Exception as e:
        print(f"❌ String allow_none失败: {e}")
    
    # 查看字段的所有属性
    print("\n4. 查看Integer字段的所有属性:")
    int_field = fields.Integer()
    for attr in dir(int_field):
        if not attr.startswith('_'):
            try:
                value = getattr(int_field, attr)
                if not callable(value):
                    print(f"  {attr}: {value}")
            except:
                pass

if __name__ == "__main__":
    test_restx_field_properties()
