"""
系统设置相关Schema
用于序列化和反序列化系统设置、企业信息、企业银行账户、单据模板、备份记录等数据
"""
from marshmallow import Schema, fields, validate, post_load
from app.models.setting import SystemSetting, CompanyInfo, CompanyBankAccount, DocumentTemplate, BackupRecord


class SystemSettingSchema(Schema):
    """系统设置Schema"""
    id = fields.Integer(dump_only=True)
    key = fields.String(required=True, validate=validate.Length(min=1, max=100))
    value = fields.String(required=True)
    description = fields.String(allow_none=True, validate=validate.Length(max=255))
    category = fields.String(missing='general', validate=validate.Length(max=50))
    data_type = fields.String(missing='string', validate=validate.OneOf(['string', 'integer', 'float', 'boolean', 'json']))
    is_public = fields.Boolean(missing=False)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def make_system_setting(self, data, **kwargs):
        return data


class CompanyInfoSchema(Schema):
    """企业信息Schema"""
    id = fields.Integer(dump_only=True)
    company_name = fields.String(required=True, validate=validate.Length(min=1, max=200))
    legal_representative = fields.String(allow_none=True, validate=validate.Length(max=100))
    registered_address = fields.String(allow_none=True, validate=validate.Length(max=500))
    office_address = fields.String(allow_none=True, validate=validate.Length(max=500))
    contact_phone = fields.String(allow_none=True, validate=validate.Length(max=50))
    contact_email = fields.Email(allow_none=True, validate=validate.Length(max=100))
    tax_id = fields.String(allow_none=True, validate=validate.Length(max=50))
    business_license = fields.String(allow_none=True, validate=validate.Length(max=50))
    website = fields.Url(allow_none=True, validate=validate.Length(max=200))
    logo_url = fields.Url(allow_none=True, validate=validate.Length(max=500))
    description = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def make_company_info(self, data, **kwargs):
        return data


class CompanyBankAccountSchema(Schema):
    """企业银行账户Schema"""
    id = fields.Integer(dump_only=True)
    bank_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    account_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    account_number = fields.String(required=True, validate=validate.Length(min=1, max=50))
    account_type = fields.String(missing='对公账户', validate=validate.Length(max=20))
    is_default = fields.Boolean(missing=False)
    notes = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def make_company_bank_account(self, data, **kwargs):
        return data


class DocumentTemplateSchema(Schema):
    """单据模板Schema"""
    id = fields.Integer(dump_only=True)
    name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    type = fields.String(required=True, validate=validate.OneOf([
        'quotation', 'order', 'delivery_note', 'statement', 'invoice', 'receipt'
    ]))
    content = fields.String(required=True)
    is_default = fields.Boolean(missing=False)
    description = fields.String(allow_none=True, validate=validate.Length(max=255))
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def make_document_template(self, data, **kwargs):
        return data


class BackupRecordSchema(Schema):
    """备份记录Schema"""
    id = fields.Integer(dump_only=True)
    backup_name = fields.String(required=True, validate=validate.Length(min=1, max=200))
    backup_type = fields.String(missing='manual', validate=validate.OneOf(['manual', 'auto', 'scheduled']))
    file_path = fields.String(allow_none=True, validate=validate.Length(max=500))
    file_size = fields.Integer(allow_none=True, validate=validate.Range(min=0))
    status = fields.String(missing='pending', validate=validate.OneOf([
        'pending', 'running', 'completed', 'failed', 'cancelled'
    ]))
    error_message = fields.String(allow_none=True)
    started_at = fields.DateTime(allow_none=True)
    completed_at = fields.DateTime(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    @post_load
    def make_backup_record(self, data, **kwargs):
        return data


# 简化版Schema，用于列表显示
class SystemSettingSimpleSchema(Schema):
    """系统设置简化Schema"""
    id = fields.Integer()
    key = fields.String()
    value = fields.String()
    category = fields.String()
    is_public = fields.Boolean()


class CompanyInfoSimpleSchema(Schema):
    """企业信息简化Schema"""
    id = fields.Integer()
    company_name = fields.String()
    contact_phone = fields.String()
    contact_email = fields.String()


class CompanyBankAccountSimpleSchema(Schema):
    """企业银行账户简化Schema"""
    id = fields.Integer()
    bank_name = fields.String()
    account_name = fields.String()
    account_number = fields.String()
    is_default = fields.Boolean()


class DocumentTemplateSimpleSchema(Schema):
    """单据模板简化Schema"""
    id = fields.Integer()
    name = fields.String()
    type = fields.String()
    is_default = fields.Boolean()


class BackupRecordSimpleSchema(Schema):
    """备份记录简化Schema"""
    id = fields.Integer()
    backup_name = fields.String()
    backup_type = fields.String()
    status = fields.String()
    file_size = fields.Integer()
    created_at = fields.DateTime()
