"""
数据备份和恢复API
提供完整的备份管理功能
"""
import os
from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError

from app import db
from app.models.system import BackupRecord, BackupSettings
from app.schemas.system import BackupRecordSchema, BackupSettingsSchema, CreateBackupSchema
# 延迟导入备份服务，避免应用上下文问题
from app.utils.response import success_response, error_response, not_found_response

def get_backup_service():
    """获取备份服务实例"""
    from app.services.backup_service import backup_service
    return backup_service

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 创建命名空间
api = Namespace('backup', description='数据备份和恢复相关操作')

# API模型定义
backup_record_model = api.model('BackupRecord', {
    'id': fields.Integer(description='备份ID'),
    'name': fields.String(required=True, description='备份名称'),
    'filename': fields.String(description='文件名'),
    'file_size': fields.Integer(description='文件大小'),
    'backup_type': fields.String(description='备份类型'),
    'status': fields.String(description='状态'),
    'description': fields.String(description='描述'),
    'created_at': fields.DateTime(description='创建时间'),
    'completed_at': fields.DateTime(description='完成时间')
})

backup_settings_model = api.model('BackupSettings', {
    'auto_backup_enabled': fields.Boolean(required=True, description='是否启用自动备份'),
    'backup_frequency': fields.String(required=True, description='备份频率'),
    'backup_time': fields.String(required=True, description='备份时间'),
    'backup_day': fields.Integer(description='备份日期'),
    'keep_count': fields.Integer(required=True, description='保留数量'),
    'backup_path': fields.String(required=True, description='备份路径')
})

create_backup_model = api.model('CreateBackup', {
    'name': fields.String(required=True, description='备份名称'),
    'description': fields.String(description='备份描述')
})


@api.route('/backups')
class BackupList(Resource):
    @api.doc('get_backup_list')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    def get(self):
        """获取备份列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            
            result = get_backup_service().get_backup_list(page=page, per_page=per_page)
            return make_response(success_response, result, "获取备份列表成功")
            
        except Exception as e:
            current_app.logger.error(f"获取备份列表失败: {str(e)}")
            return make_response(error_response, f"获取备份列表失败: {str(e)}")
    
    @api.doc('create_backup')
    @api.expect(create_backup_model)
    def post(self):
        """创建新备份"""
        try:
            # 验证请求数据
            schema = CreateBackupSchema()
            data = schema.load(request.get_json() or {})
            
            # 创建备份
            backup_record = get_backup_service().create_backup(
                name=data['name'],
                description=data.get('description'),
                backup_type=data.get('backup_type', 'manual')
            )
            
            # 序列化返回数据
            result_schema = BackupRecordSchema()
            result = result_schema.dump(backup_record)
            
            return make_response(success_response, result, "备份创建成功", 201)
            
        except ValidationError as e:
            return make_response(error_response, e.messages, "数据验证失败", 400)
        except Exception as e:
            current_app.logger.error(f"创建备份失败: {str(e)}")
            return make_response(error_response, f"创建备份失败: {str(e)}")


@api.route('/backups/<int:backup_id>')
class BackupDetail(Resource):
    @api.doc('get_backup_detail')
    def get(self, backup_id):
        """获取备份详情"""
        try:
            backup = get_backup_service().get_backup_by_id(backup_id)
            if not backup:
                return make_response(not_found_response, "备份记录不存在")
            
            schema = BackupRecordSchema()
            result = schema.dump(backup)
            
            return make_response(success_response, result, "获取备份详情成功")
            
        except Exception as e:
            current_app.logger.error(f"获取备份详情失败: {str(e)}")
            return make_response(error_response, f"获取备份详情失败: {str(e)}")
    
    @api.doc('delete_backup')
    def delete(self, backup_id):
        """删除备份"""
        try:
            success = get_backup_service().delete_backup(backup_id)
            if not success:
                return make_response(not_found_response, "备份记录不存在")
            
            return make_response(success_response, message="删除备份成功")
            
        except Exception as e:
            current_app.logger.error(f"删除备份失败: {str(e)}")
            return make_response(error_response, f"删除备份失败: {str(e)}")


@api.route('/backups/<int:backup_id>/download')
class BackupDownload(Resource):
    @api.doc('download_backup')
    def get(self, backup_id):
        """下载备份文件"""
        try:
            backup = get_backup_service().get_backup_by_id(backup_id)
            if not backup:
                return make_response(not_found_response, "备份记录不存在")
            
            if not os.path.exists(backup.file_path):
                return make_response(error_response, "备份文件不存在")
            
            return send_file(
                backup.file_path,
                as_attachment=True,
                download_name=backup.filename,
                mimetype='application/octet-stream'
            )
            
        except Exception as e:
            current_app.logger.error(f"下载备份失败: {str(e)}")
            return make_response(error_response, f"下载备份失败: {str(e)}")


@api.route('/backups/<int:backup_id>/restore')
class BackupRestore(Resource):
    @api.doc('restore_backup')
    def post(self, backup_id):
        """从备份恢复数据"""
        try:
            get_backup_service().restore_from_backup(backup_id)
            return make_response(success_response, message="数据恢复成功")
            
        except ValueError as e:
            return make_response(error_response, str(e), 400)
        except FileNotFoundError as e:
            return make_response(not_found_response, str(e))
        except Exception as e:
            current_app.logger.error(f"数据恢复失败: {str(e)}")
            return make_response(error_response, f"数据恢复失败: {str(e)}")


@api.route('/backups/upload')
class BackupUpload(Resource):
    @api.doc('upload_backup')
    def post(self):
        """上传备份文件并恢复"""
        try:
            # 检查文件
            if 'backup_file' not in request.files:
                return make_response(error_response, "请选择备份文件", 400)
            
            file = request.files['backup_file']
            if file.filename == '':
                return make_response(error_response, "请选择备份文件", 400)
            
            # 获取其他参数
            name = request.form.get('name', f"上传备份_{file.filename}")
            description = request.form.get('description', '从外部上传的备份文件')
            
            # 上传并恢复
            backup_record = get_backup_service().upload_and_restore_backup(
                file=file,
                name=name,
                description=description
            )
            
            # 序列化返回数据
            schema = BackupRecordSchema()
            result = schema.dump(backup_record)
            
            return make_response(success_response, result, "上传备份并恢复成功", 201)
            
        except ValueError as e:
            return make_response(error_response, str(e), 400)
        except Exception as e:
            current_app.logger.error(f"上传备份失败: {str(e)}")
            return make_response(error_response, f"上传备份失败: {str(e)}")


@api.route('/backups/statistics')
class BackupStatistics(Resource):
    @api.doc('get_backup_statistics')
    def get(self):
        """获取备份统计信息"""
        try:
            result = get_backup_service().get_backup_statistics()
            return make_response(success_response, result, "获取备份统计成功")
            
        except Exception as e:
            current_app.logger.error(f"获取备份统计失败: {str(e)}")
            return make_response(error_response, f"获取备份统计失败: {str(e)}")


@api.route('/backup-settings')
class BackupSettingsResource(Resource):
    @api.doc('get_backup_settings')
    def get(self):
        """获取备份设置"""
        try:
            settings = get_backup_service().get_backup_settings()
            if not settings:
                return make_response(not_found_response, "备份设置不存在")
            
            schema = BackupSettingsSchema()
            result = schema.dump(settings)
            
            return make_response(success_response, result, "获取备份设置成功")
            
        except Exception as e:
            current_app.logger.error(f"获取备份设置失败: {str(e)}")
            return make_response(error_response, f"获取备份设置失败: {str(e)}")
    
    @api.doc('update_backup_settings')
    @api.expect(backup_settings_model)
    def put(self):
        """更新备份设置"""
        try:
            # 验证请求数据
            schema = BackupSettingsSchema()
            data = schema.load(request.get_json() or {})
            
            # 更新设置
            settings = get_backup_service().update_backup_settings(data)
            
            # 序列化返回数据
            result = schema.dump(settings)
            
            return make_response(success_response, result, "更新备份设置成功")
            
        except ValidationError as e:
            return make_response(error_response, e.messages, "数据验证失败", 400)
        except Exception as e:
            current_app.logger.error(f"更新备份设置失败: {str(e)}")
            return make_response(error_response, f"更新备份设置失败: {str(e)}")


@api.route('/scheduler/status')
class SchedulerStatus(Resource):
    @api.doc('get_scheduler_status')
    def get(self):
        """获取调度器状态"""
        try:
            from app.services.scheduler_service import scheduler_service

            # 获取备份任务状态
            backup_job = scheduler_service.get_backup_job_status()

            # 获取所有任务列表
            all_jobs = scheduler_service.list_jobs()

            result = {
                'backup_job': backup_job,
                'all_jobs': all_jobs,
                'scheduler_running': scheduler_service._initialized and scheduler_service.scheduler.running if scheduler_service.scheduler else False
            }

            return make_response(success_response, result, "获取调度器状态成功")

        except Exception as e:
            current_app.logger.error(f"获取调度器状态失败: {str(e)}")
            return make_response(error_response, f"获取调度器状态失败: {str(e)}")
