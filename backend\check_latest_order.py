#!/usr/bin/env python3
"""
检查最新订单的产品来源信息
"""

import sqlite3
import os

def check_latest_order():
    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询最新的订单
        cursor.execute("""
            SELECT id, order_number, customer_id, quotation_ids, created_at
            FROM orders 
            ORDER BY id DESC 
            LIMIT 1
        """)
        
        latest_order = cursor.fetchone()
        if not latest_order:
            print("❌ 没有找到订单")
            return
            
        order_id, order_number, customer_id, quotation_ids, created_at = latest_order
        print(f"\n🎯 最新订单信息:")
        print(f"  订单ID: {order_id}")
        print(f"  订单号: {order_number}")
        print(f"  客户ID: {customer_id}")
        print(f"  关联报价单: {quotation_ids}")
        print(f"  创建时间: {created_at}")
        
        # 查询该订单的所有产品
        cursor.execute("""
            SELECT id, product_specification_id, quantity, unit_price, 
                   source_type, source_id, source_display
            FROM order_products 
            WHERE order_id = ?
            ORDER BY id
        """, (order_id,))
        
        products = cursor.fetchall()
        print(f"\n📦 订单产品明细 (共{len(products)}个产品):")
        
        for product in products:
            prod_id, spec_id, quantity, unit_price, source_type, source_id, source_display = product
            print(f"  产品{prod_id}:")
            print(f"    规格ID: {spec_id}")
            print(f"    数量: {quantity}")
            print(f"    单价: {unit_price}")
            print(f"    来源类型: {source_type}")
            print(f"    来源ID: {source_id}")
            print(f"    来源显示: {source_display}")
            print("    ---")
            
        # 统计来源类型
        cursor.execute("""
            SELECT source_type, COUNT(*) as count
            FROM order_products 
            WHERE order_id = ?
            GROUP BY source_type
        """, (order_id,))
        
        source_stats = cursor.fetchall()
        print(f"\n📊 产品来源统计:")
        for source_type, count in source_stats:
            print(f"  {source_type}: {count}个产品")
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_latest_order()
