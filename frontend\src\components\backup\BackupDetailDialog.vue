<template>
  <el-dialog
    v-model="dialogVisible"
    title="备份详情"
    width="700px"
    :close-on-click-modal="false"
  >
    <div v-if="backup" class="backup-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="备份名称" :span="2">
          <el-tag size="large" type="primary">{{ backup.name }}</el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="备份类型">
          <el-tag :type="backup.backup_type === 'manual' ? 'primary' : 'success'">
            {{ backup.backup_type === 'manual' ? '手动备份' : '自动备份' }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="备份状态">
          <el-tag :type="getStatusType(backup.status)">
            {{ getStatusText(backup.status) }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="文件名称">
          {{ backup.filename }}
        </el-descriptions-item>
        
        <el-descriptions-item label="文件大小">
          {{ formatFileSize(backup.file_size) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(backup.created_at) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="完成时间">
          {{ formatDateTime(backup.completed_at) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="校验和" v-if="backup.checksum">
          <el-input 
            :value="backup.checksum" 
            readonly 
            size="small"
            style="font-family: monospace;"
          />
        </el-descriptions-item>
        
        <el-descriptions-item label="备份描述" :span="2" v-if="backup.description">
          <div class="description-content">
            {{ backup.description }}
          </div>
        </el-descriptions-item>
        
        <el-descriptions-item label="错误信息" :span="2" v-if="backup.error_message">
          <el-alert
            :title="backup.error_message"
            type="error"
            :closable="false"
            show-icon
          />
        </el-descriptions-item>
      </el-descriptions>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button 
          type="primary" 
          @click="downloadBackup"
          :disabled="backup.status !== 'completed'"
        >
          <el-icon><Download /></el-icon>
          下载备份
        </el-button>
        
        <el-button 
          type="warning" 
          @click="restoreBackup"
          :disabled="backup.status !== 'completed'"
        >
          <el-icon><RefreshRight /></el-icon>
          恢复数据
        </el-button>
        
        <el-button 
          type="danger" 
          @click="deleteBackup"
        >
          <el-icon><Delete /></el-icon>
          删除备份
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, RefreshRight, Delete } from '@element-plus/icons-vue'
import { backupApi } from '@/api/system'

// Props
interface Props {
  modelValue: boolean
  backup: any
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'restore', backup: any): void
}

const emit = defineEmits<Emits>()

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 下载备份
const downloadBackup = async () => {
  if (!props.backup) return
  
  try {
    const blob = await backupApi.downloadBackup(props.backup.id)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = props.backup.filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 恢复备份
const restoreBackup = () => {
  if (!props.backup) return
  emit('restore', props.backup)
  dialogVisible.value = false
}

// 删除备份
const deleteBackup = async () => {
  if (!props.backup) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除备份 "${props.backup.name}" 吗？此操作不可恢复！`,
      '删除备份',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await backupApi.deleteBackup(props.backup.id)
    ElMessage.success('删除备份成功')
    dialogVisible.value = false
    
    // 通知父组件刷新列表
    emit('restore', null) // 复用restore事件来通知刷新
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除备份失败:', error)
      ElMessage.error('删除备份失败')
    }
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (!bytes || bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

const getStatusType = (status: string) => {
  const statusMap = {
    'pending': 'info',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '等待中',
    'running': '进行中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.backup-detail {
  padding: 0;
}

.description-content {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  color: #606266;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.action-buttons {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
  background-color: #fafafa;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

:deep(.el-descriptions__cell) {
  padding: 12px 16px;
}

:deep(.el-input__inner) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
