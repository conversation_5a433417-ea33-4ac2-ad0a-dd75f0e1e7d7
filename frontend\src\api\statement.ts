/**
 * 对账单管理API
 * 包括对账单管理、发货单关联、退货单关联、导出等功能
 */
import { http } from './request'
import type { ListResponse } from '@/types/common'
import type { Statement, StatementCreate, StatementUpdate } from '@/types/statement'
import type { ApiResponse } from '@/types/api'

// 对账单查询参数类型
export interface StatementQuery {
  page?: number
  per_page?: number
  customer_id?: string
  status?: string
  start_date?: string
  end_date?: string
}

// 可用发货单查询参数类型
export interface AvailableDeliveryNotesQuery {
  page?: number
  per_page?: number
  delivery_number?: string
  order_number?: string
  start_date?: string
  end_date?: string
}

// 可用退货单查询参数类型
export interface AvailableReturnOrdersQuery {
  page?: number
  per_page?: number
  return_number?: string
  order_number?: string
  start_date?: string
  end_date?: string
}

// 导出参数类型
export interface ExportStatementsQuery {
  customer_id?: string
  status?: string
  start_date?: string
  end_date?: string
}

/**
 * 对账单管理API类
 */
export class StatementApi {
  private baseUrl = '/statements'

  /**
   * 获取对账单列表
   */
  async getList(params?: StatementQuery): Promise<{ list: Statement[]; pagination: any }> {
    console.log('调用getStatements API，参数:', params);

    try {
      const response: any = await http.get(`${this.baseUrl}`, params);

      console.log('getStatements API原始响应:', response);

      // 检查并正确解析返回数据
      if (response && typeof response === 'object') {
        // 如果直接返回格式为 {list: [], pagination: {}}
        if (Array.isArray(response.list)) {
          console.log('从response.list获取数据, 长度:', response.list.length);
          return {
            list: response.list as Statement[],
            pagination: response.pagination || {}
          };
        }

        // 如果返回格式为 {code:200, list:[], pagination:{}}
        if (response.code === 200 && Array.isArray(response.list)) {
          console.log('从response.code=200且response.list获取数据, 长度:', response.list.length);
          return {
            list: response.list as Statement[],
            pagination: response.pagination || {}
          };
        }

        // 如果返回格式为 {code:200, data:{items:[], pagination:{}}}
        if (response.code === 200 && response.data && Array.isArray(response.data.items)) {
          console.log('从response.code=200且response.data.items获取数据, 长度:', response.data.items.length);
          return {
            list: response.data.items as Statement[],
            pagination: response.data.pagination || {}
          };
        }

        // 如果返回格式为 {code:200, data:[], page:{}}
        if (response.code === 200 && Array.isArray(response.data)) {
          console.log('从response.code=200且response.data获取数据, 长度:', response.data.length);
          return {
            list: response.data as Statement[],
            pagination: response.page || {}
          };
        }

        // 如果返回格式为 {items:[], pagination:{}}
        if (Array.isArray(response.items)) {
          console.log('从response.items获取数据, 长度:', response.items.length);
          return {
            list: response.items as Statement[],
            pagination: response.pagination || {}
          };
        }

        // 对响应进行更多检查
        console.warn('未匹配到预期的响应格式，尝试深入检查response结构:', response);

        // 如果response本身就是数组（极少数情况）
        if (Array.isArray(response)) {
          console.log('response本身是数组，长度:', response.length);
          return {
            list: response as Statement[],
            pagination: {}
          };
        }

        // 遍历response查找可能的数据数组
        for (const key in response) {
          if (Array.isArray(response[key])) {
            console.log(`从response.${key}获取到数组数据，长度:`, response[key].length);
            return {
              list: response[key] as Statement[],
              pagination: {}
            };
          }
        }
      }

      // 兜底处理
      console.warn('getStatements: 未能识别的响应格式，返回空数组', response);
      return { list: [], pagination: {} };
    } catch (error) {
      console.error('getStatements API调用失败:', error);
      throw error; // 继续向上抛出错误，让调用者处理
    }
  }

  /**
   * 获取对账单详情
   */
  async getById(id: string): Promise<ApiResponse<Statement>> {
    return http.get(`${this.baseUrl}/${id}`)
  }

  /**
   * 获取对账单相关的退货单
   */
  async getReturnOrders(id: string): Promise<ApiResponse<any[]>> {
    return http.get(`${this.baseUrl}/${id}/return-orders`)
  }

  /**
   * 创建对账单
   */
  async create(data: StatementCreate): Promise<ApiResponse<Statement>> {
    return http.post(`${this.baseUrl}`, data)
  }

  /**
   * 更新对账单
   */
  async update(id: string, data: StatementUpdate): Promise<ApiResponse<Statement>> {
    return http.put(`${this.baseUrl}/${id}`, data)
  }

  /**
   * 删除对账单
   */
  async delete(id: string): Promise<ApiResponse<any>> {
    return http.delete(`${this.baseUrl}/${id}`)
  }

  /**
   * 确认对账单
   */
  async confirm(id: string, data: { due_date: string }): Promise<ApiResponse<Statement>> {
    return http.put(`${this.baseUrl}/${id}/confirm`, data)
  }

  /**
   * 获取可用的发货单列表
   */
  async getAvailableDeliveryNotes(customerId?: string, status?: string, params?: AvailableDeliveryNotesQuery): Promise<any> {
    console.log('调用getAvailableDeliveryNotes API，参数:', { customerId, status, params });

    try {
      const response: any = await http.get(`${this.baseUrl}/available-delivery-notes`, {
        ...(customerId ? { customer_id: customerId } : {}),
        ...(status ? { status } : {}),
        ...params
      });

      console.log('getAvailableDeliveryNotes API原始响应:', response);

      // 检查并正确解析返回数据
      if (response && typeof response === 'object') {
        // 如果直接返回数组
        if (Array.isArray(response)) {
          console.log('从response数组获取数据, 长度:', response.length);
          return response;
        }

        // 如果返回格式为 {code:200, data:[]}
        if (response.code === 200 && Array.isArray(response.data)) {
          console.log('从response.code=200且response.data获取数据, 长度:', response.data.length);
          return response.data;
        }

        // 如果返回格式为 {data:[]}
        if (Array.isArray(response.data)) {
          console.log('从response.data获取数据, 长度:', response.data.length);
          return response.data;
        }
      }

      // 兜底处理
      console.warn('getAvailableDeliveryNotes: 未能识别的响应格式，返回空数组', response);
      return [];
    } catch (error) {
      console.error('getAvailableDeliveryNotes API调用失败:', error);
      throw error;
    }
  }

  /**
   * 获取可用的退货单列表
   */
  async getAvailableReturnOrders(customerId?: string, status?: string, params?: AvailableReturnOrdersQuery): Promise<any> {
    console.log('调用getAvailableReturnOrders API，参数:', { customerId, status, params });

    try {
      const response: any = await http.get(`${this.baseUrl}/available-return-orders`, {
        ...(customerId ? { customer_id: customerId } : {}),
        ...(status ? { status } : {}),
        ...params
      });

      console.log('getAvailableReturnOrders API原始响应:', response);

      // 检查并正确解析返回数据
      if (response && typeof response === 'object') {
        // 如果直接返回数组
        if (Array.isArray(response)) {
          return response;
        }

        // 如果有data字段且是数组
        if (response.data && Array.isArray(response.data)) {
          return response.data;
        }

        // 如果有list字段且是数组
        if (response.list && Array.isArray(response.list)) {
          return response;
        }

        // 如果整个response就是我们需要的数据
        if (response.data) {
          return response.data;
        }
      }

      // 兜底处理
      console.warn('getAvailableReturnOrders: 未能识别的响应格式，返回空数组', response);
      return [];
    } catch (error) {
      console.error('getAvailableReturnOrders API调用失败:', error);
      throw error;
    }
  }

  /**
   * 导出对账单
   */
  async exportStatement(id: string): Promise<ApiResponse<Blob>> {
    return http.get(`${this.baseUrl}/${id}/export`, undefined, {
      responseType: 'blob'
    } as any)
  }

  /**
   * 导出对账单列表
   */
  async exportStatements(params?: ExportStatementsQuery): Promise<ApiResponse<Blob>> {
    return http.get(`${this.baseUrl}/export`, params, {
      responseType: 'blob'
    } as any)
  }

  /**
   * 强制结清对账单
   */
  async forceSettle(id: string): Promise<ApiResponse<Statement>> {
    return http.put(`${this.baseUrl}/${id}/force-settle`)
  }

  /**
   * 获取收款记录列表
   */
  async getPaymentRecords(params?: any): Promise<ApiResponse<any>> {
    return http.get('/payments/statement-payments', params)
  }

  /**
   * 创建直接收款记录
   */
  async createDirectPayment(data: {
    statement_id: number
    amount: number
    payment_method: string
    payment_source?: string
    reference_number?: string
    bank_account?: string
    payment_date: string
    notes?: string
    voucher_files?: any[]
  }): Promise<ApiResponse<any>> {
    return http.post('/payments/direct-payment', data)
  }

  /**
   * 创建直接收款记录（支持文件上传）
   */
  async createDirectPaymentWithFiles(formData: FormData): Promise<ApiResponse<any>> {
    return http.post('/payments/direct-payment-with-files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 导出对账单
   */
  async export(id: number, params: {
    format: 'xlsx' | 'pdf'
    columns?: string[]
    include_header?: boolean
  }): Promise<Blob> {
    const queryParams = new URLSearchParams()
    queryParams.append('format', params.format)
    if (params.columns && params.columns.length > 0) {
      queryParams.append('columns', params.columns.join(','))
    }
    if (params.include_header !== undefined) {
      queryParams.append('include_header', params.include_header.toString())
    }

    const response = await fetch(`/api/v1/statement-export/${id}/export?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`导出失败: ${response.statusText}`)
    }

    return response.blob()
  }
}

// 导出API实例
export const statementApi = new StatementApi()

// 导出默认实例
export default statementApi


