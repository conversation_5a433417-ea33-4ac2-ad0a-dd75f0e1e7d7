import{a as e,u as a,b as l}from"./settings-1b359923.js";import{r as t,L as s,e as o,f as n,M as r,o as u,c as d,i as c,h as i,U as p,a as m,N as f,g,k as _,j as b,l as y,Z as v}from"./index-3d4c440c.js";import{_ as h}from"./_plugin-vue_export-helper-1b428a4d.js";const V={class:"system-settings-container"},k={class:"flex-between"},x=["src"],w=["src"],S=h({__name:"SystemSettings",setup(h){const S=t("company"),O=s({company:!1}),N=s({company:!1}),$=t(!1),J=t(null),U=s({id:null,name:"",address:"",contact_person:"",phone:"",email:"",website:"",tax_number:"",bank_name:"",bank_account:"",logo:null,logo_url:"",business_license:null,business_license_url:"",notes:""}),j=t(null),I={name:[{required:!0,message:"请输入企业名称",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],address:[{max:200,message:"长度不能超过 200 个字符",trigger:"blur"}],contact_person:[{max:50,message:"长度不能超过 50 个字符",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/,message:"请输入有效的电话号码",trigger:"blur"}],email:[{type:"email",message:"请输入有效的邮箱地址",trigger:"blur"}],website:[{pattern:/^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,message:"请输入有效的网址",trigger:"blur"}],tax_number:[{pattern:/^[A-Z0-9]{15,20}$/,message:"请输入有效的税号",trigger:"blur"}],bank_account:[{pattern:/^\d{10,30}$/,message:"请输入有效的银行账号",trigger:"blur"}]},C=e=>{if(!e)return"";if(e.startsWith("http")||e.startsWith("blob"))return e;const a="http://127.0.0.1:5001";return e.startsWith("/")?`${a}${e}`:`${a}/${e}`},E=async()=>{O.company=!0;try{const a=await e();console.log("获取到的企业信息:",a);const l=a.data||a;Object.keys(U).forEach((e=>{e in l&&(U[e]=l[e])})),j.value=JSON.parse(JSON.stringify(U)),localStorage.setItem("companyInfo",JSON.stringify(U)),console.log("企业信息已保存到localStorage"),p.success("企业信息加载成功")}catch(a){console.error("获取企业信息失败:",a);const e=localStorage.getItem("companyInfo");if(e)try{const a=JSON.parse(e);Object.keys(U).forEach((e=>{e in a&&(U[e]=a[e])})),j.value=JSON.parse(JSON.stringify(U)),p.info("已从本地缓存恢复企业信息")}catch(l){console.error("解析本地缓存的企业信息失败:",l),p.error("获取企业信息失败，且无法从本地缓存恢复")}else p.error("获取企业信息失败")}finally{O.company=!1}},F=async()=>{J.value&&await J.value.validate((async e=>{if(!e)return p.warning("请正确填写企业信息"),!1;N.company=!0;try{const e=new FormData;Object.keys(U).forEach((a=>{"logo"!==a&&"business_license"!==a&&null!==U[a]&&e.append(a,U[a])})),U.logo instanceof File&&e.append("logo",U.logo),U.business_license instanceof File&&e.append("business_license",U.business_license);const l=await a(e);console.log("保存企业信息成功:",l),j.value=JSON.parse(JSON.stringify(U)),localStorage.setItem("companyInfo",JSON.stringify(U)),p.success("企业信息保存成功"),$.value=!1}catch(l){console.error("保存企业信息失败:",l),p.error("保存企业信息失败")}finally{N.company=!1}}))},W=()=>{j.value&&Object.keys(U).forEach((e=>{e in j.value&&(U[e]=j.value[e])})),$.value=!1},q=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<5;return a?!!l||(p.error("图片大小不能超过 5MB!"),!1):(p.error("只能上传图片文件!"),!1)},z=async(e,a)=>{const{file:t}=e;N.company=!0;try{const e=await l(t);e&&e.data&&e.data.url?(U[a]=e.data.url,p.success("上传成功")):p.error("上传失败: 服务器响应格式不正确")}catch(s){p.error(`上传失败: ${s.message||"未知错误"}`)}finally{N.company=!1}},L=e=>{z(e,"logo")},M=e=>{z(e,"license_image")};return o((()=>{E()})),(e,a)=>{const l=n("el-card"),t=n("el-button"),s=n("el-input"),o=n("el-form-item"),p=n("el-col"),h=n("el-row"),j=n("el-icon"),z=n("el-upload"),R=n("el-form"),Z=n("el-tab-pane"),A=n("el-empty"),B=n("el-tabs"),D=r("loading");return u(),d("div",V,[c(l,{class:"header-card"},{default:i((()=>a[9]||(a[9]=[m("div",{class:"flex-between"},[m("h2",{class:"form-title"},"系统设置")],-1)]))),_:1}),c(B,{modelValue:S.value,"onUpdate:modelValue":a[8]||(a[8]=e=>S.value=e),class:"settings-tabs"},{default:i((()=>[c(Z,{label:"企业信息",name:"company"},{default:i((()=>[f((u(),g(l,null,{header:i((()=>[m("div",k,[a[14]||(a[14]=m("span",null,"企业详细信息",-1)),m("div",null,[$.value?b("",!0):(u(),g(t,{key:0,type:"primary",onClick:a[0]||(a[0]=e=>$.value=!0)},{default:i((()=>a[10]||(a[10]=[_("编辑")]))),_:1})),$.value?b("",!0):(u(),g(t,{key:1,type:"info",onClick:E},{default:i((()=>a[11]||(a[11]=[_("查看上次保存")]))),_:1})),$.value?(u(),g(t,{key:2,type:"success",onClick:F,loading:N.company},{default:i((()=>a[12]||(a[12]=[_("保存")]))),_:1},8,["loading"])):b("",!0),$.value?(u(),g(t,{key:3,onClick:W},{default:i((()=>a[13]||(a[13]=[_("取消")]))),_:1})):b("",!0)])])])),default:i((()=>[c(R,{ref_key:"companyFormRef",ref:J,model:U,rules:I,"label-width":"120px",disabled:!$.value},{default:i((()=>[c(h,{gutter:20},{default:i((()=>[c(p,{span:12},{default:i((()=>[c(o,{label:"企业名称",prop:"name"},{default:i((()=>[c(s,{modelValue:U.name,"onUpdate:modelValue":a[1]||(a[1]=e=>U.name=e)},null,8,["modelValue"])])),_:1})])),_:1}),c(p,{span:12},{default:i((()=>[c(o,{label:"统一社会信用代码",prop:"tax_id"},{default:i((()=>[c(s,{modelValue:U.tax_id,"onUpdate:modelValue":a[2]||(a[2]=e=>U.tax_id=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(h,{gutter:20},{default:i((()=>[c(p,{span:12},{default:i((()=>[c(o,{label:"联系人",prop:"contact"},{default:i((()=>[c(s,{modelValue:U.contact,"onUpdate:modelValue":a[3]||(a[3]=e=>U.contact=e)},null,8,["modelValue"])])),_:1})])),_:1}),c(p,{span:12},{default:i((()=>[c(o,{label:"联系电话",prop:"phone"},{default:i((()=>[c(s,{modelValue:U.phone,"onUpdate:modelValue":a[4]||(a[4]=e=>U.phone=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(h,{gutter:20},{default:i((()=>[c(p,{span:12},{default:i((()=>[c(o,{label:"电子邮箱",prop:"email"},{default:i((()=>[c(s,{modelValue:U.email,"onUpdate:modelValue":a[5]||(a[5]=e=>U.email=e)},null,8,["modelValue"])])),_:1})])),_:1}),c(p,{span:12},{default:i((()=>[c(o,{label:"传真",prop:"fax"},{default:i((()=>[c(s,{modelValue:U.fax,"onUpdate:modelValue":a[6]||(a[6]=e=>U.fax=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(h,{gutter:20},{default:i((()=>[c(p,{span:24},{default:i((()=>[c(o,{label:"公司地址",prop:"address"},{default:i((()=>[c(s,{modelValue:U.address,"onUpdate:modelValue":a[7]||(a[7]=e=>U.address=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(h,{gutter:20},{default:i((()=>[c(p,{span:12},{default:i((()=>[c(o,{label:"公司Logo",prop:"logo"},{default:i((()=>[c(z,{class:"avatar-uploader",action:"#","show-file-list":!1,"http-request":L,"before-upload":q},{default:i((()=>[U.logo?(u(),d("img",{key:0,src:C(U.logo),class:"avatar"},null,8,x)):(u(),g(j,{key:1,class:"avatar-uploader-icon"},{default:i((()=>[c(y(v))])),_:1}))])),_:1})])),_:1})])),_:1}),c(p,{span:12},{default:i((()=>[c(o,{label:"营业执照",prop:"license_image"},{default:i((()=>[c(z,{class:"avatar-uploader",action:"#","show-file-list":!1,"http-request":M,"before-upload":q},{default:i((()=>[U.license_image?(u(),d("img",{key:0,src:C(U.license_image),class:"avatar"},null,8,w)):(u(),g(j,{key:1,class:"avatar-uploader-icon"},{default:i((()=>[c(y(v))])),_:1}))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model","disabled"])])),_:1})),[[D,O.company]])])),_:1}),c(Z,{label:"银行账户",name:"bank"},{default:i((()=>[c(A,{description:"银行账户管理功能待实现"})])),_:1}),c(Z,{label:"用户管理",name:"users"},{default:i((()=>[c(A,{description:"用户管理功能待实现"})])),_:1})])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-f8296260"]]);export{S as default};
