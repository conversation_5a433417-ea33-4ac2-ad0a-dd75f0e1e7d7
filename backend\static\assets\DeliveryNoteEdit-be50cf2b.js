import{J as e,u as a,b as t,r as l,d as r,e as i,w as d,f as o,M as u,o as n,c as s,i as c,h as _,a as p,t as m,k as v,N as y,g as f,F as g,m as h,j as b,U as w,W as q}from"./index-3d4c440c.js";import{b as V,u as k,c as $}from"./delivery-8900e898.js";import{c as A,i as U,s as x}from"./order-d6035a15.js";import{_ as C}from"./_plugin-vue_export-helper-1b428a4d.js";const S={class:"delivery-note-edit"},j={class:"flex-between"},N={class:"page-title"},E={class:"operation-buttons"},I={key:0,class:"warning-notice"},M={class:"action-buttons"},Y=C(e({__name:"DeliveryNoteEdit",setup(e,{expose:C}){const Y=a(),D=t(),P=l(),z=l(!1),O=l(!1),B=l(!1),J=l([]),R=l(null),T=l(null),F=r((()=>Boolean(Y.params.id)));C({isEdit:F});const W=r((()=>Number(Y.params.id))),G=r((()=>{const e=Y.params.orderId||Y.query.orderId;return e?Number(e):null})),H=l({id:"",order_id:String(G.value||0),order_number:"",delivery_number:"自动生成",delivery_date:(new Date).toISOString().slice(0,10),customer_id:"",delivery_method:"logistics",logistics_company:"",tracking_number:"",recipient_name:"",recipient_phone:"",delivery_address_snapshot:"",notes:"",status:"pending",products:[],customer_name:""}),K={order_id:[{required:!0,message:"请选择关联订单",trigger:"change"}],delivery_date:[{required:!0,message:"请选择送货日期",trigger:"change"}],recipient_name:[{required:!0,message:"请输入收货人姓名",trigger:"blur"}],recipient_phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],delivery_address_snapshot:[{required:!0,message:"请输入送货地址",trigger:"blur"}],logistics_company:[{required:!0,message:"请输入物流公司名称",trigger:"blur",validator:(e,a,t)=>{"logistics"!==H.value.delivery_method||a?t():t(new Error("请输入物流公司名称"))}}],tracking_number:[{required:!0,message:"请输入运单号",trigger:"blur",validator:(e,a,t)=>{"logistics"!==H.value.delivery_method||a?t():t(new Error("请输入运单号"))}}]},L=r((()=>H.value.products.some((e=>e.quantity>Q(e)&&e.quantity>0)))),Q=e=>{const a=e.order_quantity||0,t=e.delivered_quantity||e.shipped_quantity||0;return Math.max(0,a-t)},X=e=>{const a=Q(e);e.quantity>a&&e.quantity>0&&w.warning(`产品"${e.product_name}"的发货数量${e.quantity}超过订单未发货数量${a}，系统将自动更新订单数量`),e.quantity<0?e.quantity=0:e.quantity=Math.floor(e.quantity)};r((()=>H.value.products.reduce(((e,a)=>e+a.unit_price*a.quantity),0)));const Z=async e=>{var a,t,l,r;if(e){z.value=!0;try{const d=(await A(String(e))).data;if(d)if(console.log("订单详情数据:",d),console.log("订单客户数据:",d.customer),console.log("订单产品数据:",d.products),R.value=d,T.value=d,H.value.order_id=String(d.id),H.value.order_number=d.order_number||"",H.value.customer_id=(null==(a=d.customer)?void 0:a.id)||"",H.value.customer_name=(null==(t=d.customer)?void 0:t.name)||"",H.value.recipient_name=(null==(l=d.customer)?void 0:l.contact)||"",H.value.recipient_phone=(null==(r=d.customer)?void 0:r.phone)||"",H.value.delivery_address_snapshot=d.project_address||"",H.value.delivery_method="送货上门"===d.delivery_terms?"self":"logistics",F.value)H.value.products.forEach((e=>{var a;const t=null==(a=d.products)?void 0:a.find((a=>a.id===e.order_product_id));t&&(e.order_quantity=t.quantity||0,e.delivered_quantity=t.delivered_quantity||t.shipped_quantity||0)}));else{if(!d.products||0===d.products.length){console.log("订单没有产品数据，尝试通过getOrderAvailableProducts获取...");try{const a=await U(e);a&&a.data&&Array.isArray(a.data)?(console.log("通过API获取到的产品数据:",a.data),d.products=a.data):console.warn("getOrderAvailableProducts API没有返回有效数据")}catch(i){console.error("获取订单可用产品失败:",i),w.warning("无法获取订单产品数据，可能影响送货单创建")}}if(d.products&&d.products.length>0){console.log("准备映射订单产品到发货单项目"),d.products.forEach(((e,a)=>{console.log(`详细产品${a+1}数据:`,JSON.stringify(e,null,2))}));const e=d.products.map((e=>{var a,t;const l=e.delivered_quantity||e.shipped_quantity||0,r=Math.max(0,(e.quantity||0)-l),i=e.specification_description||(null==(a=e.product_specification)?void 0:a.specification)||(null==(t=e.specification)?void 0:t.specification)||"";return console.log(`产品 ${e.product_name} 订单数量: ${e.quantity}, 已发货数量: ${l}, 剩余数量: ${r}, 规格: ${i}, 单位: ${e.product_unit||e.unit||"个"}`),{id:void 0,order_product_id:e.id,product_id:e.product_id,product_specification_id:e.product_specification_id,quantity:0,unit_price:e.unit_price||0,product_name:e.product_name||e.name||"",product_model:e.product_model||e.model||"",specification_description:i,specification:{specification:i},product_unit:e.product_unit||e.unit||"个",order_quantity:e.quantity||0,shipped_quantity:l,delivered_quantity:l,notes:e.notes||void 0}}));H.value.products=e}else console.warn("订单中没有产品数据，无法创建送货单项目"),w.warning("订单中没有产品数据，请检查订单后重试")}}catch(i){console.error("获取订单详情失败",i),w.error("获取订单详情失败")}finally{z.value=!1}}},ee=e=>{"self"===e&&(H.value.logistics_company="",H.value.tracking_number="")},ae=async e=>{if(!e)return H.value.customer_id="",H.value.customer_name="",H.value.products=[],H.value.recipient_name="",H.value.recipient_phone="",H.value.delivery_address_snapshot="",void(H.value.order_number="");await Z(Number(e))},te=async e=>{if(null!=e){B.value=!0;try{const a=await x(e);a?("data"in a&&a.data?Array.isArray(a.data)?J.value=a.data:"list"in a.data&&Array.isArray(a.data.list)?J.value=a.data.list:"items"in a.data&&Array.isArray(a.data.items)?J.value=a.data.items:J.value=[]:"list"in a&&Array.isArray(a.list)?J.value=a.list:Array.isArray(a)?J.value=a:J.value=[],console.log("订单搜索结果:",J.value)):J.value=[]}catch(a){console.error("搜索订单失败:",a),w.error("搜索订单失败"),J.value=[]}finally{B.value=!1}}},le=()=>{D.go(-1)},re=async()=>{P.value&&await P.value.validate((async e=>{var a,t;if(!e)return void w.warning("表单填写有误，请检查");const l=H.value.products.filter((e=>e.quantity&&e.quantity>0)),r=H.value.products.length-l.length;if(H.value.products=l,0!==H.value.products.length){if(r>0&&w.info(`已自动移除 ${r} 个发货数量为0的商品项`),L.value)try{await q.confirm("部分产品的发货数量超过订单未发货数量，系统将自动更新订单中的产品数量。确定继续保存吗？","确认提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})}catch(i){return}O.value=!0;try{if("self"===H.value.delivery_method&&(H.value.logistics_company="",H.value.tracking_number=""),F.value){const e={delivery_date:H.value.delivery_date,logistics_company:H.value.logistics_company||void 0,tracking_number:H.value.tracking_number||void 0,notes:H.value.notes||void 0,recipient_name:H.value.recipient_name,recipient_phone:H.value.recipient_phone,delivery_address_snapshot:H.value.delivery_address_snapshot};await k(String(W.value),e),w.success("送货单更新成功")}else{const e=H.value.products.map((e=>({order_product_id:e.order_product_id,quantity:e.quantity,notes:e.notes||void 0}))),a={order_id:Number(H.value.order_id),delivery_date:H.value.delivery_date,recipient_name:H.value.recipient_name,recipient_phone:H.value.recipient_phone,delivery_address_snapshot:H.value.delivery_address_snapshot,logistics_company:H.value.logistics_company||void 0,tracking_number:H.value.tracking_number||void 0,notes:H.value.notes||void 0,items:e};await $(String(H.value.order_id),a),w.success("送货单创建成功")}G.value?D.push(`/orders/${G.value}`):D.push("/delivery")}catch(i){console.error("保存送货单失败",i);const e=(null==(t=null==(a=i.response)?void 0:a.data)?void 0:t.message)||i.message||"保存送货单失败";w.error(e)}finally{O.value=!1}}else w.warning("送货单必须包含至少一个商品")}))};return i((()=>{(async()=>{var e,a,t,l,r,i;if(F.value){z.value=!0;try{console.log(`正在加载ID为${W.value}的送货单详情...`);const e=await V(String(W.value));if(!e||!e.data)throw new Error("获取送货单详情失败：API返回空数据");const d=e.data;console.log("送货单详情数据:",d),d&&(H.value={id:d.id,delivery_number:d.delivery_number,order_id:d.order_id,order_number:d.order_number||"",customer_id:(null==(t=null==(a=d.order)?void 0:a.customer)?void 0:t.id)||"",delivery_date:d.delivery_date,customer_name:(null==(r=null==(l=d.order)?void 0:l.customer)?void 0:r.name)||"",recipient_name:d.recipient_name||"",recipient_phone:d.recipient_phone||"",delivery_address_snapshot:d.delivery_address_snapshot||(null==(i=d.order)?void 0:i.project_address)||"",delivery_method:d.logistics_company?"logistics":"self",logistics_company:d.logistics_company||"",tracking_number:d.tracking_number||"",notes:d.notes||"",status:d.status,products:(d.items||[]).map((e=>(console.log("处理送货单项目:",e),{id:e.id,order_product_id:e.order_product_id,product_id:e.product_id,product_specification_id:e.product_specification_id,quantity:e.quantity,unit_price:e.unit_price||0,product_name:e.product_name,product_model:e.product_model,specification_description:e.specification_description||e.specification,specification:{specification:e.specification_description||e.specification},product_unit:e.product_unit||e.unit,order_quantity:e.order_quantity||0,shipped_quantity:e.shipped_quantity||0,delivered_quantity:e.delivered_quantity||e.shipped_quantity||0,notes:e.notes||""})))},console.log("表单数据已更新:",H.value),d.order_id&&(console.log("开始加载关联订单详情:",d.order_id),await Z(Number(d.order_id))))}catch(d){console.error("获取送货单详情失败",d),w.error(`获取送货单详情失败: ${d instanceof Error?d.message:"未知错误"}`)}finally{z.value=!1}}else if(G.value){const a=await A(String(G.value));a&&a.data&&(J.value=[{id:a.data.id,order_number:a.data.order_number,customer_name:(null==(e=a.data.customer)?void 0:e.name)||"",status:a.data.status,customer_id:a.data.customer_id,project_name:a.data.project_name,total_amount:a.data.total_amount,expected_date:a.data.expected_date,created_at:a.data.created_at,updated_at:a.data.updated_at}]),await Z(G.value)}})()})),d((()=>H.value.order_id),((e,a)=>{!F.value&&e&&e!==a&&Z(Number(e))}),{immediate:!1}),(e,a)=>{const t=o("el-button"),l=o("el-card"),r=o("el-input"),i=o("el-form-item"),d=o("el-col"),w=o("el-date-picker"),q=o("el-row"),V=o("el-option"),k=o("el-select"),$=o("el-table-column"),A=o("el-input-number"),U=o("el-table"),x=o("el-alert"),C=o("el-form"),Y=u("loading");return n(),s("div",S,[c(l,{class:"header-card mb-20"},{default:_((()=>[p("div",j,[p("h2",N,m(F.value?"编辑送货单":"新增送货单"),1),p("div",null,[c(t,{onClick:le},{default:_((()=>a[11]||(a[11]=[v("返回")]))),_:1}),c(t,{type:"primary",onClick:re,loading:O.value},{default:_((()=>a[12]||(a[12]=[v("保存")]))),_:1},8,["loading"])])])])),_:1}),y((n(),f(C,{ref_key:"formRef",ref:P,model:H.value,rules:K,"label-width":"120px",class:"mb-20"},{default:_((()=>[c(l,{class:"mb-20"},{header:_((()=>a[13]||(a[13]=[p("div",{class:"card-header"},[p("h3",null,"基本信息")],-1)]))),default:_((()=>[c(q,{gutter:20},{default:_((()=>[c(d,{span:12},{default:_((()=>[c(i,{label:"送货单号",prop:"delivery_number"},{default:_((()=>[c(r,{modelValue:H.value.delivery_number,"onUpdate:modelValue":a[0]||(a[0]=e=>H.value.delivery_number=e),placeholder:"自动生成",disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),c(d,{span:12},{default:_((()=>[c(i,{label:"送货日期",prop:"delivery_date"},{default:_((()=>[c(w,{modelValue:H.value.delivery_date,"onUpdate:modelValue":a[1]||(a[1]=e=>H.value.delivery_date=e),type:"date",placeholder:"选择送货日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(q,{gutter:20},{default:_((()=>[c(d,{span:12},{default:_((()=>[c(i,{label:"关联订单",prop:"order_number"},{default:_((()=>[c(k,{modelValue:H.value.order_number,"onUpdate:modelValue":a[2]||(a[2]=e=>H.value.order_number=e),placeholder:"选择关联订单",style:{width:"100%"},filterable:"",remote:"","remote-method":te,loading:B.value,onChange:ae},{default:_((()=>[(n(!0),s(g,null,h(J.value,(e=>(n(),f(V,{key:e.id,label:`${e.order_number} - ${e.customer_name}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})])),_:1}),c(d,{span:12},{default:_((()=>[c(i,{label:"客户名称"},{default:_((()=>[c(r,{modelValue:H.value.customer_name,"onUpdate:modelValue":a[3]||(a[3]=e=>H.value.customer_name=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(q,{gutter:20},{default:_((()=>[c(d,{span:12},{default:_((()=>[c(i,{label:"收货人",prop:"recipient_name"},{default:_((()=>[c(r,{modelValue:H.value.recipient_name,"onUpdate:modelValue":a[4]||(a[4]=e=>H.value.recipient_name=e),placeholder:"请输入收货人姓名"},null,8,["modelValue"])])),_:1})])),_:1}),c(d,{span:12},{default:_((()=>[c(i,{label:"联系电话",prop:"recipient_phone"},{default:_((()=>[c(r,{modelValue:H.value.recipient_phone,"onUpdate:modelValue":a[5]||(a[5]=e=>H.value.recipient_phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(q,null,{default:_((()=>[c(d,{span:24},{default:_((()=>[c(i,{label:"送货地址",prop:"delivery_address_snapshot"},{default:_((()=>[c(r,{modelValue:H.value.delivery_address_snapshot,"onUpdate:modelValue":a[6]||(a[6]=e=>H.value.delivery_address_snapshot=e),placeholder:"请输入送货地址"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(q,{gutter:20},{default:_((()=>[c(d,{span:12},{default:_((()=>[c(i,{label:"配送方式",prop:"delivery_method"},{default:_((()=>[c(k,{modelValue:H.value.delivery_method,"onUpdate:modelValue":a[7]||(a[7]=e=>H.value.delivery_method=e),placeholder:"选择配送方式",style:{width:"100%"},onChange:ee},{default:_((()=>[c(V,{label:"物流配送",value:"logistics"}),c(V,{label:"自主配送",value:"self"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),"self"!==H.value.delivery_method?(n(),f(d,{key:0,span:12},{default:_((()=>[c(i,{label:"运单号",prop:"tracking_number"},{default:_((()=>[c(r,{modelValue:H.value.tracking_number,"onUpdate:modelValue":a[8]||(a[8]=e=>H.value.tracking_number=e),placeholder:"请输入物流运单号"},null,8,["modelValue"])])),_:1})])),_:1})):b("",!0)])),_:1}),"self"!==H.value.delivery_method?(n(),f(q,{key:0,gutter:20},{default:_((()=>[c(d,{span:12},{default:_((()=>[c(i,{label:"物流公司",prop:"logistics_company"},{default:_((()=>[c(r,{modelValue:H.value.logistics_company,"onUpdate:modelValue":a[9]||(a[9]=e=>H.value.logistics_company=e),placeholder:"请输入物流公司名称"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})):b("",!0),c(q,null,{default:_((()=>[c(d,{span:24},{default:_((()=>[c(i,{label:"备注",prop:"notes"},{default:_((()=>[c(r,{modelValue:H.value.notes,"onUpdate:modelValue":a[10]||(a[10]=e=>H.value.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),c(l,null,{header:_((()=>a[14]||(a[14]=[p("div",{class:"card-header"},[p("h3",null,"商品信息")],-1)]))),default:_((()=>[c(U,{data:H.value.products,border:"",style:{width:"100%"}},{default:_((()=>[c($,{label:"序号",type:"index",width:"40",align:"center"}),c($,{prop:"product_name",label:"产品名称","min-width":"120"}),c($,{prop:"product_model",label:"型号",width:"80"}),c($,{label:"规格",width:"80"},{default:_((({row:e})=>[v(m(e.specification_description||e.specification&&e.specification.specification||""),1)])),_:1}),c($,{label:"单位",width:"60"},{default:_((({row:e})=>[v(m(e.product_unit||e.unit||"个"),1)])),_:1}),c($,{label:"订单数量",width:"80"},{default:_((({row:e})=>[v(m(e.order_quantity),1)])),_:1}),c($,{label:"已发货",width:"70"},{default:_((({row:e})=>[v(m(e.delivered_quantity||e.shipped_quantity||0),1)])),_:1}),c($,{label:"待发货",width:"70"},{default:_((({row:e})=>[v(m(Q(e)),1)])),_:1}),c($,{prop:"quantity",label:"本次发货",width:"120"},{default:_((({row:e})=>[c(A,{modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,min:0,precision:0,"controls-position":"right",size:"small",style:{width:"100%"},onChange:a=>X(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),c($,{prop:"notes",label:"备注","min-width":"150"},{default:_((({row:e})=>[c(r,{modelValue:e.notes,"onUpdate:modelValue":a=>e.notes=a,size:"small",placeholder:"输入备注"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),c($,{label:"操作",width:"120"},{default:_((({$index:e,row:l})=>[p("div",E,[c(t,{link:"",type:"primary",size:"small",onClick:e=>X(l)},{default:_((()=>a[15]||(a[15]=[v(" 检查数量 ")]))),_:2},1032,["onClick"]),F.value?b("",!0):(n(),f(t,{key:0,link:"",type:"danger",size:"small",onClick:a=>{return t=e,void H.value.products.splice(t,1);var t}},{default:_((()=>a[16]||(a[16]=[v(" 移除 ")]))),_:2},1032,["onClick"]))])])),_:1})])),_:1},8,["data"]),L.value?(n(),s("div",I,[c(x,{title:"提示：部分产品本次发货数量超过订单未发货数量，系统将自动更新订单对应产品数量",type:"warning","show-icon":"",closable:!1})])):b("",!0)])),_:1})])),_:1},8,["model"])),[[Y,z.value]]),p("div",M,[c(t,{type:"primary",onClick:re,loading:O.value},{default:_((()=>a[17]||(a[17]=[v("保存")]))),_:1},8,["loading"]),c(t,{onClick:le},{default:_((()=>a[18]||(a[18]=[v("取消")]))),_:1})])])}}}),[["__scopeId","data-v-e2ee797f"]]);export{Y as default};
