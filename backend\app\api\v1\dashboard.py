"""
仪表盘API模块
提供系统统计数据、待处理业务、销售分析等仪表盘功能
"""
from flask import request
from flask_restx import Resource, fields
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta
import calendar
import os

from flask_restx import Namespace
from app.models.customer import Customer
from app.models.product import Product, ProductCategory
from app.models.quotation import QuotationRequest, Quotation
from app.models.order import Order, OrderProduct
from app.models.finance import PaymentRecord
from app.utils.response import success_response, error_response
from app import db

# 创建仪表盘命名空间
api = Namespace('dashboard', description='仪表盘数据接口')

# 统计数据响应模型
stats_model = api.model('DashboardStats', {
    'customers_total': fields.Integer(description='客户总数'),
    'products_total': fields.Integer(description='产品总数'),
    'quotations_total': fields.Integer(description='报价单总数'),
    'orders_total': fields.Integer(description='订单总数'),
    'orders_pending': fields.Integer(description='待处理订单数'),
    'orders_completed': fields.Integer(description='已完成订单数'),
    'revenue_total': fields.Float(description='总收入'),
    'revenue_month': fields.Float(description='本月收入'),
    'last_updated': fields.String(description='最后更新时间')
})

# 待处理报价单模型
pending_quotation_model = api.model('PendingQuotation', {
    'id': fields.Integer(description='报价单ID'),
    'quotation_number': fields.String(description='报价单号'),
    'customer_name': fields.String(description='客户名称'),
    'project_name': fields.String(description='项目名称'),
    'total_amount': fields.Float(description='总金额'),
    'created_at': fields.String(description='创建时间'),
    'status': fields.String(description='状态')
})

# 待发货订单模型
pending_order_model = api.model('PendingOrder', {
    'id': fields.Integer(description='订单ID'),
    'order_number': fields.String(description='订单号'),
    'customer_name': fields.String(description='客户名称'),
    'project_name': fields.String(description='项目名称'),
    'total_amount': fields.Float(description='总金额'),
    'order_date': fields.String(description='订单日期'),
    'status': fields.String(description='状态')
})

# 销售趋势模型
sales_trend_model = api.model('SalesTrend', {
    'date': fields.String(description='日期'),
    'amount': fields.Float(description='销售金额'),
    'orders_count': fields.Integer(description='订单数量')
})

# 产品销售占比模型
product_sales_model = api.model('ProductSales', {
    'category_name': fields.String(description='产品分类名称'),
    'sales_amount': fields.Float(description='销售金额'),
    'percentage': fields.Float(description='占比百分比'),
    'orders_count': fields.Integer(description='订单数量')
})

# 天气信息模型
weather_model = api.model('Weather', {
    'city': fields.String(description='城市'),
    'temperature': fields.String(description='温度'),
    'weather': fields.String(description='天气状况'),
    'humidity': fields.String(description='湿度'),
    'wind': fields.String(description='风力'),
    'update_time': fields.String(description='更新时间')
})

# 用户信息模型
user_info_model = api.model('UserInfo', {
    'username': fields.String(description='用户名'),
    'role': fields.String(description='角色'),
    'last_login': fields.String(description='最后登录时间'),
    'login_count': fields.Integer(description='登录次数'),
    'permissions': fields.List(fields.String, description='权限列表')
})


@api.route('/stats')
class DashboardStats(Resource):
    @api.doc('get_dashboard_stats')
    def get(self):
        """获取工作台统计数据"""
        try:
            # 获取基础统计数据
            customers_total = Customer.query.count()
            products_total = Product.query.count()
            quotations_total = Quotation.query.count()
            orders_total = Order.query.count()
            
            # 获取订单状态统计（使用order_status字段）
            orders_pending = Order.query.filter(
                Order.order_status.in_(['待确认', '已确认', '生产中', '待发货'])
            ).count()

            orders_completed = Order.query.filter(
                Order.order_status == '已完成'
            ).count()
            
            # 计算总收入（基于已完成订单）
            revenue_total_result = db.session.query(
                func.sum(Order.total_amount)
            ).filter(Order.order_status == '已完成').scalar()
            revenue_total = float(revenue_total_result or 0)

            # 计算本月收入
            current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            revenue_month_result = db.session.query(
                func.sum(Order.total_amount)
            ).filter(
                and_(
                    Order.order_status == '已完成',
                    Order.created_at >= current_month_start
                )
            ).scalar()
            revenue_month = float(revenue_month_result or 0)
            
            stats_data = {
                'customers_total': customers_total,
                'products_total': products_total,
                'quotations_total': quotations_total,
                'orders_total': orders_total,
                'orders_pending': orders_pending,
                'orders_completed': orders_completed,
                'revenue_total': revenue_total,
                'revenue_month': revenue_month,
                'last_updated': datetime.now().isoformat()
            }
            
            return success_response(data=stats_data, message='获取统计数据成功')
            
        except Exception as e:
            return error_response(message=f'获取统计数据失败: {str(e)}', code=500)


@api.route('/pending_quotations')
class PendingQuotations(Resource):
    @api.doc('get_pending_quotations')
    def get(self):
        """获取待处理报价单"""
        try:
            # 获取查询参数
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 10, type=int), 100)
            
            # 查询待处理的报价单
            pending_quotations = Quotation.query.filter(
                Quotation.status.in_(['草稿', '已提交'])
            ).order_by(desc(Quotation.created_at)).paginate(
                page=page, per_page=per_page, error_out=False
            )
            
            quotations_data = []
            for quotation in pending_quotations.items:
                quotations_data.append({
                    'id': quotation.id,
                    'quotation_number': quotation.quotation_number,
                    'customer_name': quotation.customer.name if quotation.customer else '',
                    'project_name': quotation.project_name or '',
                    'total_amount': float(quotation.total_amount or 0),
                    'created_at': quotation.created_at.isoformat() if quotation.created_at else '',
                    'status': quotation.status or '草稿'
                })
            
            return success_response(
                data={
                    'items': quotations_data,
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': pending_quotations.total,
                        'pages': pending_quotations.pages
                    }
                },
                message='获取待处理报价单成功'
            )
            
        except Exception as e:
            return error_response(message=f'获取待处理报价单失败: {str(e)}', code=500)


@api.route('/pending_orders')
class PendingOrders(Resource):
    @api.doc('get_pending_orders')
    def get(self):
        """获取待发货订单"""
        try:
            # 获取查询参数
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 10, type=int), 100)
            
            # 查询待发货的订单
            pending_orders = Order.query.filter(
                Order.order_status.in_(['已确认', '生产中', '待发货'])
            ).order_by(desc(Order.created_at)).paginate(
                page=page, per_page=per_page, error_out=False
            )

            orders_data = []
            for order in pending_orders.items:
                orders_data.append({
                    'id': order.id,
                    'order_number': order.order_number,
                    'customer_name': order.customer.name if order.customer else '',
                    'project_name': order.project_name or '',
                    'total_amount': float(order.total_amount or 0),
                    'order_date': order.created_at.isoformat() if order.created_at else '',
                    'status': order.order_status or '待确认'
                })
            
            return success_response(
                data={
                    'items': orders_data,
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': pending_orders.total,
                        'pages': pending_orders.pages
                    }
                },
                message='获取待发货订单成功'
            )
            
        except Exception as e:
            return error_response(message=f'获取待发货订单失败: {str(e)}', code=500)


@api.route('/sales_trends')
class SalesTrends(Resource):
    @api.doc('get_sales_trends')
    def get(self):
        """获取销售趋势数据"""
        try:
            # 获取查询参数
            days = request.args.get('days', 30, type=int)  # 默认30天
            days = min(days, 365)  # 最多一年

            # 计算日期范围
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)

            # 查询销售数据
            sales_data = db.session.query(
                func.date(Order.created_at).label('date'),
                func.sum(Order.total_amount).label('amount'),
                func.count(Order.id).label('orders_count')
            ).filter(
                and_(
                    func.date(Order.created_at) >= start_date,
                    func.date(Order.created_at) <= end_date,
                    Order.order_status.in_(['已确认', '生产中', '待发货', '发货中', '已发货', '已完成'])
                )
            ).group_by(func.date(Order.created_at)).order_by(func.date(Order.created_at)).all()

            # 构建完整的日期序列
            trends_data = []
            current_date = start_date
            sales_dict = {item.date: item for item in sales_data}

            while current_date <= end_date:
                if current_date in sales_dict:
                    item = sales_dict[current_date]
                    trends_data.append({
                        'date': current_date.isoformat(),
                        'amount': float(item.amount or 0),
                        'orders_count': item.orders_count or 0
                    })
                else:
                    trends_data.append({
                        'date': current_date.isoformat(),
                        'amount': 0.0,
                        'orders_count': 0
                    })
                current_date += timedelta(days=1)

            return success_response(
                data=trends_data,
                message='获取销售趋势成功'
            )

        except Exception as e:
            return error_response(message=f'获取销售趋势失败: {str(e)}', code=500)


@api.route('/product_sales')
class ProductSales(Resource):
    @api.doc('get_product_sales')
    def get(self):
        """获取产品销售占比"""
        try:
            # 获取查询参数
            days = request.args.get('days', 30, type=int)  # 默认30天
            days = min(days, 365)  # 最多一年

            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # 查询产品分类销售数据
            from app.models.product import ProductSpecification
            category_sales = db.session.query(
                ProductCategory.name.label('category_name'),
                func.sum(OrderProduct.total_price).label('sales_amount'),
                func.count(OrderProduct.id).label('orders_count')
            ).join(
                ProductSpecification, ProductSpecification.id == OrderProduct.product_specification_id
            ).join(
                Product, Product.id == ProductSpecification.product_id
            ).join(
                ProductCategory, ProductCategory.id == Product.category_id
            ).join(
                Order, Order.id == OrderProduct.order_id
            ).filter(
                and_(
                    Order.created_at >= start_date,
                    Order.created_at <= end_date,
                    Order.order_status.in_(['已确认', '生产中', '待发货', '发货中', '已发货', '已完成'])
                )
            ).group_by(ProductCategory.id, ProductCategory.name).all()

            # 计算总销售额
            total_sales = sum(item.sales_amount or 0 for item in category_sales)

            # 构建响应数据
            sales_data = []
            for item in category_sales:
                sales_amount = float(item.sales_amount or 0)
                percentage = (sales_amount / total_sales * 100) if total_sales > 0 else 0

                sales_data.append({
                    'category_name': item.category_name or '未分类',
                    'sales_amount': sales_amount,
                    'percentage': round(percentage, 2),
                    'orders_count': item.orders_count or 0
                })

            # 按销售额排序
            sales_data.sort(key=lambda x: x['sales_amount'], reverse=True)

            return success_response(
                data=sales_data,
                message='获取产品销售占比成功'
            )

        except Exception as e:
            return error_response(message=f'获取产品销售占比失败: {str(e)}', code=500)


@api.route('/weather')
class Weather(Resource):
    @api.doc('get_weather')
    def get(self):
        """获取天气信息"""
        try:
            # 获取城市参数，默认为北京
            city = request.args.get('city', '北京')

            # 这里可以集成第三方天气API，如和风天气、心知天气等
            # 为了演示，这里返回模拟数据
            weather_data = {
                'city': city,
                'temperature': '22°C',
                'weather': '晴',
                'humidity': '45%',
                'wind': '东南风 2级',
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # 如果配置了天气API，可以使用以下代码获取真实天气数据
            # weather_api_key = os.getenv('WEATHER_API_KEY')
            # if weather_api_key:
            #     try:
            #         # 示例：使用和风天气API
            #         url = f"https://devapi.qweather.com/v7/weather/now"
            #         params = {
            #             'location': city,
            #             'key': weather_api_key
            #         }
            #         response = requests.get(url, params=params, timeout=5)
            #         if response.status_code == 200:
            #             data = response.json()
            #             if data.get('code') == '200':
            #                 now = data.get('now', {})
            #                 weather_data = {
            #                     'city': city,
            #                     'temperature': f"{now.get('temp', '--')}°C",
            #                     'weather': now.get('text', '--'),
            #                     'humidity': f"{now.get('humidity', '--')}%",
            #                     'wind': f"{now.get('windDir', '--')} {now.get('windScale', '--')}级",
            #                     'update_time': now.get('obsTime', datetime.now().isoformat())
            #                 }
            #     except Exception as e:
            #         # 如果API调用失败，使用默认数据
            #         pass

            return success_response(
                data=weather_data,
                message='获取天气信息成功'
            )

        except Exception as e:
            return error_response(message=f'获取天气信息失败: {str(e)}', code=500)


@api.route('/user_info')
class UserInfo(Resource):
    @api.doc('get_user_info')
    def get(self):
        """获取当前用户信息"""
        try:
            # 这里应该从session或JWT token中获取用户信息
            # 为了演示，返回模拟数据
            user_data = {
                'username': '管理员',
                'role': '系统管理员',
                'last_login': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'login_count': 156,
                'permissions': [
                    'dashboard:view',
                    'customer:manage',
                    'product:manage',
                    'quotation:manage',
                    'order:manage',
                    'finance:manage',
                    'system:manage'
                ]
            }

            # 如果有用户认证系统，可以使用以下代码获取真实用户信息
            # from flask_login import current_user
            # if current_user.is_authenticated:
            #     user_data = {
            #         'username': current_user.username,
            #         'role': current_user.role.name if current_user.role else '普通用户',
            #         'last_login': current_user.last_login.isoformat() if current_user.last_login else None,
            #         'login_count': current_user.login_count or 0,
            #         'permissions': [perm.name for perm in current_user.permissions] if hasattr(current_user, 'permissions') else []
            #     }

            return success_response(
                data=user_data,
                message='获取用户信息成功'
            )

        except Exception as e:
            return error_response(message=f'获取用户信息失败: {str(e)}', code=500)
