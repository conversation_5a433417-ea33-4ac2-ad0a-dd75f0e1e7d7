<template>
  <div class="payment-record-edit">
    <el-card class="header-card mb-20">
      <div class="card-header">
        <div class="left">
          <h2 class="page-title">{{ isEdit ? '编辑收款记录' : '新增收款记录' }}</h2>
          <el-tag type="info">{{ isEdit ? '修改已有收款记录信息' : '创建新的收款记录' }}</el-tag>
        </div>
        <div class="right">
          <el-button @click="goBack">
            <el-icon><Back /></el-icon> 返回列表
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 编辑表单 -->
    <el-card v-loading="loading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="payment-form"
        status-icon
      >
        <!-- 基本信息部分 -->
        <div class="form-section-title">基本信息</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="收款单号" prop="paymentNo">
              <el-input 
                v-model="formData.paymentNo" 
                placeholder="系统自动生成" 
                disabled
                v-if="isEdit"
              />
              <el-input 
                v-model="formData.paymentNo" 
                placeholder="系统自动生成，可手动修改" 
                v-else
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款日期" prop="paymentDate">
              <el-date-picker
                v-model="formData.paymentDate"
                type="date"
                placeholder="选择收款日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联订单" prop="orderId">
              <el-select
                v-model="formData.orderId"
                placeholder="选择关联订单"
                filterable
                remote
                :remote-method="searchOrders"
                :loading="orderLoading"
                style="width: 100%"
                @change="handleOrderChange"
                :reserve-keyword="false"
                clearable
                @visible-change="handleSelectVisible"
              >
                <el-option
                  v-for="item in orderOptions"
                  :key="item.id"
                  :label="`${item.orderNo} - ${item.customerName}`"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-input v-model="formData.customerName" placeholder="选择订单后自动填充" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="收款方式" prop="paymentMethod">
              <el-select v-model="formData.paymentMethod" placeholder="选择收款方式" style="width: 100%">
                <el-option label="银行转账" value="bank_transfer" />
                <el-option label="现金" value="cash" />
                <el-option label="在线支付" value="online_payment" />
                <el-option label="支票" value="check" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款金额" prop="amount">
              <el-input-number
                v-model="formData.amount"
                :precision="2"
                :step="100"
                :min="0"
                style="width: 100%"
                placeholder="输入收款金额"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="formData.paymentMethod === 'bank_transfer'">
          <el-col :span="24">
            <el-form-item label="收款账户" prop="bankAccount">
              <template v-if="isView">
                <el-input :model-value="formData.bankAccountDisplay || formData.bankAccount" disabled />
              </template>
              <el-select
                v-else
                v-model="formData.bankAccount"
                placeholder="选择公司收款账户"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in bankAccountOptions"
                  :key="item.id"
                  :label="`${item.bankName} - ${item.accountNumber} (${item.accountName})`"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 订单信息部分 -->
        <div class="form-section-title" v-if="formData.orderId">订单信息</div>
        
        <el-row :gutter="20" v-if="formData.orderId">
          <el-col :span="24">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="订单编号">{{ orderInfo.orderNo }}</el-descriptions-item>
              <el-descriptions-item label="订单金额">{{ formatCurrency(orderInfo.totalAmount) }}</el-descriptions-item>
              <el-descriptions-item label="创建日期">{{ formatDate(orderInfo.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="订单状态">
                <el-tag :type="getOrderStatusType(orderInfo.status)">{{ orderInfo.statusText }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="已收款">{{ formatCurrency(orderInfo.paidAmount) }}</el-descriptions-item>
              <el-descriptions-item label="未收款">{{ formatCurrency(orderInfo.totalAmount - orderInfo.paidAmount) }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 收款记录部分 -->
        <div class="form-section-title" v-if="formData.orderId && paymentHistoryList.length > 0">收款记录历史</div>
        
        <el-row :gutter="20" v-if="formData.orderId && paymentHistoryList.length > 0">
          <el-col :span="24">
            <el-table
              :data="paymentHistoryList"
              border
              stripe
              style="width: 100%"
            >
              <el-table-column prop="paymentNo" label="收款单号" min-width="120" />
              <el-table-column prop="amount" label="收款金额" width="120">
                <template #default="scope">
                  {{ formatCurrency(scope.row.amount) }}
                </template>
              </el-table-column>
              <el-table-column prop="paymentMethod" label="收款方式" width="120">
                <template #default="scope">
                  {{ formatPaymentMethod(scope.row.paymentMethod) }}
                </template>
              </el-table-column>
              <el-table-column prop="paymentDate" label="收款日期" width="120" />
              <el-table-column prop="createBy" label="录入人" width="100" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ formatStatus(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>

        <!-- 其他信息部分 -->
        <div class="form-section-title">附加信息</div>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="交易凭证" prop="transactionProof">
              <el-upload
                class="transaction-proof-upload"
                action=""
                :http-request="handleFileUpload"
                :file-list="fileList"
                :limit="5"
                multiple
              >
                <el-button type="primary">上传凭证</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持jpg、png、pdf格式，单个文件不超过10MB</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注说明" prop="notes">
              <el-input
                v-model="formData.notes"
                type="textarea"
                :rows="3"
                placeholder="请输入收款相关的补充说明"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 表单操作按钮 -->
        <el-form-item class="form-buttons">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">保存</el-button>
          <el-button type="success" @click="handleSaveAndConfirm" :loading="saveLoading">保存并确认收款</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Back, Upload, Delete } from '@element-plus/icons-vue';
import { orderApi } from '@/api/order';
import { paymentRecordApi, bankAccountApi } from '@/api/finance';

const route = useRoute();
const router = useRouter();
const formRef = ref(null);
const loading = ref(false);
const saveLoading = ref(false);
const orderLoading = ref(false);

// 是否是编辑模式
const isEdit = computed(() => {
  return !!route.params.id;
});

// 是否是查看模式
const isView = computed(() => {
  return route.meta?.isView === true;
});

// 表单数据
const formData = reactive({
  id: '',
  paymentNo: '',
  orderId: '',
  orderNo: '',
  customerId: '',
  customerName: '',
  paymentDate: new Date().toISOString().split('T')[0],
  paymentMethod: '',
  amount: 0,
  bankAccount: '',
  bankAccountDisplay: '',
  notes: '',
  status: 'pending',
  transactionProof: []
});

// 表单验证规则
const rules = {
  orderId: [
    { required: true, message: '请选择关联订单', trigger: 'change' }
  ],
  paymentDate: [
    { required: true, message: '请选择收款日期', trigger: 'change' }
  ],
  paymentMethod: [
    { required: true, message: '请选择收款方式', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入收款金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '收款金额必须大于0', trigger: 'blur' }
  ],
  bankAccount: [
    { required: true, message: '请选择收款账户', trigger: 'change' }
  ]
};

// 订单选项
const orderOptions = ref([]);

// 银行账户选项
const bankAccountOptions = ref([]);

// 订单信息
const orderInfo = reactive({
  orderNo: '',
  totalAmount: 0,
  paidAmount: 0,
  unpaidAmount: 0,
  status: '',
  statusText: '',
  createTime: ''
});

// 收款记录历史
const paymentHistoryList = ref([]);

// 文件列表
const fileList = ref([]);

// 加载银行账户选项
const loadBankAccounts = async () => {
  try {
    const response = await bankAccountApi.getList() as any;
    console.log('银行账户响应:', response);
    
    // 处理不同的响应格式
    let accounts = [];
    if (Array.isArray(response)) {
      accounts = response;
    } else if (response && typeof response === 'object') {
      if (response.data && Array.isArray(response.data)) {
        accounts = response.data;
      } else if (response.items && Array.isArray(response.items)) {
        accounts = response.items;
      }
    }
    
    // 转换为下拉选项格式
    bankAccountOptions.value = accounts.map((account: any) => ({
      id: account.id,
      bankName: account.bank_name,
      accountName: account.account_name,
      accountNumber: account.account_number,
      isDefault: account.is_default
    }));
    
    // 如果有默认账户，自动选择
    const defaultAccount = bankAccountOptions.value.find((account: any) => account.isDefault);
    if (defaultAccount && formData.paymentMethod === 'bank_transfer' && !formData.bankAccount) {
      formData.bankAccount = defaultAccount.id;
    }
  } catch (error) {
    console.error('获取银行账户失败:', error);
    ElMessage.error('获取银行账户失败');
  }
};

// 获取收款记录详情
const getPaymentDetail = async (id: number) => {
  loading.value = true;
  try {
    console.log('获取收款记录详情，ID:', id);
    const response = await paymentRecordApi.getById(id) as any;
    console.log('收款记录详情响应:', response);
    
    if (!response || !response.data) {
      ElMessage.error('获取收款记录详情失败');
      return;
    }
    
    const paymentDetail = response.data;
    console.log('收款记录详情数据:', paymentDetail);
    
    // 填充表单数据
    formData.id = paymentDetail.id;
    formData.paymentNo = paymentDetail.payment_number || `SK${paymentDetail.id}`;
    formData.paymentDate = paymentDetail.payment_date;
    formData.paymentMethod = paymentDetail.payment_method;
    formData.amount = parseFloat(paymentDetail.amount) || 0;
    formData.notes = paymentDetail.notes;
    formData.status = paymentDetail.status;
    formData.orderId = paymentDetail.order_id;
    formData.customerName = paymentDetail.customer_name || '';
    formData.customerId = paymentDetail.customer_id;
    
    // 处理银行账户信息
    if (paymentDetail.payment_method === 'bank_transfer') {
      // 如果后端返回了格式化的银行账户信息
      if (paymentDetail.bank_account_info) {
        console.log('使用后端格式化的银行账户信息:', paymentDetail.bank_account_info);
        // 在UI上显示格式化的银行账户信息，但在表单数据中保留原始ID
        formData.bankAccountDisplay = paymentDetail.bank_account_info;
        formData.bankAccount = paymentDetail.bank_account;
      } else {
        // 否则使用原始bank_account
        formData.bankAccount = paymentDetail.bank_account;
        console.log('使用原始银行账户信息:', paymentDetail.bank_account);
      }
    }
    
    console.log('填充后的表单数据:', formData);
    
    // 如果API返回了订单金额信息，直接使用
    if (paymentDetail.order_id) {
      // 更新订单信息
      orderInfo.orderNo = paymentDetail.order_number || '';
      orderInfo.totalAmount = parseFloat(paymentDetail.total_amount) || 0;
      orderInfo.paidAmount = parseFloat(paymentDetail.paid_amount) || 0;
      orderInfo.unpaidAmount = parseFloat(paymentDetail.total_amount) - parseFloat(paymentDetail.paid_amount) || 0;
      orderInfo.status = paymentDetail.status || '';
      orderInfo.statusText = paymentDetail.status || '';
      orderInfo.createTime = paymentDetail.created_at || '';
      
      console.log('从收款记录中获取的订单信息:', orderInfo);
      
      // 获取收款历史
      await getPaymentHistory(paymentDetail.order_id);
    } else {
      console.warn('收款记录中没有关联订单ID');
    }
    
  } catch (error) {
    console.error('获取收款记录详情失败:', error);
    ElMessage.error('获取收款记录详情失败');
  } finally {
    loading.value = false;
  }
};

// 搜索订单
const searchOrders = async (query: string) => {
  orderLoading.value = true;
  try {
    // 构建查询参数
    const params: any = {
      status: 'confirmed,delivered,completed', // 只获取可收款的订单状态
      page: 1,
      per_page: 20
    };
    
    // 如果有查询关键词，添加到参数中
    if (query) {
      params.order_number = query;
    }
    
    console.log('搜索订单参数:', params);
    const response = await orderApi.getList(params) as any;
    console.log('搜索订单响应:', response);
    
    let items = [];
    if (Array.isArray(response)) {
      items = response;
    } else if (response && response.data) {
      if (Array.isArray(response.data)) {
        items = response.data;
      } else if (response.data.items && Array.isArray(response.data.items)) {
        items = response.data.items;
      }
    }
    
    orderOptions.value = items.map((item: any) => ({
      id: item.id,
      orderNo: item.order_number,
      customerName: item.customer?.name || item.customer_name || '',
      totalAmount: item.total_amount,
      paidAmount: item.paid_amount || 0,
      unpaidAmount: (item.total_amount || 0) - (item.paid_amount || 0)
    }));
    
    console.log('订单选项已更新，数量:', orderOptions.value.length);
  } catch (error) {
    console.error('搜索订单失败:', error);
    ElMessage.error('搜索订单失败');
  } finally {
    orderLoading.value = false;
  }
};

// 处理订单选择变化
const handleOrderChange = async (orderId: number) => {
  if (orderId) {
    try {
      const response = await orderApi.getById(orderId) as any;
      const order = response.data || response;
      
      // 自动填充客户信息
      formData.customerName = order.customer?.name || order.customer_name || '';
      formData.customerId = order.customer_id;
      
      // 更新订单信息
      orderInfo.orderNo = order.order_number;
      orderInfo.totalAmount = order.total_amount || 0;
      orderInfo.paidAmount = order.paid_amount || 0;
      orderInfo.unpaidAmount = (order.total_amount || 0) - (order.paid_amount || 0);
      orderInfo.status = order.order_status;
      orderInfo.statusText = order.order_status;
      orderInfo.createTime = order.created_at;
      
      // 获取收款历史
      await getPaymentHistory(orderId);
    } catch (error) {
      console.error('获取订单详情失败:', error);
    }
  }
};

// 获取收款历史
const getPaymentHistory = async (orderId: number) => {
  try {
    // 这里应该调用获取订单收款历史的API
    // const response = await getOrderPaymentHistory(orderId);
    // paymentHistoryList.value = response.data || [];
    
    // 暂时使用模拟数据
    paymentHistoryList.value = [];
  } catch (error) {
    console.error('获取收款历史失败:', error);
  }
};

// 处理下拉框显示变化
const handleSelectVisible = (visible: boolean) => {
  if (visible && orderOptions.value.length === 0) {
    searchOrders('');
  }
};

// 文件上传处理
const handleFileUpload = (options: any) => {
  // 处理文件上传逻辑
  console.log('文件上传:', options);
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 保存
const handleSave = async () => {
  try {
    await formRef.value?.validate();
    
    saveLoading.value = true;
    
    const submitData = {
      ...formData,
      order_id: formData.orderId,
      payment_date: formData.paymentDate,
      payment_method: formData.paymentMethod,
      bank_account: formData.bankAccount,
      status: 'pending'
    };
    
    if (isEdit.value) {
      await paymentRecordApi.update(Number(route.params.id), submitData);
      ElMessage.success('更新收款记录成功');
    } else {
      await paymentRecordApi.create(submitData);
      ElMessage.success('创建收款记录成功');
    }
    
    router.push('/finance/payment-records');
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  } finally {
    saveLoading.value = false;
  }
};

// 保存并确认
const handleSaveAndConfirm = async () => {
  try {
    await formRef.value?.validate();
    
    saveLoading.value = true;
    
    const submitData = {
      ...formData,
      order_id: formData.orderId,
      payment_date: formData.paymentDate,
      payment_method: formData.paymentMethod,
      bank_account: formData.bankAccount,
      status: 'confirmed'
    };
    
    if (isEdit.value) {
      await paymentRecordApi.update(Number(route.params.id), submitData);
      ElMessage.success('更新并确认收款记录成功');
    } else {
      await paymentRecordApi.create(submitData);
      ElMessage.success('创建并确认收款记录成功');
    }
    
    router.push('/finance/payment-records');
  } catch (error) {
    console.error('保存并确认失败:', error);
    ElMessage.error('保存并确认失败');
  } finally {
    saveLoading.value = false;
  }
};

// 格式化货币
const formatCurrency = (value: number) => {
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
  } catch (error) {
    return dateStr;
  }
};

// 格式化收款方式
const formatPaymentMethod = (method: string) => {
  const methodMap: Record<string, string> = {
    'bank_transfer': '银行转账',
    'cash': '现金',
    'check': '支票',
    'electronic': '电子支付',
    'online_payment': '在线支付',
    'other': '其他'
  };
  return methodMap[method] || method;
};

// 格式化状态
const formatStatus = (status: string) => {
  return status;
};

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'warning',
    'confirmed': 'success',
    'cancelled': 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取订单状态类型
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'warning',
    'confirmed': 'success',
    'delivered': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  };
  return statusMap[status] || 'info';
};

onMounted(() => {
  loadBankAccounts();
  
  if (isEdit.value) {
    getPaymentDetail(Number(route.params.id));
  } else {
    // 检查是否从订单页面跳转过来
    const orderId = route.query.order_id;
    if (orderId) {
      formData.orderId = Number(orderId);
      handleOrderChange(Number(orderId));
    }
  }
});
</script>

<style lang="scss" scoped>
.payment-record-edit {
  .header-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .payment-form {
    .form-section-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin: 20px 0 15px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #EBEEF5;
    }
    
    .form-buttons {
      margin-top: 30px;
      text-align: center;
    }
    
    .transaction-proof-upload {
      .el-upload__tip {
        margin-top: 5px;
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>
