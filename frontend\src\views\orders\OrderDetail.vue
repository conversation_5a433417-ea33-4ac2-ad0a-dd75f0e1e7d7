<template>
  <div v-if="isOrderLoaded" class="order-detail">
    <!-- 头部区域 -->
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="form-title">订单详情</h2>
        <div>
          <el-button @click="goBack">返回</el-button>
          <el-button
            v-if="canEdit"
            type="primary"
            @click="handleEdit"
          >编辑</el-button>
          <el-button type="success" @click="handleExport">导出订单</el-button>
          <!-- 状态回退按钮 - 根据需求调整显示逻辑 -->
          <!-- 已确认状态：不显示取消确认按钮 -->
          <!-- 生产中状态：不显示确认和开始生产按钮 -->
          <!-- 待发货状态：不显示开始生产和准备发货按钮 -->

          <!-- 原有的发货按钮 -->
          <el-button
            v-if="canCreateDeliveryNote"
            type="success"
            @click="handleCreateDelivery"
          >
            <el-icon><Van /></el-icon>
            发货
          </el-button>
          <el-button
            v-if="canCreateReturn"
            type="warning"
            @click="handleCreateReturn"
          >
            <el-icon><RefreshLeft /></el-icon>
            退货
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 双状态管理器 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>订单状态管理</span>
          <span class="status-tip">点击状态可直接切换</span>
        </div>
      </template>

      <el-row :gutter="20">
        <!-- 发货状态 -->
        <el-col :span="24">
          <div class="status-section">
            <h4 class="status-section-title">发货状态</h4>
            <div class="status-selector">
              <div class="status-grid">
                <div
                  v-for="status in availableOrderStatuses"
                  :key="status.value"
                  :class="[
                    'status-item',
                    { 'active': (orderInfo?.order_status || orderInfo?.status) === status.value },
                    { 'clickable': isStatusClickable(status.value) },
                    { 'flexible': isFlexibleStatus(status.value) },
                    { 'disabled': !isStatusAvailable(status.value) }
                  ]"
                  @click="isStatusClickable(status.value) ? handleOrderStatusChange(status.value) : null"
                  :title="getStatusTooltip(status.value)"
                >
                  <div class="status-icon">
                    <el-icon v-if="(orderInfo?.order_status || orderInfo?.status) === status.value">
                      <Check />
                    </el-icon>
                    <span v-else class="status-number">{{ status.step }}</span>
                  </div>
                  <div class="status-text">{{ status.label }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>


      </el-row>
    </el-card>



    <!-- 信息卡片 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :span="8">
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <span>基本信息</span>
            </div>
          </template>
          <div class="info-body">
            <div class="info-item">
              <span class="label">订单号:</span>
              <span class="value">{{ orderInfo?.order_number ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">发货状态:</span>
              <span class="value">
                <el-tag :type="getStatusType(orderInfo?.order_status || orderInfo?.status || '')">
                  {{ orderInfo?.order_status || orderInfo?.status || '未知' }}
                </el-tag>
              </span>
            </div>
            <div class="info-item">
              <span class="label">财务状态:</span>
              <span class="value">
                <el-tag :type="getPaymentStatusType(orderInfo?.payment_status || '')">
                  {{ orderInfo?.payment_status || '未知' }}
                </el-tag>
              </span>
            </div>
            <div class="info-item">
              <span class="label">订单日期:</span>
              <span class="value">{{ orderInfo?.created_at ? formatDate(orderInfo.created_at) : new Date().toLocaleDateString('zh-CN') }}</span>
            </div>
            <div class="info-item">
              <span class="label">预计交期:</span>
              <span class="value">{{ orderInfo?.expected_date ? formatDate(orderInfo.expected_date) : '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">关联报价单:</span>
              <span class="value">
                <template v-if="orderInfo?.quotations && orderInfo.quotations.length > 0">
                  <el-link
                    v-for="(quotation, index) in orderInfo.quotations"
                    :key="quotation.id"
                    type="primary"
                    @click="viewQuotation(quotation.id)"
                    class="quotation-link"
                  >
                    {{ quotation.quotation_number }}{{ index < orderInfo.quotations.length - 1 ? ', ' : '' }}
                  </el-link>
                </template>
                <span v-else>无</span>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <span>客户信息</span>
            </div>
          </template>
          <div class="info-body">
            <div class="info-item">
              <span class="label">客户名称:</span>
              <span class="value">{{ orderInfo?.customer?.name ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系人:</span>
              <span class="value">{{ orderInfo?.customer?.contact ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系电话:</span>
              <span class="value">{{ orderInfo?.customer?.phone ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">项目名称:</span>
              <span class="value">{{ orderInfo?.project_name ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">项目地址:</span>
              <span class="value">{{ orderInfo?.project_address ?? '' }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="info-card">
          <template #header>
            <div class="info-header">
              <span>商务信息</span>
            </div>
          </template>
          <div class="info-body">
            <div class="info-item">
              <span class="label">订单总额:</span>
              <span class="value amount">{{ formatCurrency(orderInfo?.total_amount ?? 0) }}</span>
            </div>
            <div class="info-item">
              <span class="label">已收款:</span>
              <span class="value">{{ formatCurrency(orderInfo?.paid_amount ?? 0) }}</span>
            </div>
            <div class="info-item">
              <span class="label">待收款:</span>
              <span class="value">{{ formatCurrency((orderInfo?.total_amount ?? 0) - (orderInfo?.paid_amount ?? 0)) }}</span>
            </div>
            <div class="info-item">
              <span class="label">付款条件:</span>
              <span class="value">{{ orderInfo?.payment_terms ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">交货条件:</span>
              <span class="value">{{ orderInfo?.delivery_terms ?? '' }}</span>
            </div>
            <div class="info-item">
              <span class="label">对账状态:</span>
              <div class="value statement-status-inline">
                <el-tag
                  :type="getStatementStatusType(currentStatementStatus)"
                  effect="dark"
                  size="small"
                >
                  {{ currentStatementStatus }}
                </el-tag>
                <el-button
                  v-if="currentStatementStatus !== '已结清'"
                  type="primary"
                  size="small"
                  plain
                  @click="handleForceSettle"
                  class="force-settle-btn"
                >
                  强制结清
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 发货单信息 -->
    <el-card v-if="orderInfo?.delivery_notes && orderInfo.delivery_notes.length > 0" class="mb-20">
      <template #header>
        <div class="card-header">
          <span>发货单信息</span>
          <span class="status-tip">共 {{ orderInfo.delivery_notes.length }} 个发货单</span>
        </div>
      </template>

      <el-table :data="orderInfo.delivery_notes" border stripe>
        <el-table-column prop="delivery_number" label="发货单号" width="180" />
        <el-table-column prop="delivery_date" label="发货日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.delivery_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getDeliveryStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="settlement_status" label="结清状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getSettlementStatusType(row.settlement_status)">
              {{ row.settlement_status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_amount" label="发货金额" width="120">
          <template #default="{ row }">
            {{ formatCurrency(row.total_amount || 0) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewDeliveryNote(row.id)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 退货单信息 -->
    <el-card v-if="orderInfo?.return_orders && orderInfo.return_orders.length > 0" class="mb-20">
      <template #header>
        <div class="card-header">
          <span>退货单信息</span>
          <span class="status-tip">共 {{ orderInfo.return_orders.length }} 个退货单</span>
        </div>
      </template>

      <el-table :data="orderInfo.return_orders" border stripe>
        <el-table-column prop="return_number" label="退货单号" width="180" />
        <el-table-column prop="return_date" label="退货日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.return_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getReturnStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="settlement_status" label="结清状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getSettlementStatusType(row.settlement_status)">
              {{ row.settlement_status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_amount" label="退货金额" width="120">
          <template #default="{ row }">
            {{ formatCurrency(row.total_amount || 0) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewReturnOrder(row.id)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 产品明细 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>产品明细</span>
        </div>
      </template>
      
      <el-table :data="orderInfo?.products || []" border stripe :row-class-name="getProductRowClassName">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="product_name" label="产品名称" min-width="150">
          <template #default="{ row }">
            <span :class="{ 'fully-delivered': isProductFullyDelivered(row) }">
              {{ row.product_name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="product_model" label="产品型号" width="120" />
        <el-table-column label="规格" width="120">
          <template #default="{ row }">
            {{ row.specification_description || row.product_specification?.specification || '' }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="100" />
        <el-table-column label="已发货" width="100">
          <template #default="{ row }">
            <span :class="{ 'fully-delivered-text': isProductFullyDelivered(row) }">
              {{ row.delivered_quantity || 0 }}
              <el-icon v-if="isProductFullyDelivered(row)" style="margin-left: 4px; color: #67c23a;">
                <Check />
              </el-icon>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="单位" width="80">
          <template #default="{ row }">
            {{ row.product_unit || row.product?.unit || '' }}
          </template>
        </el-table-column>
        <el-table-column label="单价" width="120">
          <template #default="{ row }">
            {{ formatCurrency(row.unit_price || 0) }}
          </template>
        </el-table-column>
        <el-table-column label="折扣(%)" width="100">
          <template #default="{ row }">
            {{ row.discount || 0 }}%
          </template>
        </el-table-column>
        <el-table-column label="税率(%)" width="100">
          <template #default="{ row }">
            {{ row.tax_rate || 0 }}%
          </template>
        </el-table-column>
        <el-table-column label="小计" width="120">
          <template #default="{ row }">
            {{ formatCurrency(row.total_price || 0) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 合计信息 -->
      <div class="total-section mt-20">
        <el-row :gutter="20">
          <el-col :span="18"></el-col>
          <el-col :span="6">
            <div class="total-info">
              <div class="total-row">
                <span>订单总额：</span>
                <span class="total-amount">{{ formatCurrency(orderInfo?.total_amount ?? 0) }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 备注信息 -->
    <el-card v-if="orderInfo?.notes" class="mb-20">
      <template #header>
        <div class="card-header">
          <span>备注信息</span>
        </div>
      </template>
      <p>{{ orderInfo.notes }}</p>
    </el-card>

    <!-- 导出对话框 -->
    <OrderExportDialog
      v-model="exportDialogVisible"
      :order-id="orderInfo?.id"
      :project-name="orderInfo?.project_name"
      @export-success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Van, RefreshLeft } from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import OrderExportDialog from '@/components/OrderExportDialog.vue'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const isOrderLoaded = ref(false)

// 导出相关状态
const exportDialogVisible = ref(false)

// 订单数据
const orderInfo = ref<any>({})

// 发货状态定义
const availableOrderStatuses = ref([
  { value: '待确认', label: '待确认', step: 1 },
  { value: '已确认', label: '已确认', step: 2 },
  { value: '生产中', label: '生产中', step: 3 },
  { value: '待发货', label: '待发货', step: 4 },
  { value: '部分发货', label: '部分发货', step: 5 },
  { value: '全部发货', label: '全部发货', step: 6 },
  { value: '已完成', label: '已完成', step: 7 },
  { value: '已取消', label: '已取消', step: 8 }
])

// 灵活状态定义（可以互相转换的状态）
const flexibleStatuses = ['待确认', '已确认', '生产中', '待发货']

// 判断状态是否为灵活状态
const isFlexibleStatus = (status: string) => {
  return flexibleStatuses.includes(status)
}

// 判断状态是否可点击
const isStatusClickable = (status: string) => {
  const currentStatus = orderInfo.value?.order_status
  if (status === currentStatus) {
    return false // 当前状态不可点击
  }

  // 使用后端返回的可转换状态信息
  const validTransitions = orderInfo.value?.status_info?.valid_order_transitions || []
  return validTransitions.includes(status)
}

// 判断状态是否可用（包括当前状态和可转换状态）
const isStatusAvailable = (status: string) => {
  const currentStatus = orderInfo.value?.order_status
  if (status === currentStatus) {
    return true // 当前状态总是可用的
  }

  // 使用后端返回的可转换状态信息
  const validTransitions = orderInfo.value?.status_info?.valid_order_transitions || []
  return validTransitions.includes(status)
}

// 获取状态提示信息
const getStatusTooltip = (status: string) => {
  const currentStatus = orderInfo.value?.order_status

  if (status === currentStatus) {
    return '当前状态'
  }

  if (isStatusClickable(status)) {
    return '点击切换到此状态'
  }

  if (isFlexibleStatus(status)) {
    return '前期状态，但当前不可切换'
  }

  return '此状态不可直接切换'
}



// 兼容性：保持原有状态定义
const availableStatuses = ref([
  { value: '待确认', label: '待确认', step: 1 },
  { value: '已确认', label: '已确认', step: 2 },
  { value: '生产中', label: '生产中', step: 3 },
  { value: '待发货', label: '待发货', step: 4 },
  { value: '部分发货', label: '部分发货', step: 5 },
  { value: '全部发货', label: '全部发货', step: 6 },
  { value: '已完成', label: '已完成', step: 7 }
])



// 计算属性
const canEdit = computed(() => {
  // 基于发货状态判断是否可编辑
  const orderStatus = orderInfo.value?.order_status || orderInfo.value?.status
  return ['待确认', '已确认', '生产中', '待发货'].includes(orderStatus)
})

// 实时计算对账状态
const currentStatementStatus = computed(() => {
  if (!orderInfo.value) return '未对账'

  return calculateStatementStatus(
    orderInfo.value.total_amount,
    orderInfo.value.paid_amount,
    orderInfo.value.manual_statement_status
  )
})

const canApprove = computed(() => {
  const orderStatus = orderInfo.value?.order_status || orderInfo.value?.status
  return orderStatus === '待确认'
})

const canCreateDeliveryNote = computed(() => {
  const orderStatus = orderInfo.value?.order_status || orderInfo.value?.status
  return ['已确认', '生产中', '待发货', '部分发货', '发货中'].includes(orderStatus)
})

const canCreateReturn = computed(() => {
  const orderStatus = orderInfo.value?.order_status || orderInfo.value?.status
  return ['部分发货', '全部发货', '已签收', '已完成'].includes(orderStatus)
})

// 获取状态步骤
const getStatusStep = (status: string) => {
  const statusMap: Record<string, number> = {
    'pending': 0,
    'confirmed': 1,
    'in_production': 2,
    'ready_to_ship': 3,
    'shipping': 4,
    'all_shipped': 5,
    'all_statement': 6,
    'completed': 7
  }
  return statusMap[status] || 0
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '待确认': 'warning',
    '已确认': 'success',
    '生产中': 'primary',
    '待发货': 'warning',
    '部分发货': 'primary',
    '全部发货': 'success',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  // 直接返回中文状态，因为后端已经返回中文
  return status || '未知状态'
}

// 计算对账状态
const calculateStatementStatus = (totalAmount: number, paidAmount: number, manualStatus?: string) => {
  // 如果手动设置为已结清，则优先使用手动状态
  if (manualStatus === '已结清') {
    return '已结清'
  }

  const total = Number(totalAmount) || 0
  const paid = Number(paidAmount) || 0

  if (paid === 0) {
    return '未对账'
  } else if (paid < total) {
    return '部分对账'
  } else if (paid >= total) {
    return '已对账'
  }

  return '未对账'
}

// 获取对账状态类型
const getStatementStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '未对账': 'info',
    '部分对账': 'warning',
    '已对账': 'success',
    '已结清': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取财务状态类型
const getPaymentStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '未收款': 'danger',
    '部分收款': 'warning',
    '已收款': 'success'
  }
  return typeMap[status] || 'info'
}

// 格式化货币
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('zh-CN', { 
    style: 'currency', 
    currency: 'CNY' 
  }).format(value)
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

// 返回
const goBack = () => {
  // 获取当前浏览器历史记录
  const historyLength = window.history.length
  
  // 如果历史记录长度小于等于1，直接返回列表页
  if (historyLength <= 1) {
    router.push('/orders')
    return
  }
  
  // 检查document.referrer来判断上一页是否是编辑页面
  const referrer = document.referrer
  const isFromEditPage = referrer && (
    referrer.includes('/orders/edit/') || 
    referrer.includes('/orders/new')
  )
  
  if (isFromEditPage) {
    // 如果来自编辑页面，返回到列表页
    router.push('/orders')
  } else {
    // 否则返回上一页
    router.back()
  }
}

// 编辑订单
const handleEdit = () => {
  router.push(`/orders/edit/${route.params.id}`)
}

// 确认订单
const handleConfirm = async () => {
  if (!orderInfo.value || orderInfo.value.order_status !== '待确认') {
    ElMessage.warning('只有待确认状态的订单可以进行确认')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确认后订单将进入已确认状态，是否继续？',
      '确认订单',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用API更新订单状态
    await orderApi.updateOrderStatus(orderInfo.value.id, '已确认')
    ElMessage.success('订单确认成功')

    // 刷新订单详情
    getOrderDetail()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '确认订单失败')
    }
  }
}

// 状态回退操作
const handleStatusRevert = async (targetStatus: string) => {
  const currentStatus = orderInfo.value?.order_status

  const statusMap: Record<string, string> = {
    '待确认': '取消确认',
    '已确认': '确认',
    '生产中': '开始生产',
    '待发货': '准备发货'
  }

  const actionName = statusMap[targetStatus] || '状态回退'

  try {
    await ElMessageBox.confirm(
      `确定要执行"${actionName}"操作，将状态从"${currentStatus}"变更为"${targetStatus}"吗？`,
      actionName,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用API更新订单状态
    await orderApi.updateOrderStatus(orderInfo.value.id, targetStatus)
    ElMessage.success(`${actionName}成功`)

    // 刷新订单详情
    await getOrderDetail()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || `${actionName}失败`)
    }
  }
}

// 导出订单
const handleExport = () => {
  exportDialogVisible.value = true
}

// 导出成功处理
const handleExportSuccess = () => {
  // 导出成功后的处理
}

// 创建发货单
const handleCreateDelivery = () => {
  const orderStatus = orderInfo.value?.order_status || orderInfo.value?.status
  const validStatuses = ['已确认', '生产中', '待发货', '部分发货', '发货中']

  if (!validStatuses.includes(orderStatus)) {
    ElMessage.warning(`当前状态不支持发货操作，当前状态: ${orderStatus}`)
    return
  }

  // 跳转到发货单创建页面，带上订单ID
  router.push({
    path: '/delivery-notes/new',
    query: { order_id: route.params.id }
  })
}

// 创建退货单
const handleCreateReturn = () => {
  const orderStatus = orderInfo.value?.order_status || orderInfo.value?.status
  const validStatuses = ['部分发货', '全部发货', '已签收', '已完成']

  if (!validStatuses.includes(orderStatus)) {
    ElMessage.warning(`当前状态不支持退货操作，当前状态: ${orderStatus}。\n说明：只有已发货的订单（有发货单记录）才能进行退货操作。`)
    return
  }

  // 跳转到退货单创建页面，带上订单信息
  router.push({
    path: '/returns/create',
    query: {
      orderId: route.params.id,
      orderNumber: orderInfo.value?.order_number,
      projectName: orderInfo.value?.project_name
    }
  })
}

// 查看报价单
const viewQuotation = (quotationId: number) => {
  if (quotationId) {
    router.push(`/quotations/view/${quotationId}`)
  }
}

// 处理状态变更(兼容方法)
const handleStatusChange = async (newStatus: string) => {
  if (newStatus === orderInfo.value?.status) {
    return // 相同状态不需要更新
  }

  try {
    await ElMessageBox.confirm(
      `确定要将订单状态更改为"${newStatus}"吗？`,
      '状态变更确认',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    loading.value = true

    // 调用后端API更新状态
    await orderApi.updateStatus(route.params.id as string, newStatus)

    // 更新本地状态
    orderInfo.value.status = newStatus
    orderInfo.value.order_status = newStatus // 同时更新发货状态

    ElMessage.success(`订单状态已更新为"${newStatus}"`)
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('更新订单状态失败:', error)
      ElMessage.error(error?.message || '更新订单状态失败')
    }
  } finally {
    loading.value = false
  }
}



// 处理发货状态变更
const handleOrderStatusChange = async (newOrderStatus: string) => {
  if (newOrderStatus === orderInfo.value?.order_status) {
    return // 相同状态不需要更新
  }

  // 检查状态转换是否允许
  const currentStatus = orderInfo.value?.order_status
  const flexibleStatuses = ['待确认', '已确认', '生产中', '待发货']
  const restrictedStatuses = ['部分发货', '全部发货', '已完成', '已取消']

  // 如果当前状态是受限状态，不允许转换到前面的状态
  if (restrictedStatuses.includes(currentStatus)) {
    const currentStep = availableOrderStatuses.value.find(s => s.value === currentStatus)?.step || 0
    const newStep = availableOrderStatuses.value.find(s => s.value === newOrderStatus)?.step || 0

    if (newStep < currentStep) {
      ElMessage.warning(`${currentStatus}状态不能回退到${newOrderStatus}状态`)
      return
    }
  }

  // 如果目标状态是受限状态，且当前状态不在灵活状态中，需要特殊处理
  if (restrictedStatuses.includes(newOrderStatus) && !flexibleStatuses.includes(currentStatus)) {
    ElMessage.warning(`当前状态不能直接切换到${newOrderStatus}状态`)
    return
  }

  try {
    let confirmMessage = `确定要将发货状态更改为"${newOrderStatus}"吗？`

    // 对于灵活状态之间的转换，给出特殊提示
    if (flexibleStatuses.includes(currentStatus) && flexibleStatuses.includes(newOrderStatus)) {
      confirmMessage = `前期状态可以灵活切换。确定要将发货状态更改为"${newOrderStatus}"吗？`
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '确认发货状态变更',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true

    // 调用后端API更新发货状态
    await orderApi.updateOrderStatus(route.params.id as string, newOrderStatus)

    // 更新本地状态
    orderInfo.value.order_status = newOrderStatus
    orderInfo.value.status = newOrderStatus // 保持兼容性

    ElMessage.success(`发货状态已更新为"${newOrderStatus}"`)

    // 刷新订单详情以获取最新数据
    await getOrderDetail()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('更新发货状态失败:', error)
      ElMessage.error(error?.message || '更新发货状态失败')
    }
  } finally {
    loading.value = false
  }
}



// 处理强制结清
const handleForceSettle = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要强制将对账状态设置为"已结清"吗？\n注意：这将忽略实际的收款金额。`,
      '强制结清确认',
      {
        type: 'warning',
        confirmButtonText: '确定强制结清',
        cancelButtonText: '取消'
      }
    )

    loading.value = true

    // 这里可以调用专门的对账状态更新API，暂时先更新本地状态
    // 如果后端有专门的对账状态API，可以替换为：
    // await orderApi.updateStatementStatus(route.params.id as string, '已结清')

    // 设置手动状态标记
    orderInfo.value.manual_statement_status = '已结清'

    ElMessage.success('对账状态已强制设置为"已结清"')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('强制结清失败:', error)
      ElMessage.error(error?.message || '强制结清失败')
    }
  } finally {
    loading.value = false
  }
}

// 获取订单详情
const getOrderDetail = async () => {
  try {
    loading.value = true
    const response = await orderApi.getById(Number(route.params.id))
    orderInfo.value = response

    // 根据金额自动计算对账状态（除非手动设置为已结清）
    const calculatedStatus = calculateStatementStatus(
      orderInfo.value.total_amount,
      orderInfo.value.paid_amount,
      orderInfo.value.manual_statement_status
    )
    orderInfo.value.statement_status = calculatedStatus

    isOrderLoaded.value = true
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 发货单相关方法
const getDeliveryStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待发出': 'warning',
    '已发出': 'primary',
    '运输中': 'primary',
    '已签收': 'success',
    '已作废': 'info'
  }
  return statusMap[status] || 'info'
}

const viewDeliveryNote = (id: number) => {
  router.push(`/delivery-notes/view/${id}`)
}

// 退货单相关方法
const getReturnStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待确认': 'warning',
    '退货中': 'primary',
    '已签收': 'success',
    '已拒绝': 'danger',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

const viewReturnOrder = (id: number) => {
  router.push(`/returns/view/${id}`)
}

// 结清状态相关方法
const getSettlementStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '未结清': 'warning',
    '部分结清': 'primary',
    '已结清': 'success'
  }
  return statusMap[status] || 'info'
}

// 产品发货状态相关方法
const isProductFullyDelivered = (row: any) => {
  const delivered = row.delivered_quantity || 0
  const total = row.quantity || 0
  return delivered >= total && total > 0
}

const getProductRowClassName = ({ row }: { row: any }) => {
  if (isProductFullyDelivered(row)) {
    return 'fully-delivered-row'
  }
  return ''
}



// 初始化
onMounted(() => {
  getOrderDetail()
})
</script>

<style lang="scss" scoped>
.order-detail {
  .header-card {
    .form-title {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
    }

    .flex-between {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .info-card {
    height: 100%;

    .info-header {
      font-weight: bold;
    }

    .info-body {
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
        }

        .value {
          color: #333;
          font-size: 14px;
          text-align: right;

          &.amount {
            color: #e6a23c;
            font-weight: bold;
          }

          &.statement-status-inline {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;

            .force-settle-btn {
              font-size: 12px;
              padding: 2px 8px;
              height: 24px;
            }
          }

          .quotation-link {
            margin-right: 4px;

            &:not(:last-child)::after {
              content: ', ';
              color: #606266;
              margin-left: 2px;
            }
          }
        }
      }
    }
  }

  .card-header {
    font-size: 16px;
    font-weight: bold;
  }

  .total-section {
    .total-info {
      text-align: right;

      .total-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-top: 1px solid #ebeef5;

        .total-amount {
          font-size: 18px;
          font-weight: bold;
          color: #e6a23c;
        }
      }
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .mt-20 {
    margin-top: 20px;
  }

  // 状态选择器样式
  .status-selector {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .status-tip {
        font-size: 12px;
        color: #999;
        font-weight: normal;
      }
    }

    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
      padding: 16px 0;
    }

    .status-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 12px;
      border: 2px solid #e4e7ed;
      border-radius: 8px;
      transition: all 0.3s ease;
      background: #fff;

      &.active {
        border-color: #409eff;
        background: #ecf5ff;
        color: #409eff;

        .status-icon {
          background: #409eff;
          color: #fff;
        }
      }

      &.clickable {
        cursor: pointer;

        &:hover {
          border-color: #409eff;
          background: #f5f7fa;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }
      }

      &.flexible {
        border-color: #67c23a;

        &:not(.active) {
          background: #f0f9ff;
          border-color: #67c23a;

          .status-icon {
            background: #67c23a;
            color: #fff;
          }
        }

        &.clickable:hover {
          border-color: #67c23a;
          background: #e1f3d8;
          box-shadow: 0 4px 12px rgba(103, 194, 58, 0.15);
        }
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: #f5f7fa;
        border-color: #dcdfe6;
        color: #c0c4cc;

        .status-icon {
          background: #dcdfe6;
          color: #c0c4cc;
        }

        .status-text {
          color: #c0c4cc;
        }

        &:hover {
          transform: none;
          box-shadow: none;
          border-color: #dcdfe6;
          background: #f5f7fa;
        }
      }

      .status-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #e4e7ed;
        color: #909399;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: bold;
        transition: all 0.3s ease;

        .status-number {
          font-size: 12px;
        }
      }

      .status-text {
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        line-height: 1.2;
      }
    }
  }

  // 双状态系统样式
  .status-section {
    .status-section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e4e7ed;
    }
  }



  /* 已完全发货的产品行样式 - 使用最高优先级覆盖所有样式 */
  :deep(.fully-delivered-row) {
    background: #f0f9ff !important;
  }

  :deep(.fully-delivered-row:hover) {
    background: #e1f5fe !important;
  }

  :deep(.fully-delivered-row td) {
    background: #f0f9ff !important;
  }

  :deep(.fully-delivered-row:hover td) {
    background: #e1f5fe !important;
  }

  :deep(.el-table__row.fully-delivered-row) {
    background: #f0f9ff !important;
  }

  :deep(.el-table__row.fully-delivered-row:hover) {
    background: #e1f5fe !important;
  }

  :deep(.el-table__row.fully-delivered-row td) {
    background: #f0f9ff !important;
  }

  :deep(.el-table__row.fully-delivered-row:hover td) {
    background: #e1f5fe !important;
  }

  :deep(.el-table--striped .el-table__row.fully-delivered-row) {
    background: #f0f9ff !important;
  }

  :deep(.el-table--striped .el-table__row.fully-delivered-row:hover) {
    background: #e1f5fe !important;
  }

  :deep(.el-table--striped .el-table__row.fully-delivered-row td) {
    background: #f0f9ff !important;
  }

  :deep(.el-table--striped .el-table__row.fully-delivered-row:hover td) {
    background: #e1f5fe !important;
  }

  /* 已完全发货的产品名称样式 */
  .fully-delivered {
    color: #67c23a;
    font-weight: 500;
  }

  /* 已完全发货的文字样式 */
  .fully-delivered-text {
    color: #67c23a;
    font-weight: 500;
  }


}
</style>
