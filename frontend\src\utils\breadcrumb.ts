import type { RouteLocationMatched, RouteLocationNormalized } from 'vue-router'
import type { BreadcrumbItem } from '@/types/router'

// 路由标题映射
const routeTitleMap: Record<string, string> = {
  // 工作台
  Dashboard: '工作台',

  // 客户管理
  Customers: '客户管理',
  CustomerNew: '新增客户',
  CustomerEdit: '编辑客户',
  CustomerDetail: '客户详情',

  // 产品管理
  Products: '产品管理',
  ProductNew: '新增产品',
  ProductEdit: '编辑产品',
  ProductDetail: '产品详情',
  ProductCategories: '产品分类',
  ProductCategoryEdit: '编辑分类',
  ProductBrands: '品牌管理',
  BrandNew: '新增品牌',
  BrandEdit: '编辑品牌',

  // 报价管理
  Quotations: '报价管理',
  QuotationNew: '新增报价',
  QuotationEdit: '编辑报价',
  QuotationDetail: '报价详情',
  QuotationRequests: '报价需求',
  QuotationRequestNew: '新增需求',
  QuotationRequestEdit: '编辑需求',
  QuotationRequestDetail: '需求详情',
  QuotationTemplates: '报价模板',
  QuotationTemplateCreate: '新建模板',
  QuotationTemplateEdit: '编辑模板',
  QuotationTemplateView: '查看模板',

  // 订单管理
  Orders: '订单管理',
  OrderNew: '新增订单',
  OrderEdit: '编辑订单',
  OrderDetail: '订单详情',

  // 发货管理
  DeliveryList: '发货管理',
  DeliveryCreate: '新增发货单',
  DeliveryNoteCreate: '创建发货单',
  DeliveryDetail: '发货单详情',
  DeliveryEdit: '编辑发货单',

  // 退货管理
  ReturnOrders: '退货管理',
  ReturnOrderNew: '新增退货',
  ReturnOrderEdit: '编辑退货',
  ReturnOrderDetail: '退货详情',

  // 对账管理
  Statements: '对账管理',
  StatementNew: '新增对账单',
  StatementEdit: '编辑对账单',
  StatementDetail: '对账单详情',

  // 财务管理
  Finance: '财务管理',
  FinancialReports: '财务报表',
  PaymentRecords: '收款记录',
  PaymentRecordAdd: '新增收款',
  PaymentRecordEdit: '编辑收款',
  PaymentRecordDetail: '收款详情',
  PaymentPending: '待确认收款',
  RefundRecords: '退款记录',
  RefundRecordAdd: '新增退款',
  RefundRecordEdit: '编辑退款',
  RefundRecordDetail: '退款详情',
  RefundPending: '待处理退款',
  Receivables: '应收款项',

  // 系统设置
  Settings: '系统设置',
  DataBackup: '数据备份',
  ErrorLogs: '错误日志',

  // 其他
  Login: '登录',
  NotFound: '页面不存在',
}

// 生成面包屑
export function generateBreadcrumbs(matched: RouteLocationMatched[]): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = []

  matched.forEach((route, index) => {
    // 跳过根路径和布局组件
    if (route.path === '/' && route.name !== 'Dashboard') {
      return
    }

    const routeName = route.name as string
    const routeMeta = route.meta

    // 获取标题
    const title =
      routeMeta?.title || routeMeta?.breadcrumbTitle || routeTitleMap[routeName] || routeName

    // 处理动态路由参数 - 从当前路由获取参数
    // 注意：RouteLocationMatched 没有 params，需要从当前路由获取

    // 是否可点击（不是最后一个且有路径）
    const isLast = index === matched.length - 1
    const path = isLast ? undefined : route.path

    breadcrumbs.push({
      title,
      path,
      disabled: isLast,
    })
  })

  return breadcrumbs
}

// 获取页面标题
export function getPageTitle(route: RouteLocationNormalized): string {
  const routeName = route.name as string
  const routeMeta = route.meta

  let title = routeMeta?.title || routeTitleMap[routeName] || routeName

  // 处理动态路由参数
  if (route.params.id && (routeName?.includes('Edit') || routeName?.includes('Detail'))) {
    title = title.replace(/\$\{id\}/g, route.params.id as string)
  }

  return title
}

// 设置文档标题
export function setDocumentTitle(title: string, appName = '柏成物资配送管理系统'): void {
  document.title = title ? `${title} - ${appName}` : appName
}
