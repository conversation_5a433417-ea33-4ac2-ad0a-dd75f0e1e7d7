/**
 * 对账单退款API
 */
import { http } from './request'
import type { ApiResponse } from '@/types/api'

export interface StatementRefund {
  id: number
  refund_number: string
  statement_id: number
  refund_date: string
  amount: string
  refund_method: string
  refund_target: string
  reference_number?: string
  bank_account?: string
  balance_transaction_id?: number
  notes?: string
  voucher_files?: string
  status: string
  created_by?: string
  created_at: string
  updated_at: string
  
  // 关联信息
  statement_number?: string
  customer_id?: number
  customer_name?: string
}

export interface CreateStatementRefundParams {
  statement_id: number
  refund_date: Date | string
  amount: number
  refund_target: 'direct' | 'balance'
  refund_method?: string
  reference_number?: string
  bank_account?: string
  notes?: string
  created_by?: string
}

export interface ListStatementRefundParams {
  statement_id?: number
  status?: string
  page?: number
  per_page?: number
}

export interface UpdateStatementRefundStatusParams {
  status: string
  notes?: string
}

export interface RefundCalculation {
  statement_id: number
  statement_number: string
  total_amount: number
  paid_amount: number
  refund_amount: number
  needs_refund: boolean
}

export const statementRefundApi = {
  /**
   * 获取退款记录列表
   */
  async list(params: ListStatementRefundParams = {}): Promise<ApiResponse<{
    refunds: StatementRefund[]
    pagination: {
      page: number
      per_page: number
      total: number
      pages: number
      has_prev: boolean
      has_next: boolean
    }
  }>> {
    return http.get('/statement-refunds', { params })
  },

  /**
   * 创建退款记录
   */
  async create(data: CreateStatementRefundParams): Promise<ApiResponse<StatementRefund>> {
    // 格式化日期
    const submitData = {
      ...data,
      refund_date: data.refund_date instanceof Date 
        ? data.refund_date.toISOString().split('T')[0]
        : data.refund_date
    }
    
    return http.post('/statement-refunds', submitData)
  },

  /**
   * 获取退款记录详情
   */
  async getById(id: number): Promise<ApiResponse<StatementRefund>> {
    return http.get(`/statement-refunds/${id}`)
  },

  /**
   * 更新退款记录状态
   */
  async updateStatus(id: number, data: UpdateStatementRefundStatusParams): Promise<ApiResponse<StatementRefund>> {
    return http.put(`/statement-refunds/${id}`, data)
  },

  /**
   * 计算对账单可退款金额
   */
  async calculateRefund(statementId: number): Promise<ApiResponse<RefundCalculation>> {
    return http.get(`/statement-refunds/statement/${statementId}/calculate-refund`)
  },

  /**
   * 获取对账单的退款记录
   */
  async getByStatementId(statementId: number): Promise<ApiResponse<{
    refunds: StatementRefund[]
    pagination: {
      page: number
      per_page: number
      total: number
      pages: number
      has_prev: boolean
      has_next: boolean
    }
  }>> {
    return this.list({ statement_id: statementId })
  }
}
