import request from './request'

// 收款记录相关API
export const paymentRecordApi = {
  // 获取收款记录列表
  getList: (params: any) => {
    return request.get('/payment-records', { params })
  },

  // 获取收款记录详情
  getById: (id: number) => {
    return request.get(`/payment-records/${id}`)
  },

  // 创建收款记录
  create: (data: any) => {
    return request.post('/payment-records', data)
  },

  // 更新收款记录
  update: (id: number, data: any) => {
    return request.put(`/payment-records/${id}`, data)
  },

  // 删除收款记录
  delete: (id: number) => {
    return request.delete(`/payment-records/${id}`)
  },

  // 更新收款记录状态
  updateStatus: (id: number, status: string, notes?: string) => {
    return request.patch(`/payment-records/${id}/status`, { status, notes })
  },

  // 确认收款
  confirm: (id: number, notes?: string) => {
    return request.post(`/payment-records/${id}/confirm`, { notes })
  },

  // 取消收款
  cancel: (id: number, reason?: string) => {
    return request.post(`/payment-records/${id}/cancel`, { reason })
  },

  // 获取收款统计
  getStats: (params?: any) => {
    return request.get('/payment-records/stats', { params })
  },

  // 导出收款记录
  export: (params?: any) => {
    return request.get('/payment-records/export', {
      params,
      responseType: 'blob'
    })
  }
}

// 应收款项相关API
export const receivableApi = {
  // 获取应收款项列表
  getList: (params: any) => {
    return request.get('/finance/receivables', { params })
  },

  // 获取应收款项详情
  getById: (id: number) => {
    return request.get(`/finance/receivables/${id}`)
  },

  // 获取应收款项统计
  getStats: (params?: any) => {
    return request.get('/finance/receivables/stats', { params })
  },

  // 导出应收款项
  export: (params?: any) => {
    return request.get('/finance/receivables/export', {
      params,
      responseType: 'blob'
    })
  }
}

// 退款记录相关API
export const refundRecordApi = {
  // 获取退款记录列表
  getList: (params: any) => {
    return request.get('/refund-records', { params })
  },

  // 获取退款记录详情
  getById: (id: number) => {
    return request.get(`/refund-records/${id}`)
  },

  // 创建退款记录
  create: (data: any) => {
    return request.post('/refund-records', data)
  },

  // 更新退款记录
  update: (id: number, data: any) => {
    return request.put(`/refund-records/${id}`, data)
  },

  // 删除退款记录
  delete: (id: number) => {
    return request.delete(`/refund-records/${id}`)
  },

  // 更新退款记录状态
  updateStatus: (id: number, status: string, notes?: string) => {
    return request.patch(`/refund-records/${id}/status`, { status, notes })
  },

  // 确认退款
  confirm: (id: number, notes?: string) => {
    return request.post(`/refund-records/${id}/confirm`, { notes })
  },

  // 取消退款
  cancel: (id: number, reason?: string) => {
    return request.post(`/refund-records/${id}/cancel`, { reason })
  },

  // 获取退款统计
  getStats: (params?: any) => {
    return request.get('/refund-records/stats', { params })
  }
}

// 财务报表相关API
export const financialReportApi = {
  // 获取收款报表
  getPaymentReport: (params: any) => {
    return request.get('/financial-reports/payment', { params })
  },

  // 获取应收款报表
  getReceivableReport: (params: any) => {
    return request.get('/financial-reports/receivable', { params })
  },

  // 获取退款报表
  getRefundReport: (params: any) => {
    return request.get('/financial-reports/refund', { params })
  },

  // 获取财务概览
  getOverview: (params?: any) => {
    return request.get('/financial-reports/overview', { params })
  },

  // 导出财务报表
  export: (type: string, params?: any) => {
    return request.get(`/financial-reports/${type}/export`, {
      params,
      responseType: 'blob'
    })
  }
}

// 银行账户相关API
export const bankAccountApi = {
  // 获取银行账户列表
  getList: (params?: any) => {
    return request.get('/bank-accounts', { params })
  },

  // 获取银行账户详情
  getById: (id: number) => {
    return request.get(`/bank-accounts/${id}`)
  },

  // 创建银行账户
  create: (data: any) => {
    return request.post('/bank-accounts', data)
  },

  // 更新银行账户
  update: (id: number, data: any) => {
    return request.put(`/bank-accounts/${id}`, data)
  },

  // 删除银行账户
  delete: (id: number) => {
    return request.delete(`/bank-accounts/${id}`)
  }
}

// 兼容原项目的API函数
export const getPaymentRecords = paymentRecordApi.getList
export const getPaymentRecordDetail = paymentRecordApi.getById
export const createPaymentRecord = paymentRecordApi.create
export const updatePaymentRecord = paymentRecordApi.update
export const deletePaymentRecord = paymentRecordApi.delete
export const updatePaymentRecordStatus = paymentRecordApi.updateStatus
export const confirmPaymentRecord = paymentRecordApi.confirm
export const cancelPaymentRecord = paymentRecordApi.cancel
export const getPaymentStatistics = paymentRecordApi.getStats
export const exportPaymentRecords = paymentRecordApi.export

export const getReceivables = receivableApi.getList
export const getReceivableDetail = receivableApi.getById
export const getReceivableStatistics = receivableApi.getStats
export const exportReceivables = receivableApi.export

export const getRefundRecords = refundRecordApi.getList
export const getRefundRecordDetail = refundRecordApi.getById
export const createRefundRecord = refundRecordApi.create
export const updateRefundRecord = refundRecordApi.update
export const deleteRefundRecord = refundRecordApi.delete
export const updateRefundRecordStatus = refundRecordApi.updateStatus
export const confirmRefundRecord = refundRecordApi.confirm
export const cancelRefundRecord = refundRecordApi.cancel
export const getRefundStatistics = refundRecordApi.getStats

export const getPaymentReport = financialReportApi.getPaymentReport
export const getReceivableReport = financialReportApi.getReceivableReport
export const getRefundReport = financialReportApi.getRefundReport
export const getFinancialOverview = financialReportApi.getOverview
export const exportFinancialReport = financialReportApi.export

export const getBankAccounts = bankAccountApi.getList
export const getBankAccountDetail = bankAccountApi.getById
export const createBankAccount = bankAccountApi.create
export const updateBankAccount = bankAccountApi.update
export const deleteBankAccount = bankAccountApi.delete

// 获取可收款订单列表
export const getPayableOrders = (params: any) => {
  return request.get('/orders', { 
    params: {
      ...params,
      status: 'confirmed,delivered,completed' // 只获取可收款的订单状态
    }
  })
}

// 获取订单收款历史
export const getOrderPaymentHistory = (orderId: number) => {
  return request.get(`/orders/${orderId}/payment-history`)
}

// 获取订单详情（用于收款）
export const getOrderForPayment = (orderId: number) => {
  return request.get(`/orders/${orderId}`)
}
