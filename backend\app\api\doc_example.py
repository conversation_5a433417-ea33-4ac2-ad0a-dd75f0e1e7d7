"""
API响应装饰器示例
展示如何为API添加详细的响应文档
"""
from flask_restx import Namespace, Resource, fields
from app.api.response_models import (
    success_response_model, error_response_model, 
    validation_error_response_model, not_found_response_model,
    paginated_response_model
)

# 创建命名空间
api = Namespace('example', description='API文档示例')

# 注册响应模型
success_model = api.model('SuccessResponse', success_response_model)
error_model = api.model('ErrorResponse', error_response_model)
validation_error_model = api.model('ValidationErrorResponse', validation_error_response_model)
not_found_model = api.model('NotFoundResponse', not_found_response_model)
paginated_model = api.model('PaginatedResponse', paginated_response_model)

# 示例数据模型
customer_model = api.model('Customer', {
    'id': fields.Integer(description='客户ID', example=1),
    'name': fields.String(required=True, description='客户名称', example='测试公司'),
    'contact': fields.String(description='联系人', example='张三'),
    'phone': fields.String(description='联系电话', example='13800138000'),
    'email': fields.String(description='邮箱地址', example='<EMAIL>'),
    'address': fields.String(description='地址', example='北京市朝阳区'),
    'status': fields.String(description='客户状态', example='active'),
    'level': fields.String(description='客户等级', example='A'),
    'created_at': fields.DateTime(description='创建时间'),
    'updated_at': fields.DateTime(description='更新时间')
})

@api.route('/customers')
class CustomerList(Resource):
    @api.doc('get_customers', 
             description='获取客户列表，支持分页和多条件筛选',
             responses={
                 200: ('成功', paginated_model),
                 400: ('请求参数错误', error_model),
                 500: ('服务器内部错误', error_model)
             })
    @api.param('page', '页码', type='integer', default=1, example=1)
    @api.param('per_page', '每页数量', type='integer', default=20, example=20)
    @api.param('name', '客户名称搜索', example='测试公司')
    @api.param('status', '客户状态', enum=['active', 'inactive'], example='active')
    @api.param('level', '客户等级', enum=['A', 'B', 'C'], example='A')
    def get(self):
        """
        获取客户列表
        
        支持以下功能：
        - 分页查询
        - 按客户名称模糊搜索
        - 按客户状态筛选
        - 按客户等级筛选
        
        返回数据包含客户的基本信息，不包含敏感数据。
        """
        pass
    
    @api.doc('create_customer',
             description='创建新客户',
             responses={
                 201: ('创建成功', success_model),
                 400: ('请求参数错误', validation_error_model),
                 409: ('客户已存在', error_model),
                 500: ('服务器内部错误', error_model)
             })
    @api.expect(customer_model, validate=True)
    def post(self):
        """
        创建新客户
        
        注意事项：
        - 客户名称不能重复
        - 联系电话格式需要正确
        - 邮箱地址需要有效
        
        创建成功后返回完整的客户信息。
        """
        pass

@api.route('/customers/<int:customer_id>')
class CustomerDetail(Resource):
    @api.doc('get_customer',
             description='根据ID获取客户详情',
             responses={
                 200: ('成功', success_model),
                 404: ('客户不存在', not_found_model),
                 500: ('服务器内部错误', error_model)
             })
    def get(self, customer_id):
        """
        获取客户详情
        
        返回指定ID客户的完整信息，包括：
        - 基本信息
        - 联系方式
        - 银行账户（如果有）
        - 送货地址（如果有）
        """
        pass
    
    @api.doc('update_customer',
             description='更新客户信息',
             responses={
                 200: ('更新成功', success_model),
                 400: ('请求参数错误', validation_error_model),
                 404: ('客户不存在', not_found_model),
                 409: ('客户名称冲突', error_model),
                 500: ('服务器内部错误', error_model)
             })
    @api.expect(customer_model, validate=True)
    def put(self, customer_id):
        """
        更新客户信息
        
        支持部分更新，只需要传入需要修改的字段。
        更新后返回完整的客户信息。
        """
        pass
    
    @api.doc('delete_customer',
             description='删除客户',
             responses={
                 200: ('删除成功', success_model),
                 404: ('客户不存在', not_found_model),
                 409: ('客户有关联数据，无法删除', error_model),
                 500: ('服务器内部错误', error_model)
             })
    def delete(self, customer_id):
        """
        删除客户
        
        注意事项：
        - 如果客户有关联的订单、报价单等数据，无法删除
        - 删除操作不可逆，请谨慎操作
        """
        pass
