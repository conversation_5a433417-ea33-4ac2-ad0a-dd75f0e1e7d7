import{J as e,u as a,b as l,r as t,d as i,L as o,e as d,f as u,o as s,c as n,i as r,h as c,a as p,t as m,k as f,l as _,F as v,m as h,V as y,U as V,Z as b,g,a4 as w}from"./index-3d4c440c.js";import{g as U,u as k,a as q}from"./quotationTemplate-3cbc44ef.js";import{l as C}from"./product-ab10366a.js";import{_ as x}from"./_plugin-vue_export-helper-1b428a4d.js";const F={class:"app-container"},T={class:"card-header"},$=x(e({__name:"QuotationTemplateForm",setup(e){const x=a(),$=l(),j=t(),E=t(!1),Q=t("basic"),R=i((()=>x.params.id?Number(x.params.id):null)),I=i((()=>!!R.value)),J=o({name:"",description:"",is_default:!1,items:[]}),L=o({name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],items:[{type:"array",required:!0,message:"请至少添加一个模板项目",trigger:"change",validator:(e,a,l)=>{if(a&&0!==a.length){for(const e of a){if(!e.product_id||!e.product_specification_id)return void l(new Error("模板项目中的产品和规格不能为空"));if(e.quantity<=0)return void l(new Error("项目数量必须大于0"))}l()}else l(new Error("请至少添加一个模板项目"))}}]}),N=t({}),Z=t({}),z=(e,a)=>{const l=(Z.value[a]||[]).find((a=>a.id===e));l&&(J.items[a].unit_price=l.price)},A=()=>{J.items.push({product_id:0,product_specification_id:0,quantity:1,unit_price:void 0,discount:void 0,notes:""});const e=J.items.length-1;N.value[e]=[],Z.value[e]=[]},B=async()=>{if(R.value){E.value=!0;try{const e=await U(R.value);J.id=e.id,J.name=e.name,J.description=e.description,J.is_default=e.is_default,J.items=e.items,await y();for(let a=0;a<J.items.length;a++){const e=J.items[a];if(e.product_id&&e.product){if(N.value[a]=[e.product],e.product.specifications?Z.value[a]=e.product.specifications:Z.value[a]=[],e.product_specification_id&&e.product.specifications){e.product.specifications.find((a=>a.id===e.product_specification_id))&&e.unit_price}}else e.product_id&&(N.value[a]=[],Z.value[a]=[])}}catch(e){console.error("Failed to load template:",e),V.error("加载报价模板失败"),$.push({name:"QuotationTemplates"})}finally{E.value=!1}}},D=async()=>{j.value&&await j.value.validate((async e=>{if(e){E.value=!0;try{const e=J.items.map((e=>({id:e.id,product_id:e.product_id,product_specification_id:e.product_specification_id,quantity:e.quantity,unit_price:e.unit_price,discount:e.discount,notes:e.notes})));if(I.value&&R.value){const a={id:R.value,name:J.name,description:J.description,is_default:J.is_default,items:e.map((e=>{const{product:a,specification:l,...t}=e;return t}))};await k(R.value,a),V.success("更新成功")}else{const a={name:J.name,description:J.description,is_default:J.is_default,items:e.map((({id:e,...a})=>a)).map((e=>{const{product:a,specification:l,...t}=e;return t}))};await q(a),V.success("创建成功")}$.push({name:"QuotationTemplates"})}catch(a){console.error("Form submission error:",a);let e="操作失败";a.response&&a.response.data&&a.response.data.message&&(e=a.response.data.message),V.error(e)}finally{E.value=!1}}}))},G=()=>{j.value&&j.value.resetFields(),J.items=[],I.value?B():(J.name="",J.description="",J.is_default=!1)},H=()=>{$.push({name:"QuotationTemplates"})};return d((()=>{I.value&&B()})),(e,a)=>{const l=u("el-button"),t=u("el-input"),i=u("el-form-item"),o=u("el-col"),d=u("el-switch"),y=u("el-row"),V=u("el-tab-pane"),U=u("el-option"),k=u("el-select"),q=u("el-table-column"),x=u("el-input-number"),$=u("el-table"),R=u("el-tabs"),B=u("el-form"),K=u("el-card");return s(),n("div",F,[r(K,null,{header:c((()=>[p("div",T,[p("span",null,m(I.value?"编辑报价模板":"新建报价模板"),1),r(l,{type:"primary",onClick:H,style:{float:"right"}},{default:c((()=>a[4]||(a[4]=[f("返回列表")]))),_:1})])])),default:c((()=>[r(B,{ref_key:"templateFormRef",ref:j,model:J,rules:L,"label-width":"120px"},{default:c((()=>[r(R,{modelValue:Q.value,"onUpdate:modelValue":a[3]||(a[3]=e=>Q.value=e)},{default:c((()=>[r(V,{label:"基本信息",name:"basic"},{default:c((()=>[r(y,{gutter:20},{default:c((()=>[r(o,{span:12},{default:c((()=>[r(i,{label:"模板名称",prop:"name"},{default:c((()=>[r(t,{modelValue:J.name,"onUpdate:modelValue":a[0]||(a[0]=e=>J.name=e),placeholder:"请输入模板名称"},null,8,["modelValue"])])),_:1})])),_:1}),r(o,{span:12},{default:c((()=>[r(i,{label:"是否默认"},{default:c((()=>[r(d,{modelValue:J.is_default,"onUpdate:modelValue":a[1]||(a[1]=e=>J.is_default=e)},null,8,["modelValue"])])),_:1})])),_:1}),r(o,{span:24},{default:c((()=>[r(i,{label:"模板描述",prop:"description"},{default:c((()=>[r(t,{type:"textarea",modelValue:J.description,"onUpdate:modelValue":a[2]||(a[2]=e=>J.description=e),placeholder:"请输入模板描述"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),r(V,{label:"模板项目",name:"items"},{default:c((()=>[r(l,{type:"success",icon:_(b),onClick:A,class:"mb-2"},{default:c((()=>a[5]||(a[5]=[f("添加项目")]))),_:1},8,["icon"]),r($,{data:J.items,border:""},{default:c((()=>[r(q,{label:"产品","min-width":"250"},{default:c((({row:e,$index:a})=>[r(k,{modelValue:e.product_id,"onUpdate:modelValue":a=>e.product_id=a,placeholder:"选择产品",filterable:"",remote:"","remote-method":e=>(async(e,a)=>{if(e)try{const l=await C({name:e,per_page:20,with_specifications:!0});N.value[a]=l.list}catch(l){console.error("Error searching products:",l),N.value[a]=[]}else N.value[a]=[]})(e,a),onChange:e=>(async(e,a)=>{const l=(N.value[a]||[]).find((a=>a.id===e));l?(J.items[a].product_id=l.id,J.items[a].product_specification_id=0,Z.value[a]=l.specifications||[],1===Z.value[a].length&&(J.items[a].product_specification_id=Z.value[a][0].id,z(J.items[a].product_specification_id,a))):Z.value[a]=[]})(e,a),style:{width:"100%"},"value-key":"id"},{default:c((()=>[(s(!0),n(v,null,h(N.value[a]||[],(e=>(s(),g(U,{key:e.id,label:`${e.name} (${e.model||"无型号"})`,value:e.id},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","remote-method","onChange"])])),_:1}),r(q,{label:"规格","min-width":"200"},{default:c((({row:e,$index:a})=>[r(k,{modelValue:e.product_specification_id,"onUpdate:modelValue":a=>e.product_specification_id=a,placeholder:"选择规格",disabled:!e.product_id||!Z.value[a]||0===Z.value[a].length,style:{width:"100%"},"value-key":"id",onChange:e=>z(e,a)},{default:c((()=>[(s(!0),n(v,null,h(Z.value[a]||[],(e=>(s(),g(U,{key:e.id,label:e.specification,value:e.id},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])),_:1}),r(q,{label:"数量",prop:"quantity",width:"120"},{default:c((({row:e})=>[r(x,{modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,min:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),r(q,{label:"单价(可选)",prop:"unit_price",width:"150"},{default:c((({row:e})=>[r(x,{modelValue:e.unit_price,"onUpdate:modelValue":a=>e.unit_price=a,precision:2,min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),r(q,{label:"折扣(可选)",prop:"discount",width:"150"},{default:c((({row:e})=>[r(x,{modelValue:e.discount,"onUpdate:modelValue":a=>e.discount=a,precision:2,min:0,max:1,step:"0.01","controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),r(q,{label:"备注",prop:"notes"},{default:c((({row:e})=>[r(t,{modelValue:e.notes,"onUpdate:modelValue":a=>e.notes=a,placeholder:"备注"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),r(q,{label:"操作",width:"100",align:"center"},{default:c((({$index:e})=>[r(l,{type:"danger",link:"",icon:_(w),onClick:a=>{return l=e,J.items.splice(l,1),delete N.value[l],void delete Z.value[l];var l}},{default:c((()=>a[6]||(a[6]=[f("删除")]))),_:2},1032,["icon","onClick"])])),_:1})])),_:1},8,["data"])])),_:1})])),_:1},8,["modelValue"]),r(i,{class:"mt-4"},{default:c((()=>[r(l,{type:"primary",onClick:D,loading:E.value},{default:c((()=>a[7]||(a[7]=[f("保存")]))),_:1},8,["loading"]),r(l,{onClick:G},{default:c((()=>a[8]||(a[8]=[f("重置")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})])}}}),[["__scopeId","data-v-8ef2a578"]]);export{$ as default};
