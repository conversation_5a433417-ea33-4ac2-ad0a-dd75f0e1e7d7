# EMB系统环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置值

# Flask应用配置
FLASK_APP=run.py
FLASK_ENV=development
FLASK_DEBUG=True

# 服务器配置
HOST=0.0.0.0
PORT=5001

# 数据库配置
DATABASE_URL=sqlite:///project.db
# 生产环境示例：
# DATABASE_URL=mysql://username:password@localhost/emb_system
# DATABASE_URL=postgresql://username:password@localhost/emb_system

# 安全配置
SECRET_KEY=your_super_secret_key_change_this_in_production
# 生产环境请使用强密钥，例如：
# SECRET_KEY=a_very_long_random_string_with_numbers_and_symbols_123456

# API配置
API_TITLE=EMB System API
API_VERSION=1.0
API_DESCRIPTION=工程物资报价及订单管理系统API文档

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 数据库连接池配置
SQLALCHEMY_POOL_SIZE=10
SQLALCHEMY_POOL_TIMEOUT=20
SQLALCHEMY_POOL_RECYCLE=3600
SQLALCHEMY_MAX_OVERFLOW=20

# 跨域配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization

# 文件上传配置
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads
ALLOWED_EXTENSIONS=txt,pdf,png,jpg,jpeg,gif,xlsx,xls,csv

# 缓存配置
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# 邮件配置（如果需要）
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password

# 第三方服务配置
# 如果集成了其他服务，在这里添加相关配置

# 备份配置
BACKUP_FOLDER=backups
AUTO_BACKUP_ENABLED=True
BACKUP_RETENTION_DAYS=30

# 性能监控
ENABLE_PROFILING=False
SLOW_QUERY_THRESHOLD=0.5

# 安全配置
SESSION_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
PERMANENT_SESSION_LIFETIME=3600

# 开发环境特定配置
TESTING=False
WTF_CSRF_ENABLED=True
