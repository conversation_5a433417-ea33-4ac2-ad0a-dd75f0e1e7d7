import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import type { LoadingInstance } from 'element-plus/es/components/loading/src/loading'

// 请求配置接口
interface RequestConfig extends InternalAxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
  errorMessage?: string
}

// 响应数据接口
interface ResponseData<T = any> {
  success: boolean
  data: T
  message?: string
  code?: number
}

// 全局loading实例
let loadingInstance: LoadingInstance | null = null
let loadingCount = 0

// 显示loading
const showLoading = () => {
  if (loadingCount === 0) {
    loadingInstance = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
  }
  loadingCount++
}

// 隐藏loading
const hideLoading = () => {
  loadingCount--
  if (loadingCount <= 0) {
    loadingInstance?.close()
    loadingInstance = null
    loadingCount = 0
  }
}

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: '/api/v1', // 通过Vite代理到后端
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config: RequestConfig) => {
    // 显示loading
    if (config.showLoading !== false) {
      showLoading()
    }

    // 添加token认证
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳，防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }

    // 打印请求信息（开发环境）
    if (import.meta.env.DEV) {
      console.log(`🚀 [${config.method?.toUpperCase()}] ${config.url}`, {
        params: config.params,
        data: config.data,
      })
    }

    return config
  },
  error => {
    hideLoading()
    console.error('请求错误:', error)
    ElMessage.error('请求配置错误')
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ResponseData>) => {
    hideLoading()

    const { data } = response
    const config = response.config as RequestConfig

    // 打印响应信息（开发环境）
    if (import.meta.env.DEV) {
      console.log(`✅ [${response.status}] ${config.url}`, data)
    }

    // 根据后端API的响应格式进行处理
    if (response.status >= 200 && response.status < 300) {
      // 如果后端返回的数据有success字段，根据success判断
      if (typeof data === 'object' && data !== null && 'success' in data) {
        if (data.success) {
          return data.data || data
        } else {
          // 207状态码表示部分成功，不应该抛出错误
          if (response.status === 207) {
            return data
          }

          const errorMsg = data.message || '请求失败'
          if (config.showError !== false) {
            ElMessage.error(config.errorMessage || errorMsg)
          }
          return Promise.reject(new Error(errorMsg))
        }
      }
      // 直接返回数据
      return data
    } else {
      const errorMsg = data?.message || '请求失败'
      if (config.showError !== false) {
        ElMessage.error(config.errorMessage || errorMsg)
      }
      return Promise.reject(new Error(errorMsg))
    }
  },
  (error: AxiosError) => {
    hideLoading()

    const config = error.config as RequestConfig

    // 打印错误信息（开发环境）
    if (import.meta.env.DEV) {
      console.error(`❌ [${error.response?.status || 'Network'}] ${config?.url}`, error)
    }

    // 如果配置了不显示错误，直接返回
    if (config?.showError === false) {
      return Promise.reject(error)
    }

    let errorMessage = '请求失败'

    if (error.response) {
      const { status, data } = error.response
      const errorData = data as { message?: string }

      switch (status) {
        case 400:
          errorMessage = errorData?.message || '请求参数错误'
          break
        case 401:
          errorMessage = '未授权，请重新登录'
          // 清除token并跳转到登录页
          localStorage.removeItem('token')
          // 这里可以添加路由跳转逻辑
          // router.push('/login')
          break
        case 403:
          errorMessage = '拒绝访问，权限不足'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 422:
          errorMessage = errorData?.message || '数据验证失败'
          break
        case 429:
          errorMessage = '请求过于频繁，请稍后再试'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        case 502:
          errorMessage = '网关错误'
          break
        case 503:
          errorMessage = '服务暂时不可用'
          break
        case 504:
          errorMessage = '网关超时'
          break
        default:
          errorMessage = errorData?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请稍后再试'
      } else {
        errorMessage = '网络错误，请检查网络连接'
      }
    } else {
      errorMessage = '请求配置错误'
    }

    // 显示错误消息
    ElMessage.error(config?.errorMessage || errorMessage)

    return Promise.reject(error)
  }
)

// 便捷的请求方法
export const http = {
  get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<T> {
    return request.get(url, { params, ...config })
  },

  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return request.post(url, data, config)
  },

  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return request.put(url, data, config)
  },

  delete<T = any>(url: string, params?: any, config?: RequestConfig): Promise<T> {
    return request.delete(url, { params, ...config })
  },

  patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return request.patch(url, data, config)
  },

  // 上传文件
  upload<T = any>(url: string, formData: FormData, config?: RequestConfig): Promise<T> {
    return request.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    })
  },

  // 下载文件
  async download(url: string, params?: any, filename?: string): Promise<void> {
    const response = await request.get(url, {
      params,
      responseType: 'blob',
      showLoading: true,
    } as RequestConfig)

    // 确保response是Blob类型
    const blob = response instanceof Blob ? response : new Blob([response as unknown as BlobPart])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  },
}

export default request
