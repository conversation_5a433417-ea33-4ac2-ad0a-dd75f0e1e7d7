#!/usr/bin/env python3
"""
测试已取消订单不计入结清状态的逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.order import Order

def test_cancelled_orders_exclusion():
    """测试已取消订单不计入结清状态的逻辑"""
    app = create_app()
    
    with app.app_context():
        print("🧪 测试已取消订单不计入结清状态的逻辑")
        print("=" * 60)
        
        try:
            # 查找有已取消退货单的订单
            order = Order.query.filter(
                Order.return_orders.any(status='已取消')
            ).first()
            
            if not order:
                print("❌ 没有找到包含已取消退货单的订单")
                return
            
            print(f"📋 测试订单: {order.order_number}")
            print(f"   当前发货状态: {order.order_status}")
            print(f"   当前财务状态: {order.payment_status}")
            
            # 显示所有发货单和退货单的状态
            print(f"\n🚚 发货单信息:")
            for i, dn in enumerate(list(order.delivery_notes), 1):
                print(f"   {i}. {dn.delivery_number}")
                print(f"      状态: {dn.status}")
                print(f"      结清状态: {dn.settlement_status}")
                print(f"      是否计入: {'否' if dn.status == '已取消' else '是'}")

            print(f"\n🔄 退货单信息:")
            for i, ro in enumerate(list(order.return_orders), 1):
                print(f"   {i}. {ro.return_number}")
                print(f"      状态: {ro.status}")
                print(f"      结清状态: {ro.settlement_status}")
                print(f"      是否计入: {'否' if ro.status == '已取消' else '是'}")
            
            # 测试新的计算逻辑
            print(f"\n🔍 计算逻辑测试:")
            
            # 获取有效的单据（排除已取消的）
            all_delivery_notes = list(order.delivery_notes)
            all_return_orders = list(order.return_orders)
            valid_delivery_notes = [dn for dn in all_delivery_notes if dn.status != '已取消']
            valid_return_orders = [ro for ro in all_return_orders if ro.status != '已取消']

            print(f"   总发货单数: {len(all_delivery_notes)}")
            print(f"   有效发货单数: {len(valid_delivery_notes)} (排除已取消)")
            print(f"   总退货单数: {len(all_return_orders)}")
            print(f"   有效退货单数: {len(valid_return_orders)} (排除已取消)")
            
            # 统计结清状态
            settled_delivery_count = sum(1 for dn in valid_delivery_notes if dn.settlement_status == '已结清')
            settled_return_count = sum(1 for ro in valid_return_orders if ro.settlement_status == '已结清')
            partial_delivery_count = sum(1 for dn in valid_delivery_notes if dn.settlement_status == '部分结清')
            partial_return_count = sum(1 for ro in valid_return_orders if ro.settlement_status == '部分结清')
            
            total_settled = settled_delivery_count + settled_return_count
            total_partial = partial_delivery_count + partial_return_count
            total_valid_documents = len(valid_delivery_notes) + len(valid_return_orders)
            
            print(f"\n📊 结清状态统计 (仅有效单据):")
            print(f"   已结清发货单: {settled_delivery_count}")
            print(f"   已结清退货单: {settled_return_count}")
            print(f"   部分结清发货单: {partial_delivery_count}")
            print(f"   部分结清退货单: {partial_return_count}")
            print(f"   总已结清: {total_settled}")
            print(f"   总部分结清: {total_partial}")
            print(f"   总有效单据: {total_valid_documents}")
            
            # 计算预期的财务状态
            if not valid_delivery_notes and not valid_return_orders:
                expected_status = '未收款'
            elif total_settled == 0 and total_partial == 0:
                expected_status = '未收款'
            elif order.order_status == '全部发货' and total_settled == total_valid_documents:
                expected_status = '已收款'
            elif total_settled > 0 or total_partial > 0:
                expected_status = '部分收款'
            else:
                expected_status = '未收款'
            
            print(f"\n🎯 财务状态计算:")
            print(f"   当前状态: {order.payment_status}")
            print(f"   预期状态: {expected_status}")
            
            # 执行自动更新
            print(f"\n🔄 执行自动更新...")
            old_status = order.payment_status
            order.auto_update_payment_status()
            db.session.commit()
            
            print(f"✅ 更新完成")
            print(f"   更新前: {old_status}")
            print(f"   更新后: {order.payment_status}")
            print(f"   是否变化: {'是' if old_status != order.payment_status else '否'}")
            
            # 验证结果
            if order.payment_status == expected_status:
                print(f"✅ 验证通过: 财务状态计算正确")
            else:
                print(f"❌ 验证失败: 期望 {expected_status}, 实际 {order.payment_status}")
            
            print(f"\n🎉 测试完成!")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    test_cancelled_orders_exclusion()
