<template>
  <el-dialog
    v-model="visible"
    title="导出退货单配置"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form :model="exportConfig" label-width="120px">
      <el-form-item label="导出格式">
        <el-radio-group v-model="exportConfig.format">
          <el-radio value="xlsx">Excel (XLSX)</el-radio>
          <el-radio value="pdf">PDF</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="包含表头">
        <el-switch v-model="exportConfig.includeHeader" />
      </el-form-item>
      
      <el-form-item label="导出列">
        <div class="export-columns-container">
          <div class="select-all-container">
            <el-checkbox v-model="exportConfig.selectAll" @change="handleSelectAllChange" class="select-all-checkbox">
              全选
            </el-checkbox>
          </div>

          <div class="columns-grid">
            <!-- 基本信息字段 -->
            <div class="column-category">
              <div class="category-header">
                <div class="category-title">基本信息</div>
                <el-checkbox v-model="basicSelectAll" @change="handleBasicSelectAllChange" class="category-select-all">
                  全选
                </el-checkbox>
              </div>
              <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
                <el-checkbox v-for="column in basicColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                  {{ column.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 产品明细字段 -->
            <div class="column-category">
              <div class="category-header">
                <div class="category-title">产品明细</div>
                <el-checkbox v-model="itemSelectAll" @change="handleItemSelectAllChange" class="category-select-all">
                  全选
                </el-checkbox>
              </div>
              <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
                <el-checkbox v-for="column in itemColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                  {{ column.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exporting">
          确认导出
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { returnApi } from '@/api/return'

interface Props {
  modelValue: boolean
  returnOrderId?: number
  returnOrderName?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  returnOrderId: undefined,
  returnOrderName: ''
})

const emit = defineEmits<Emits>()

const visible = ref(false)
const exporting = ref(false)

// 定义可导出的字段
const availableColumns = [
  // 基本信息字段
  { prop: 'return_number', label: '退货单号', category: 'basic' },
  { prop: 'order_number', label: '关联订单号', category: 'basic' },
  { prop: 'project_name', label: '项目名称', category: 'basic' },
  { prop: 'customer_name', label: '客户名称', category: 'basic' },
  { prop: 'return_date', label: '退货日期', category: 'basic' },
  { prop: 'status', label: '退货状态', category: 'basic' },
  { prop: 'settlement_status', label: '结清状态', category: 'basic' },
  { prop: 'settlement_date', label: '结清日期', category: 'basic' },
  { prop: 'reason', label: '退货原因', category: 'basic' },
  { prop: 'total_amount', label: '退货总金额', category: 'basic' },
  { prop: 'notes', label: '退货备注', category: 'basic' },
  { prop: 'created_at', label: '创建时间', category: 'basic' },
  
  // 产品明细字段
  { prop: 'product_name', label: '产品名称', category: 'items' },
  { prop: 'product_model', label: '产品型号', category: 'items' },
  { prop: 'specification_description', label: '规格描述', category: 'items' },
  { prop: 'quantity', label: '退货数量', category: 'items' },
  { prop: 'product_unit', label: '单位', category: 'items' },
  { prop: 'unit_price', label: '单价', category: 'items' },
  { prop: 'amount', label: '退货金额', category: 'items' },
  { prop: 'item_reason', label: '产品退货原因', category: 'items' },
  { prop: 'item_notes', label: '产品备注', category: 'items' }
]

// 分离基本信息和产品明细字段
const basicColumns = computed(() => availableColumns.filter(col => col.category === 'basic'))
const itemColumns = computed(() => availableColumns.filter(col => col.category === 'items'))

const exportConfig = reactive({
  format: 'xlsx' as 'xlsx' | 'pdf',
  includeHeader: true,
  selectedColumns: availableColumns.map(col => col.prop),
  selectAll: true
})

// 分类全选状态
const basicSelectAll = ref(true)
const itemSelectAll = ref(true)

const handleSelectAllChange = (val: boolean) => {
  if (val) {
    exportConfig.selectedColumns = availableColumns.map(col => col.prop)
    basicSelectAll.value = true
    itemSelectAll.value = true
  } else {
    exportConfig.selectedColumns = []
    basicSelectAll.value = false
    itemSelectAll.value = false
  }
}

// 基本信息分类全选处理
const handleBasicSelectAllChange = (val: boolean) => {
  const basicProps = basicColumns.value.map(col => col.prop)
  if (val) {
    // 添加基本信息字段到选中列表
    const newSelectedColumns = [...exportConfig.selectedColumns]
    basicProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    // 从选中列表中移除基本信息字段
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !basicProps.includes(col))
  }
  updateSelectAllStatus()
}

// 产品明细分类全选处理
const handleItemSelectAllChange = (val: boolean) => {
  const itemProps = itemColumns.value.map(col => col.prop)
  if (val) {
    // 添加产品明细字段到选中列表
    const newSelectedColumns = [...exportConfig.selectedColumns]
    itemProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    // 从选中列表中移除产品明细字段
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !itemProps.includes(col))
  }
  updateSelectAllStatus()
}

// 更新全选状态
const updateSelectAllStatus = () => {
  const basicProps = basicColumns.value.map(col => col.prop)
  const itemProps = itemColumns.value.map(col => col.prop)

  // 更新分类全选状态
  basicSelectAll.value = basicProps.every(prop => exportConfig.selectedColumns.includes(prop))
  itemSelectAll.value = itemProps.every(prop => exportConfig.selectedColumns.includes(prop))

  // 更新总全选状态
  exportConfig.selectAll = exportConfig.selectedColumns.length === availableColumns.length
}

const handleColumnChange = () => {
  updateSelectAllStatus()
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleClose = () => {
  visible.value = false
}

// 导出退货单
const handleExport = async () => {
  if (!props.returnOrderId) {
    ElMessage.error('缺少退货单ID')
    return
  }

  if (exportConfig.selectedColumns.length === 0) {
    ElMessage.warning('请至少选择一列进行导出')
    return
  }

  exporting.value = true
  try {
    const response = await returnApi.export(props.returnOrderId, {
      format: exportConfig.format,
      columns: exportConfig.selectedColumns,
      include_header: exportConfig.includeHeader
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(response)
    const link = document.createElement('a')
    link.href = url
    link.download = `退货单_${props.returnOrderName || props.returnOrderId}.${exportConfig.format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped>
.export-columns-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.select-all-container {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.select-all-checkbox {
  margin-bottom: 0;
}

.columns-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.column-category {
  margin-bottom: 16px;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.category-title {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.category-select-all {
  font-size: 12px;
  color: #409eff;
}

.column-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 20px;
}

.column-checkboxes.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 16px;
  padding-left: 20px;
}

.column-checkboxes .el-checkbox {
  margin-right: 0;
}
</style>
