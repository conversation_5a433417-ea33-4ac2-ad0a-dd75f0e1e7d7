import{I as a}from"./index-3d4c440c.js";function t(t){return a({url:"/api/v1/product-categories",method:"GET",params:t})}async function e(){const t=await a.get("/api/v1/product-categories",{params:{per_page:999}});return t&&t.items?t.items:[]}async function r(t){return await a.post("/api/v1/product-categories",t)}async function o(t,e){return await a.put(`/api/v1/product-categories/${t}`,e)}async function s(t){return await a.delete(`/api/v1/product-categories/${t}`)}async function i(){console.log("请求产品分类树数据...");try{const t=await a({url:"/api/v1/product-categories",method:"get",params:{tree:!0,timestamp:(new Date).getTime()}});console.log("产品分类树响应原始数据:",t);let e=[];if(Array.isArray(t))e=t,console.log("API直接返回分类数组格式1");else if(t&&t.items&&Array.isArray(t.items))e=t.items,console.log("API返回items数组格式2");else if(t&&t.data)Array.isArray(t.data)?(e=t.data,console.log("API返回data数组格式3")):t.data.items&&Array.isArray(t.data.items)&&(e=t.data.items,console.log("API返回data.items数组格式4"));else if(t&&"object"==typeof t)for(const a in t)if(Array.isArray(t[a])){console.log(`找到了数组属性 ${a}，尝试使用`),e=t[a];break}return console.log(`分类树数据提取结果: ${e.length}个类别`),e}catch(t){return console.error("获取产品分类树失败:",t),[]}}export{e as a,r as c,s as d,i as g,t as l,o as u};
