import{r as e,w as l,f as a,o as d,c as t,i as u,h as n,a as o,l as i,k as s,g as r,j as m,W as c,U as p,Z as f}from"./index-3d4c440c.js";import{_}from"./_plugin-vue_export-helper-1b428a4d.js";const b={class:"bank-accounts"},V={class:"card-header flex-between"},h={key:1,class:"empty-block"},v=_({__name:"CustomerBankAccounts",props:{modelValue:{type:Array,required:!0,default:()=>[]},customerName:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(_,{expose:v,emit:g}){const w=_,y=g,k=e(0);l((()=>w.modelValue),((e,l)=>{if(e&&e.length>0){let a=!1;for(let l=0;l<e.length;l++)e[l].is_default&&(a?e[l].is_default=!1:(k.value=l,a=!0));a?JSON.stringify(e)!==JSON.stringify(l)&&y("update:modelValue",[...e]):(e[0].is_default=!0,k.value=0,y("update:modelValue",[...e]))}else k.value=-1}),{immediate:!0,deep:!0});const U=()=>{const e=[...w.modelValue],l={bank_name:"",account_name:w.customerName||"",account_number:"",is_default:0===e.length};if(e.push(l),l.is_default){for(let l=0;l<e.length-1;l++)e[l].is_default=!1;k.value=e.length-1}else e.length>0&&!e.some((e=>e.is_default))&&(e[0].is_default=!0,k.value=0);y("update:modelValue",e)},x=()=>{const e=w.modelValue.map(((e,l)=>({...e,is_default:l===k.value})));y("update:modelValue",e)};return v({validate:()=>{if(0===w.modelValue.length)return p.warning("请至少添加一个银行账户"),!1;for(const e of w.modelValue){if(!e.bank_name)return p.warning("请填写开户行名称"),!1;if(!e.account_name)return p.warning("请填写账户名称"),!1;if(!e.account_number)return p.warning("请填写账号"),!1}return!0}}),(e,l)=>{const v=a("el-icon"),g=a("el-button"),C=a("el-input"),N=a("el-table-column"),S=a("el-radio"),A=a("el-table"),I=a("el-card");return d(),t("div",b,[u(I,null,{header:n((()=>[o("div",V,[l[2]||(l[2]=o("h3",null,"银行账户",-1)),u(g,{type:"primary",onClick:U},{default:n((()=>[u(v,null,{default:n((()=>[u(i(f))])),_:1}),l[1]||(l[1]=s(" 添加账户 "))])),_:1})])])),default:n((()=>[_.modelValue.length>0?(d(),r(A,{key:0,data:_.modelValue,border:"",style:{width:"100%"}},{default:n((()=>[u(N,{label:"开户行","min-width":"180"},{default:n((({row:e})=>[u(C,{modelValue:e.bank_name,"onUpdate:modelValue":l=>e.bank_name=l,placeholder:"请输入开户行",disabled:_.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(N,{label:"账户名称","min-width":"180"},{default:n((({row:e})=>[u(C,{modelValue:e.account_name,"onUpdate:modelValue":l=>e.account_name=l,placeholder:"请输入账户名称",disabled:_.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(N,{label:"账号","min-width":"180"},{default:n((({row:e})=>[u(C,{modelValue:e.account_number,"onUpdate:modelValue":l=>e.account_number=l,placeholder:"请输入账号",disabled:_.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(N,{label:"默认账户",width:"100"},{default:n((({row:e,$index:a})=>[u(S,{modelValue:k.value,"onUpdate:modelValue":l[0]||(l[0]=e=>k.value=e),label:a,onChange:x,disabled:_.disabled},{default:n((()=>l[3]||(l[3]=[s(" 默认 ")]))),_:2},1032,["modelValue","label","disabled"])])),_:1}),_.disabled?m("",!0):(d(),r(N,{key:0,label:"操作",width:"120"},{default:n((({$index:e})=>[u(g,{type:"danger",link:"",onClick:l=>{return a=e,void c.confirm("确定要删除该银行账户吗？","删除确认").then((()=>{const e=[...w.modelValue];e.splice(a,1)[0].is_default&&e.length>0?(e[0].is_default=!0,k.value=0):0===e.length?k.value=-1:(k.value=e.findIndex((e=>e.is_default)),-1===k.value&&e.length>0&&(e[0].is_default=!0,k.value=0)),y("update:modelValue",e),p.success("银行账户已删除")})).catch((()=>{}));var a}},{default:n((()=>l[4]||(l[4]=[s("删除")]))),_:2},1032,["onClick"])])),_:1}))])),_:1},8,["data"])):(d(),t("div",h,l[5]||(l[5]=[o("span",{class:"empty-text"},"暂无银行账户信息",-1)])))])),_:1})])}}},[["__scopeId","data-v-899483b8"]]),g={class:"delivery-addresses"},w={class:"card-header flex-between"},y={key:1,class:"empty-block"},k=_({__name:"CustomerDeliveryAddresses",props:{modelValue:{type:Array,required:!0,default:()=>[]},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(_,{expose:b,emit:V}){const h=_,v=V,k=e(0);l((()=>h.modelValue),((e,l)=>{if(e&&e.length>0){let a=!1;for(let l=0;l<e.length;l++)e[l].is_default&&(a?e[l].is_default=!1:(k.value=l,a=!0));a?JSON.stringify(e)!==JSON.stringify(l)&&v("update:modelValue",[...e]):(e[0].is_default=!0,k.value=0,v("update:modelValue",[...e]))}else k.value=-1}),{immediate:!0,deep:!0});const U=()=>{const e=[...h.modelValue],l={province:"",city:"",district:"",detailed_address:"",contact_person:"",contact_phone:"",is_default:0===e.length};if(e.push(l),l.is_default){for(let l=0;l<e.length-1;l++)e[l].is_default=!1;k.value=e.length-1}else e.length>0&&!e.some((e=>e.is_default))&&(e[0].is_default=!0,k.value=0);v("update:modelValue",e)},x=()=>{const e=h.modelValue.map(((e,l)=>({...e,is_default:l===k.value})));v("update:modelValue",e)};return b({validate:()=>{if(0===h.modelValue.length)return p.warning("请至少添加一个送货地址"),!1;for(const e of h.modelValue){if(!e.province)return p.warning("请填写省份"),!1;if(!e.city)return p.warning("请填写城市"),!1;if(!e.district)return p.warning("请填写区县"),!1;if(!e.detailed_address)return p.warning("请填写详细地址"),!1;if(!e.contact_person)return p.warning("请填写联系人"),!1;if(!e.contact_phone)return p.warning("请填写联系电话"),!1}return!0}}),(e,l)=>{const b=a("el-icon"),V=a("el-button"),C=a("el-input"),N=a("el-table-column"),S=a("el-radio"),A=a("el-table"),I=a("el-card");return d(),t("div",g,[u(I,null,{header:n((()=>[o("div",w,[l[2]||(l[2]=o("h3",null,"送货地址",-1)),u(V,{type:"primary",onClick:U},{default:n((()=>[u(b,null,{default:n((()=>[u(i(f))])),_:1}),l[1]||(l[1]=s(" 添加地址 "))])),_:1})])])),default:n((()=>[_.modelValue.length>0?(d(),r(A,{key:0,data:_.modelValue,border:"",style:{width:"100%"}},{default:n((()=>[u(N,{label:"省份",width:"120"},{default:n((({row:e})=>[u(C,{modelValue:e.province,"onUpdate:modelValue":l=>e.province=l,placeholder:"请输入省份",disabled:_.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(N,{label:"城市",width:"120"},{default:n((({row:e})=>[u(C,{modelValue:e.city,"onUpdate:modelValue":l=>e.city=l,placeholder:"请输入城市",disabled:_.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(N,{label:"区县",width:"120"},{default:n((({row:e})=>[u(C,{modelValue:e.district,"onUpdate:modelValue":l=>e.district=l,placeholder:"请输入区县",disabled:_.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(N,{label:"详细地址","min-width":"200"},{default:n((({row:e})=>[u(C,{modelValue:e.detailed_address,"onUpdate:modelValue":l=>e.detailed_address=l,placeholder:"请输入详细地址",disabled:_.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(N,{label:"联系人",width:"120"},{default:n((({row:e})=>[u(C,{modelValue:e.contact_person,"onUpdate:modelValue":l=>e.contact_person=l,placeholder:"请输入联系人",disabled:_.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(N,{label:"联系电话",width:"150"},{default:n((({row:e})=>[u(C,{modelValue:e.contact_phone,"onUpdate:modelValue":l=>e.contact_phone=l,placeholder:"请输入联系电话",disabled:_.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(N,{label:"默认地址",width:"100"},{default:n((({row:e,$index:a})=>[u(S,{modelValue:k.value,"onUpdate:modelValue":l[0]||(l[0]=e=>k.value=e),label:a,onChange:x,disabled:_.disabled},{default:n((()=>l[3]||(l[3]=[s(" 默认 ")]))),_:2},1032,["modelValue","label","disabled"])])),_:1}),_.disabled?m("",!0):(d(),r(N,{key:0,label:"操作",width:"120"},{default:n((({$index:e})=>[u(V,{type:"danger",link:"",onClick:l=>{return a=e,void c.confirm("确定要删除该送货地址吗？","删除确认").then((()=>{const e=[...h.modelValue];e.splice(a,1)[0].is_default&&e.length>0?(e[0].is_default=!0,k.value=0):0===e.length?k.value=-1:(k.value=e.findIndex((e=>e.is_default)),-1===k.value&&e.length>0&&(e[0].is_default=!0,k.value=0)),v("update:modelValue",e),p.success("送货地址已删除")})).catch((()=>{}));var a}},{default:n((()=>l[4]||(l[4]=[s("删除")]))),_:2},1032,["onClick"])])),_:1}))])),_:1},8,["data"])):(d(),t("div",y,l[5]||(l[5]=[o("span",{class:"empty-text"},"暂无送货地址信息",-1)])))])),_:1})])}}},[["__scopeId","data-v-d11d7ac6"]]);export{v as C,k as a};
