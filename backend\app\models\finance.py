"""
财务管理相关模型
包括收款记录、退款记录、对账单、应收账款
"""
from app import db
from datetime import datetime
from app.models.base import BaseModel


class PaymentRecord(BaseModel):
    """收款记录表"""
    __tablename__ = 'payment_records'
    
    id = db.Column(db.Integer, primary_key=True)
    payment_number = db.Column(db.String(50), nullable=False, unique=True, default='', comment='收款单号')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    payment_date = db.Column(db.Date, nullable=False, comment='收款日期')
    amount = db.Column(db.Float, nullable=False, comment='收款金额')
    payment_method = db.Column(db.String(50), nullable=False, comment='支付方式：bank_transfer, cash, online_payment等')
    reference_number = db.Column(db.String(100), comment='交易流水号')
    notes = db.Column(db.Text, comment='备注')
    created_by = db.Column(db.String(50), comment='创建人')
    bank_account = db.Column(db.String(200), comment='收款账户')
    status = db.Column(db.String(20), default='待确认', comment='状态：待确认、已确认、已取消')
    
    # 关联关系
    order = db.relationship('Order', backref=db.backref('payment_records', lazy=True))
    
    def __repr__(self):
        return f'<PaymentRecord {self.id} - Order {self.order_id}>'


class RefundRecord(BaseModel):
    """退款记录表"""
    __tablename__ = 'refund_records'

    id = db.Column(db.Integer, primary_key=True)
    return_order_id = db.Column(db.Integer, db.ForeignKey('return_orders.id'), nullable=False, comment='退货单ID')
    refund_no = db.Column(db.String(50), nullable=False, unique=True, comment='退款单号')
    refund_date = db.Column(db.Date, nullable=False, comment='退款日期')
    amount = db.Column(db.Float, nullable=False, comment='退款金额')
    refund_method = db.Column(db.String(50), nullable=False, comment='退款方式：bank_transfer, cash, original_channel等')
    reference_number = db.Column(db.String(100), comment='交易流水号')
    bank_account = db.Column(db.String(200), comment='退款账户')
    status = db.Column(db.String(20), default='待处理', comment='状态：待处理、已退款、已取消')
    notes = db.Column(db.Text, comment='备注')
    created_by = db.Column(db.String(50), comment='创建人')

    # 关联关系
    return_order = db.relationship('ReturnOrder', backref=db.backref('refund_records', lazy=True))

    def __repr__(self):
        return f'<RefundRecord {self.id} - ReturnOrder {self.return_order_id}>'


class StatementRefund(BaseModel):
    """对账单退款记录模型"""
    __tablename__ = 'statement_refunds'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    statement_id = db.Column(db.Integer, db.ForeignKey('statements.id'), nullable=False, comment='对账单ID')
    refund_number = db.Column(db.String(50), nullable=False, unique=True, comment='退款单号')
    refund_date = db.Column(db.Date, nullable=False, comment='退款日期')
    amount = db.Column(db.Numeric(12, 2), nullable=False, comment='退款金额')
    refund_method = db.Column(db.String(50), nullable=False, comment='退款方式：bank_transfer、cash、balance等')
    refund_target = db.Column(db.String(20), nullable=False, default='direct', comment='退款目标：direct（直接退款）、balance（退回余额）')
    reference_number = db.Column(db.String(100), nullable=True, comment='交易流水号')
    bank_account = db.Column(db.String(200), nullable=True, comment='退款账户')
    balance_transaction_id = db.Column(db.Integer, db.ForeignKey('balance_transactions.id'), nullable=True, comment='关联的余额交易ID')
    notes = db.Column(db.Text, nullable=True, comment='备注')
    voucher_files = db.Column(db.Text, nullable=True, comment='退款凭据文件（JSON格式存储）')
    status = db.Column(db.String(20), nullable=False, default='待处理', comment='状态：待处理、已退款、已取消')
    created_by = db.Column(db.String(50), nullable=True, comment='创建人')

    # 关联关系
    statement = db.relationship('Statement', backref=db.backref('refunds', lazy=True))
    balance_transaction = db.relationship('BalanceTransaction', backref=db.backref('statement_refund', uselist=False))

    def __repr__(self):
        return f'<StatementRefund {self.refund_number}: Statement {self.statement_id} - {self.amount}>'

    @classmethod
    def create_direct_refund(cls, statement_id: int, amount, refund_method: str,
                           refund_date=None, reference_number: str = None,
                           bank_account: str = None, notes: str = None, created_by: str = None,
                           voucher_files: str = None):
        """创建直接退款记录"""
        from decimal import Decimal
        import uuid

        if refund_date is None:
            refund_date = datetime.now().date()

        # 生成退款单号
        refund_number = f"TK{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4().int)[:6]}"

        refund = cls(
            statement_id=statement_id,
            refund_number=refund_number,
            refund_date=refund_date,
            amount=Decimal(str(amount)),
            refund_method=refund_method,
            refund_target='direct',
            reference_number=reference_number,
            bank_account=bank_account,
            notes=notes,
            voucher_files=voucher_files,
            created_by=created_by
        )
        return refund

    @classmethod
    def create_balance_refund(cls, statement_id: int, amount, customer_id: int,
                            refund_date=None, notes: str = None, created_by: str = None):
        """创建余额退款记录"""
        from decimal import Decimal
        import uuid

        if refund_date is None:
            refund_date = datetime.now().date()

        # 生成退款单号
        refund_number = f"TK{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4().int)[:6]}"

        # 获取或创建客户余额
        from .payment import CustomerBalance
        customer_balance = CustomerBalance.query.filter_by(customer_id=customer_id).first()
        if not customer_balance:
            customer_balance = CustomerBalance(customer_id=customer_id, balance=0.00)
            db.session.add(customer_balance)
            db.session.flush()  # 确保获得ID

        # 增加余额
        transaction = customer_balance.add_balance(
            amount=Decimal(str(amount)),
            description=f"对账单退款 {statement_id}",
            reference_type='statement_refund',
            reference_id=statement_id
        )

        # 创建退款记录
        refund = cls(
            statement_id=statement_id,
            refund_number=refund_number,
            refund_date=refund_date,
            amount=Decimal(str(amount)),
            refund_method='balance',
            refund_target='balance',
            balance_transaction_id=transaction.id,
            notes=notes,
            created_by=created_by
        )
        return refund


class StatementDeliveryNote(db.Model):
    """对账单-发货单关联表"""
    __tablename__ = 'statement_delivery_notes'
    
    id = db.Column(db.Integer, primary_key=True)
    statement_id = db.Column(db.Integer, db.ForeignKey('statements.id'), nullable=False, comment='对账单ID')
    delivery_note_id = db.Column(db.Integer, db.ForeignKey('delivery_notes.id'), nullable=False, comment='发货单ID')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    
    # 关联关系
    statement = db.relationship('Statement', back_populates='statement_delivery_notes', overlaps="delivery_notes")
    delivery_note = db.relationship('DeliveryNote', back_populates='statement_delivery_notes', overlaps="statements")
    
    def __repr__(self):
        return f'<StatementDeliveryNote {self.id}>'


class Statement(BaseModel):
    """对账单表"""
    __tablename__ = 'statements'
    
    id = db.Column(db.Integer, primary_key=True)
    statement_number = db.Column(db.String(50), nullable=False, unique=True, comment='对账单号')
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False, comment='客户ID')
    statement_date = db.Column(db.Date, nullable=False, default=datetime.now().date(), comment='对账日期')
    status = db.Column(db.String(20), nullable=False, default='待确认', comment='状态(待确认, 已确认, 已收款, 已取消)')
    due_date = db.Column(db.Date, nullable=True, comment='应付款日期')
    total_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0, comment='总金额')
    discount_amount = db.Column(db.Numeric(12, 2), default=0.0, comment='优惠金额')
    adjusted_total_amount = db.Column(db.Numeric(12, 2), nullable=False, comment='调整后总金额')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 收款状态字段
    paid_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0.00, comment='已付金额')
    settlement_date = db.Column(db.Date, nullable=True, comment='结清日期')
    
    # 关联关系
    customer = db.relationship('Customer', backref=db.backref('statements', lazy=True))
    statement_delivery_notes = db.relationship('StatementDeliveryNote', back_populates='statement', cascade='all, delete-orphan', overlaps="delivery_notes")
    delivery_notes = db.relationship('DeliveryNote', secondary='statement_delivery_notes', back_populates='statements', overlaps="statement_delivery_notes,statement,delivery_note")
    statement_return_orders = db.relationship('StatementReturnOrder', back_populates='statement', cascade='all, delete-orphan', overlaps="return_orders")
    return_orders = db.relationship('ReturnOrder', secondary='statement_return_orders', back_populates='statements', overlaps="statement_return_orders,statement,return_order")
    # 注意：新逻辑下，应收账款不再直接关联对账单，而是通过发货单/退货单关联
    payments = db.relationship('StatementPayment', backref=db.backref('statement', lazy=True), cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Statement {self.statement_number}>'
    
    def calculate_total_amount(self) -> float:
        """计算对账单总金额，基于所有关联的发货单和退货单"""
        total = 0.0

        # 加上发货单金额
        for delivery_note in self.delivery_notes:
            # 直接使用发货单的总金额（已考虑折扣和税率）
            total += float(delivery_note.total_amount or 0)

        # 减去退货单金额
        for return_order in self.return_orders:
            # 计算退货单金额（需要考虑折扣和税率）
            return_amount = 0.0
            for item in return_order.items:
                if item.order_product:
                    from decimal import Decimal
                    unit_price = Decimal(str(item.order_product.unit_price or 0))
                    discount_rate = Decimal(str(item.order_product.discount or 0)) / Decimal('100')
                    tax_rate = Decimal(str(item.order_product.tax_rate or 0)) / Decimal('100')
                    quantity = Decimal(str(item.quantity or 0))

                    actual_price = unit_price * (Decimal('1') - discount_rate) * (Decimal('1') + tax_rate)
                    item_amount = actual_price * quantity
                    return_amount += float(item_amount)

            total -= return_amount

        return total

    def calculate_adjusted_total_amount(self) -> float:
        """计算调整后总金额（总金额 - 优惠金额）"""
        base_total = self.calculate_total_amount()
        discount = float(self.discount_amount or 0)
        return base_total - discount
    
    def confirm(self, due_date):
        """确认对账单"""
        if self.status == '已确认':
            raise ValueError("对账单已经是确认状态")

        self.status = '已确认'
        self.due_date = due_date
        self.updated_at = datetime.now()

        # 新逻辑：对账单确认后创建应收账款
        self._create_or_update_receivable()
        return True

    def _create_or_update_receivable(self):
        """创建或更新应收账款（基于对账单调整后金额 - 已付金额）"""
        from decimal import Decimal

        # 计算应收账款金额 = 调整后金额 - 已付金额
        adjusted_total = Decimal(str(self.adjusted_total_amount or 0))
        paid_amount = Decimal(str(self.paid_amount or 0))
        receivable_amount = adjusted_total - paid_amount

        # 如果金额为0或负数，不创建应收账款
        if receivable_amount <= 0:
            return None

        # 查找或创建应收账款记录
        receivable = StatementReceivable.query.filter_by(statement_id=self.id).first()

        if receivable:
            # 更新现有记录
            receivable.amount = receivable_amount
            receivable.due_date = self.due_date
            receivable.updated_at = datetime.now()
        else:
            # 创建新记录
            receivable = StatementReceivable(
                statement_id=self.id,
                customer_id=self.customer_id,
                amount=receivable_amount,
                due_date=self.due_date or self.statement_date
            )
            db.session.add(receivable)

        return receivable

    def update_status_only(self):
        """仅更新状态，不添加收款"""
        from decimal import Decimal

        # 使用计算方法确保调整后总金额的准确性
        total = Decimal(str(self.calculate_adjusted_total_amount()))
        paid = Decimal(str(self.paid_amount or 0))

        # 修复负金额对账单的状态计算逻辑
        if total >= 0:
            # 正金额对账单（正常收款）
            if paid <= 0:
                # 如果没有收款，保持原状态（待确认或已确认）
                pass
            elif paid < total:
                self.status = '部分收款'
            elif paid >= total:
                self.status = '已结清'
                if not self.settlement_date:
                    self.settlement_date = datetime.now().date()
        else:
            # 负金额对账单（退款场景）
            if paid >= 0:
                # 如果没有退款，保持原状态（待确认或已确认）
                pass
            elif abs(paid) < abs(total):
                self.status = '部分收款'  # 部分退款
            elif abs(paid) >= abs(total):
                self.status = '已结清'  # 完全退款
                if not self.settlement_date:
                    self.settlement_date = datetime.now().date()

    def add_payment(self, amount, payment_method='direct', payment_source='direct', **kwargs):
        """添加收款记录并更新付款状态"""
        from decimal import Decimal

        # 更新已付金额
        self.paid_amount = Decimal(str(self.paid_amount or 0)) + Decimal(str(amount))

        # 更新对账单状态（只使用一个status字段）
        # 使用计算方法确保调整后总金额的准确性
        total = Decimal(str(self.calculate_adjusted_total_amount()))
        paid = Decimal(str(self.paid_amount))

        # 修复负金额对账单的状态计算逻辑
        if total >= 0:
            # 正金额对账单（正常收款）
            if paid <= 0:
                # 如果没有收款，保持原状态（待确认或已确认）
                pass
            elif paid < total:
                self.status = '部分收款'
                # 部分收款时，同步更新相关单据为部分结清状态
                self._update_related_documents_partial_settlement()
            elif paid >= total:
                self.status = '已结清'
                if not self.settlement_date:
                    self.settlement_date = datetime.now().date()
                # 完全收款时，自动结清相关单据并更新应收账款
                self._settle_related_documents()
        else:
            # 负金额对账单（退款场景）
            if paid >= 0:
                # 如果没有退款，保持原状态（待确认或已确认）
                pass
            elif abs(paid) < abs(total):
                self.status = '部分收款'  # 部分退款
                # 部分退款时，同步更新相关单据为部分结清状态
                self._update_related_documents_partial_settlement()
            elif abs(paid) >= abs(total):
                self.status = '已结清'  # 完全退款
                if not self.settlement_date:
                    self.settlement_date = datetime.now().date()
                # 完全退款时，自动结清相关单据并更新应收账款
                self._settle_related_documents()

        # 收款后更新应收账款
        self._update_receivable_after_payment()

        self.updated_at = datetime.now()

    def add_refund(self, amount, refund_method='direct', refund_target='direct', **kwargs):
        """添加退款记录并更新付款状态"""
        from decimal import Decimal

        # 更新已付金额（减少）
        self.paid_amount = Decimal(str(self.paid_amount or 0)) - Decimal(str(amount))

        # 重新计算状态（修复负金额对账单的状态计算逻辑）
        total = Decimal(str(self.calculate_adjusted_total_amount()))
        paid = Decimal(str(self.paid_amount))

        if total >= 0:
            # 正金额对账单（正常收款）
            if paid <= 0:
                self.status = '已确认'  # 正金额但无收款，回到已确认状态
            elif paid < total:
                self.status = '部分收款'
            elif paid >= total:
                self.status = '已结清'
        else:
            # 负金额对账单（退款场景）
            if paid >= 0:
                self.status = '已确认'  # 负金额但无退款，回到已确认状态
            elif abs(paid) < abs(total):
                self.status = '部分收款'  # 部分退款
            elif abs(paid) >= abs(total):
                self.status = '已结清'  # 完全退款

        self.updated_at = datetime.now()
        return True

    def calculate_refund_amount(self):
        """计算需要退款的金额"""
        from decimal import Decimal

        total = Decimal(str(self.calculate_adjusted_total_amount()))
        paid = Decimal(str(self.paid_amount or 0))

        # 如果已付金额大于实际金额，返回差额
        if paid > total:
            return paid - total
        return Decimal('0')

    def needs_refund(self):
        """判断是否需要退款"""
        return self.calculate_refund_amount() > 0

    def settle_statement(self):
        """结清对账单，同时结清相关的发货单和退货单"""
        if self.status != '已结清':
            raise ValueError("对账单未完全收款，无法结清")

        # 设置结清日期
        if not self.settlement_date:
            self.settlement_date = datetime.now().date()

        # 结清相关单据并更新应收账款
        self._settle_related_documents()
        self.updated_at = datetime.now()

    def _settle_related_documents(self):
        """结清相关的发货单和退货单，并更新应收账款"""
        settlement_date = self.settlement_date or datetime.now().date()

        # 结清所有关联的发货单（排除已取消的）
        for delivery_note in self.delivery_notes:
            if delivery_note.status != '已取消' and delivery_note.settlement_status in ['未结清', '部分结清']:
                delivery_note.settle(settlement_date)

        # 结清所有关联的退货单（排除已取消的）
        for return_order in self.return_orders:
            if return_order.status != '已取消' and return_order.settlement_status in ['未结清', '部分结清']:
                return_order.settle(settlement_date)

        # 更新客户应收账款
        receivable = Receivable.get_or_create_for_customer(self.customer_id)

        # 结清应收账款明细
        delivery_note_ids = [dn.id for dn in self.delivery_notes]
        return_order_ids = [ro.id for ro in self.return_orders]

        if delivery_note_ids or return_order_ids:
            receivable.settle_items(
                delivery_note_ids=delivery_note_ids,
                return_order_ids=return_order_ids
            )

    def _update_related_documents_partial_settlement(self):
        """部分收款时，更新相关发货单和退货单为部分结清状态"""
        # 更新所有关联的发货单为部分结清状态（排除已取消的）
        for delivery_note in self.delivery_notes:
            if delivery_note.status != '已取消' and delivery_note.settlement_status == '未结清':
                delivery_note.settlement_status = '部分结清'
                delivery_note.updated_at = datetime.now()
                # 更新关联订单的收款状态
                if delivery_note.order:
                    delivery_note.order.auto_update_payment_status()

        # 更新所有关联的退货单为部分结清状态（排除已取消的）
        for return_order in self.return_orders:
            if return_order.status != '已取消' and return_order.settlement_status == '未结清':
                return_order.settlement_status = '部分结清'
                return_order.updated_at = datetime.now()
                # 更新关联订单的收款状态
                if return_order.order:
                    return_order.order.auto_update_payment_status()

    def _update_receivable_after_payment(self):
        """收款后更新应收账款"""
        from decimal import Decimal

        # 查找对应的应收账款记录
        receivable = StatementReceivable.query.filter_by(statement_id=self.id).first()
        if not receivable:
            return

        # 重新计算应收账款金额 = 调整后金额 - 已付金额
        adjusted_total = Decimal(str(self.adjusted_total_amount or 0))
        paid_amount = Decimal(str(self.paid_amount or 0))
        new_receivable_amount = adjusted_total - paid_amount

        if new_receivable_amount <= 0:
            # 如果应收金额为0或负数，删除应收账款记录
            db.session.delete(receivable)
        else:
            # 更新应收账款金额和状态
            receivable.amount = new_receivable_amount
            receivable.paid_amount = paid_amount

            # 更新应收账款状态
            if paid_amount <= 0:
                receivable.status = '未支付'
            elif paid_amount >= adjusted_total:
                receivable.status = '已支付'
            else:
                receivable.status = '部分支付'

            receivable.last_payment_date = datetime.now().date()
            receivable.updated_at = datetime.now()

    def is_settled(self):
        """检查对账单是否已结清"""
        return self.status == '已结清' and self.settlement_date is not None


class StatementReceivable(db.Model):
    """基于对账单的应收账款模型"""
    __tablename__ = 'statement_receivables'

    id = db.Column(db.Integer, primary_key=True)
    statement_id = db.Column(db.Integer, db.ForeignKey('statements.id'), nullable=False, unique=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)  # 应收金额
    paid_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)  # 已付金额
    due_date = db.Column(db.Date, nullable=False)  # 到期日期
    status = db.Column(db.String(20), nullable=False, default='未支付')  # 未支付, 部分支付, 已支付, 逾期
    last_payment_date = db.Column(db.Date)  # 最后付款日期
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联关系
    statement = db.relationship('Statement', backref=db.backref('receivable', uselist=False))
    customer = db.relationship('Customer', backref='statement_receivables')

    def __repr__(self):
        return f'<StatementReceivable {self.id} - Statement {self.statement_id}: ¥{self.amount}>'

    @property
    def outstanding_amount(self):
        """未付金额"""
        from decimal import Decimal
        return Decimal(str(self.amount)) - Decimal(str(self.paid_amount or 0))

    @property
    def is_overdue(self):
        """是否逾期"""
        from datetime import date
        return self.due_date < date.today() and self.outstanding_amount > 0

    def update_status(self):
        """更新状态"""
        from decimal import Decimal
        from datetime import date

        amount = Decimal(str(self.amount))
        paid = Decimal(str(self.paid_amount or 0))

        if paid <= 0:
            self.status = '未支付'
        elif paid >= amount:
            self.status = '已支付'
        else:
            self.status = '部分支付'

        # 检查是否逾期
        if self.due_date < date.today() and paid < amount:
            self.status = '逾期'

        self.updated_at = datetime.now()


class Receivable(BaseModel):
    """应收账款表（按客户维度）"""
    __tablename__ = 'receivables'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False, unique=True, comment='客户ID')
    amount = db.Column(db.Numeric(12, 2), nullable=False, default=0, comment='当前应收金额')

    # 关联关系
    customer = db.relationship('Customer', backref=db.backref('receivable', uselist=False))

    def __repr__(self):
        return f'<Receivable {self.id} - Customer {self.customer_id}: ¥{self.amount}>'

    @classmethod
    def get_or_create_for_customer(cls, customer_id):
        """获取或创建客户的应收账款记录"""
        receivable = cls.query.filter_by(customer_id=customer_id).first()
        if not receivable:
            receivable = cls(customer_id=customer_id, amount=0)
            db.session.add(receivable)
            db.session.flush()  # 获取ID
        return receivable

    def recalculate_amount(self):
        """重新计算应收账款金额（基于未结清的明细）"""
        total = db.session.query(db.func.sum(ReceivableDetail.amount)).filter(
            ReceivableDetail.customer_id == self.customer_id,
            ReceivableDetail.settlement_status == '未结清'
        ).scalar() or 0

        self.amount = total
        self.updated_at = datetime.now()
        return self.amount

    def add_delivery_note(self, delivery_note):
        """添加发货单到应收账款"""
        # 检查是否已存在
        existing = ReceivableDetail.query.filter_by(
            source_type='delivery_note',
            source_id=delivery_note.id
        ).first()

        if existing:
            return existing

        detail = ReceivableDetail(
            customer_id=self.customer_id,
            source_type='delivery_note',
            source_id=delivery_note.id,
            amount=delivery_note.total_amount,
            settlement_status=delivery_note.settlement_status
        )
        db.session.add(detail)

        # 如果未结清，增加应收金额
        if delivery_note.settlement_status == '未结清':
            from decimal import Decimal
            self.amount += Decimal(str(delivery_note.total_amount))
            self.updated_at = datetime.now()

        return detail

    def add_return_order(self, return_order):
        """添加退货单到应收账款（减少金额）"""
        # 检查是否已存在
        existing = ReceivableDetail.query.filter_by(
            source_type='return_order',
            source_id=return_order.id
        ).first()

        if existing:
            return existing

        # 计算退货单金额
        return_amount = self._calculate_return_order_amount(return_order)

        detail = ReceivableDetail(
            customer_id=self.customer_id,
            source_type='return_order',
            source_id=return_order.id,
            amount=-return_amount,  # 负数
            settlement_status=return_order.settlement_status
        )
        db.session.add(detail)

        # 如果未结清，减少应收金额
        if return_order.settlement_status == '未结清':
            from decimal import Decimal
            self.amount -= Decimal(str(return_amount))
            self.updated_at = datetime.now()

        return detail

    def _calculate_return_order_amount(self, return_order):
        """计算退货单金额"""
        total = 0.0
        for item in return_order.items:
            if item.order_product:
                from decimal import Decimal
                unit_price = Decimal(str(item.order_product.unit_price or 0))
                discount_rate = Decimal(str(item.order_product.discount or 0)) / Decimal('100')
                tax_rate = Decimal(str(item.order_product.tax_rate or 0)) / Decimal('100')
                quantity = Decimal(str(item.quantity or 0))

                actual_price = unit_price * (Decimal('1') - discount_rate) * (Decimal('1') + tax_rate)
                item_amount = actual_price * quantity
                total += float(item_amount)
        return total

    def settle_items(self, delivery_note_ids=None, return_order_ids=None):
        """结清指定的发货单和退货单"""
        from decimal import Decimal

        settlement_date = datetime.now().date()
        settled_amount = Decimal('0')

        # 结清发货单
        if delivery_note_ids:
            details = ReceivableDetail.query.filter(
                ReceivableDetail.customer_id == self.customer_id,
                ReceivableDetail.source_type == 'delivery_note',
                ReceivableDetail.source_id.in_(delivery_note_ids),
                ReceivableDetail.settlement_status == '未结清'
            ).all()

            for detail in details:
                detail.settlement_status = '已结清'
                detail.settlement_date = settlement_date
                detail.updated_at = datetime.now()
                settled_amount += Decimal(str(detail.amount))

        # 结清退货单
        if return_order_ids:
            details = ReceivableDetail.query.filter(
                ReceivableDetail.customer_id == self.customer_id,
                ReceivableDetail.source_type == 'return_order',
                ReceivableDetail.source_id.in_(return_order_ids),
                ReceivableDetail.settlement_status == '未结清'
            ).all()

            for detail in details:
                detail.settlement_status = '已结清'
                detail.settlement_date = settlement_date
                detail.updated_at = datetime.now()
                settled_amount += Decimal(str(detail.amount))  # 退货单金额是负数

        # 更新应收账款总额
        self.amount = Decimal(str(self.amount)) - settled_amount
        self.updated_at = datetime.now()

        return float(settled_amount)


class ReceivableDetail(BaseModel):
    """应收账款明细表"""
    __tablename__ = 'receivable_details'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False, comment='客户ID')
    source_type = db.Column(db.String(20), nullable=False, comment='来源类型: delivery_note, return_order')
    source_id = db.Column(db.Integer, nullable=False, comment='来源ID')
    amount = db.Column(db.Numeric(12, 2), nullable=False, comment='金额（发货单为正，退货单为负）')
    settlement_status = db.Column(db.String(20), nullable=False, default='未结清', comment='结清状态')
    settlement_date = db.Column(db.Date, nullable=True, comment='结清日期')

    # 关联关系
    customer = db.relationship('Customer')

    # 唯一约束
    __table_args__ = (
        db.UniqueConstraint('source_type', 'source_id', name='uq_receivable_detail_source'),
        db.CheckConstraint("source_type IN ('delivery_note', 'return_order')", name='ck_source_type'),
        db.CheckConstraint("settlement_status IN ('未结清', '已结清')", name='ck_settlement_status'),
    )

    def __repr__(self):
        return f'<ReceivableDetail {self.source_type}:{self.source_id} ¥{self.amount}>'

    @property
    def source_object(self):
        """获取来源对象"""
        if self.source_type == 'delivery_note':
            from .order import DeliveryNote
            return DeliveryNote.query.get(self.source_id)
        elif self.source_type == 'return_order':
            from .return_order import ReturnOrder
            return ReturnOrder.query.get(self.source_id)
        return None


class StatementReturnOrder(db.Model):
    """对账单退货单关联表"""
    __tablename__ = 'statement_return_orders'

    id = db.Column(db.Integer, primary_key=True)
    statement_id = db.Column(db.Integer, db.ForeignKey('statements.id'), nullable=False, comment='对账单ID')
    return_order_id = db.Column(db.Integer, db.ForeignKey('return_orders.id'), nullable=False, comment='退货单ID')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')

    # 关联关系
    statement = db.relationship('Statement', back_populates='statement_return_orders', overlaps="return_orders")
    return_order = db.relationship('ReturnOrder', back_populates='statement_return_orders', overlaps="statements")

    def __repr__(self):
        return f'<StatementReturnOrder {self.id}>'
