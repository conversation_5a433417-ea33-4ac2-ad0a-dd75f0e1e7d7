"""
客户管理API单元测试
"""
import pytest
import json
from app.models.customer import Customer


class TestCustomerAPI:
    """客户API测试类"""
    
    def test_get_customers_empty(self, client, db_session):
        """测试获取空客户列表"""
        response = client.get('/api/v1/customers')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取客户列表成功'
        assert data['data'] == []
        assert 'pagination' in data
    
    def test_get_customers_with_data(self, client, db_session, sample_customer):
        """测试获取有数据的客户列表"""
        response = client.get('/api/v1/customers')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert len(data['data']) == 1
        assert data['data'][0]['name'] == '测试公司'
        assert data['data'][0]['contact'] == '张三'
    
    def test_get_customers_with_pagination(self, client, db_session):
        """测试客户列表分页"""
        # 创建多个客户
        for i in range(5):
            customer = Customer(
                name=f'测试公司{i}',
                contact=f'联系人{i}',
                phone=f'1380013800{i}',
                email=f'test{i}@example.com',
                address=f'地址{i}'
            )
            db_session.add(customer)
        db_session.commit()
        
        # 测试分页
        response = client.get('/api/v1/customers?page=1&per_page=3')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']) == 3
        assert data['pagination']['page'] == 1
        assert data['pagination']['per_page'] == 3
        assert data['pagination']['total'] == 5
    
    def test_get_customers_with_search(self, client, db_session, sample_customer):
        """测试客户搜索"""
        response = client.get('/api/v1/customers?name=测试')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']) == 1
        assert data['data'][0]['name'] == '测试公司'
    
    def test_create_customer_success(self, client, db_session, test_data_generator):
        """测试成功创建客户"""
        customer_data = test_data_generator.customer_data()
        
        response = client.post('/api/v1/customers', 
                             json=customer_data,
                             content_type='application/json')
        assert response.status_code == 201
        
        data = response.get_json()
        assert data['code'] == 201
        assert data['message'] == '客户创建成功'
        assert data['data']['name'] == customer_data['name']
        assert data['data']['contact'] == customer_data['contact']
        
        # 验证数据库中确实创建了客户
        customer = Customer.query.filter_by(name=customer_data['name']).first()
        assert customer is not None
        assert customer.contact == customer_data['contact']
    
    def test_create_customer_validation_error(self, client, db_session):
        """测试创建客户时的验证错误"""
        # 缺少必填字段
        invalid_data = {
            'contact': '张三',
            'phone': '13800138000'
            # 缺少 name 字段
        }
        
        response = client.post('/api/v1/customers', 
                             json=invalid_data,
                             content_type='application/json')
        assert response.status_code == 400
        
        data = response.get_json()
        assert data['code'] == 400
        assert 'errors' in data
    
    def test_create_customer_duplicate_name(self, client, db_session, sample_customer):
        """测试创建重复名称的客户"""
        duplicate_data = {
            'name': '测试公司',  # 与sample_customer重复
            'contact': '李四',
            'phone': '13900139000',
            'email': '<EMAIL>',
            'address': '上海市浦东新区'
        }
        
        response = client.post('/api/v1/customers', 
                             json=duplicate_data,
                             content_type='application/json')
        assert response.status_code == 400
        
        data = response.get_json()
        assert data['code'] == 400
        assert '客户名称已存在' in data['message']
    
    def test_get_customer_detail_success(self, client, db_session, sample_customer):
        """测试成功获取客户详情"""
        response = client.get(f'/api/v1/customers/{sample_customer.id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['id'] == sample_customer.id
        assert data['data']['name'] == sample_customer.name
        assert data['data']['contact'] == sample_customer.contact
    
    def test_get_customer_detail_not_found(self, client, db_session):
        """测试获取不存在的客户详情"""
        response = client.get('/api/v1/customers/999')
        assert response.status_code == 404
        
        data = response.get_json()
        assert data['code'] == 404
        assert '客户不存在' in data['message']
    
    def test_update_customer_success(self, client, db_session, sample_customer):
        """测试成功更新客户"""
        update_data = {
            'name': '更新后的公司名称',
            'contact': '更新后的联系人',
            'phone': '13700137000'
        }
        
        response = client.put(f'/api/v1/customers/{sample_customer.id}',
                            json=update_data,
                            content_type='application/json')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['name'] == update_data['name']
        assert data['data']['contact'] == update_data['contact']
        
        # 验证数据库中的数据确实更新了
        updated_customer = Customer.query.get(sample_customer.id)
        assert updated_customer.name == update_data['name']
        assert updated_customer.contact == update_data['contact']
    
    def test_update_customer_not_found(self, client, db_session):
        """测试更新不存在的客户"""
        update_data = {
            'name': '不存在的客户',
            'contact': '联系人'
        }
        
        response = client.put('/api/v1/customers/999',
                            json=update_data,
                            content_type='application/json')
        assert response.status_code == 404
        
        data = response.get_json()
        assert data['code'] == 404
        assert '客户不存在' in data['message']
    
    def test_delete_customer_success(self, client, db_session, sample_customer):
        """测试成功删除客户"""
        customer_id = sample_customer.id
        
        response = client.delete(f'/api/v1/customers/{customer_id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert '客户删除成功' in data['message']
        
        # 验证数据库中的客户确实被删除了
        deleted_customer = Customer.query.get(customer_id)
        assert deleted_customer is None
    
    def test_delete_customer_not_found(self, client, db_session):
        """测试删除不存在的客户"""
        response = client.delete('/api/v1/customers/999')
        assert response.status_code == 404
        
        data = response.get_json()
        assert data['code'] == 404
        assert '客户不存在' in data['message']
    
    def test_customer_status_filter(self, client, db_session):
        """测试按状态筛选客户"""
        # 创建不同状态的客户
        active_customer = Customer(
            name='活跃客户',
            contact='张三',
            phone='13800138000',
            email='<EMAIL>',
            status='正常'
        )
        inactive_customer = Customer(
            name='非活跃客户',
            contact='李四',
            phone='13900139000',
            email='<EMAIL>',
            status='禁用'
        )
        db_session.add_all([active_customer, inactive_customer])
        db_session.commit()
        
        # 测试筛选活跃客户
        response = client.get('/api/v1/customers?status=正常')
        assert response.status_code == 200

        data = response.get_json()
        assert len(data['data']) == 1
        assert data['data'][0]['name'] == '活跃客户'
        assert data['data'][0]['status'] == '正常'
    
    def test_customer_level_filter(self, client, db_session):
        """测试按等级筛选客户"""
        # 创建不同等级的客户
        a_level_customer = Customer(
            name='A级客户',
            contact='张三',
            phone='13800138000',
            email='<EMAIL>',
            level='A'
        )
        b_level_customer = Customer(
            name='B级客户',
            contact='李四',
            phone='13900139000',
            email='<EMAIL>',
            level='B'
        )
        db_session.add_all([a_level_customer, b_level_customer])
        db_session.commit()
        
        # 测试筛选A级客户
        response = client.get('/api/v1/customers?level=A')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']) == 1
        assert data['data'][0]['name'] == 'A级客户'
        assert data['data'][0]['level'] == 'A'
