"""
产品图片管理API
提供产品图片的上传、获取、删除等功能
基于原项目API实现，确保与现有数据库结构100%兼容
"""
from flask import request, current_app, send_file, send_from_directory
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage
import os
import uuid
from datetime import datetime
import mimetypes

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from app.models.product import Product, ProductImage
from app.schemas.product import ProductImageSchema
from app.utils.response import (
    success_response, 
    error_response, 
    validation_error_response,
    not_found_response
)
from app import db

# 创建命名空间
api = Namespace('product-images', description='产品图片管理API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 定义API模型用于Swagger文档
product_image_model = api.model('ProductImage', {
    'id': fields.Integer(description='图片ID'),
    'product_id': fields.Integer(description='产品ID'),
    'url': fields.String(description='图片URL'),
    'alt_text': fields.String(description='图片描述'),
    'is_main': fields.Boolean(description='是否主图'),
    'sort_order': fields.Integer(description='排序'),
    'file_size': fields.Integer(description='文件大小'),
    'width': fields.Integer(description='图片宽度'),
    'height': fields.Integer(description='图片高度')
})

# 配置
UPLOAD_FOLDER = 'uploads/products'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    """确保上传文件夹存在"""
    if not os.path.exists(UPLOAD_FOLDER):
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def generate_filename(original_filename):
    """生成唯一的文件名"""
    ext = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else ''
    unique_filename = f"{uuid.uuid4().hex}.{ext}"
    return unique_filename

def get_image_dimensions(file_path):
    """获取图片尺寸"""
    if not PIL_AVAILABLE:
        return None, None
    try:
        with Image.open(file_path) as img:
            return img.size  # (width, height)
    except Exception:
        return None, None


@api.route('/products/<int:product_id>/images')
class ProductImageList(Resource):
    @api.doc('get_product_images')
    def get(self, product_id):
        """获取产品图片列表"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            images = ProductImage.query.filter_by(product_id=product_id).order_by(
                ProductImage.is_main.desc(),
                ProductImage.sort_order,
                ProductImage.created_at
            ).all()

            images_data = ProductImageSchema(many=True).dump(images)
            return make_response(success_response, images_data, "获取产品图片列表成功")

        except Exception as e:
            current_app.logger.error(f"获取产品图片列表失败: {str(e)}")
            return make_response(error_response, f"获取产品图片列表失败: {str(e)}")


@api.route('/products/<int:product_id>/images/upload')
class ProductImageUpload(Resource):
    @api.doc('upload_product_image')
    def post(self, product_id):
        """上传产品图片"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            if 'file' not in request.files:
                return make_response(error_response, "没有找到文件", code=400)

            file = request.files['file']
            if file.filename == '':
                return make_response(error_response, "没有选择文件", code=400)

            if not allowed_file(file.filename):
                return make_response(error_response, f"不支持的文件类型，支持的格式: {', '.join(ALLOWED_EXTENSIONS)}", code=400)

            # 检查文件大小
            file.seek(0, os.SEEK_END)
            file_size = file.tell()
            file.seek(0)
            
            if file_size > MAX_FILE_SIZE:
                return make_response(error_response, f"文件大小超过限制 ({MAX_FILE_SIZE // (1024*1024)}MB)", code=400)

            # 确保上传目录存在
            ensure_upload_folder()

            # 生成唯一文件名
            filename = generate_filename(file.filename)
            file_path = os.path.join(UPLOAD_FOLDER, filename)

            # 保存文件
            file.save(file_path)

            # 获取图片尺寸
            width, height = get_image_dimensions(file_path)

            # 获取其他参数
            alt_text = request.form.get('alt_text', '')
            is_main = request.form.get('is_main', 'false').lower() == 'true'
            sort_order = int(request.form.get('sort_order', 0))

            # 如果设为主图，将其他图片设为非主图
            if is_main:
                ProductImage.query.filter_by(product_id=product_id).update({'is_main': False})
                # 同时更新产品的主图字段
                product.image = f"/{file_path}"

            # 创建图片记录
            product_image = ProductImage(
                product_id=product_id,
                url=f"/{file_path}",
                file_name=filename,
                alt_text=alt_text,
                is_main=is_main,
                sort_order=sort_order,
                file_size=file_size,
                width=width,
                height=height
            )

            db.session.add(product_image)
            db.session.commit()

            return make_response(success_response, ProductImageSchema().dump(product_image), "图片上传成功", 201)

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"上传产品图片失败: {str(e)}")
            return make_response(error_response, f"上传产品图片失败: {str(e)}")


@api.route('/uploads/<path:filename>')
class ServeUpload(Resource):
    @api.doc('serve_upload')
    def get(self, filename):
        """提供上传文件的访问服务"""
        try:
            # 安全检查，防止路径遍历攻击
            safe_filename = secure_filename(filename)
            file_path = os.path.join(UPLOAD_FOLDER, safe_filename)
            
            if not os.path.exists(file_path):
                return make_response(not_found_response, "文件不存在")

            # 获取MIME类型
            mimetype = mimetypes.guess_type(file_path)[0]
            if not mimetype:
                mimetype = 'application/octet-stream'

            return send_file(file_path, mimetype=mimetype)

        except Exception as e:
            current_app.logger.error(f"提供文件服务失败: {str(e)}")
            return make_response(error_response, f"提供文件服务失败: {str(e)}")


@api.route('/products/images/<int:image_id>')
class ProductImageDetail(Resource):
    @api.doc('delete_product_image')
    def delete(self, image_id):
        """删除产品图片"""
        try:
            product_image = ProductImage.query.get(image_id)
            if not product_image:
                return make_response(not_found_response, "产品图片不存在")

            # 删除文件
            if product_image.url and product_image.url.startswith('/'):
                file_path = product_image.url[1:]  # 移除开头的 '/'
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except OSError as e:
                        current_app.logger.warning(f"删除图片文件失败: {str(e)}")

            # 如果删除的是主图，设置另一张图片为主图
            if product_image.is_main:
                other_image = ProductImage.query.filter(
                    ProductImage.product_id == product_image.product_id,
                    ProductImage.id != image_id
                ).first()
                if other_image:
                    other_image.is_main = True
                    # 更新产品的主图字段
                    product = Product.query.get(product_image.product_id)
                    if product:
                        product.image = other_image.url
                else:
                    # 没有其他图片，清空产品主图
                    product = Product.query.get(product_image.product_id)
                    if product:
                        product.image = None

            # 删除数据库记录
            db.session.delete(product_image)
            db.session.commit()

            return make_response(success_response, message="产品图片删除成功")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除产品图片失败: {str(e)}")
            return make_response(error_response, f"删除产品图片失败: {str(e)}")
