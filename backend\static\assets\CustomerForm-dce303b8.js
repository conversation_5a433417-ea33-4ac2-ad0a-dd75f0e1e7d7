import{C as e,a as l}from"./CustomerDeliveryAddresses-9519c02d.js";import{J as a,r as u,w as d,f as t,M as o,N as r,o as s,g as n,h as m,i as p,a as i,k as v}from"./index-3d4c440c.js";import{_ as c}from"./_plugin-vue_export-helper-1b428a4d.js";const _=c(a({__name:"CustomerForm",props:{customerData:{},loading:{type:Boolean}},setup(a,{expose:c}){const _=a,f=u(null),g=u(null),b=u(null),V=u({id:void 0,name:"",tax_id:"",contact:"",phone:"",email:"",address:"",level:"normal",source:"",status:"normal",notes:"",bank_accounts:[],delivery_addresses:[]});d((()=>_.customerData),(e=>{e&&Object.keys(e).length>0?V.value={id:void 0,name:"",tax_id:"",contact:"",phone:"",email:"",address:"",level:"normal",source:"",status:"normal",notes:"",bank_accounts:[],delivery_addresses:[],...e}:V.value={id:void 0,name:"",tax_id:"",contact:"",phone:"",email:"",address:"",level:"normal",source:"",status:"normal",notes:"",bank_accounts:[],delivery_addresses:[]}}),{immediate:!0,deep:!0});const h={name:[{required:!0,message:"请输入公司名称",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],contact:[{required:!0,message:"请输入联系人姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],level:[{required:!0,message:"请选择客户等级",trigger:"change"}],status:[{required:!0,message:"请选择客户状态",trigger:"change"}]};return c({validate:async()=>{if(!f.value)return!1;const e=await f.value.validate();let l=!0;g.value&&V.value.bank_accounts&&V.value.bank_accounts.length>0&&(l=await g.value.validate());let a=!0;return b.value&&V.value.delivery_addresses&&V.value.delivery_addresses.length>0&&(a=await b.value.validate()),e&&l&&a},getFormData:()=>V.value}),(a,u)=>{const d=t("el-input"),c=t("el-form-item"),_=t("el-col"),y=t("el-row"),x=t("el-option"),k=t("el-select"),U=t("el-radio"),w=t("el-radio-group"),q=t("el-card"),j=t("el-form"),C=o("loading");return r((s(),n(j,{ref_key:"formRef",ref:f,model:V.value,rules:h,"label-width":"120px"},{default:m((()=>[p(q,{class:"mb-20"},{header:m((()=>u[12]||(u[12]=[i("div",{class:"card-header"},[i("h3",null,"基本信息")],-1)]))),default:m((()=>[p(y,{gutter:20},{default:m((()=>[p(_,{span:12},{default:m((()=>[p(c,{label:"公司名称",prop:"name"},{default:m((()=>[p(d,{modelValue:V.value.name,"onUpdate:modelValue":u[0]||(u[0]=e=>V.value.name=e),placeholder:"请输入公司名称"},null,8,["modelValue"])])),_:1})])),_:1}),p(_,{span:12},{default:m((()=>[p(c,{label:"统一社会代码",prop:"tax_id"},{default:m((()=>[p(d,{modelValue:V.value.tax_id,"onUpdate:modelValue":u[1]||(u[1]=e=>V.value.tax_id=e),placeholder:"请输入统一社会代码"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),p(y,{gutter:20},{default:m((()=>[p(_,{span:12},{default:m((()=>[p(c,{label:"联系人",prop:"contact"},{default:m((()=>[p(d,{modelValue:V.value.contact,"onUpdate:modelValue":u[2]||(u[2]=e=>V.value.contact=e),placeholder:"请输入联系人姓名"},null,8,["modelValue"])])),_:1})])),_:1}),p(_,{span:12},{default:m((()=>[p(c,{label:"联系电话",prop:"phone"},{default:m((()=>[p(d,{modelValue:V.value.phone,"onUpdate:modelValue":u[3]||(u[3]=e=>V.value.phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),p(y,{gutter:20},{default:m((()=>[p(_,{span:12},{default:m((()=>[p(c,{label:"电子邮箱",prop:"email"},{default:m((()=>[p(d,{modelValue:V.value.email,"onUpdate:modelValue":u[4]||(u[4]=e=>V.value.email=e),placeholder:"请输入电子邮箱"},null,8,["modelValue"])])),_:1})])),_:1}),p(_,{span:12},{default:m((()=>[p(c,{label:"客户等级",prop:"level"},{default:m((()=>[p(k,{modelValue:V.value.level,"onUpdate:modelValue":u[5]||(u[5]=e=>V.value.level=e),placeholder:"请选择客户等级",style:{width:"100%"}},{default:m((()=>[p(x,{label:"普通客户",value:"normal"}),p(x,{label:"重要客户",value:"important"}),p(x,{label:"VIP客户",value:"vip"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),p(y,{gutter:20},{default:m((()=>[p(_,{span:12},{default:m((()=>[p(c,{label:"客户来源",prop:"source"},{default:m((()=>[p(k,{modelValue:V.value.source,"onUpdate:modelValue":u[6]||(u[6]=e=>V.value.source=e),placeholder:"请选择客户来源",style:{width:"100%"}},{default:m((()=>[p(x,{label:"网络渠道",value:"internet"}),p(x,{label:"客户推荐",value:"referral"}),p(x,{label:"展会",value:"exhibition"}),p(x,{label:"销售拜访",value:"visit"}),p(x,{label:"老客户",value:"old_customer"}),p(x,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),p(_,{span:12},{default:m((()=>[p(c,{label:"客户状态",prop:"status"},{default:m((()=>[p(w,{modelValue:V.value.status,"onUpdate:modelValue":u[7]||(u[7]=e=>V.value.status=e)},{default:m((()=>[p(U,{value:"normal"},{default:m((()=>u[13]||(u[13]=[v("正常")]))),_:1}),p(U,{value:"disabled"},{default:m((()=>u[14]||(u[14]=[v("禁用")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),p(y,null,{default:m((()=>[p(_,{span:24},{default:m((()=>[p(c,{label:"公司地址",prop:"address"},{default:m((()=>[p(d,{modelValue:V.value.address,"onUpdate:modelValue":u[8]||(u[8]=e=>V.value.address=e),placeholder:"请输入公司地址"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),p(y,null,{default:m((()=>[p(_,{span:24},{default:m((()=>[p(c,{label:"备注",prop:"notes"},{default:m((()=>[p(d,{modelValue:V.value.notes,"onUpdate:modelValue":u[9]||(u[9]=e=>V.value.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),p(e,{modelValue:V.value.bank_accounts,"onUpdate:modelValue":u[10]||(u[10]=e=>V.value.bank_accounts=e),"customer-name":V.value.name,ref_key:"bankAccountsRef",ref:g},null,8,["modelValue","customer-name"]),p(l,{modelValue:V.value.delivery_addresses,"onUpdate:modelValue":u[11]||(u[11]=e=>V.value.delivery_addresses=e),ref_key:"deliveryAddressesRef",ref:b},null,8,["modelValue"])])),_:1},8,["model"])),[[C,a.loading]])}}}),[["__scopeId","data-v-e39c33a2"]]);export{_ as C};
