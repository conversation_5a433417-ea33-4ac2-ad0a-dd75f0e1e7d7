#!/usr/bin/env python3
"""
数据迁移脚本：将"发货中"状态迁移为"部分发货"状态

这个脚本用于优化订单状态逻辑，移除"发货中"状态，统一使用"部分发货"状态。

运行方式：
python migrate_shipping_status.py

注意：运行前请备份数据库！
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def migrate_shipping_status():
    """迁移发货中状态为部分发货状态"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件不存在: {db_path}")
        return False
    
    print(f"开始迁移数据库: {db_path}")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 开始事务
        cursor.execute("BEGIN TRANSACTION")
        
        # 1. 查询当前"发货中"状态的订单
        cursor.execute("""
            SELECT id, order_number, status, order_status 
            FROM orders 
            WHERE status = '发货中' OR order_status = '发货中'
        """)
        
        shipping_orders = cursor.fetchall()
        print(f"找到 {len(shipping_orders)} 个'发货中'状态的订单")
        
        if shipping_orders:
            print("订单详情:")
            for order in shipping_orders:
                print(f"  - 订单ID: {order[0]}, 订单号: {order[1]}, 状态: {order[2]}, 物流状态: {order[3]}")
        
        # 2. 更新订单状态
        updated_count = 0
        for order in shipping_orders:
            order_id = order[0]
            order_number = order[1]
            
            # 更新状态为"部分发货"
            cursor.execute("""
                UPDATE orders 
                SET status = '部分发货', 
                    order_status = '部分发货',
                    updated_at = ?
                WHERE id = ?
            """, (datetime.utcnow(), order_id))
            
            # 创建状态历史记录
            cursor.execute("""
                INSERT INTO order_status_history (order_id, status, comment, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                order_id,
                '物流:部分发货',
                '系统迁移：将"发货中"状态更新为"部分发货"状态',
                datetime.utcnow(),
                datetime.utcnow()
            ))
            
            updated_count += 1
            print(f"  ✓ 已更新订单 {order_number} (ID: {order_id})")
        
        # 3. 检查是否有其他表引用了"发货中"状态
        print("\n检查其他可能的引用...")
        
        # 检查状态历史表
        cursor.execute("""
            SELECT COUNT(*) FROM order_status_history 
            WHERE status LIKE '%发货中%'
        """)
        history_count = cursor.fetchone()[0]
        if history_count > 0:
            print(f"  - 状态历史表中有 {history_count} 条'发货中'相关记录（保留作为历史记录）")
        
        # 提交事务
        cursor.execute("COMMIT")
        
        print(f"\n✅ 迁移完成！")
        print(f"   - 更新了 {updated_count} 个订单的状态")
        print(f"   - 所有'发货中'状态已更改为'部分发货'状态")
        print(f"   - 已创建相应的状态历史记录")
        
        return True
        
    except Exception as e:
        # 回滚事务
        cursor.execute("ROLLBACK")
        print(f"❌ 迁移失败: {str(e)}")
        return False
        
    finally:
        conn.close()

def verify_migration():
    """验证迁移结果"""
    
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件不存在: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查是否还有"发货中"状态的订单
        cursor.execute("""
            SELECT COUNT(*) FROM orders 
            WHERE status = '发货中' OR order_status = '发货中'
        """)
        
        remaining_count = cursor.fetchone()[0]
        
        if remaining_count == 0:
            print("✅ 验证通过：没有发现'发货中'状态的订单")
        else:
            print(f"⚠️  警告：仍有 {remaining_count} 个订单处于'发货中'状态")
        
        # 统计各状态的订单数量
        cursor.execute("""
            SELECT order_status, COUNT(*) 
            FROM orders 
            GROUP BY order_status 
            ORDER BY COUNT(*) DESC
        """)
        
        status_counts = cursor.fetchall()
        print("\n当前订单状态分布:")
        for status, count in status_counts:
            print(f"  - {status}: {count} 个订单")
            
    except Exception as e:
        print(f"验证失败: {str(e)}")
        
    finally:
        conn.close()

if __name__ == "__main__":
    print("=" * 60)
    print("EMB订单状态迁移工具")
    print("功能：将'发货中'状态迁移为'部分发货'状态")
    print("=" * 60)
    
    # 确认操作
    response = input("\n⚠️  这将修改数据库中的订单状态，请确认是否继续？(y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        sys.exit(0)
    
    # 执行迁移
    success = migrate_shipping_status()
    
    if success:
        print("\n" + "=" * 60)
        print("验证迁移结果...")
        verify_migration()
        print("=" * 60)
        print("✅ 迁移完成！现在可以重启应用程序。")
    else:
        print("❌ 迁移失败，请检查错误信息并重试。")
        sys.exit(1)
