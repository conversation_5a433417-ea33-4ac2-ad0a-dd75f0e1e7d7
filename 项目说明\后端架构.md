# EMB项目后端架构详解

## 📋 架构概览

EMB后端采用Flask + Flask-RESTX构建的RESTful API架构，提供完整的工程物资管理业务接口。

### 核心特性
- **RESTful API设计**: 标准化的HTTP接口
- **自动API文档**: Swagger/OpenAPI 3.0自动生成
- **模块化架构**: 清晰的代码组织和模块划分
- **统一错误处理**: 标准化的错误响应格式
- **数据验证**: 完整的输入数据验证机制

## 🏗️ 应用架构

### Flask应用初始化
```python
# backend/app/__init__.py
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_restx import Api

# 全局扩展实例
db = SQLAlchemy()
restx_api = Api(
    version='1.0',
    title='EMB System API',
    description='工程物资报价及订单管理系统API文档',
    doc='/docs/'
)

def create_app():
    """应用工厂模式创建Flask应用"""
    app = Flask(__name__)
    
    # 配置加载
    app.config.from_object(config_class)
    
    # 扩展初始化
    db.init_app(app)
    restx_api.init_app(app)
    
    # 注册组件
    register_error_handlers(app)
    register_namespaces(app)
    register_middleware(app)
    
    return app
```

### 应用工厂模式优势
1. **配置灵活**: 支持多环境配置
2. **测试友好**: 便于单元测试和集成测试
3. **扩展管理**: 统一的扩展初始化
4. **循环导入避免**: 解决模块间循环依赖

## 🗄️ 数据模型架构

### 基础模型类
```python
# backend/app/models/base.py
class BaseModel(db.Model):
    """所有模型的基类"""
    __abstract__ = True
    
    # 通用时间戳
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    def to_dict(self, exclude=None):
        """模型转字典"""
        
    def from_dict(self, data):
        """字典更新模型"""
        
    def save(self):
        """保存到数据库"""
        
    def delete(self):
        """从数据库删除"""
```

### 模型继承体系
```
BaseModel (抽象基类)
├── Customer (客户)
├── CustomerBankAccount (客户银行账户)
├── CustomerDeliveryAddress (客户送货地址)
├── Product (产品)
├── ProductCategory (产品分类)
├── ProductSpecification (产品规格)
├── ProductAttribute (产品属性)
├── ProductImage (产品图片)
├── Quotation (报价单)
├── QuotationItem (报价项)
├── QuotationRequest (报价需求)
├── QuotationRequestItem (报价需求项)
├── Order (订单)
├── OrderProduct (订单产品)
├── OrderStatusHistory (订单状态历史)
├── DeliveryNote (发货单)
├── DeliveryNoteItem (发货单项)
├── ReturnOrder (退货单)
├── ReturnOrderItem (退货单项)
├── Statement (对账单)
├── StatementDeliveryNote (对账单发货单)
├── Receivable (应收款)
├── PaymentRecord (收款记录)
├── RefundRecord (退款记录)
├── ErrorLog (错误日志)
├── Brand (品牌)
└── SystemSetting (系统设置)
```

### 数据模型特性
1. **统一基类**: 所有模型继承BaseModel
2. **时间戳**: 自动记录创建和更新时间
3. **序列化**: 内置to_dict()方法
4. **CRUD操作**: 统一的保存和删除方法
5. **关系映射**: 完整的外键关系定义

## 🔌 API架构设计

### 命名空间组织
```python
# API命名空间注册
restx_api.add_namespace(test_ns, path='/test')
restx_api.add_namespace(customers_ns, path='/api/v1/customers')
restx_api.add_namespace(products_ns, path='/api/v1/products')
restx_api.add_namespace(system_ns, path='/api/v1/system')
restx_api.add_namespace(quotations_ns, path='/api/v1/quotations')
restx_api.add_namespace(orders_ns, path='/api/v1/orders')
restx_api.add_namespace(finance_ns, path='/api/v1/finance')
restx_api.add_namespace(dashboard_ns, path='/api/v1/dashboard')
restx_api.add_namespace(delivery_notes_ns, path='/api/v1/delivery-notes')
restx_api.add_namespace(returns_ns, path='/api/v1/returns')
restx_api.add_namespace(statements_ns, path='/api/v1/statements')
restx_api.add_namespace(uploads_ns, path='/api/v1/uploads')
restx_api.add_namespace(error_logs_ns, path='/api/v1/error-logs')
restx_api.add_namespace(order_export_ns, path='/api/v1/order-export')
restx_api.add_namespace(order_print_ns, path='/api/v1/order-print')
restx_api.add_namespace(product_images_ns, path='/api/v1/product-images')
```

### API模块结构
```
backend/app/api/
├── test.py                 # 测试和健康检查接口
└── v1/                     # API版本1
    ├── customers.py        # 客户管理API
    ├── products.py         # 产品管理API
    ├── quotations.py       # 报价管理API
    ├── orders.py           # 订单管理API
    ├── delivery_notes.py   # 发货单API
    ├── returns.py          # 退货管理API
    ├── statements.py       # 对账单API
    ├── finance.py          # 财务管理API
    ├── dashboard.py        # 工作台API
    ├── system.py           # 系统设置API
    ├── uploads.py          # 文件上传API
    ├── error_logs.py       # 错误日志API
    ├── order_export.py     # 订单导出API
    ├── order_print.py      # 订单打印API
    └── product_images.py   # 产品图片API
```

### RESTful API设计原则
1. **资源导向**: URL表示资源，HTTP方法表示操作
2. **统一接口**: 标准的HTTP状态码和响应格式
3. **无状态**: 每个请求包含完整的信息
4. **可缓存**: 支持HTTP缓存机制
5. **分层系统**: 清晰的架构层次

## 📊 数据库架构

### 数据库配置
```python
# config.py
class Config:
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///project.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
class DevelopmentConfig(Config):
    SQLALCHEMY_DATABASE_URI = 'sqlite:///dev_project.db'
    
class ProductionConfig(Config):
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    
class TestingConfig(Config):
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
```

### 数据库特性
1. **多数据库支持**: SQLite、PostgreSQL、MySQL
2. **环境隔离**: 开发、测试、生产环境分离
3. **连接池**: 自动连接池管理
4. **事务支持**: 完整的事务处理
5. **迁移管理**: Flask-Migrate支持

### 核心业务表关系
```
客户管理:
Customer (客户) 1:N CustomerBankAccount (银行账户)
Customer (客户) 1:N CustomerDeliveryAddress (送货地址)

产品管理:
ProductCategory (分类) 1:N Product (产品)
Product (产品) 1:N ProductSpecification (规格)
Product (产品) 1:N ProductAttribute (属性)
Product (产品) 1:N ProductImage (图片)
Brand (品牌) 1:N Product (产品)

报价管理:
Customer (客户) 1:N Quotation (报价单)
Quotation (报价单) 1:N QuotationItem (报价项)
Customer (客户) 1:N QuotationRequest (报价需求)
QuotationRequest (需求) 1:N QuotationRequestItem (需求项)

订单管理:
Customer (客户) 1:N Order (订单)
Order (订单) 1:N OrderProduct (订单产品)
Order (订单) 1:N OrderStatusHistory (状态历史)
Order (订单) 1:N DeliveryNote (发货单)
DeliveryNote (发货单) 1:N DeliveryNoteItem (发货项)

财务管理:
Customer (客户) 1:N Statement (对账单)
Statement (对账单) 1:N StatementDeliveryNote (对账发货单)
Customer (客户) 1:N Receivable (应收款)
Customer (客户) 1:N PaymentRecord (收款记录)
Customer (客户) 1:N RefundRecord (退款记录)
```

## 🔧 中间件架构

### 路径重写中间件
```python
# backend/app/middleware/path_rewrite.py
def register_path_rewrite_middleware(app):
    """注册路径重写中间件，用于API兼容性"""
    @app.before_request
    def rewrite_paths():
        # 处理API路径兼容性
        pass
```

### 错误处理中间件
```python
# backend/app/utils/error_handlers.py
def register_error_handlers(app):
    """注册全局错误处理器"""
    
    @app.errorhandler(404)
    def not_found(error):
        return {'code': 404, 'message': '资源不存在'}, 404
        
    @app.errorhandler(500)
    def internal_error(error):
        return {'code': 500, 'message': '服务器内部错误'}, 500
```

### 日志中间件
```python
def setup_logging(app):
    """配置应用日志"""
    if not app.debug:
        # 生产环境日志配置
        pass
```

## 📝 数据验证架构

### Marshmallow Schema
```python
# backend/app/schemas/customer.py
from marshmallow import Schema, fields, validate

class CustomerSchema(Schema):
    """客户数据验证模式"""
    name = fields.Str(required=True, validate=validate.Length(min=1, max=100))
    contact = fields.Str(required=True, validate=validate.Length(min=1, max=50))
    phone = fields.Str(required=True, validate=validate.Regexp(r'^1[3-9]\d{9}$'))
    email = fields.Email(allow_none=True)
    address = fields.Str(allow_none=True, validate=validate.Length(max=200))
    status = fields.Str(validate=validate.OneOf(['active', 'inactive']))
```

### 验证特性
1. **输入验证**: 严格的数据格式验证
2. **类型转换**: 自动数据类型转换
3. **错误收集**: 详细的验证错误信息
4. **序列化**: 输出数据格式化
5. **自定义验证**: 支持自定义验证规则

## 🔄 业务逻辑架构

### 服务层模式
```python
# backend/app/services/customer_service.py
class CustomerService:
    """客户业务逻辑服务"""
    
    @staticmethod
    def create_customer(data):
        """创建客户"""
        # 数据验证
        schema = CustomerSchema()
        validated_data = schema.load(data)
        
        # 业务逻辑
        customer = Customer(**validated_data)
        customer.save()
        
        return customer.to_dict()
    
    @staticmethod
    def get_customer_list(filters=None):
        """获取客户列表"""
        query = Customer.query
        
        # 应用过滤条件
        if filters:
            query = apply_filters(query, filters)
            
        return [customer.to_dict() for customer in query.all()]
```

### 业务逻辑特性
1. **服务层**: 独立的业务逻辑层
2. **事务管理**: 完整的事务处理
3. **错误处理**: 统一的异常处理
4. **数据转换**: 模型与DTO转换
5. **业务规则**: 复杂业务规则实现

## 🚀 性能优化

### 数据库优化
1. **查询优化**: 使用索引和查询优化
2. **连接池**: 数据库连接池管理
3. **懒加载**: 关联数据懒加载
4. **批量操作**: 批量插入和更新
5. **缓存策略**: 查询结果缓存

### API优化
1. **分页查询**: 大数据量分页处理
2. **字段选择**: 按需返回字段
3. **压缩响应**: Gzip压缩
4. **缓存头**: HTTP缓存控制
5. **异步处理**: 长时间操作异步化

## 🔒 安全架构

### 安全特性
1. **输入验证**: 严格的输入数据验证
2. **SQL注入防护**: ORM防止SQL注入
3. **XSS防护**: 输出数据转义
4. **CSRF保护**: CSRF令牌验证
5. **错误信息**: 安全的错误信息返回

### 配置安全
```python
# config.py
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    WTF_CSRF_ENABLED = True
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
```

## 📈 监控和日志

### 日志架构
1. **应用日志**: 业务操作日志
2. **错误日志**: 异常和错误记录
3. **访问日志**: API访问记录
4. **性能日志**: 性能监控数据
5. **安全日志**: 安全事件记录

### 监控指标
1. **响应时间**: API响应时间监控
2. **错误率**: 错误请求比例
3. **吞吐量**: 请求处理能力
4. **资源使用**: CPU、内存使用率
5. **数据库性能**: 查询性能监控

---

**后端架构版本**: v1.0  
**最后更新**: 2025年1月  
**技术栈**: Flask 2.3.3 + Flask-RESTX + SQLAlchemy  
**维护状态**: 🔄 持续优化中
