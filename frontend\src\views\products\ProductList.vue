<template>
  <div class="product-list">
    <!-- 搜索栏 -->
    <el-card class="search-card mb-20">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="产品名称">
          <el-input v-model="searchForm.name" placeholder="请输入产品名称" clearable />
        </el-form-item>
        <el-form-item label="产品型号">
          <el-input v-model="searchForm.model" placeholder="请输入产品型号" clearable />
        </el-form-item>
        <el-form-item label="产品分类">
          <el-select 
            v-model="searchForm.category_id" 
            placeholder="请选择产品分类" 
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="category in categoryOptions"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="品牌">
          <el-select 
            v-model="searchForm.brand_id" 
            placeholder="请选择品牌" 
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="brand in brandOptions"
              :key="brand.id"
              :label="brand.name"
              :value="brand.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品状态">
          <el-select 
            v-model="searchForm.status" 
            placeholder="请选择产品状态" 
            clearable
            style="width: 150px"
          >
            <el-option label="正常" value="正常" />
            <el-option label="禁用" value="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="mb-20">
      <div class="flex-between">
        <div>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增产品
          </el-button>
          <el-button type="success" @click="handleImport">
            <el-icon><Download /></el-icon>
            导入产品
          </el-button>
          <el-button type="warning" @click="handleExport">
            <el-icon><Upload /></el-icon>
            导出产品
          </el-button>
          <el-button
            type="info"
            @click="handleDownloadTemplate"
            :loading="templateLoading"
          >
            <el-icon><Document /></el-icon>
            下载模板
          </el-button>
        </div>
        <div>
          <el-button-group>
            <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
              <el-icon><Menu /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 表格视图 -->
    <el-card v-if="viewMode === 'table'">
      <div v-loading="loading">
        <el-table
          :data="displayedProductList"
          :span-method="objectSpanMethod"
          border
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" :selectable="(row) => row.isFirstSpec" />
          <el-table-column prop="id" label="ID" width="50" />
          <el-table-column label="产品名称" min-width="150">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleView(row)" class="name-link">
                {{ row.name }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="70" align="center">
            <template #default="{ row }">
              <div v-if="row.isFirstSpec" class="product-actions-vertical">
                <el-button type="primary" link size="small" @click="handleEdit(row)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" link size="small" @click="handleDelete(row)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="model" label="产品型号" width="120" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="category_name" label="产品类别" width="120" />
          <el-table-column prop="specification" label="产品规格" width="120" />
          <el-table-column prop="cost_price" label="成本价" width="100">
            <template #default="{ row }">
              <span v-if="row.cost_price">¥{{ row.cost_price }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="suggested_price" label="建议售价" width="100">
            <template #default="{ row }">
              <span v-if="row.suggested_price">¥{{ row.suggested_price }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="tax_rate" label="税率(%)" width="80">
            <template #default="{ row }">
              <span v-if="row.tax_rate">{{ row.tax_rate }}%</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="产品属性" width="200">
            <template #default="{ row }">
              <div v-if="row.attributes && row.attributes.length > 0" class="attributes-cell">
                <div
                  v-for="attr in row.attributes"
                  :key="attr.id"
                  class="attribute-item"
                >
                  <span class="attr-name">{{ attr.attribute_name }}:</span>
                  <span class="attr-value">{{ attr.attribute_value }}</span>
                </div>
              </div>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" width="150">
            <template #default="{ row }">
              <div v-if="row.notes" class="notes-cell">
                {{ row.notes }}
              </div>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === '正常' ? 'success' : 'danger'">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex-between mt-20">
          <el-button-group>
            <el-button :disabled="!selectedProducts.length" @click="handleBatchDelete">
              批量删除
            </el-button>
            <el-button :disabled="!selectedProducts.length" @click="handleBatchExport">
              批量导出
            </el-button>
          </el-button-group>
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.per_page"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 卡片视图 -->
    <template v-else>
      <el-row :gutter="20">
        <el-col v-for="product in productList" :key="product.id" :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="product-card mb-20">
            <template #header>
              <div class="flex-between">
                <span class="product-name">{{ product.name }}</span>
                <el-tag :type="product.status === '正常' ? 'success' : 'danger'">
                  {{ product.status }}
                </el-tag>
              </div>
            </template>
            <div class="product-image">
              <el-image 
                :src="product.main_image || '/default-product.png'"
                fit="contain"
                style="width: 100%; height: 150px;"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
            <div class="product-info mt-10">
              <p><span class="info-label">型号：</span>{{ product.model }}</p>
              <p><span class="info-label">单位：</span>{{ product.unit }}</p>
              <p><span class="info-label">分类：</span>{{ product.category_name || '未分类' }}</p>
              <p><span class="info-label">品牌：</span>{{ product.brand_name || '无品牌' }}</p>
              <p><span class="info-label">价格：</span>{{ product.price_range || '暂无价格' }}</p>
            </div>
            <div class="product-actions mt-10">
              <el-button-group>
                <el-button type="primary" link @click="handleEdit(product)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="info" link @click="handleView(product)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button type="danger" link @click="handleDelete(product)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </el-button-group>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 分页 -->
      <div class="flex-center mt-20">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[12, 24, 48, 96]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </template>

    <!-- 导入产品对话框 - 三步式流程 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入产品"
      width="90%"
      top="5vh"
      :before-close="handleImportDialogClose"
      destroy-on-close
    >
      <div class="import-dialog-content">
        <!-- 步骤指示器 -->
        <el-steps :active="importStep" finish-status="success" style="margin-bottom: 20px;">
          <el-step title="上传文件" description="选择Excel文件"></el-step>
          <el-step title="预览确认" description="检查和编辑数据"></el-step>
          <el-step title="导入结果" description="查看导入结果"></el-step>
        </el-steps>

        <!-- 步骤1: 文件上传 -->
        <div v-if="importStep === 0" class="step-upload">
          <div class="import-tips">
            <div class="tips-header">
              <el-icon><InfoFilled /></el-icon>
              <span>导入说明</span>
            </div>
            <ul class="tips-list">
              <li>支持 .xlsx 和 .xls 格式的Excel文件</li>
              <li>支持智能表头识别，列名可以是中文或英文</li>
              <li>支持复杂Excel格式，包括合并单元格</li>
              <li>文件大小不超过10MB</li>
            </ul>
          </div>

          <div class="upload-section">
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              drag
              action=""
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :limit="1"
              accept=".xlsx,.xls"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">拖拽文件到此处或 <em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传 .xlsx/.xls 文件，且不超过 10MB
                </div>
              </template>
            </el-upload>
          </div>
        </div>

        <!-- 步骤2: 数据预览 -->
        <div v-if="importStep === 1" class="step-preview">
          <ProductImportPreview
            v-if="previewData.length > 0"
            :preview-data="previewData"
            :categories="categoryOptions"
            :mappings="previewMappings"
            @success="handleImportSuccess"
            @cancel="importStep = 0"
          />
          <el-empty v-else description="未检测到有效的产品数据" />
        </div>

        <!-- 步骤3: 导入结果 -->
        <div v-if="importStep === 2" class="step-result">
          <el-result
            v-if="importResult && importResult.imported_count > 0"
            icon="success"
            title="导入成功"
            :sub-title="`成功导入 ${importResult.imported_count} 个产品${importResult.failed_count > 0 ? `，失败 ${importResult.failed_count} 个` : ''}`"
          >
            <template #extra>
              <el-button @click="importStep = 0">继续导入</el-button>
              <el-button type="primary" @click="handleImportDialogClose">完成</el-button>
            </template>
          </el-result>

          <el-result
            v-else
            icon="error"
            title="导入失败"
            :sub-title="importResult ? `无法导入产品，${importResult.errors && importResult.errors.length > 0 ? importResult.errors[0] : '请检查数据格式'}` : '导入过程出现错误'"
          >
            <template #extra>
              <el-button @click="importStep = 0">返回重试</el-button>
              <el-button type="primary" @click="handleImportDialogClose">关闭</el-button>
            </template>
          </el-result>

          <!-- 详细错误信息 -->
          <div v-if="importResult && importResult.errors && importResult.errors.length > 0" class="error-details">
            <el-collapse>
              <el-collapse-item title="查看详细错误信息">
                <ul class="error-list">
                  <li v-for="error in importResult.errors" :key="error">{{ error }}</li>
                </ul>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="importStep === 1" @click="importStep = 0">上一步</el-button>
          <el-button @click="handleImportDialogClose">
            {{ importStep === 2 ? '关闭' : '取消' }}
          </el-button>
          <el-button
            v-if="importStep === 0"
            type="primary"
            @click="handlePreviewImport"
            :disabled="!selectedFile"
            :loading="previewLoading"
          >
            下一步
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Upload,
  Download,
  Grid,
  Menu,
  Edit,
  View,
  Delete,
  Picture,
  InfoFilled,
  UploadFilled,
  Document,
  WarningFilled
} from '@element-plus/icons-vue'
import { productApi, categoryApi, brandApi } from '@/api/product'
import ProductImportPreview from './components/ProductImportPreview.vue'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  name: '',
  model: '',
  category_id: '',
  brand_id: '',
  status: ''
})

// 视图模式
const viewMode = ref('table')

// 分页信息
const pagination = reactive({
  page: 1,
  per_page: 10,
  total: 0
})

// 产品列表数据
const productList = ref([])
const displayedProductList = ref([])
const selectedProducts = ref([])
const loading = ref(false)

// 选项数据
const categoryOptions = ref([])
const brandOptions = ref([])

// 获取产品列表
const getProductList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      per_page: pagination.per_page,
      with_specifications: true,
      with_attributes: true,
      ...searchForm
    }
    
    const response = await productApi.getList(params) as any
    
    // 检查响应格式并处理数据
    if (Array.isArray(response)) {
      productList.value = response
      pagination.total = response.length
    } else if (response && typeof response === 'object') {
      productList.value = response.data || response.items || []
      pagination.total = response.pagination?.total || response.total || 0
    } else {
      productList.value = []
      pagination.total = 0
    }

    // 转换数据为显示格式
    transformProductListForDisplay()
  } catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
    productList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 转换产品列表为显示格式（包含规格信息）
const transformProductListForDisplay = () => {
  const displayList: any[] = []

  productList.value.forEach((product: any) => {
    if (product.specifications && product.specifications.length > 0) {
      // 有规格的产品，每个规格一行
      product.specifications.forEach((spec: any, index: number) => {
        displayList.push({
          ...product,
          specification: spec.specification || '-',
          cost_price: spec.cost_price || 0,
          suggested_price: spec.suggested_price || 0,
          tax_rate: spec.tax_rate || 0,
          isFirstSpec: index === 0, // 标记是否为第一个规格
          specIndex: index,
          totalSpecs: product.specifications.length
        })
      })
    } else {
      // 没有规格的产品，显示一行
      displayList.push({
        ...product,
        specification: '-',
        cost_price: 0,
        suggested_price: 0,
        tax_rate: 0,
        isFirstSpec: true,
        specIndex: 0,
        totalSpecs: 1
      })
    }
  })

  displayedProductList.value = displayList
}

// 表格合并方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  // 需要合并的列：选择框、ID、产品名称、操作、产品型号、单位、产品类别、属性、备注、状态
  const mergeColumns = [0, 1, 2, 3, 4, 5, 6, 11, 12, 13] // 对应的列索引

  if (mergeColumns.includes(columnIndex)) {
    if (row.isFirstSpec) {
      // 第一个规格行，显示合并的行数
      return {
        rowspan: row.totalSpecs,
        colspan: 1
      }
    } else {
      // 非第一个规格行，隐藏
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }

  // 规格相关列不合并
  return {
    rowspan: 1,
    colspan: 1
  }
}

// 获取分类选项
const getCategoryOptions = async () => {
  try {
    const response = await categoryApi.getList({ per_page: 999 }) as any
    categoryOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
  } catch (error) {
    console.error('获取分类选项失败:', error)
  }
}

// 获取品牌选项
const getBrandOptions = async () => {
  try {
    const response = await brandApi.getList({ per_page: 999 }) as any
    brandOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
  } catch (error) {
    console.error('获取品牌选项失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getProductList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    model: '',
    category_id: '',
    brand_id: '',
    status: ''
  })
  pagination.page = 1
  getProductList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.per_page = size
  pagination.page = 1
  getProductList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  getProductList()
}

// 选择处理
const handleSelectionChange = (selection: any[]) => {
  selectedProducts.value = selection
}

// 操作处理
const handleAdd = () => {
  router.push('/products/new')
}

const handleEdit = (row: any) => {
  router.push(`/products/edit/${row.id}`)
}

const handleView = (row: any) => {
  router.push(`/products/view/${row.id}`)
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除产品"${row.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await productApi.delete(row.id)
    ElMessage.success('删除成功')
    getProductList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除产品失败:', error)
      ElMessage.error('删除产品失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedProducts.value.length} 个产品吗？`, '确认批量删除', {
      type: 'warning'
    })
    
    const ids = selectedProducts.value.map((product: any) => product.id)
    await productApi.batchDelete(ids)
    
    ElMessage.success('批量删除成功')
    getProductList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 导入导出相关状态
const importDialogVisible = ref(false)
const selectedFile = ref(null)
const uploadRef = ref()
const importResult = ref(null)
const templateLoading = ref(false)

// 三步式导入相关状态
const importStep = ref(0)  // 0: 上传文件, 1: 预览确认, 2: 导入结果
const previewLoading = ref(false)
const previewData = ref([])
const previewMappings = ref({})

// 导入功能
const handleImport = () => {
  // 重置所有状态
  importDialogVisible.value = true
  importStep.value = 0
  selectedFile.value = null
  importResult.value = null
  previewData.value = []
  previewMappings.value = {}

  // 清理上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 文件选择处理
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
}

// 文件移除处理
const handleFileRemove = () => {
  selectedFile.value = null
}

// 预览导入
const handlePreviewImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    previewLoading.value = true

    const formData = new FormData()
    formData.append('file', selectedFile.value)

    const response = await productApi.previewImport(formData)

    // 响应拦截器已经处理了success判断，直接使用返回的数据
    previewData.value = response.preview_data || []
    previewMappings.value = response.mappings || {}
    categoryOptions.value = response.categories || []

    if (previewData.value.length > 0) {
      importStep.value = 1  // 进入预览步骤
      ElMessage.success(`成功解析 ${previewData.value.length} 个产品`)
    } else {
      ElMessage.warning('未检测到有效的产品数据，请检查Excel文件格式')
    }
  } catch (error) {
    console.error('预览导入失败:', error)
    ElMessage.error('预览导入失败')
  } finally {
    previewLoading.value = false
  }
}

// 导入成功处理
const handleImportSuccess = (result: any) => {
  importResult.value = result
  importStep.value = 2  // 进入结果步骤

  // 刷新产品列表
  getProductList()
}

// 执行导入
const handleImportSubmit = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    const formData = new FormData()
    formData.append('file', selectedFile.value)

    const response = await productApi.import(formData)

    if (response.success) {
      importResult.value = response
      ElMessage.success('导入成功')
      getProductList() // 刷新列表
    } else {
      importResult.value = response
      ElMessage.warning('导入完成，但存在错误，请查看详情')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  }
}

// 关闭导入对话框
const handleImportDialogClose = () => {
  importDialogVisible.value = false
  importStep.value = 0
  selectedFile.value = null
  importResult.value = null
  previewData.value = []
  previewMappings.value = {}

  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 下载导入模板
const handleDownloadTemplate = async () => {
  try {
    templateLoading.value = true
    const response = await productApi.downloadTemplate()
    const blob = response.data || response

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `产品导入模板_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  } finally {
    templateLoading.value = false
  }
}

// 导出功能
const handleExport = async () => {
  try {
    const params = {
      name: searchForm.name,
      model: searchForm.model,
      category_id: searchForm.category_id,
      brand_id: searchForm.brand_id,
      status: searchForm.status
    }

    const response = await productApi.export(params)
    const blob = response.data || response

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `产品列表_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 批量导出
const handleBatchExport = async () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请选择要导出的产品')
    return
  }

  try {
    const ids = selectedProducts.value.map((product: any) => product.id)
    const response = await productApi.batchExport(ids)
    const blob = response.data || response

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `选中产品_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('批量导出成功')
  } catch (error) {
    console.error('批量导出失败:', error)
    ElMessage.error('批量导出失败')
  }
}

// 工具函数
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 初始化
onMounted(() => {
  getProductList()
  getCategoryOptions()
  getBrandOptions()
})
</script>

<style lang="scss" scoped>
.product-list {
  .search-card {
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .mt-20 {
    margin-top: 20px;
  }
  
  .mt-10 {
    margin-top: 10px;
  }
  
  .product-card {
    height: 100%;
    
    .product-name {
      font-weight: bold;
      font-size: 16px;
    }
    
    .product-image {
      .image-slot {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 150px;
        background: #f5f7fa;
        color: #909399;
        font-size: 30px;
      }
    }
    
    .product-info {
      p {
        margin: 8px 0;
        
        .info-label {
          color: #909399;
          font-size: 14px;
        }
      }
    }
    
    .product-actions {
      text-align: center;
    }
  }

  // 产品名称链接样式
  .name-link {
    font-weight: 500;
    text-align: left;
    padding: 0;
    height: auto;

    &:hover {
      text-decoration: underline;
    }
  }

  // 表格中的竖直操作按钮样式
  .product-actions-vertical {
    display: flex;
    flex-direction: column;
    gap: 6px;
    align-items: center;

    .el-button {
      padding: 2px 6px;
      font-size: 12px;
      min-width: 50px;

      .el-icon {
        margin-right: 2px;
      }
    }
  }

  // 表格中的水平操作按钮样式
  .product-actions-horizontal {
    display: flex;
    gap: 8px;
    align-items: center;

    .el-button {
      padding: 2px 6px;
      font-size: 12px;

      .el-icon {
        margin-right: 2px;
      }
    }
  }

  // 导入对话框样式
  .import-dialog-content {
    .import-tips {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border-left: 4px solid #409eff;

      .tips-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-weight: bold;
        color: #409eff;

        .el-icon {
          margin-right: 8px;
        }
      }

      .tips-content {
        p {
          margin: 8px 0;
          color: #606266;
          line-height: 1.5;
        }
      }
    }

    .upload-demo {
      .el-upload {
        width: 100%;
      }
    }

    .selected-file {
      margin-top: 15px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;

      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .el-icon {
          color: #409eff;
        }

        .file-size {
          color: #909399;
          font-size: 12px;
        }
      }
    }

    .import-result {
      margin-top: 20px;

      .error-details {
        margin-top: 15px;

        .error-title {
          font-weight: bold;
          color: #e6a23c;
          margin-bottom: 10px;
        }

        .error-list {
          max-height: 200px;
          overflow-y: auto;
          background-color: #fdf6ec;
          border: 1px solid #f5dab1;
          border-radius: 4px;
          padding: 10px;

          .error-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 1.4;

            &:last-child {
              margin-bottom: 0;
            }

            .error-icon {
              color: #e6a23c;
              margin-right: 8px;
              margin-top: 2px;
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }

  // 属性列样式
  .attributes-cell {
    .attribute-item {
      display: block;
      margin-bottom: 4px;
      font-size: 12px;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }

      .attr-name {
        font-weight: 500;
        color: #606266;
        margin-right: 4px;
      }

      .attr-value {
        color: #303133;
      }
    }
  }

  // 备注列样式
  .notes-cell {
    font-size: 12px;
    line-height: 1.4;
    color: #606266;
    word-break: break-word;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}
</style>
