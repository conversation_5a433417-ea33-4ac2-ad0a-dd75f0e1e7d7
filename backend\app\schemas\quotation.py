"""
报价管理相关的序列化模式
基于原项目的Schema定义，确保与现有数据库结构100%兼容
"""
from marshmallow import Schema, fields, validate, validates, ValidationError
from typing import Dict, List, Optional, Any


class QuotationItemSchema(Schema):
    """报价单项目序列化模式"""
    id = fields.Integer(dump_only=True)
    quotation_id = fields.Integer(required=True)
    product_id = fields.Integer(required=True)
    product_specification_id = fields.Integer(allow_none=True)
    quantity = fields.Integer(required=True, validate=validate.Range(min=1))
    unit_price = fields.Decimal(required=True, validate=validate.Range(min=0), as_string=True)
    discount = fields.Decimal(validate=validate.Range(min=0, max=100), allow_none=True, as_string=True)
    tax_rate = fields.Decimal(validate=validate.Range(min=0, max=100), allow_none=True, as_string=True)
    total_price = fields.Decimal(dump_only=True, as_string=True)
    notes = fields.String(allow_none=True)
    
    # 快照字段，与模型字段名一致
    product_name = fields.String(allow_none=True)
    product_model = fields.String(allow_none=True)
    product_specification = fields.String(allow_none=True)
    product_unit = fields.String(allow_none=True)

    
    # 关联信息（用于显示当前产品信息）
    product = fields.Nested('ProductSimpleSchema', only=('id', 'name', 'model', 'unit'), dump_only=True)
    specification = fields.Nested('ProductSpecificationSchema', only=('id', 'specification'), dump_only=True)
    
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class QuotationRequestItemSchema(Schema):
    """报价需求项目序列化模式"""
    id = fields.Integer(dump_only=True)
    request_id = fields.Integer(required=True, load_only=True)
    
    # 用户原始输入字段
    original_product_name = fields.String(allow_none=True, validate=validate.Length(max=200))
    original_product_model = fields.String(allow_none=True, validate=validate.Length(max=100))
    original_product_spec = fields.String(allow_none=True, validate=validate.Length(max=200))
    original_unit = fields.String(allow_none=True, validate=validate.Length(max=50))

    # 前端编辑字段，也作为匹配输入源
    product_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    product_model = fields.String(validate=validate.Length(max=100), allow_none=True)
    product_spec = fields.String(validate=validate.Length(max=100), allow_none=True)
    quantity = fields.Integer(required=True, validate=validate.Range(min=1))
    unit = fields.String(required=True, validate=validate.Length(min=1, max=20))
    notes = fields.String(allow_none=True)

    # 匹配结果字段
    matched_product_id = fields.Integer(allow_none=True)
    matched_product_specification_id = fields.Integer(allow_none=True)
    match_notes = fields.String(allow_none=True, dump_only=True)
    match_type = fields.String(allow_none=True, validate=validate.OneOf(['manual', 'auto']))

    # 关联信息
    matched_product = fields.Nested(
        'ProductSimpleSchema',
        only=('id', 'name', 'model', 'unit'), 
        dump_only=True
    )
    matched_specification = fields.Nested(
        'ProductSpecificationSchema', 
        only=('id', 'specification', 'tax_rate', 'suggested_price'),
        dump_only=True
    )
    
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class QuotationRequestSchema(Schema):
    """报价需求序列化模式"""
    id = fields.Integer(dump_only=True)
    request_number = fields.String(dump_only=True)
    customer_id = fields.Integer(required=True)
    project_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    project_address = fields.String(validate=validate.Length(max=200), allow_none=True)
    expected_date = fields.Date(allow_none=True)
    status = fields.String(
        validate=validate.OneOf(['待确认', '已确认']),
        load_default='待确认'
    )
    notes = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    # 关联信息
    customer = fields.Nested('CustomerSimpleSchema', only=('id', 'name', 'contact', 'phone'), dump_only=True)
    customer_name = fields.String(dump_only=True, attribute='customer.name')
    items = fields.List(fields.Nested(QuotationRequestItemSchema), required=False)
    quotations = fields.Method("get_quotations", dump_only=True)  # 获取关联的报价单列表

    def get_quotations(self, obj):
        """获取关联的报价单信息"""
        quotations = obj.quotations
        return QuotationSimpleSchema(many=True, only=('id', 'quotation_number', 'status', 'total_amount', 'created_at')).dump(quotations)


class QuotationRequestCreateSchema(QuotationRequestSchema):
    """创建报价需求的序列化模式"""
    pass


class QuotationRequestUpdateSchema(QuotationRequestSchema):
    """更新报价需求的序列化模式"""
    class Meta:
        partial = True
    
    items = fields.List(fields.Nested(QuotationRequestItemSchema), required=False)
    status = fields.String(validate=validate.OneOf(['待确认', '已确认']))


class QuotationSchema(Schema):
    """报价单序列化模式"""
    id = fields.Integer(dump_only=True)
    quotation_number = fields.String(dump_only=True)
    request_id = fields.Integer(allow_none=True)
    customer_id = fields.Integer(required=True)
    project_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    project_address = fields.String(validate=validate.Length(max=200), allow_none=True)
    valid_until = fields.Date(required=True)
    payment_terms = fields.String(validate=validate.Length(max=100), allow_none=True)
    delivery_terms = fields.String(validate=validate.Length(max=100), allow_none=True)
    status = fields.String(
        validate=validate.OneOf(['待确认', '已确认', '已拒绝', '已过期']),
        load_default='待确认'
    )
    total_amount = fields.Decimal(dump_only=True, as_string=True)
    notes = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    # 关联状态信息
    is_linked_to_order = fields.Boolean(dump_only=True)
    linked_order_number = fields.String(dump_only=True, allow_none=True)
    
    # 关联信息
    customer = fields.Nested('CustomerSimpleSchema', only=('id', 'name', 'contact', 'phone'), dump_only=True)
    quotation_request = fields.Nested(
        QuotationRequestSchema, 
        only=('id', 'request_number', 'project_name'), 
        dump_only=True
    )
    items = fields.List(fields.Nested(QuotationItemSchema), dump_only=True)


class QuotationSimpleSchema(Schema):
    """简化版报价单序列化模式，用于列表展示"""
    id = fields.Integer(dump_only=True)
    quotation_number = fields.String(dump_only=True)
    customer_id = fields.Integer(dump_only=True)
    customer_name = fields.String(dump_only=True, attribute='customer.name')
    project_name = fields.String(dump_only=True)
    valid_until = fields.Date(dump_only=True)
    status = fields.String(dump_only=True)
    total_amount = fields.Decimal(dump_only=True, as_string=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    # 关联状态信息
    is_linked_to_order = fields.Boolean(dump_only=True)
    linked_order_number = fields.String(dump_only=True, allow_none=True)


class QuotationTemplateItemSchema(Schema):
    """报价模板项目序列化模式"""
    id = fields.Integer(dump_only=True)
    template_id = fields.Integer(required=True, load_only=True)
    product_id = fields.Integer(required=True)
    product_specification_id = fields.Integer(allow_none=True)
    quantity = fields.Integer(required=True, validate=validate.Range(min=1))
    unit_price = fields.Decimal(allow_none=True, as_string=True)  # 允许为空，使用产品当前价格
    discount = fields.Decimal(validate=validate.Range(min=0, max=100), allow_none=True, as_string=True)
    tax_rate = fields.Decimal(validate=validate.Range(min=0, max=100), allow_none=True, as_string=True)
    notes = fields.String(allow_none=True)

    # 产品快照字段
    product_name_snapshot = fields.String(dump_only=True)
    product_model_snapshot = fields.String(dump_only=True)
    product_specification_snapshot = fields.String(dump_only=True)
    product_unit_snapshot = fields.String(dump_only=True)

    # 关联信息
    product = fields.Nested('ProductSimpleSchema', only=('id', 'name', 'model'), dump_only=True)
    specification = fields.Nested('ProductSpecificationSchema', only=('id', 'specification'), dump_only=True)
    
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class QuotationTemplateSchema(Schema):
    """报价模板序列化模式"""
    id = fields.Integer(dump_only=True)
    name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    description = fields.String(allow_none=True)
    is_default = fields.Boolean(load_default=False)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    items = fields.List(fields.Nested(QuotationTemplateItemSchema), required=False, allow_none=True)


class QuotationTemplateSimpleSchema(Schema):
    """简化版报价模板序列化模式"""
    id = fields.Integer(dump_only=True)
    name = fields.String(dump_only=True)
    description = fields.String(dump_only=True)
    is_default = fields.Boolean(dump_only=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
