import { createRouter, createWebHistory } from 'vue-router'
import type { App } from 'vue'
import { setDocumentTitle } from '@/utils/breadcrumb'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 主布局
    {
      path: '/',
      component: () => import('@/layouts/MainLayout.vue'),
      redirect: '/',
      children: [
        // 工作台
        {
          path: '',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue'),
          meta: {
            title: '工作台',
            icon: 'Monitor',
            requiresAuth: true,
          },
        },

        // 收款管理测试页面
        {
          path: 'test-payment',
          name: 'TestPayment',
          component: () => import('@/views/TestPayment.vue'),
          meta: {
            title: '收款管理测试',
            icon: 'Money',
            requiresAuth: false,
          },
        },

        // 客户管理
        {
          path: 'customers',
          name: 'Customers',
          component: () => import('@/views/customers/CustomerList.vue'),
          meta: {
            title: '客户列表',
            icon: 'User',
            requiresAuth: true,
          },
        },
        {
          path: 'customers/new',
          name: 'CustomersNew',
          component: () => import('@/views/customers/CustomerForm.vue'),
          meta: {
            title: '新增客户',
            icon: 'User',
            requiresAuth: true,
          },
        },
        {
          path: 'customers/edit/:id',
          name: 'CustomersEdit',
          component: () => import('@/views/customers/CustomerForm.vue'),
          meta: {
            title: '编辑客户',
            icon: 'User',
            requiresAuth: true,
          },
        },
        {
          path: 'customers/view/:id',
          name: 'CustomersView',
          component: () => import('@/views/customers/CustomerDetail.vue'),
          meta: {
            title: '客户详情',
            icon: 'User',
            requiresAuth: true,
          },
        },

        // 产品管理
        {
          path: 'products',
          name: 'Products',
          component: () => import('@/views/products/ProductList.vue'),
          meta: {
            title: '产品列表',
            icon: 'Box',
            requiresAuth: true,
          },
        },
        {
          path: 'products/new',
          name: 'ProductsNew',
          component: () => import('@/views/products/ProductForm.vue'),
          meta: {
            title: '新增产品',
            icon: 'Box',
            requiresAuth: true,
          },
        },
        {
          path: 'products/edit/:id',
          name: 'ProductsEdit',
          component: () => import('@/views/products/ProductForm.vue'),
          meta: {
            title: '编辑产品',
            icon: 'Goods',
            requiresAuth: true,
          },
        },
        {
          path: 'products/view/:id',
          name: 'ProductsView',
          component: () => import('@/views/products/ProductDetail.vue'),
          meta: {
            title: '产品详情',
            icon: 'Box',
            requiresAuth: true,
          },
        },
        {
          path: 'products/categories',
          name: 'ProductCategories',
          component: () => import('@/views/products/CategoryList.vue'),
          meta: {
            title: '产品分类管理',
            icon: 'FolderOpened',
            requiresAuth: true,
          },
        },
        {
          path: 'products/brands',
          name: 'ProductBrands',
          component: () => import('@/views/products/BrandList.vue'),
          meta: {
            title: '品牌管理',
            icon: 'Star',
            requiresAuth: true,
          },
        },


        // 订单管理
        {
          path: 'orders',
          name: 'Orders',
          component: () => import('@/views/orders/OrderList.vue'),
          meta: {
            title: '订单列表',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'orders/new',
          name: 'OrdersNew',
          component: () => import('@/views/orders/OrderForm.vue'),
          meta: {
            title: '新增订单',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'orders/edit/:id',
          name: 'OrdersEdit',
          component: () => import('@/views/orders/OrderForm.vue'),
          meta: {
            title: '编辑订单',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'orders/view/:id',
          name: 'OrdersView',
          component: () => import('@/views/orders/OrderDetail.vue'),
          meta: {
            title: '订单详情',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'test/excel',
          name: 'ExcelTest',
          component: () => import('@/views/test/ExcelTest.vue'),
          meta: {
            title: 'Excel测试',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'delivery-notes',
          name: 'DeliveryNotes',
          component: () => import('@/views/delivery/DeliveryNoteList.vue'),
          meta: {
            title: '发货单管理',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'delivery-notes/new',
          name: 'DeliveryNotesNew',
          component: () => import('@/views/delivery/DeliveryNoteForm.vue'),
          meta: {
            title: '新建发货单',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'delivery-notes/edit/:id',
          name: 'DeliveryNotesEdit',
          component: () => import('@/views/delivery/DeliveryNoteForm.vue'),
          meta: {
            title: '编辑发货单',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'delivery-notes/view/:id',
          name: 'DeliveryNotesView',
          component: () => import('@/views/delivery/DeliveryNoteDetail.vue'),
          meta: {
            title: '发货单详情',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'return-orders',
          name: 'ReturnOrders',
          component: () => import('@/views/returns/ReturnOrderList.vue'),
          meta: {
            title: '退货管理',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'statements',
          name: 'Statements',
          component: () => import('@/views/statements/StatementList.vue'),
          meta: {
            title: '对账单列表',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'statements/create',
          name: 'StatementsCreate',
          component: () => import('@/views/statements/StatementCreate.vue'),
          meta: {
            title: '新建对账单',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'statements/edit/:id',
          name: 'StatementsEdit',
          component: () => import('@/views/statements/StatementEdit.vue'),
          meta: {
            title: '编辑对账单',
            icon: 'Document',
            requiresAuth: true,
          },
        },
        {
          path: 'statements/:id',
          name: 'StatementsView',
          component: () => import('@/views/statements/StatementDetail.vue'),
          meta: {
            title: '对账单详情',
            icon: 'Document',
            requiresAuth: true,
          },
        },

        // 报价管理
        {
          path: 'quotation-requests',
          name: 'QuotationRequests',
          component: () => import('@/views/quotations/QuotationRequestList.vue'),
          meta: {
            title: '报价需求表',
            icon: 'Files',
            requiresAuth: true,
          },
        },
        {
          path: 'quotation-requests/new',
          name: 'QuotationRequestsNew',
          component: () => import('@/views/quotations/QuotationRequestForm.vue'),
          meta: {
            title: '新增报价需求',
            icon: 'Files',
            requiresAuth: true,
          },
        },
        {
          path: 'quotation-requests/edit/:id',
          name: 'QuotationRequestsEdit',
          component: () => import('@/views/quotations/QuotationRequestForm.vue'),
          meta: {
            title: '编辑报价需求',
            icon: 'Files',
            requiresAuth: true,
          },
        },
        {
          path: 'quotation-requests/view/:id',
          name: 'QuotationRequestsView',
          component: () => import('@/views/quotations/QuotationRequestDetail.vue'),
          meta: {
            title: '报价需求详情',
            icon: 'Files',
            requiresAuth: true,
          },
        },
        {
          path: 'quotations',
          name: 'Quotations',
          component: () => import('@/views/quotations/QuotationList.vue'),
          meta: {
            title: '报价单列表',
            icon: 'Files',
            requiresAuth: true,
          },
        },
        {
          path: 'quotations/new',
          name: 'QuotationsNew',
          component: () => import('@/views/quotations/QuotationForm.vue'),
          meta: {
            title: '新增报价单',
            icon: 'Files',
            requiresAuth: true,
          },
        },
        {
          path: 'quotations/edit/:id',
          name: 'QuotationsEdit',
          component: () => import('@/views/quotations/QuotationForm.vue'),
          meta: {
            title: '编辑报价单',
            icon: 'Files',
            requiresAuth: true,
          },
        },
        {
          path: 'quotations/view/:id',
          name: 'QuotationsView',
          component: () => import('@/views/quotations/QuotationDetail.vue'),
          meta: {
            title: '报价单详情',
            icon: 'Files',
            requiresAuth: true,
          },
        },
        {
          path: 'quotation-templates',
          name: 'QuotationTemplates',
          component: () => import('@/views/HomeView.vue'), // 临时使用HomeView
          meta: {
            title: '报价模板',
            icon: 'Files',
            requiresAuth: true,
          },
        },

        // 财务管理
        {
          path: 'finance/payment-records',
          name: 'PaymentRecords',
          component: () => import('@/views/finance/PaymentRecordList.vue'),
          meta: {
            title: '收款记录',
            icon: 'Money',
            requiresAuth: true,
          },
        },
        {
          path: 'finance/payment-records/new',
          name: 'PaymentRecordsNew',
          component: () => import('@/views/finance/PaymentRecordForm.vue'),
          meta: {
            title: '新增收款记录',
            icon: 'Money',
            requiresAuth: true,
          },
        },
        {
          path: 'finance/payment-records/edit/:id',
          name: 'PaymentRecordsEdit',
          component: () => import('@/views/finance/PaymentRecordForm.vue'),
          meta: {
            title: '编辑收款记录',
            icon: 'Money',
            requiresAuth: true,
          },
        },
        {
          path: 'finance/payment-records/view/:id',
          name: 'PaymentRecordsView',
          component: () => import('@/views/finance/PaymentRecordForm.vue'),
          meta: {
            title: '收款记录详情',
            icon: 'Money',
            requiresAuth: true,
            isView: true,
          },
        },
        {
          path: 'finance/refunds',
          name: 'RefundRecord',
          component: () => import('@/views/finance/RefundRecordList.vue'),
          meta: {
            title: '退款记录',
            icon: 'Money',
            requiresAuth: true,
          },
        },
        {
          path: 'finance/receivables',
          name: 'Receivables',
          component: () => import('@/views/finance/ReceivableStatistics.vue'),
          meta: {
            title: '应收款项',
            icon: 'Money',
            requiresAuth: true,
          },
        },

        // 企业设置 - 已移至系统管理
        // {
        //   path: 'enterprise/company-info',
        //   name: 'CompanyInfo',
        //   component: () => import('@/views/system/SystemSettings.vue'),
        //   meta: {
        //     title: '企业信息',
        //     icon: 'OfficeBuilding',
        //     requiresAuth: true,
        //   },
        // },
        // {
        //   path: 'enterprise/bank-accounts',
        //   name: 'EnterpriseBankAccounts',
        //   component: () => import('@/views/system/BankAccountList.vue'),
        //   meta: {
        //     title: '银行账户',
        //     icon: 'CreditCard',
        //     requiresAuth: true,
        //   },
        // },
        // 用户管理 - 暂时隐藏
        // {
        //   path: 'enterprise/users',
        //   name: 'UserManagement',
        //   component: () => import('@/views/system/UserList.vue'),
        //   meta: {
        //     title: '用户管理',
        //     icon: 'User',
        //     requiresAuth: true,
        //   },
        // },

        // 系统管理
        {
          path: 'system/company-info',
          name: 'SystemCompanyInfo',
          component: () => import('@/views/system/SystemSettings.vue'),
          meta: {
            title: '企业信息',
            icon: 'OfficeBuilding',
            requiresAuth: true,
          },
        },
        {
          path: 'system/bank-accounts',
          name: 'SystemBankAccounts',
          component: () => import('@/views/system/BankAccountList.vue'),
          meta: {
            title: '银行账户',
            icon: 'CreditCard',
            requiresAuth: true,
          },
        },
        {
          path: 'system/backup',
          name: 'DataBackup',
          component: () => import('@/views/system/DataBackup.vue'),
          meta: {
            title: '数据备份',
            icon: 'Download',
            requiresAuth: true,
          },
        },

        // 退货单管理
        {
          path: 'returns',
          name: 'ReturnOrderList',
          component: () => import('@/views/returns/ReturnOrderList.vue'),
          meta: {
            title: '退货单列表',
            icon: 'RefreshLeft',
            requiresAuth: true,
          },
        },
        {
          path: 'returns/create',
          name: 'ReturnOrderCreate',
          component: () => import('@/views/returns/ReturnOrderCreate.vue'),
          meta: {
            title: '新建退货单',
            icon: 'RefreshLeft',
            requiresAuth: true,
          },
        },
        {
          path: 'returns/:id',
          name: 'ReturnOrderDetail',
          component: () => import('@/views/returns/ReturnOrderDetail.vue'),
          meta: {
            title: '退货单详情',
            icon: 'RefreshLeft',
            requiresAuth: true,
          },
        },
        {
          path: 'returns/:id/edit',
          name: 'ReturnOrderEdit',
          component: () => import('@/views/returns/ReturnOrderEdit.vue'),
          meta: {
            title: '编辑退货单',
            icon: 'RefreshLeft',
            requiresAuth: true,
          },
        },

      ],
    },



    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/AboutView.vue'), // 临时使用AboutView
      meta: {
        title: '页面不存在',
      },
    },
  ],
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 暂时禁用认证检查，用于开发测试
  // TODO: 在实际部署时启用认证

  // 检查认证
  // const token = localStorage.getItem('token')
  // const requiresAuth = to.meta.requiresAuth !== false

  // if (requiresAuth && !token) {
  //   // 需要认证但未登录，跳转到登录页
  //   next({ name: 'Login', query: { redirect: to.fullPath } })
  //   return
  // }

  // if (to.name === 'Login' && token) {
  //   // 已登录用户访问登录页，跳转到首页
  //   next({ name: 'Dashboard' })
  //   return
  // }

  next()
})

// 全局后置守卫
router.afterEach(to => {
  // 设置页面标题
  const title = to.meta.title as string
  setDocumentTitle(title)
})

// 安装路由
export function setupRouter(app: App) {
  app.use(router)
}

export default router
