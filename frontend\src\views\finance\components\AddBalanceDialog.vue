<template>
  <el-dialog
    v-model="dialogVisible"
    title="客户余额充值"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent
    >
      <el-form-item label="选择客户" prop="customer_id">
        <el-select
          v-model="form.customer_id"
          placeholder="请选择客户"
          filterable
          style="width: 100%"
          @change="handleCustomerChange"
        >
          <el-option
            v-for="customer in customers"
            :key="customer.id"
            :label="customer.name"
            :value="customer.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="当前余额" v-if="currentBalance !== null">
        <div class="current-balance">
          <span class="balance-amount">{{ formatCurrency(currentBalance) }}</span>
          <el-button
            type="text"
            size="small"
            @click="refreshBalance"
            :loading="balanceLoading"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="充值金额" prop="amount">
        <el-input-number
          v-model="form.amount"
          :min="0.01"
          :max="999999.99"
          :precision="2"
          :step="100"
          style="width: 100%"
          placeholder="请输入充值金额"
        />
      </el-form-item>

      <el-form-item label="充值说明" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入充值说明（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="关联类型" prop="reference_type">
        <el-select
          v-model="form.reference_type"
          placeholder="请选择关联类型（可选）"
          clearable
          style="width: 100%"
        >
          <el-option label="订单" value="order" />
          <el-option label="对账单" value="statement" />
          <el-option label="退款" value="refund" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <el-form-item label="关联ID" prop="reference_id" v-if="form.reference_type">
        <el-input-number
          v-model="form.reference_id"
          :min="1"
          style="width: 100%"
          placeholder="请输入关联记录ID"
        />
      </el-form-item>

      <!-- 充值后预览 -->
      <el-form-item label="充值后余额" v-if="form.amount && currentBalance !== null">
        <div class="preview-balance">
          <span class="preview-amount">{{ formatCurrency(currentBalance + form.amount) }}</span>
          <span class="preview-increase">
            (+{{ formatCurrency(form.amount) }})
          </span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          确认充值
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { paymentApi } from '@/api/payment'
import type { AddBalanceRequest } from '@/types/payment'
import { customerApi } from '@/api/customer'

// 定义props和emits
const props = defineProps<{
  modelValue: boolean
  customerId?: number
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const balanceLoading = ref(false)
const customers = ref<any[]>([])
const currentBalance = ref<number | null>(null)

// 表单数据
const form = reactive<AddBalanceRequest>({
  customer_id: 0,
  amount: 0,
  description: '',
  reference_type: '',
  reference_id: undefined
})

// 表单验证规则
const rules: FormRules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入充值金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '充值金额必须大于0', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 格式化货币
const formatCurrency = (amount: number): string => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 加载客户列表
const loadCustomers = async () => {
  try {
    const response = await customerApi.getList({ per_page: 1000 })
    customers.value = response.data?.data || []
  } catch (error) {
    console.error('加载客户列表失败:', error)
  }
}

// 获取客户余额
const getCustomerBalance = async (customerId: number) => {
  if (!customerId) return
  
  balanceLoading.value = true
  try {
    const response = await paymentApi.getCustomerBalance(customerId)
    currentBalance.value = response.data.balance
  } catch (error) {
    console.error('获取客户余额失败:', error)
    currentBalance.value = null
  } finally {
    balanceLoading.value = false
  }
}

// 客户选择改变
const handleCustomerChange = (customerId: number) => {
  getCustomerBalance(customerId)
}

// 刷新余额
const refreshBalance = () => {
  if (form.customer_id) {
    getCustomerBalance(form.customer_id)
  }
}

// 重置表单
const resetForm = () => {
  form.customer_id = props.customerId || 0
  form.amount = 0
  form.description = ''
  form.reference_type = ''
  form.reference_id = undefined
  currentBalance.value = null
  
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 确认对话框
    await ElMessageBox.confirm(
      `确认为客户充值 ${formatCurrency(form.amount)} 吗？`,
      '确认充值',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    submitLoading.value = true
    
    await paymentApi.addBalance(form)
    
    ElMessage.success('余额充值成功')
    emit('success')
    handleClose()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('余额充值失败:', error)
      ElMessage.error(error.message || '余额充值失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    loadCustomers()
    resetForm()
    
    // 如果传入了客户ID，自动获取余额
    if (props.customerId) {
      getCustomerBalance(props.customerId)
    }
  }
})
</script>

<style scoped>
.current-balance {
  display: flex;
  align-items: center;
  gap: 10px;
}

.balance-amount {
  font-size: 18px;
  font-weight: bold;
  color: #67C23A;
}

.preview-balance {
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-amount {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

.preview-increase {
  font-size: 14px;
  color: #67C23A;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
