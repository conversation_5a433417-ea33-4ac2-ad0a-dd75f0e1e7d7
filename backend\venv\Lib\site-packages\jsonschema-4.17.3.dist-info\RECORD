../../Scripts/jsonschema.exe,sha256=mLpTdrrCeQjlOdVyXtsxrKTcK87gS641Pbuc7PWiznM,108401
jsonschema-4.17.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
jsonschema-4.17.3.dist-info/METADATA,sha256=YnDEs-j6S4At_Fq54u7PqfYG4gQT8E5nrp8vu2PXeXw,7879
jsonschema-4.17.3.dist-info/RECORD,,
jsonschema-4.17.3.dist-info/WHEEL,sha256=NaLmgHHW_f9jTvv_wRh9vcK7c7EK9o5fwsIXMOzoGgM,87
jsonschema-4.17.3.dist-info/entry_points.txt,sha256=vO7rX4Fs_xIVJy2pnAtKgTSxfpnozAVQ0DjCmpMxnWE,51
jsonschema-4.17.3.dist-info/licenses/COPYING,sha256=T5KgFaE8TRoEC-8BiqE0MLTxvHO0Gxa7hGw0Z2bedDk,1057
jsonschema/__init__.py,sha256=FRdJDXN8-AFk-Fj1qclckQsZNeGQB__r_QuMjtRoze4,2187
jsonschema/__main__.py,sha256=Sfz1ZNeogymj_KZxq6JXY3F6O_1v28sLIiskusifQ5s,40
jsonschema/__pycache__/__init__.cpython-313.pyc,,
jsonschema/__pycache__/__main__.cpython-313.pyc,,
jsonschema/__pycache__/_format.cpython-313.pyc,,
jsonschema/__pycache__/_legacy_validators.cpython-313.pyc,,
jsonschema/__pycache__/_types.cpython-313.pyc,,
jsonschema/__pycache__/_utils.cpython-313.pyc,,
jsonschema/__pycache__/_validators.cpython-313.pyc,,
jsonschema/__pycache__/cli.cpython-313.pyc,,
jsonschema/__pycache__/exceptions.cpython-313.pyc,,
jsonschema/__pycache__/protocols.cpython-313.pyc,,
jsonschema/__pycache__/validators.cpython-313.pyc,,
jsonschema/_format.py,sha256=P7gjEZzWa1dU0wDu3NbyG1cNW6drM3i454a_7sJE8TY,14575
jsonschema/_legacy_validators.py,sha256=0KDc3X0gTzuW52NRQJ123ZW_IBLcIDsBaVFVfMo_vNY,10549
jsonschema/_types.py,sha256=YgKkjzf97pKRsiuc5RN76jJPGCpmdMHJlhcOVWjCW78,5425
jsonschema/_utils.py,sha256=D3oRGTSk6llsZDFdDAVQWBEi62Uy_4SWVbjPiG9PJcs,10429
jsonschema/_validators.py,sha256=4H0TI2BIXhmJrmF3iC1VzKgxwhCfiGreJfBWBtP20HY,15956
jsonschema/benchmarks/__init__.py,sha256=A0sQrxDBVHSyQ-8ru3L11hMXf3q9gVuB9x_YgHb4R9M,70
jsonschema/benchmarks/__pycache__/__init__.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/issue232.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/json_schema_test_suite.cpython-313.pyc,,
jsonschema/benchmarks/issue232.py,sha256=GKQBwm03sf-pPSxBxc4YDvBBnMYknOk6m-WtTntN5VE,506
jsonschema/benchmarks/issue232/issue.json,sha256=eaPOZjMRu5u8RpKrsA9uk7ucPZS5tkKG4D_hkOTQ3Hk,117105
jsonschema/benchmarks/json_schema_test_suite.py,sha256=PvfabpUYcF4_7csYDTcTauED8rnFEGYbdY5RqTXD08s,320
jsonschema/cli.py,sha256=DJhWQs6X5nsmWfUqrnSjmAdzg5Sbd3igcRGguHJAEQU,8518
jsonschema/exceptions.py,sha256=asZ8nwPwuQlMCXTb7Ztyi1OuxkqRiPnxK9TuwiDqGRo,11336
jsonschema/protocols.py,sha256=zfj-Rmc2rsNIQjEIFnnMsHlYEoS-rprnBHTG5pgvCy0,7295
jsonschema/schemas/draft2019-09.json,sha256=e3YbPhIfCgyh6ioLjizIVrz4AWBLgmjXG6yqICvAwTs,1785
jsonschema/schemas/draft2020-12.json,sha256=Qdp29a-3zgYtJI92JGOpL3ykfk4PkFsiS6av7vkd7Q8,2452
jsonschema/schemas/draft3.json,sha256=LPdfZENvtb43Si6qJ6uLfh_WUcm0ba6mxnsC_WTiRYs,2600
jsonschema/schemas/draft4.json,sha256=4UidC0dV8CeTMCWR0_y48Htok6gqlPJIlfjk7fEbguI,4357
jsonschema/schemas/draft6.json,sha256=wp386fVINcOgbAOzxdXsDtp3cGVo-cTffPvHVmpRAG0,4437
jsonschema/schemas/draft7.json,sha256=PVOSCIJhYGxVm2A_OFMpyfGrRbXWZ-uZBodFOwVdQF4,4819
jsonschema/schemas/vocabularies/draft2019-09/applicator,sha256=aJUQDplyb7sQcFhRK77D7P1LJOj9L6zuPlBe5ysNTDE,1860
jsonschema/schemas/vocabularies/draft2019-09/content,sha256=m31PVaTi_bAsQwBo_f-rxzKt3OI42j8d8mkCScM1MnQ,517
jsonschema/schemas/vocabularies/draft2019-09/core,sha256=taLElX9kldClCB8ECevooU5BOayyA_x0hHH47eKvWyw,1531
jsonschema/schemas/vocabularies/draft2019-09/meta-data,sha256=1H4kRd1qgicaKY2DzGxsuNSuHhXg3Fa-zTehY-zwEoY,892
jsonschema/schemas/vocabularies/draft2019-09/validation,sha256=HlJsHTNac0gF_ILPV5jBK5YK19olF8Zs2lobCTWcPBw,2834
jsonschema/schemas/vocabularies/draft2020-12/applicator,sha256=xKbkFHuR_vf-ptwFjLG_k0AvdBS3ZXiosWqvHa1qrO8,1659
jsonschema/schemas/vocabularies/draft2020-12/content,sha256=CDQ3R3ZOSlgUJieTz01lIFenkThjxZUNQyl-jh_axbY,519
jsonschema/schemas/vocabularies/draft2020-12/core,sha256=wtEqjk3RHTNt_IOj9mOqTGnwtJs76wlP_rJbUxb0gD0,1564
jsonschema/schemas/vocabularies/draft2020-12/format,sha256=UOu_55BhGoSbjMQAoJwdDg-2q1wNQ6DyIgH9NiUFa_Q,403
jsonschema/schemas/vocabularies/draft2020-12/format-annotation,sha256=q8d1rf79idIjWBcNm_k_Tr0jSVY7u-3WDwK-98gSvMA,448
jsonschema/schemas/vocabularies/draft2020-12/format-assertion,sha256=xSJCuaG7eGsmw-gset1CjDH5yW5XXc6Z5W6l_qptogw,445
jsonschema/schemas/vocabularies/draft2020-12/meta-data,sha256=j3bW4U9Bubku-TO3CM3FFEyLUmhlGtEZGEhfsXVPHHY,892
jsonschema/schemas/vocabularies/draft2020-12/unevaluated,sha256=Lb-8tzmUtnCwl2SSre4f_7RsIWgnhNL1pMpWH54tDLQ,506
jsonschema/schemas/vocabularies/draft2020-12/validation,sha256=cBCjHlQfMtK-ch4t40jfdcmzaHaj7TBId_wKvaHTelg,2834
jsonschema/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jsonschema/tests/__pycache__/__init__.cpython-313.pyc,,
jsonschema/tests/__pycache__/_helpers.cpython-313.pyc,,
jsonschema/tests/__pycache__/_suite.cpython-313.pyc,,
jsonschema/tests/__pycache__/fuzz_validate.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_cli.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_deprecations.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_exceptions.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_format.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_jsonschema_test_suite.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_types.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_utils.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_validators.cpython-313.pyc,,
jsonschema/tests/_helpers.py,sha256=yoWpWVYq4auuTPPd1_8FXV77RlczQtiyutSh6c191ZM,618
jsonschema/tests/_suite.py,sha256=EyzqI3EGlJiMX_o3C-W51J2m4JVRU0uqrWFjdmCspgI,7052
jsonschema/tests/fuzz_validate.py,sha256=fUA7yTJIihaCwJplkUehZeyB84HcXEcqtY5oPJXIO7I,1114
jsonschema/tests/test_cli.py,sha256=e9kjwfVp1sSBCF9ffGH7FwgcRlTaBZUehcWiM-BmmLk,28857
jsonschema/tests/test_deprecations.py,sha256=uf0ct-i9V3UBc3zm8DRcgxCitURKl2-Qt3wpAUoK0GU,9284
jsonschema/tests/test_exceptions.py,sha256=vvh4zfHVvE9egGn4qc1jQo2s8fh09oVt1WcCRpaKivM,19639
jsonschema/tests/test_format.py,sha256=h79gKpcKV58qOQOn8TsJNf2HFkrUYzNZ3Uai_cc2nQE,3810
jsonschema/tests/test_jsonschema_test_suite.py,sha256=-sUumFw_1MTcfMGrG5-Zw3_yldVHCto6SxPh-aoh64g,18090
jsonschema/tests/test_types.py,sha256=_m7chMGj9U5G57jB_CmUuP5mSit3PET584ehoIlKkf4,6983
jsonschema/tests/test_utils.py,sha256=lJRVYyQeZQTUCTU_M3BhlkxPMgjsc8KQCd7U_Qkook8,3749
jsonschema/tests/test_validators.py,sha256=JW4Y5mzVMuumYdBCEFhxPO7EIqN-k2MnbwzBXHq7LIE,78785
jsonschema/validators.py,sha256=zwfdaiQFvH-Z9EoEH-G_Bh8hCQzOirJg6-_AFoT8_ow,38150
