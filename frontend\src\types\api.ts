// 通用API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean
  data: T
  message?: string
  code?: number
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 兼容后端的分页响应类型
export interface PaginatedListResponse<T> {
  items: T[]
  pagination: {
    page: number
    per_page: number
    total: number
    pages: number
    has_prev: boolean
    has_next: boolean
  }
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 客户相关类型
export interface Customer {
  id: number
  name: string
  contact: string
  phone?: string
  email?: string
  address?: string
  tax_id?: string
  source?: string
  level: string
  status: string
  notes?: string
  created_at: string
  updated_at: string
  bank_accounts?: CustomerBankAccount[]
  delivery_addresses?: CustomerDeliveryAddress[]
}

export interface CustomerBankAccount {
  id: number
  customer_id: number
  bank_name: string
  account_name: string
  account_number: string
  is_default: boolean
  notes?: string
  created_at: string
  updated_at: string
}

export interface CustomerDeliveryAddress {
  id: number
  customer_id: number
  province: string
  city: string
  district: string
  detailed_address: string
  contact_person: string
  contact_phone: string
  is_default: boolean
  notes?: string
  full_address?: string
  created_at: string
  updated_at: string
}

export interface CustomerDeliveryAddress {
  id: number
  customer_id: number
  address: string
  contact_person: string
  phone: string
  is_default: boolean
}

// 产品相关类型
export interface Product {
  id: number
  name: string
  model: string
  unit: string
  category_id: number
  brand_id?: number
  image?: string
  description?: string
  notes?: string
  status: string
  created_at: string
  updated_at: string

  // 关联数据
  category?: ProductCategory
  brand?: Brand
  category_name?: string
  brand_name?: string
  specifications?: ProductSpecification[]
  attributes?: ProductAttribute[]
  images?: ProductImage[]

  // 计算字段
  price_range?: string
  main_image?: string
}

export interface ProductSpecification {
  id: number
  product_id: number
  specification: string
  cost_price: number
  suggested_price: number
  min_price?: number
  max_price?: number
  tax_rate: number
  is_default: boolean
  notes?: string
  created_at: string
  updated_at: string
}

export interface ProductAttribute {
  id: number
  product_id: number
  attribute_name: string
  attribute_value: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface ProductImage {
  id: number
  product_id: number
  image_url: string
  alt_text?: string
  sort_order: number
  is_main: boolean
  created_at: string
  updated_at: string
}

export interface ProductCategory {
  id: number
  name: string
  description?: string
  parent_id?: number
  sort_order: number
  created_at: string
  updated_at: string
  children?: ProductCategory[]
}

export interface Brand {
  id: number
  name: string
  description?: string
  logo?: string
  website?: string
  created_at: string
  updated_at: string
}

// 报价相关类型
export interface Quotation {
  id: number
  quotation_number: string
  request_id?: number
  customer_id: number
  project_name: string
  project_address?: string
  valid_until: string
  payment_terms?: string
  delivery_terms?: string
  status: string
  total_amount: string
  notes?: string
  created_at: string
  updated_at: string

  // 关联数据
  customer?: Customer
  quotation_request?: QuotationRequest
  items?: QuotationItem[]

  // 显示字段
  customer_name?: string
}

export interface QuotationItem {
  id: number
  quotation_id: number
  product_id: number
  product_specification_id?: number
  quantity: number
  unit_price: string
  discount?: string
  tax_rate?: string
  total_price: string
  notes?: string
  created_at: string
  updated_at: string

  // 关联数据
  product?: Product
  specification?: ProductSpecification

  // 显示字段
  product_name?: string
  product_model?: string
  product_unit?: string
}

export interface QuotationRequest {
  id: number
  request_number: string
  customer_id?: number
  project_name?: string
  project_address?: string
  expected_date?: string
  status: string
  notes?: string
  created_at: string
  updated_at: string

  // 关联数据
  customer?: Customer
  items?: QuotationRequestItem[]
  quotations?: Quotation[]

  // 显示字段
  customer_name?: string
}

export interface QuotationRequestItem {
  id: number
  request_id: number
  product_name: string
  product_model?: string
  product_spec?: string
  quantity: number
  unit: string
  notes?: string
  created_at: string
  updated_at: string

  // 匹配结果
  matched_product_id?: number
  matched_product_specification_id?: number
  matched_product?: Product
  matched_specification?: ProductSpecification
}

export interface QuotationTemplate {
  id: number
  name: string
  description?: string
  is_default: boolean
  created_at: string
  updated_at: string

  // 关联数据
  items?: QuotationTemplateItem[]
}

export interface QuotationTemplateItem {
  id: number
  template_id: number
  product_id: number
  product_specification_id: number
  quantity: number
  unit_price?: string
  discount?: string
  tax_rate?: string
  notes?: string
  created_at: string
  updated_at: string

  // 产品快照
  product_name_snapshot?: string
  product_model_snapshot?: string
  product_specification_snapshot?: string
  product_unit_snapshot?: string

  // 关联数据
  product?: Product
  specification?: ProductSpecification
}

// 订单相关类型
export interface Order {
  id: number
  order_number: string
  quotation_id?: number
  customer_id: number
  project_name?: string
  project_address?: string
  expected_date?: string
  payment_terms?: string
  delivery_terms?: string
  status: string
  total_amount: string
  paid_amount?: number
  payment_status?: string
  notes?: string
  created_at: string
  updated_at: string

  // 关联数据
  customer?: Customer
  quotation?: Quotation
  items?: OrderItem[]
  delivery_notes?: DeliveryNote[]
  payment_records?: PaymentRecord[]
  status_history?: OrderStatusHistory[]
  attachments?: OrderAttachment[]

  // 显示字段
  customer_name?: string
  quotation_number?: string
}

export interface OrderItem {
  id: number
  order_id: number
  product_id: number
  product_specification_id?: number
  quantity: number
  unit_price: string
  discount?: string
  tax_rate?: string
  total_price: string
  notes?: string
  created_at: string
  updated_at: string

  // 关联数据
  product?: Product
  specification?: ProductSpecification

  // 显示字段
  product_name?: string
  product_model?: string
  product_unit?: string
}

export interface OrderStatusHistory {
  id: number
  order_id: number
  status: string
  comment?: string
  operator?: string
  created_at: string
}

export interface OrderAttachment {
  id: number
  order_id: number
  file_name: string
  file_path: string
  file_size: number
  file_type: string
  uploader?: string
  upload_time: string
}

// 发货单类型
export interface DeliveryNote {
  id: number
  delivery_number: string
  order_id: number
  status: 'pending' | 'shipped' | 'delivered'
  delivery_date?: string
  tracking_number?: string
  created_at: string
  updated_at: string
  order?: Order
}

// 退货单类型
export interface ReturnOrder {
  id: number
  return_number: string
  order_id: number
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  reason: string
  return_amount: number
  created_at: string
  updated_at: string
  order?: Order
}

// 对账单类型
export interface Statement {
  id: number
  statement_number: string
  customer_id: number
  period_start: string
  period_end: string
  total_amount: number
  status: 'draft' | 'sent' | 'confirmed'
  created_at: string
  updated_at: string
  customer?: Customer
}

// 财务相关类型
export interface PaymentRecord {
  id: number
  order_id: number
  amount: number
  payment_method: string
  payment_date: string
  status: 'pending' | 'confirmed' | 'failed'
  created_at: string
  updated_at: string
  order?: Order
}

export interface RefundRecord {
  id: number
  order_id: number
  amount: number
  reason: string
  refund_date: string
  status: 'pending' | 'approved' | 'completed'
  created_at: string
  updated_at: string
  order?: Order
}

// 仪表板统计类型（匹配后端API）
export interface DashboardStats {
  customers_total: number
  products_total: number
  quotations_total: number
  orders_total: number
  orders_pending: number
  orders_completed: number
  revenue_total: number
  revenue_month: number
  last_updated: string
}

// 图表数据类型
export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string[]
    borderColor?: string[]
  }[]
}
