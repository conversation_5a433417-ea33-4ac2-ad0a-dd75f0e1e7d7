"""
退货管理API模块
提供退货单的完整CRUD操作、状态管理、退货审批和退款处理功能
确保与订单和发货单模块的协同工作
"""
from flask import request, current_app
from flask_restx import Resource, Namespace, fields
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, or_, desc, func
from marshmallow import ValidationError
from datetime import datetime, timedelta
import uuid

from app.models.order import Order, OrderProduct, DeliveryNote, DeliveryNoteItem
from app.models.return_order import ReturnOrder, ReturnOrderItem
from app.models.customer import Customer
from app.models.product import Product, ProductSpecification
from app.schemas.order import (
    ReturnOrderSchema, ReturnOrderSimpleSchema, ReturnOrderItemSchema
)
from app.utils.response import success_response, error_response
from app.utils.pagination import PaginationHelper
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('returns', description='退货管理API')

# 自动从Marshmallow Schema生成API模型
return_order_model = create_input_model(api, ReturnOrderSchema, 'ReturnOrderInput')
return_order_item_model = create_input_model(api, ReturnOrderItemSchema, 'ReturnOrderItemInput')

# 状态更新模型（手动定义，因为没有对应的Schema）
return_order_status_model = api.model('ReturnOrderStatus', {
    'status': fields.String(required=True, description='新状态'),
    'notes': fields.String(description='备注')
})

# 退货单状态定义
RETURN_ORDER_STATUSES = ['待确认', '退货中', '已签收', '已拒绝', '已取消']

# 状态流转规则
STATUS_TRANSITIONS = {
    '待确认': ['退货中', '已拒绝', '已取消'],
    '退货中': ['已签收', '已取消'],
    '已签收': [],
    '已拒绝': [],
    '已取消': []
}


@api.route('')
class ReturnOrderList(Resource):
    @api.doc('get_return_orders')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('order_id', '订单ID', type='integer')
    @api.param('return_number', '退货单编号搜索')
    @api.param('status', '退货状态')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    @api.param('customer_name', '客户名称搜索')
    @api.param('reason', '退货原因搜索')
    def get(self):
        """获取退货单列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 20, type=int), 100)
            order_id = request.args.get('order_id', type=int)
            return_number = request.args.get('return_number', '').strip()
            status = request.args.get('status', '').strip()
            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()
            customer_name = request.args.get('customer_name', '').strip()
            reason = request.args.get('reason', '').strip()

            # 构建查询
            query = ReturnOrder.query.options(
                joinedload(ReturnOrder.order).joinedload(Order.customer)
            )

            # 应用过滤条件
            if order_id:
                query = query.filter(ReturnOrder.order_id == order_id)
            
            if return_number:
                query = query.filter(ReturnOrder.return_number.ilike(f'%{return_number}%'))
            
            if status:
                query = query.filter(ReturnOrder.status == status)
            
            if reason:
                query = query.filter(ReturnOrder.reason.ilike(f'%{reason}%'))
            
            if customer_name:
                query = query.join(Order).join(Customer).filter(
                    Customer.name.ilike(f'%{customer_name}%')
                )
            
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(ReturnOrder.return_date >= start)
                except ValueError:
                    return error_response("开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            if end_date:
                try:
                    end = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(ReturnOrder.return_date <= end)
                except ValueError:
                    return error_response("结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            # 排序
            query = query.order_by(desc(ReturnOrder.created_at))

            # 分页
            pagination = query.paginate(
                page=page, 
                per_page=per_page, 
                error_out=False
            )

            # 序列化数据
            return_orders = []
            for return_order in pagination.items:
                data = ReturnOrderSimpleSchema().dump(return_order)
                # 添加客户信息
                if return_order.order and return_order.order.customer:
                    data['customer_name'] = return_order.order.customer.name
                return_orders.append(data)

            return success_response({
                'items': return_orders,
                'pagination': {
                    'page': pagination.page,
                    'per_page': pagination.per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                }
            }, '获取退货单列表成功')

        except Exception as e:
            current_app.logger.error(f"获取退货单列表失败: {str(e)}")
            return error_response(f"获取退货单列表失败: {str(e)}", code=500)

    @api.doc('create_return_order',
             body=return_order_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 404: 'Order Not Found',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建退货单"""
        try:
            data = request.get_json() or {}
            items_data = data.pop('items', [])

            # 数据验证
            return_order_schema = ReturnOrderSchema(exclude=(
                'id', 'return_number', 'order', 'items', 'created_at', 'updated_at'
            ))
            validated_data = return_order_schema.load(data)

            # 检查订单是否存在
            order = Order.query.get(validated_data['order_id'])
            if not order:
                return error_response("订单不存在", code=404)

            # 检查订单状态（使用order_status字段）
            if order.order_status in ['已取消', '待确认']:
                return error_response("订单状态不允许创建退货单", code=400)

            # 生成退货单编号
            return_number = f"RT{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"
            validated_data['return_number'] = return_number

            # 创建退货单对象
            return_order = ReturnOrder(**validated_data)
            db.session.add(return_order)
            db.session.flush()  # 获取ID

            # 创建退货单项目
            if items_data:
                for item_data in items_data:
                    # 验证订单产品
                    order_product = OrderProduct.query.filter_by(
                        id=item_data.get('order_product_id'),
                        order_id=order.id
                    ).first()
                    
                    if not order_product:
                        db.session.rollback()
                        return error_response(f"订单产品ID {item_data.get('order_product_id')} 不存在或不属于该订单", code=400)

                    # 检查退货数量（不能超过已发货数量）
                    delivered_quantity = order_product.delivered_quantity or 0
                    if item_data['quantity'] > delivered_quantity:
                        db.session.rollback()
                        return error_response(f"退货数量超过已发货数量，产品 {order_product.product_name} 已发货数量为 {delivered_quantity}", code=400)

                    # 创建退货单项目
                    return_item = ReturnOrderItem(
                        return_order_id=return_order.id,
                        order_product_id=item_data['order_product_id'],
                        product_specification_id=order_product.product_specification_id,
                        quantity=item_data['quantity'],
                        reason=item_data.get('reason', ''),
                        notes=item_data.get('notes', '')
                    )

                    # 填充快照字段
                    product_spec = order_product.product_specification
                    product = product_spec.product if product_spec else None
                    
                    if product:
                        return_item.product_name = product.name
                        return_item.product_model = product.model
                        return_item.product_unit = product.unit
                    if product_spec:
                        return_item.specification_description = product_spec.specification

                    db.session.add(return_item)

            # 计算并更新退货单总金额
            return_order.update_total_amount()

            db.session.commit()

            # 返回创建的退货单信息
            created_return_order = ReturnOrder.query.get(return_order.id)
            response_data = ReturnOrderSchema().dump(created_return_order)
            return success_response(response_data, "退货单创建成功", code=201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            db.session.rollback()
            return error_response("数据验证失败", errors=e.messages, code=400)
        except Exception as e:
            current_app.logger.error(f"创建退货单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"创建退货单失败: {str(e)}", code=500)


@api.route('/<int:return_order_id>')
class ReturnOrderDetail(Resource):
    @api.doc('get_return_order')
    def get(self, return_order_id):
        """获取退货单详情"""
        try:
            return_order = ReturnOrder.query.options(
                joinedload(ReturnOrder.order).joinedload(Order.customer),
                joinedload(ReturnOrder.items)
            ).get(return_order_id)

            if not return_order:
                return error_response("退货单不存在", code=404)

            return_order_data = ReturnOrderSchema().dump(return_order)

            # 添加客户信息
            if return_order.order and return_order.order.customer:
                return_order_data['customer_name'] = return_order.order.customer.name

            return success_response(return_order_data, "获取退货单详情成功")

        except Exception as e:
            current_app.logger.error(f"获取退货单详情失败: {str(e)}")
            return error_response(f"获取退货单详情失败: {str(e)}", code=500)

    @api.doc('update_return_order',
             body=return_order_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Return Order Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, return_order_id):
        """更新退货单"""
        try:
            return_order = ReturnOrder.query.get(return_order_id)
            if not return_order:
                return error_response("退货单不存在", code=404)

            # 检查是否可以编辑
            if return_order.status not in ['待确认']:
                return error_response("当前状态的退货单不可编辑", code=400)

            data = request.get_json() or {}
            items_data = data.pop('items', None)

            # 验证主表数据
            return_order_schema = ReturnOrderSchema(exclude=(
                'id', 'return_number', 'order', 'items', 'created_at', 'updated_at'
            ))
            validated_data = return_order_schema.load(data, partial=True)

            # 更新主表字段
            for key, value in validated_data.items():
                setattr(return_order, key, value)

            # 处理项目数据
            if items_data is not None:
                # 删除现有项目
                for item in return_order.items:
                    db.session.delete(item)

                # 添加新项目
                for item_data in items_data:
                    # 验证订单产品
                    order_product = OrderProduct.query.filter_by(
                        id=item_data.get('order_product_id'),
                        order_id=return_order.order_id
                    ).first()

                    if not order_product:
                        db.session.rollback()
                        return error_response(f"订单产品ID {item_data.get('order_product_id')} 不存在或不属于该订单", code=400)

                    # 检查退货数量
                    delivered_quantity = order_product.delivered_quantity or 0
                    if item_data['quantity'] > delivered_quantity:
                        db.session.rollback()
                        return error_response(f"退货数量超过已发货数量，产品已发货数量为 {delivered_quantity}", code=400)

                    # 创建退货单项目
                    return_item = ReturnOrderItem(
                        return_order_id=return_order.id,
                        order_product_id=item_data['order_product_id'],
                        product_specification_id=order_product.product_specification_id,
                        quantity=item_data['quantity'],
                        reason=item_data.get('reason', ''),
                        notes=item_data.get('notes', '')
                    )

                    # 填充快照字段
                    product_spec = order_product.product_specification
                    product = product_spec.product if product_spec else None

                    if product:
                        return_item.product_name = product.name
                        return_item.product_model = product.model
                        return_item.product_unit = product.unit
                    if product_spec:
                        return_item.specification_description = product_spec.specification

                    db.session.add(return_item)

            # 计算并更新退货单总金额
            return_order.update_total_amount()

            db.session.commit()

            # 返回更新后的数据
            updated_data = ReturnOrderSchema().dump(return_order)
            return success_response(updated_data, "退货单更新成功")

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            db.session.rollback()
            return error_response("数据验证失败", errors=e.messages, code=400)
        except Exception as e:
            current_app.logger.error(f"更新退货单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新退货单失败: {str(e)}", code=500)

    @api.doc('delete_return_order')
    def delete(self, return_order_id):
        """删除退货单"""
        try:
            return_order = ReturnOrder.query.get(return_order_id)
            if not return_order:
                return error_response("退货单不存在", code=404)

            # 检查是否可以删除
            if return_order.status not in ['待确认']:
                return error_response("只有待确认状态的退货单可以删除", code=400)

            db.session.delete(return_order)
            db.session.commit()
            return success_response(message="退货单删除成功")

        except Exception as e:
            current_app.logger.error(f"删除退货单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"删除退货单失败: {str(e)}", code=500)


@api.route('/<int:return_order_id>/status')
class ReturnOrderStatus(Resource):
    @api.doc('update_return_order_status',
             body=return_order_status_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Return Order Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, return_order_id):
        """更新退货单状态"""
        try:
            return_order = ReturnOrder.query.get(return_order_id)
            if not return_order:
                return error_response("退货单不存在", code=404)

            data = request.get_json() or {}
            new_status = data.get('status', '').strip()
            notes = data.get('notes', '').strip()

            # 调试信息
            current_app.logger.info(f"收到状态更新请求: {data}")
            current_app.logger.info(f"新状态: '{new_status}' (长度: {len(new_status)}, 字节: {new_status.encode('utf-8')})")
            current_app.logger.info(f"允许的状态: {RETURN_ORDER_STATUSES}")

            # 验证状态
            if new_status not in RETURN_ORDER_STATUSES:
                current_app.logger.error(f"状态验证失败: '{new_status}' 不在 {RETURN_ORDER_STATUSES} 中")
                return error_response(f"无效的状态值，允许的状态: {', '.join(RETURN_ORDER_STATUSES)}", code=400)

            # 检查状态流转规则
            current_status = return_order.status
            allowed_statuses = STATUS_TRANSITIONS.get(current_status, [])

            if new_status not in allowed_statuses:
                return error_response(f"不能从 '{current_status}' 状态转换到 '{new_status}' 状态", code=400)

            # 更新状态（使用模型方法，会自动处理应收账款）
            return_order.update_status(new_status)
            if notes:
                return_order.notes = notes

            db.session.commit()

            # 返回更新后的数据
            response_data = ReturnOrderSchema().dump(return_order)
            return success_response(response_data, f"退货单状态已更新为 '{new_status}'")

        except Exception as e:
            current_app.logger.error(f"更新退货单状态失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新退货单状态失败: {str(e)}", code=500)


@api.route('/statuses')
class ReturnOrderStatuses(Resource):
    @api.doc('get_return_order_statuses')
    def get(self):
        """获取退货单状态列表"""
        try:
            return success_response({
                'statuses': RETURN_ORDER_STATUSES,
                'transitions': STATUS_TRANSITIONS,
                'descriptions': {
                    '待确认': '退货申请已提交，等待确认',
                    '退货中': '退货申请已确认，正在处理退货',
                    '已签收': '退货已签收，退款已处理',
                    '已拒绝': '退货申请被拒绝',
                    '已取消': '退货申请已取消'
                }
            }, "获取退货单状态列表成功")

        except Exception as e:
            current_app.logger.error(f"获取退货单状态列表失败: {str(e)}")
            return error_response(f"获取退货单状态列表失败: {str(e)}", code=500)


@api.route('/available-delivery-notes')
class AvailableDeliveryNotes(Resource):
    @api.doc('get_available_delivery_notes')
    @api.param('order_id', '订单ID', type='integer')
    @api.param('customer_id', '客户ID', type='integer')
    def get(self):
        """获取可退货的发货单"""
        try:
            order_id = request.args.get('order_id', type=int)
            customer_id = request.args.get('customer_id', type=int)

            # 构建查询
            query = DeliveryNote.query.options(
                joinedload(DeliveryNote.order).joinedload(Order.customer),
                joinedload(DeliveryNote.items)
            ).filter(
                DeliveryNote.status.in_(['已签收'])  # 只有已签收的发货单可以退货
            )

            if order_id:
                query = query.filter(DeliveryNote.order_id == order_id)

            if customer_id:
                query = query.join(Order).filter(Order.customer_id == customer_id)

            delivery_notes = query.order_by(desc(DeliveryNote.delivery_date)).all()

            # 构建响应数据
            available_delivery_notes = []
            for delivery_note in delivery_notes:
                delivery_data = {
                    'id': delivery_note.id,
                    'delivery_number': delivery_note.delivery_number,
                    'order_id': delivery_note.order_id,
                    'order_number': delivery_note.order.order_number if delivery_note.order else '',
                    'customer_name': delivery_note.order.customer.name if delivery_note.order and delivery_note.order.customer else '',
                    'delivery_date': delivery_note.delivery_date.isoformat() if delivery_note.delivery_date else None,
                    'status': delivery_note.status,
                    'items': []
                }

                # 添加发货单项目信息
                for item in delivery_note.items:
                    # 计算已退货数量
                    returned_quantity = db.session.query(func.sum(ReturnOrderItem.quantity)).filter(
                        ReturnOrderItem.order_product_id == item.order_product_id,
                        ReturnOrderItem.return_order.has(ReturnOrder.status.in_(['退货中', '已完成']))
                    ).scalar() or 0

                    # 可退货数量
                    returnable_quantity = item.quantity - returned_quantity

                    if returnable_quantity > 0:  # 只显示可退货的项目
                        delivery_data['items'].append({
                            'id': item.id,
                            'order_product_id': item.order_product_id,
                            'product_specification_id': item.product_specification_id,
                            'product_name': item.product_name,
                            'product_model': item.product_model,
                            'product_unit': item.product_unit,
                            'specification_description': item.specification_description,
                            'delivered_quantity': item.quantity,
                            'returned_quantity': returned_quantity,
                            'returnable_quantity': returnable_quantity
                        })

                # 只添加有可退货项目的发货单
                if delivery_data['items']:
                    available_delivery_notes.append(delivery_data)

            return success_response(available_delivery_notes, "获取可退货发货单成功")

        except Exception as e:
            current_app.logger.error(f"获取可退货发货单失败: {str(e)}")
            return error_response(f"获取可退货发货单失败: {str(e)}", code=500)


@api.route('/from-order/<int:order_id>')
class ReturnOrderFromOrder(Resource):
    @api.doc('create_return_order_from_order')
    def post(self, order_id):
        """基于订单创建退货单模板"""
        try:
            # 检查订单是否存在
            order = Order.query.options(
                joinedload(Order.customer),
                joinedload(Order.products)
            ).get(order_id)

            if not order:
                return error_response("订单不存在", code=404)

            # 检查订单状态
            if order.status in ['已取消', '待确认']:
                return error_response("订单状态不允许创建退货单", code=400)

            # 获取可退货的产品
            returnable_products = []
            for order_product in order.products:
                delivered_quantity = order_product.delivered_quantity or 0

                if delivered_quantity > 0:
                    # 计算已退货数量
                    returned_quantity = db.session.query(func.sum(ReturnOrderItem.quantity)).filter(
                        ReturnOrderItem.order_product_id == order_product.id,
                        ReturnOrderItem.return_order.has(ReturnOrder.status.in_(['退货中', '已完成']))
                    ).scalar() or 0

                    # 可退货数量
                    returnable_quantity = delivered_quantity - returned_quantity

                    if returnable_quantity > 0:
                        # 获取产品信息
                        product_spec = order_product.product_specification
                        product = product_spec.product if product_spec else None

                        returnable_products.append({
                            'order_product_id': order_product.id,
                            'product_specification_id': order_product.product_specification_id,
                            'product_name': product.name if product else '未知产品',
                            'product_model': product.model if product else '',
                            'product_unit': product.unit if product else '',
                            'specification_description': product_spec.specification if product_spec else '',
                            'ordered_quantity': order_product.quantity,
                            'delivered_quantity': delivered_quantity,
                            'returned_quantity': returned_quantity,
                            'returnable_quantity': returnable_quantity
                        })

            # 构建退货单模板
            template_data = {
                'order_id': order.id,
                'order_number': order.order_number,
                'customer_name': order.customer.name if order.customer else '',
                'project_name': order.project_name,
                'return_date': datetime.now().isoformat(),
                'status': '待确认',
                'reason': '',
                'notes': '',
                'available_products': returnable_products
            }

            return success_response(template_data, "获取退货单模板成功")

        except Exception as e:
            current_app.logger.error(f"基于订单创建退货单模板失败: {str(e)}")
            return error_response(f"基于订单创建退货单模板失败: {str(e)}", code=500)


@api.route('/product-returned-quantity')
class ProductReturnedQuantity(Resource):
    @api.doc('get_product_returned_quantity')
    @api.param('order_product_id', '订单产品ID', type='integer', required=True)
    def get(self):
        """查询产品已退货数量"""
        try:
            order_product_id = request.args.get('order_product_id', type=int)

            if not order_product_id:
                return error_response("订单产品ID不能为空", code=400)

            # 检查订单产品是否存在
            order_product = OrderProduct.query.get(order_product_id)
            if not order_product:
                return error_response("订单产品不存在", code=404)

            # 计算已退货数量
            returned_quantity = db.session.query(func.sum(ReturnOrderItem.quantity)).filter(
                ReturnOrderItem.order_product_id == order_product_id,
                ReturnOrderItem.return_order.has(ReturnOrder.status.in_(['退货中', '已完成']))
            ).scalar() or 0

            # 计算可退货数量
            delivered_quantity = order_product.delivered_quantity or 0
            returnable_quantity = delivered_quantity - returned_quantity

            response_data = {
                'order_product_id': order_product_id,
                'ordered_quantity': order_product.quantity,
                'delivered_quantity': delivered_quantity,
                'returned_quantity': returned_quantity,
                'returnable_quantity': max(0, returnable_quantity)
            }

            return success_response(response_data, "查询产品退货数量成功")

        except Exception as e:
            current_app.logger.error(f"查询产品退货数量失败: {str(e)}")
            return error_response(f"查询产品退货数量失败: {str(e)}", code=500)


@api.route('/reasons')
class ReturnReasons(Resource):
    @api.doc('get_return_reasons')
    def get(self):
        """获取退货原因列表"""
        try:
            reasons = [
                '质量问题',
                '规格不符',
                '数量错误',
                '包装破损',
                '延期交货',
                '客户取消',
                '其他原因'
            ]

            return success_response({
                'reasons': reasons,
                'descriptions': {
                    '质量问题': '产品质量不符合要求',
                    '规格不符': '产品规格与订单要求不符',
                    '数量错误': '发货数量与订单不符',
                    '包装破损': '产品包装在运输过程中损坏',
                    '延期交货': '交货时间超出约定期限',
                    '客户取消': '客户主动取消订单',
                    '其他原因': '其他未列出的原因'
                }
            }, "获取退货原因列表成功")

        except Exception as e:
            current_app.logger.error(f"获取退货原因列表失败: {str(e)}")
            return error_response(f"获取退货原因列表失败: {str(e)}", code=500)
