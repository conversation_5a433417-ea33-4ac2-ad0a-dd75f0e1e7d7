"""
Marshmallow Schema 到 Flask-RESTx 模型的自动转换工具
优化版本：使用缓存避免重复转换
"""
from flask_restx import fields as restx_fields
from marshmallow import fields as ma_fields
from typing import Dict, Any, Type
from marshmallow import Schema

# 全局缓存，避免重复转换
_model_cache = {}

def clear_model_cache():
    """清空模型缓存（用于测试或重新加载）"""
    global _model_cache
    _model_cache.clear()


def schema_to_restx_model(api, schema_class: Type[Schema], model_name: str, exclude_fields: list = None) -> Dict[str, Any]:
    """
    从Marshmallow Schema自动生成Flask-RESTx模型（带缓存优化）

    Args:
        api: Flask-RESTx Api实例
        schema_class: Marshmallow Schema类
        model_name: 生成的模型名称
        exclude_fields: 要排除的字段列表

    Returns:
        Flask-RESTx模型对象
    """
    if exclude_fields is None:
        exclude_fields = []

    # 创建缓存键
    cache_key = f"{schema_class.__name__}_{model_name}_{hash(tuple(sorted(exclude_fields)))}"

    # 检查缓存
    if cache_key in _model_cache:
        # print(f"缓存命中: {cache_key}")  # 调试信息
        return _model_cache[cache_key]

    fields_dict = {}
    schema = schema_class()

    for field_name, field_obj in schema.fields.items():
        # 跳过排除的字段
        if field_name in exclude_fields:
            continue

        # 跳过dump_only字段（通常是ID、时间戳等）
        if field_obj.dump_only:
            continue

        restx_field = _convert_marshmallow_field_to_restx(field_obj, field_name)
        if restx_field:
            fields_dict[field_name] = restx_field

    # 创建模型并缓存
    model = api.model(model_name, fields_dict)
    _model_cache[cache_key] = model
    # print(f"缓存新建: {cache_key}")  # 调试信息

    return model


def _convert_marshmallow_field_to_restx(ma_field, field_name: str):
    """
    将单个Marshmallow字段转换为Flask-RESTx字段
    """
    # 获取字段的基本属性
    required = ma_field.required
    allow_none = getattr(ma_field, 'allow_none', False)
    description = ma_field.metadata.get('description', f'{field_name}字段')

    # 调试输出（可选）
    # print(f"转换字段 {field_name}: required={required}, allow_none={allow_none}, type={type(ma_field).__name__}")

    # 根据Marshmallow字段类型转换
    if isinstance(ma_field, ma_fields.Str):
        field = restx_fields.String(
            required=required,
            description=description
        )
        if allow_none:
            field.allow_none = True
        return field

    elif isinstance(ma_field, ma_fields.Int):
        field = restx_fields.Integer(
            required=required,
            description=description
        )
        if allow_none:
            field.allow_none = True
        return field
    
    elif isinstance(ma_field, ma_fields.Float):
        field = restx_fields.Float(
            required=required,
            description=description
        )
        if allow_none:
            field.allow_none = True
        return field

    elif isinstance(ma_field, ma_fields.Decimal):
        field = restx_fields.Float(  # RESTx没有Decimal，用Float代替
            required=required,
            description=description
        )
        if allow_none:
            field.allow_none = True
        return field

    elif isinstance(ma_field, ma_fields.Bool):
        field = restx_fields.Boolean(
            required=required,
            description=description
        )
        if allow_none:
            field.allow_none = True
        return field

    elif isinstance(ma_field, ma_fields.DateTime):
        field = restx_fields.DateTime(
            required=required,
            description=description
        )
        if allow_none:
            field.allow_none = True
        return field

    elif isinstance(ma_field, ma_fields.Date):
        field = restx_fields.Date(
            required=required,
            description=description
        )
        if allow_none:
            field.allow_none = True
        return field
    
    elif isinstance(ma_field, ma_fields.List):
        # 处理列表字段
        if hasattr(ma_field, 'inner') and ma_field.inner:
            inner_field = _convert_marshmallow_field_to_restx(ma_field.inner, f"{field_name}_item")
            if inner_field:
                return restx_fields.List(
                    inner_field,
                    required=required,
                    allow_none=allow_none,
                    description=description
                )
        return restx_fields.List(
            restx_fields.Raw,
            required=required,
            allow_none=allow_none,
            description=description
        )
    
    elif isinstance(ma_field, ma_fields.Nested):
        # 嵌套字段暂时用Raw处理，避免循环依赖
        return restx_fields.Raw(
            required=required,
            allow_none=allow_none,
            description=f"{description}（嵌套对象）"
        )
    
    elif isinstance(ma_field, ma_fields.Method):
        # Method字段通常是计算字段，跳过
        return None
    
    else:
        # 未知字段类型，使用Raw
        return restx_fields.Raw(
            required=required,
            allow_none=allow_none,
            description=f"{description}（{type(ma_field).__name__}）"
        )


def create_input_model(api, schema_class: Type[Schema], model_name: str) -> Dict[str, Any]:
    """
    创建用于输入的模型（排除dump_only字段）
    """
    exclude_fields = [
        'id', 'created_at', 'updated_at',  # 通用排除字段
        'category', 'brand', 'category_name', 'brand_name',  # 关联对象
        'price_range', 'main_image'  # 计算字段
    ]

    return schema_to_restx_model(api, schema_class, model_name, exclude_fields)


def create_output_model(api, schema_class: Type[Schema], model_name: str) -> Dict[str, Any]:
    """
    创建用于输出的模型（包含所有字段）
    """
    # 检查缓存
    cache_key = f"{schema_class.__name__}_{model_name}_output"
    if cache_key in _model_cache:
        return _model_cache[cache_key]

    fields_dict = {}
    schema = schema_class()

    for field_name, field_obj in schema.fields.items():
        restx_field = _convert_marshmallow_field_to_restx(field_obj, field_name)
        if restx_field:
            fields_dict[field_name] = restx_field

    # 创建模型并缓存
    model = api.model(model_name, fields_dict)
    _model_cache[cache_key] = model

    return model
