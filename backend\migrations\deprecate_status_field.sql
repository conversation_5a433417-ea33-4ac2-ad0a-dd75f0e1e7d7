-- 订单状态系统重构：完全废弃status字段迁移脚本（SQL版本）
-- 执行时间：2025-01-25
-- 说明：将现有的单一status字段完全迁移到双状态系统（order_status + payment_status）

-- ============================================================================
-- 重要提醒：执行前请备份数据库！
-- 备份命令：cp project.db project.db.backup_$(date +%Y%m%d_%H%M%S)
-- ============================================================================

-- 1. 检查当前数据状态
SELECT '=== 迁移前数据统计 ===' as info;
SELECT 
    '总订单数' as metric,
    COUNT(*) as count
FROM orders;

SELECT 
    '有status数据的订单' as metric,
    COUNT(*) as count
FROM orders 
WHERE status IS NOT NULL AND status != '';

SELECT 
    '需要迁移的订单' as metric,
    COUNT(*) as count
FROM orders 
WHERE (order_status IS NULL OR order_status = '' OR order_status = '待确认')
AND status IS NOT NULL AND status != '';

-- 2. 显示当前status分布
SELECT '=== 当前status字段分布 ===' as info;
SELECT 
    status,
    COUNT(*) as count
FROM orders 
WHERE status IS NOT NULL AND status != ''
GROUP BY status
ORDER BY count DESC;

-- 3. 数据迁移：将status数据迁移到order_status
SELECT '=== 开始数据迁移 ===' as info;

-- 3.1 标准物流状态迁移
UPDATE orders 
SET order_status = status
WHERE status IN ('待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消')
AND (order_status IS NULL OR order_status = '' OR order_status = '待确认');

-- 3.2 发货中状态迁移为部分发货
UPDATE orders 
SET order_status = '部分发货'
WHERE status = '发货中'
AND (order_status IS NULL OR order_status = '' OR order_status = '待确认');

-- 3.3 对账相关状态迁移为全部发货
UPDATE orders 
SET order_status = '全部发货'
WHERE status IN ('待对账', '部分对账', '全部对账')
AND (order_status IS NULL OR order_status = '' OR order_status = '待确认');

-- 3.4 收款相关状态迁移为已完成
UPDATE orders 
SET order_status = '已完成'
WHERE status IN ('待收款', '部分收款', '已结清')
AND (order_status IS NULL OR order_status = '' OR order_status = '待确认');

-- 3.5 处理其他未知状态（设置为待确认）
UPDATE orders 
SET order_status = '待确认'
WHERE (order_status IS NULL OR order_status = '')
AND status IS NOT NULL AND status != '';

-- 4. 财务状态计算：根据金额自动计算payment_status
SELECT '=== 计算财务状态 ===' as info;

-- 4.1 未收款（已付金额为0或NULL）
UPDATE orders 
SET payment_status = '未收款'
WHERE (paid_amount IS NULL OR paid_amount = 0)
AND (payment_status IS NULL OR payment_status = '');

-- 4.2 已收款（已付金额 >= 总金额）
UPDATE orders 
SET payment_status = '已收款'
WHERE paid_amount >= total_amount 
AND total_amount > 0
AND (payment_status IS NULL OR payment_status = '');

-- 4.3 部分收款（0 < 已付金额 < 总金额）
UPDATE orders 
SET payment_status = '部分收款'
WHERE paid_amount > 0 
AND paid_amount < total_amount
AND total_amount > 0
AND (payment_status IS NULL OR payment_status = '');

-- 4.4 处理总金额为0的特殊情况
UPDATE orders 
SET payment_status = '已收款'
WHERE total_amount = 0
AND (payment_status IS NULL OR payment_status = '');

-- 5. 验证迁移结果
SELECT '=== 迁移后数据验证 ===' as info;

-- 5.1 检查是否有空的order_status
SELECT 
    '空order_status数量' as metric,
    COUNT(*) as count
FROM orders 
WHERE order_status IS NULL OR order_status = '';

-- 5.2 检查是否有空的payment_status
SELECT 
    '空payment_status数量' as metric,
    COUNT(*) as count
FROM orders 
WHERE payment_status IS NULL OR payment_status = '';

-- 5.3 显示迁移后的状态分布
SELECT '=== 迁移后状态分布 ===' as info;
SELECT 
    order_status,
    payment_status,
    COUNT(*) as count
FROM orders 
GROUP BY order_status, payment_status
ORDER BY order_status, payment_status;

-- 6. 添加数据库约束（确保数据完整性）
SELECT '=== 添加数据库约束 ===' as info;

-- 6.1 添加物流状态约束
-- 注意：SQLite的约束添加可能有限制，如果失败请忽略
-- ALTER TABLE orders ADD CONSTRAINT chk_order_status 
-- CHECK (order_status IN ('待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消'));

-- 6.2 添加财务状态约束
-- ALTER TABLE orders ADD CONSTRAINT chk_payment_status 
-- CHECK (payment_status IN ('未收款', '部分收款', '已收款'));

-- 7. 创建索引以提高查询性能
SELECT '=== 创建索引 ===' as info;

CREATE INDEX IF NOT EXISTS idx_orders_order_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_combined_status ON orders(order_status, payment_status);

-- 8. 最终验证
SELECT '=== 最终验证结果 ===' as info;

-- 8.1 总体统计
SELECT 
    '总订单数' as metric,
    COUNT(*) as count
FROM orders;

-- 8.2 状态完整性检查
SELECT 
    '有效order_status的订单' as metric,
    COUNT(*) as count
FROM orders 
WHERE order_status IN ('待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消');

SELECT 
    '有效payment_status的订单' as metric,
    COUNT(*) as count
FROM orders 
WHERE payment_status IN ('未收款', '部分收款', '已收款');

-- 8.3 数据一致性检查
SELECT 
    '数据一致性检查' as info,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过'
        ELSE '❌ 失败'
    END as result
FROM orders 
WHERE order_status IS NULL 
   OR order_status = ''
   OR payment_status IS NULL 
   OR payment_status = ''
   OR order_status NOT IN ('待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消')
   OR payment_status NOT IN ('未收款', '部分收款', '已收款');

-- ============================================================================
-- 迁移完成提醒
-- ============================================================================
SELECT '=== 迁移完成 ===' as info;
SELECT '下一步操作：' as next_steps;
SELECT '1. 检查迁移结果是否正确' as step1;
SELECT '2. 修改后端代码移除status字段依赖' as step2;
SELECT '3. 修改前端代码使用新的双状态字段' as step3;
SELECT '4. 进行全面测试' as step4;
SELECT '5. 可选：删除或重命名status字段' as step5;

-- ============================================================================
-- 可选：重命名status字段（作为备份保留）
-- 注意：SQLite不支持直接删除列，只能重命名
-- ============================================================================
-- ALTER TABLE orders RENAME COLUMN status TO status_deprecated;

-- ============================================================================
-- 回滚脚本（如果需要回滚）
-- ============================================================================
/*
-- 回滚迁移（将order_status数据复制回status字段）
UPDATE orders SET status = order_status WHERE status_deprecated IS NOT NULL;

-- 恢复原字段名
ALTER TABLE orders RENAME COLUMN status_deprecated TO status;

-- 清空双状态字段（如果需要）
UPDATE orders SET order_status = NULL, payment_status = NULL;
*/
