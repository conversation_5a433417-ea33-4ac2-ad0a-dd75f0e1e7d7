# EMB项目开发指南

## 📋 开发环境配置

### 环境要求
- **Python**: 3.8+
- **Node.js**: 16+
- **数据库**: PostgreSQL 12+ / MySQL 8.0+ / SQLite 3.x
- **Git**: 2.0+
- **pip**: 20.0+

### 快速开始

#### 1. 克隆项目
```bash
git clone https://github.com/kkkdkk/EMB-new.git
cd EMB-new
```

#### 2. 后端环境配置
```bash
cd backend

# 创建虚拟环境
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/macOS
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等

# 初始化数据库
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all(); print('数据库初始化完成')"

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等

# 启动后端服务
python run.py
```

#### 3. 环境变量详细配置
```bash
# .env文件完整配置示例 (基于.env.example)

# Flask应用配置
FLASK_APP=run.py
FLASK_ENV=development
FLASK_DEBUG=True

# 服务器配置
HOST=0.0.0.0
PORT=5001

# 数据库配置
DATABASE_URL=sqlite:///project.db

# 安全配置
SECRET_KEY=your_super_secret_key_change_this_in_production

# API配置
API_TITLE=EMB System API
API_VERSION=1.0
API_DESCRIPTION=工程物资报价及订单管理系统API文档

# 文件上传配置
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads
ALLOWED_EXTENSIONS=txt,pdf,png,jpg,jpeg,gif,xlsx,xls,csv

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 跨域配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# 备份配置
BACKUP_FOLDER=backups
AUTO_BACKUP_ENABLED=True
BACKUP_RETENTION_DAYS=30
```

#### 4. 前端环境配置
```bash
cd frontend

# 安装依赖
npm install

# 启动前端服务
npm run dev
```

#### 5. 访问应用
- **前端应用**: http://localhost:3001
- **后端API**: http://localhost:5001
- **API文档**: http://localhost:5001/docs/
- **健康检查**: http://localhost:5001/test/health

## 🏗️ 代码规范

### Python后端规范

#### 1. 代码风格
- 遵循 **PEP 8** 规范
- 使用 **类型注解** 提高代码可读性
- 编写详细的 **文档字符串**
- 统一的 **错误处理** 机制

```python
# ✅ 好的示例
def create_customer(customer_data: dict) -> dict:
    """创建新客户
    
    Args:
        customer_data: 客户数据字典
        
    Returns:
        dict: 创建的客户信息
        
    Raises:
        ValidationError: 数据验证失败
        DatabaseError: 数据库操作失败
    """
    try:
        # 数据验证
        schema = CustomerSchema()
        validated_data = schema.load(customer_data)
        
        # 创建客户
        customer = Customer(**validated_data)
        db.session.add(customer)
        db.session.commit()
        
        return customer.to_dict()
    except Exception as e:
        db.session.rollback()
        raise
```

#### 2. 项目结构规范
```
backend/app/
├── api/v1/           # API路由模块
│   ├── customers.py  # 客户管理API
│   ├── products.py   # 产品管理API
│   └── ...
├── models/           # 数据模型
│   ├── customer.py   # 客户模型
│   ├── product.py    # 产品模型
│   └── ...
├── schemas/          # 数据验证模式
│   ├── customer.py   # 客户验证模式
│   ├── product.py    # 产品验证模式
│   └── ...
├── utils/            # 工具函数
└── middleware/       # 中间件
```

#### 3. API开发规范
- **RESTful设计**: 遵循REST API设计原则
- **统一响应格式**: 标准化JSON响应结构
- **错误处理**: 统一错误码和错误信息
- **数据验证**: 使用Marshmallow进行数据验证

```python
# 统一响应格式
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": "2024-01-01T00:00:00Z"
}

# 错误响应格式
{
    "code": 400,
    "message": "请求参数错误",
    "errors": {
        "name": ["客户名称不能为空"]
    },
    "timestamp": "2024-01-01T00:00:00Z"
}
```

### TypeScript前端规范

#### 1. 代码风格
- 使用 **TypeScript** 进行类型安全开发
- 遵循 **ESLint** 和 **Prettier** 配置
- 使用 **Vue 3 Composition API**
- 组件化开发思想

```typescript
// ✅ 好的示例
interface Customer {
  id: number
  name: string
  contact: string
  phone: string
  email?: string
  status: 'active' | 'inactive'
}

const useCustomers = () => {
  const customers = ref<Customer[]>([])
  const loading = ref(false)
  
  const fetchCustomers = async (): Promise<void> => {
    loading.value = true
    try {
      const response = await customerApi.getList()
      customers.value = response.data
    } catch (error) {
      ElMessage.error('获取客户列表失败')
    } finally {
      loading.value = false
    }
  }
  
  return {
    customers: readonly(customers),
    loading: readonly(loading),
    fetchCustomers
  }
}
```

#### 2. 组件开发规范
```vue
<script setup lang="ts">
// 组件props定义
interface Props {
  customer?: Customer
  readonly?: boolean
}

// 使用defineProps和defineEmits
const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

const emit = defineEmits<{
  save: [customer: Customer]
  cancel: []
}>()

// 响应式数据
const form = reactive<CustomerForm>({
  name: '',
  contact: '',
  phone: '',
  email: ''
})

// 计算属性
const isValid = computed(() => {
  return form.name && form.contact && form.phone
})

// 方法
const handleSave = () => {
  if (isValid.value) {
    emit('save', form as Customer)
  }
}
</script>

<template>
  <el-form :model="form" label-width="100px">
    <el-form-item label="客户名称" required>
      <el-input v-model="form.name" :readonly="readonly" />
    </el-form-item>
    <!-- 其他表单项 -->
  </el-form>
</template>
```

## 🔧 开发流程

### API开发流程

1. **定义数据模型** (`models/`)
   - 创建SQLAlchemy模型类
   - 定义表结构和关系
   - 添加必要的索引和约束

2. **创建数据验证** (`schemas/`)
   - 使用Marshmallow创建验证模式
   - 定义输入输出数据格式
   - 添加自定义验证规则

3. **实现API端点** (`api/v1/`)
   - 创建Flask-RESTX资源类
   - 实现CRUD操作
   - 添加API文档注释

4. **编写测试用例** (`tests/`)
   - 单元测试：测试API端点
   - 集成测试：测试业务流程
   - 数据库测试：测试模型关系

5. **更新API文档**
   - Swagger文档自动生成
   - 添加详细的接口说明
   - 提供请求响应示例

### 前端开发流程

1. **创建API接口** (`src/api/`)
   - 定义API调用函数
   - 统一错误处理
   - 类型定义

2. **创建页面组件** (`src/views/`)
   - 实现页面布局
   - 集成业务逻辑
   - 添加交互功能

3. **创建公共组件** (`src/components/`)
   - 可复用的UI组件
   - 业务组件封装
   - 组件文档

4. **状态管理** (`src/stores/`)
   - 使用Pinia管理状态
   - 模块化状态设计
   - 持久化配置

5. **路由配置** (`src/router/`)
   - 页面路由定义
   - 路由守卫
   - 懒加载配置

## 🧪 测试策略

### 后端测试

#### 1. 单元测试
```python
# tests/test_customers.py
def test_create_customer(client, db):
    """测试创建客户"""
    customer_data = {
        'name': '测试公司',
        'contact': '张三',
        'phone': '13800138000'
    }
    
    response = client.post('/api/v1/customers', json=customer_data)
    
    assert response.status_code == 201
    assert response.json['data']['name'] == '测试公司'
```

#### 2. 集成测试
```python
def test_customer_order_flow(client, db):
    """测试客户-订单业务流程"""
    # 1. 创建客户
    customer = create_test_customer(client)
    
    # 2. 创建产品
    product = create_test_product(client)
    
    # 3. 创建报价单
    quotation = create_test_quotation(client, customer['id'], product['id'])
    
    # 4. 创建订单
    order = create_test_order(client, quotation['id'])
    
    # 验证业务流程
    assert order['customer_id'] == customer['id']
    assert order['quotation_id'] == quotation['id']
```

#### 3. 运行测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试文件
python -m pytest tests/test_customers.py -v

# 运行覆盖率测试
python -m pytest --cov=app tests/ --cov-report=html

# 生成测试报告
python -m pytest --html=reports/report.html
```

### 前端测试

#### 1. 组件测试
```typescript
// tests/components/CustomerForm.spec.ts
import { mount } from '@vue/test-utils'
import CustomerForm from '@/components/CustomerForm.vue'

describe('CustomerForm', () => {
  it('should emit save event when form is valid', async () => {
    const wrapper = mount(CustomerForm)
    
    await wrapper.find('input[name="name"]').setValue('测试公司')
    await wrapper.find('input[name="contact"]').setValue('张三')
    await wrapper.find('input[name="phone"]').setValue('13800138000')
    
    await wrapper.find('form').trigger('submit')
    
    expect(wrapper.emitted('save')).toBeTruthy()
  })
})
```

## 🗄️ 数据库管理

### 数据库迁移

#### 1. 使用Flask-Migrate（推荐）
```bash
# 初始化迁移
flask db init

# 生成迁移文件
flask db migrate -m "添加客户表"

# 应用迁移
flask db upgrade

# 回滚迁移
flask db downgrade
```

#### 2. 直接创建表
```bash
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

### 数据库配置

#### 1. 开发环境（SQLite）
```bash
DATABASE_URL=sqlite:///project.db
```

#### 2. 生产环境（PostgreSQL）
```bash
DATABASE_URL=postgresql://username:password@localhost:5432/emb_system
```

#### 3. 连接池配置
```python
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_size': 10,
    'pool_timeout': 20,
    'pool_recycle': 3600,
    'max_overflow': 20
}
```

## 🚀 部署指南

### 开发环境部署
```bash
# 后端
cd backend
python run.py

# 前端
cd frontend
npm run dev
```

### 生产环境部署

#### 1. 后端部署
```bash
# 安装生产依赖
pip install -r requirements-prod.txt

# 使用Gunicorn部署
gunicorn --config gunicorn.conf.py wsgi:application
```

#### 2. 前端构建
```bash
# 构建生产版本
npm run build

# 部署到Web服务器
cp -r dist/* /var/www/html/
```

#### 3. Docker部署
```bash
# 构建镜像
docker build -t emb-system .

# 运行容器
docker run -d \
  --name emb-api \
  -p 5001:5001 \
  -e DATABASE_URL=****************************** \
  emb-system

# 使用Docker Compose
docker-compose up -d
```

## 🔍 调试技巧

### 后端调试

#### 1. 启用调试模式
```bash
export FLASK_ENV=development
export FLASK_DEBUG=True
python run.py
```

#### 2. 数据库调试
```python
# 启用SQL查询日志
app.config['SQLALCHEMY_ECHO'] = True
```

#### 3. 日志调试
```bash
export LOG_LEVEL=DEBUG
tail -f logs/app.log
```

### 前端调试

#### 1. 开发者工具
- 使用浏览器开发者工具
- Vue DevTools扩展
- 网络请求监控

#### 2. 调试配置
```typescript
// vite.config.ts
export default defineConfig({
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  }
})
```

## 📝 提交规范

### Git提交格式
```
类型(范围): 简短描述

详细描述（可选）

相关Issue: #123
```

### 提交类型
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 示例
```bash
git commit -m "feat(customers): 添加客户批量导入功能

- 支持Excel文件导入
- 添加数据验证和错误提示
- 更新API文档

相关Issue: #123"
```

---

**开发指南版本**: v1.0  
**最后更新**: 2025年1月  
**适用项目**: EMB工程物资报价及订单管理系统
