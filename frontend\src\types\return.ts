import type { Order, OrderProduct } from './order'

/**
 * 退货单项目接口
 */
export interface ReturnOrderItem {
  id?: number
  return_order_id?: number
  order_product_id: number
  product_specification_id: number
  quantity: number
  reason?: string
  notes?: string
  
  // 快照字段 (后端 dump_only)
  product_name?: string
  product_model?: string
  product_unit?: string
  specification_description?: string
  
  // 关联信息 (后端 dump_only)
  order_product?: OrderProduct
}

/**
 * 退货单状态类型
 */
export type ReturnOrderStatus = '待确认' | '退货中' | '已签收' | '已拒绝' | '已取消'

/**
 * 退货单接口
 */
export interface ReturnOrder {
  id?: number
  order_id: number
  return_number?: string
  return_date: string
  status?: ReturnOrderStatus
  reason: string
  notes?: string

  // 关联信息 (后端 dump_only)
  order?: Order
  items: ReturnOrderItem[]

  // 显示字段
  customer_name?: string
  project_name?: string
  delivery_number?: string

  // 金额字段
  total_amount?: number | string

  // 时间戳
  created_at?: string
  updated_at?: string
}

/**
 * 创建退货单时的请求数据
 */
export interface CreateReturnOrderData {
  order_id: number
  return_date: string
  reason: string
  notes?: string
  items: Array<{
    order_product_id: number
    product_specification_id: number
    quantity: number
    reason?: string
    notes?: string
  }>
}

/**
 * 更新退货单时的请求数据
 */
export interface UpdateReturnOrderData extends Partial<Omit<CreateReturnOrderData, 'order_id'>> {
  status?: ReturnOrderStatus
}

/**
 * 退货单列表查询参数
 */
export interface ListReturnOrderParams {
  page?: number
  per_page?: number
  order_id?: number
  return_number?: string
  status?: ReturnOrderStatus
  start_date?: string
  end_date?: string
  customer_name?: string
  reason?: string
  [key: string]: any
}

/**
 * 可退货发货单接口
 */
export interface ReturnableDeliveryNote {
  id: number
  delivery_number: string
  order_id: number
  customer_name: string
  project_name: string
  delivery_date: string
  status: string
  items: Array<{
    id: number
    order_product_id: number
    product_specification_id: number
    product_name: string
    product_model: string
    specification_description: string
    delivered_quantity: number
    returned_quantity: number
    returnable_quantity: number
  }>
}
