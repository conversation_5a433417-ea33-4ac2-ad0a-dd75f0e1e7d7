"""
退货单相关模型
包括退货单、退货单项目
"""
from app import db
from datetime import datetime
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class ReturnOrder(BaseModel):
    """退货单模型"""
    __tablename__ = 'return_orders'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    return_number = db.Column(db.String(32), unique=True, nullable=False, comment='退货单号')
    return_date = db.Column(db.Date, nullable=False, comment='退货日期')
    status = db.Column(db.String(20), nullable=False, default='待确认', comment='状态：待确认、退货中、已签收、已拒绝、已取消')
    reason = db.Column(db.String(500), comment='退货原因')
    notes = db.Column(db.String(500), comment='备注')
    total_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00, comment='退货单总金额')

    # 结清状态字段
    settlement_status = db.Column(db.String(20), nullable=False, default='未结清', comment='结清状态 (未结清, 已结清)')
    settlement_date = db.Column(db.Date, nullable=True, comment='结清日期')

    # 关联关系
    order = relationship('Order', backref='return_orders')
    items = relationship('ReturnOrderItem', backref='return_order', cascade='all, delete-orphan')
    statement_return_orders = relationship('StatementReturnOrder', back_populates='return_order', cascade='all, delete-orphan', overlaps="statements")
    statements = relationship('Statement', secondary='statement_return_orders', back_populates='return_orders', overlaps="statement_return_orders,statement,return_order")

    def __repr__(self):
        return f'<ReturnOrder {self.return_number}>'

    def calculate_total_amount(self) -> float:
        """计算退货单总金额"""
        total = 0.0
        if self.items:
            for item in self.items:
                if item.order_product:
                    # 计算含税金额：单价 × (1 - 折扣率) × (1 + 税率) × 数量
                    unit_price = float(item.order_product.unit_price or 0)
                    discount_rate = float(item.order_product.discount or 0) / 100
                    tax_rate = float(item.order_product.tax_rate or 0) / 100
                    quantity = float(item.quantity or 0)

                    item_total = unit_price * (1 - discount_rate) * (1 + tax_rate) * quantity
                    total += item_total
        return total

    def update_total_amount(self):
        """更新退货单总金额"""
        self.total_amount = self.calculate_total_amount()

    def settle(self, settlement_date=None):
        """标记退货单为已结清"""
        if settlement_date is None:
            settlement_date = datetime.now().date()

        self.settlement_status = '已结清'
        self.settlement_date = settlement_date
        self.updated_at = datetime.now()

        # 更新关联订单的收款状态
        if self.order:
            self.order.auto_update_payment_status()

    def unsettle(self):
        """取消退货单结清状态"""
        self.settlement_status = '未结清'
        self.settlement_date = None
        self.updated_at = datetime.now()

        # 更新关联订单的收款状态
        if self.order:
            self.order.auto_update_payment_status()

    def update_status(self, new_status):
        """更新退货单状态，如果是完成状态则更新应收账款"""
        old_status = self.status
        self.status = new_status
        self.updated_at = datetime.now()

        # 注意：新逻辑下，应收账款不再在退货单完成时创建，而是在对账单确认时创建

    # 注意：新逻辑下，退货单不再直接管理应收账款，应收账款由对账单管理


class ReturnOrderItem(BaseModel):
    """退货单项目模型"""
    __tablename__ = 'return_order_items'

    id = db.Column(db.Integer, primary_key=True)
    return_order_id = db.Column(db.Integer, db.ForeignKey('return_orders.id'), nullable=False, comment='退货单ID')
    order_product_id = db.Column(db.Integer, db.ForeignKey('order_products.id'), nullable=False, comment='订单产品ID')
    product_specification_id = db.Column(db.Integer, db.ForeignKey('product_specifications.id'), nullable=False, comment='产品规格ID')
    quantity = db.Column(db.Float, nullable=False, comment='退货数量')
    reason = db.Column(db.String(500), comment='退货原因')
    notes = db.Column(db.String(500), comment='备注')

    # 冗余字段，用于快照
    product_name = db.Column(db.String(100), comment='产品名称(快照)')
    product_model = db.Column(db.String(100), comment='产品型号(快照)')
    product_unit = db.Column(db.String(20), comment='产品单位(快照)')
    specification_description = db.Column(db.String(200), comment='产品规格描述(快照)')

    # 关联关系
    order_product = relationship('OrderProduct', backref='return_items')
    product_specification = relationship('ProductSpecification')

    def __repr__(self):
        return f'<ReturnOrderItem {self.id}>'
