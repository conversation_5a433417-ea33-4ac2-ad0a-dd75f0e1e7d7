<template>
  <div class="brand-list">
    <el-card class="header-card mb-20">
      <div class="card-header">
        <div class="left">
          <h2 class="page-title">品牌管理</h2>
          <el-tag type="info">管理产品品牌信息</el-tag>
        </div>
        <div class="right">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon> 新增品牌
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 搜索栏 -->
    <el-card class="mb-20">
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form" @submit.prevent="fetchData">
          <el-form-item label="品牌名称">
            <el-input 
              v-model="searchForm.name" 
              placeholder="输入品牌名称" 
              clearable 
              @keyup.enter="fetchData"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="fetchData">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 品牌列表 -->
    <el-card>
      <div class="table-container" v-loading="loading">
        <el-table 
          :data="brandList" 
          border 
          stripe 
          style="width: 100%"
          @sort-change="handleSortChange"
          :default-sort="{ prop: 'sort_order', order: 'ascending' }"
        >
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="name" label="品牌名称" min-width="150" sortable="custom" />
          <el-table-column prop="logo_url" label="品牌标志" width="120">
            <template #default="scope">
              <el-image 
                v-if="scope.row.logo_url" 
                :src="scope.row.logo_url" 
                fit="contain"
                style="width: 80px; height: 40px;"
                :preview-src-list="[scope.row.logo_url]"
                preview-teleported
                @error="handleLogoError"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <span>加载失败</span>
                  </div>
                </template>
              </el-image>
              <div v-else class="default-logo">
                <el-icon><Picture /></el-icon>
                <span>暂无图标</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="品牌描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="website" label="官网" min-width="150">
            <template #default="scope">
              <el-link v-if="scope.row.website" type="primary" :href="scope.row.website" target="_blank">
                {{ scope.row.website }}
              </el-link>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="sort_order" label="排序" width="100" sortable="custom" />

          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="200">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>

              <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 品牌编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑品牌' : '新增品牌'"
      width="600px"
      destroy-on-close
      @closed="handleDialogClosed"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="品牌名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入品牌名称" />
        </el-form-item>
        
        <el-form-item label="品牌描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入品牌描述"
          />
        </el-form-item>
        
        <el-form-item label="官网地址" prop="website">
          <el-input v-model="form.website" placeholder="请输入官网地址">
            <template #prepend>https://</template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="Logo地址" prop="logo_url">
          <el-input v-model="form.logo_url" placeholder="请输入Logo图片地址" />
          <div class="form-tip">请输入Logo图片的URL地址</div>
        </el-form-item>
        
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="form.sort_order" :min="0" :max="999" style="width: 100%" />
          <div class="form-tip">数字越小排序越靠前</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Picture } from '@element-plus/icons-vue'
import { brandManagementApi } from '@/api/system'

const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref(null)

// 搜索表单
const searchForm = reactive({
  name: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 排序信息
const sortParams = reactive({
  prop: 'sort_order',
  order: 'ascending'
})

// 品牌列表
const brandList = ref([])

// 表单数据
const form = reactive({
  id: null,
  name: '',
  description: '',
  website: '',
  logo_url: '',
  sort_order: 0
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入品牌名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '不能超过500个字符', trigger: 'blur' }
  ],
  website: [
    { pattern: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/, message: '请输入有效的网址', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', message: '排序必须为数字', trigger: 'blur' }
  ]
}

// API排序参数
const apiSortOrder = computed(() => {
  if (!sortParams.prop) return null
  return sortParams.order === 'descending' ? `-${sortParams.prop}` : sortParams.prop
})

// 获取品牌列表
const fetchData = async (options: { silent?: boolean } = {}) => {
  loading.value = true
  try {
    const params: any = {
      page: pagination.page,
      per_page: pagination.pageSize,
      name: searchForm.name || undefined,
      sort: apiSortOrder.value || undefined
    }
    
    // 清理undefined参数
    Object.keys(params).forEach(key => params[key] === undefined && delete params[key])

    const response = await brandManagementApi.getList(params) as any
    
    // 处理响应数据
    if (Array.isArray(response)) {
      brandList.value = response
      pagination.total = response.length
    } else if (response && typeof response === 'object') {
      const data = response.data
      if (Array.isArray(data)) {
        brandList.value = data
      } else if (data && typeof data === 'object' && Object.keys(data).length === 0) {
        brandList.value = []
      } else {
        brandList.value = []
      }
      pagination.total = response.pagination?.total || 0
    } else {
      brandList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取品牌列表失败:', error)
    // 只在非静默模式下显示错误消息
    if (!options.silent) {
      ElMessage.error('获取品牌列表失败')
    }

    // 使用模拟数据
    brandList.value = [
      {
        id: 1,
        name: '华为',
        description: '华为技术有限公司',
        website: 'https://www.huawei.com',
        logo_url: 'https://placehold.co/80x40/4A6FE3/FFFFFF.svg?text=华为',
        sort_order: 1,
        created_at: '2025-01-01 10:00:00'
      },
      {
        id: 2,
        name: '小米',
        description: '小米科技有限责任公司',
        website: 'https://www.mi.com',
        logo_url: 'https://placehold.co/80x40/FF6900/FFFFFF.svg?text=小米',
        sort_order: 2,
        created_at: '2025-01-02 14:30:00'
      },
      {
        id: 3,
        name: '苹果',
        description: '苹果公司',
        website: 'https://www.apple.com',
        logo_url: 'https://placehold.co/80x40/000000/FFFFFF.svg?text=Apple',
        sort_order: 3,
        created_at: '2025-01-03 09:15:00'
      }
    ]
    pagination.total = 3
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.name = ''
  pagination.page = 1
  fetchData()
}

// 处理排序变化
const handleSortChange = ({ prop, order }: { prop: string, order: 'ascending' | 'descending' | null }) => {
  sortParams.prop = prop
  sortParams.order = order ?? 'ascending'
  fetchData()
}

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.page = 1
  fetchData()
}

// 处理当前页变化
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 新增品牌
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑品牌
const handleEdit = (row: any) => {
  isEdit.value = true
  Object.assign(form, {
    id: row.id,
    name: row.name,
    description: row.description || '',
    website: row.website || '',
    logo_url: row.logo_url || '',
    sort_order: row.sort_order || 0
  })
  dialogVisible.value = true
}



// 删除品牌
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除品牌 "${row.name}" 吗？此操作不可恢复！`,
      '删除品牌',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await brandManagementApi.delete(row.id)
    ElMessage.success('删除品牌成功')
    
    // 如果当前页只有一条数据且不是第一页，则回到上一页
    if (brandList.value.length === 1 && pagination.page > 1) {
      pagination.page -= 1
    }
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除品牌失败:', error)
      ElMessage.error('删除品牌失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    const valid = await formRef.value.validate()
    if (valid) {
      submitting.value = true
      try {
        // 处理URL格式
        let websiteUrl = form.website
        if (websiteUrl && !websiteUrl.match(/^https?:\/\//)) {
          websiteUrl = 'https://' + websiteUrl
        }

        let logoUrl = form.logo_url
        if (logoUrl && !logoUrl.match(/^https?:\/\//)) {
          logoUrl = 'https://' + logoUrl
        }

        const submitData: any = {
          name: form.name,
          sort_order: form.sort_order
        }

        // 只有非空值才添加到提交数据中
        if (form.description && form.description.trim()) {
          submitData.description = form.description.trim()
        }
        if (websiteUrl && websiteUrl.trim()) {
          submitData.website = websiteUrl.trim()
        }
        if (logoUrl && logoUrl.trim()) {
          submitData.logo_url = logoUrl.trim()
        }

        if (isEdit.value) {
          await brandManagementApi.update(form.id, submitData)
          ElMessage.success('更新品牌成功')
        } else {
          await brandManagementApi.create(submitData)
          ElMessage.success('创建品牌成功')
        }

        dialogVisible.value = false
        // 延迟刷新数据，避免与成功消息冲突
        setTimeout(() => {
          fetchData({ silent: true })
        }, 100)
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error(isEdit.value ? '更新品牌失败' : '创建品牌失败')
      } finally {
        submitting.value = false
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    name: '',
    description: '',
    website: '',
    logo_url: '',
    sort_order: 0
  })
}

// 对话框关闭处理
const handleDialogClosed = () => {
  resetForm()
  formRef.value?.clearValidate()
}

// 处理Logo加载错误
const handleLogoError = (e: Event) => {
  console.warn('Logo加载失败:', (e.target as HTMLImageElement).src)
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.brand-list {
  .header-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .search-bar {
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-container {
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
  
  .image-error, .default-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 40px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #909399;
    font-size: 12px;
    
    .el-icon {
      font-size: 16px;
      margin-bottom: 2px;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }
}
</style>
