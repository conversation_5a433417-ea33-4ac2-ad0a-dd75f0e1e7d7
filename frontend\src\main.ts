import './assets/main.css'

import { createApp } from 'vue'

import App from './App.vue'
import { setupRouter } from './router'
import { setupStore } from './stores'
import { initializeApp } from './utils/init'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

async function bootstrap() {
  const app = createApp(App)

  // 注册Element Plus
  app.use(ElementPlus)

  // 注册Element Plus图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }

  // 设置状态管理
  setupStore(app)

  // 设置路由
  setupRouter(app)

  // 挂载应用
  app.mount('#app')

  // 初始化应用状态
  await initializeApp()
}

// 启动应用
bootstrap().catch(console.error)
