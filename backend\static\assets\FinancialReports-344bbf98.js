import{r as e,L as a,d as t,e as l,f as r,M as s,o as n,c as u,i as o,h as d,N as i,g as c,a as m,k as p,l as _,j as f,F as v,m as y,t as g,V as b,U as h,$ as w,ad as D,X as C,Y as k,R as V,P as T}from"./index-3d4c440c.js";import{_ as R}from"./_plugin-vue_export-helper-1b428a4d.js";import{as as Y,el as S,ek as x,em as M,en as G,eo as I,ep as z,ej as N,eq as O}from"./install-028483b9.js";import{i as U}from"./install-da801927.js";const $={class:"financial-reports"},q={class:"card-header"},j={class:"left"},L={class:"right"},P={class:"report-selector"},A={class:"current-period"},F={class:"current-period"},E={class:"current-period"},W={class:"report-area"},X={key:0,class:"no-data"},B={key:1,class:"report-content"},H={class:"statistic-value"},J={class:"statistic-footer"},K={class:"statistic-value"},Q={class:"statistic-footer"},Z={class:"statistic-value"},ee={class:"statistic-footer"},ae={class:"statistic-value"},te={class:"statistic-footer"},le={class:"chart-container"},re={class:"chart-container"},se={class:"table-container"},ne={class:"pagination-container"},ue=R({__name:"FinancialReports",setup(R){Y([S,U,x,M,G,I,z,N]);const ue=e(!1),oe=e(!1),de=e(null),ie=e(null),ce=e("sales"),me=e(!1),pe=a({page:1,pageSize:10,total:0}),_e=a({reportType:"sales_summary",timeRange:"current_month",dateRange:[new Date((new Date).setDate(1)).toISOString().split("T")[0],(new Date).toISOString().split("T")[0]],productCategory:"",customerId:""}),fe=a({reportType:"income_expense_summary",timeRange:"current_month",dateRange:[new Date((new Date).setDate(1)).toISOString().split("T")[0],(new Date).toISOString().split("T")[0]]}),ve=a({reportType:"receivable_analysis",endDate:(new Date).toISOString().split("T")[0],customerId:""}),ye=e([]),ge=e([{id:"1",name:"电子元件"},{id:"2",name:"机械设备"},{id:"3",name:"建筑材料"},{id:"4",name:"电气设备"},{id:"5",name:"安防设备"}]),be=a({totalSales:258e4,salesGrowth:15.8,orderCount:356,orderCountGrowth:12.3,averageOrderValue:7247.19,aovGrowth:3.5,customerCount:48,customerCountGrowth:4.2}),he=e([]),we=t((()=>{switch(ce.value){case"sales":return De(_e.timeRange,_e.dateRange);case"income_expense":return De(fe.timeRange,fe.dateRange);case"receivable_payable":return ve.endDate;default:return""}})),De=(e,a)=>{const t=new Date,l=t.getFullYear(),r=t.getMonth()+1;switch(e){case"current_month":return`${l}年${r}月`;case"last_month":return 1===r?l-1+"年12月":`${l}年${r-1}月`;case"current_quarter":return`${l}年第${Math.ceil(r/3)}季度`;case"last_quarter":const e=Math.ceil(r/3)-1||4;return`${4===e?l-1:l}年第${e}季度`;case"current_year":return`${l}年`;case"last_year":return l-1+"年";case"custom":return Array.isArray(a)&&2===a.length?`${a[0]} 至 ${a[1]}`:"自定义时间段";default:return""}},Ce=e=>{me.value=!1},ke=()=>{me.value=!1},Ve=e=>{switch(ce.value){case"sales":"custom"!==e&&(_e.dateRange=Te(e));break;case"income_expense":"custom"!==e&&(fe.dateRange=Te(e))}},Te=e=>{const a=new Date,t=a.getFullYear(),l=a.getMonth();let r,s;switch(e){case"current_month":default:r=new Date(t,l,1),s=new Date;break;case"last_month":r=new Date(t,l-1,1),s=new Date(t,l,0);break;case"current_quarter":const e=3*Math.floor(l/3);r=new Date(t,e,1),s=new Date;break;case"last_quarter":const a=3*Math.floor(l/3)-3;r=new Date(t,a,1),s=new Date(t,a+3,0);break;case"current_year":r=new Date(t,0,1),s=new Date;break;case"last_year":r=new Date(t-1,0,1),s=new Date(t-1,11,31)}return[r.toISOString().split("T")[0],s.toISOString().split("T")[0]]},Re=async e=>{if(!(e.length<2)){oe.value=!0;try{await new Promise((e=>setTimeout(e,300))),ye.value=[{id:"1",name:"北京某某科技有限公司"},{id:"2",name:"上海某某贸易有限公司"},{id:"3",name:"广州某某建设工程有限公司"},{id:"4",name:"深圳某某电子科技有限公司"},{id:"5",name:"杭州某某软件有限公司"}].filter((a=>a.name.includes(e)))}catch(a){console.error("搜索客户失败:",a)}finally{oe.value=!1}}},Ye=()=>{_e.reportType="sales_summary",_e.timeRange="current_month",_e.dateRange=Te("current_month"),_e.productCategory="",_e.customerId=""},Se=()=>{fe.reportType="income_expense_summary",fe.timeRange="current_month",fe.dateRange=Te("current_month")},xe=()=>{ve.reportType="receivable_analysis",ve.endDate=(new Date).toISOString().split("T")[0],ve.customerId=""},Me=async()=>{ue.value=!0,me.value=!1;try{switch(ce.value){case"sales":await Ge();break;case"income_expense":await Ie();break;case"receivable_payable":await ze()}me.value=!0,b((()=>{"sales"===ce.value&&Ne()}))}catch(e){console.error("生成报表失败:",e),h.error("生成报表失败")}finally{ue.value=!1}},Ge=async()=>{await new Promise((e=>setTimeout(e,800))),he.value=[{date:"2023-08-01",orderNo:"DD20230801001",customerName:"北京某某科技有限公司",amount:56e3,status:"已完成"},{date:"2023-08-03",orderNo:"DD20230803001",customerName:"上海某某贸易有限公司",amount:42500,status:"已完成"},{date:"2023-08-05",orderNo:"DD20230805001",customerName:"广州某某建设工程有限公司",amount:75e3,status:"已完成"},{date:"2023-08-08",orderNo:"DD20230808001",customerName:"深圳某某电子科技有限公司",amount:31e3,status:"已送达"},{date:"2023-08-10",orderNo:"DD20230810001",customerName:"杭州某某软件有限公司",amount:47500,status:"生产中"}],pe.total=356,Object.assign(be,{totalSales:258e4,salesGrowth:15.8,orderCount:356,orderCountGrowth:12.3,averageOrderValue:7247.19,aovGrowth:3.5,customerCount:48,customerCountGrowth:4.2})},Ie=async()=>{await new Promise((e=>setTimeout(e,800)))},ze=async()=>{await new Promise((e=>setTimeout(e,800)))},Ne=()=>{Oe(),Ue()},Oe=()=>{const e=de.value;if(!e)return;const a=O(e);a.setOption({title:{text:"销售趋势"},tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}}},legend:{data:["销售额","订单数"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["1月","2月","3月","4月","5月","6月","7月","8月"]},yAxis:[{type:"value",name:"销售额",axisLabel:{formatter:"{value} 元"}},{type:"value",name:"订单数",axisLabel:{formatter:"{value}"}}],series:[{name:"销售额",type:"line",smooth:!0,yAxisIndex:0,data:[15e4,18e4,22e4,26e4,27e4,32e4,35e4,4e5]},{name:"订单数",type:"bar",yAxisIndex:1,data:[20,25,30,35,38,42,48,52]}]}),window.addEventListener("resize",(()=>{a.resize()}))},Ue=()=>{const e=ie.value;if(!e)return;const a=O(e);a.setOption({title:{text:"销售分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:["电子元件","机械设备","建筑材料","电气设备","安防设备"]},series:[{name:"销售额",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:85e4,name:"电子元件"},{value:68e4,name:"机械设备"},{value:48e4,name:"建筑材料"},{value:38e4,name:"电气设备"},{value:19e4,name:"安防设备"}]}]}),window.addEventListener("resize",(()=>{a.resize()}))},$e=()=>{me.value?(h.success("开始导出报表，请稍候..."),setTimeout((()=>{h.success("报表导出成功")}),1500)):h.warning("请先生成报表后再导出")},qe=()=>{me.value?(h.success("正在准备打印报表..."),setTimeout((()=>{window.print()}),1e3)):h.warning("请先生成报表后再打印")},je=e=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(e),Le=e=>{pe.pageSize=e,Me()},Pe=e=>{pe.page=e,Me()};return l((()=>{_e.dateRange=Te(_e.timeRange),fe.dateRange=Te(fe.timeRange)})),(e,a)=>{const t=r("el-tag"),l=r("el-icon"),b=r("el-button"),h=r("el-card"),R=r("el-option"),Y=r("el-select"),S=r("el-form-item"),x=r("el-date-picker"),M=r("el-form"),G=r("el-col"),I=r("el-row"),z=r("el-tab-pane"),N=r("el-tabs"),O=r("el-empty"),U=r("el-table-column"),De=r("el-table"),Te=r("el-pagination"),Ge=s("loading");return n(),u("div",$,[o(h,{class:"header-card mb-20"},{default:d((()=>[m("div",q,[m("div",j,[a[15]||(a[15]=m("h2",{class:"page-title"},"财务报表",-1)),o(t,{type:"info"},{default:d((()=>a[14]||(a[14]=[p("查看和导出各类财务报表")]))),_:1})]),m("div",L,[o(b,{type:"primary",onClick:$e},{default:d((()=>[o(l,null,{default:d((()=>[o(_(w))])),_:1}),a[16]||(a[16]=p(" 导出报表 "))])),_:1}),o(b,{type:"success",onClick:qe},{default:d((()=>[o(l,null,{default:d((()=>[o(_(D))])),_:1}),a[17]||(a[17]=p(" 打印报表 "))])),_:1})])])])),_:1}),o(h,{class:"mb-20"},{default:d((()=>[m("div",P,[o(N,{modelValue:ce.value,"onUpdate:modelValue":a[11]||(a[11]=e=>ce.value=e),type:"card",onTabChange:Ce},{default:d((()=>[o(z,{label:"销售报表",name:"sales"},{default:d((()=>[o(I,{gutter:20},{default:d((()=>[o(G,{span:18},{default:d((()=>[o(M,{inline:!0,model:_e,class:"filter-form"},{default:d((()=>[o(S,{label:"报表类型"},{default:d((()=>[o(Y,{modelValue:_e.reportType,"onUpdate:modelValue":a[0]||(a[0]=e=>_e.reportType=e),placeholder:"选择报表类型",onChange:ke},{default:d((()=>[o(R,{label:"销售额统计",value:"sales_summary"}),o(R,{label:"产品销售分析",value:"product_sales"}),o(R,{label:"客户销售分析",value:"customer_sales"}),o(R,{label:"销售趋势分析",value:"sales_trend"})])),_:1},8,["modelValue"])])),_:1}),o(S,{label:"时间范围"},{default:d((()=>[o(Y,{modelValue:_e.timeRange,"onUpdate:modelValue":a[1]||(a[1]=e=>_e.timeRange=e),placeholder:"选择时间范围",onChange:Ve},{default:d((()=>[o(R,{label:"本月",value:"current_month"}),o(R,{label:"上月",value:"last_month"}),o(R,{label:"本季度",value:"current_quarter"}),o(R,{label:"上季度",value:"last_quarter"}),o(R,{label:"本年度",value:"current_year"}),o(R,{label:"上年度",value:"last_year"}),o(R,{label:"自定义",value:"custom"})])),_:1},8,["modelValue"])])),_:1}),"custom"===_e.timeRange?(n(),c(S,{key:0,label:"日期区间"},{default:d((()=>[o(x,{modelValue:_e.dateRange,"onUpdate:modelValue":a[2]||(a[2]=e=>_e.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1})):f("",!0),"product_sales"===_e.reportType?(n(),c(S,{key:1},{default:d((()=>[o(Y,{modelValue:_e.productCategory,"onUpdate:modelValue":a[3]||(a[3]=e=>_e.productCategory=e),placeholder:"选择产品类别",clearable:""},{default:d((()=>[(n(!0),u(v,null,y(ge.value,(e=>(n(),c(R,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})):f("",!0),"customer_sales"===_e.reportType?(n(),c(S,{key:2},{default:d((()=>[o(Y,{modelValue:_e.customerId,"onUpdate:modelValue":a[4]||(a[4]=e=>_e.customerId=e),placeholder:"选择客户",clearable:"",filterable:"",remote:"","remote-method":Re,loading:oe.value},{default:d((()=>[(n(!0),u(v,null,y(ye.value,(e=>(n(),c(R,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})):f("",!0),o(S,null,{default:d((()=>[o(b,{type:"primary",onClick:Me},{default:d((()=>[o(l,null,{default:d((()=>[o(_(C))])),_:1}),a[18]||(a[18]=p(" 生成报表 "))])),_:1}),o(b,{onClick:Ye},{default:d((()=>[o(l,null,{default:d((()=>[o(_(k))])),_:1}),a[19]||(a[19]=p(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),o(G,{span:6,class:"time-display"},{default:d((()=>[m("div",A,[a[20]||(a[20]=p(" 当前期间：")),m("strong",null,g(we.value),1)])])),_:1})])),_:1})])),_:1}),o(z,{label:"收支报表",name:"income_expense"},{default:d((()=>[o(I,{gutter:20},{default:d((()=>[o(G,{span:18},{default:d((()=>[o(M,{inline:!0,model:fe,class:"filter-form"},{default:d((()=>[o(S,{label:"报表类型"},{default:d((()=>[o(Y,{modelValue:fe.reportType,"onUpdate:modelValue":a[5]||(a[5]=e=>fe.reportType=e),placeholder:"选择报表类型",onChange:ke},{default:d((()=>[o(R,{label:"收支汇总",value:"income_expense_summary"}),o(R,{label:"收支明细",value:"income_expense_detail"}),o(R,{label:"收支趋势",value:"income_expense_trend"}),o(R,{label:"利润分析",value:"profit_analysis"})])),_:1},8,["modelValue"])])),_:1}),o(S,{label:"时间范围"},{default:d((()=>[o(Y,{modelValue:fe.timeRange,"onUpdate:modelValue":a[6]||(a[6]=e=>fe.timeRange=e),placeholder:"选择时间范围",onChange:Ve},{default:d((()=>[o(R,{label:"本月",value:"current_month"}),o(R,{label:"上月",value:"last_month"}),o(R,{label:"本季度",value:"current_quarter"}),o(R,{label:"上季度",value:"last_quarter"}),o(R,{label:"本年度",value:"current_year"}),o(R,{label:"上年度",value:"last_year"}),o(R,{label:"自定义",value:"custom"})])),_:1},8,["modelValue"])])),_:1}),"custom"===fe.timeRange?(n(),c(S,{key:0,label:"日期区间"},{default:d((()=>[o(x,{modelValue:fe.dateRange,"onUpdate:modelValue":a[7]||(a[7]=e=>fe.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1})):f("",!0),o(S,null,{default:d((()=>[o(b,{type:"primary",onClick:Me},{default:d((()=>[o(l,null,{default:d((()=>[o(_(C))])),_:1}),a[21]||(a[21]=p(" 生成报表 "))])),_:1}),o(b,{onClick:Se},{default:d((()=>[o(l,null,{default:d((()=>[o(_(k))])),_:1}),a[22]||(a[22]=p(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),o(G,{span:6,class:"time-display"},{default:d((()=>[m("div",F,[a[23]||(a[23]=p(" 当前期间：")),m("strong",null,g(we.value),1)])])),_:1})])),_:1})])),_:1}),o(z,{label:"应收应付报表",name:"receivable_payable"},{default:d((()=>[o(I,{gutter:20},{default:d((()=>[o(G,{span:18},{default:d((()=>[o(M,{inline:!0,model:ve,class:"filter-form"},{default:d((()=>[o(S,{label:"报表类型"},{default:d((()=>[o(Y,{modelValue:ve.reportType,"onUpdate:modelValue":a[8]||(a[8]=e=>ve.reportType=e),placeholder:"选择报表类型",onChange:ke},{default:d((()=>[o(R,{label:"应收账款分析",value:"receivable_analysis"}),o(R,{label:"应付账款分析",value:"payable_analysis"}),o(R,{label:"账龄分析",value:"aging_analysis"}),o(R,{label:"回款预测",value:"collection_forecast"})])),_:1},8,["modelValue"])])),_:1}),o(S,{label:"截止日期"},{default:d((()=>[o(x,{modelValue:ve.endDate,"onUpdate:modelValue":a[9]||(a[9]=e=>ve.endDate=e),type:"date",placeholder:"选择截止日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),"receivable_analysis"===ve.reportType||"payable_analysis"===ve.reportType?(n(),c(S,{key:0},{default:d((()=>[o(Y,{modelValue:ve.customerId,"onUpdate:modelValue":a[10]||(a[10]=e=>ve.customerId=e),placeholder:"选择客户",clearable:"",filterable:"",remote:"","remote-method":Re,loading:oe.value},{default:d((()=>[(n(!0),u(v,null,y(ye.value,(e=>(n(),c(R,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})):f("",!0),o(S,null,{default:d((()=>[o(b,{type:"primary",onClick:Me},{default:d((()=>[o(l,null,{default:d((()=>[o(_(C))])),_:1}),a[24]||(a[24]=p(" 生成报表 "))])),_:1}),o(b,{onClick:xe},{default:d((()=>[o(l,null,{default:d((()=>[o(_(k))])),_:1}),a[25]||(a[25]=p(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),o(G,{span:6,class:"time-display"},{default:d((()=>[m("div",E,[a[26]||(a[26]=p(" 截止日期：")),m("strong",null,g(ve.endDate||"未选择"),1)])])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"])])])),_:1}),i((n(),c(h,null,{default:d((()=>[m("div",W,[me.value?f("",!0):(n(),u("div",X,[o(O,{description:"请选择报表类型和时间范围，然后点击'生成报表'按钮"})])),"sales"===ce.value&&"sales_summary"===_e.reportType&&me.value?(n(),u("div",B,[a[40]||(a[40]=m("h3",{class:"report-title"},"销售额统计报表",-1)),o(I,{gutter:20,class:"mb-20"},{default:d((()=>[o(G,{span:6},{default:d((()=>[o(h,{class:"statistic-card"},{default:d((()=>[m("div",H,g(je(be.totalSales)),1),a[29]||(a[29]=m("div",{class:"statistic-title"},"总销售额",-1)),m("div",J,[be.salesGrowth>=0?(n(),c(t,{key:0,type:"success"},{default:d((()=>[a[27]||(a[27]=p(" 较上期 ")),o(l,null,{default:d((()=>[o(_(V))])),_:1}),p(" "+g(be.salesGrowth)+"% ",1)])),_:1})):(n(),c(t,{key:1,type:"danger"},{default:d((()=>[a[28]||(a[28]=p(" 较上期 ")),o(l,null,{default:d((()=>[o(_(T))])),_:1}),p(" "+g(Math.abs(be.salesGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),o(G,{span:6},{default:d((()=>[o(h,{class:"statistic-card"},{default:d((()=>[m("div",K,g(be.orderCount),1),a[32]||(a[32]=m("div",{class:"statistic-title"},"订单数量",-1)),m("div",Q,[be.orderCountGrowth>=0?(n(),c(t,{key:0,type:"success"},{default:d((()=>[a[30]||(a[30]=p(" 较上期 ")),o(l,null,{default:d((()=>[o(_(V))])),_:1}),p(" "+g(be.orderCountGrowth)+"% ",1)])),_:1})):(n(),c(t,{key:1,type:"danger"},{default:d((()=>[a[31]||(a[31]=p(" 较上期 ")),o(l,null,{default:d((()=>[o(_(T))])),_:1}),p(" "+g(Math.abs(be.orderCountGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),o(G,{span:6},{default:d((()=>[o(h,{class:"statistic-card"},{default:d((()=>[m("div",Z,g(je(be.averageOrderValue)),1),a[35]||(a[35]=m("div",{class:"statistic-title"},"平均订单金额",-1)),m("div",ee,[be.aovGrowth>=0?(n(),c(t,{key:0,type:"success"},{default:d((()=>[a[33]||(a[33]=p(" 较上期 ")),o(l,null,{default:d((()=>[o(_(V))])),_:1}),p(" "+g(be.aovGrowth)+"% ",1)])),_:1})):(n(),c(t,{key:1,type:"danger"},{default:d((()=>[a[34]||(a[34]=p(" 较上期 ")),o(l,null,{default:d((()=>[o(_(T))])),_:1}),p(" "+g(Math.abs(be.aovGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),o(G,{span:6},{default:d((()=>[o(h,{class:"statistic-card"},{default:d((()=>[m("div",ae,g(be.customerCount),1),a[38]||(a[38]=m("div",{class:"statistic-title"},"客户数量",-1)),m("div",te,[be.customerCountGrowth>=0?(n(),c(t,{key:0,type:"success"},{default:d((()=>[a[36]||(a[36]=p(" 较上期 ")),o(l,null,{default:d((()=>[o(_(V))])),_:1}),p(" "+g(be.customerCountGrowth)+"% ",1)])),_:1})):(n(),c(t,{key:1,type:"danger"},{default:d((()=>[a[37]||(a[37]=p(" 较上期 ")),o(l,null,{default:d((()=>[o(_(T))])),_:1}),p(" "+g(Math.abs(be.customerCountGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1})])),_:1}),o(I,{gutter:20,class:"mb-20"},{default:d((()=>[o(G,{span:12},{default:d((()=>[m("div",le,[m("div",{ref_key:"salesTrendChart",ref:de,class:"chart"},null,512)])])),_:1}),o(G,{span:12},{default:d((()=>[m("div",re,[m("div",{ref_key:"salesDistributionChart",ref:ie,class:"chart"},null,512)])])),_:1})])),_:1}),m("div",se,[a[39]||(a[39]=m("h4",null,"销售明细",-1)),o(De,{data:he.value,border:"",stripe:"",style:{width:"100%"}},{default:d((()=>[o(U,{prop:"date",label:"日期",width:"120"}),o(U,{prop:"orderNo",label:"订单编号","min-width":"150"}),o(U,{prop:"customerName",label:"客户名称","min-width":"200"}),o(U,{prop:"amount",label:"销售金额",width:"150"},{default:d((e=>[p(g(je(e.row.amount)),1)])),_:1}),o(U,{prop:"status",label:"订单状态",width:"100"},{default:d((e=>{return[o(t,{type:(a=e.row.status,{"待确认":"info","已确认":"primary","生产中":"warning","待发货":"warning","发货中":"warning","已送达":"success","已完成":"success","已取消":"danger"}[a]||"info")},{default:d((()=>[p(g(e.row.status),1)])),_:2},1032,["type"])];var a})),_:1})])),_:1},8,["data"]),m("div",ne,[o(Te,{"current-page":pe.page,"onUpdate:currentPage":a[12]||(a[12]=e=>pe.page=e),"page-size":pe.pageSize,"onUpdate:pageSize":a[13]||(a[13]=e=>pe.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:pe.total,onSizeChange:Le,onCurrentChange:Pe},null,8,["current-page","page-size","total"])])])])):f("",!0)])])),_:1})),[[Ge,ue.value]])])}}},[["__scopeId","data-v-9740caf6"]]);export{ue as default};
