<template>
  <div v-if="quotation" class="quotation-detail">
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="form-title">报价单详情</h2>
        <div>
          <el-button @click="goBack">返回</el-button>
          <el-button
            v-if="quotation.status === '待确认'"
            type="primary"
            @click="handleEdit"
          >
            编辑
          </el-button>
          <el-button
            v-if="quotation.status === '待确认'"
            type="success"
            @click="handleConfirm"
          >
            确认报价单
          </el-button>
          <el-button
            v-if="quotation.status === '已确认' && !quotation.is_linked_to_order"
            type="success"
            @click="createOrder"
          >
            创建订单
          </el-button>
          <el-button
            v-if="quotation.status === '已确认' && !quotation.is_linked_to_order"
            type="primary"
            @click="addToOrder"
          >
            添加到订单
          </el-button>
          <el-button
            v-if="quotation.status === '已确认' && !quotation.is_linked_to_order"
            type="warning"
            @click="handleCancelConfirm"
          >
            取消确认
          </el-button>
          <el-button type="primary" @click="showExportDialog">导出报价单</el-button>
        </div>
      </div>
    </el-card>

    <!-- 基本信息 -->
    <el-card class="mb-20">
      <template #header>
        <h3>基本信息</h3>
      </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="报价单编号">{{ quotation.quotation_number }}</el-descriptions-item>
            <el-descriptions-item label="客户名称">{{ quotation.customer?.name }}</el-descriptions-item>
            <el-descriptions-item label="项目名称">{{ quotation.project_name }}</el-descriptions-item>
            <el-descriptions-item label="项目地址">{{ quotation.project_address }}</el-descriptions-item>
            <el-descriptions-item label="有效期至">{{ quotation.valid_until }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(quotation.status || '')">{{ quotation.status }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="付款条件">{{ quotation.payment_terms }}</el-descriptions-item>
            <el-descriptions-item label="交货条件">{{ quotation.delivery_terms }}</el-descriptions-item>
            <el-descriptions-item label="总金额">{{ quotation.total_amount }} 元</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ quotation.created_at }}</el-descriptions-item>
            <el-descriptions-item label="关联订单" :span="2" v-if="quotation.related_orders && quotation.related_orders.length > 0">
              <span v-for="(order, index) in quotation.related_orders" :key="order.id">
                <el-link type="primary" @click="handleViewOrder(order.id)">{{ order.order_number }}</el-link>
                <span v-if="index < quotation.related_orders.length - 1">、</span>
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ quotation.notes }}</el-descriptions-item>
          </el-descriptions>
    </el-card>

    <!-- 产品明细 -->
    <el-card>
      <template #header>
        <h3>产品明细</h3>
      </template>
          <el-table 
            :data="quotation.items" 
            style="width: 100%" 
            border 
            stripe
            show-summary
            :summary-method="getSummaries"
          >
            <el-table-column prop="product_name" label="产品名称" min-width="160" />
            <el-table-column prop="product_model" label="型号" width="100" />
            <el-table-column prop="product_specification" label="规格" min-width="80" />
            <el-table-column prop="product_unit" label="单位" width="60" />
            <el-table-column prop="quantity" label="数量" width="60" />
            <el-table-column prop="unit_price" label="单价" width="110">
              <template #default="{ row }">
                {{ formatCurrency(row.unit_price) }}
              </template>
            </el-table-column>
            <el-table-column prop="discount" label="折扣(%)" width="80">
              <template #default="{ row }">
                {{ row.discount }}
              </template>
            </el-table-column>
            <el-table-column prop="tax_rate" label="税率(%)" width="80">
              <template #default="{ row }">
                {{ row.tax_rate }}
              </template>
            </el-table-column>
            <el-table-column prop="total_price" label="金额" width="100">
              <template #default="{ row }">
                {{ formatCurrency(row.total_price) }}
              </template>
            </el-table-column>
            <el-table-column prop="notes" label="备注" min-width="150" show-overflow-tooltip />
      </el-table>
    </el-card>
    
    <!-- 导出配置对话框 -->
    <QuotationExportDialog
      v-model="exportDialogVisible"
      :quotation-id="quotation?.id"
      :quotation-number="quotation?.quotation_number"
      @export-success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { quotationApi } from '@/api/quotation'
import { orderApi } from '@/api/order'
import { formatCurrency } from '@/utils/format'
import type { TableColumnCtx } from 'element-plus'
import SelectOrderDialog from '@/components/SelectOrderDialog.vue'
import QuotationExportDialog from '@/components/QuotationExportDialog.vue'

const route = useRoute()
const router = useRouter()


const quotation = ref<any>(null)
const exportDialogVisible = ref(false)

// 返回上一页  
const goBack = () => {
  // 获取当前浏览器历史记录
  const historyLength = window.history.length
  
  // 如果历史记录长度小于等于1，直接返回列表页
  if (historyLength <= 1) {
    router.push('/quotations')
    return
  }
  
  // 检查document.referrer来判断上一页是否是编辑页面
  const referrer = document.referrer
  const isFromEditPage = referrer && (
    referrer.includes('/quotations/edit/') || 
    referrer.includes('/quotations/new') ||
    referrer.includes('/quotations/create-from-request/')
  )
  
  if (isFromEditPage) {
    // 如果来自编辑页面，返回到列表页
    router.push('/quotations')
  } else {
    // 否则返回上一页
    router.back()
  }
}

// 编辑报价单
const handleEdit = () => {
  router.push(`/quotations/edit/${quotation.value.id}`)
}

// 确认报价单
const handleConfirm = async () => {
  try {
    await ElMessageBox.confirm('确定要确认此报价单吗？确认后状态将变为已确认。', '确认报价单', {
      type: 'warning'
    })

    await quotationApi.updateStatus(quotation.value.id, '已确认')
    ElMessage.success('报价单已确认')

    // 重新加载报价单数据以更新状态
    await loadQuotation()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认报价单失败:', error)
      const errorMessage = error.response?.data?.message || error.message || '未知错误'
      ElMessage.error(`确认报价单失败: ${errorMessage}`)
    }
  }
}

// 取消确认报价单
const handleCancelConfirm = async () => {
  try {
    await ElMessageBox.confirm('确定要取消确认此报价单吗？状态将变为待确认。', '取消确认', {
      type: 'warning'
    })

    await quotationApi.updateStatus(quotation.value.id, '待确认')
    ElMessage.success('已取消确认')

    // 重新加载报价单数据以更新状态
    await loadQuotation()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消确认失败:', error)
      const errorMessage = error.response?.data?.message || error.message || '未知错误'
      ElMessage.error(`取消确认失败: ${errorMessage}`)
    }
  }
}

// 创建订单
const createOrder = async () => {
  try {
    await ElMessageBox.confirm('确定要将此报价单转为订单吗？', '确认', {
      type: 'warning'
    })

    const response = await quotationApi.createOrder(quotation.value.id, {})
    ElMessage.success('订单创建成功')

    // 跳转到新创建的订单编辑页面
    if (response && response.id) {
      router.push(`/orders/edit/${response.id}`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建订单失败:', error)
      ElMessage.error('创建订单失败')
    }
  }
}

// 添加到订单
const addToOrder = () => {
  // TODO: 实现添加到现有订单的功能
  ElMessage.info('添加到订单功能待实现')
}

// 显示导出对话框
const showExportDialog = () => {
  exportDialogVisible.value = true
}

// 导出成功回调
const handleExportSuccess = () => {
  // 可以在这里添加导出成功后的处理逻辑
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap = {
    '待确认': 'warning',
    '已确认': 'success',
    '已拒绝': 'danger',
    '已过期': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取订单状态类型
const getOrderStatusType = (status: string) => {
  const statusMap = {
    '待确认': 'warning',
    '已确认': 'primary',
    '生产中': 'warning',
    '待发货': 'info',
    '部分发货': 'warning',
    '全部发货': 'success',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取收款状态类型
const getPaymentStatusType = (status: string) => {
  const statusMap = {
    '未收款': 'danger',
    '部分收款': 'warning',
    '已收款': 'success'
  }
  return statusMap[status] || 'info'
}

// 查看订单详情
const handleViewOrder = (orderId: number) => {
  router.push(`/orders/view/${orderId}`)
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}



// 表格汇总
const getSummaries = (param: { columns: TableColumnCtx<any>[]; data: any[] }) => {
  const { columns, data } = param
  const sums: string[] = []
  
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    
    const prop = column.property
    if (prop && ['quantity', 'total_price'].includes(prop)) {
      const values = data.map(item => Number(item[prop]))
      const total = values.reduce((prev, curr) => prev + curr, 0)
      
      if (prop === 'total_price') {
        sums[index] = formatCurrency(total)
      } else {
        sums[index] = total.toString()
      }
    } else {
      sums[index] = ''
    }
  })
  
  return sums
}

// 加载报价单数据
const loadQuotation = async () => {
  try {
    const id = route.params.id as string
    const response = await quotationApi.getById(parseInt(id))
    quotation.value = response.data || response
  } catch (error) {
    console.error('加载报价单失败:', error)
    ElMessage.error('加载报价单失败')
  }
}

onMounted(() => {
  loadQuotation()
})
</script>

<style scoped>
.quotation-detail {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-title {
  margin: 0;
  font-size: 18px;
}

.mb-20 {
  margin-bottom: 20px;
}

.column-checkboxes {
  display: flex;
  flex-wrap: wrap;
}

.column-checkboxes .el-checkbox {
  margin-right: 15px;
  margin-bottom: 10px;
}
</style>
