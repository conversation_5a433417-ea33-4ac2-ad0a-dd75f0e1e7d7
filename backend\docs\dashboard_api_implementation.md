# Dashboard API 实现文档

## 概述

本文档描述了EMB系统仪表盘API的实现，包括所有API端点、功能特性和测试覆盖。

## API端点

### 1. 统计数据 `/api/v1/dashboard/stats`

**功能**: 获取系统整体统计数据
**方法**: GET
**响应数据**:
- `customers_total`: 客户总数
- `products_total`: 产品总数  
- `quotations_total`: 报价单总数
- `orders_total`: 订单总数
- `orders_pending`: 待处理订单数
- `orders_completed`: 已完成订单数
- `revenue_total`: 总收入
- `revenue_month`: 本月收入
- `last_updated`: 最后更新时间

### 2. 待处理报价单 `/api/v1/dashboard/pending_quotations`

**功能**: 获取待处理的报价单列表
**方法**: GET
**查询参数**:
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 10, 最大: 100)

**过滤条件**: 状态为 '草稿' 或 '已提交' 的报价单

### 3. 待发货订单 `/api/v1/dashboard/pending_orders`

**功能**: 获取待发货的订单列表
**方法**: GET
**查询参数**:
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 10, 最大: 100)

**过滤条件**: 状态为 '已确认'、'生产中' 或 '待发货' 的订单

### 4. 销售趋势 `/api/v1/dashboard/sales_trends`

**功能**: 获取销售趋势数据
**方法**: GET
**查询参数**:
- `days`: 统计天数 (默认: 30, 最大: 365)

**返回数据**: 每日销售金额和订单数量的时间序列

### 5. 产品销售占比 `/api/v1/dashboard/product_sales`

**功能**: 获取产品分类销售占比
**方法**: GET
**查询参数**:
- `days`: 统计天数 (默认: 30, 最大: 365)

**返回数据**: 各产品分类的销售金额、占比和订单数量

### 6. 天气信息 `/api/v1/dashboard/weather`

**功能**: 获取天气信息 (模拟数据)
**方法**: GET
**查询参数**:
- `city`: 城市名称 (默认: '北京')

**返回数据**: 城市、温度、天气状况、湿度、风力等信息

### 7. 用户信息 `/api/v1/dashboard/user_info`

**功能**: 获取当前用户信息 (模拟数据)
**方法**: GET

**返回数据**: 用户名、角色、最后登录时间、登录次数、权限列表

## 技术实现

### 架构设计

- **框架**: Flask + Flask-RESTX
- **数据库**: SQLAlchemy ORM
- **响应格式**: 统一的JSON响应格式
- **分页**: 使用SQLAlchemy的paginate方法
- **错误处理**: 统一的错误响应格式

### 数据模型关联

API涉及以下数据模型:
- `Customer`: 客户信息
- `Product`: 产品信息
- `ProductCategory`: 产品分类
- `ProductSpecification`: 产品规格
- `Quotation`: 报价单
- `Order`: 订单
- `OrderProduct`: 订单产品明细
- `CustomerDeliveryAddress`: 客户送货地址

### 查询优化

- 使用JOIN查询减少数据库访问次数
- 合理使用索引字段进行过滤和排序
- 分页查询避免大数据量加载
- 使用聚合函数进行统计计算

## 测试覆盖

### 测试文件: `tests/test_dashboard.py`

包含13个测试用例，覆盖所有API端点:

1. **统计数据测试**
   - 空数据统计
   - 有数据统计

2. **待处理报价单测试**
   - 空列表
   - 有数据列表
   - 状态过滤验证

3. **待发货订单测试**
   - 空列表
   - 有数据列表
   - 状态过滤验证

4. **销售趋势测试**
   - 空数据趋势
   - 自定义天数参数

5. **产品销售占比测试**
   - 空数据占比

6. **天气信息测试**
   - 默认城市
   - 指定城市

7. **用户信息测试**
   - 基本用户信息

8. **分页功能测试**
   - 分页参数验证

### 测试结果

```
13 passed, 3 warnings
```

所有测试用例均通过，警告为SQLite Decimal类型转换提示，不影响功能。

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": { ... },
  "success": true
}
```

### 错误响应
```json
{
  "code": 500,
  "message": "错误信息",
  "success": false
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "获取数据成功",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 10,
      "total": 100,
      "pages": 10
    }
  },
  "success": true
}
```

## 部署说明

1. **依赖安装**: 确保安装了所有必需的Python包
2. **数据库迁移**: 运行数据库迁移创建必要的表结构
3. **API注册**: Dashboard API已在应用初始化时自动注册
4. **访问路径**: 所有API均在 `/api/v1/dashboard/` 路径下

## 扩展建议

1. **缓存优化**: 对统计数据添加Redis缓存
2. **实时数据**: 使用WebSocket推送实时更新
3. **权限控制**: 添加基于角色的访问控制
4. **数据导出**: 支持Excel/PDF格式导出
5. **图表数据**: 优化图表展示的数据格式
6. **性能监控**: 添加API性能监控和日志

## 维护说明

- 定期检查数据库查询性能
- 监控API响应时间
- 更新测试用例覆盖新功能
- 保持文档与代码同步更新
