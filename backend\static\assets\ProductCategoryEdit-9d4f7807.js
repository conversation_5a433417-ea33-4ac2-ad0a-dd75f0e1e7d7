import{u as e,b as l,r as a,d as r,e as d,f as t,o,c as s,i,h as n,U as u,a as p,t as m,k as v,F as _,m as c,g as f,l as b}from"./index-3d4c440c.js";import{_ as g}from"./_plugin-vue_export-helper-1b428a4d.js";const V={class:"category-edit"},h={class:"flex-between"},x={class:"form-title"},y=g({__name:"ProductCategoryEdit",setup(g){const y=e(),w=l(),k=a(null),U=r((()=>!y.params.id||"new"===y.params.id)),C=a([{id:1,name:"阀门",parent_id:0,level:1,sort_order:1,description:"阀门类产品",notes:""},{id:2,name:"止回阀",parent_id:1,level:2,sort_order:1,description:"阀门/止回阀",notes:""},{id:3,name:"球阀",parent_id:1,level:2,sort_order:2,description:"阀门/球阀",notes:""},{id:4,name:"水泵",parent_id:0,level:1,sort_order:2,description:"水泵类产品",notes:""},{id:5,name:"离心泵",parent_id:4,level:2,sort_order:1,description:"水泵/离心泵",notes:""},{id:6,name:"潜水泵",parent_id:4,level:2,sort_order:2,description:"水泵/潜水泵",notes:""},{id:7,name:"管道",parent_id:0,level:1,sort_order:3,description:"管道类产品",notes:""},{id:8,name:"钢管",parent_id:7,level:2,sort_order:1,description:"管道/钢管",notes:""},{id:9,name:"PVC管",parent_id:7,level:2,sort_order:2,description:"管道/PVC管",notes:""}]),q=a({name:"",parent_id:0,level:1,sort_order:1,description:"",notes:""}),N={name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],parent_id:[{required:!0,message:"请选择父级分类",trigger:"change"}],sort_order:[{required:!0,message:"请输入排序",trigger:"blur"}]},P=()=>{k.value.validate((e=>{if(!e)return u.error("请检查表单内容"),!1;if(q.value.level=(e=>{if(0===e)return 1;const l=C.value.find((l=>l.id===e));return l?l.level+1:1})(q.value.parent_id),U.value){const e=Math.max(...C.value.map((e=>e.id)))+1,l={...q.value,id:e};C.value.push(l),u.success("分类添加成功")}else{const e=C.value.findIndex((e=>e.id===Number(y.params.id)));-1!==e&&(C.value[e]={...q.value},u.success("分类更新成功"))}j()}))},j=()=>{w.back()};return d((()=>{(()=>{if(U.value)q.value={name:"",parent_id:0,level:1,sort_order:C.value.filter((e=>0===e.parent_id)).length+1,description:"",notes:""};else{const e=Number(y.params.id),l=C.value.find((l=>l.id===e));l?q.value={...l}:(u.error("分类不存在"),j())}})()})),(e,l)=>{const a=t("el-button"),r=t("el-card"),d=t("el-input"),u=t("el-form-item"),g=t("el-option"),w=t("el-select"),I=t("el-input-number"),R=t("el-form");return o(),s("div",V,[i(r,{class:"header-card mb-20"},{default:n((()=>[p("div",h,[p("h2",x,m(U.value?"新增产品分类":"编辑产品分类"),1),p("div",null,[i(a,{onClick:j},{default:n((()=>l[5]||(l[5]=[v("返回")]))),_:1}),i(a,{type:"primary",onClick:P},{default:n((()=>l[6]||(l[6]=[v("保存")]))),_:1})])])])),_:1}),i(r,null,{default:n((()=>[i(R,{ref_key:"formRef",ref:k,model:q.value,rules:N,"label-width":"120px",class:"category-form"},{default:n((()=>[i(u,{label:"分类名称",prop:"name"},{default:n((()=>[i(d,{modelValue:q.value.name,"onUpdate:modelValue":l[0]||(l[0]=e=>q.value.name=e),placeholder:"请输入分类名称"},null,8,["modelValue"])])),_:1}),i(u,{label:"父级分类",prop:"parent_id"},{default:n((()=>[i(w,{modelValue:q.value.parent_id,"onUpdate:modelValue":l[1]||(l[1]=e=>q.value.parent_id=e),placeholder:"请选择父级分类",style:{width:"100%"}},{default:n((()=>[i(g,{label:"无父级分类",value:0}),(o(!0),s(_,null,c(C.value,(e=>(o(),f(g,{key:e.id,label:e.name,value:e.id,disabled:e.id===Number(b(y).params.id)},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(u,{label:"排序",prop:"sort_order"},{default:n((()=>[i(I,{modelValue:q.value.sort_order,"onUpdate:modelValue":l[2]||(l[2]=e=>q.value.sort_order=e),min:1,max:99,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),i(u,{label:"描述",prop:"description"},{default:n((()=>[i(d,{modelValue:q.value.description,"onUpdate:modelValue":l[3]||(l[3]=e=>q.value.description=e),type:"textarea",rows:3,placeholder:"请输入分类描述"},null,8,["modelValue"])])),_:1}),i(u,{label:"备注",prop:"notes"},{default:n((()=>[i(d,{modelValue:q.value.notes,"onUpdate:modelValue":l[4]||(l[4]=e=>q.value.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1})])}}},[["__scopeId","data-v-a7c3d83c"]]);export{y as default};
