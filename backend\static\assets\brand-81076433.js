import{I as r}from"./index-3d4c440c.js";async function a(){try{const a=await r({url:"/api/v1/brands",method:"get",params:{per_page:999}});return console.log("getAllBrands 响应:",a),a&&a.data&&Array.isArray(a.data.items)?a.data.items:a&&Array.isArray(a.items)?a.items:[]}catch(a){return console.error("获取所有品牌失败:",a),[]}}async function t(a){try{const t=await r({url:"/api/v1/brands",method:"get",params:a});return console.log("listBrands 原始响应:",t),t&&t.data&&Array.isArray(t.data.items)?t.data:t&&Array.isArray(t.items)?t:{items:[],page_info:{total_items:0,total_pages:0,current_page:a.page||1,per_page:a.per_page||10}}}catch(t){return console.error("获取品牌列表失败:",t),{items:[],page_info:{total_items:0,total_pages:0,current_page:a.page||1,per_page:a.per_page||10}}}}function e(a){return r({url:`/api/v1/brands/${a}`,method:"get"})}function s(a){return r({url:"/api/v1/brands",method:"post",data:a})}function n(a,t){return r({url:`/api/v1/brands/${a}`,method:"put",data:t})}function o(a){return r({url:`/api/v1/brands/${a}`,method:"delete"})}async function i(){try{const a=await r({url:"/api/v1/brands",method:"get",params:{per_page:1,sort:"-sort_order"}});if(console.log("获取最大排序值响应:",a),a&&Array.isArray(a.items)&&a.items.length>0){const r=a.items[0].sort_order||0;return Number(r)}return 0}catch(a){return console.error("获取最大排序值失败:",a),99}}export{i as a,e as b,s as c,o as d,a as g,t as l,n as u};
