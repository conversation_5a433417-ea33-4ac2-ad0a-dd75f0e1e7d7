"""
财务管理API
提供收款记录、退款记录、对账单和应收款管理的CRUD操作
基于原项目API实现，确保与现有数据库结构100%兼容
"""
from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from sqlalchemy import or_, desc, func, case
from sqlalchemy.orm import selectinload, joinedload
from typing import Dict, List, Optional, Any, Tuple
import uuid
from datetime import datetime, date, timedelta
from decimal import Decimal

from app.models.finance import PaymentRecord, RefundRecord, Statement, StatementDeliveryNote, Receivable, StatementReceivable
from app.models.customer import Customer
from app.models.order import Order, DeliveryNote
from app.models.return_order import ReturnOrder
from app.models.customer import Customer
from app.models.system import CompanyBankAccount
from app.schemas.finance import (
    PaymentRecordSchema, RefundRecordSchema, StatementSchema, StatementCreateSchema, StatementUpdateSchema,
    ReceivableSchema, ReceivableStatisticsSchema, CompanyBankAccountSchema,
    FinanceStatisticsSchema, PaymentStatisticsSchema, AgingAnalysisSchema
)
from app.utils.response import (
    success_response,
    error_response,
    paginated_response,
    validation_error_response,
    not_found_response
)
from app.utils.pagination import PaginationHelper
from app.utils.exceptions import ValidationError as CustomValidationError, NotFoundError
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('finance', description='财务管理API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 自动从Marshmallow Schema生成API模型
payment_record_model = create_input_model(api, PaymentRecordSchema, 'PaymentRecordInput')
refund_record_model = create_input_model(api, RefundRecordSchema, 'RefundRecordInput')
statement_model = create_input_model(api, StatementCreateSchema, 'StatementInput')


# 收款记录相关API
@api.route('/payment-records')
class PaymentRecordList(Resource):
    @api.doc('get_payment_records')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('order_id', '订单ID', type='integer')
    @api.param('payment_method', '收款方式')
    @api.param('status', '收款状态')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取收款记录列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            customer_id = request.args.get('customer_id', type=int)
            order_id = request.args.get('order_id', type=int)
            payment_method = request.args.get('payment_method', '')
            status = request.args.get('status', '')
            start_date = request.args.get('start_date', '')
            end_date = request.args.get('end_date', '')
            
            # 构建查询，包含关联信息
            query = db.session.query(
                PaymentRecord,
                Order.order_number,
                Customer.name.label('customer_name')
            ).join(
                Order, PaymentRecord.order_id == Order.id
            ).join(
                Customer, Order.customer_id == Customer.id
            )
            
            # 应用过滤条件
            if customer_id:
                query = query.filter(Order.customer_id == customer_id)
            if order_id:
                query = query.filter(PaymentRecord.order_id == order_id)
            if payment_method:
                query = query.filter(PaymentRecord.payment_method == payment_method)
            if status:
                query = query.filter(PaymentRecord.status == status)
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d').date()
                    query = query.filter(PaymentRecord.payment_date >= start)
                except ValueError:
                    return make_response(error_response, "开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            if end_date:
                try:
                    end = datetime.strptime(end_date, '%Y-%m-%d').date()
                    query = query.filter(PaymentRecord.payment_date <= end)
                except ValueError:
                    return make_response(error_response, "结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            # 排序
            query = query.order_by(PaymentRecord.payment_date.desc())
            
            # 分页
            pagination = query.paginate(page=page, per_page=per_page, error_out=False)
            
            # 准备响应数据
            results = []
            for record, order_number, customer_name in pagination.items:
                data = PaymentRecordSchema().dump(record)
                data['order_number'] = order_number
                data['customer_name'] = customer_name
                
                # 生成收款单号（如果没有）
                if not hasattr(record, 'payment_number') or not record.payment_number:
                    data['payment_number'] = f"SK{record.created_at.strftime('%Y%m%d')}{record.id:04d}"
                
                # 处理银行账户信息
                if record.payment_method == 'bank_transfer' and record.bank_account:
                    try:
                        if record.bank_account.isdigit():
                            bank_account = CompanyBankAccount.query.get(int(record.bank_account))
                            if bank_account:
                                data['bank_account_info'] = f"{bank_account.bank_name} - {bank_account.account_number} ({bank_account.account_name})"
                    except Exception as e:
                        current_app.logger.error(f"获取银行账户信息失败: {str(e)}")
                
                results.append(data)
            
            # 构建分页信息
            page_info = {
                'page': pagination.page,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev,
                'next_num': pagination.next_num,
                'prev_num': pagination.prev_num,
            }
            
            # 构建响应数据
            response_data = {
                'data': results,
                'pagination': page_info
            }
            return make_response(success_response, response_data, "获取收款记录列表成功")
            
        except Exception as e:
            current_app.logger.error(f"获取收款记录列表失败: {str(e)}")
            return make_response(error_response, f"获取收款记录列表失败: {str(e)}")

    @api.doc('create_payment_record',
             body=payment_record_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 404: 'Order Not Found',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建收款记录"""
        try:
            data = request.get_json() or {}
            
            # 数据验证
            payment_schema = PaymentRecordSchema(exclude=(
                'id', 'payment_number', 'status', 'order_number', 'customer_name', 
                'customer_id', 'bank_account_info', 'created_at', 'updated_at'
            ))
            validated_data = payment_schema.load(data)

            # 检查订单是否存在
            order = Order.query.get(validated_data['order_id'])
            if not order:
                return make_response(error_response, "订单不存在", errors={"order_id": "指定的订单不存在"})

            # 生成收款单号
            payment_number = f"SK{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"

            # 创建收款记录
            payment_record = PaymentRecord(
                order_id=validated_data['order_id'],
                payment_date=validated_data['payment_date'],
                amount=validated_data['amount'],
                payment_method=validated_data['payment_method'],
                reference_number=validated_data.get('reference_number', ''),
                bank_account=validated_data.get('bank_account', ''),
                notes=validated_data.get('notes', ''),
                status='待确认',
                created_by=validated_data.get('created_by', '')
            )
            
            # 尝试设置收款单号
            try:
                payment_record.payment_number = payment_number
            except:
                pass
            
            db.session.add(payment_record)
            
            # 更新订单已付款金额
            order.paid_amount = (order.paid_amount or Decimal('0.0')) + Decimal(str(validated_data['amount']))
            
            db.session.commit()

            # 返回创建的收款记录信息
            result = PaymentRecordSchema().dump(payment_record)
            result['order_number'] = order.order_number
            result['customer_name'] = order.customer.name if order.customer else ''
            if 'payment_number' not in result:
                result['payment_number'] = payment_number
            
            return make_response(success_response, result, "收款记录创建成功", 201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建收款记录失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建收款记录失败: {str(e)}")


@api.route('/payment-records/<int:payment_id>')
class PaymentRecordDetail(Resource):
    @api.doc('get_payment_record')
    def get(self, payment_id):
        """获取收款记录详情"""
        try:
            payment_record = PaymentRecord.query.get(payment_id)
            if not payment_record:
                return make_response(not_found_response, "收款记录不存在")

            # 获取关联订单和客户信息
            order = Order.query.get(payment_record.order_id)
            customer = Customer.query.get(order.customer_id) if order else None

            # 准备响应数据
            result = PaymentRecordSchema().dump(payment_record)

            # 生成收款单号（如果没有）
            if not hasattr(payment_record, 'payment_number') or not payment_record.payment_number:
                result['payment_number'] = f"SK{payment_record.created_at.strftime('%Y%m%d')}{payment_record.id:04d}"

            # 处理银行账户信息
            if payment_record.payment_method == 'bank_transfer' and payment_record.bank_account:
                try:
                    if payment_record.bank_account.isdigit():
                        bank_account = CompanyBankAccount.query.get(int(payment_record.bank_account))
                        if bank_account:
                            result['bank_account_info'] = f"{bank_account.bank_name} - {bank_account.account_number} ({bank_account.account_name})"
                except Exception as e:
                    current_app.logger.error(f"获取银行账户信息失败: {str(e)}")

            # 添加关联信息
            if order:
                result['order_number'] = order.order_number
                result['customer_name'] = customer.name if customer else ''
                result['customer_id'] = customer.id if customer else None
                result['total_amount'] = float(order.total_amount) if order.total_amount else 0
                result['paid_amount'] = float(order.paid_amount) if order.paid_amount else 0
                result['unpaid_amount'] = float(order.total_amount - order.paid_amount) if order.total_amount and order.paid_amount else 0

            return make_response(success_response, result, "获取收款记录详情成功")

        except Exception as e:
            current_app.logger.error(f"获取收款记录详情失败: {str(e)}")
            return make_response(error_response, f"获取收款记录详情失败: {str(e)}")


@api.route('/payment-records/<int:payment_id>/status')
class PaymentRecordStatus(Resource):
    @api.doc('update_payment_record_status')
    def put(self, payment_id):
        """更新收款记录状态"""
        try:
            payment_record = PaymentRecord.query.get(payment_id)
            if not payment_record:
                return make_response(not_found_response, "收款记录不存在")

            data = request.get_json() or {}

            if 'status' not in data:
                return make_response(error_response, "缺少状态参数", code=400)

            new_status = data['status']
            if new_status not in ['待确认', '已确认', '已取消']:
                return make_response(error_response, "无效的状态值", code=400)

            # 更新状态
            old_status = payment_record.status
            payment_record.status = new_status
            payment_record.updated_at = datetime.now()

            # 处理订单已付款金额
            order = Order.query.get(payment_record.order_id)
            if order:
                if new_status == '已取消' and old_status != '已取消':
                    # 取消收款，减少已付款金额
                    order.paid_amount = (order.paid_amount or Decimal('0.0')) - Decimal(str(payment_record.amount))
                elif new_status == '已确认' and old_status == '已取消':
                    # 确认收款，增加已付款金额
                    order.paid_amount = (order.paid_amount or Decimal('0.0')) + Decimal(str(payment_record.amount))

            db.session.commit()

            return make_response(success_response, {"status": new_status}, "收款记录状态更新成功")

        except Exception as e:
            current_app.logger.error(f"更新收款记录状态失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新收款记录状态失败: {str(e)}")


# 退款记录相关API
@api.route('/refund-records')
class RefundRecordList(Resource):
    @api.doc('get_refund_records')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('return_order_id', '退货单ID', type='integer')
    @api.param('refund_method', '退款方式')
    @api.param('status', '退款状态')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取退款记录列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            customer_id = request.args.get('customer_id', type=int)
            return_order_id = request.args.get('return_order_id', type=int)
            refund_method = request.args.get('refund_method', '')
            status = request.args.get('status', '')
            start_date = request.args.get('start_date', '')
            end_date = request.args.get('end_date', '')

            # 构建查询，包含关联信息
            query = db.session.query(
                RefundRecord,
                ReturnOrder.return_number,
                ReturnOrder.order_id,
                Order.order_number,
                Customer.id.label('customer_id'),
                Customer.name.label('customer_name')
            ).join(
                ReturnOrder, RefundRecord.return_order_id == ReturnOrder.id
            ).join(
                Order, ReturnOrder.order_id == Order.id
            ).join(
                Customer, Order.customer_id == Customer.id
            )

            # 应用过滤条件
            if customer_id:
                query = query.filter(Order.customer_id == customer_id)
            if return_order_id:
                query = query.filter(RefundRecord.return_order_id == return_order_id)
            if refund_method:
                query = query.filter(RefundRecord.refund_method == refund_method)
            if status:
                query = query.filter(RefundRecord.status == status)
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d').date()
                    query = query.filter(RefundRecord.refund_date >= start)
                except ValueError:
                    return make_response(error_response, "开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            if end_date:
                try:
                    end = datetime.strptime(end_date, '%Y-%m-%d').date()
                    query = query.filter(RefundRecord.refund_date <= end)
                except ValueError:
                    return make_response(error_response, "结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            # 排序
            query = query.order_by(RefundRecord.refund_date.desc())

            # 分页
            pagination = query.paginate(page=page, per_page=per_page, error_out=False)

            # 准备响应数据
            results = []
            for record, return_number, order_id, order_number, customer_id, customer_name in pagination.items:
                data = RefundRecordSchema().dump(record)
                data['return_order_number'] = return_number
                data['order_id'] = order_id
                data['order_number'] = order_number
                data['customer_id'] = customer_id
                data['customer_name'] = customer_name
                results.append(data)

            # 构建分页信息
            page_info = {
                'page': pagination.page,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev,
                'next_num': pagination.next_num,
                'prev_num': pagination.prev_num,
            }

            # 构建响应数据
            response_data = {
                'data': results,
                'pagination': page_info
            }
            return make_response(success_response, response_data, "获取退款记录列表成功")

        except Exception as e:
            current_app.logger.error(f"获取退款记录列表失败: {str(e)}")
            return make_response(error_response, f"获取退款记录列表失败: {str(e)}")

    @api.doc('create_refund_record',
             body=refund_record_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 404: 'Return Order Not Found',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建退款记录"""
        try:
            data = request.get_json() or {}

            # 数据验证
            refund_schema = RefundRecordSchema(exclude=(
                'id', 'refund_no', 'status', 'return_order_number', 'order_id', 'order_number',
                'customer_id', 'customer_name', 'bank_account_info', 'created_at', 'updated_at'
            ))
            validated_data = refund_schema.load(data)

            # 检查退货单是否存在
            return_order = ReturnOrder.query.get(validated_data['return_order_id'])
            if not return_order:
                return make_response(error_response, "退货单不存在", errors={"return_order_id": "指定的退货单不存在"})

            # 获取关联的订单
            order = Order.query.get(return_order.order_id)
            if not order:
                return make_response(error_response, "关联订单不存在")

            # 生成退款单号
            refund_no = f"RF{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4().int)[:6]}"

            # 创建退款记录
            refund_record = RefundRecord(
                return_order_id=validated_data['return_order_id'],
                refund_no=refund_no,
                refund_date=validated_data['refund_date'],
                amount=validated_data['amount'],
                refund_method=validated_data['refund_method'],
                reference_number=validated_data.get('reference_number', ''),
                bank_account=str(validated_data.get('bank_account', '')),
                notes=validated_data.get('notes', ''),
                status='待处理',
                created_by=validated_data.get('created_by', '')
            )

            db.session.add(refund_record)
            db.session.commit()

            # 返回创建的退款记录信息
            result = RefundRecordSchema().dump(refund_record)
            result['return_order_number'] = return_order.return_number
            result['order_id'] = return_order.order_id
            result['order_number'] = order.order_number
            result['customer_id'] = order.customer_id
            result['customer_name'] = order.customer.name if order.customer else None

            return make_response(success_response, result, "退款记录创建成功", 201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建退款记录失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建退款记录失败: {str(e)}")


@api.route('/refund-records/<int:refund_id>')
class RefundRecordDetail(Resource):
    @api.doc('get_refund_record')
    def get(self, refund_id):
        """获取退款记录详情"""
        try:
            refund_record = RefundRecord.query.get(refund_id)
            if not refund_record:
                return make_response(not_found_response, "退款记录不存在")

            # 获取关联的退货单、订单和客户信息
            return_order = ReturnOrder.query.get(refund_record.return_order_id)
            if not return_order:
                return make_response(error_response, "关联的退货单不存在")

            order = Order.query.get(return_order.order_id)
            customer = Customer.query.get(order.customer_id) if order else None

            # 准备响应数据
            result = RefundRecordSchema().dump(refund_record)
            result['return_order_number'] = return_order.return_number
            result['order_id'] = return_order.order_id
            result['order_number'] = order.order_number if order else None
            result['customer_id'] = order.customer_id if order else None
            result['customer_name'] = customer.name if customer else None

            # 处理银行账户信息
            if refund_record.refund_method == 'bank_transfer' and refund_record.bank_account:
                try:
                    bank_account = CompanyBankAccount.query.get(refund_record.bank_account)
                    if bank_account:
                        result['bank_account_info'] = f"{bank_account.bank_name} - {bank_account.account_number} ({bank_account.account_name})"
                except Exception as e:
                    current_app.logger.error(f"获取银行账户信息失败: {str(e)}")

            return make_response(success_response, result, "获取退款记录详情成功")

        except Exception as e:
            current_app.logger.error(f"获取退款记录详情失败: {str(e)}")
            return make_response(error_response, f"获取退款记录详情失败: {str(e)}")


@api.route('/refund-records/<int:refund_id>/status')
class RefundRecordStatus(Resource):
    @api.doc('update_refund_record_status')
    def put(self, refund_id):
        """更新退款记录状态"""
        try:
            refund_record = RefundRecord.query.get(refund_id)
            if not refund_record:
                return make_response(not_found_response, "退款记录不存在")

            data = request.get_json() or {}

            if 'status' not in data:
                return make_response(error_response, "缺少状态参数", code=400)

            new_status = data['status']
            if new_status not in ['待处理', '已退款', '已取消']:
                return make_response(error_response, "无效的状态值", code=400)

            # 更新状态
            old_status = refund_record.status
            refund_record.status = new_status
            refund_record.updated_at = datetime.now()

            # 处理订单已付款金额
            if new_status == '已退款' and old_status != '已退款':
                return_order = ReturnOrder.query.get(refund_record.return_order_id)
                if return_order and return_order.order_id:
                    order = Order.query.get(return_order.order_id)
                    if order:
                        order.paid_amount = (order.paid_amount or Decimal('0.0')) - Decimal(str(refund_record.amount))

            # 如果取消退款，需要恢复订单已付款金额
            if new_status == '已取消' and old_status == '已退款':
                return_order = ReturnOrder.query.get(refund_record.return_order_id)
                if return_order and return_order.order_id:
                    order = Order.query.get(return_order.order_id)
                    if order:
                        order.paid_amount = (order.paid_amount or Decimal('0.0')) + Decimal(str(refund_record.amount))

            db.session.commit()

            return make_response(success_response, {"status": new_status}, "退款记录状态更新成功")

        except Exception as e:
            current_app.logger.error(f"更新退款记录状态失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新退款记录状态失败: {str(e)}")


# 对账单相关API
@api.route('/statements')
class StatementList(Resource):
    @api.doc('get_statements')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('status', '对账单状态')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取对账单列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 10, type=int), 100)
            customer_id = request.args.get('customer_id', type=int)
            status = request.args.get('status', '')
            start_date = request.args.get('start_date', '')
            end_date = request.args.get('end_date', '')

            query = Statement.query.options(joinedload(Statement.customer))

            # 应用过滤条件
            if customer_id:
                query = query.filter(Statement.customer_id == customer_id)
            if status:
                query = query.filter(Statement.status == status)
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(Statement.statement_date >= start)
                except ValueError:
                    return make_response(error_response, "开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            if end_date:
                try:
                    end = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                    query = query.filter(Statement.statement_date < end)
                except ValueError:
                    return make_response(error_response, "结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            # 排序
            query = query.order_by(Statement.created_at.desc())

            # 分页
            pagination = query.paginate(page=page, per_page=per_page, error_out=False)

            # 序列化数据
            statements = pagination.items
            data = []

            for statement in statements:
                try:
                    statement_data = StatementSchema().dump(statement)

                    # 重新计算对账单金额
                    delivery_notes = statement.delivery_notes
                    delivery_total = 0

                    for delivery_note in delivery_notes:
                        note_total = 0
                        # 直接使用发货单的总金额（已考虑折扣和税率）
                        try:
                            note_total = float(getattr(delivery_note, 'total_amount', 0) or 0)
                        except Exception as e:
                            current_app.logger.warning(f"获取发货单总金额时出错: {str(e)}")
                            note_total = 0

                        delivery_total += note_total

                    # 计算退货总金额
                    order_ids = [dn.order_id for dn in delivery_notes if dn.order_id]
                    return_total = 0

                    if order_ids:
                        return_orders = ReturnOrder.query.filter(ReturnOrder.order_id.in_(order_ids)).all()
                        for return_order in return_orders:
                            order_return_total = 0
                            for item in getattr(return_order, 'items', []):
                                try:
                                    quantity = getattr(item, 'quantity', 0)
                                    unit_price = 0

                                    order_product = getattr(item, 'order_product', None)
                                    if order_product:
                                        unit_price = getattr(order_product, 'unit_price', 0)

                                    item_amount = quantity * unit_price
                                    order_return_total += item_amount
                                except Exception as e:
                                    current_app.logger.warning(f"计算退货单项目金额时出错: {str(e)}")

                            return_total += order_return_total

                    # 计算净结算金额
                    net_amount = delivery_total - return_total
                    statement_data['total_amount'] = str(net_amount)

                    data.append(statement_data)

                except Exception as e:
                    current_app.logger.error(f"处理对账单数据时出错: {str(e)}")

            # 构建分页信息
            pagination_data = {
                'total_count': pagination.total,
                'total_pages': pagination.pages,
                'current_page': pagination.page,
                'per_page': pagination.per_page
            }

            return {
                'code': 200,
                'message': '获取对账单列表成功',
                'list': data,
                'pagination': pagination_data
            }, 200

        except Exception as e:
            current_app.logger.error(f"获取对账单列表失败: {str(e)}")
            return make_response(error_response, f"获取对账单列表失败: {str(e)}")

    @api.doc('create_statement',
             body=statement_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 404: 'Customer Not Found',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建对账单"""
        try:
            data = request.get_json() or {}
            delivery_note_ids = data.pop('delivery_note_ids', [])

            # 生成对账单号
            statement_number = f"ST{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"

            # 计算对账单金额
            product_quantities = {}
            product_prices = {}
            delivery_notes = []
            order_ids = []

            for delivery_note_id in delivery_note_ids:
                delivery_note = DeliveryNote.query.get(delivery_note_id)
                if not delivery_note:
                    return make_response(error_response, f"发货单ID {delivery_note_id} 不存在", errors={"delivery_note_ids": "无效的发货单ID"})

                delivery_notes.append(delivery_note)
                if delivery_note.order_id:
                    order_ids.append(delivery_note.order_id)

                # 统计发货数量和单价
                for item in delivery_note.items:
                    product_id = None
                    unit_price = 0
                    quantity = getattr(item, 'quantity', 0)

                    order_product = getattr(item, 'order_product', None)
                    if order_product:
                        product_id = getattr(order_product, 'product_id', None)
                        unit_price = getattr(order_product, 'unit_price', 0)

                    if product_id and quantity > 0:
                        if product_id in product_quantities:
                            product_quantities[product_id] += quantity
                        else:
                            product_quantities[product_id] = quantity

                        if product_id not in product_prices:
                            product_prices[product_id] = unit_price

            # 处理退货单
            if order_ids:
                return_orders = ReturnOrder.query.filter(ReturnOrder.order_id.in_(order_ids)).all()
                for return_order in return_orders:
                    for item in getattr(return_order, 'items', []):
                        order_product = getattr(item, 'order_product', None)
                        if order_product:
                            product_id = getattr(order_product, 'product_id', None)
                            quantity = getattr(item, 'quantity', 0)

                            if product_id and product_id in product_quantities:
                                product_quantities[product_id] -= quantity

            # 计算净结算金额
            total_amount = 0
            for product_id, quantity in product_quantities.items():
                if quantity > 0 and product_id in product_prices:
                    unit_price = product_prices[product_id]
                    total_amount += quantity * unit_price

            # 构造完整的数据
            complete_data = {
                'statement_number': statement_number,
                'customer_id': data.get('customer_id'),
                'statement_date': data.get('statement_date'),
                'due_date': data.get('due_date'),
                'status': '待确认',
                'notes': data.get('notes'),
                'total_amount': str(total_amount),
                'delivery_note_ids': delivery_note_ids
            }

            # 验证数据
            statement_create_schema = StatementCreateSchema()
            validated_data = statement_create_schema.load(complete_data)

            # 创建对账单
            statement = Statement(
                statement_number=validated_data['statement_number'],
                customer_id=validated_data['customer_id'],
                statement_date=validated_data['statement_date'],
                due_date=validated_data.get('due_date'),
                status=validated_data['status'],
                notes=validated_data.get('notes'),
                total_amount=validated_data['total_amount'],
            )
            db.session.add(statement)
            db.session.flush()

            # 处理发货单关联
            for delivery_note_id in delivery_note_ids:
                statement_delivery_note = StatementDeliveryNote(
                    statement_id=statement.id,
                    delivery_note_id=delivery_note_id
                )
                db.session.add(statement_delivery_note)

            db.session.commit()

            # 返回创建的对账单数据
            return make_response(success_response, StatementSchema().dump(statement), "对账单创建成功", 201)

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建对账单失败: {str(e)}")
            return make_response(error_response, f"创建对账单失败: {str(e)}")


# 应收账款相关API
@api.route('/receivables')
class ReceivableList(Resource):
    @api.doc('get_receivables')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('view_type', '视图类型 (statement: 按对账单, customer: 按客户)', type='string', default='statement')
    @api.param('status', '状态筛选')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取应收账款列表（基于对账单）"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            customer_id = request.args.get('customer_id', type=int)
            view_type = request.args.get('view_type', 'statement')
            status = request.args.get('status', '')
            start_date = request.args.get('start_date', '')
            end_date = request.args.get('end_date', '')

            if view_type == 'statement':
                # 按对账单视图
                query = db.session.query(
                    StatementReceivable,
                    Statement.statement_number,
                    Statement.statement_date,
                    Statement.status.label('statement_status'),
                    Customer.name.label('customer_name')
                ).join(
                    Statement, StatementReceivable.statement_id == Statement.id
                ).join(
                    Customer, StatementReceivable.customer_id == Customer.id
                )

                # 应用过滤条件
                if customer_id:
                    query = query.filter(StatementReceivable.customer_id == customer_id)
                if status:
                    # 根据应收账款状态筛选
                    if status == '未支付':
                        query = query.filter(StatementReceivable.paid_amount == 0)
                    elif status == '部分支付':
                        query = query.filter(
                            StatementReceivable.paid_amount > 0,
                            StatementReceivable.paid_amount < StatementReceivable.amount
                        )
                    elif status == '已支付':
                        query = query.filter(StatementReceivable.paid_amount >= StatementReceivable.amount)
                    elif status == '逾期':
                        query = query.filter(
                            StatementReceivable.due_date < date.today(),
                            StatementReceivable.paid_amount < StatementReceivable.amount
                        )

                if start_date:
                    try:
                        start = datetime.strptime(start_date, '%Y-%m-%d').date()
                        query = query.filter(Statement.statement_date >= start)
                    except ValueError:
                        pass

                if end_date:
                    try:
                        end = datetime.strptime(end_date, '%Y-%m-%d').date()
                        query = query.filter(Statement.statement_date <= end)
                    except ValueError:
                        pass

                # 排序
                query = query.order_by(Statement.statement_date.desc())

                # 分页
                pagination = query.paginate(
                    page=page,
                    per_page=per_page,
                    error_out=False
                )

                # 构建响应数据
                items = []
                for receivable, statement_number, statement_date, statement_status, customer_name in pagination.items:
                    outstanding_amount = float(receivable.amount) - float(receivable.paid_amount or 0)

                    # 计算状态
                    if receivable.paid_amount == 0:
                        status = '未支付'
                    elif receivable.paid_amount >= receivable.amount:
                        status = '已支付'
                    else:
                        status = '部分支付'

                    # 检查是否逾期
                    if receivable.due_date < date.today() and outstanding_amount > 0:
                        status = '逾期'

                    items.append({
                        'id': receivable.id,
                        'statement_id': receivable.statement_id,
                        'statement_number': statement_number,
                        'statement_date': statement_date.isoformat() if statement_date else None,
                        'statement_status': statement_status,
                        'customer_id': receivable.customer_id,
                        'customer_name': customer_name,
                        'amount': float(receivable.amount),
                        'paid_amount': float(receivable.paid_amount or 0),
                        'outstanding_amount': outstanding_amount,
                        'due_date': receivable.due_date.isoformat() if receivable.due_date else None,
                        'status': status,
                        'last_payment_date': receivable.last_payment_date.isoformat() if receivable.last_payment_date else None,
                        'created_at': receivable.created_at.isoformat() if receivable.created_at else None
                    })

                return success_response({
                    'items': items,
                    'pagination': {
                        'page': pagination.page,
                        'per_page': pagination.per_page,
                        'total': pagination.total,
                        'pages': pagination.pages,
                        'has_prev': pagination.has_prev,
                        'has_next': pagination.has_next
                    }
                }, '获取应收账款列表成功')

            else:
                # 按客户视图
                query = db.session.query(
                    Customer.id.label('customer_id'),
                    Customer.name.label('customer_name'),
                    func.sum(StatementReceivable.amount).label('total_amount'),
                    func.sum(StatementReceivable.paid_amount).label('paid_amount'),
                    func.count(StatementReceivable.id).label('statement_count'),
                    func.max(StatementReceivable.last_payment_date).label('last_payment_date')
                ).join(
                    StatementReceivable, Customer.id == StatementReceivable.customer_id
                ).group_by(Customer.id, Customer.name)

                # 应用过滤条件
                if customer_id:
                    query = query.filter(Customer.id == customer_id)

                # 排序
                query = query.order_by(func.sum(StatementReceivable.amount).desc())

                # 分页
                pagination = query.paginate(
                    page=page,
                    per_page=per_page,
                    error_out=False
                )

                # 构建响应数据
                items = []
                for row in pagination.items:
                    total_amount = float(row.total_amount or 0)
                    paid_amount = float(row.paid_amount or 0)
                    outstanding_amount = total_amount - paid_amount

                    # 计算逾期金额
                    overdue_query = db.session.query(
                        func.sum(StatementReceivable.amount - StatementReceivable.paid_amount)
                    ).filter(
                        StatementReceivable.customer_id == row.customer_id,
                        StatementReceivable.due_date < date.today(),
                        StatementReceivable.paid_amount < StatementReceivable.amount
                    )
                    overdue_amount = float(overdue_query.scalar() or 0)

                    items.append({
                        'customer_id': row.customer_id,
                        'customer_name': row.customer_name,
                        'total_amount': total_amount,
                        'paid_amount': paid_amount,
                        'outstanding_amount': outstanding_amount,
                        'overdue_amount': overdue_amount,
                        'statement_count': row.statement_count,
                        'last_payment_date': row.last_payment_date.isoformat() if row.last_payment_date else None
                    })

                return success_response({
                    'items': items,
                    'pagination': {
                        'page': pagination.page,
                        'per_page': pagination.per_page,
                        'total': pagination.total,
                        'pages': pagination.pages,
                        'has_prev': pagination.has_prev,
                        'has_next': pagination.has_next
                    }
                }, '获取应收账款列表成功')

        except Exception as e:
            current_app.logger.error(f"获取应收账款列表失败: {str(e)}")
            return error_response(f"获取应收账款列表失败: {str(e)}", code=500)


@api.route('/receivables/stats')
class ReceivableStats(Resource):
    @api.doc('get_receivable_stats')
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取应收账款统计信息"""
        try:
            customer_id = request.args.get('customer_id', type=int)
            start_date = request.args.get('start_date', '')
            end_date = request.args.get('end_date', '')

            # 基础查询
            query = StatementReceivable.query

            # 应用过滤条件
            if customer_id:
                query = query.filter(StatementReceivable.customer_id == customer_id)

            if start_date or end_date:
                query = query.join(Statement)
                if start_date:
                    try:
                        start = datetime.strptime(start_date, '%Y-%m-%d').date()
                        query = query.filter(Statement.statement_date >= start)
                    except ValueError:
                        pass
                if end_date:
                    try:
                        end = datetime.strptime(end_date, '%Y-%m-%d').date()
                        query = query.filter(Statement.statement_date <= end)
                    except ValueError:
                        pass

            # 计算统计数据
            receivables = query.all()

            total_receivable = sum(float(r.amount) for r in receivables)
            total_paid = sum(float(r.paid_amount or 0) for r in receivables)
            total_outstanding = total_receivable - total_paid

            # 逾期金额
            overdue_amount = sum(
                float(r.amount) - float(r.paid_amount or 0)
                for r in receivables
                if r.due_date < date.today() and float(r.amount) > float(r.paid_amount or 0)
            )

            # 逾期比例
            overdue_percentage = (overdue_amount / total_outstanding * 100) if total_outstanding > 0 else 0

            # 对账单数量
            statement_count = len(set(r.statement_id for r in receivables))

            # 客户数量
            customer_count = len(set(r.customer_id for r in receivables))

            statistics = {
                'total_receivable': total_receivable,
                'total_paid': total_paid,
                'total_outstanding': total_outstanding,
                'overdue_amount': overdue_amount,
                'overdue_percentage': round(overdue_percentage, 2),
                'statement_count': statement_count,
                'customer_count': customer_count,
                'total_receivable_growth': 0,  # 暂时设为0，后续可以实现同比增长计算
            }

            return success_response(statistics, '获取应收账款统计信息成功')

        except Exception as e:
            current_app.logger.error(f"获取应收账款统计信息失败: {str(e)}")
            return error_response(f"获取应收账款统计信息失败: {str(e)}", code=500)
