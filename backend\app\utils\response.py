"""
统一API响应格式工具
提供标准化的成功、错误和分页响应格式
"""
from flask import jsonify
from typing import Any, Dict, List, Optional, Union
import math


def success_response(
    data: Any = None,
    message: str = '操作成功',
    code: int = 200
) -> tuple:
    """
    统一的成功响应格式

    Args:
        data: 响应数据
        message: 响应消息
        code: HTTP状态码

    Returns:
        tuple: (响应字典, HTTP状态码)
    """
    response_data = {
        'code': code,
        'message': message,
        'data': data,
        'success': True
    }
    return response_data, code


def error_response(
    message: str = '操作失败',
    errors: Optional[Union[Dict, List, str]] = None,
    code: int = 400,
    data: Any = None
) -> tuple:
    """
    统一的错误响应格式

    Args:
        message: 错误消息
        errors: 详细错误信息
        code: HTTP状态码
        data: 额外数据（可选）

    Returns:
        tuple: (响应字典, HTTP状态码)
    """
    response_data = {
        'code': code,
        'message': message,
        'success': False
    }

    if errors is not None:
        response_data['errors'] = errors

    if data is not None:
        response_data['data'] = data

    return response_data, code


def paginated_response(
    data: List[Any],
    total: int,
    page: int,
    per_page: int,
    message: str = '查询成功',
    code: int = 200
) -> tuple:
    """
    统一的分页响应格式

    Args:
        data: 分页数据列表
        total: 总记录数
        page: 当前页码
        per_page: 每页记录数
        message: 响应消息
        code: HTTP状态码

    Returns:
        tuple: (响应字典, HTTP状态码)
    """
    # 计算分页信息
    total_pages = math.ceil(total / per_page) if per_page > 0 else 0
    has_prev = page > 1
    has_next = page < total_pages

    response_data = {
        'code': code,
        'message': message,
        'data': data,
        'success': True,
        'pagination': {
            'total': total,
            'page': page,
            'per_page': per_page,
            'total_pages': total_pages,
            'has_prev': has_prev,
            'has_next': has_next,
            'prev_page': page - 1 if has_prev else None,
            'next_page': page + 1 if has_next else None
        }
    }
    return response_data, code


def validation_error_response(
    errors: Dict[str, List[str]],
    message: str = '数据验证失败'
) -> tuple:
    """
    数据验证错误响应格式

    Args:
        errors: 字段验证错误字典
        message: 错误消息

    Returns:
        tuple: (响应字典, HTTP状态码)
    """
    return error_response(
        message=message,
        errors=errors,
        code=422
    )


def not_found_response(
    message: str = '资源不存在',
    resource: str = None
) -> tuple:
    """
    404错误响应格式
    
    Args:
        message: 错误消息
        resource: 资源名称
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    if resource:
        message = f'{resource}不存在'
        
    return error_response(
        message=message,
        code=404
    )


def unauthorized_response(
    message: str = '未授权访问'
) -> tuple:
    """
    401错误响应格式
    
    Args:
        message: 错误消息
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    return error_response(
        message=message,
        code=401
    )


def forbidden_response(
    message: str = '禁止访问'
) -> tuple:
    """
    403错误响应格式
    
    Args:
        message: 错误消息
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    return error_response(
        message=message,
        code=403
    )


def server_error_response(
    message: str = '服务器内部错误'
) -> tuple:
    """
    500错误响应格式
    
    Args:
        message: 错误消息
        
    Returns:
        tuple: (响应JSON, HTTP状态码)
    """
    return error_response(
        message=message,
        code=500
    )
