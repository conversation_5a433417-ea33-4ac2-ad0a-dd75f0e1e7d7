"""
API路径重写中间件
将原项目的路径格式重写为新的API路径格式，确保前端无缝迁移
"""
from flask import request, redirect, url_for
import re


class PathRewriteMiddleware:
    """API路径重写中间件"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化中间件"""
        app.before_request(self.rewrite_path)
    
    def rewrite_path(self):
        """重写请求路径"""
        path = request.path
        
        # 如果已经是新格式的路径，直接返回
        if path.startswith('/api/v1/'):
            return
        
        # 如果是静态文件或其他特殊路径，不处理
        if path.startswith(('/static/', '/docs/', '/swagger.json', '/favicon.ico')):
            return
        
        # 定义路径映射规则
        rewrite_rules = [
            # 客户管理
            (r'^/customers(/.*)?$', r'/api/v1/customers\1'),
            
            # 产品管理
            (r'^/products(/.*)?$', r'/api/v1/products\1'),
            (r'^/product-categories(/.*)?$', r'/api/v1/products/categories\1'),
            (r'^/brands(/.*)?$', r'/api/v1/products/brands\1'),
            
            # 报价管理
            (r'^/quotations(/.*)?$', r'/api/v1/quotations\1'),
            (r'^/quotation-requests(/.*)?$', r'/api/v1/quotations/requests\1'),
            (r'^/quotation-templates(/.*)?$', r'/api/v1/quotations/templates\1'),
            (r'^/quotation-items(/.*)?$', r'/api/v1/quotations/items\1'),
            
            # 订单管理
            (r'^/orders(/.*)?$', r'/api/v1/orders\1'),
            
            # 发货单管理
            (r'^/delivery-notes(/.*)?$', r'/api/v1/delivery-notes\1'),
            
            # 退货管理
            (r'^/returns(/.*)?$', r'/api/v1/returns\1'),
            
            # 对账单管理
            (r'^/statements(/.*)?$', r'/api/v1/statements\1'),
            
            # 财务管理
            (r'^/payment-records(/.*)?$', r'/api/v1/finance/payment-records\1'),
            (r'^/refund-records(/.*)?$', r'/api/v1/finance/refund-records\1'),
            (r'^/receivables(/.*)?$', r'/api/v1/finance/receivables\1'),
            (r'^/financial-reports(/.*)?$', r'/api/v1/finance/reports\1'),
            
            # 工作台
            (r'^/dashboard(/.*)?$', r'/api/v1/dashboard\1'),
            
            # 系统管理
            (r'^/system(/.*)?$', r'/api/v1/system\1'),
            
            # 文件上传
            (r'^/uploads(/.*)?$', r'/api/v1/uploads\1'),
            
            # 错误日志
            (r'^/error-logs(/.*)?$', r'/api/v1/error-logs\1'),

            # 订单导出和打印
            (r'^/order-export(/.*)?$', r'/api/v1/order-export\1'),
            (r'^/order-print(/.*)?$', r'/api/v1/order-print\1'),

            # 产品图片
            (r'^/product-images(/.*)?$', r'/api/v1/product-images\1'),
        ]
        
        # 应用重写规则
        for pattern, replacement in rewrite_rules:
            if re.match(pattern, path):
                new_path = re.sub(pattern, replacement, path)
                
                # 保持查询参数
                query_string = request.query_string.decode('utf-8')
                if query_string:
                    new_path += '?' + query_string
                
                # 重定向到新路径
                return redirect(new_path, code=301)
        
        # 如果没有匹配的规则，返回None（继续正常处理）
        return None


def register_path_rewrite_middleware(app):
    """注册路径重写中间件"""
    middleware = PathRewriteMiddleware()
    middleware.init_app(app)
    return middleware
