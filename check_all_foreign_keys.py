#!/usr/bin/env python3
"""
检查所有引用customers表的外键约束
"""
import sqlite3
import os

def check_all_foreign_keys():
    """检查所有外键约束"""
    db_path = r'D:\code\EMB-new\backend\instance\project.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查所有引用customers表的外键约束")
        print("=" * 60)
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # 检查每个表的外键约束
        tables_with_customer_fk = []
        
        for table in tables:
            try:
                cursor.execute(f"PRAGMA foreign_key_list({table})")
                foreign_keys = cursor.fetchall()
                
                for fk in foreign_keys:
                    if fk[2] == 'customers':  # 引用customers表
                        tables_with_customer_fk.append({
                            'table': table,
                            'column': fk[3],
                            'ref_table': fk[2],
                            'ref_column': fk[4],
                            'on_delete': fk[6],
                            'on_update': fk[5]
                        })
                        print(f"📋 {table}.{fk[3]} -> {fk[2]}.{fk[4]} (ON DELETE: {fk[6]})")
                        
            except Exception as e:
                print(f"❌ 检查表 {table} 失败: {str(e)}")
        
        print(f"\n📊 总共找到 {len(tables_with_customer_fk)} 个引用customers表的外键")
        
        # 检查客户ID 13的具体关联数据
        customer_id = 13
        print(f"\n🔍 检查客户ID {customer_id} 的所有关联数据:")
        
        for fk_info in tables_with_customer_fk:
            table = fk_info['table']
            column = fk_info['column']
            
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {column} = ?", (customer_id,))
                count = cursor.fetchone()[0]
                print(f"  {table}: {count} 条记录")
                
                if count > 0:
                    # 显示具体记录
                    cursor.execute(f"SELECT * FROM {table} WHERE {column} = ? LIMIT 3", (customer_id,))
                    records = cursor.fetchall()
                    for record in records:
                        print(f"    - {record}")
                        
            except Exception as e:
                print(f"    ❌ 检查失败: {str(e)}")
        
        # 检查是否有ON DELETE NO ACTION的约束
        print(f"\n⚠️  需要修复的外键约束 (ON DELETE: NO ACTION):")
        no_action_fks = [fk for fk in tables_with_customer_fk if fk['on_delete'] == 'NO ACTION']
        
        for fk in no_action_fks:
            print(f"  {fk['table']}.{fk['column']} -> {fk['ref_table']}.{fk['ref_column']}")
        
        if no_action_fks:
            print(f"\n💡 这些表的外键约束阻止了客户删除，需要修复为 CASCADE 或手动删除关联数据")
        else:
            print(f"\n✅ 所有外键约束都已正确配置")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查过程出错: {str(e)}")

if __name__ == "__main__":
    check_all_foreign_keys()
