"""添加匹配备注字段

Revision ID: add_match_notes_field
Revises: 
Create Date: 2024-12-26

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_match_notes_field'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """添加match_notes字段到quotation_request_items表"""
    # 添加match_notes字段
    op.add_column('quotation_request_items', 
                  sa.Column('match_notes', sa.Text(), nullable=True, comment='匹配备注信息'))


def downgrade():
    """移除match_notes字段"""
    op.drop_column('quotation_request_items', 'match_notes')
