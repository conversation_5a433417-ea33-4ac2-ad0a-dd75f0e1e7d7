"""
收款管理API
包括客户余额管理、余额交易记录、对账单收款等功能
"""
import json
import os
import uuid
from datetime import datetime, date
from decimal import Decimal
from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from sqlalchemy import desc, asc
from sqlalchemy.exc import IntegrityError
from werkzeug.utils import secure_filename

from app import db
from app.models import Customer, CustomerBalance, BalanceTransaction, StatementPayment, Statement
from app.utils.response import success_response, error_response, paginated_response
from app.utils.exceptions import ValidationError, NotFoundError, BusinessLogicError
from app.api.response_models import success_response_model, error_response_model, paginated_response_model

# 创建API命名空间
api = Namespace('payments', description='收款管理API')

# 定义请求和响应模型
customer_balance_model = api.model('CustomerBalance', {
    'id': fields.Integer(description='余额记录ID'),
    'customer_id': fields.Integer(description='客户ID'),
    'customer_name': fields.String(description='客户名称'),
    'balance': fields.Float(description='可用余额'),
    'frozen_balance': fields.Float(description='冻结余额'),
    'created_at': fields.DateTime(description='创建时间'),
    'updated_at': fields.DateTime(description='更新时间')
})

balance_transaction_model = api.model('BalanceTransaction', {
    'id': fields.Integer(description='交易记录ID'),
    'customer_id': fields.Integer(description='客户ID'),
    'customer_name': fields.String(description='客户名称'),
    'transaction_type': fields.String(description='交易类型'),
    'amount': fields.Float(description='交易金额'),
    'balance_before': fields.Float(description='交易前余额'),
    'balance_after': fields.Float(description='交易后余额'),
    'reference_type': fields.String(description='关联类型'),
    'reference_id': fields.Integer(description='关联记录ID'),
    'description': fields.String(description='交易描述'),
    'created_at': fields.DateTime(description='创建时间')
})

statement_payment_model = api.model('StatementPayment', {
    'id': fields.Integer(description='收款记录ID'),
    'statement_id': fields.Integer(description='对账单ID'),
    'statement_number': fields.String(description='对账单号'),
    'payment_date': fields.Date(description='收款日期'),
    'amount': fields.Float(description='收款金额'),
    'payment_method': fields.String(description='支付方式'),
    'payment_source': fields.String(description='付款来源'),
    'reference_number': fields.String(description='交易流水号'),
    'bank_account': fields.String(description='收款账户'),
    'notes': fields.String(description='备注'),
    'status': fields.String(description='状态'),
    'created_by': fields.String(description='创建人'),
    'created_at': fields.DateTime(description='创建时间')
})

# 请求模型
add_balance_request = api.model('AddBalanceRequest', {
    'customer_id': fields.Integer(required=True, description='客户ID'),
    'amount': fields.Float(required=True, description='充值金额', min=0.01),
    'description': fields.String(description='充值描述'),
    'reference_type': fields.String(description='关联类型'),
    'reference_id': fields.Integer(description='关联记录ID')
})

direct_payment_request = api.model('DirectPaymentRequest', {
    'statement_id': fields.Integer(required=True, description='对账单ID'),
    'amount': fields.Float(required=True, description='收款金额', min=0.01),
    'payment_method': fields.String(required=True, description='支付方式'),
    'payment_date': fields.Date(description='收款日期'),
    'reference_number': fields.String(description='交易流水号'),
    'bank_account': fields.String(description='收款账户'),
    'notes': fields.String(description='备注'),
    'created_by': fields.String(description='创建人')
})

balance_payment_request = api.model('BalancePaymentRequest', {
    'statement_id': fields.Integer(required=True, description='对账单ID'),
    'customer_id': fields.Integer(required=True, description='客户ID'),
    'amount': fields.Float(required=True, description='支付金额', min=0.01),
    'payment_date': fields.Date(description='支付日期'),
    'notes': fields.String(description='备注'),
    'created_by': fields.String(description='创建人')
})


@api.route('/customer-balances')
class CustomerBalanceListAPI(Resource):
    @api.doc('获取客户余额列表')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('customer_id', '客户ID筛选', type='integer')
    @api.param('search', '客户名称搜索')
    @api.marshal_with(paginated_response_model)
    def get(self):
        """获取客户余额列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 20, type=int), 100)
            customer_id = request.args.get('customer_id', type=int)
            search = request.args.get('search', '').strip()
            
            # 构建查询
            query = db.session.query(CustomerBalance, Customer).join(Customer)
            
            # 筛选条件
            if customer_id:
                query = query.filter(CustomerBalance.customer_id == customer_id)
            
            if search:
                query = query.filter(Customer.name.contains(search))
            
            # 排序
            query = query.order_by(desc(CustomerBalance.updated_at))
            
            # 分页
            pagination = query.paginate(
                page=page, per_page=per_page, error_out=False
            )
            
            # 格式化数据
            items = []
            for balance, customer in pagination.items:
                items.append({
                    'id': balance.id,
                    'customer_id': balance.customer_id,
                    'customer_name': customer.name,
                    'balance': float(balance.balance),
                    'frozen_balance': float(balance.frozen_balance),
                    'created_at': balance.created_at.isoformat(),
                    'updated_at': balance.updated_at.isoformat()
                })
            
            return paginated_response(
                data=items,
                total=pagination.total,
                page=pagination.page,
                per_page=pagination.per_page,
                message='获取客户余额列表成功'
            )
            
        except Exception as e:
            return error_response(f'获取客户余额列表失败: {str(e)}', code=500)


@api.route('/customer-balances/<int:customer_id>')
class CustomerBalanceAPI(Resource):
    @api.doc('获取客户余额详情')
    @api.marshal_with(success_response_model)
    def get(self, customer_id):
        """获取指定客户的余额详情"""
        try:
            balance = CustomerBalance.query.filter_by(customer_id=customer_id).first()
            if not balance:
                raise NotFoundError('客户余额记录不存在')
            
            customer = Customer.query.get(customer_id)
            if not customer:
                raise NotFoundError('客户不存在')
            
            data = {
                'id': balance.id,
                'customer_id': balance.customer_id,
                'customer_name': customer.name,
                'balance': float(balance.balance),
                'frozen_balance': float(balance.frozen_balance),
                'created_at': balance.created_at.isoformat(),
                'updated_at': balance.updated_at.isoformat()
            }
            
            return success_response(data=data, message='获取客户余额详情成功')
            
        except NotFoundError as e:
            return error_response(str(e), code=404)
        except Exception as e:
            return error_response(f'获取客户余额详情失败: {str(e)}', code=500)


@api.route('/add-balance')
class AddBalanceAPI(Resource):
    @api.doc('客户余额充值')
    @api.expect(add_balance_request)
    @api.marshal_with(success_response_model)
    def post(self):
        """为客户余额充值"""
        try:
            data = request.get_json()
            
            # 验证必填字段
            if not data.get('customer_id'):
                raise ValidationError('客户ID不能为空')
            if not data.get('amount') or data.get('amount') <= 0:
                raise ValidationError('充值金额必须大于0')
            
            customer_id = data['customer_id']
            amount = Decimal(str(data['amount']))
            description = data.get('description', '余额充值')
            reference_type = data.get('reference_type')
            reference_id = data.get('reference_id')
            
            # 检查客户是否存在
            customer = Customer.query.get(customer_id)
            if not customer:
                raise NotFoundError('客户不存在')
            
            # 获取客户余额记录
            balance = CustomerBalance.query.filter_by(customer_id=customer_id).first()
            if not balance:
                raise NotFoundError('客户余额记录不存在')
            
            # 执行充值
            transaction = balance.add_balance(
                amount=amount,
                description=description,
                reference_type=reference_type,
                reference_id=reference_id
            )
            
            db.session.commit()
            
            return success_response(
                data={
                    'transaction_id': transaction.id,
                    'new_balance': float(balance.balance),
                    'amount': float(amount)
                },
                message='余额充值成功'
            )
            
        except ValidationError as e:
            return error_response(str(e), code=422)
        except NotFoundError as e:
            return error_response(str(e), code=404)
        except Exception as e:
            db.session.rollback()
            return error_response(f'余额充值失败: {str(e)}', code=500)


@api.route('/balance-transactions')
class BalanceTransactionListAPI(Resource):
    @api.doc('获取余额交易记录列表')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('customer_id', '客户ID筛选', type='integer')
    @api.param('transaction_type', '交易类型筛选')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    @api.marshal_with(paginated_response_model)
    def get(self):
        """获取余额交易记录列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 20, type=int), 100)
            customer_id = request.args.get('customer_id', type=int)
            transaction_type = request.args.get('transaction_type', '').strip()
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')

            # 构建查询
            query = db.session.query(BalanceTransaction, Customer).join(Customer)

            # 筛选条件
            if customer_id:
                query = query.filter(BalanceTransaction.customer_id == customer_id)

            if transaction_type:
                query = query.filter(BalanceTransaction.transaction_type == transaction_type)

            if start_date:
                try:
                    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(BalanceTransaction.created_at >= start_dt)
                except ValueError:
                    raise ValidationError('开始日期格式错误，请使用 YYYY-MM-DD 格式')

            if end_date:
                try:
                    end_dt = datetime.strptime(end_date, '%Y-%m-%d')
                    # 结束日期包含当天，所以加一天
                    end_dt = end_dt.replace(hour=23, minute=59, second=59)
                    query = query.filter(BalanceTransaction.created_at <= end_dt)
                except ValueError:
                    raise ValidationError('结束日期格式错误，请使用 YYYY-MM-DD 格式')

            # 排序
            query = query.order_by(desc(BalanceTransaction.created_at))

            # 分页
            pagination = query.paginate(
                page=page, per_page=per_page, error_out=False
            )

            # 格式化数据
            items = []
            for transaction, customer in pagination.items:
                items.append({
                    'id': transaction.id,
                    'customer_id': transaction.customer_id,
                    'customer_name': customer.name,
                    'transaction_type': transaction.transaction_type,
                    'amount': float(transaction.amount),
                    'balance_before': float(transaction.balance_before),
                    'balance_after': float(transaction.balance_after),
                    'reference_type': transaction.reference_type,
                    'reference_id': transaction.reference_id,
                    'description': transaction.description,
                    'created_at': transaction.created_at.isoformat()
                })

            return paginated_response(
                data=items,
                total=pagination.total,
                page=pagination.page,
                per_page=pagination.per_page,
                message='获取余额交易记录列表成功'
            )

        except ValidationError as e:
            return error_response(str(e), code=422)
        except Exception as e:
            return error_response(f'获取余额交易记录列表失败: {str(e)}', code=500)


@api.route('/statement-payments')
class StatementPaymentListAPI(Resource):
    @api.doc('获取对账单收款记录列表')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('statement_id', '对账单ID筛选', type='integer')
    @api.param('payment_source', '付款来源筛选')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    @api.marshal_with(paginated_response_model)
    def get(self):
        """获取对账单收款记录列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 20, type=int), 100)
            statement_id = request.args.get('statement_id', type=int)
            payment_source = request.args.get('payment_source', '').strip()
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')

            # 构建查询，包含客户信息
            query = db.session.query(StatementPayment, Statement, Customer).join(
                Statement, StatementPayment.statement_id == Statement.id
            ).join(
                Customer, Statement.customer_id == Customer.id
            )

            # 筛选条件
            if statement_id:
                query = query.filter(StatementPayment.statement_id == statement_id)

            if payment_source:
                query = query.filter(StatementPayment.payment_source == payment_source)

            # 添加对账单号筛选
            statement_number = request.args.get('statement_number', '').strip()
            if statement_number:
                query = query.filter(Statement.statement_number.like(f'%{statement_number}%'))

            # 添加客户筛选
            customer_id = request.args.get('customer_id', type=int)
            if customer_id:
                query = query.filter(Statement.customer_id == customer_id)

            # 添加支付方式筛选
            payment_method = request.args.get('payment_method', '').strip()
            if payment_method:
                query = query.filter(StatementPayment.payment_method == payment_method)

            if start_date:
                try:
                    start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
                    query = query.filter(StatementPayment.payment_date >= start_dt)
                except ValueError:
                    raise ValidationError('开始日期格式错误，请使用 YYYY-MM-DD 格式')

            if end_date:
                try:
                    end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
                    query = query.filter(StatementPayment.payment_date <= end_dt)
                except ValueError:
                    raise ValidationError('结束日期格式错误，请使用 YYYY-MM-DD 格式')

            # 排序
            query = query.order_by(desc(StatementPayment.payment_date), desc(StatementPayment.created_at))

            # 分页
            pagination = query.paginate(
                page=page, per_page=per_page, error_out=False
            )

            # 格式化数据
            items = []
            for payment, statement, customer in pagination.items:
                # 解析voucher_files JSON
                voucher_files = []
                if payment.voucher_files:
                    try:
                        voucher_files = json.loads(payment.voucher_files)
                    except (json.JSONDecodeError, TypeError):
                        voucher_files = []

                items.append({
                    'id': payment.id,
                    'statement_id': payment.statement_id,
                    'statement_number': statement.statement_number,
                    'customer_id': customer.id,
                    'customer_name': customer.name,
                    'payment_date': payment.payment_date.isoformat(),
                    'amount': float(payment.amount),
                    'payment_method': payment.payment_method,
                    'payment_source': payment.payment_source,
                    'reference_number': payment.reference_number,
                    'bank_account': payment.bank_account,
                    'notes': payment.notes,
                    'voucher_files': voucher_files,
                    'created_at': payment.created_at.isoformat()
                })

            return paginated_response(
                data=items,
                total=pagination.total,
                page=pagination.page,
                per_page=pagination.per_page,
                message='获取对账单收款记录列表成功'
            )

        except ValidationError as e:
            return error_response(str(e), code=422)
        except Exception as e:
            return error_response(f'获取对账单收款记录列表失败: {str(e)}', code=500)


@api.route('/direct-payment')
class DirectPaymentAPI(Resource):
    @api.doc('创建直接收款记录')
    @api.expect(direct_payment_request)
    @api.marshal_with(success_response_model)
    def post(self):
        """创建直接收款记录"""
        try:
            data = request.get_json()

            # 验证必填字段
            if not data.get('statement_id'):
                raise ValidationError('对账单ID不能为空')
            if not data.get('amount') or data.get('amount') <= 0:
                raise ValidationError('收款金额必须大于0')
            if not data.get('payment_method'):
                raise ValidationError('支付方式不能为空')

            statement_id = data['statement_id']
            amount = Decimal(str(data['amount']))
            payment_method = data['payment_method']
            payment_date = data.get('payment_date')
            reference_number = data.get('reference_number')
            bank_account = data.get('bank_account')
            notes = data.get('notes')
            created_by = data.get('created_by')
            voucher_files = data.get('voucher_files', [])

            # 解析支付日期
            if payment_date:
                try:
                    payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()
                except ValueError:
                    raise ValidationError('支付日期格式错误，请使用 YYYY-MM-DD 格式')

            # 检查对账单是否存在
            statement = Statement.query.get(statement_id)
            if not statement:
                raise NotFoundError('对账单不存在')

            # 处理收款凭据文件（如果有的话）
            voucher_files_json = None
            if voucher_files and isinstance(voucher_files, list):
                voucher_files_json = json.dumps(voucher_files)

            # 创建直接收款记录
            payment = StatementPayment.create_direct_payment(
                statement_id=statement_id,
                amount=amount,
                payment_method=payment_method,
                payment_date=payment_date,
                reference_number=reference_number,
                bank_account=bank_account,
                notes=notes,
                voucher_files=voucher_files_json,
                created_by=created_by
            )

            db.session.add(payment)

            # 更新对账单付款状态
            statement.add_payment(amount)

            db.session.commit()

            return success_response(
                data={
                    'payment_id': payment.id,
                    'statement_status': statement.status,
                    'statement_paid_amount': float(statement.paid_amount),
                    'amount': float(amount)
                },
                message='直接收款记录创建成功'
            )

        except ValidationError as e:
            return error_response(str(e), code=422)
        except NotFoundError as e:
            return error_response(str(e), code=404)
        except Exception as e:
            db.session.rollback()
            return error_response(f'创建直接收款记录失败: {str(e)}', code=500)


@api.route('/direct-payment-with-files')
class DirectPaymentWithFilesAPI(Resource):
    @api.doc('创建直接收款记录（支持文件上传）')
    def post(self):
        """创建直接收款记录（支持文件上传）"""
        try:
            # 获取表单数据
            statement_id = request.form.get('statement_id')
            amount = request.form.get('amount')
            payment_method = request.form.get('payment_method')
            payment_source = request.form.get('payment_source', 'direct')
            reference_number = request.form.get('reference_number')
            bank_account = request.form.get('bank_account')
            payment_date = request.form.get('payment_date')
            notes = request.form.get('notes')
            created_by = request.form.get('created_by')
            voucher_files_count = int(request.form.get('voucher_files_count', 0))

            # 验证必填字段
            if not statement_id:
                raise ValidationError('对账单ID不能为空')
            if not amount or float(amount) <= 0:
                raise ValidationError('收款金额必须大于0')
            if not payment_method:
                raise ValidationError('支付方式不能为空')

            statement_id = int(statement_id)
            amount = Decimal(str(amount))

            # 解析支付日期
            if payment_date:
                try:
                    payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()
                except ValueError:
                    raise ValidationError('支付日期格式错误，请使用 YYYY-MM-DD 格式')

            # 检查对账单是否存在
            statement = Statement.query.get(statement_id)
            if not statement:
                raise NotFoundError('对账单不存在')

            # 处理上传的文件
            voucher_files = []
            upload_folder = os.path.join(current_app.root_path, '..', 'uploads', 'vouchers')
            os.makedirs(upload_folder, exist_ok=True)

            for i in range(voucher_files_count):
                file_key = f'voucher_file_{i}'
                if file_key in request.files:
                    file = request.files[file_key]
                    if file and file.filename:
                        # 生成安全的文件名
                        filename = secure_filename(file.filename)
                        unique_filename = f"{uuid.uuid4().hex}_{filename}"
                        file_path = os.path.join(upload_folder, unique_filename)

                        # 保存文件
                        file.save(file_path)

                        # 构建文件信息
                        file_info = {
                            'filename': unique_filename,
                            'original_filename': filename,
                            'url': f"/api/v1/uploads/vouchers/{unique_filename}",
                            'size': os.path.getsize(file_path),
                            'type': file.content_type or 'application/octet-stream'
                        }
                        voucher_files.append(file_info)

            # 将文件信息转换为JSON字符串
            voucher_files_json = json.dumps(voucher_files) if voucher_files else None

            # 创建直接收款记录
            payment = StatementPayment.create_direct_payment(
                statement_id=statement_id,
                amount=amount,
                payment_method=payment_method,
                payment_date=payment_date,
                reference_number=reference_number,
                bank_account=bank_account,
                notes=notes,
                voucher_files=voucher_files_json,
                created_by=created_by
            )

            db.session.add(payment)

            # 更新对账单付款状态
            statement.add_payment(amount)

            db.session.commit()

            return success_response(
                data={
                    'payment_id': payment.id,
                    'statement_status': statement.status,
                    'statement_paid_amount': float(statement.paid_amount),
                    'amount': float(amount),
                    'voucher_files': voucher_files
                },
                message='直接收款记录创建成功'
            )

        except ValidationError as e:
            return error_response(str(e), code=422)
        except NotFoundError as e:
            return error_response(str(e), code=404)
        except Exception as e:
            db.session.rollback()
            return error_response(f'创建直接收款记录失败: {str(e)}', code=500)


@api.route('/balance-payment')
class BalancePaymentAPI(Resource):
    @api.doc('创建余额支付记录')
    @api.expect(balance_payment_request)
    @api.marshal_with(success_response_model)
    def post(self):
        """创建余额支付记录"""
        try:
            data = request.get_json()

            # 验证必填字段
            if not data.get('statement_id'):
                raise ValidationError('对账单ID不能为空')
            if not data.get('customer_id'):
                raise ValidationError('客户ID不能为空')
            if not data.get('amount') or data.get('amount') <= 0:
                raise ValidationError('支付金额必须大于0')

            statement_id = data['statement_id']
            customer_id = data['customer_id']
            amount = Decimal(str(data['amount']))
            payment_date = data.get('payment_date')
            notes = data.get('notes')
            created_by = data.get('created_by')

            # 解析支付日期
            if payment_date:
                try:
                    payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()
                except ValueError:
                    raise ValidationError('支付日期格式错误，请使用 YYYY-MM-DD 格式')

            # 检查对账单是否存在
            statement = Statement.query.get(statement_id)
            if not statement:
                raise NotFoundError('对账单不存在')

            # 检查客户是否存在
            customer = Customer.query.get(customer_id)
            if not customer:
                raise NotFoundError('客户不存在')

            # 验证对账单是否属于该客户
            if statement.customer_id != customer_id:
                raise BusinessLogicError('对账单不属于指定客户')

            # 创建余额支付记录
            payment = StatementPayment.create_balance_payment(
                statement_id=statement_id,
                amount=amount,
                customer_id=customer_id,
                payment_date=payment_date,
                notes=notes,
                created_by=created_by
            )

            db.session.add(payment)

            # 更新对账单付款状态
            statement.add_payment(amount)

            db.session.commit()

            # 获取更新后的客户余额
            balance = CustomerBalance.query.filter_by(customer_id=customer_id).first()

            return success_response(
                data={
                    'payment_id': payment.id,
                    'statement_status': statement.status,
                    'statement_paid_amount': float(statement.paid_amount),
                    'customer_new_balance': float(balance.balance) if balance else 0,
                    'amount': float(amount)
                },
                message='余额支付记录创建成功'
            )

        except ValidationError as e:
            return error_response(str(e), code=422)
        except NotFoundError as e:
            return error_response(str(e), code=404)
        except BusinessLogicError as e:
            return error_response(str(e), code=400)
        except Exception as e:
            db.session.rollback()
            return error_response(f'创建余额支付记录失败: {str(e)}', code=500)


@api.route('/settle-statement/<int:statement_id>')
class SettleStatementAPI(Resource):
    @api.doc('结清对账单')
    @api.marshal_with(success_response_model)
    def post(self, statement_id):
        """结清对账单，同时结清相关的发货单和退货单"""
        try:
            # 检查对账单是否存在
            statement = Statement.query.get(statement_id)
            if not statement:
                raise NotFoundError('对账单不存在')

            # 检查对账单是否已完全收款
            if statement.status != '已结清':
                raise BusinessLogicError('对账单未完全收款，无法结清')

            # 结清对账单
            statement.settle_statement()

            db.session.commit()

            return success_response(
                data={
                    'statement_id': statement_id,
                    'settlement_date': statement.settlement_date.isoformat() if statement.settlement_date else None,
                    'delivery_notes_settled': len(statement.delivery_notes),
                    'return_orders_settled': len(statement.return_orders)
                },
                message='对账单结清成功'
            )

        except NotFoundError as e:
            return error_response(str(e), code=404)
        except BusinessLogicError as e:
            return error_response(str(e), code=400)
        except Exception as e:
            db.session.rollback()
            return error_response(f'结清对账单失败: {str(e)}', code=500)


@api.route('/payment-stats')
class PaymentStatsAPI(Resource):
    @api.doc('获取收款统计数据')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    @api.param('customer_id', '客户ID筛选', type='integer')
    @api.marshal_with(success_response_model)
    def get(self):
        """获取收款统计数据"""
        try:
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')
            customer_id = request.args.get('customer_id', type=int)

            # 构建基础查询
            payment_query = StatementPayment.query.filter(StatementPayment.status == '已确认')
            balance_query = BalanceTransaction.query.filter(BalanceTransaction.transaction_type == '充值')

            # 日期筛选
            if start_date:
                try:
                    start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
                    payment_query = payment_query.filter(StatementPayment.payment_date >= start_dt)
                    balance_query = balance_query.filter(BalanceTransaction.created_at >= datetime.combine(start_dt, datetime.min.time()))
                except ValueError:
                    raise ValidationError('开始日期格式错误，请使用 YYYY-MM-DD 格式')

            if end_date:
                try:
                    end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
                    payment_query = payment_query.filter(StatementPayment.payment_date <= end_dt)
                    balance_query = balance_query.filter(BalanceTransaction.created_at <= datetime.combine(end_dt, datetime.max.time()))
                except ValueError:
                    raise ValidationError('结束日期格式错误，请使用 YYYY-MM-DD 格式')

            # 客户筛选
            if customer_id:
                # 对于收款记录，需要通过对账单关联客户
                payment_query = payment_query.join(Statement).filter(Statement.customer_id == customer_id)
                balance_query = balance_query.filter(BalanceTransaction.customer_id == customer_id)

            # 统计数据
            # 对账单收款统计
            direct_payments = payment_query.filter(StatementPayment.payment_source == 'direct').all()
            balance_payments = payment_query.filter(StatementPayment.payment_source == 'balance').all()

            direct_payment_amount = sum(float(p.amount) for p in direct_payments)
            balance_payment_amount = sum(float(p.amount) for p in balance_payments)
            total_payment_amount = direct_payment_amount + balance_payment_amount

            # 余额充值统计
            balance_transactions = balance_query.all()
            total_recharge_amount = sum(float(t.amount) for t in balance_transactions)

            # 客户余额统计
            balance_stats_query = CustomerBalance.query
            if customer_id:
                balance_stats_query = balance_stats_query.filter(CustomerBalance.customer_id == customer_id)

            total_customer_balance = sum(float(b.balance) for b in balance_stats_query.all())
            total_frozen_balance = sum(float(b.frozen_balance) for b in balance_stats_query.all())

            data = {
                'payment_stats': {
                    'direct_payment_count': len(direct_payments),
                    'direct_payment_amount': direct_payment_amount,
                    'balance_payment_count': len(balance_payments),
                    'balance_payment_amount': balance_payment_amount,
                    'total_payment_count': len(direct_payments) + len(balance_payments),
                    'total_payment_amount': total_payment_amount
                },
                'balance_stats': {
                    'recharge_count': len(balance_transactions),
                    'total_recharge_amount': total_recharge_amount,
                    'total_customer_balance': total_customer_balance,
                    'total_frozen_balance': total_frozen_balance
                },
                'period': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'customer_id': customer_id
                }
            }

            return success_response(data=data, message='获取收款统计数据成功')

        except ValidationError as e:
            return error_response(str(e), code=422)
        except Exception as e:
            return error_response(f'获取收款统计数据失败: {str(e)}', code=500)
