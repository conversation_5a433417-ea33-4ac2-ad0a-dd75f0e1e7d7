from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_restx import Api
from flask_cors import CORS
import os

# 全局变量
db = SQLAlchemy()
restx_api = Api(
    version='1.0',
    title='EMB System API',
    description='工程物资报价及订单管理系统API文档',
    doc='/docs/'
)

def create_app():
    """创建Flask应用实例"""
    app = Flask(__name__)

    # 基础配置
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///project.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['RESTX_VALIDATE'] = True
    app.config['RESTX_MASK_SWAGGER'] = False
    app.config['JSON_AS_ASCII'] = False

    # SQLite外键约束配置
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }

    # 初始化扩展
    db.init_app(app)
    restx_api.init_app(app)

    # 启用SQLite外键约束
    if 'sqlite' in app.config['SQLALCHEMY_DATABASE_URI']:
        from sqlalchemy import event
        from sqlalchemy.engine import Engine

        @event.listens_for(Engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.close()

    # 配置CORS
    CORS(app, origins=[
        'http://localhost:3001',
        'http://127.0.0.1:3001',
        'http://**************:3001',
        'http://home.128228.xyz:3001'
    ], supports_credentials=True)

    # 注册错误处理器
    from .utils.error_handlers import register_error_handlers, setup_logging
    register_error_handlers(app)
    setup_logging(app)

    # 配置静态文件服务
    from flask import send_from_directory

    @app.route('/uploads/<path:filename>')
    def uploaded_file(filename):
        """提供上传文件的访问服务"""
        upload_dir = os.path.join(app.root_path, '..', 'uploads')
        return send_from_directory(upload_dir, filename)

    @app.route('/api/v1/uploads/<path:filename>')
    def uploaded_file_api(filename):
        """提供上传文件的访问服务（API路径）"""
        upload_dir = os.path.join(app.root_path, '..', 'uploads')
        return send_from_directory(upload_dir, filename)

    # 注册API命名空间
    from .api.test import api as test_ns
    restx_api.add_namespace(test_ns, path='/test')

    from .api.v1.customers import api as customers_ns
    restx_api.add_namespace(customers_ns, path='/api/v1/customers')

    from .api.v1.products import api as products_ns
    restx_api.add_namespace(products_ns, path='/api/v1/products')

    from .api.v1.system import api as system_ns
    restx_api.add_namespace(system_ns, path='/api/v1/system')

    from .api.v1.quotations import api as quotations_ns
    restx_api.add_namespace(quotations_ns, path='/api/v1/quotations')

    from .api.v1.orders import api as orders_ns
    restx_api.add_namespace(orders_ns, path='/api/v1/orders')

    from .api.v1.finance import api as finance_ns
    restx_api.add_namespace(finance_ns, path='/api/v1/finance')

    from .api.v1.dashboard import api as dashboard_ns
    restx_api.add_namespace(dashboard_ns, path='/api/v1/dashboard')

    from .api.v1.delivery_notes import api as delivery_notes_ns
    restx_api.add_namespace(delivery_notes_ns, path='/api/v1/delivery-notes')

    from .api.v1.delivery_export import api as delivery_export_ns
    restx_api.add_namespace(delivery_export_ns, path='/api/v1/delivery-export')

    from .api.v1.returns import api as returns_ns
    restx_api.add_namespace(returns_ns, path='/api/v1/returns')

    from .api.v1.return_export import api as return_export_ns
    restx_api.add_namespace(return_export_ns, path='/api/v1/return-export')

    from .api.v1.statements import api as statements_ns
    restx_api.add_namespace(statements_ns, path='/api/v1/statements')

    from .api.v1.payments import api as payments_ns
    restx_api.add_namespace(payments_ns, path='/api/v1/payments')

    # 新增的API模块
    from .api.v1.uploads import api as uploads_ns
    restx_api.add_namespace(uploads_ns, path='/api/v1/uploads')

    from .api.v1.error_logs import api as error_logs_ns
    restx_api.add_namespace(error_logs_ns, path='/api/v1/error-logs')

    from .api.v1.order_export import api as order_export_ns
    restx_api.add_namespace(order_export_ns, path='/api/v1/order-export')

    from .api.v1.order_print import api as order_print_ns
    restx_api.add_namespace(order_print_ns, path='/api/v1/order-print')

    from .api.v1.product_images import api as product_images_ns
    restx_api.add_namespace(product_images_ns, path='/api/v1/product-images')

    from .api.v1.statement_refunds import api as statement_refunds_ns
    restx_api.add_namespace(statement_refunds_ns, path='/api/v1/statement-refunds')

    from .api.v1.backup import api as backup_ns
    restx_api.add_namespace(backup_ns, path='/api/v1/backup')

    # from .api.v1.statement_export import api as statement_export_ns
    # restx_api.add_namespace(statement_export_ns, path='/api/v1/statement-export')

    # 注册路径重写中间件（用于API兼容性）
    from .middleware.path_rewrite import register_path_rewrite_middleware
    register_path_rewrite_middleware(app)

    # 导入模型
    with app.app_context():
        from . import models

    # 初始化调度器服务
    from app.services.scheduler_service import scheduler_service
    scheduler_service.init_app(app)

    # 注册应用关闭时的清理函数
    import atexit
    atexit.register(scheduler_service.shutdown)

    return app
