<template>
  <div class="order-edit">
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="form-title">{{ isEdit ? '编辑订单' : '新增订单' }}</h2>
        <div>
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="handleSave('draft')" :loading="submitting">保存</el-button>
        </div>
      </div>
    </el-card>

    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="order-form">
      <!-- 基本信息 -->
      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单号" prop="order_number">
              <el-input v-model="formData.order_number" placeholder="自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单日期" prop="order_date">
              <el-date-picker
                v-model="formData.order_date"
                type="date"
                placeholder="选择日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联报价单">
              <div class="quotation-container">
                <div v-if="formData.quotation_ids && formData.quotation_ids.length > 0" class="quotation-list">
                  <el-tag
                    v-for="(quotationNumber, index) in formData.quotation_numbers"
                    :key="index"
                    closable
                    @close="handleRemoveQuotation(index)"
                    class="quotation-tag"
                  >
                    {{ quotationNumber }}
                  </el-tag>
                </div>
                <el-input
                  v-else
                  value="无关联报价单"
                  placeholder="无关联报价单"
                  disabled
                />
                <div class="quotation-actions">
                  <el-button type="primary" @click="handleSelectQuotation">选择报价单</el-button>
                  <el-button
                    v-if="formData.quotation_ids && formData.quotation_ids.length > 0"
                    type="danger"
                    @click="handleClearAllQuotations"
                    size="small"
                  >
                    清除全部
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计交期" prop="expected_date">
              <el-date-picker
                v-model="formData.expected_date"
                type="date"
                placeholder="选择日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户信息 -->
      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <h3>客户信息</h3>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customer_id">
              <el-select
                v-model="formData.customer_id"
                filterable
                remote
                placeholder="请选择客户"
                :remote-method="remoteSearchCustomers"
                :loading="customerLoading"
                style="width: 100%"
                @change="handleCustomerChange"
              >
                <el-option
                  v-for="item in customerOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人">
              <el-input v-model="formData.contact_person" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话">
              <el-input v-model="formData.contact_phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="project_name">
              <el-input v-model="formData.project_name" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="项目地址" prop="project_address">
              <el-input v-model="formData.project_address" placeholder="请输入项目地址" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 商务信息 -->
      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <h3>商务信息</h3>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="付款条件" prop="payment_terms">
              <el-input v-model="formData.payment_terms" placeholder="例如：预付30%，发货前付款40%，验收合格后30天内付款30%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="交货条件" prop="delivery_terms">
              <el-select v-model="formData.delivery_terms" placeholder="请选择交货条件" style="width: 100%">
                <el-option label="送货上门" value="送货上门" />
                <el-option label="客户自提" value="客户自提" />
                <el-option label="物流配送" value="物流配送" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="formData.notes"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 产品明细 -->
      <el-card class="mb-20">
        <template #header>
          <div class="card-header">
            <h3>产品明细</h3>
            <div class="header-actions">
              <el-button type="success" size="small" @click="handleSelectProducts">
                <el-icon><Select /></el-icon>
                选择产品
              </el-button>
            </div>
          </div>
        </template>

        <!-- 统一设置工具栏 -->
        <div class="batch-settings-toolbar" style="margin-bottom: 16px; padding: 12px; background-color: #f5f7fa; border-radius: 4px;">
          <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
            <span style="font-weight: 500; color: #606266;">统一设置：</span>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="font-size: 14px; color: #606266;">税率：</span>
              <el-input-number
                v-model="batchSettings.taxRate"
                :min="0"
                :max="100"
                :precision="2"
                controls-position="right"
                style="width: 120px;"
                placeholder="税率"
              />
              <span style="font-size: 12px; color: #909399;">%</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="font-size: 14px; color: #606266;">折扣：</span>
              <el-input-number
                v-model="batchSettings.discount"
                :min="0"
                :max="100"
                :precision="0"
                controls-position="right"
                style="width: 120px;"
                placeholder="折扣"
              />
              <span style="font-size: 12px; color: #909399;">%</span>
            </div>
            <el-button
              type="primary"
              size="small"
              @click="applyBatchSettings"
              :disabled="formData.order_products.length === 0"
            >
              <el-icon><Setting /></el-icon>
              应用到所有产品
            </el-button>
          </div>
        </div>

        <el-table :data="formData.order_products" border style="width: 100%">
          <el-table-column type="index" label="序号" width="50" />
          <el-table-column label="产品名称" min-width="180">
            <template #default="{ row }">
              <div class="readonly-field">{{ row.product_name || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="型号" width="120">
            <template #default="{ row }">
              <div class="readonly-field">{{ row.product_model || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="规格" min-width="100">
            <template #default="{ row }">
              <div class="readonly-field">{{ row.specification || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="单位" width="90">
            <template #default="{ row }">
              <div class="readonly-field">{{ row.unit || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="来源" width="120">
            <template #default="{ row }">
              <el-tag v-if="row.source_type === 'quotation'" type="info" size="small">
                {{ row.source_display || '报价单' }}
              </el-tag>
              <el-tag v-else type="warning" size="small">手动添加</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="数量" width="100">
            <template #default="{ row }">
              <el-input-number v-model="row.quantity" :min="1" controls-position="right" style="width: 100%;" @change="calculateRowTotal(row)" />
            </template>
          </el-table-column>
          <el-table-column label="单价" width="100">
            <template #default="{ row }">
              <el-input-number v-model="row.unit_price" :min="0" :precision="2" controls-position="right" @change="calculateRowTotal(row)" />
            </template>
          </el-table-column>
          <el-table-column label="税率(%)" width="80">
            <template #default="{ row }">
              <el-input-number v-model="row.tax_rate" :min="0" :max="100" :precision="2" controls-position="right" style="width: 200%;" @change="calculateRowTotal(row)" />
            </template>
          </el-table-column>
          <el-table-column label="折扣(%)" width="80">
            <template #default="{ row }">
              <el-input-number v-model="row.discount" :min="0" :max="100" :precision="0" controls-position="right" @change="calculateRowTotal(row)" />
            </template>
          </el-table-column>
          <el-table-column label="总价" width="120">
            <template #default="{ row }">
              {{ formatCurrency(row.total_price) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ $index }">
              <el-button type="danger" link @click="handleDeleteProduct($index)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="formData.order_products.length === 0" class="empty-data">
          <el-empty description="暂无产品数据" />
        </div>

        <div v-else class="order-summary">
          <div class="summary-item">
            <span>产品数量:</span>
            <span>{{ getTotalQuantity() }} 件</span>
          </div>
          <div class="summary-item">
            <span>订单总金额:</span>
            <span class="highlight">{{ formatCurrency(getTotalAmount()) }}</span>
          </div>
        </div>
      </el-card>
    </el-form>

    <!-- 选择报价单对话框 -->
    <el-dialog
      v-model="quotationDialogVisible"
      title="选择报价单"
      width="800px"
      destroy-on-close
    >
      <div class="mb-10">
        <el-alert
          title="请选择已确认状态的报价单，一个订单可以关联多个报价单"
          type="info"
          show-icon
          :closable="false"
        >
          <template #default>
            <p>💡 提示：可以点击表格行或使用复选框来选择报价单</p>
            <p>📝 选择文字时不会触发行选择，可以正常复制文本内容</p>
          </template>
        </el-alert>
      </div>

      <el-form :inline="true" class="mb-20">
        <el-form-item label="报价单号">
          <el-input
            v-model="quotationSearchForm.quotation_number"
            placeholder="请输入报价单号"
            clearable
            @keyup.enter="searchQuotations"
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input
            v-model="quotationSearchForm.customer_name"
            placeholder="请输入客户名称"
            clearable
            @keyup.enter="searchQuotations"
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input
            v-model="quotationSearchForm.project_name"
            placeholder="请输入项目名称"
            clearable
            @keyup.enter="searchQuotations"
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchQuotations" :loading="quotationLoading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuotationSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="quotationTableRef"
        :data="quotationList"
        :key="quotationList.length"
        border
        style="width: 100%"
        @selection-change="handleQuotationSelectionChange"
        @row-click="handleQuotationRowClick"
        v-loading="quotationLoading"
        :empty-text="quotationLoading ? '搜索中...' : '暂无数据'"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="quotation_number" label="报价单号" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-tag
                v-if="isQuotationSelected(row.id)"
                type="success"
                size="small"
                effect="plain"
                class="mr-1"
              >
                已选择
              </el-tag>
              <span>{{ row.quotation_number }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="客户名称" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.customer?.name || row.customer_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="project_name" label="项目名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="total_amount" label="报价金额" width="120">
          <template #default="{ row }">
            {{ formatCurrency(row.total_amount || 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="valid_until" label="有效期至" width="120">
          <template #default="{ row }">
            {{ row.valid_until ? formatDate(row.valid_until) : '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="120">
          <template #default="{ row }">
            {{ row.created_at ? formatDate(row.created_at) : '-' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="flex-between mt-20">
        <span class="pagination-info">
          共 {{ quotationPagination.total }} 条记录
        </span>
        <el-pagination
          v-model:current-page="quotationPagination.currentPage"
          v-model:page-size="quotationPagination.pageSize"
          :total="quotationPagination.total"
          :page-sizes="[10, 20, 50]"
          layout="sizes, prev, pager, next"
          @size-change="handleQuotationSizeChange"
          @current-change="handleQuotationCurrentChange"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="quotationDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelectQuotation">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 选择产品对话框 -->
    <el-dialog
      v-model="selectProductDialogVisible"
      title="选择产品"
      width="800px"
      destroy-on-close
    >
      <div class="product-search mb-20">
        <el-input
          v-model="productSearchKeyword"
          placeholder="搜索产品名称或型号"
          clearable
          @input="searchProducts"
        >
          <template #append>
            <el-button :icon="Search" @click="searchProducts"></el-button>
          </template>
        </el-input>
      </div>

      <el-table
        :data="productSearchResults"
        border
        style="width: 100%"
        @row-click="handleProductRowClick"
        row-key="id"
      >
        <el-table-column prop="name" label="产品名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="model" label="型号" width="120" show-overflow-tooltip />
        <el-table-column label="规格" min-width="180">
          <template #default="{ row }">
            <div v-if="row.specifications && row.specifications.length > 0">
              <el-checkbox-group v-model="row.selectedSpecIds">
                <div v-for="spec in row.specifications" :key="spec.id" style="margin-bottom: 5px;">
                  <el-checkbox :label="spec.id">
                    <span :class="{ 'existing-spec-text': isExistingSpecification(spec.id) }">
                      {{ spec.specification }} (¥{{ spec.suggested_price || spec.price || 0 }})
                    </span>
                    <el-tag
                      v-if="isExistingSpecification(spec.id)"
                      type="info"
                      size="small"
                      class="existing-spec-tag ml-5"
                      effect="plain"
                    >
                      已选择
                    </el-tag>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
            <el-empty v-else description="无规格信息" :image-size="40" />
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
      </el-table>

      <div class="flex-between mt-20">
        <span></span>
        <el-pagination
          v-model:current-page="productPagination.currentPage"
          v-model:page-size="productPagination.pageSize"
          :total="productPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleProductSizeChange"
          @current-change="handleProductCurrentChange"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="selectProductDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelectProducts">确定添加</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, Select, Upload, Search, Refresh, Delete, Setting } from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import { customerApi } from '@/api/customer'
import { quotationApi } from '@/api/quotation'
import { productApi } from '@/api/product'
import * as XLSX from 'xlsx'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref()

// 状态
const loading = ref(false)
const submitting = ref(false)
const customerLoading = ref(false)

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)

// 选项数据
const customerOptions = ref([])
const quotationOptions = ref([])
const productOptions = ref([])

// 对话框状态
const quotationDialogVisible = ref(false)
const selectProductDialogVisible = ref(false)

// 报价单搜索相关
const quotationLoading = ref(false)

const quotationSearchForm = reactive({
  quotation_number: '',
  customer_name: '',
  project_name: '',
  status: '已确认'
})
const quotationPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 报价单搜索
const quotationList = ref([])
const quotationTableRef = ref()
const selectedQuotation = ref(null)
const selectedQuotations = ref([]) // 多选报价单

// 产品搜索
const productSearchKeyword = ref('')
const productSearchResults = ref([])
const productPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})



// 表单数据
const formData = reactive({
  id: null,
  order_number: '',
  order_date: new Date().toISOString().split('T')[0],
  quotation_ids: [] as number[], // 多个报价单ID数组
  quotation_numbers: [] as string[], // 多个报价单号数组
  expected_date: '',
  customer_id: '',
  delivery_address_id: null,
  contact_person: '',
  contact_phone: '',
  project_name: '',
  project_address: '',
  payment_terms: '',
  delivery_terms: '',
  notes: '',
  order_products: [] as any[]
})

// 批量设置数据
const batchSettings = reactive({
  taxRate: 13.0,  // 默认税率13%
  discount: 0.0   // 默认折扣0%
})

// 表单验证规则
const rules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  project_name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  expected_date: [
    { required: true, message: '请选择预计交期', trigger: 'change' }
  ]
}

// 计算总金额
const getTotalAmount = () => {
  return formData.order_products.reduce((total, item) => {
    return total + (item.total_price || 0)
  }, 0)
}

// 计算总数量
const getTotalQuantity = () => {
  return formData.order_products.reduce((total, item) => {
    return total + (item.quantity || 0)
  }, 0)
}

// 搜索客户
const remoteSearchCustomers = async (query: string) => {
  if (query) {
    try {
      customerLoading.value = true
      const response = await customerApi.getList({ name: query, per_page: 20 }) as any
      customerOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('搜索客户失败:', error)
    } finally {
      customerLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

// 客户变化
const handleCustomerChange = async (customerId: number) => {
  if (customerId) {
    try {
      const customer = await customerApi.getById(customerId) as any
      if (customer) {
        formData.contact_person = customer.contact || ''
        formData.contact_phone = customer.phone || ''
      }
    } catch (error) {
      console.error('获取客户详情失败:', error)
    }
  }
}

// 选择报价单
const handleSelectQuotation = () => {
  quotationSearchForm.quotation_number = ''
  quotationSearchForm.customer_name = ''
  quotationSearchForm.project_name = ''
  quotationList.value = []
  selectedQuotation.value = null
  quotationPagination.currentPage = 1
  quotationPagination.total = 0
  quotationDialogVisible.value = true
  searchQuotations()
}

// 搜索报价单
const searchQuotations = async () => {
  try {
    quotationLoading.value = true
    const params: any = {
      page: quotationPagination.currentPage,
      per_page: quotationPagination.pageSize,
      status: '已确认' // 只显示已确认的报价单
    }

    // 添加搜索条件
    if (quotationSearchForm.quotation_number) {
      params.quotation_number = quotationSearchForm.quotation_number
    }
    if (quotationSearchForm.customer_name) {
      params.customer_name = quotationSearchForm.customer_name
    }
    if (quotationSearchForm.project_name) {
      params.project_name = quotationSearchForm.project_name
    }

    const response = await quotationApi.getList(params) as any

    // 处理响应数据 - 修复响应结构判断
    if (response && Array.isArray(response)) {
      // 响应直接就是数据数组
      let items = response

      // 过滤掉已关联的报价单（但排除当前订单已关联的报价单）
      if (isEdit.value && formData.quotation_ids) {
        // 编辑模式：过滤掉已关联的报价单，但保留当前订单已关联的报价单
        const currentQuotationIds = formData.quotation_ids
        items = items.filter(item =>
          !item.is_linked_to_order || currentQuotationIds.includes(item.id)
        )
      } else {
        // 新增模式：过滤掉已关联的报价单
        items = items.filter(item => !item.is_linked_to_order)
      }

      let total = items.length
      console.log('直接使用响应数组:', { itemsLength: items.length, total })

      console.log('数据结构分析:', {
        responseData: response.data,
        pagination: response.pagination,
        isArray: Array.isArray(response.data),
        items: items,
        itemsLength: items.length,
        total: total
      })

      // 最简单的数据设置方式
      try {
        console.log('开始设置数据，items长度:', items.length)
        console.log('items内容:', items)

        // 清空现有数据
        quotationList.value.length = 0

        // 逐个添加数据项
        items.forEach((item, index) => {
          quotationList.value.push(item)
          console.log(`添加第${index + 1}项:`, item.quotation_number)
        })

        // 设置分页
        quotationPagination.total = total

        console.log('最终 quotationList 长度:', quotationList.value.length)
        console.log('最终 quotationPagination.total:', quotationPagination.total)
        console.log('数据设置完成')

        // 强制触发表格重新渲染
        nextTick(() => {
          console.log('🔄 nextTick 执行，强制更新表格')
          if (quotationTableRef.value) {
            console.log('📊 表格引用存在，执行 doLayout')
            quotationTableRef.value.doLayout()
          } else {
            console.log('❌ 表格引用不存在')
          }
        })

      } catch (error) {
        console.error('设置数据时出错:', error)
        quotationList.value = []
        quotationPagination.total = 0
      }
    } else if (response && response.items && Array.isArray(response.items)) {
      // 响应结构是 { items: [], total: 0 }
      let items = response.items

      // 过滤掉已关联的报价单（但排除当前订单已关联的报价单）
      if (isEdit.value && formData.quotation_ids) {
        // 编辑模式：过滤掉已关联的报价单，但保留当前订单已关联的报价单
        const currentQuotationIds = formData.quotation_ids
        items = items.filter(item => {
          const shouldKeep = !item.is_linked_to_order || currentQuotationIds.includes(item.id)
          return shouldKeep
        })
      } else {
        // 新增模式：过滤掉已关联的报价单
        items = items.filter(item => {
          const shouldKeep = !item.is_linked_to_order
          return shouldKeep
        })
      }

      const total = items.length

      try {
        // 清空现有数据
        quotationList.value.length = 0

        // 逐个添加数据项
        items.forEach((item) => {
          quotationList.value.push(item)
        })

        // 设置分页
        quotationPagination.total = total

        // 强制触发表格重新渲染
        nextTick(() => {
          if (quotationTableRef.value) {
            quotationTableRef.value.doLayout()
          }
        })

      } catch (error) {
        console.error('设置数据时出错:', error)
        quotationList.value = []
        quotationPagination.total = 0
      }
    } else {
      quotationList.value = []
      quotationPagination.total = 0
    }
  } catch (error) {
    console.error('搜索报价单失败:', error)
    ElMessage.error('搜索报价单失败')
    quotationList.value = []
    quotationPagination.total = 0
  } finally {
    quotationLoading.value = false
  }
}

// 重置报价单搜索
const resetQuotationSearch = () => {
  quotationSearchForm.quotation_number = ''
  quotationSearchForm.customer_name = ''
  quotationSearchForm.project_name = ''
  quotationPagination.currentPage = 1
  searchQuotations()
}

// 报价单分页处理
const handleQuotationSizeChange = (size: number) => {
  quotationPagination.pageSize = size
  quotationPagination.currentPage = 1
  searchQuotations()
}

const handleQuotationCurrentChange = (page: number) => {
  quotationPagination.currentPage = page
  searchQuotations()
}

// 报价单多选处理
const handleQuotationSelectionChange = (selection: any[]) => {
  selectedQuotations.value = selection
}

// 处理报价单行点击事件
const handleQuotationRowClick = (row: any, column: any, event: Event) => {
  if (!quotationTableRef.value) return

  // 延迟检查，确保文本选择操作完成
  setTimeout(() => {
    // 检查是否有文本被选中（用户正在选择文字）
    const selection = window.getSelection()
    if (selection && selection.toString().trim().length > 0) {
      return // 如果有文本被选中，不触发行选择
    }

    // 检查当前行是否已被选中
    const isSelected = selectedQuotations.value.some(item => item.id === row.id)

    if (isSelected) {
      // 如果已选中，则取消选中
      quotationTableRef.value.toggleRowSelection(row, false)
    } else {
      // 如果未选中，则选中
      quotationTableRef.value.toggleRowSelection(row, true)
    }
  }, 10) // 短暂延迟，让文本选择操作先完成
}

// 确认选择报价单
const confirmSelectQuotation = async () => {
  if (!selectedQuotations.value || selectedQuotations.value.length === 0) {
    ElMessage.warning('请选择至少一个报价单')
    return
  }

  try {
    const newQuotationIds = []
    const newQuotationNumbers = []
    let allProducts = []
    let primaryQuotation = null

    // 处理每个选中的报价单
    for (const quotation of selectedQuotations.value) {
      // 检查是否已经关联过这个报价单
      if (formData.quotation_ids.includes(quotation.id)) {
        ElMessage.warning(`报价单 ${quotation.quotation_number} 已经关联，跳过`)
        continue
      }

      // 获取完整的报价单详情
      const quotationDetail = await quotationApi.getById(quotation.id) as any
      console.log('报价单详情:', quotationDetail)

      if (!quotationDetail) {
        ElMessage.error(`获取报价单 ${quotation.quotation_number} 详情失败`)
        continue
      }

      newQuotationIds.push(quotationDetail.id)
      newQuotationNumbers.push(quotationDetail.quotation_number)

      // 第一个报价单作为主报价单，用于填充基本信息
      if (!primaryQuotation) {
        primaryQuotation = quotationDetail

        // 如果没有客户信息，从主报价单获取
        if (!formData.customer_id && quotationDetail.customer_id) {
          formData.customer_id = quotationDetail.customer_id
          formData.contact_person = quotationDetail.customer?.contact || ''
          formData.contact_phone = quotationDetail.customer?.phone || ''

          // 将客户添加到选项中
          if (quotationDetail.customer) {
            customerOptions.value = [quotationDetail.customer]
          }
        }

        // 如果没有项目信息，从主报价单获取
        if (!formData.project_name && quotationDetail.project_name) {
          formData.project_name = quotationDetail.project_name
        }
        if (!formData.project_address && quotationDetail.project_address) {
          formData.project_address = quotationDetail.project_address
        }
        if (!formData.payment_terms && quotationDetail.payment_terms) {
          formData.payment_terms = quotationDetail.payment_terms
        }
        if (!formData.delivery_terms && quotationDetail.delivery_terms) {
          formData.delivery_terms = quotationDetail.delivery_terms
        }
      }

      // 收集所有产品
      if (quotationDetail.items && quotationDetail.items.length > 0) {
        const products = quotationDetail.items.map((item: any) => {
          const product = {
            product_id: item.product_id || 0,
            product_specification_id: item.product_specification_id || 0,
            product_name: item.product_name || '',
            product_model: item.product_model || '',
            specification: typeof item.specification === 'string' ? item.specification : (item.specification?.specification || ''),
            unit: item.product_unit || item.unit || '',
            quantity: item.quantity || 1,
            unit_price: item.unit_price || 0,
            discount: item.discount || 0,
            tax_rate: item.tax_rate || 13,
            total_price: 0,
            notes: item.notes || '',
            source: quotationDetail.quotation_number, // 标记来源
            source_type: 'quotation', // 来源类型
            source_id: quotationDetail.id, // 来源报价单ID
            source_display: quotationDetail.quotation_number // 来源显示
          }
          // 重新计算总价
          calculateRowTotal(product)
          return product
        })
        allProducts.push(...products)
      }
    }

    if (newQuotationIds.length === 0) {
      ElMessage.warning('没有新的报价单可以关联')
      return
    }

    // 更新报价单信息
    formData.quotation_ids.push(...newQuotationIds)
    formData.quotation_numbers.push(...newQuotationNumbers)

    // 询问是否导入产品明细
    if (allProducts.length > 0) {
      try {
        await ElMessageBox.confirm(
          `是否同时导入所选报价单中的 ${allProducts.length} 个产品？`,
          '导入产品确认',
          {
            type: 'question',
            confirmButtonText: '导入产品',
            cancelButtonText: '仅导入基本信息'
          }
        )

        // 导入产品明细
        formData.order_products.push(...allProducts)
        ElMessage.success(`成功关联 ${newQuotationIds.length} 个报价单并导入产品明细`)
      } catch {
        // 用户选择不导入产品
        ElMessage.success(`成功关联 ${newQuotationIds.length} 个报价单`)
      }
    } else {
      ElMessage.success(`成功关联 ${newQuotationIds.length} 个报价单`)
    }

    quotationDialogVisible.value = false
  } catch (error) {
    console.error('关联报价单失败:', error)
    ElMessage.error('关联报价单失败')
  }
}

// 检查报价单是否已被选择
const isQuotationSelected = (quotationId: number) => {
  return formData.quotation_ids.includes(quotationId)
}

// 移除单个报价单
const handleRemoveQuotation = (index: number) => {
  if (formData.quotation_ids && formData.quotation_numbers) {
    formData.quotation_ids.splice(index, 1)
    formData.quotation_numbers.splice(index, 1)

    ElMessage.success('报价单移除成功')
  }
}

// 清除所有报价单
const handleClearAllQuotations = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除所有关联的报价单吗？',
      '清除确认',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    formData.quotation_ids = []
    formData.quotation_numbers = []

    ElMessage.success('所有报价单已清除')
  } catch {
    // 用户取消
  }
}

// 选择产品
const handleSelectProducts = () => {
  productSearchKeyword.value = ''
  productPagination.currentPage = 1
  productSearchResults.value = []
  searchProducts()
  selectProductDialogVisible.value = true
}

// 搜索产品
const searchProducts = async () => {
  try {
    const params = {
      page: productPagination.currentPage,
      per_page: productPagination.pageSize,
      search: productSearchKeyword.value || undefined,
      with_specifications: true
    }
    const response = await productApi.getList(params) as any

    let productList = []
    if (Array.isArray(response)) {
      productList = response
    } else if (response?.data?.list) {
      productList = response.data.list
    } else if (response?.data) {
      productList = Array.isArray(response.data) ? response.data : []
    }

    // 为每个产品添加selectedSpecIds属性
    productSearchResults.value = productList.map(product => ({
      ...product,
      selectedSpecIds: [],
      specifications: product.specifications || []
    }))

    // 获取分页信息
    if (response?.pagination) {
      productPagination.total = response.pagination.total || 0
    } else if (response?.data?.pagination) {
      productPagination.total = response.data.pagination.total || 0
    }
  } catch (error) {
    console.error('搜索产品失败:', error)
    ElMessage.error('搜索产品失败')
    productSearchResults.value = []
    productPagination.total = 0
  }
}

// 产品分页处理
const handleProductSizeChange = (size: number) => {
  productPagination.pageSize = size
  productPagination.currentPage = 1
  searchProducts()
}

const handleProductCurrentChange = (page: number) => {
  productPagination.currentPage = page
  searchProducts()
}



// 判断规格是否已存在于订单中
const isExistingSpecification = (specId: number) => {
  return formData.order_products.some((item: any) => item.product_specification_id === specId)
}

// 处理产品行点击事件
const handleProductRowClick = (row: any) => {
  // 可以在这里添加行点击逻辑，比如自动选择/取消选择
}

// 确认选择产品
const confirmSelectProducts = () => {
  const selectedProducts = productSearchResults.value.filter(product =>
    product.selectedSpecIds && product.selectedSpecIds.length > 0
  )

  for (const product of selectedProducts) {
    for (const specId of product.selectedSpecIds) {
      const spec = product.specifications.find(s => s.id === specId)
      if (spec) {
        formData.order_products.push({
          product_id: product.id,
          product_specification_id: specId,
          product_name: product.name,
          product_model: product.model,
          specification: spec.specification,
          unit: product.unit,
          quantity: 1,
          unit_price: Number(spec.suggested_price) || Number(spec.price) || 0,
          discount: 0,
          tax_rate: 13,
          total_price: Number(spec.suggested_price) || Number(spec.price) || 0,
          notes: '',
          source_type: 'manual',
          source_id: null,
          source_display: null
        })
      }
    }
  }

  selectProductDialogVisible.value = false
  ElMessage.success('产品添加成功')
}

// 计算行总价
const calculateRowTotal = (row: any) => {
  const quantity = row.quantity || 0
  const unitPrice = row.unit_price || 0
  const discount = row.discount || 0
  const taxRate = row.tax_rate || 0

  const subtotal = quantity * unitPrice
  const discountAmount = subtotal * (discount / 100)
  const afterDiscount = subtotal - discountAmount
  const taxAmount = afterDiscount * (taxRate / 100)

  row.total_price = afterDiscount + taxAmount
}

// 批量应用税率和折扣设置
const applyBatchSettings = () => {
  if (formData.order_products.length === 0) {
    ElMessage.warning('暂无产品数据')
    return
  }

  // 应用到所有产品
  formData.order_products.forEach(product => {
    product.tax_rate = batchSettings.taxRate
    product.discount = batchSettings.discount
    // 重新计算总价
    calculateRowTotal(product)
  })

  ElMessage.success(`已为 ${formData.order_products.length} 个产品设置税率 ${batchSettings.taxRate}% 和折扣 ${batchSettings.discount}%`)
}

// 删除产品
const handleDeleteProduct = (index: number) => {
  formData.order_products.splice(index, 1)
}

// 下载模板
const downloadTemplate = () => {
  const templateData = [
    ['产品名称*', '型号', '规格', '单位*', '数量*', '单价*', '折扣率(%)', '税率(%)', '备注'],
    ['示例：工业控制器', 'PLC-001', '24V/16点输入输出', '台', '5', '1200.00', '5', '13', '主控制器，含配套软件'],
    ['示例：传感器', 'SENSOR-002', '温度传感器 -40~85℃', '个', '10', '150.00', '0', '13', '高精度温度检测'],
    ['示例：电缆', 'CABLE-003', '4芯屏蔽电缆 AWG18', '米', '100', '8.50', '10', '13', '工业级屏蔽电缆'],
    ['示例：开关电源', 'PSU-004', '24V/10A 开关电源', '个', '2', '280.00', '0', '13', '工业级开关电源']
  ]

  const worksheet = XLSX.utils.aoa_to_sheet(templateData)

  // 设置列宽
  worksheet['!cols'] = [
    { wch: 25 }, // 产品名称
    { wch: 15 }, // 型号
    { wch: 25 }, // 规格
    { wch: 8 },  // 单位
    { wch: 8 },  // 数量
    { wch: 12 }, // 单价
    { wch: 12 }, // 折扣率
    { wch: 12 }, // 税率
    { wch: 35 }  // 备注
  ]

  // 设置表头样式
  const headerStyle = {
    font: { bold: true, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "4472C4" } },
    alignment: { horizontal: "center", vertical: "center" }
  }

  // 应用表头样式
  for (let col = 0; col < templateData[0].length; col++) {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: col })
    if (!worksheet[cellRef]) worksheet[cellRef] = { t: 's', v: templateData[0][col] }
    worksheet[cellRef].s = headerStyle
  }

  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, '产品明细模板')

  // 添加说明工作表
  const instructionData = [
    ['订单产品导入模板使用说明'],
    [''],
    ['必填字段说明：'],
    ['产品名称*', '产品的完整名称，不能为空'],
    ['单位*', '产品的计量单位，如：个、台、套、米、根、件、箱等'],
    ['数量*', '订购数量，必须为正数'],
    ['单价*', '产品单价，必须为正数'],
    [''],
    ['可选字段说明：'],
    ['型号', '产品型号或规格代码'],
    ['规格', '产品的详细规格描述'],
    ['折扣率(%)', '折扣百分比，如：10 表示10%折扣，默认为0'],
    ['税率(%)', '税率百分比，如：13 表示13%税率，默认为13'],
    ['备注', '产品相关的备注信息'],
    [''],
    ['注意事项：'],
    ['1. 请不要修改表头（第一行）的内容'],
    ['2. 数量和单价必须为数字格式'],
    ['3. 折扣率和税率请填写百分比数值'],
    ['4. 空行将被自动跳过'],
    ['5. 导入时会自动计算总价（含税、折扣后）']
  ]

  const instructionSheet = XLSX.utils.aoa_to_sheet(instructionData)
  instructionSheet['!cols'] = [{ wch: 30 }, { wch: 50 }]

  // 设置说明标题样式
  const titleStyle = {
    font: { bold: true, size: 14, color: { rgb: "1F4E79" } },
    alignment: { horizontal: "center" }
  }
  instructionSheet['A1'].s = titleStyle

  XLSX.utils.book_append_sheet(workbook, instructionSheet, '使用说明')

  XLSX.writeFile(workbook, '订单产品导入模板.xlsx')
  ElMessage.success('模板文件已开始下载，请查看"使用说明"工作表了解详细说明')
}












// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待确认': 'warning',
    '已确认': 'success',
    '已拒绝': 'danger',
    '已过期': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取报价单状态类型
const getQuotationStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '草稿': 'info',
    '待确认': 'warning',
    '已确认': 'success',
    '已拒绝': 'danger',
    '已过期': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取报价单状态标签
const getQuotationStatusLabel = (status: string) => {
  // 后端返回的已经是中文状态，直接返回
  return status || '未知状态'
}

// 清除报价单关联
const handleClearQuotation = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除报价单关联吗？这将清除从报价单导入的基本信息，但不会影响已添加的产品明细。',
      '确认清除',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    // 清除报价单相关信息
    formData.quotation_ids = []
    formData.quotation_numbers = []

    ElMessage.success('已清除报价单关联')
  } catch {
    // 用户取消操作
  }
}

// 返回
const goBack = () => {
  router.back()
}

// 保存订单
const handleSave = async (status: string) => {
  try {
    await formRef.value?.validate()

    if (formData.order_products.length === 0) {
      ElMessage.warning('请至少添加一个产品')
      return
    }

    submitting.value = true

    // 如果没有送货地址ID，尝试获取客户的默认送货地址
    let deliveryAddressId = formData.delivery_address_id
    if (!deliveryAddressId && formData.customer_id) {
      try {
        const customerResponse = await customerApi.getById(formData.customer_id) as any
        const customer = customerResponse.data || customerResponse
        if (customer.delivery_addresses && customer.delivery_addresses.length > 0) {
          // 优先使用默认地址，否则使用第一个地址
          const defaultAddress = customer.delivery_addresses.find((addr: any) => addr.is_default)
          deliveryAddressId = defaultAddress ? defaultAddress.id : customer.delivery_addresses[0].id
        }
      } catch (error) {
        console.warn('获取客户送货地址失败:', error)
      }
    }

    // 准备后端期望的数据格式，过滤掉null值
    const orderData: any = {
      customer_id: formData.customer_id,
      project_name: formData.project_name,
      delivery_address_id: deliveryAddressId || 1, // 如果还是没有，使用ID为1的地址作为默认值
      // 使用新的双状态系统
      order_status: '待确认',  // 发货状态，新订单都是待确认
      payment_status: '未收款',  // 收款状态，新订单都是未收款
      products: formData.order_products.map(item => {
        const productData: any = {
          product_specification_id: Number(item.product_specification_id),
          quantity: Number(item.quantity),
          unit_price: Number(item.unit_price) || 0, // 保存原始单价
          discount: Number(item.discount) || 0, // 保存折扣率
          tax_rate: Number(item.tax_rate) || 13, // 保存税率
          // 包含产品来源信息
          source_type: item.source_type || 'manual',
          source_id: item.source_id || null,
          source_display: item.source_display || null
        }

        return productData
      })
    }

    // 始终发送quotation_ids字段，即使为空数组也要发送，这样后端才能知道用户想要清除关联报价单
    if (formData.quotation_ids && formData.quotation_ids.length > 0) {
      orderData.quotation_ids = JSON.stringify(formData.quotation_ids)
    } else {
      // 发送空字符串表示清除所有关联报价单
      orderData.quotation_ids = ""
    }

    if (formData.project_address && formData.project_address.trim()) {
      orderData.project_address = formData.project_address.trim()
    }

    // expected_date是必填字段，确保总是包含
    if (formData.expected_date) {
      orderData.expected_date = new Date(formData.expected_date).toISOString()
    } else {
      // 如果没有设置预计交期，使用当前日期+15天作为默认值
      const defaultDate = new Date()
      defaultDate.setDate(defaultDate.getDate() + 15)
      orderData.expected_date = defaultDate.toISOString()
    }

    if (formData.payment_terms && formData.payment_terms.trim()) {
      orderData.payment_terms = formData.payment_terms.trim()
    }

    if (formData.delivery_terms && formData.delivery_terms.trim()) {
      orderData.delivery_terms = formData.delivery_terms.trim()
    }

    if (formData.notes && formData.notes.trim()) {
      orderData.notes = formData.notes.trim()
    }

    console.log('formData.expected_date:', formData.expected_date)
    console.log('发送的订单数据:', orderData)

    if (isEdit.value) {
      await orderApi.update(route.params.id as string, orderData)
      ElMessage.success('订单更新成功')
    } else {
      await orderApi.create(orderData)
      ElMessage.success('订单创建成功')
    }

    router.push('/orders')
  } catch (error: any) {
    console.error('保存订单失败:', error)

    // 检查是否是送货地址相关的错误
    const errorMessage = error?.response?.data?.message || error?.message || '保存订单失败'
    if (errorMessage.includes('送货地址')) {
      ElMessageBox.confirm(
        '客户没有设置送货地址，是否现在为客户添加送货地址？',
        '缺少送货地址',
        {
          confirmButtonText: '添加地址',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 跳转到客户管理页面添加地址
        router.push(`/customers/edit/${formData.customer_id}`)
      }).catch(() => {
        // 用户取消，不做任何操作
      })
    } else {
      ElMessage.error(errorMessage)
    }
  } finally {
    submitting.value = false
  }
}

// 格式化货币
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value)
}

// 初始化
onMounted(async () => {
  if (isEdit.value) {
    // 编辑模式：加载现有订单数据
    try {
      loading.value = true
      const order = await orderApi.getById(route.params.id as string) as any
      if (order) {
        // 复制基本信息
        Object.assign(formData, order)
        // 处理产品明细：后端返回的是products，前端需要order_products
        if (order.products && Array.isArray(order.products)) {
          formData.order_products = order.products.map((product: any) => ({
            ...product,
            // 字段映射：后端字段名 -> 前端字段名
            specification: product.specification_description || product.specification || '',
            unit: product.product_unit || product.unit || '',
            // 保持原有的产品来源信息，只有当字段为undefined时才设置默认值
            source_type: product.source_type !== undefined ? product.source_type : 'manual',
            source_id: product.source_id !== undefined ? product.source_id : null,
            source_display: product.source_display !== undefined ? product.source_display : null
          }))

          // 重新计算每个产品的总价
          formData.order_products.forEach((product: any) => {
            calculateRowTotal(product)
          })
        }
        // 处理多个报价单
        if (order.quotations && Array.isArray(order.quotations)) {
          formData.quotation_ids = order.quotations.map(q => q.id)
          formData.quotation_numbers = order.quotations.map(q => q.quotation_number)
        } else {
          formData.quotation_ids = []
          formData.quotation_numbers = []
        }
        // 处理客户信息显示
        if (order.customer && order.customer.name) {
          formData.customer_name = order.customer.name
          // 将当前客户添加到选项中，确保el-select能正确显示
          customerOptions.value = [{
            id: order.customer.id,
            name: order.customer.name,
            contact: order.customer.contact || '',
            phone: order.customer.phone || ''
          }]
        }
      }
    } catch (error) {
      console.error('获取订单详情失败:', error)
      ElMessage.error('获取订单详情失败')
    } finally {
      loading.value = false
    }
  } else {
    // 新增模式：检查是否有报价单ID参数
    const quotationId = route.query.quotationId as string
    if (quotationId) {
      try {
        loading.value = true
        // 获取报价单详情并预填充
        const quotation = await quotationApi.getById(quotationId) as any
        console.log('报价单详情数据:', quotation)
        console.log('报价单产品字段:', quotation?.products, quotation?.items)
        if (quotation) {
          // 预填充基本信息
          formData.customer_id = quotation.customer_id
          formData.project_name = quotation.project_name || ''
          formData.project_address = quotation.project_address || ''
          formData.payment_terms = quotation.payment_terms || ''
          formData.delivery_terms = quotation.delivery_terms || ''
          formData.notes = `基于报价单 ${quotation.quotation_number} 创建`

          // 设置关联报价单数组格式
          formData.quotation_ids = [quotation.id]
          formData.quotation_numbers = [quotation.quotation_number]

          // 预填充客户信息
          if (quotation.customer) {
            formData.customer_name = quotation.customer.name
            formData.contact_person = quotation.customer.contact || ''
            formData.contact_phone = quotation.customer.phone || ''
            // 将客户添加到选项中
            customerOptions.value = [{
              id: quotation.customer.id,
              name: quotation.customer.name,
              contact: quotation.customer.contact || '',
              phone: quotation.customer.phone || ''
            }]
          }

          // 预填充产品明细
          if (quotation.items && Array.isArray(quotation.items)) {
            formData.order_products = quotation.items.map((item: any) => ({
              product_id: item.product_id || 0,
              product_specification_id: item.product_specification_id || 0,
              product_name: item.product_name || '',
              product_model: item.product_model || '',
              specification: typeof item.specification === 'string' ? item.specification : (item.specification?.specification || ''),
              unit: item.product_unit || item.unit || '',
              quantity: item.quantity || 1,
              unit_price: item.unit_price || 0,
              discount: item.discount || 0,
              tax_rate: item.tax_rate || 13,
              total_price: 0,
              notes: item.notes || '',
              source: quotation.quotation_number, // 标记来源
              source_type: 'quotation', // 来源类型
              source_id: quotation.id, // 来源报价单ID
              source_display: quotation.quotation_number // 来源显示
            }))

            // 重新计算每个产品的总价
            formData.order_products.forEach((product: any) => {
              calculateRowTotal(product)
            })
          }

          ElMessage.success('已从报价单预填充数据，请完善信息后保存')
        }
      } catch (error) {
        console.error('获取报价单详情失败:', error)
        ElMessage.error('获取报价单详情失败')
      } finally {
        loading.value = false
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.order-edit {
  .header-card {
    .form-title {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
    }

    .flex-between {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .order-form {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: bold;
      }

      .header-actions {
        .el-button {
          margin-left: 10px;
        }
      }
    }

    .flex-between {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
    }

    .quotation-container {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .quotation-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      min-height: 32px;
      align-items: center;
    }

    .quotation-tag {
      margin: 0;
    }

    .quotation-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .empty-data {
      padding: 40px 0;
      text-align: center;
    }

    .order-summary {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .summary-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .highlight {
          font-size: 18px;
          font-weight: bold;
          color: #e6a23c;
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }

  .pagination-info {
    color: #606266;
    font-size: 14px;
  }

  .product-search {
    .el-input-group__append {
      padding: 0;
    }
  }

  .excel-upload-tip {
    margin-bottom: 20px;

    .upload-instructions {
      margin-top: 10px;

      p {
        margin: 8px 0;
        font-size: 14px;

        strong {
          color: #409eff;
        }
      }

      ul {
        margin: 8px 0;
        padding-left: 20px;

        li {
          margin: 4px 0;
          font-size: 13px;
          color: #666;
        }
      }
    }
  }

  .excel-uploader {
    .el-upload__tip {
      margin-top: 10px;
      color: #606266;
      font-size: 12px;
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .mt-20 {
    margin-top: 20px;
  }

  .mb-10 {
    margin-bottom: 10px;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }



  /* 已存在规格样式 */
  .existing-spec-text {
    color: #909399;
  }

  .existing-spec-tag {
    font-size: 10px;
    height: 16px;
    line-height: 14px;
  }

  .ml-5 {
    margin-left: 5px;
  }

  /* 表格行点击样式 */
  :deep(.el-table__body tr) {
    cursor: pointer;
  }

  :deep(.el-table__body tr:hover) {
    background-color: #f5f7fa;
  }

  /* 表格文本选择优化 */
  :deep(.el-table__body tr td) {
    user-select: text; /* 允许文本选择 */
  }

  :deep(.el-table__body tr td span) {
    user-select: text; /* 确保span内的文本可以被选择 */
  }

  /* 只读字段样式 */
  .readonly-field {
    padding: 8px 12px;
    background-color: #f5f7fa;
    color: #909399;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    min-height: 32px;
    display: flex;
    align-items: center;
    cursor: not-allowed;
    user-select: text;
  }
}
</style>
