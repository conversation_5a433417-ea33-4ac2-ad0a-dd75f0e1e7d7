<template>
  <div class="balance-transaction-list">
    <!-- 搜索筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select
            v-model="searchForm.customer_id"
            placeholder="选择客户"
            clearable
            filterable
            @change="handleSearch"
          >
            <el-option
              v-for="customer in customers"
              :key="customer.id"
              :label="customer.name"
              :value="customer.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.transaction_type"
            placeholder="交易类型"
            clearable
            @change="handleSearch"
          >
            <el-option label="充值" value="充值" />
            <el-option label="消费" value="消费" />
            <el-option label="退款" value="退款" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column prop="customer_name" label="客户名称" min-width="180" />
      
      <el-table-column prop="transaction_type" label="交易类型" width="100">
        <template #default="{ row }">
          <el-tag
            :type="getTransactionTypeTag(row.transaction_type)"
            size="small"
          >
            {{ row.transaction_type }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="amount" label="交易金额" width="150">
        <template #default="{ row }">
          <span
            class="amount"
            :class="{
              'positive': row.amount > 0,
              'negative': row.amount < 0
            }"
          >
            {{ formatCurrency(row.amount) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="balance_before" label="交易前余额" width="150">
        <template #default="{ row }">
          {{ formatCurrency(row.balance_before) }}
        </template>
      </el-table-column>

      <el-table-column prop="balance_after" label="交易后余额" width="150">
        <template #default="{ row }">
          <span class="balance-after">
            {{ formatCurrency(row.balance_after) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="description" label="交易描述" min-width="200" />

      <el-table-column prop="reference_type" label="关联类型" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.reference_type" type="info" size="small">
            {{ row.reference_type }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="交易时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.per_page"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { paymentApi } from '@/api/payment'
import type { BalanceTransaction } from '@/types/payment'
import { customerApi } from '@/api/customer'

// 响应式数据
const loading = ref(false)
const tableData = ref<BalanceTransaction[]>([])
const customers = ref<any[]>([])
const dateRange = ref<[string, string] | null>(null)

// 搜索表单
const searchForm = reactive({
  customer_id: undefined,
  transaction_type: '',
  start_date: '',
  end_date: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

// 格式化货币
const formatCurrency = (amount: number): string => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 格式化日期时间
const formatDateTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取交易类型标签样式
const getTransactionTypeTag = (type: string): string => {
  switch (type) {
    case '充值':
      return 'success'
    case '消费':
      return 'warning'
    case '退款':
      return 'info'
    default:
      return ''
  }
}

// 加载客户列表
const loadCustomers = async () => {
  try {
    const response = await customerApi.getList({ per_page: 1000 })
    customers.value = response.data?.data || []
  } catch (error) {
    console.error('加载客户列表失败:', error)
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      per_page: pagination.per_page,
      ...searchForm
    }

    const response = await paymentApi.getBalanceTransactions(params)
    tableData.value = response.data || []
    pagination.total = response.pagination?.total || 0
  } catch (error) {
    console.error('加载余额交易记录失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 日期范围改变
const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.start_date = dates[0]
    searchForm.end_date = dates[1]
  } else {
    searchForm.start_date = ''
    searchForm.end_date = ''
  }
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.per_page = size
  pagination.page = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 刷新数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  refresh
})

// 组件挂载时加载数据
onMounted(() => {
  loadCustomers()
  loadData()
})
</script>

<style scoped>
.balance-transaction-list {
  padding: 0;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.amount {
  font-weight: bold;
}

.amount.positive {
  color: #67C23A;
}

.amount.negative {
  color: #F56C6C;
}

.balance-after {
  font-weight: 500;
  color: #409EFF;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>
