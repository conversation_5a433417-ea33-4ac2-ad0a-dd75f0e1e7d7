"""
对账单管理API单元测试
"""
import pytest
import json
from datetime import datetime, timedelta
from decimal import Decimal
from app.models.customer import Customer, CustomerDeliveryAddress
from app.models.product import Product, ProductCategory, ProductSpecification
from app.models.order import Order, OrderProduct, DeliveryNote, DeliveryNoteItem
from app.models.finance import Statement, StatementDeliveryNote, Receivable


class TestStatementsAPI:
    """对账单管理API测试类"""
    
    def test_get_statements_empty(self, client, db_session):
        """测试获取空的对账单列表"""
        response = client.get('/api/v1/statements')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取对账单列表成功'
        assert data['data']['items'] == []
        assert 'pagination' in data['data']
    
    def test_get_statements_with_data(self, client, db_session, sample_customer, sample_product):
        """测试获取有数据的对账单列表"""
        # 创建对账单
        statement = Statement(
            statement_number='ST20240101001',
            customer_id=sample_customer.id,
            statement_date=datetime.now().date(),
            status='草稿',
            total_amount=Decimal('1500.00')
        )
        db_session.add(statement)
        db_session.commit()
        
        response = client.get('/api/v1/statements')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']['items']) == 1
        assert data['data']['items'][0]['statement_number'] == 'ST20240101001'
        assert data['data']['items'][0]['status'] == '草稿'
    
    def test_get_statement_detail(self, client, db_session, sample_customer, sample_product):
        """测试获取对账单详情"""
        # 创建对账单
        statement = Statement(
            statement_number='ST20240101001',
            customer_id=sample_customer.id,
            statement_date=datetime.now().date(),
            status='草稿',
            total_amount=Decimal('1500.00')
        )
        db_session.add(statement)
        db_session.commit()
        
        response = client.get(f'/api/v1/statements/{statement.id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['statement_number'] == 'ST20240101001'
        assert data['data']['customer_name'] == sample_customer.name
    
    def test_get_statement_not_found(self, client, db_session):
        """测试获取不存在的对账单"""
        response = client.get('/api/v1/statements/999')
        assert response.status_code == 404
        
        data = response.get_json()
        assert data['code'] == 404
        assert '对账单不存在' in data['message']
    
    def test_create_statement(self, client, db_session, sample_customer, sample_product):
        """测试创建对账单"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='已完成',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        # 创建产品规格
        spec = ProductSpecification.query.filter_by(product_id=sample_product.id).first()
        if not spec:
            spec = ProductSpecification(
                product_id=sample_product.id,
                specification='标准规格',
                cost_price=80.00,
                suggested_price=100.00,
                tax_rate=13.0
            )
            db_session.add(spec)
            db_session.flush()
        
        # 创建订单产品
        order_product = OrderProduct(
            order_id=order.id,
            product_specification_id=spec.id,
            quantity=10,
            unit_price=100.00,
            total_price=1000.00
        )
        db_session.add(order_product)
        db_session.flush()
        
        # 创建发货单
        delivery_note = DeliveryNote(
            delivery_number='DN20240101001',
            order_id=order.id,
            delivery_date=datetime.now(),
            recipient_name='测试收货人',
            recipient_phone='13800138000',
            delivery_address_snapshot='福建省泉州市丰泽区测试送货地址',
            status='已签收'
        )
        db_session.add(delivery_note)
        db_session.flush()
        
        # 创建发货单项目
        delivery_item = DeliveryNoteItem(
            delivery_note_id=delivery_note.id,
            order_product_id=order_product.id,
            product_specification_id=spec.id,
            quantity=8,
            product_name=sample_product.name,
            product_model=sample_product.model,
            product_unit=sample_product.unit,
            specification_description=spec.specification
        )
        db_session.add(delivery_item)
        db_session.commit()
        
        # 创建对账单数据
        statement_data = {
            'customer_id': sample_customer.id,
            'statement_date': datetime.now().date().isoformat(),
            'due_date': (datetime.now().date() + timedelta(days=30)).isoformat(),
            'status': '草稿',
            'notes': '测试对账单',
            'delivery_note_ids': [delivery_note.id]
        }
        
        response = client.post('/api/v1/statements', 
                             data=json.dumps(statement_data),
                             content_type='application/json')
        assert response.status_code == 201
        
        data = response.get_json()
        assert data['code'] == 201
        assert data['message'] == '对账单创建成功'
        assert 'ST' in data['data']['statement_number']
        assert len(data['data']['delivery_notes']) == 1
    
    def test_create_statement_invalid_customer(self, client, db_session):
        """测试创建对账单时客户不存在"""
        statement_data = {
            'customer_id': 999,
            'statement_date': datetime.now().date().isoformat(),
            'status': '草稿',
            'delivery_note_ids': [1]
        }
        
        response = client.post('/api/v1/statements',
                             data=json.dumps(statement_data),
                             content_type='application/json')
        assert response.status_code == 404
        
        data = response.get_json()
        assert '客户不存在' in data['message']
    
    def test_update_statement_status(self, client, db_session, sample_customer, sample_product):
        """测试更新对账单状态"""
        # 创建对账单
        statement = Statement(
            statement_number='ST20240101001',
            customer_id=sample_customer.id,
            statement_date=datetime.now().date(),
            status='草稿',
            total_amount=Decimal('1500.00')
        )
        db_session.add(statement)
        db_session.commit()
        
        # 更新状态
        status_data = {
            'status': '待确认',
            'notes': '提交确认'
        }
        
        response = client.put(f'/api/v1/statements/{statement.id}/status',
                            data=json.dumps(status_data),
                            content_type='application/json')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['status'] == '待确认'
    
    def test_confirm_statement(self, client, db_session, sample_customer, sample_product):
        """测试确认对账单"""
        # 创建对账单
        statement = Statement(
            statement_number='ST20240101001',
            customer_id=sample_customer.id,
            statement_date=datetime.now().date(),
            due_date=datetime.now().date() + timedelta(days=30),
            status='待确认',
            total_amount=Decimal('1500.00')
        )
        db_session.add(statement)
        db_session.commit()
        
        response = client.put(f'/api/v1/statements/{statement.id}/confirm')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['status'] == '已确认'
        
        # 检查是否创建了应收款记录
        receivable = Receivable.query.filter_by(statement_id=statement.id).first()
        assert receivable is not None
        assert receivable.amount == statement.total_amount
    
    def test_get_statement_statuses(self, client, db_session):
        """测试获取对账单状态列表"""
        response = client.get('/api/v1/statements/statuses')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert 'statuses' in data['data']
        assert 'transitions' in data['data']
        assert 'descriptions' in data['data']
        assert '草稿' in data['data']['statuses']
        assert '待确认' in data['data']['statuses']
    
    def test_get_available_delivery_notes(self, client, db_session, sample_customer, sample_product):
        """测试获取可用发货单"""
        # 创建测试数据
        delivery_address = CustomerDeliveryAddress(
            customer_id=sample_customer.id,
            province='福建省',
            city='泉州市',
            district='丰泽区',
            detailed_address='测试送货地址',
            contact_person='测试联系人',
            contact_phone='13800138000',
            is_default=True
        )
        db_session.add(delivery_address)
        db_session.flush()
        
        order = Order(
            order_number='ORD20240101001',
            customer_id=sample_customer.id,
            project_name='测试项目',
            status='已完成',
            total_amount=1500.00,
            delivery_address_id=delivery_address.id
        )
        db_session.add(order)
        db_session.flush()
        
        # 创建已签收的发货单
        delivery_note = DeliveryNote(
            delivery_number='DN20240101001',
            order_id=order.id,
            delivery_date=datetime.now(),
            recipient_name='测试收货人',
            recipient_phone='13800138000',
            delivery_address_snapshot='福建省泉州市丰泽区测试送货地址',
            status='已签收'
        )
        db_session.add(delivery_note)
        db_session.commit()
        
        response = client.get(f'/api/v1/statements/available-delivery-notes?customer_id={sample_customer.id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert len(data['data']) == 1
        assert data['data'][0]['delivery_number'] == 'DN20240101001'
    
    def test_get_statement_statistics(self, client, db_session, sample_customer, sample_product):
        """测试获取对账单统计信息"""
        # 创建不同状态的对账单
        statement1 = Statement(
            statement_number='ST20240101001',
            customer_id=sample_customer.id,
            statement_date=datetime.now().date(),
            status='草稿',
            total_amount=Decimal('1000.00')
        )
        statement2 = Statement(
            statement_number='ST20240101002',
            customer_id=sample_customer.id,
            statement_date=datetime.now().date(),
            status='已确认',
            total_amount=Decimal('2000.00')
        )
        db_session.add_all([statement1, statement2])
        db_session.commit()
        
        response = client.get('/api/v1/statements/statistics')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['total_count'] == 2
        assert data['data']['total_amount'] == 3000.0
        assert 'status_statistics' in data['data']
