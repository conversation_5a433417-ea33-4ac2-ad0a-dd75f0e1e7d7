import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'
import type { App } from 'vue'

// 创建Pinia实例
const pinia = createPinia()

// 配置持久化插件
pinia.use(
  createPersistedState({
    // 默认存储到localStorage
    storage: localStorage,
    // 可以配置序列化方式
    serializer: {
      serialize: JSON.stringify,
      deserialize: JSON.parse,
    },
    // 默认key前缀
    key: (id) => `emb-${id}`,
  })
)

// 安装Pinia
export function setupStore(app: App) {
  app.use(pinia)
}

// 导出所有store
export { useUserStore } from './modules/user'
export { useAppStore } from './modules/app'
export { useCacheStore } from './modules/cache'

// 导出类型
export type { UserInfo, LoginForm } from './modules/user'
export type { AppSettings, ThemeMode, Language, LayoutMode } from './modules/app'
export type { CacheItem, CacheConfig } from './modules/cache'

export { pinia }
export default pinia
