import{u as e,b as t,r as a,d as r,L as o,w as n,e as u,f as l,M as d,o as s,c as i,i as c,h as m,N as p,g as f,U as _,I as g,a as h,t as b,k as y,l as v,F as A,m as w,j as k,W as N,ak as M}from"./index-3d4c440c.js";import{h as I,e as D,i as V,j as O,f as x}from"./finance-789ce3e6.js";import{g as T}from"./return-614d16fd.js";import{_ as X}from"./_plugin-vue_export-helper-1b428a4d.js";const $={class:"refund-record-edit"},F={class:"card-header"},P={class:"left"},S={class:"page-title"},j={class:"right"},U={class:"bank-account-option"},q={class:"bank-name"},C={class:"account-info"},Y={class:"account-number"},R={key:0,class:"form-item-help"},E={key:2,class:"form-section-title"},H={key:4,class:"form-section-title"},B=X({__name:"RefundRecordEdit",setup(X){const B=e(),z=t(),L=a(null),J=a(!1),K=a(!1),W=a(!1),G=r((()=>void 0!==B.params.id)),Q=r((()=>!0===B.meta.isView)),Z=o({id:"",refundNo:G.value?"":`TK${(new Date).getFullYear()}${String((new Date).getMonth()+1).padStart(2,"0")}${String((new Date).getDate()).padStart(2,"0")}${String(Math.floor(1e4*Math.random())).padStart(4,"0")}`,returnOrderId:"",returnOrderNo:"",customerId:"",customerName:"",refundDate:(new Date).toISOString().split("T")[0],refundMethod:"",amount:0,bankAccount:"",originalPaymentMethod:"",notes:"",status:"pending",transactionProof:[]}),ee={returnOrderId:[{required:!0,message:"请选择关联退货单",trigger:"change"}],refundDate:[{required:!0,message:"请选择退款日期",trigger:"change"}],refundMethod:[{required:!0,message:"请选择退款方式",trigger:"change"}],amount:[{required:!0,message:"请输入退款金额",trigger:"blur"},{type:"number",min:.01,message:"退款金额必须大于0",trigger:"blur"}],bankAccount:[{required:!0,message:"请选择退款账户",trigger:"change",validator:(e,t,a)=>{"bank_transfer"!==Z.refundMethod||t?a():a(new Error("请选择退款账户"))}}],originalPaymentMethod:[{required:!0,message:"请填写原付款方式",trigger:"blur",validator:(e,t,a)=>{"original_channel"!==Z.refundMethod||t?a():a(new Error("请填写原付款方式"))}}]},te=a([]),ae=a([]),re=o({returnOrderNo:"",totalAmount:0,refundedAmount:0,pendingAmount:0,status:"",statusText:"",createTime:""}),oe=a([]),ne=a([]),ue=async e=>{if(!(e.length<2)){W.value=!0;try{let a;console.log("搜索可退款退货单，关键词:",e);try{a=await V({keyword:e}),console.log("可退款退货单响应:",a),a&&a.data&&Array.isArray(a.data)&&(console.log("退货单原始数据:",JSON.stringify(a.data)),console.log("退货单状态统计:",a.data.reduce(((e,t)=>(e[t.status]=(e[t.status]||0)+1,e)),{})))}catch(t){console.warn("API调用失败，使用模拟数据:",t);a={data:[{id:"1",return_order_number:"TH20250601001",customer_name:"北京某某科技有限公司",customer_id:"1",total_amount:5e4,refunded_amount:2e4,pending_amount:3e4,status:"approved",status_text:"已确认"},{id:"2",return_order_number:"TH20250602001",customer_name:"上海某某贸易有限公司",customer_id:"2",total_amount:32e3,refunded_amount:0,pending_amount:32e3,status:"approved",status_text:"已确认"},{id:"3",return_order_number:"TH20250603001",customer_name:"广州某某建设工程有限公司",customer_id:"3",total_amount:68e3,refunded_amount:15e3,pending_amount:53e3,status:"approved",status_text:"已确认"}].filter((t=>t.return_order_number.includes(e)||t.customer_name.includes(e)))}}a&&a.data&&Array.isArray(a.data)?(te.value=a.data.map((e=>({id:e.id,returnOrderNo:e.return_order_number,customerName:e.customer_name,customerId:e.customer_id,totalAmount:parseFloat(e.total_amount)||0,refundedAmount:parseFloat(e.refunded_amount)||0,pendingAmount:parseFloat(e.pending_amount)||0,status:e.status,statusText:e.status_text||e.status}))),console.log("退货单选项已更新:",te.value)):(te.value=[],console.warn("搜索退货单响应中没有找到有效数据"))}catch(a){console.error("搜索退货单失败:",a),a.response&&(console.error("错误响应状态:",a.response.status),console.error("错误响应数据:",a.response.data)),_.error("搜索退货单失败"),te.value=[]}finally{W.value=!1}}},le=async e=>{if(e){J.value=!0;try{const t=te.value.find((t=>t.id===e));if(!t)return void _.error("未找到选择的退货单");console.log("选中的退货单信息:",t),Z.customerId=t.customerId,Z.customerName=t.customerName,Z.returnOrderNo=t.returnOrderNo,Object.assign(re,{returnOrderNo:t.returnOrderNo,totalAmount:0,refundedAmount:0,pendingAmount:0,status:"",statusText:"",createTime:""}),await se(e),await ie(e),await de(),re.pendingAmount<=0&&t.pendingAmount>0&&(console.log(`使用选择项中的待退款金额: ${t.pendingAmount}`),re.pendingAmount=t.pendingAmount,re.totalAmount<=0&&(re.totalAmount=t.totalAmount||t.pendingAmount),re.refundedAmount<=0&&t.refundedAmount>0&&(re.refundedAmount=t.refundedAmount)),Z.amount=re.pendingAmount,console.log("设置预填充金额:",Z.amount)}catch(t){console.error("处理退货单变更失败:",t),_.error("处理退货单变更失败")}finally{J.value=!1}}},de=async()=>{try{let t;console.log("获取系统银行账户配置");try{t=await g({url:"/api/v1/company-bank-accounts",method:"get"}),console.log("公司银行账户响应:",t)}catch(e){console.warn("API调用失败，使用模拟数据:",e);t={data:[{id:"1",bank_name:"中国建设银行",account_number:"6222XXXXXXXXXXXX1234",account_name:"北京某某科技有限公司",is_default:!0},{id:"2",bank_name:"中国工商银行",account_number:"6222XXXXXXXXXXXX5678",account_name:"北京某某科技有限公司",is_default:!1}]}}if(t&&t.data&&Array.isArray(t.data)){ae.value=t.data.map((e=>({id:e.id,bankName:e.bank_name,accountNumber:e.account_number,accountName:e.account_name,isDefault:e.is_default}))),console.log("系统银行账户已更新:",ae.value);const e=ae.value.find((e=>e.isDefault));e&&"bank_transfer"===Z.refundMethod&&!Z.bankAccount&&(Z.bankAccount=e.id)}else ae.value=[],console.warn("银行账户响应中没有找到有效数据")}catch(t){console.error("获取银行账户失败:",t),t.response&&(console.error("错误响应状态:",t.response.status),console.error("错误响应数据:",t.response.data)),_.error("获取系统银行账户失败"),ae.value=[]}},se=async e=>{try{let r;console.log("获取退货单详情，ID:",e);try{if(r=await T(e),console.log("退货单详情响应:",r),r&&r.data){const a=r.data;re.returnOrderNo=a.return_number||"",re.status=a.status||"",re.statusText=a.status_text||a.status||"",re.createTime=a.created_at||"";let o=0;if(a.items&&Array.isArray(a.items))for(const e of a.items){const t=parseFloat(e.quantity)||0;let a=0;void 0!==e.unit_price?a=parseFloat(e.unit_price):e.order_product&&e.order_product.unit_price?a=parseFloat(e.order_product.unit_price):e.total_price&&(a=parseFloat(e.total_price)/t);const r=t*a;o+=r,console.log(`退货项 ${e.id||"未知"}: 数量=${t}, 单价=${a}, 金额=${r}`)}else void 0!==a.total_amount&&(o=parseFloat(a.total_amount));re.totalAmount=o;try{const t=await D({return_order_id:e,status:"refunded",per_page:100});console.log("退款记录响应:",t);let a=0;if(t&&t.data){const e=Array.isArray(t.data)?t.data:t.data.items||t.data.list||[];Array.isArray(e)&&e.forEach((e=>{a+=parseFloat(e.amount)||0}))}re.refundedAmount=a}catch(t){console.warn(`获取退款记录失败: ${t}`),re.refundedAmount=0}re.pendingAmount=Math.max(0,re.totalAmount-re.refundedAmount),console.log("计算后的退货单金额:",{totalAmount:re.totalAmount,refundedAmount:re.refundedAmount,pendingAmount:re.pendingAmount}),Z.amount&&0!==Z.amount||(Z.amount=re.pendingAmount)}}catch(a){console.warn("API调用失败，使用模拟数据:",a);r={data:{id:e,return_number:re.returnOrderNo||`RET${e}`,customer_id:Z.customerId||"1",customer_name:Z.customerName||"模拟客户",refunded_amount:0,status:"approved",status_text:"已确认",created_at:(new Date).toISOString(),items:[{id:1,quantity:2,unit_price:1e3,product_name:"单偏心对夹式蝶阀",product_model:"hjk5456",specification_description:"DN50"},{id:2,quantity:3,unit_price:1500,product_name:"旋流防止器",product_model:"gf527",specification_description:"DN50"}]}};let t=0;const o=0;if(r.data.items&&Array.isArray(r.data.items))for(const e of r.data.items){const a=parseFloat(e.quantity)||0,r=parseFloat(e.unit_price)||0;t+=a*r,console.log(`模拟退货项 ${e.id}: 数量=${a}, 单价=${r}, 金额=${a*r}`)}re.totalAmount=t,re.refundedAmount=o,re.pendingAmount=t-o,Z.amount&&0!==Z.amount||(Z.amount=re.pendingAmount)}if(!r||!r.data)return void _.error("获取退货单详情失败");console.log("更新后的退货单信息:",re)}catch(r){console.error("获取退货单信息失败:",r),r.response&&(console.error("错误响应状态:",r.response.status),console.error("错误响应数据:",r.response.data)),_.error("获取退货单信息失败")}},ie=async e=>{try{console.log("获取退款历史，退货单ID:",e);const t=await D({return_order_id:e,per_page:100});console.log("退款历史响应:",t),t&&t.data&&Array.isArray(t.data)?(oe.value=t.data.map((e=>({id:e.id,refundNo:e.refund_no||e.refund_number||"",amount:parseFloat(e.amount)||0,refundMethod:e.refund_method||"",refundDate:e.refund_date||"",createBy:e.created_by||"",status:e.status||""}))),console.log("退款历史记录已更新:",oe.value)):(console.warn("未找到退款历史记录，使用空数组"),oe.value=[])}catch(t){console.error("获取退款历史失败:",t),t.response&&(console.error("错误响应状态:",t.response.status),console.error("错误响应数据:",t.response.data)),_.warning("获取退款历史失败，使用空数据"),oe.value=[]}},ce=async e=>{try{const{file:t}=e;if(!["image/jpeg","image/png","application/pdf"].includes(t.type))return void _.error("只支持jpg、png、pdf格式文件");if(t.size>10485760)return void _.error("文件大小不能超过10MB");_.info("正在上传文件，请稍候..."),await new Promise((e=>setTimeout(e,1e3)));const a=Date.now().toString();ne.value.push({name:t.name,url:URL.createObjectURL(t),id:a}),Z.transactionProof.push(a),_.success("文件上传成功")}catch(t){console.error("文件上传失败:",t),_.error("文件上传失败")}},me=async(e=!1)=>{var t;K.value=!0;try{if(await L.value.validate(),Z.amount>re.pendingAmount)return _.warning(`退款金额不能超过待退款金额：${be(re.pendingAmount)}`),void(K.value=!1);const o={return_order_id:Z.returnOrderId,refund_date:Z.refundDate,amount:Z.amount,refund_method:Z.refundMethod,notes:Z.notes||""};"original_channel"===Z.refundMethod&&Z.originalPaymentMethod&&(o.reference_number=Z.originalPaymentMethod),"bank_transfer"===Z.refundMethod&&Z.bankAccount&&(o.bank_account=String(Z.bankAccount)),console.log("准备保存退款记录:",o);try{const t=await O(o);if(console.log("创建退款记录响应:",t),t&&0===t.code){if(e&&t.data&&t.data.id)try{console.log("确认退款，ID:",t.data.id);const e=await x(t.data.id,"已退款");console.log("确认退款响应:",e),e&&0===e.code?_.success(G.value?"退款记录修改并确认成功":"退款记录创建并确认成功"):_.warning("退款记录已创建，但确认操作失败")}catch(a){console.error("确认退款失败:",a),_.warning("退款记录已创建，但确认操作失败")}else _.success(G.value?"退款记录修改成功":"退款记录创建成功");z.push("/refund-record")}else{const e=(null==t?void 0:t.message)||"保存失败，请重试";_.error(e)}}catch(r){if(console.error("API调用失败:",r),r.response)if(console.error("错误状态码:",r.response.status),console.error("错误响应头:",r.response.headers),console.error("错误响应数据:",JSON.stringify(r.response.data)),r.response.data&&r.response.data.errors){const e=r.response.data.errors;for(const t in e)_.error(`${t}: ${e[t].join(", ")}`)}else _.error((null==(t=r.response.data)?void 0:t.message)||"保存退款记录失败");else r.request?(console.error("请求已发送但未收到响应"),_.error("网络错误，请检查网络连接")):(console.error("请求配置错误:",r.message),_.error("请求错误: "+r.message))}}catch(o){console.error("表单验证失败:",o),_.error("表单验证失败，请检查输入")}finally{K.value=!1}},pe=()=>{me(!1)},fe=()=>{N.confirm("确认退款后将无法修改退款信息，是否继续？","确认提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((()=>{me(!0)})).catch((()=>{}))},_e=()=>{z.push("/refund-record")},ge=e=>({pending:"待处理",refunded:"已退款",canceled:"已取消"}[e]||e),he=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN")},be=e=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(e),ye=async()=>{W.value=!0;try{let t;console.log("加载默认可退款退货单列表");try{t=await V(),console.log("可退款退货单响应:",t),t&&t.data&&Array.isArray(t.data)&&(console.log("获取到退货单数量:",t.data.length),console.log("退货单状态统计:",t.data.reduce(((e,t)=>(e[t.status]=(e[t.status]||0)+1,e)),{})))}catch(e){console.warn("API调用失败，使用模拟数据:",e),t={data:[{id:"1",return_order_number:"TH20250601001",customer_name:"北京某某科技有限公司",customer_id:"1",total_amount:5e4,refunded_amount:2e4,pending_amount:3e4,status:"approved",status_text:"已确认"},{id:"2",return_order_number:"TH20250602001",customer_name:"上海某某贸易有限公司",customer_id:"2",total_amount:32e3,refunded_amount:0,pending_amount:32e3,status:"approved",status_text:"已确认"},{id:"3",return_order_number:"TH20250603001",customer_name:"广州某某建设工程有限公司",customer_id:"3",total_amount:68e3,refunded_amount:15e3,pending_amount:53e3,status:"approved",status_text:"已确认"}]}}t&&t.data&&Array.isArray(t.data)?(te.value=t.data.map((e=>({id:e.id,returnOrderNo:e.return_order_number,customerName:e.customer_name,customerId:e.customer_id,totalAmount:parseFloat(e.total_amount)||0,refundedAmount:parseFloat(e.refunded_amount)||0,pendingAmount:parseFloat(e.pending_amount)||0,status:e.status,statusText:e.status_text||e.status}))),console.log("退货单选项已更新:",te.value)):(te.value=[],console.warn("获取可退款退货单响应中没有找到有效数据"))}catch(t){console.error("加载默认退货单列表失败:",t),t.response&&(console.error("错误响应状态:",t.response.status),console.error("错误响应数据:",t.response.data)),_.error("加载退货单列表失败"),te.value=[]}finally{W.value=!1}},ve=e=>{e&&0===te.value.length&&ye()},Ae=e=>{console.log("退款方式变更为:",e),Z.bankAccount="",Z.originalPaymentMethod="","bank_transfer"===e&&Z.customerId&&de(Z.customerId)};return n((()=>Z.refundMethod),(e=>{Ae(e)})),u((()=>{console.log("RefundRecordEdit组件挂载，路由名称:",B.name,"路由参数:",B.params);const e=B.params.id;e?(console.log("编辑或查看模式，ID:",e),(async e=>{J.value=!0;try{console.log("获取退款记录详情，ID:",e);const t=await I(e);if(console.log("退款记录详情响应:",t),!t||!t.data)return void _.error("获取退款记录详情失败");const a=t.data;Z.id=a.id,Z.refundNo=a.refund_number||`TK${a.id}`,Z.refundDate=a.refund_date,Z.refundMethod=a.refund_method,Z.amount=parseFloat(a.amount)||0,Z.notes=a.notes,Z.status=a.status,Z.returnOrderId=a.return_order_id,Z.returnOrderNo=a.return_order_number||"",Z.customerName=a.customer_name||"",Z.customerId=a.customer_id,"bank_transfer"===a.refund_method&&(Z.bankAccount=a.bank_account),"original_channel"===a.refund_method&&(Z.originalPaymentMethod=a.original_payment_method||""),a.return_order_id&&(await se(a.return_order_id),await ie(a.return_order_id),a.customer_id&&await de(a.customer_id))}catch(t){console.error("获取退款记录详情失败:",t),t.response&&(console.error("错误响应状态:",t.response.status),console.error("错误响应数据:",t.response.data)),_.error("获取退款记录详情失败")}finally{J.value=!1}})(e)):(console.log("新增模式"),Z.refundDate=(new Date).toISOString().split("T")[0],ye())})),(e,t)=>{const a=l("el-tag"),r=l("el-icon"),o=l("el-button"),n=l("el-card"),u=l("el-input"),_=l("el-form-item"),g=l("el-col"),N=l("el-date-picker"),I=l("el-row"),D=l("el-option"),V=l("el-select"),O=l("el-input-number"),x=l("el-descriptions-item"),T=l("el-descriptions"),X=l("el-table-column"),B=l("el-table"),z=l("el-upload"),de=l("el-form"),se=d("loading");return s(),i("div",$,[c(n,{class:"header-card mb-20"},{default:m((()=>[h("div",F,[h("div",P,[h("h2",S,b(G.value?"编辑退款记录":"新增退款记录"),1),c(a,{type:"info"},{default:m((()=>[y(b(G.value?"修改已有退款记录信息":"创建新的退款记录"),1)])),_:1})]),h("div",j,[c(o,{onClick:_e},{default:m((()=>[c(r,null,{default:m((()=>[c(v(M))])),_:1}),t[10]||(t[10]=y(" 返回列表 "))])),_:1})])])])),_:1}),p((s(),f(n,null,{default:m((()=>[c(de,{ref_key:"formRef",ref:L,model:Z,rules:ee,"label-width":"120px",class:"refund-form","status-icon":""},{default:m((()=>[t[17]||(t[17]=h("div",{class:"form-section-title"},"基本信息",-1)),c(I,{gutter:20},{default:m((()=>[c(g,{span:12},{default:m((()=>[c(_,{label:"退款单号",prop:"refundNo"},{default:m((()=>[G.value?(s(),f(u,{key:0,modelValue:Z.refundNo,"onUpdate:modelValue":t[0]||(t[0]=e=>Z.refundNo=e),placeholder:"系统自动生成",disabled:""},null,8,["modelValue"])):(s(),f(u,{key:1,modelValue:Z.refundNo,"onUpdate:modelValue":t[1]||(t[1]=e=>Z.refundNo=e),placeholder:"系统自动生成，可手动修改"},null,8,["modelValue"]))])),_:1})])),_:1}),c(g,{span:12},{default:m((()=>[c(_,{label:"退款日期",prop:"refundDate"},{default:m((()=>[c(N,{modelValue:Z.refundDate,"onUpdate:modelValue":t[2]||(t[2]=e=>Z.refundDate=e),type:"date",placeholder:"选择退款日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(I,{gutter:20},{default:m((()=>[c(g,{span:12},{default:m((()=>[c(_,{label:"关联退货单",prop:"returnOrderId"},{default:m((()=>[c(V,{modelValue:Z.returnOrderId,"onUpdate:modelValue":t[3]||(t[3]=e=>Z.returnOrderId=e),placeholder:"请选择关联退货单",filterable:"",remote:"","remote-method":ue,loading:W.value,style:{width:"100%"},disabled:Q.value||G.value&&"pending"!==Z.status,onChange:le,onVisibleChange:ve},{default:m((()=>[(s(!0),i(A,null,w(te.value,(e=>(s(),f(D,{key:e.id,label:`${e.returnOrderNo} - ${e.customerName}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading","disabled"])])),_:1})])),_:1}),c(g,{span:12},{default:m((()=>[c(_,{label:"客户名称",prop:"customerName"},{default:m((()=>[c(u,{modelValue:Z.customerName,"onUpdate:modelValue":t[4]||(t[4]=e=>Z.customerName=e),placeholder:"选择退货单后自动填充",disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(I,{gutter:20},{default:m((()=>[c(g,{span:12},{default:m((()=>[c(_,{label:"退款方式",prop:"refundMethod"},{default:m((()=>[c(V,{modelValue:Z.refundMethod,"onUpdate:modelValue":t[5]||(t[5]=e=>Z.refundMethod=e),placeholder:"请选择退款方式",style:{width:"100%"},disabled:Q.value,onChange:Ae},{default:m((()=>[c(D,{label:"银行转账",value:"bank_transfer"}),c(D,{label:"现金",value:"cash"}),c(D,{label:"原支付渠道",value:"original_channel"}),c(D,{label:"其他",value:"other"})])),_:1},8,["modelValue","disabled"])])),_:1})])),_:1}),c(g,{span:12},{default:m((()=>[c(_,{label:"退款金额",prop:"amount"},{default:m((()=>[c(O,{modelValue:Z.amount,"onUpdate:modelValue":t[6]||(t[6]=e=>Z.amount=e),precision:2,step:100,min:0,style:{width:"100%"},placeholder:"输入退款金额"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),"bank_transfer"===Z.refundMethod?(s(),f(I,{key:0,gutter:20},{default:m((()=>[c(g,{span:24},{default:m((()=>[c(_,{label:"退款账户",prop:"bankAccount"},{default:m((()=>[c(V,{modelValue:Z.bankAccount,"onUpdate:modelValue":t[7]||(t[7]=e=>Z.bankAccount=e),placeholder:"请选择退款账户",style:{width:"100%"},disabled:Q.value},{default:m((()=>[(s(!0),i(A,null,w(ae.value,(e=>(s(),f(D,{key:e.id,label:`${e.bankName} - ${e.accountName} (${e.accountNumber})`,value:e.id},{default:m((()=>[h("div",U,[h("div",q,b(e.bankName),1),h("div",C,[h("span",null,b(e.accountName),1),h("span",Y,b(e.accountNumber),1)]),e.isDefault?(s(),f(a,{key:0,size:"small",type:"success"},{default:m((()=>t[11]||(t[11]=[y("默认")]))),_:1})):k("",!0)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","disabled"]),ae.value.length?k("",!0):(s(),i("div",R," 该客户暂无银行账户信息，请先在客户管理中添加银行账户 "))])),_:1})])),_:1})])),_:1})):k("",!0),"original_channel"===Z.refundMethod?(s(),f(I,{key:1,gutter:20},{default:m((()=>[c(g,{span:24},{default:m((()=>[c(_,{label:"原付款方式",prop:"originalPaymentMethod"},{default:m((()=>[c(u,{modelValue:Z.originalPaymentMethod,"onUpdate:modelValue":t[8]||(t[8]=e=>Z.originalPaymentMethod=e),placeholder:"填写原付款方式信息",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})):k("",!0),Z.returnOrderId?(s(),i("div",E,"退货单信息")):k("",!0),Z.returnOrderId?(s(),f(I,{key:3,gutter:20},{default:m((()=>[c(g,{span:24},{default:m((()=>[c(T,{column:3,border:""},{default:m((()=>[c(x,{label:"退货单号"},{default:m((()=>[y(b(re.returnOrderNo),1)])),_:1}),c(x,{label:"退货金额"},{default:m((()=>[y(b(be(re.totalAmount)),1)])),_:1}),c(x,{label:"创建日期"},{default:m((()=>[y(b(he(re.createTime)),1)])),_:1}),c(x,{label:"退货状态"},{default:m((()=>{return[c(a,{type:(e=re.status,{draft:"info",pending:"warning",approved:"primary",received:"success",refunded:"success",completed:"success",canceled:"danger"}[e]||"info")},{default:m((()=>[y(b(re.statusText),1)])),_:1},8,["type"])];var e})),_:1}),c(x,{label:"已退款"},{default:m((()=>[y(b(be(re.refundedAmount)),1)])),_:1}),c(x,{label:"待退款"},{default:m((()=>[y(b(be(re.pendingAmount)),1)])),_:1})])),_:1})])),_:1})])),_:1})):k("",!0),Z.returnOrderId&&oe.value.length>0?(s(),i("div",H,"退款记录历史")):k("",!0),Z.returnOrderId&&oe.value.length>0?(s(),f(I,{key:5,gutter:20},{default:m((()=>[c(g,{span:24},{default:m((()=>[c(B,{data:oe.value,border:"",stripe:"",style:{width:"100%"}},{default:m((()=>[c(X,{prop:"refundNo",label:"退款单号","min-width":"120"}),c(X,{prop:"amount",label:"退款金额",width:"120"},{default:m((e=>[y(b(be(e.row.amount)),1)])),_:1}),c(X,{prop:"refundMethod",label:"退款方式",width:"120"},{default:m((e=>{return[y(b((t=e.row.refundMethod,{bank_transfer:"银行转账",cash:"现金",original_channel:"原支付渠道",other:"其他"}[t]||t)),1)];var t})),_:1}),c(X,{prop:"refundDate",label:"退款日期",width:"120"}),c(X,{prop:"status",label:"状态",width:"100"},{default:m((e=>{return[c(a,{type:(t=e.row.status,{pending:"warning",refunded:"success",canceled:"danger"}[t]||"info")},{default:m((()=>[y(b(ge(e.row.status)),1)])),_:2},1032,["type"])];var t})),_:1})])),_:1},8,["data"])])),_:1})])),_:1})):k("",!0),t[18]||(t[18]=h("div",{class:"form-section-title"},"附加信息",-1)),c(I,{gutter:20},{default:m((()=>[c(g,{span:24},{default:m((()=>[c(_,{label:"交易凭证",prop:"transactionProof"},{default:m((()=>[c(z,{class:"transaction-proof-upload",action:"","http-request":ce,"file-list":ne.value,limit:5,multiple:""},{tip:m((()=>t[13]||(t[13]=[h("div",{class:"el-upload__tip"},"支持jpg、png、pdf格式，单个文件不超过10MB",-1)]))),default:m((()=>[c(o,{type:"primary"},{default:m((()=>t[12]||(t[12]=[y("上传凭证")]))),_:1})])),_:1},8,["file-list"])])),_:1})])),_:1})])),_:1}),c(I,{gutter:20},{default:m((()=>[c(g,{span:24},{default:m((()=>[c(_,{label:"备注说明",prop:"notes"},{default:m((()=>[c(u,{modelValue:Z.notes,"onUpdate:modelValue":t[9]||(t[9]=e=>Z.notes=e),type:"textarea",rows:3,placeholder:"请输入退款相关的补充说明"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c(_,{class:"form-buttons"},{default:m((()=>[c(o,{onClick:_e},{default:m((()=>t[14]||(t[14]=[y("取消")]))),_:1}),c(o,{type:"primary",onClick:pe,loading:K.value},{default:m((()=>t[15]||(t[15]=[y("保存")]))),_:1},8,["loading"]),c(o,{type:"success",onClick:fe,loading:K.value},{default:m((()=>t[16]||(t[16]=[y("保存并确认退款")]))),_:1},8,["loading"])])),_:1})])),_:1},8,["model"])])),_:1})),[[se,J.value]])])}}},[["__scopeId","data-v-f7cf2dba"]]);export{B as default};
