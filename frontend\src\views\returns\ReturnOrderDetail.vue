<template>
  <div class="return-order-detail">
    <div class="page-header">
      <h2>退货单详情</h2>
      <div>
        <el-button @click="handleBack">返回</el-button>
        <el-button v-if="returnOrder?.status === '待确认'" type="primary" @click="handleEdit">编辑</el-button>
        <el-button type="info" @click="handleExport">导出退货单</el-button>
        <el-button v-if="returnOrder?.status === '待确认'" type="success" @click="handleConfirm">确认</el-button>
        <el-button v-if="returnOrder?.status === '退货中'" type="success" @click="handleComplete">完成</el-button>
      </div>
    </div>

    <div v-loading="loading" class="detail-container">
      <el-card v-if="returnOrder">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="退货单号">{{ returnOrder.return_number }}</el-descriptions-item>
          <el-descriptions-item label="退货状态">
            <el-tag :type="getStatusType(returnOrder.status)">{{ returnOrder.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="结清状态">
            <el-tag :type="getSettlementStatusType(returnOrder.settlement_status)">
              {{ returnOrder.settlement_status || '未结清' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="关联订单">{{ returnOrder.order?.order_number }}</el-descriptions-item>
          <el-descriptions-item label="项目名称">{{ returnOrder.order?.project_name }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ returnOrder.customer_name }}</el-descriptions-item>
          <el-descriptions-item label="退货日期">{{ formatDate(returnOrder.return_date) }}</el-descriptions-item>
          <el-descriptions-item label="退货原因" :span="2">{{ returnOrder.reason }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ returnOrder.notes || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(returnOrder.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="结清日期" v-if="returnOrder.settlement_status === '已结清'">
            {{ formatDate(returnOrder.settlement_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" v-if="returnOrder.settlement_status !== '已结清'">
            {{ formatDateTime(returnOrder.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card style="margin-top: 20px;" v-if="returnOrder">
        <template #header>
          <span>退货产品明细</span>
        </template>
        
        <el-table :data="returnOrder.items" border stripe>
          <el-table-column prop="product_name" label="产品名称" min-width="150" />
          <el-table-column prop="product_model" label="产品型号" min-width="120" />
          <el-table-column prop="specification_description" label="规格描述" min-width="150" />
          <el-table-column prop="quantity" label="退货数量" width="100" />
          <el-table-column prop="product_unit" label="单位" width="80" />
          <el-table-column prop="unit_price" label="单价" width="120">
            <template #default="{ row }">
              ¥{{ row.unit_price || '0.00' }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="退货金额" width="120">
            <template #default="{ row }">
              ¥{{ row.amount || '0.00' }}
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" min-width="200">
            <template #default="{ row }">
              {{ row.notes || '-' }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 总金额统计 -->
        <div class="mt-4 text-right">
          <el-row justify="end">
            <el-col :span="8">
              <div class="total-summary">
                <div class="summary-item">
                  <span class="label">退货产品数量：</span>
                  <span class="value">{{ returnOrder?.items?.length || 0 }} 种</span>
                </div>
                <div class="summary-item total-amount">
                  <span class="label">退货总金额：</span>
                  <span class="value">{{ formatTotalAmount }}</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 导出对话框 -->
    <ReturnOrderExportDialog
      v-model="exportDialogVisible"
      :return-order-id="returnOrder?.id"
      :return-order-name="returnOrder?.return_number"
      @success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ReturnOrder, ReturnOrderStatus } from '@/types/return'
import { returnApi } from '@/api/return'
import ReturnOrderExportDialog from '@/components/ReturnOrderExportDialog.vue'

const router = useRouter()
const route = useRoute()

// 状态
const loading = ref(false)
const returnOrder = ref<ReturnOrder | null>(null)

// 导出相关状态
const exportDialogVisible = ref(false)

// 格式化总金额
const formatTotalAmount = computed(() => {
  if (!returnOrder.value?.total_amount) return '¥0.00'
  const amount = typeof returnOrder.value.total_amount === 'string'
    ? parseFloat(returnOrder.value.total_amount)
    : returnOrder.value.total_amount
  return `¥${amount.toFixed(2)}`
})

// 获取退货单详情
const fetchReturnOrder = async () => {
  const id = Number(route.params.id)
  if (!id) {
    ElMessage.error('无效的退货单ID')
    router.back()
    return
  }

  loading.value = true
  try {
    const response = await returnApi.getById(id)
    returnOrder.value = response.data || response
  } catch (error) {
    console.error('获取退货单详情失败:', error)
    ElMessage.error('获取退货单详情失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (date: string | undefined) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (datetime: string | undefined) => {
  if (!datetime) return '-'
  return new Date(datetime).toLocaleString('zh-CN')
}

// 状态样式
const getStatusType = (status: ReturnOrderStatus | undefined) => {
  const statusMap: Record<ReturnOrderStatus, string> = {
    '待确认': 'warning',
    '退货中': 'primary',
    '已签收': 'success',
    '已拒绝': 'danger',
    '已取消': 'info'
  }
  return statusMap[status || '待确认']
}

// 结清状态样式
const getSettlementStatusType = (settlementStatus: string) => {
  const statusMap: Record<string, string> = {
    '未结清': 'warning',
    '已结清': 'success'
  }
  return statusMap[settlementStatus] || 'warning'
}

// 操作
const handleBack = () => {
  router.back()
}

const handleEdit = () => {
  router.push(`/returns/${returnOrder.value?.id}/edit`)
}

// 导出退货单
const handleExport = () => {
  exportDialogVisible.value = true
}

// 导出成功处理
const handleExportSuccess = () => {
  // 导出成功后的处理逻辑
}

const handleConfirm = async () => {
  if (!returnOrder.value) return
  
  try {
    await ElMessageBox.confirm('确认要确认此退货单吗？', '提示', {
      type: 'warning'
    })
    await returnApi.updateStatus(returnOrder.value.id!, '退货中')
    ElMessage.success('退货单已确认，进入退货中状态')
    fetchReturnOrder()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认退货单失败:', error)
      ElMessage.error('确认退货单失败')
    }
  }
}

const handleComplete = async () => {
  if (!returnOrder.value) return

  try {
    await ElMessageBox.confirm('确认要完成此退货单吗？', '提示', {
      type: 'warning'
    })
    await returnApi.updateStatus(returnOrder.value.id!, '已完成')
    ElMessage.success('退货单已完成')
    fetchReturnOrder()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成退货单失败:', error)
      ElMessage.error('完成退货单失败')
    }
  }
}

onMounted(() => {
  fetchReturnOrder()
})
</script>

<style scoped>
.return-order-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.detail-container {
  max-width: 1200px;
}

.total-summary {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item .label {
  font-weight: 500;
  color: #606266;
}

.summary-item .value {
  font-weight: 600;
  color: #303133;
}

.total-amount {
  border-top: 1px solid #e4e7ed;
  padding-top: 8px;
  margin-top: 8px;
}

.total-amount .label {
  font-size: 16px;
  font-weight: 600;
}

.total-amount .value {
  font-size: 18px;
  font-weight: 700;
  color: #f56c6c;
}
</style>
