import{r as e,L as t,w as o,f as a,o as l,g as i,h as u,a as n,i as s,k as r,c as d,m as c,F as p,j as m,V as _,U as g,J as f,u as v,b as h,d as D,e as y,M as b,N as q,t as w,l as I,W as R,ai as Q,aj as S}from"./index-3d4c440c.js";import{e as $,u as A,s as P}from"./quotationRequest-a49a4856.js";import{c as V,a as x,u as k,l as C}from"./product-ab10366a.js";import{j as N,h as O,k as z}from"./quotation-219e97aa.js";import{f as U,a as F}from"./format-552375ee.js";import{d as L}from"./customer-471ca075.js";import{a as M,g as j,l as E}from"./productCategory-3670b9eb.js";import{g as T}from"./brand-81076433.js";import{_ as J}from"./_plugin-vue_export-helper-1b428a4d.js";const B={class:"dialog-footer"},K=J({__name:"AddProductFromQuotationItemDialog",props:{visible:{type:Boolean,default:!1},quotationItem:{type:Object,default:null},title:{type:String,default:"从报价行添加新产品"}},emits:["update:visible","productCreated","closed"],setup(f,{emit:v}){const h=f,D=v,y=e(),b=e(!1),q=e("basic"),w=t({name:"",model:"",category_id:null,brand_id:null,unit:"",description:"",status:"正常",specifications:[]}),I=t({name:[{required:!0,message:"产品名称不能为空",trigger:"blur"}],model:[{required:!0,message:"产品型号不能为空",trigger:"blur"}],category_id:[{required:!0,message:"请选择产品分类",trigger:"change"}],unit:[{required:!0,message:"单位不能为空",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}),R=e([]),Q=e([]),S=e([]);async function $(){try{if(console.log("[AddProductDialog] 开始获取产品分类数据..."),R.value=await M(),console.log("[AddProductDialog] 获取到平铺产品分类列表:",R.value.length,"个分类"),console.log("[AddProductDialog] 尝试调用getProductCategoryTree API..."),Q.value=await j(),console.log("[AddProductDialog] 产品分类树加载完成:",Q.value),!Q.value||0===Q.value.length){console.warn("[AddProductDialog] 产品分类树为空，尝试备用方案...");try{const e=await E({tree:!0,timestamp:(new Date).getTime(),all:!0});console.log("[AddProductDialog] 备用API响应:",e),e&&Array.isArray(e)?Q.value=e:e&&e.items&&(Q.value=e.items)}catch(e){console.error("[AddProductDialog] 备用API调用失败:",e)}Q.value&&0!==Q.value.length||(console.log("[AddProductDialog] 尝试从平铺列表构建树形结构"),R.value.length>0?(Q.value=function(e){const t=JSON.parse(JSON.stringify(e)),o=new Map,a=[];return t.forEach((e=>{e.children=[],o.set(e.id,e)})),t.forEach((e=>{if(null===e.parent_id||0===e.parent_id)a.push(e);else{const t=o.get(e.parent_id);t?t.children.push(e):a.push(e)}})),a}(R.value),console.log("[AddProductDialog] 从平铺列表构建的树形结构:",Q.value)):(console.log("[AddProductDialog] 创建示例分类数据"),Q.value=[{id:9999,name:"阀门类",parent_id:null,level:1,sort_order:1,description:"示例分类",notes:null,children:[{id:9998,name:"闸阀",parent_id:9999,level:2,sort_order:1,description:"示例子分类",notes:null,children:[]}]},{id:9997,name:"管材管件",parent_id:null,level:1,sort_order:2,description:"示例分类",notes:null,children:[]}]))}console.log("[AddProductDialog] 最终使用的产品分类树:",Q.value)}catch(t){console.error("[AddProductDialog] 获取产品分类失败:",t),g.error("获取产品分类失败"),Q.value=[{id:9999,name:"阀门类",parent_id:null,level:1,sort_order:1,description:"示例分类",notes:null,children:[]},{id:9998,name:"管材管件",parent_id:null,level:1,sort_order:2,description:"示例分类",notes:null,children:[]}]}}function A(e){w.specifications.push({name:e||"",code:"",price:0,tax_rate:.13,status:"正常"}),_((()=>{q.value="specifications"}))}function P(){D("update:visible",!1)}o((()=>h.visible),(e=>{if(e){if(console.log("[AddProductDialog] Dialog opened. Received props.quotationItem:",JSON.parse(JSON.stringify(h.quotationItem))),$(),async function(){try{S.value=await T()}catch(e){console.error("获取品牌列表失败:",e),g.error("获取品牌列表失败")}}(),function(){y.value&&y.value.resetFields();Object.assign(w,{name:"",model:"",category_id:null,brand_id:null,unit:"",description:"",status:"正常",specifications:[]}),w.specifications=[]}(),h.quotationItem){w.name=h.quotationItem.original_product_name&&""!==h.quotationItem.original_product_name.trim()?h.quotationItem.original_product_name:h.quotationItem.product_name||"",w.model=h.quotationItem.original_product_model&&""!==h.quotationItem.original_product_model.trim()?h.quotationItem.original_product_model:h.quotationItem.product_model||"",w.unit=h.quotationItem.original_unit&&""!==h.quotationItem.original_unit.trim()?h.quotationItem.original_unit:h.quotationItem.unit||"";const e=h.quotationItem.original_product_spec&&""!==h.quotationItem.original_product_spec.trim()?h.quotationItem.original_product_spec:h.quotationItem.product_spec||null;e&&0===w.specifications.length?A(e):w.name&&w.model&&0===w.specifications.length&&A()}w.name&&w.model&&0===w.specifications.length&&A(),q.value="basic"}})),o((()=>h.quotationItem),(e=>{e&&(console.log("QuotationItem changed, updating form:",e),w.name=e.product_name||e.original_product_name||"",w.model=e.product_model||e.original_product_model||"",w.unit=e.unit||e.original_unit||"个",(e.product_spec||e.original_product_spec)&&(0===w.specifications.length&&A(),w.specifications[0].name=e.product_spec||e.original_product_spec||"标准规格"),0===w.specifications.length&&(A(),w.specifications[0].name="标准规格"))}),{immediate:!0});const x=async()=>{y.value&&await y.value.validate((async(e,t)=>{if(e){b.value=!0;try{const e=w.specifications.map((e=>({specification:e.name,code:e.code,suggested_price:e.price,cost_price:e.cost_price||0,tax_rate:e.tax_rate}))),t={name:w.name,model:w.model,category_id:w.category_id,brand_id:w.brand_id,unit:w.unit,description:w.description,status:w.status,specifications:e};console.log("创建产品请求数据:",t);const o=await V(t);console.log("创建产品成功:",o),g.success(`产品 "${w.name}" 创建成功`),D("productCreated",o),D("update:visible",!1)}catch(o){console.error("创建产品失败:",o),g.error("创建产品失败，请重试")}finally{b.value=!1}}else console.error("表单验证失败:",t)}))};return(e,t)=>{const o=a("el-input"),_=a("el-form-item"),g=a("el-col"),v=a("el-row"),h=a("el-cascader"),D=a("el-option"),R=a("el-select"),$=a("el-tab-pane"),V=a("el-alert"),k=a("el-input-number"),C=a("el-button"),N=a("el-tabs"),O=a("el-form"),z=a("el-dialog");return l(),i(z,{title:f.title,"model-value":f.visible,width:"60%","before-close":P,onClosed:t[8]||(t[8]=t=>e.$emit("closed"))},{footer:u((()=>[n("span",B,[s(C,{onClick:P},{default:u((()=>t[10]||(t[10]=[r("取消")]))),_:1}),s(C,{type:"primary",onClick:x,loading:b.value},{default:u((()=>t[11]||(t[11]=[r(" 确定创建 ")]))),_:1},8,["loading"])])])),default:u((()=>[s(O,{ref_key:"productFormRef",ref:y,model:w,rules:I,"label-width":"120px",class:"product-form"},{default:u((()=>[s(N,{modelValue:q.value,"onUpdate:modelValue":t[7]||(t[7]=e=>q.value=e)},{default:u((()=>[s($,{label:"基本信息",name:"basic"},{default:u((()=>[s(v,null,{default:u((()=>[s(g,{span:12},{default:u((()=>[s(_,{label:"产品名称",prop:"name"},{default:u((()=>[s(o,{modelValue:w.name,"onUpdate:modelValue":t[0]||(t[0]=e=>w.name=e),placeholder:"请输入产品名称"},null,8,["modelValue"])])),_:1})])),_:1}),s(g,{span:12},{default:u((()=>[s(_,{label:"产品型号",prop:"model"},{default:u((()=>[s(o,{modelValue:w.model,"onUpdate:modelValue":t[1]||(t[1]=e=>w.model=e),placeholder:"请输入产品型号"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(v,null,{default:u((()=>[s(g,{span:12},{default:u((()=>[s(_,{label:"产品分类",prop:"category_id"},{default:u((()=>[s(h,{modelValue:w.category_id,"onUpdate:modelValue":t[2]||(t[2]=e=>w.category_id=e),placeholder:"请选择产品分类",options:Q.value,props:{value:"id",label:"name",children:"children",checkStrictly:!0,emitPath:!1},filterable:"",clearable:"",style:{width:"100%"}},null,8,["modelValue","options"])])),_:1})])),_:1}),s(g,{span:12},{default:u((()=>[s(_,{label:"品牌",prop:"brand_id"},{default:u((()=>[s(R,{modelValue:w.brand_id,"onUpdate:modelValue":t[3]||(t[3]=e=>w.brand_id=e),placeholder:"请选择品牌 (可选)",filterable:"",clearable:"",style:{width:"100%"}},{default:u((()=>[(l(!0),d(p,null,c(S.value,(e=>(l(),i(D,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(v,null,{default:u((()=>[s(g,{span:12},{default:u((()=>[s(_,{label:"单位",prop:"unit"},{default:u((()=>[s(o,{modelValue:w.unit,"onUpdate:modelValue":t[4]||(t[4]=e=>w.unit=e),placeholder:"请输入单位"},null,8,["modelValue"])])),_:1})])),_:1}),s(g,{span:12},{default:u((()=>[s(_,{label:"状态",prop:"status"},{default:u((()=>[s(R,{modelValue:w.status,"onUpdate:modelValue":t[5]||(t[5]=e=>w.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:u((()=>[s(D,{label:"启用",value:"正常"}),s(D,{label:"禁用",value:"禁用"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(_,{label:"描述",prop:"description"},{default:u((()=>[s(o,{modelValue:w.description,"onUpdate:modelValue":t[6]||(t[6]=e=>w.description=e),type:"textarea",rows:3,placeholder:"请输入产品描述"},null,8,["modelValue"])])),_:1})])),_:1}),s($,{label:"规格价格",name:"specifications"},{default:u((()=>[w.name&&w.model?m("",!0):(l(),i(V,{key:0,title:"请先填写产品名称和型号",type:"warning","show-icon":"",closable:!1,style:{"margin-bottom":"15px"}})),(l(!0),d(p,null,c(w.specifications,((e,t)=>(l(),d("div",{key:t,class:"specification-item"},[s(v,{gutter:10},{default:u((()=>[s(g,{span:6},{default:u((()=>[s(_,{label:"规格名称",prop:`specifications.${t}.name`,rules:[{required:!0,message:"规格名称不能为空",trigger:"blur"}],"label-width":"80px"},{default:u((()=>[s(o,{modelValue:e.name,"onUpdate:modelValue":t=>e.name=t,placeholder:"如: 颜色, 尺寸"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1032,["prop"])])),_:2},1024),s(g,{span:5},{default:u((()=>[s(_,{label:"规格编码",prop:`specifications.${t}.code`,"label-width":"80px"},{default:u((()=>[s(o,{modelValue:e.code,"onUpdate:modelValue":t=>e.code=t,placeholder:"可选"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1032,["prop"])])),_:2},1024),s(g,{span:5},{default:u((()=>[s(_,{label:"价格",prop:`specifications.${t}.price`,rules:[{required:!0,message:"价格不能为空",trigger:"blur"},{type:"number",message:"价格必须为数字"}],"label-width":"60px"},{default:u((()=>[s(k,{modelValue:e.price,"onUpdate:modelValue":t=>e.price=t,precision:2,step:.01,min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1032,["prop"])])),_:2},1024),s(g,{span:5},{default:u((()=>[s(_,{label:"税率 (%)",prop:`specifications.${t}.tax_rate`,rules:[{required:!0,message:"税率不能为空",trigger:"blur"},{type:"number",message:"税率必须为数字"}],"label-width":"70px"},{default:u((()=>[s(k,{modelValue:e.tax_rate,"onUpdate:modelValue":t=>e.tax_rate=t,precision:2,step:.01,min:0,max:1,"controls-position":"right",style:{width:"100%"},placeholder:"如: 0.13"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1032,["prop"])])),_:2},1024),s(g,{span:3,style:{"text-align":"right"}},{default:u((()=>[s(C,{type:"danger",icon:"Delete",circle:"",onClick:e=>function(e){w.specifications.splice(e,1)}(t)},null,8,["onClick"])])),_:2},1024)])),_:2},1024)])))),128)),s(C,{type:"primary",icon:"Plus",onClick:A,disabled:!w.name||!w.model},{default:u((()=>t[9]||(t[9]=[r(" 添加规格 ")]))),_:1},8,["disabled"])])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["model","rules"])])),_:1},8,["title","model-value"])}}},[["__scopeId","data-v-13ab3696"]]),H={class:"dialog-footer"},Y=J(f({__name:"AddSpecificationToProductDialog",props:{visible:{type:Boolean,default:!1},productId:{default:null},quotationItem:{default:null},title:{default:"为产品添加新规格"}},emits:["update:visible","specificationAdded","closed"],setup(d,{emit:c}){const p=d,m=c,_=e(),f=e(!1),v=t({specification:"",cost_price:0,suggested_price:0,min_price:null,max_price:null,tax_rate:.13,is_default:!1,notes:""}),h=t({specification:[{required:!0,message:"规格名称不能为空",trigger:"blur"}],cost_price:[{required:!0,message:"成本价不能为空",trigger:"blur"},{type:"number",message:"成本价必须为数字",trigger:"blur"}],suggested_price:[{required:!0,message:"建议售价不能为空",trigger:"blur"},{type:"number",message:"建议售价必须为数字",trigger:"blur"}],tax_rate:[{required:!0,message:"税率不能为空",trigger:"blur"},{type:"number",message:"税率必须为数字",trigger:"blur"}]});function D(){m("update:visible",!1)}async function y(){var e,t,o;if(_.value)if(p.productId)try{f.value=!0;if(await _.value.validate()){const t=await x(p.productId);if(!t)return g.error("未能获取到当前产品信息。"),void(f.value=!1);const o={specification:v.specification,cost_price:Number(v.cost_price),suggested_price:Number(v.suggested_price),min_price:null!==v.min_price?Number(v.min_price):null,max_price:null!==v.max_price?Number(v.max_price):null,tax_rate:100*Number(v.tax_rate),is_default:v.is_default,notes:v.notes||null},a=t.specifications?t.specifications.map((e=>({id:e.id,specification:e.specification,cost_price:e.cost_price,suggested_price:e.suggested_price,min_price:e.min_price,max_price:e.max_price,tax_rate:e.tax_rate,is_default:e.is_default,notes:e.notes,product_id:e.product_id}))):[],l={specifications:[...a,o]},i=await k(p.productId,l);g.success("新规格添加成功!");const u=null==(e=i.specifications)?void 0:e.find((e=>e.specification===o.specification&&!a.some((t=>t.id===e.id))));m("specificationAdded",u||{...o,id:-1,product_id:p.productId},p.productId),D()}else g.error("表单验证失败，请检查输入。")}catch(a){console.error("添加规格失败:",a);const e=(null==(o=null==(t=null==a?void 0:a.response)?void 0:t.data)?void 0:o.message)||((null==a?void 0:a.errors)?JSON.stringify(a.errors):(null==a?void 0:a.message)||"添加规格失败");g.error(e)}finally{f.value=!1}else g.error("产品ID缺失，无法添加规格。")}return o((()=>p.visible),(e=>{if(e){!function(){_.value&&_.value.resetFields();Object.assign(v,{specification:"",cost_price:0,suggested_price:0,min_price:null,max_price:null,tax_rate:.13,is_default:!1,notes:""})}(),console.log("[AddSpecDialog] Dialog visible. Props received:",JSON.parse(JSON.stringify(p)));let e=null;p.quotationItem&&(p.quotationItem.original_product_spec?(e=p.quotationItem.original_product_spec,console.log("[AddSpecDialog] Using original_product_spec for pre-fill:",e)):p.quotationItem.product_spec?(e=p.quotationItem.product_spec,console.log("[AddSpecDialog] original_product_spec is empty, falling back to product_spec for pre-fill:",e)):console.log("[AddSpecDialog] Both original_product_spec and product_spec are empty.")),e?(v.specification=e,console.log("[AddSpecDialog] Pre-filling specForm.specification with:",e)):(console.log("[AddSpecDialog] NOT pre-filling specForm.specification. Reason:"),p.quotationItem?(console.log("[AddSpecDialog] - Both original_product_spec and product_spec are null, undefined, or empty."),console.log("[AddSpecDialog]   original_product_spec value:",p.quotationItem.original_product_spec),console.log("[AddSpecDialog]   product_spec value:",p.quotationItem.product_spec)):console.log("[AddSpecDialog] - props.quotationItem is null or undefined."))}})),(e,t)=>{const o=a("el-input"),d=a("el-form-item"),c=a("el-input-number"),p=a("el-col"),m=a("el-row"),g=a("el-switch"),b=a("el-form"),q=a("el-button"),w=a("el-dialog");return l(),i(w,{title:e.title,"model-value":e.visible,width:"50%","before-close":D,onClosed:t[8]||(t[8]=t=>e.$emit("closed"))},{footer:u((()=>[n("span",H,[s(q,{onClick:D},{default:u((()=>t[9]||(t[9]=[r("取消")]))),_:1}),s(q,{type:"primary",onClick:y,loading:f.value},{default:u((()=>t[10]||(t[10]=[r(" 确定添加规格 ")]))),_:1},8,["loading"])])])),default:u((()=>[s(b,{ref_key:"specFormRef",ref:_,model:v,rules:h,"label-width":"100px",class:"spec-form"},{default:u((()=>[s(d,{label:"规格名称",prop:"specification"},{default:u((()=>[s(o,{modelValue:v.specification,"onUpdate:modelValue":t[0]||(t[0]=e=>v.specification=e),placeholder:"例如: 红色, XL, 10米/卷"},null,8,["modelValue"])])),_:1}),s(m,{gutter:10},{default:u((()=>[s(p,{span:12},{default:u((()=>[s(d,{label:"成本价",prop:"cost_price"},{default:u((()=>[s(c,{modelValue:v.cost_price,"onUpdate:modelValue":t[1]||(t[1]=e=>v.cost_price=e),precision:2,step:.01,min:0,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1}),s(p,{span:12},{default:u((()=>[s(d,{label:"建议售价",prop:"suggested_price"},{default:u((()=>[s(c,{modelValue:v.suggested_price,"onUpdate:modelValue":t[2]||(t[2]=e=>v.suggested_price=e),precision:2,step:.01,min:0,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(m,{gutter:10},{default:u((()=>[s(p,{span:12},{default:u((()=>[s(d,{label:"最低售价",prop:"min_price"},{default:u((()=>[s(c,{modelValue:v.min_price,"onUpdate:modelValue":t[3]||(t[3]=e=>v.min_price=e),precision:2,step:.01,min:0,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1}),s(p,{span:12},{default:u((()=>[s(d,{label:"最高售价",prop:"max_price"},{default:u((()=>[s(c,{modelValue:v.max_price,"onUpdate:modelValue":t[4]||(t[4]=e=>v.max_price=e),precision:2,step:.01,min:0,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(d,{label:"税率",prop:"tax_rate"},{default:u((()=>[s(c,{modelValue:v.tax_rate,"onUpdate:modelValue":t[5]||(t[5]=e=>v.tax_rate=e),precision:4,step:.01,min:0,max:1,"controls-position":"right",placeholder:"例如: 0.13 (表示13%)"},null,8,["modelValue"])])),_:1}),s(d,{label:"默认规格",prop:"is_default"},{default:u((()=>[s(g,{modelValue:v.is_default,"onUpdate:modelValue":t[6]||(t[6]=e=>v.is_default=e)},null,8,["modelValue"])])),_:1}),s(d,{label:"备注",prop:"notes"},{default:u((()=>[s(o,{modelValue:v.notes,"onUpdate:modelValue":t[7]||(t[7]=e=>v.notes=e),type:"textarea",rows:2,placeholder:"可选备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","model-value"])}}}),[["__scopeId","data-v-db2ae002"]]),W={class:"quotation-request-detail-page"},X={class:"card-header"},G={class:"card-header"},Z={key:0},ee={key:1},te={key:0},oe={key:1},ae=["innerHTML"],le={key:1},ie={style:{"margin-top":"5px"}},ue={class:"dialog-footer"},ne=J({__name:"QuotationRequestDetail",setup(t){const f=v(),V=h();e("basic");const k=e(!1),M=e([]),j=e([]),E=e({}),T=e(!1),J=e(!1),B=e(!1),H=e(!1),ne=e(!1),se=e(!1),re=e(!0),de=e(null),ce=e(null),pe=e(!1),me=e(null),_e=e(!1),ge=e(null),fe=e(null),ve=D((()=>de.value?`报价需求: ${de.value.request_number||"详情"}`:"报价需求详情")),he=D((()=>{if(!de.value||!de.value.items||0===de.value.items.length)return[];const e=JSON.parse(JSON.stringify(de.value.items)).map((e=>({...e,product_name_rowspan:1,product_model_rowspan:1,is_fully_matched:!!e.matched_product_id&&!!e.matched_product_specification_id,is_product_matched_only:!!e.matched_product_id&&!e.matched_product_specification_id,matched_product:e.matched_product,matched_specification:e.matched_specification})));e.sort(((e,t)=>{const o=e.product_name||"",a=t.product_name||"",l=e.product_model||"",i=t.product_model||"";return o<a?-1:o>a?1:l<i?-1:l>i?1:0}));for(let t=0;t<e.length;t++){if(t>0&&e[t].product_name===e[t-1].product_name&&e[t].product_model===e[t-1].product_model){e[t].product_name_rowspan=0;let o=t-1;for(;o>=0&&0===e[o].product_name_rowspan;)o--;o>=0&&e[o].product_name_rowspan++}if(t>0&&e[t].product_name===e[t-1].product_name&&e[t].product_model===e[t-1].product_model){e[t].product_model_rowspan=0;let o=t-1;for(;o>=0&&0===e[o].product_model_rowspan&&e[o].product_name===e[t].product_name;)o--;o>=0&&e[o].product_name===e[t].product_name&&e[o].product_model_rowspan++}}return e})),De=({row:e,column:t,rowIndex:o,columnIndex:a})=>{const l=t.property;if("product_name"===l||"product_model"===l){const t=(e.product_name||"")+"|"+(e.product_model||"");let a=0,l=-1;for(let e=0;e<he.value.length;e++){const o=he.value[e];if((o.product_name||"")+"|"+(o.product_model||"")===t)-1===l&&(l=e),a++;else if(-1!==l)break}if(o===l&&a>0)return{rowspan:a,colspan:1};if(o>l&&o<l+a)return{rowspan:0,colspan:0}}return{rowspan:1,colspan:1}},ye=e=>{if(!e)return"info";return{"草稿":"info","已提交":"warning","正式":"success","已报价":"success","已处理":"success","已取消":"danger",draft:"info",submitted:"warning",official:"success",quoted:"success",processed:"success",cancelled:"danger"}[e]||"info"},be=e=>{if(!e)return"-";return e.replace(/(\[系统备注:[^\]]*\]|\[系统匹配信息:[^\]]*\])/g,'<strong style="color: #E6A23C;">$1</strong>').replace(/\n/g,"<br>")},qe=()=>{V.back()},we=()=>{de.value&&de.value.id&&V.push(`/quotation-requests/${de.value.id}/edit`)},Ie=async()=>{if(de.value&&de.value.id){B.value=!0;try{const e=await $(de.value.id),t=new Blob([e.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),o=window.URL.createObjectURL(t),a=document.createElement("a");a.href=o,a.download=`报价需求表_${de.value.request_number||"export"}.xlsx`,a.click(),window.URL.revokeObjectURL(o)}catch(e){console.error("导出失败:",e),g.error("导出失败")}finally{B.value=!1}}},Re=async()=>{if(de.value&&de.value.id)if("official"===de.value.status||"正式"===de.value.status)try{R.confirm('请选择创建报价单后是否自动标记该报价需求为"已报价"状态？',"创建报价单",{confirmButtonText:"保存并标记为已报价",cancelButtonText:"仅创建不改变状态",distinguishCancelAndClose:!0,type:"info"}).then((async()=>{await Qe(!0)})).catch((e=>{"cancel"===e?Qe(!1):g.info("已取消创建报价单")}))}catch(e){console.error("创建报价单失败:",e),g.error("创建报价单失败")}else g.warning('报价需求表当前状态不是"正式"，无法创建报价单。')},Qe=async e=>{var t,o,a,l,i;try{const{value:u}=await R.prompt("请输入报价单有效期至 (YYYY-MM-DD):","创建报价单",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"date",inputValidator:e=>!!e||"有效期不能为空"}),n={valid_until:u,update_request_status:e,request_data:{project_name:null==(t=de.value)?void 0:t.project_name,project_address:null==(o=de.value)?void 0:o.project_address,customer_id:null==(a=de.value)?void 0:a.customer_id,notes:null==(l=de.value)?void 0:l.notes}};console.log("[QuotationRequestDetail.vue] 创建报价单，传递数据:",n);const s=await O(de.value.id,n);let r;if(console.log("[QuotationRequestDetail.vue] 创建报价单成功，返回数据:",s),s&&"object"==typeof s&&(console.log("[QuotationRequestDetail.vue] 创建报价单响应结构分析:",{hasID:"id"in s,hasData:"data"in s,hasCode:"code"in s,directValue:s.id,nestedValue:null==(i=s.data)?void 0:i.id,responseKeys:Object.keys(s)}),"id"in s?(r=s.id,console.log("[QuotationRequestDetail.vue] 从res.id直接获取:",r)):s.data&&"object"==typeof s.data&&"id"in s.data?(r=s.data.id,console.log("[QuotationRequestDetail.vue] 从res.data.id获取:",r)):void 0!==s.code&&s.data&&"object"==typeof s.data?"id"in s.data?(r=s.data.id,console.log("[QuotationRequestDetail.vue] 从res.data.id获取(code存在):",r)):"number"==typeof s.data&&(r=s.data,console.log("[QuotationRequestDetail.vue] 从res.data获取数字:",r)):"number"==typeof s&&(r=s,console.log("[QuotationRequestDetail.vue] API直接返回ID:",r))),!r)return console.error("[QuotationRequestDetail.vue] 无法从响应中提取报价单ID:",s),void g.warning("报价单创建成功，但无法自动跳转到编辑页面。请从报价单列表中查找。");g.success(`报价单创建成功${e?'，报价需求状态已更新为"已报价"':""}，正在跳转到编辑页面`),V.push(`/quotations/${r}/edit`),e&&setTimeout((()=>{Oe()}),500)}catch(u){"cancel"!==u?(console.error("创建报价单失败:",u),g.error("创建报价单过程中发生错误")):g.info("已取消创建报价单")}},Se=async e=>{const t=e.original_product_name||e.product_name,o=e.original_product_model||e.product_model;if(!t)return void console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - No product name to search.`);if(o){const a={name:t,model:o,per_page:10};console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Attempting exact match with params:`,a);try{const t=await C(a);if(console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Exact match response:`,t),t&&t.list&&t.list.length>0){if(1===t.list.length){const o=t.list[0];return console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Exact match found:`,o),e.form_matched_product_id=o.id,void(await xe(e,o.id))}console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Exact match yielded multiple results (${t.list.length}). No auto-select.`)}else console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Exact match yielded no results or invalid response:`,t)}catch(l){console.error(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Error during exact match product search:`,l)}}const a={name:t,per_page:10};console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Attempting name-only search with params:`,a);try{const o=await C(a);if(console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Name-only search response:`,o),o&&o.list&&o.list.length>0)if(1===o.list.length){const t=o.list[0];console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Name-only match found:`,t),e.form_matched_product_id=t.id,await xe(e,t.id)}else{console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Name-only search yielded multiple results (${o.list.length}). No auto-select.`);const a=o.list.find((e=>{var o;return(null==(o=e.name)?void 0:o.toLowerCase())===t.toLowerCase()}));a&&(console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Found exact name match in multiple results:`,a),e.form_matched_product_id=a.id,await xe(e,a.id))}else console.log(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Name-only search yielded no results or invalid response:`,o)}catch(l){console.error(`[QuotationRequestDetail.vue] AutoMatch: Item ID ${e.id} - Error during name-only product search:`,l)}},$e=async()=>{J.value=!0;try{console.log("[QuotationRequestDetail.vue] openStandardizeDialog - 开始获取最新报价需求数据");const t=await je();if(console.log("[QuotationRequestDetail.vue] openStandardizeDialog - 获取到最新报价需求数据:",t),!t.items||0===t.items.length)return g.info("此报价需求没有项目可以调整。"),void(J.value=!1);M.value=t.items.map((e=>({...e,form_matched_product_id:e.matched_product_id||null,form_matched_specification_id:e.matched_product_specification_id||null,product_name:e.original_product_name||e.product_name,product_model:e.original_product_model||e.product_model,product_spec:e.original_product_spec||e.product_spec}))),console.log("[QuotationRequestDetail.vue] openStandardizeDialog - 已准备标准化项目数据:",M.value),T.value=!1,j.value=[],ne.value=!1,console.log("[QuotationRequestDetail.vue] openStandardizeDialog - 开始加载产品选项");try{await Ve(!0),console.log("[QuotationRequestDetail.vue] openStandardizeDialog - 产品选项加载完成，数量:",j.value.length)}catch(e){console.error("[QuotationRequestDetail.vue] openStandardizeDialog - 加载产品选项失败:",e),g.warning("加载产品选项失败，但您仍可以通过搜索添加产品")}console.log("[QuotationRequestDetail.vue] openStandardizeDialog - 开始自动匹配尝试");const o=M.value.map((e=>e.form_matched_product_id?Promise.resolve():Se(e)));if(await Promise.all(o),console.log("[QuotationRequestDetail.vue] openStandardizeDialog - 所有自动匹配尝试已完成"),0===M.value.length)return g.warning("没有找到可调整的项目"),void(J.value=!1);k.value=!0,console.log("[QuotationRequestDetail.vue] openStandardizeDialog - 对话框已显示")}catch(e){console.error("[QuotationRequestDetail.vue] 打开手动调整对话框失败:",e),g.error(e instanceof Error?e.message:"加载需求项目失败，请重试。")}finally{J.value=!1}};let Ae=null;const Pe=async e=>{console.log("[QuotationRequestDetail.vue] searchProductsForDialog CALLED with query:",e),Ae&&clearTimeout(Ae),Ae=window.setTimeout((async()=>{if(console.log("[QuotationRequestDetail.vue] searchProductsForDialog TIMEOUT_CALLBACK query:",e),e){ne.value=!0,console.log("[QuotationRequestDetail.vue] searchProductsForDialog - Fetching products for query:",e);try{const t=await C({name:e,per_page:50});if(console.log("[QuotationRequestDetail.vue] searchProductsForDialog - API响应:",t),t&&"object"==typeof t&&"message"in t){const e=t;if(e.message&&"获取产品列表成功"!==e.message&&e.message.includes("失败"))return console.warn("[QuotationRequestDetail.vue] searchProductsForDialog - API返回错误消息:",e.message),void g.warning(`搜索产品失败: ${e.message}`)}if(t&&t.list)j.value=t.list||[],console.log("[QuotationRequestDetail.vue] searchProductsForDialog - Found",t.list.length,"products for query:",e,"Sample:",t.list.length>0?JSON.parse(JSON.stringify(t.list[0])):"none"),0===t.list.length&&g.info(`未找到符合 "${e}" 的产品`);else{console.warn("[QuotationRequestDetail.vue] searchProductsForDialog - API返回异常格式:",t);let e=[];const o=t;o&&o.data&&Array.isArray(o.data)?(console.log("[QuotationRequestDetail.vue] searchProductsForDialog - 尝试从response.data中提取产品列表"),e=o.data):o&&Array.isArray(o)&&(console.log("[QuotationRequestDetail.vue] searchProductsForDialog - 尝试将整个response作为产品列表处理"),e=o),e.length>0?(j.value=e,console.log("[QuotationRequestDetail.vue] searchProductsForDialog - 成功从非标准响应中提取到",e.length,"个产品")):(console.warn("[QuotationRequestDetail.vue] searchProductsForDialog - 无法从响应中提取产品列表"),g.warning("搜索结果格式异常，请尝试其他关键词"))}}catch(t){console.error("[QuotationRequestDetail.vue] 搜索产品失败:",t),g.error("产品搜索失败，请重试")}}else if(!ne.value&&T.value&&j.value.length>0)console.log("[QuotationRequestDetail.vue] searchProductsForDialog - Query is empty, initial options loaded, user has not typed. Keeping current options.");else try{console.log("[QuotationRequestDetail.vue] searchProductsForDialog - Query cleared, restoring initial product list");const e=await C({per_page:100});console.log("[QuotationRequestDetail.vue] searchProductsForDialog - 恢复初始列表响应:",e),e&&e.list?(j.value=e.list||[],console.log("[QuotationRequestDetail.vue] searchProductsForDialog - Restored",j.value.length,"products to initial list"),0===j.value.length&&g.info("产品列表为空，请考虑添加产品")):(console.warn("[QuotationRequestDetail.vue] searchProductsForDialog - 恢复初始列表返回异常格式:",e),g.warning("加载产品列表失败"))}catch(t){console.error("[QuotationRequestDetail.vue] 恢复初始产品列表失败:",t),g.error("加载产品列表失败，请刷新页面重试")}}),300)},Ve=async(e=!1)=>{console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded CALLED. Force input:",e,"InitialLoaded:",T.value,"OptionsLength:",j.value.length);const t=!0===e;if(console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - shouldActuallyForce:",t),t||!T.value)try{console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - Fetching products (initial or forced).");const e=await C({per_page:100});if(console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - API响应:",e),e&&"object"==typeof e&&"message"in e){const t=e;if(t.message&&"获取产品列表成功"!==t.message&&t.message.includes("失败"))return console.warn("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - API返回错误消息:",t.message),g.warning(`加载产品列表失败: ${t.message}`),T.value=!0,void(j.value=j.value||[])}if(e&&e.list&&e.list.length>0)j.value=e.list,T.value&&!t||(T.value=!0),console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - Initial productOptionsForDialog SET:",JSON.parse(JSON.stringify(j.value.slice(0,5))),`...and ${j.value.length-5} more items`);else{let t=[];const o=e;o&&o.data&&Array.isArray(o.data)?(console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - 尝试从response.data中提取产品列表"),t=o.data):o&&Array.isArray(o)&&(console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - 尝试将整个response作为产品列表处理"),t=o),t.length>0?(j.value=t,T.value=!0,console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - 成功从非标准响应中提取到",t.length,"个产品")):(console.warn("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - API返回了空的产品列表或格式不正确:",e),T.value=!0,j.value=[],g.warning("产品列表为空，可能需要先添加产品"))}}catch(o){console.error("[QuotationRequestDetail.vue] 加载初始产品列表失败:",o),g.warning("加载产品列表失败，请尝试手动搜索"),T.value=!0,j.value=[]}else 0===j.value.length&&T.value?console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - SKIPPED: Initial products were loaded, but options are currently empty (likely cleared by remote search)."):console.log("[QuotationRequestDetail.vue] ensureProductOptionsLoaded - SKIPPED: Initial products already loaded and options exist, or no force.")},xe=async(e,t)=>{if(console.log(`[QuotationRequestDetail.vue] handleDialogProductChange - Product ID: ${t}, Row ID: ${e.id}`),e.form_matched_specification_id=null,t){if(E.value[t]&&E.value[t].length>0){console.log(`[QuotationRequestDetail.vue] Using cached specifications for product ${t}`);const o=E.value[t];return void(o&&o.length>0&&ke(e,t,o))}console.log(`[QuotationRequestDetail.vue] Fetching full product details for product ID: ${t} to get specifications.`);try{const o=await x(t);let a;if(console.log("[QuotationRequestDetail.vue] Raw response from getProduct:",o),o&&"object"==typeof o){const e=o;200!==e.code&&0!==e.code||!e.data?e.id?(a=o,console.log("[QuotationRequestDetail.vue] Response is directly a product object")):(console.warn("[QuotationRequestDetail.vue] Unrecognized API response format:",o),a=o):(a=e.data,console.log(`[QuotationRequestDetail.vue] Using standard API response data field, code=${e.code}`))}else console.warn("[QuotationRequestDetail.vue] Invalid API response type:",typeof o),a=o;const l=a.specifications||[];console.log(`[QuotationRequestDetail.vue] Extracted specifications for product ${t}:`,l),E.value={...E.value,[t]:l},l&&l.length>0?ke(e,t,l):console.log(`[QuotationRequestDetail.vue] No specifications found for product ${t}`)}catch(o){console.error(`[QuotationRequestDetail.vue] 获取产品 ${t} 的详情 (含规格) 失败:`,o),g.error(`加载产品 ${t} 的规格失败`),E.value={...E.value,[t]:[]}}}else console.log("[QuotationRequestDetail.vue] Product ID is null, specifications selection cleared.")},ke=(e,t,o)=>{console.log(`[QuotationRequestDetail.vue] attemptAutoMatchSpecification CALLED for Product ID ${t}, Item ID ${e.id}. Available specs count: ${o?o.length:0}`);let a=null;if(!o||0===o.length)return void console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Product ID ${t} has no specifications. No auto-selection.`);const l=e.original_product_spec||e.product_spec;if(!l)return console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Item ID ${e.id} has no specification to match.`),void(1===o.length&&(a=o[0].id,console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Product ID ${t} has only one spec, auto-selecting ID ${a} ("${o[0].specification}")`)));const i=l.toLowerCase().trim();console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Attempting to match spec name: "${i}" for Product ID ${t}`);const u=o.find((e=>{var t;return(null==(t=e.specification)?void 0:t.toLowerCase().trim())===i}));if(u)a=u.id,console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Found EXACT match for spec "${l}": ID ${a} ("${u.specification}")`);else{const e=o.filter((e=>{var t,o;return(null==(t=e.specification)?void 0:t.toLowerCase().trim().includes(i))||i.includes(null==(o=e.specification)?void 0:o.toLowerCase().trim())}));1===e.length?(a=e[0].id,console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Found CONTAINS match for spec "${l}": ID ${a} ("${e[0].specification}")`)):e.length>1?console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Multiple CONTAINS matches (${e.length}) found for spec "${l}". No auto-selection.`):(console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: No CONTAINS match found for spec "${l}". Checking if product has only one spec.`),1===o.length&&(a=o[0].id,console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Product ID ${t} has only one spec, auto-selecting ID ${a} ("${o[0].specification}")`)))}null!==a?e.form_matched_specification_id!==a?(e.form_matched_specification_id=a,console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Set form_matched_specification_id to ${a} for item ${e.id}`)):console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: Item ${e.id} already had spec ${a} selected. No change.`):console.log(`[QuotationRequestDetail.vue] AutoMatchSpec: No specification was auto-selected for item ${e.id}. Current selection (if any) is preserved.`)},Ce=async()=>{var e,t,o,a,l;if(!de.value||!de.value.id)return g.error("报价需求ID不存在，无法更新。"),void(J.value=!1);console.log("[SubmitStandardize_V2] Function Start. Current Dialog Items:",JSON.parse(JSON.stringify(M.value)));try{console.log(`[SubmitStandardize_V2] Attempting to re-fetch latest state for request ID: ${de.value.id}`);const e=await je();console.log(`[SubmitStandardize_V2] Status re-fetched just before submit: ${e.status} (Full object: ${JSON.stringify(e)})`);const t=["草稿","已提交"];if(!e.status||!t.includes(e.status))return g.error(`操作被阻止：报价需求状态已变为 '${e.status||"未知"}'，不再允许修改条目。请刷新页面或检查需求状态。`),k.value=!1,J.value=!1,Oe(),void console.warn("[SubmitStandardize_V2] Submit blocked due to status mismatch after re-fetch.");de.value=e,console.log(`[SubmitStandardize_V2] quotationRequest.value updated to latest status: ${de.value.status}`)}catch(s){return console.error("[SubmitStandardize_V2] Error re-fetching state before submit:",s),g.error("获取最新报价需求状态失败，无法继续操作。请重试。"),void(J.value=!1)}const i=["草稿","已提交"];if(!de.value.status||!i.includes(de.value.status))return g.warning(`前端状态 (${de.value.status}) 与API允许的操作状态 (${i.join("/")}) 不符，已阻止API调用。`),k.value=!1,J.value=!1,void console.warn("[SubmitStandardize_V2] Submit blocked due to frontend status vs API allowed status mismatch.");J.value=!0,console.log("[SubmitStandardize_V2] isSubmittingMatch set to true.");const u=new Map(null==(e=de.value.items)?void 0:e.map((e=>[e.id,e])));console.log("[SubmitStandardize_V2] Original items from quotationRequest.value:",JSON.parse(JSON.stringify(Array.from(u.values()))));const n=M.value.filter((e=>{const t=u.get(e.id);if(!t)return console.warn(`[SubmitStandardize_V2] Item ID ${e.id} from dialog not found in original items. This might indicate a sync issue.`),!0;const o=e.form_matched_product_id!==t.matched_product_id||e.form_matched_specification_id!==t.matched_product_specification_id;return o&&console.log(`[SubmitStandardize_V2] Item ID ${e.id} marked for update. Dialog: P_ID=${e.form_matched_product_id}, S_ID=${e.form_matched_specification_id}. Original: P_ID=${t.matched_product_id}, S_ID=${t.matched_product_specification_id}`),o}));if(console.log("[SubmitStandardize_V2] Items identified for update (itemsToUpdate):",JSON.parse(JSON.stringify(n))),0===n.length)return g.info("没有更改需要提交。"),k.value=!1,J.value=!1,void console.log("[SubmitStandardize_V2] No changes to submit.");try{console.log("[SubmitStandardize_V2] Preparing to call apiUpdateItemMatch for multiple items.");const e=n.map((e=>{var t,o;if(!e.id)throw console.error("[SubmitStandardize_V2] Item ID missing in itemsToUpdate an item:",JSON.parse(JSON.stringify(e))),new Error("项目ID缺失，无法更新。");const a={product_id:e.form_matched_product_id,specification_id:e.form_matched_specification_id};return console.log(`[SubmitStandardize_V2] API Call for Item ID: ${e.id}, Payload:`,JSON.parse(JSON.stringify(a)),`Request ID: ${null==(t=de.value)?void 0:t.id}`),A(null==(o=de.value)?void 0:o.id,e.id,a)})),t=await Promise.all(e);console.log("[SubmitStandardize_V2] Promise.all results from apiUpdateItemMatch:",t),g.success(`${n.length} 个项目的匹配信息更新成功。`),k.value=!1,console.log("[SubmitStandardize_V2] Calling fetchQuotationRequestDetails() after successful update."),Oe()}catch(r){console.error("[SubmitStandardize_V2] Error during apiUpdateItemMatch or Promise.all:",r);const e=r;let i="更新匹配信息失败，请检查控制台日志。";if(e.isBizError&&e.bizData)i=`更新失败: ${e.bizData.message} (前端状态: ${null==(t=de.value)?void 0:t.status}, 后端业务码: ${e.bizData.code})`;else if(e.isAxiosError){const t=e.axiosResponse;i=t&&t.data&&t.data.message?`更新失败: ${t.data.message} (前端状态: ${null==(o=de.value)?void 0:o.status}, HTTP状态: ${t.status})`:t?`更新失败: HTTP ${t.status}错误 (前端状态: ${null==(a=de.value)?void 0:a.status})`:`更新失败: ${e.message} (前端状态: ${null==(l=de.value)?void 0:l.status})`}else e.message&&(i=e.message);g.error(i)}finally{J.value=!1,console.log("[SubmitStandardize_V2] isSubmittingMatch set to false in finally block.")}},Ne=async()=>{if(de.value&&de.value.id)try{await R.confirm("这将尝试根据系统规则自动匹配和更新所有需求条目。此操作可能覆盖手动调整。确定继续吗？","自动规整确认",{confirmButtonText:"确定规整",cancelButtonText:"取消",type:"warning"}),H.value=!0,await P(de.value.id),g.success("所有条目自动规整完成，请检查结果。"),Oe()}catch(e){"cancel"!==e&&(console.error("自动规整失败:",e),g.error((null==e?void 0:e.message)||"自动规整失败"))}finally{H.value=!1}else g.error("报价需求信息不完整，无法执行操作。")},Oe=async()=>{re.value=!0,ce.value=null;const e=f.params.id;if(!e)return g.error("无效的报价需求ID"),re.value=!1,void V.push({name:"QuotationRequests"});try{console.log(`[QuotationRequestDetail.vue] 正在获取报价需求ID: ${e} 的详情`);const o=await N(Number(e));let a;if(console.log("[QuotationRequestDetail.vue] 获取到报价需求详情响应:",o),!o||"object"!=typeof o)throw new Error("无效的API响应");{const e=o;if(200!==e.code&&0!==e.code||!e.data)if(e.id)a=o,console.log("[QuotationRequestDetail.vue] 响应直接是数据对象");else{if(e.code&&200!==e.code&&0!==e.code)throw new Error(e.message||"获取报价需求失败");console.warn("[QuotationRequestDetail.vue] 尝试从未识别的API响应格式中提取数据",o),e.data&&e.data.id?(a=e.data,console.log("[QuotationRequestDetail.vue] 从非标准响应中成功提取数据")):(a=o,g.warning("API响应格式不符合预期，数据可能不完整"))}else a=e.data,console.log(`[QuotationRequestDetail.vue] 使用标准API响应中的data字段，code=${e.code}`)}if(!a||!a.id)throw new Error("获取报价需求详情失败：返回数据无效");if(de.value=a,console.log("[QuotationRequestDetail.vue] 报价需求数据已更新:",de.value),de.value&&de.value.customer_id)try{console.log(`[QuotationRequestDetail.vue] 正在获取客户ID: ${de.value.customer_id} 的详情`);const e=await L(de.value.customer_id);let t;if(e&&"object"==typeof e){const o=e;200!==o.code&&0!==o.code||!o.data?(o.id||console.warn("[QuotationRequestDetail.vue] 无法识别的客户API响应格式",e),t=e):t=o.data}t&&t.id?(ce.value=t,console.log("[QuotationRequestDetail.vue] 客户详情已更新:",ce.value)):(console.warn("[QuotationRequestDetail.vue] 客户详情数据无效",t),ce.value=null)}catch(t){console.error("[QuotationRequestDetail.vue] 获取客户详情失败:",t),g.warning("获取客户详情失败，将显示客户ID"),ce.value=null}}catch(o){console.error("[QuotationRequestDetail.vue] 获取报价需求详情失败:",o),g.error(`获取报价需求详情失败: ${o instanceof Error?o.message:"未知错误"}`),de.value=null}finally{re.value=!1}};y((()=>{Oe()})),o(M,(e=>{e.forEach((e=>{e.form_matched_product_id&&E.value[e.form_matched_product_id]}))}),{deep:!0});const ze=()=>{ge.value=null,fe.value=null},Ue=(e,t)=>{if(t){if(g.success(`规格 "${e.specification}" 已成功添加到产品ID ${t}。`),E.value[t]?E.value[t].push(e):E.value[t]=[e],fe.value&&fe.value.form_matched_product_id===t){const t=M.value.find((e=>{var t;return e.id===(null==(t=fe.value)?void 0:t.id)}));t&&(t.form_matched_specification_id=e.id,console.log(`[QuotationRequestDetail] Updated item ${t.id} to use new spec ${e.id}`))}_e.value=!1}},Fe=async e=>{if(!me.value)return;g.success(`产品 "${e.name}" 已成功创建并关联到当前报价条目。`);const t=M.value.find((e=>{var t;return e.id===(null==(t=me.value)?void 0:t.id)}));if(t){if(t.matched_product_id=e.id,t.form_matched_product_id=e.id,j.value.find((t=>t.id===e.id))||(console.log("将新创建的产品添加到产品选项列表:",e),j.value.unshift({id:e.id,name:e.name,model:e.model,unit:e.unit})),e.specifications&&e.specifications.length>0){if(console.log("更新规格映射表，产品ID:",e.id,"规格:",e.specifications),E.value[e.id]=e.specifications,1===e.specifications.length){const o=e.specifications[0].id;t.matched_product_specification_id=o,t.form_matched_specification_id=o,g.info(`已自动选择规格: ${e.specifications[0].specification||""}`)}}else console.warn("新创建的产品没有规格信息"),E.value[e.id]=[];_((()=>{xe(t,e.id)}))}else console.warn("Could not find the item in standardizeItems to update after product creation.");pe.value=!1},Le=D((()=>!(!de.value||!de.value.items||0===de.value.items.length)&&de.value.items.every((e=>!!e.matched_product_specification_id)))),Me=async()=>{var e,t;if(de.value&&de.value.id){se.value=!0;try{await z(de.value.id,{status:"official"}),g.success("报价需求已成功更新为正式状态！"),Oe()}catch(o){console.error("标记为正式失败:",o),g.error((null==(t=null==(e=o.response)?void 0:e.data)?void 0:t.message)||o.message||"标记为正式失败，请重试。")}finally{se.value=!1}}},je=async()=>{const e=f.params.id;if(!e)throw new Error("无效的报价需求ID");console.log(`[QuotationRequestDetail.vue] loadLatestQuotationRequest - Fetching request ID: ${e}`);try{const o=await N(Number(e));let a;if(console.log("[QuotationRequestDetail.vue] loadLatestQuotationRequest - Raw response:",o),!o||"object"!=typeof o)throw console.warn("[QuotationRequestDetail.vue] loadLatestQuotationRequest - Invalid API response type:",typeof o),new Error("无效的API响应类型");{const e=o;if(200!==e.code&&0!==e.code||!e.data){if(!e.id)throw console.warn("[QuotationRequestDetail.vue] loadLatestQuotationRequest - Unrecognized API response format:",o),new Error("API响应格式不符合预期");a=o,console.log("[QuotationRequestDetail.vue] loadLatestQuotationRequest - Response is directly a request object")}else a=e.data,console.log(`[QuotationRequestDetail.vue] loadLatestQuotationRequest - Using standard response data field, code=${e.code}`)}if(!a||!a.id)throw new Error("API返回的数据无效或不完整");if(de.value=a,a.customer_id&&(!ce.value||ce.value.id!==a.customer_id))try{console.log(`[QuotationRequestDetail.vue] loadLatestQuotationRequest - Fetching customer details for ID: ${a.customer_id}`);const e=await L(a.customer_id);let t;if(e&&"object"==typeof e){const o=e;200!==o.code&&0!==o.code||!o.data?(o.id||console.warn("[QuotationRequestDetail.vue] loadLatestQuotationRequest - Unrecognized customer API response format:",e),t=e):t=o.data}t&&t.id?(ce.value=t,console.log("[QuotationRequestDetail.vue] loadLatestQuotationRequest - Customer details updated:",ce.value)):(console.warn("[QuotationRequestDetail.vue] loadLatestQuotationRequest - Invalid customer data:",t),ce.value=null)}catch(t){console.error("[QuotationRequestDetail.vue] 刷新客户详情失败",t),ce.value=null}return console.log(`[QuotationRequestDetail.vue] loadLatestQuotationRequest - Completed, request ID: ${a.id}, Status: ${a.status}`),a}catch(o){throw console.error("[QuotationRequestDetail.vue] loadLatestQuotationRequest - Error:",o),o}};return(e,t)=>{const o=a("el-page-header"),_=a("el-button"),f=a("el-descriptions-item"),v=a("el-tag"),h=a("el-descriptions"),D=a("el-card"),y=a("el-table-column"),R=a("el-table"),$=a("el-empty"),A=a("el-option"),P=a("el-select"),V=a("el-dialog"),x=b("loading");return q((l(),d("div",W,[s(o,{content:ve.value,onBack:qe},null,8,["content"]),de.value?(l(),i(D,{key:0,class:"box-card details-card"},{header:u((()=>[n("div",X,[t[9]||(t[9]=n("span",null,"报价需求详情",-1)),n("div",null,[de.value&&"草稿"===de.value.status&&Le.value?(l(),i(_,{key:0,type:"success",loading:se.value,onClick:Me,class:"mr-10"},{default:u((()=>t[5]||(t[5]=[r(" 标记为正式 ")]))),_:1},8,["loading"])):m("",!0),de.value&&"草稿"===de.value.status?(l(),i(_,{key:1,onClick:we},{default:u((()=>t[6]||(t[6]=[r("编辑")]))),_:1})):m("",!0),de.value&&"正式"===de.value.status?(l(),i(_,{key:2,type:"primary",onClick:Re,class:"mr-10"},{default:u((()=>t[7]||(t[7]=[r(" 创建报价单 ")]))),_:1})):m("",!0),s(_,{type:"info",loading:B.value,onClick:Ie},{default:u((()=>t[8]||(t[8]=[r("导出需求表")]))),_:1},8,["loading"])])])])),default:u((()=>[s(h,{column:2,border:""},{default:u((()=>[s(f,{label:"需求表号"},{default:u((()=>[r(w(de.value.request_number),1)])),_:1}),s(f,{label:"客户名称"},{default:u((()=>{var e;return[r(w((null==(e=ce.value)?void 0:e.name)||(de.value?"ID: "+de.value.customer_id:"加载中...")),1)]})),_:1}),s(f,{label:"项目名称"},{default:u((()=>[r(w(de.value.project_name),1)])),_:1}),s(f,{label:"项目地址"},{default:u((()=>[r(w(de.value.project_address),1)])),_:1}),s(f,{label:"期望货期"},{default:u((()=>[r(w(de.value.expected_date?I(U)(de.value.expected_date):"-"),1)])),_:1}),s(f,{label:"状态"},{default:u((()=>[s(v,{type:ye(de.value.status)},{default:u((()=>[r(w(de.value.status),1)])),_:1},8,["type"])])),_:1}),s(f,{label:"创建时间"},{default:u((()=>[r(w(de.value.created_at?I(F)(de.value.created_at):"-"),1)])),_:1}),s(f,{label:"更新时间"},{default:u((()=>[r(w(de.value.updated_at?I(F)(de.value.updated_at):"-"),1)])),_:1}),s(f,{label:"备注",span:2},{default:u((()=>[r(w(de.value.notes),1)])),_:1})])),_:1})])),_:1})):m("",!0),de.value&&de.value.items&&de.value.items.length>0?(l(),i(D,{key:1,class:"box-card items-card"},{header:u((()=>[n("div",G,[t[12]||(t[12]=n("span",null,"需求项目列表",-1)),n("div",null,[s(_,{type:"warning",onClick:Ne,icon:I(Q),loading:H.value,class:"mr-10"},{default:u((()=>t[10]||(t[10]=[r(" 自动规整所有条目 ")]))),_:1},8,["icon","loading"]),s(_,{type:"primary",onClick:$e,icon:I(S)},{default:u((()=>t[11]||(t[11]=[r(" 手动调整匹配 ")]))),_:1},8,["icon"])])])])),default:u((()=>[s(R,{data:he.value,border:"",style:{width:"100%"},"span-method":De},{default:u((()=>[s(y,{type:"index",label:"序号",width:"55"}),s(y,{prop:"product_name",label:"产品名称 (原始)","min-width":"150"}),s(y,{prop:"product_model",label:"产品型号 (原始)","min-width":"120"}),s(y,{prop:"product_spec",label:"产品规格 (原始)","min-width":"150"}),s(y,{prop:"quantity",label:"数量",width:"80"}),s(y,{prop:"unit",label:"单位",width:"80"}),s(y,{label:"匹配产品","min-width":"180"},{default:u((({row:e})=>[e.matched_product?(l(),d("div",Z,[n("p",null,[t[13]||(t[13]=n("strong",null,"名称:",-1)),r(" "+w(e.matched_product.name),1)]),n("p",null,[t[14]||(t[14]=n("strong",null,"型号:",-1)),r(" "+w(e.matched_product.model),1)])])):(l(),d("span",ee,"-"))])),_:1}),s(y,{label:"匹配规格","min-width":"150"},{default:u((({row:e})=>[e.matched_specification?(l(),d("span",te,w(e.matched_specification.specification),1)):(l(),d("span",oe,"-"))])),_:1}),s(y,{prop:"notes",label:"备注 (含系统)","min-width":"200"},{default:u((({row:e})=>[n("div",{innerHTML:be(e.notes)},null,8,ae)])),_:1}),de.value&&"draft"===de.value.status?(l(),i(y,{key:0,label:"操作",width:"120",fixed:"right"},{default:u((({row:e})=>[e.matched_product_specification_id?(l(),d("span",le,"-")):(l(),i(_,{key:0,type:"primary",link:"",size:"small",onClick:t=>(async e=>{var t;J.value=!0,console.log("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 为单个项目打开对话框:",e);try{console.log("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 开始获取最新报价需求数据");const a=await je();if(console.log("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 获取到最新报价需求数据:",a),!["draft","submitted","草稿","已提交"].includes(a.status||""))return g.warning(`报价需求当前状态为 "${a.status}"，不允许修改项目匹配。请确保状态为草稿或已提交。`),void(J.value=!1);const l=null==(t=a.items)?void 0:t.find((t=>t.id===e.id));if(console.log("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 在最新数据中查找项目:",l),!l)return g.error("无法在最新的需求中找到该项目，可能已被修改或删除。"),void(J.value=!1);M.value=[{...JSON.parse(JSON.stringify(l)),form_matched_product_id:l.matched_product_id||null,form_matched_specification_id:l.matched_product_specification_id||null,product_name:l.original_product_name||l.product_name,product_model:l.original_product_model||l.product_model,product_spec:l.original_product_spec||l.product_spec}],console.log("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 已准备标准化项目数据:",M.value),T.value=!1,j.value=[],ne.value=!1,console.log("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 开始加载产品选项");try{await Ve(!0),console.log("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 产品选项加载完成，数量:",j.value.length)}catch(o){console.error("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 加载产品选项失败:",o),g.warning("加载产品选项失败，但您仍可以通过搜索添加产品")}console.log("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 开始自动匹配尝试"),M.value.length>0&&!M.value[0].form_matched_product_id&&await Se(M.value[0]),k.value=!0,console.log("[QuotationRequestDetail.vue] openStandardizeDialogForItem - 对话框已显示")}catch(o){console.error("[QuotationRequestDetail.vue] 为单个条目打开手动调整对话框失败:",o),g.error(o instanceof Error?o.message:"加载项目进行调整失败，请重试。")}finally{J.value=!1}})(e)},{default:u((()=>t[15]||(t[15]=[r(" 调整匹配 ")]))),_:2},1032,["onClick"]))])),_:1})):m("",!0)])),_:1},8,["data"])])),_:1})):m("",!0),re.value||de.value?m("",!0):(l(),i($,{key:2,description:"未找到报价需求信息"})),s(V,{modelValue:k.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k.value=e),title:"需求条目匹配调整",width:"70%","destroy-on-close":"",top:"5vh"},{footer:u((()=>[n("div",ue,[s(_,{onClick:t[0]||(t[0]=e=>k.value=!1)},{default:u((()=>t[21]||(t[21]=[r("取消")]))),_:1}),s(_,{type:"primary",onClick:Ce,loading:J.value},{default:u((()=>t[22]||(t[22]=[r("确认更新匹配")]))),_:1},8,["loading"])])])),default:u((()=>[s(R,{data:M.value,border:"",stripe:"","max-height":"calc(90vh - 150px)"},{default:u((()=>[s(y,{type:"index",label:"#",width:"45",fixed:""}),s(y,{prop:"product_name",label:"原始名称","min-width":"150","show-overflow-tooltip":"",fixed:""},{default:u((({row:e})=>[r(w(e.product_name),1)])),_:1}),s(y,{prop:"product_model",label:"原始型号","min-width":"120","show-overflow-tooltip":""},{default:u((({row:e})=>[r(w(e.product_model||"---"),1)])),_:1}),s(y,{prop:"product_spec",label:"原始规格","min-width":"150","show-overflow-tooltip":""},{default:u((({row:e})=>[r(w(e.product_spec||"---"),1)])),_:1}),s(y,{prop:"quantity",label:"数量",width:"80"}),s(y,{prop:"unit",label:"单位",width:"80"}),s(y,{label:"匹配产品","min-width":"200"},{default:u((({row:e})=>[s(P,{modelValue:e.form_matched_product_id,"onUpdate:modelValue":t=>e.form_matched_product_id=t,filterable:"",clearable:"",placeholder:"选择或搜索系统产品",style:{width:"100%"},onChange:t=>xe(e,t),"loading-text":"加载产品中...",remote:"","remote-method":Pe,onFocus:Ve},{default:u((()=>[(l(!0),d(p,null,c(j.value,(e=>(l(),i(A,{key:e.id,label:`${e.name} (${e.model||"无型号"}) - [ID:${e.id}]`,value:e.id},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),s(y,{label:"匹配规格","min-width":"200"},{default:u((({row:e})=>[s(P,{modelValue:e.form_matched_specification_id,"onUpdate:modelValue":t=>e.form_matched_specification_id=t,filterable:"",clearable:"",disabled:!e.form_matched_product_id||E.value[e.form_matched_product_id]&&0===E.value[e.form_matched_product_id].length,placeholder:"选择产品规格",style:{width:"100%"},"no-data-text":"该产品无可选规格或未选择产品"},{default:u((()=>[(l(!0),d(p,null,c(E.value[e.form_matched_product_id]||[],(e=>(l(),i(A,{key:e.id,label:`${e.specification} (税率:${e.tax_rate}%, 价:${e.suggested_price})`,value:e.id},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),s(y,{label:"匹配状态与操作",width:"150",fixed:"right"},{default:u((({row:e})=>[n("div",null,[e.form_matched_product_id&&e.form_matched_specification_id?(l(),i(v,{key:0,type:"success",class:"mb-5",style:{width:"100%","text-align":"center",display:"block"}},{default:u((()=>t[16]||(t[16]=[r(" 完整匹配 ")]))),_:1})):e.form_matched_product_id?(l(),i(v,{key:1,type:"warning",class:"mb-5",style:{width:"100%","text-align":"center",display:"block"}},{default:u((()=>t[17]||(t[17]=[r(" 仅产品匹配 ")]))),_:1})):(l(),i(v,{key:2,type:"danger",class:"mb-5",style:{width:"100%","text-align":"center",display:"block"}},{default:u((()=>t[18]||(t[18]=[r(" 未匹配 ")]))),_:1}))]),n("div",ie,[e.form_matched_product_id&&!e.form_matched_specification_id?(l(),i(_,{key:0,type:"primary",link:"",size:"small",onClick:t=>{var o;(o=e).form_matched_product_id?(console.log("Triggered handleAddSpecification for item:",o),ge.value=o.form_matched_product_id,fe.value=o,_e.value=!0):g.warning("请先为该条目选择一个产品！")},style:{width:"100%"}},{default:u((()=>t[19]||(t[19]=[r(" 添加规格 ")]))),_:2},1032,["onClick"])):m("",!0),s(_,{type:"success",link:"",size:"small",onClick:t=>{return o=e,console.log("[QuotationRequestDetail.vue] handleAddNewProduct - item to be passed:",JSON.parse(JSON.stringify(o))),me.value=o,void(pe.value=!0);var o},style:{width:"100%","margin-top":"3px",display:"block"}},{default:u((()=>t[20]||(t[20]=[r(" 添加产品库 ")]))),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])])),_:1},8,["modelValue"]),s(K,{visible:pe.value,"quotation-item":me.value,title:"从报价行添加新产品到产品库","onUpdate:visible":t[2]||(t[2]=e=>pe.value=e),onProductCreated:Fe,onClosed:t[3]||(t[3]=e=>me.value=null)},null,8,["visible","quotation-item"]),s(Y,{visible:_e.value,"product-id":ge.value,"quotation-item":fe.value,title:"为产品添加新规格","onUpdate:visible":t[4]||(t[4]=e=>_e.value=e),onSpecificationAdded:Ue,onClosed:ze},null,8,["visible","product-id","quotation-item"])])),[[x,re.value]])}}},[["__scopeId","data-v-a3a63a64"]]);export{ne as default};
