<template>
  <div class="statement-detail">
    <div class="page-header">
      <el-page-header @back="handleBack" :content="pageTitle" />
      <div class="header-actions">
        <el-button v-if="statement?.status === '待确认'" type="primary" @click="handleConfirm">
          确认对账单
        </el-button>
        <el-button v-if="['已确认', '部分收款'].includes(statement?.status)" type="success" @click="handlePayment">
          收款
        </el-button>
        <el-button v-if="needsRefund" type="danger" @click="handleRefund">
          退款
        </el-button>
        <el-button v-if="['已确认', '部分收款'].includes(statement?.status)" type="warning" @click="handleForceSettle">
          强制结清
        </el-button>
        <el-button v-if="statement?.status === '待确认'" @click="handleEdit">
          编辑
        </el-button>
        <el-button type="info" @click="handleExport">
          导出对账单
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="content">
      <el-card v-if="statement" class="statement-info">
        <template #header>
          <div class="card-header">
            <span>对账单信息</span>
            <el-tag :type="getStatusType(statement.status)">
              {{ getStatusText(statement.status) }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="对账单号">
            {{ statement.statement_number }}
          </el-descriptions-item>
          <el-descriptions-item label="客户名称">
            {{ statement.customer?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="对账日期">
            {{ statement.statement_date }}
          </el-descriptions-item>
          <el-descriptions-item label="应付款日期">
            {{ statement.due_date || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(statement.status)">
              {{ getStatusText(statement.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ statement.notes || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="发货单总金额">
            {{ formatAmount(deliveryTotalAmount) }}
          </el-descriptions-item>
          <el-descriptions-item label="退货单总金额">
            {{ formatAmount(returnTotalAmount) }}
          </el-descriptions-item>
          <el-descriptions-item label="总金额">
            {{ formatAmount(statement.total_amount) }}
          </el-descriptions-item>
          <el-descriptions-item label="优惠金额">
            {{ formatAmount(statement.discount_amount || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="实际金额" :span="2">
            <span class="final-amount">{{ formatAmount(statement?.adjusted_total_amount || statement?.total_amount || 0) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="已收金额">
            <span class="paid-amount">{{ formatAmount(statement?.paid_amount || 0) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="未收金额">
            <span class="unpaid-amount">{{ formatAmount(unpaidAmount) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="结清日期" v-if="statement?.settlement_date">
            {{ statement.settlement_date }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="statement?.settlement_date ? 1 : 2">
            {{ formatDateTime(statement.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" v-if="!statement?.settlement_date">
            {{ formatDateTime(statement.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 发货单列表 -->
      <el-card v-if="statement?.delivery_notes?.length" class="delivery-notes">
        <template #header>
          <div class="card-header">
            <span>关联发货单 ({{ statement.delivery_notes.length }})</span>
            <span class="amount">发货单总金额：{{ formatAmount(deliveryTotalAmount) }}</span>
          </div>
        </template>

        <el-table :data="statement.delivery_notes" border>
          <el-table-column prop="delivery_number" label="发货单号" width="180" />
          <el-table-column label="订单号" width="180">
            <template #default="{ row }">
              {{ row.order?.order_number || row.order_number || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="delivery_date" label="发货日期" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getDeliveryStatusType(row.status)">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="发货金额" width="120">
            <template #default="{ row }">
              {{ calculateDeliveryAmount(row) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button link type="primary" @click="viewDeliveryNote(row.id)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>



      <!-- 退货单列表 -->
      <el-card v-if="statement?.return_orders?.length" class="return-orders">
        <template #header>
          <div class="card-header">
            <span>关联退货单 ({{ statement.return_orders.length }})</span>
            <span class="amount negative">退货单总金额：{{ formatAmount(returnTotalAmount) }}</span>
          </div>
        </template>

        <el-table :data="statement.return_orders" border>
          <el-table-column prop="return_number" label="退货单号" width="180" />
          <el-table-column prop="order_number" label="订单号" width="180" />
          <el-table-column prop="return_date" label="退货日期" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getReturnStatusType(row.status)">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="退货金额" width="120">
            <template #default="{ row }">
              {{ formatAmount(row.total_amount) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button link type="primary" @click="viewReturnOrder(row.id)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 收款记录列表 -->
      <el-card v-if="paymentRecords.length > 0" class="payment-records">
        <template #header>
          <div class="card-header">
            <span>收款记录 ({{ paymentRecords.length }})</span>
            <span class="amount positive">已收金额：{{ formatAmount(statement?.paid_amount || 0) }}</span>
          </div>
        </template>

        <el-table :data="paymentRecords" border>
          <el-table-column prop="payment_date" label="收款日期" width="120" />
          <el-table-column prop="amount" label="收款金额" width="120">
            <template #default="{ row }">
              <span class="amount-text positive">{{ formatAmount(row.amount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="payment_method" label="支付方式" width="120">
            <template #default="{ row }">
              {{ getPaymentMethodText(row.payment_method) }}
            </template>
          </el-table-column>
          <el-table-column prop="payment_source" label="付款来源" width="100">
            <template #default="{ row }">
              <el-tag :type="row.payment_source === 'balance' ? 'success' : 'primary'" size="small">
                {{ row.payment_source === 'balance' ? '余额支付' : '直接付款' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="收款凭据" width="120">
            <template #default="{ row }">
              <div v-if="row.voucher_files && row.voucher_files.length > 0" class="voucher-files">
                <el-tooltip
                  v-for="(file, index) in row.voucher_files"
                  :key="index"
                  :disabled="!isImageFile(file)"
                  placement="right"
                  :show-after="300"
                  :hide-after="100"
                  popper-class="voucher-image-tooltip"
                >
                  <template #content>
                    <div class="voucher-preview">
                      <img
                        :src="file.url"
                        :alt="file.original_filename"
                        class="voucher-preview-image"
                        @error="handleImageError"
                      />
                      <div class="voucher-preview-info">
                        <p class="filename">{{ file.original_filename }}</p>
                        <p class="filesize">{{ formatFileSize(file.size) }}</p>
                      </div>
                    </div>
                  </template>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="previewVoucher(file)"
                    class="voucher-button"
                  >
                    <el-icon><Picture /></el-icon>
                    凭据{{ index + 1 }}
                  </el-button>
                </el-tooltip>
              </div>
              <span v-else class="no-voucher">无凭据</span>
            </template>
          </el-table-column>
          <el-table-column prop="reference_number" label="交易流水号" width="150" />
          <el-table-column prop="bank_account" label="收款账户" width="180" />
          <el-table-column prop="notes" label="备注" min-width="150" />
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 退款记录列表 -->
      <el-card v-if="refundRecords.length > 0" class="refund-records">
        <template #header>
          <div class="card-header">
            <span>退款记录 ({{ refundRecords.length }})</span>
            <span class="amount negative">已退金额：{{ formatRefundAmount() }}</span>
          </div>
        </template>

        <el-table :data="refundRecords" border>
          <el-table-column prop="refund_date" label="退款日期" width="120" />
          <el-table-column prop="amount" label="退款金额" width="120">
            <template #default="{ row }">
              <span class="amount-text negative">{{ formatAmount(row.amount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="refund_method" label="退款方式" width="120">
            <template #default="{ row }">
              {{ getRefundMethodText(row.refund_method) }}
            </template>
          </el-table-column>
          <el-table-column prop="refund_target" label="退款目标" width="100">
            <template #default="{ row }">
              <el-tag :type="row.refund_target === 'balance' ? 'success' : 'warning'" size="small">
                {{ row.refund_target === 'balance' ? '余额退款' : '直接退款' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="reference_number" label="交易流水号" width="150">
            <template #default="{ row }">
              {{ row.reference_number || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="bank_account" label="退款账户" width="180">
            <template #default="{ row }">
              {{ row.bank_account || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" min-width="150">
            <template #default="{ row }">
              {{ row.notes || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getRefundStatusType(row.status)" size="small">
                {{ getRefundStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>

    </div>

    <!-- 确认对话框 -->
    <el-dialog
      v-model="confirmDialogVisible"
      title="确认对账单"
      width="500px"
    >
      <el-form
        ref="confirmFormRef"
        :model="confirmForm"
        :rules="confirmRules"
        label-width="100px"
      >
        <el-form-item label="应付款日期" prop="due_date">
          <el-date-picker
            v-model="confirmForm.due_date"
            type="date"
            placeholder="请选择应付款日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="confirmDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 收款对话框 -->
    <StatementPaymentDialog
      v-model="paymentDialogVisible"
      :statement-id="Number(statement?.id || 0)"
      :statement-info="statement"
      @success="handlePaymentSuccess"
    />

    <!-- 退款对话框 -->
    <StatementRefundDialog
      v-model="refundDialogVisible"
      :statement-id="Number(statement?.id || 0)"
      :statement-info="statement"
      @success="handleRefundSuccess"
    />

    <!-- 导出对话框 -->
    <StatementExportDialog
      v-model="exportDialogVisible"
      :statement-id="statement?.id"
      :statement-name="statement?.statement_number"
      @success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import type { Statement, StatementStatus } from '@/types/statement'
import { statementApi } from '@/api/statement'
import { statementRefundApi } from '@/api/statementRefund'
import { formatCurrency, formatDateTime } from '@/utils/formatter'
import StatementPaymentDialog from '@/components/StatementPaymentDialog.vue'
import StatementRefundDialog from '@/components/StatementRefundDialog.vue'
import StatementExportDialog from '@/components/StatementExportDialog.vue'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const statement = ref<Statement | null>(null)
const paymentRecords = ref<any[]>([])
const refundRecords = ref<any[]>([])

// 确认对话框
const confirmDialogVisible = ref(false)
const confirmFormRef = ref<FormInstance>()
const confirmForm = reactive({
  due_date: ''
})
const confirmRules = {
  due_date: [
    { required: true, message: '请选择应付款日期', trigger: 'change' }
  ]
}

// 收款对话框
const paymentDialogVisible = ref(false)

// 退款对话框
const refundDialogVisible = ref(false)

// 导出对话框
const exportDialogVisible = ref(false)

// 计算属性
const pageTitle = computed(() => {
  return statement.value ? `对账单详情 - ${statement.value.statement_number}` : '对账单详情'
})



const deliveryTotalAmount = computed(() => {
  if (!statement.value?.delivery_notes) return 0
  return statement.value.delivery_notes.reduce((total, deliveryNote) => {
    const amount = parseFloat(deliveryNote.total_amount || '0')
    return total + amount
  }, 0)
})

const returnTotalAmount = computed(() => {
  if (!statement.value?.return_orders) return 0
  return statement.value.return_orders.reduce((total, returnOrder) => {
    const amount = parseFloat(returnOrder.total_amount || '0')
    return total + amount
  }, 0)
})

// 计算未收金额
const unpaidAmount = computed(() => {
  const totalAmount = parseFloat(statement.value?.adjusted_total_amount || '0')
  const paidAmount = parseFloat(statement.value?.paid_amount || '0')
  return Math.max(0, totalAmount - paidAmount)
})

// 计算是否需要退款
const needsRefund = computed(() => {
  if (!statement.value) return false
  const total = parseFloat(statement.value.adjusted_total_amount || '0')
  const paid = parseFloat(statement.value.paid_amount || '0')
  return paid > total && ['已确认', '部分收款', '已结清'].includes(statement.value.status)
})

// 计算可退款金额
const refundableAmount = computed(() => {
  if (!statement.value) return 0
  const total = parseFloat(statement.value.adjusted_total_amount || '0')
  const paid = parseFloat(statement.value.paid_amount || '0')
  return Math.max(0, paid - total)
})

// 获取对账单详情
const fetchStatement = async () => {
  const id = route.params.id as string
  if (!id) {
    ElMessage.error('对账单ID不存在')
    router.push('/statements')
    return
  }

  loading.value = true
  try {
    const response = await statementApi.getById(id)
    statement.value = response.data || response
    console.log('获取对账单详情:', statement.value)
    // 获取收款记录和退款记录
    await fetchPaymentRecords()
    await fetchRefundRecords()
  } catch (error) {
    console.error('获取对账单详情失败:', error)
    ElMessage.error('获取对账单详情失败')
    router.push('/statements')
  } finally {
    loading.value = false
  }
}

// 获取收款记录
const fetchPaymentRecords = async () => {
  const id = route.params.id as string
  if (!id) return

  try {
    const response = await statementApi.getPaymentRecords({
      statement_id: Number(id),
      per_page: 100
    })
    if (response.code === 200) {
      paymentRecords.value = response.data || []
    }
  } catch (error) {
    console.error('获取收款记录失败:', error)
  }
}

// 获取退款记录
const fetchRefundRecords = async () => {
  const id = route.params.id as string
  if (!id) return

  try {
    const response = await statementRefundApi.getByStatementId(Number(id))
    // 检查响应数据结构
    if (response.refunds) {
      // 如果响应直接包含refunds数组
      refundRecords.value = response.refunds || []
    } else if (response.data?.refunds) {
      // 如果响应嵌套在data中
      refundRecords.value = response.data.refunds || []
    } else if (response.code === 200 && response.data?.refunds) {
      // 标准响应格式
      refundRecords.value = response.data.refunds || []
    }
  } catch (error) {
    console.error('获取退款记录失败:', error)
  }
}

// 返回列表
const handleBack = () => {
  router.push('/statements')
}

// 编辑对账单
const handleEdit = () => {
  router.push(`/statements/edit/${statement.value?.id}`)
}

// 收款操作
const handlePayment = () => {
  paymentDialogVisible.value = true
}

// 退款操作
const handleRefund = () => {
  refundDialogVisible.value = true
}

// 强制结清
const handleForceSettle = () => {
  const unpaid = unpaidAmount.value
  const message = unpaid > 0
    ? `当前还有 ${formatAmount(unpaid)} 未收款，确定要强制结清吗？`
    : '确定要结清此对账单吗？'

  ElMessageBox.confirm(
    message,
    '强制结清确认',
    {
      confirmButtonText: '确定结清',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await statementApi.forceSettle(statement.value!.id)
      ElMessage.success('对账单已强制结清')
      await fetchStatement() // 刷新数据
    } catch (error) {
      console.error('强制结清失败:', error)
      ElMessage.error('强制结清失败')
    }
  }).catch(() => {
    // 用户取消
  })
}

// 收款成功回调
const handlePaymentSuccess = (data: any) => {
  console.log('收款成功:', data)
  ElMessage.success('收款成功')
  // 刷新对账单信息
  fetchStatement()
}

// 退款成功回调
const handleRefundSuccess = (data: any) => {
  console.log('退款成功:', data)
  ElMessage.success('退款成功')
  // 刷新对账单信息和退款记录
  fetchStatement()
  fetchRefundRecords()
}

// 获取支付方式文本
const getPaymentMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    'bank_transfer': '银行转账',
    'cash': '现金支付',
    'wechat': '微信',
    'alipay': '支付宝',
    'balance': '余额支付',
    'other': '其他'
  }
  return methodMap[method] || method
}

// 获取退款方式文本
const getRefundMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    'cash': '现金',
    'bank_transfer': '银行转账',
    'alipay': '支付宝',
    'wechat': '微信支付',
    'balance': '余额退款',
    'other': '其他'
  }
  return methodMap[method] || method
}

// 获取退款状态文本
const getRefundStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 获取退款状态类型
const getRefundStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'failed': 'danger',
    'cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

// 计算总退款金额
const formatRefundAmount = () => {
  const totalRefund = refundRecords.value.reduce((sum, record) => {
    return sum + parseFloat(record.amount || '0')
  }, 0)
  return formatAmount(totalRefund)
}

// 判断是否为图片文件
const isImageFile = (file: any) => {
  if (!file || !file.type) return false
  return file.type.startsWith('image/')
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/src/assets/images/image-error.png' // 可以设置一个默认的错误图片
  console.warn('图片加载失败:', img.src)
}

// 预览收款凭据
const previewVoucher = (file: any) => {
  if (file.url) {
    // 如果是图片，在新窗口中打开
    if (file.type && file.type.startsWith('image/')) {
      window.open(file.url, '_blank')
    } else {
      // 其他文件类型，下载
      const link = document.createElement('a')
      link.href = file.url
      link.download = file.original_filename || file.filename
      link.click()
    }
  }
}

// 确认对账单
const handleConfirm = () => {
  if (statement.value?.due_date) {
    confirmForm.due_date = statement.value.due_date
  } else {
    confirmForm.due_date = ''
  }
  confirmDialogVisible.value = true
}

// 提交确认
const submitConfirm = async () => {
  if (!confirmFormRef.value || !statement.value) return

  await confirmFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await statementApi.confirm(statement.value!.id, {
          due_date: confirmForm.due_date
        })
        ElMessage.success('确认对账单成功')
        confirmDialogVisible.value = false
        await fetchStatement()
      } catch (error) {
        console.error('确认对账单失败:', error)
        ElMessage.error('确认对账单失败')
      }
    }
  })
}

// 导出对账单
const handleExport = () => {
  exportDialogVisible.value = true
}

// 导出成功处理
const handleExportSuccess = () => {
  // 导出成功后的处理逻辑
}

// 查看发货单
const viewDeliveryNote = (id: string) => {
  router.push(`/delivery-notes/view/${id}`)
}

// 查看退货单
const viewReturnOrder = (id: string) => {
  router.push(`/returns/view/${id}`)
}

// 计算发货单金额
const calculateDeliveryAmount = (deliveryNote: any) => {
  // 直接使用数据库中的total_amount字段
  return formatAmount(deliveryNote.total_amount || 0)
}

// 状态处理
const getStatusType = (status: StatementStatus) => {
  const statusMap: Record<string, string> = {
    'draft': 'info',
    'confirmed': 'primary',
    'paid': 'success',
    'cancelled': 'info',
    '草稿': 'info',
    '待确认': 'warning',
    '已确认': 'primary',
    '部分收款': 'warning',
    '已结清': 'success',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: StatementStatus) => {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'confirmed': '已确认',
    'paid': '已结清',
    'cancelled': '已取消',
    '草稿': '草稿',
    '待确认': '待确认',
    '已确认': '已确认',
    '部分收款': '部分收款',
    '已结清': '已结清',
    '已取消': '已取消'
  }
  return statusMap[status] || status
}

const getDeliveryStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待发货': 'warning',
    '已发货': 'primary',
    '已签收': 'success',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

const getReturnStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待确认': 'warning',
    '退货中': 'primary',
    '已签收': 'success',
    '已拒绝': 'danger',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

// 格式化金额
const formatAmount = (amount: number | string) => {
  if (amount === undefined || amount === null) return formatCurrency(0)
  return formatCurrency(amount)
}

onMounted(() => {
  fetchStatement()
})
</script>

<style lang="scss" scoped>
.statement-detail {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 0;
    border-bottom: 1px solid #ebeef5;

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .content {
    .statement-info {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .amount {
        font-size: 16px;
        font-weight: bold;
        color: #e6a23c;
      }

      .final-amount {
        font-size: 20px;
        font-weight: bold;
        color: #e6a23c;
        padding: 8px 16px;
        background-color: #fdf6ec;
        border-radius: 4px;
        border: 1px solid #f5dab1;
        display: inline-block;
      }

      .paid-amount {
        font-size: 16px;
        font-weight: bold;
        color: #67c23a;
      }

      .unpaid-amount {
        font-size: 16px;
        font-weight: bold;
        color: #f56c6c;
      }
    }

    .delivery-notes {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .return-orders {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .statement-summary {
      .amount {
        font-size: 16px;
        font-weight: bold;
        color: #67c23a;

        &.negative {
          color: #f56c6c;
        }
      }

      .total-amount {
        font-size: 18px;
        font-weight: bold;
        color: #e6a23c;
      }
    }

    .payment-records {
      margin-top: 20px;

      .voucher-files {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .no-voucher {
        color: #909399;
        font-size: 12px;
      }

      .amount-text {
        font-weight: 600;

        &.positive {
          color: #67c23a;
        }

        &.negative {
          color: #f56c6c;
        }
      }

      .voucher-button {
        margin-bottom: 2px;

        &:hover {
          background-color: rgba(64, 158, 255, 0.1);
        }
      }
    }

    .refund-records {
      margin-top: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .amount {
          font-size: 16px;
          font-weight: bold;

          &.negative {
            color: #f56c6c;
          }
        }
      }

      .amount-text {
        font-weight: 600;

        &.negative {
          color: #f56c6c;
        }
      }
    }
  }
}

/* 全局样式：收款凭据悬浮预览 */
.voucher-image-tooltip {
  max-width: 400px !important;
  padding: 8px !important;

  .voucher-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .voucher-preview-image {
      max-width: 300px;
      max-height: 200px;
      object-fit: contain;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .voucher-preview-info {
      text-align: center;

      .filename {
        margin: 0;
        font-size: 12px;
        color: #303133;
        font-weight: 500;
        word-break: break-all;
      }

      .filesize {
        margin: 4px 0 0 0;
        font-size: 11px;
        color: #909399;
      }
    }
  }
}
</style>
