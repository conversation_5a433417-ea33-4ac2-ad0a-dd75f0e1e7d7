<template>
  <div v-loading="loading" class="quotation-request-detail">
    <!-- 头部卡片 -->
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="form-title">报价需求详情</h2>
        <div>
          <el-button @click="goBack">返回</el-button>
          <el-button
            v-if="quotationRequest && quotationRequest.status === '待确认'"
            type="primary"
            @click="handleEdit"
          >
            编辑
          </el-button>
          <el-tooltip
            v-if="quotationRequest && quotationRequest.status === '待确认'"
            :content="hasUnmatchedProducts ? `有产品未匹配：${unmatchedProductNames.join('、')}` : '确认需求'"
            placement="top"
          >
            <el-button
              :type="hasUnmatchedProducts ? 'info' : 'success'"
              :loading="markAsOfficialLoading"
              :disabled="isConfirmDisabled"
              @click="handleMarkAsOfficial"
            >
              确认需求
            </el-button>
          </el-tooltip>
          <el-button
            v-if="quotationRequest && quotationRequest.status === '已确认' && (!quotationRequest.quotations || quotationRequest.quotations.length === 0)"
            type="success"
            @click="handleCreateQuotation"
          >
            创建报价单
          </el-button>
          <el-button
            v-if="quotationRequest && quotationRequest.status === '已确认' && (!quotationRequest.quotations || quotationRequest.quotations.length === 0)"
            type="warning"
            @click="handleCancelConfirm"
          >
            取消确认
          </el-button>
          <el-button type="primary" @click="showExportDialog">导出需求表</el-button>
        </div>
      </div>
    </el-card>

    <!-- 基本信息 -->
    <el-card v-if="quotationRequest" class="mb-20">
      <template #header>
        <h3>基本信息</h3>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="需求表号">{{ quotationRequest.request_number }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ customerDetails?.name || (quotationRequest ? 'ID: ' + quotationRequest.customer_id : '加载中...') }}</el-descriptions-item>
        <el-descriptions-item label="项目名称">{{ quotationRequest.project_name }}</el-descriptions-item>
        <el-descriptions-item label="项目地址">{{ quotationRequest.project_address }}</el-descriptions-item>
        <el-descriptions-item label="期望货期">{{ quotationRequest.expected_date ? formatDate(quotationRequest.expected_date) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(quotationRequest.status)">{{ quotationRequest.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ quotationRequest.created_at ? formatDateTime(quotationRequest.created_at) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ quotationRequest.updated_at ? formatDateTime(quotationRequest.updated_at) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ quotationRequest.notes }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card v-if="quotationRequest && quotationRequest.items && quotationRequest.items.length > 0" class="box-card items-card">
      <template #header>
        <div class="card-header">
          <span>需求项目列表</span>
        </div>
      </template>
      <el-table :data="quotationRequest.items" border style="width: 100%">
        <el-table-column type="index" label="序号" width="55" />

        <el-table-column label="产品名称" min-width="150">
          <template #default="{ row }">
            <div>
              <div class="original-value">{{ row.product_name }}</div>
              <div v-if="row.matched_product" class="matched-info">
                <el-icon><Link /></el-icon>
                <span>{{ row.matched_product.name }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="产品型号" min-width="120">
          <template #default="{ row }">
            <div>
              <div class="original-value">{{ row.product_model || '-' }}</div>
              <div v-if="row.matched_product" class="matched-info">
                <el-icon><Link /></el-icon>
                <span>{{ row.matched_product.model || '-' }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="产品规格" min-width="150">
          <template #default="{ row }">
            <div>
              <div class="original-value">{{ row.product_spec || '-' }}</div>
              <div v-if="row.matched_specification" class="matched-info">
                <el-icon><Link /></el-icon>
                <span>{{ row.matched_specification.specification }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="quantity" label="数量" width="80" />

        <el-table-column label="单位" width="80">
          <template #default="{ row }">
            <div>
              <div class="original-value">{{ row.unit }}</div>
              <div v-if="row.matched_product" class="matched-info">
                <el-icon><Link /></el-icon>
                <span>{{ row.matched_product.unit || '-' }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="匹配状态" width="120">
          <template #default="{ row }">
            <div v-if="row.matched_product_id">
              <el-tag
                :type="row.matched_product_specification_id ? 'success' : 'warning'"
                size="small"
              >
                {{ row.matched_product_specification_id ? '已匹配' : '部分匹配' }}
              </el-tag>
              <div v-if="row.matched_specification" class="matched-price">
                ¥{{ row.matched_specification.suggested_price || '0.00' }}
              </div>
            </div>
            <el-tag v-else type="info" size="small">
              未匹配
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="notes" label="备注" min-width="200">
          <template #default="{ row }">
            <div v-html="formatNotes(row.notes)"></div>
          </template>
        </el-table-column>

      </el-table>
    </el-card>

    <!-- 关联报价单 -->
    <el-card v-if="quotationRequest && quotationRequest.quotations && quotationRequest.quotations.length > 0" class="box-card">
      <template #header>
        <div class="card-header">
          <span>关联报价单</span>
        </div>
      </template>
      <el-table :data="quotationRequest.quotations" border style="width: 100%">
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column prop="quotation_number" label="报价单号" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getQuotationStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_amount" label="总金额" width="120">
          <template #default="{ row }">
            ¥{{ Number(row.total_amount || 0).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewQuotation(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-empty v-if="!loading && !quotationRequest" description="未找到报价需求信息" />



    <!-- 导出配置对话框 -->
    <QuotationRequestExportDialog
      v-model="exportDialogVisible"
      :request-id="quotationRequest?.id"
      :project-name="quotationRequest?.project_name"
      @export-success="handleExportSuccess"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Link } from '@element-plus/icons-vue'

import { quotationRequestApi, quotationApi } from '@/api/quotation'
import { customerApi } from '@/api/customer'
import { productApi } from '@/api/product'
import { formatDateTime } from '@/utils/format'
import QuotationRequestExportDialog from '@/components/QuotationRequestExportDialog.vue'


const route = useRoute()
const router = useRouter()

const loading = ref(true)
const quotationRequest = ref<any>(null)
const customerDetails = ref<any>(null)
const exportDialogVisible = ref(false)
const autoStandardizeLoading = ref(false)
const markAsOfficialLoading = ref(false)





const pageTitle = computed(() => {
  return quotationRequest.value ? `报价需求: ${quotationRequest.value.request_number || '详情'}` : '报价需求详情'
})

// 获取报价需求详情
const getQuotationRequestDetail = async () => {
  try {
    loading.value = true
    const requestId = Number(route.params.id)
    const response = await quotationRequestApi.getById(requestId) as any
    quotationRequest.value = response.data || response
    
    // 获取客户详情
    if (quotationRequest.value.customer_id) {
      try {
        const customerResponse = await customerApi.getById(quotationRequest.value.customer_id) as any
        customerDetails.value = customerResponse.data || customerResponse
      } catch (error) {
        console.error('获取客户详情失败:', error)
      }
    }
  } catch (error) {
    console.error('获取报价需求详情失败:', error)
    ElMessage.error('获取报价需求详情失败')
  } finally {
    loading.value = false
  }
}

// 工具函数
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待确认': 'warning',
    '已确认': 'success'
  }
  return statusMap[status] || 'info'
}

const formatNotes = (notes: string) => {
  if (!notes) return '-'
  const systemNoteRegex = /(\[系统备注:[^\]]*\]|\[系统匹配信息:[^\]]*\])/g
  return notes.replace(systemNoteRegex, '<strong style="color: #E6A23C;">$1</strong>').replace(/\n/g, '<br>')
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 操作处理
const goBack = () => {
  router.back()
}

const handleEdit = () => {
  if (quotationRequest.value && quotationRequest.value.id) {
    router.push(`/quotation-requests/edit/${quotationRequest.value.id}`)
  }
}

const handleMarkAsOfficial = async () => {
  try {
    // 检查是否有未匹配的产品
    if (hasUnmatchedProducts.value) {
      ElMessage.warning(`以下产品尚未完全匹配，请先完成产品匹配：${unmatchedProductNames.value.join('、')}`)
      return
    }

    await ElMessageBox.confirm(
      '确认后将不能再修改需求表内容，是否继续？',
      '确认操作',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    markAsOfficialLoading.value = true
    await quotationRequestApi.updateStatus(quotationRequest.value.id, { status: '已确认' })
    ElMessage.success('需求已确认')
    getQuotationRequestDetail()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('确认需求失败:', error)
      ElMessage.error('确认需求失败')
    }
  } finally {
    markAsOfficialLoading.value = false
  }
}

const handleCreateQuotation = async () => {
  try {
    await ElMessageBox.confirm('确定要从此报价需求创建报价单吗？', '创建报价单', {
      type: 'warning'
    })

    // 跳转到新建报价单页面，传递报价需求表ID作为参数
    router.push(`/quotations/new?requestId=${quotationRequest.value.id}`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('跳转失败:', error)
    }
  }
}

const handleCancelConfirm = async () => {
  try {
    await ElMessageBox.confirm(
      '取消确认后需求表将变为待确认状态，是否继续？',
      '取消确认',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用API更新状态
    await quotationRequestApi.updateStatus(quotationRequest.value.id, { status: '待确认' })
    ElMessage.success('已取消确认')

    // 刷新详情
    getQuotationRequestDetail()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '取消确认失败')
    }
  }
}

// 显示导出对话框
const showExportDialog = () => {
  exportDialogVisible.value = true
}

// 导出成功回调
const handleExportSuccess = () => {
  // 可以在这里添加导出成功后的处理逻辑
}





// 获取报价单状态类型
const getQuotationStatusType = (status: string) => {
  switch (status) {
    case '待确认':
      return 'warning'
    case '已确认':
      return 'success'
    case '已拒绝':
      return 'danger'
    case '已过期':
      return 'info'
    default:
      return 'info'
  }
}

// 查看报价单详情
const handleViewQuotation = (quotation: any) => {
  router.push(`/quotations/view/${quotation.id}`)
}

// 检查是否有未匹配的产品
const hasUnmatchedProducts = computed(() => {
  if (!quotationRequest.value || !quotationRequest.value.items) {
    return false
  }
  return quotationRequest.value.items.some(item => !item.matched_product_specification_id)
})

// 获取未匹配产品的名称列表
const unmatchedProductNames = computed(() => {
  if (!quotationRequest.value || !quotationRequest.value.items) {
    return []
  }
  return quotationRequest.value.items
    .filter(item => !item.matched_product_specification_id)
    .map(item => item.product_name || '未命名产品')
})

// 确认按钮是否禁用
const isConfirmDisabled = computed(() => {
  return hasUnmatchedProducts.value
})

// 初始化
onMounted(() => {
  getQuotationRequestDetail()
})
</script>

<style lang="scss" scoped>
.quotation-request-detail {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-title {
  margin: 0;
  font-size: 18px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-5 {
  margin-bottom: 5px;
}

.items-card {
  .el-table {
    margin-top: 20px;
  }
}

.original-value {
  font-weight: 500;
  color: #303133;
}

.matched-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: var(--el-color-success);

  .el-icon {
    font-size: 12px;
  }
}

.matched-price {
  color: #e6a23c;
  font-weight: 500;
}

  .original-value {
    font-weight: 500;
    color: #303133;
  }

  .matched-info {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
    font-size: 12px;
    color: var(--el-color-success);

    .el-icon {
      font-size: 12px;
    }
  }

  .matched-price {
    margin-top: 2px;
    font-size: 12px;
    color: #e6a23c;
    font-weight: 500;
  }

</style>
