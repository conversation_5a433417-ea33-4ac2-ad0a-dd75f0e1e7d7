import{r as a,o as s,c as e,a as t,t as r,j as l,ar as o}from"./index-3d4c440c.js";import{_ as n}from"./_plugin-vue_export-helper-1b428a4d.js";const u={class:"test-cors-container"},c={key:0,class:"result"},i=n({__name:"TestCORS",setup(n){const i=a(null),p=async()=>{try{const a=await o.get("/cors-test");i.value=a.data}catch(a){i.value={error:a.message}}};return(a,o)=>(s(),e("div",u,[o[1]||(o[1]=t("h1",null,"CORS测试页面",-1)),t("button",{onClick:p},"测试CORS"),i.value?(s(),e("div",c,[o[0]||(o[0]=t("h3",null,"测试结果：",-1)),t("pre",null,r(i.value),1)])):l("",!0)]))}},[["__scopeId","data-v-497831f1"]]);export{i as default};
