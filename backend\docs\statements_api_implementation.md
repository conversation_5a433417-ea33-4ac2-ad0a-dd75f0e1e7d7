# 对账单管理API模块实现文档

## 概述

本文档描述了EMB系统对账单管理API模块的完整实现，包括所有API端点、功能特性、状态管理、确认流程、PDF导出和与发货单、退货单模块的协同工作。

## 模块特性

### 核心功能
- ✅ 对账单完整CRUD操作
- ✅ 基于发货单生成对账单
- ✅ 状态管理和确认流程
- ✅ 自动计算对账金额
- ✅ PDF导出功能（基础实现）
- ✅ 应收款自动生成
- ✅ 可用发货单查询
- ✅ 统计分析功能

### 技术特性
- ✅ Flask-RESTX框架，自动生成API文档
- ✅ 统一的响应格式和错误处理
- ✅ 完整的数据验证和约束检查
- ✅ 分页查询和多条件过滤
- ✅ 状态流转规则验证
- ✅ 数据库事务管理

## API端点详情

### 1. 对账单列表 `GET /api/v1/statements`

**功能**: 获取对账单列表，支持多条件搜索和分页
**查询参数**:
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 20, 最大: 100)
- `customer_id`: 客户ID过滤
- `statement_number`: 对账单编号搜索
- `status`: 对账单状态过滤
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `customer_name`: 客户名称搜索

**响应格式**:
```json
{
  "code": 200,
  "message": "获取对账单列表成功",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "pages": 5,
      "has_prev": false,
      "has_next": true
    }
  }
}
```

### 2. 创建对账单 `POST /api/v1/statements`

**功能**: 创建新的对账单
**请求体**:
```json
{
  "customer_id": 1,
  "statement_date": "2024-01-01",
  "due_date": "2024-01-31",
  "status": "草稿",
  "notes": "备注信息",
  "delivery_note_ids": [1, 2, 3]
}
```

**业务逻辑**:
- 验证客户存在
- 检查发货单状态（只有已签收的可以对账）
- 验证发货单属于同一客户
- 检查发货单是否已在其他对账单中
- 自动生成对账单编号 (ST + 日期 + 随机码)
- 自动计算总金额

### 3. 对账单详情 `GET /api/v1/statements/<id>`

**功能**: 获取指定对账单的详细信息
**响应**: 包含对账单完整信息、关联发货单信息和项目明细

### 4. 更新对账单 `PUT /api/v1/statements/<id>`

**功能**: 更新对账单信息
**限制**: 只有"草稿"和"待确认"状态的对账单可以编辑
**业务逻辑**:
- 支持修改基本信息和发货单关联
- 重新计算总金额

### 5. 删除对账单 `DELETE /api/v1/statements/<id>`

**功能**: 删除对账单
**限制**: 只有"草稿"状态的对账单可以删除

### 6. 状态管理 `PUT /api/v1/statements/<id>/status`

**功能**: 更新对账单状态
**状态流转规则**:
```
草稿 → [待确认, 已取消]
待确认 → [已确认, 已取消]
已确认 → [已收款]
已收款 → []
已取消 → []
```

**请求体**:
```json
{
  "status": "已确认",
  "notes": "确认备注"
}
```

**业务逻辑**:
- 验证状态流转合法性
- 状态为"已确认"时自动创建应收款记录

### 7. 确认对账单 `PUT /api/v1/statements/<id>/confirm`

**功能**: 快速确认对账单
**业务逻辑**:
- 将状态从"待确认"更新为"已确认"
- 自动创建应收款记录

### 8. 状态信息 `GET /api/v1/statements/statuses`

**功能**: 获取所有可用状态和流转规则
**响应**: 状态列表、流转规则和状态描述

### 9. 可用发货单 `GET /api/v1/statements/available-delivery-notes`

**功能**: 获取可用于对账的发货单列表
**查询参数**:
- `customer_id`: 客户ID过滤
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)

**业务逻辑**:
- 只显示已签收且未对账的发货单
- 计算每个发货单的金额
- 包含发货单项目明细

### 10. PDF导出 `GET /api/v1/statements/<id>/pdf`

**功能**: 导出对账单PDF文件
**实现**: 基础文本格式实现，可扩展为专业PDF生成

### 11. 基于客户创建 `POST /api/v1/statements/from-customer/<customer_id>`

**功能**: 基于客户生成对账单模板
**查询参数**:
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)

**响应**: 包含客户信息、可对账发货单列表和预填充的对账单数据
**业务逻辑**:
- 验证客户存在
- 查找指定时间范围内的可对账发货单
- 自动计算总金额
- 生成对账单模板

### 12. 统计信息 `GET /api/v1/statements/statistics`

**功能**: 获取对账单统计信息
**查询参数**:
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)

**响应**: 各状态对账单数量和金额统计

## 数据模型

### Statement (对账单)
- `id`: 主键
- `statement_number`: 对账单编号 (自动生成)
- `customer_id`: 关联客户ID
- `statement_date`: 对账日期
- `due_date`: 到期日期
- `status`: 对账单状态
- `total_amount`: 总金额 (自动计算)
- `notes`: 备注

### StatementDeliveryNote (对账单-发货单关联)
- `id`: 主键
- `statement_id`: 对账单ID
- `delivery_note_id`: 发货单ID
- `created_at`: 创建时间

## 业务规则

### 状态管理
1. **状态流转**: 严格按照预定义规则进行状态转换
2. **确认流程**: 支持对账单的确认和取消
3. **应收款生成**: 确认时自动创建应收款记录

### 金额计算
1. **自动计算**: 基于关联发货单项目自动计算总金额
2. **实时更新**: 修改发货单关联时重新计算金额
3. **精确计算**: 使用Decimal类型确保金额精度

### 数据完整性
1. **发货单验证**: 只能关联已签收且未对账的发货单
2. **客户一致性**: 所有发货单必须属于同一客户
3. **唯一性约束**: 发货单不能重复关联到多个对账单

## 测试覆盖

### 测试文件: `tests/test_statements.py`

包含11个主要测试用例:

1. **基础CRUD测试**
   - 获取空列表
   - 获取有数据列表
   - 获取详情
   - 404错误处理

2. **业务逻辑测试**
   - 创建对账单
   - 客户不存在处理
   - 状态更新
   - 对账单确认

3. **辅助功能测试**
   - 状态列表获取
   - 可用发货单查询
   - 统计信息

### 测试结果
```
11 passed, 5 warnings
```

## 集成说明

### 与发货单模块协同
1. **发货单验证**: 只能对账已签收的发货单
2. **金额计算**: 基于发货单项目和订单产品单价计算
3. **状态关联**: 发货单状态影响对账可用性

### 与财务模块协同
1. **应收款生成**: 确认对账单时自动创建应收款记录
2. **收款管理**: 支持基于对账单的收款流程
3. **财务统计**: 提供对账单统计分析

### 与客户模块协同
1. **客户验证**: 创建对账单时验证客户存在
2. **客户信息**: 显示客户名称和联系信息
3. **客户筛选**: 支持按客户筛选对账单

## 扩展建议

### 功能扩展
1. **批量操作**: 支持批量创建、确认对账单
2. **模板管理**: 对账单模板功能
3. **自动化**: 定期自动生成对账单
4. **通知系统**: 状态变化通知客户

### 技术优化
1. **PDF生成**: 集成专业PDF生成库 (如reportlab)
2. **电子签名**: 支持电子签名确认
3. **缓存优化**: 对频繁查询的数据添加缓存
4. **异步处理**: 大批量操作异步处理

### 监控和日志
1. **操作日志**: 记录所有对账单操作历史
2. **性能监控**: 监控API响应时间和错误率
3. **业务指标**: 对账效率、确认率等业务指标

## 部署说明

1. **API注册**: 已在应用初始化时自动注册到 `/api/v1/statements`
2. **数据库**: 使用现有的对账单相关表结构
3. **权限**: 建议配置适当的访问权限控制
4. **监控**: 建议配置API监控和日志记录

对账单管理API模块现已完全实现并通过测试，可以为EMB系统提供完整的对账单管理功能！
