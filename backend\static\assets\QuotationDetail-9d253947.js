import{J as e,u as l,b as a,r as t,L as o,e as r,f as u,o as n,c as d,i as s,h as i,a as c,k as p,t as m,g as _,j as f,l as v,F as b,m as h,U as w,W as g}from"./index-3d4c440c.js";import{g as y,c as x,e as D}from"./quotation-219e97aa.js";import{b as C}from"./formatter-5775d610.js";import{_ as k}from"./_plugin-vue_export-helper-1b428a4d.js";const V={key:0,class:"quotation-detail"},E={class:"flex-between"},I={class:"dialog-footer"},Q=k(e({__name:"QuotationDetail",setup(e){const k=l(),Q=a(),U=t("basic"),j=t(null),A=t(!1),$=t(!1),q=[{prop:"product_name_snapshot",label:"产品名称"},{prop:"product_model_snapshot",label:"型号"},{prop:"product_spec_snapshot",label:"规格"},{prop:"product_unit_snapshot",label:"单位"},{prop:"quantity",label:"数量"},{prop:"unit_price",label:"单价"},{prop:"discount",label:"折扣(%)"},{prop:"tax_rate",label:"税率(%)"},{prop:"total_price",label:"金额"},{prop:"notes",label:"备注"}],N=o({format:"xlsx",includeHeader:!0,selectedColumns:q.map((e=>e.prop)),selectAll:!0}),L=e=>{N.selectedColumns=e?q.map((e=>e.prop)):[]},S=()=>{N.selectAll=N.selectedColumns.length===q.length},P=()=>{N.format="xlsx",N.includeHeader=!0,N.selectedColumns=q.map((e=>e.prop)),N.selectAll=!0,A.value=!0},B=({row:e,column:l,rowIndex:a,columnIndex:t})=>{if(["product_id","name","model"].includes(l.property))return e.isFirstSpec?{rowspan:e.rowSpan,colspan:1}:{rowspan:0,colspan:0}},F=e=>{const{columns:l,data:a}=e,t=[];return l.forEach(((e,l)=>{if(0!==l)if(["quantity","total_price"].includes(e.property)){const o=a.map((l=>{const a=Number(l[e.property]??0);return isNaN(a)?0:a})).reduce(((e,l)=>e+l),0);"total_price"===e.property?t[l]=C(o):t[l]=o.toString()}else t[l]="";else t[l]="合计"})),t},H=e=>({draft:"info",pending_approval:"warning",approved:"success",rejected:"danger",sent:"primary",accepted:"success",declined:"danger",expired:"info"}[e]||"info"),R=()=>{Q.back()},M=()=>{var e;Q.push({name:"QuotationEdit",params:{id:null==(e=j.value)?void 0:e.id}})},O=async()=>{var e,l;if(null==(e=j.value)?void 0:e.id)if("approved"===j.value.status||"sent"===j.value.status)try{await g.confirm("确定要基于此报价单创建订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=null==(l=(await x(j.value.id)).data)?void 0:l.id;if(!e)throw new Error("未能获取到创建的订单ID");w.success("订单创建成功"),Q.push({name:"OrderDetail",params:{id:e}})}catch(a){"cancel"!==a&&(console.error("创建订单失败:",a),w.error("创建订单失败"))}else w.warning("只有已确认或已发送的报价单才能创建订单");else w.warning("无效的报价单ID")},T=async()=>{var e;if(!(null==(e=j.value)?void 0:e.id))return void w.warning("无效的报价单ID");if(0===N.selectedColumns.length)return void w.warning("请至少选择一列进行导出");$.value=!0,w.info({message:`正在导出${"xlsx"===N.format?"Excel":"PDF"}报价单，可能需要1-3分钟，请耐心等待...`,duration:0,showClose:!0,key:"exportDetailMsg"});try{console.log("开始导出报价单，ID:",j.value.id,"配置:",N);const e=await D(j.value.id,{columns:N.selectedColumns,format:N.format,include_header:N.includeHeader});if(w.closeAll(),!(e instanceof Blob))throw console.error("导出响应不是Blob类型:",e),new Error("导出失败：服务器返回的不是有效的文件数据");const l=e.type||("xlsx"===N.format?"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":"application/pdf");console.log("导出报价单成功，准备下载，MIME类型:",l);const a=new Blob([e],{type:l}),t=window.URL.createObjectURL(a),o=document.createElement("a");o.href=t;const r=N.format;o.download=`报价单_${j.value.quotation_number||j.value.id}.${r}`,document.body.appendChild(o),o.click(),document.body.removeChild(o),setTimeout((()=>{window.URL.revokeObjectURL(t)}),100),A.value=!1,w.success("报价单导出成功")}catch(l){w.closeAll(),console.error("导出报价单失败:",l);const e=l instanceof Error?l.message.includes("timeout")?"导出超时，请减少导出数据量或联系管理员增加超时时间":l.message:"导出报价单失败";w.error(`导出报价单失败: ${e}`)}finally{$.value=!1}};return r((()=>{(async()=>{const e=k.params.id;if(!e)return w.error("无效的报价单ID"),void Q.push({name:"QuotationList"});try{console.log(`[QuotationDetail.vue] 正在获取报价单ID: ${e} 的详情`);const l=await y(parseInt(e,10));let a;if(console.log("[QuotationDetail.vue] 获取到报价单详情响应:",l),!l||"object"!=typeof l)throw new Error("无效的API响应");{const e=l;if(200!==e.code&&0!==e.code||!e.data)if(e.id)a=l,console.log("[QuotationDetail.vue] 响应直接是数据对象");else{if(e.code&&200!==e.code&&0!==e.code)throw new Error(e.message||"获取报价单失败");console.warn("[QuotationDetail.vue] 尝试从未识别的API响应格式中提取数据",l),e.data&&e.data.id?(a=e.data,console.log("[QuotationDetail.vue] 从非标准响应中成功提取数据")):(a=l,w.warning("API响应格式不符合预期，数据可能不完整"))}else a=e.data,console.log(`[QuotationDetail.vue] 使用标准API响应中的data字段，code=${e.code}`)}if(!a||!a.id)throw new Error("获取报价单详情失败：返回数据无效");const t=[],o=new Map,r=a.items||[];console.log(`[QuotationDetail.vue] 处理 ${r.length} 个报价项目`),r.forEach((e=>{void 0!==e.product_id&&null!==e.product_id&&(o.has(e.product_id)?o.set(e.product_id,o.get(e.product_id)+1):o.set(e.product_id,1))}));const u=new Set;r.forEach((e=>{const l=void 0!==e.product_id&&null!==e.product_id&&!u.has(e.product_id),a=Number(e.quantity)||0,r=Number(e.unit_price)||0,n=Number(e.discount)||0,d=Number(e.tax_rate)||0;let s=a*r;n>0&&n<=100&&(s*=n/100),d>0&&(s*=1+d/100);const i={...e,quantity:a,unit_price:r,discount:n,tax_rate:d,total_price:s,isFirstSpec:l,rowSpan:void 0!==e.product_id&&null!==e.product_id&&l?o.get(e.product_id):0};t.push(i),l&&void 0!==e.product_id&&null!==e.product_id&&u.add(e.product_id)}));const n=t.reduce(((e,l)=>e+(Number(l.total_price)||0)),0);j.value={...a,items:t,total_amount:n},console.log(`[QuotationDetail.vue] 报价单数据已更新，共 ${t.length} 个项目，总金额: ${n}`),w.success("加载报价单详情成功")}catch(l){console.error("[QuotationDetail.vue] 获取报价单详情失败:",l),w.error(`获取报价单详情失败: ${l instanceof Error?l.message:"未知错误"}`),j.value=null}})()})),(e,l)=>{const a=u("el-button"),t=u("el-card"),o=u("el-descriptions-item"),r=u("el-tag"),w=u("el-descriptions"),g=u("el-tab-pane"),y=u("el-table-column"),x=u("el-table"),D=u("el-timeline-item"),k=u("el-timeline"),Q=u("el-tabs"),X=u("el-radio"),J=u("el-radio-group"),W=u("el-form-item"),z=u("el-switch"),G=u("el-checkbox"),K=u("el-checkbox-group"),Y=u("el-form"),Z=u("el-dialog");return j.value?(n(),d("div",V,[s(t,{class:"header-card mb-20"},{default:i((()=>[c("div",E,[l[11]||(l[11]=c("h2",{class:"form-title"},"报价单详情",-1)),c("div",null,[s(a,{onClick:R},{default:i((()=>l[7]||(l[7]=[p("返回")]))),_:1}),s(a,{type:"primary",onClick:M},{default:i((()=>l[8]||(l[8]=[p("编辑")]))),_:1}),s(a,{type:"success",onClick:O},{default:i((()=>l[9]||(l[9]=[p("生成订单")]))),_:1}),s(a,{type:"info",onClick:P},{default:i((()=>l[10]||(l[10]=[p("导出报价单")]))),_:1})])])])),_:1}),s(Q,{modelValue:U.value,"onUpdate:modelValue":l[0]||(l[0]=e=>U.value=e)},{default:i((()=>[s(g,{label:"基本信息",name:"basic"},{default:i((()=>[s(t,null,{default:i((()=>[s(w,{column:2,border:""},{default:i((()=>[s(o,{label:"报价单编号"},{default:i((()=>[p(m(j.value.quotation_number),1)])),_:1}),s(o,{label:"客户名称"},{default:i((()=>{var e;return[p(m(null==(e=j.value.customer)?void 0:e.name),1)]})),_:1}),s(o,{label:"项目名称"},{default:i((()=>[p(m(j.value.project_name),1)])),_:1}),s(o,{label:"项目地址"},{default:i((()=>[p(m(j.value.project_address),1)])),_:1}),s(o,{label:"有效期至"},{default:i((()=>[p(m(j.value.valid_until),1)])),_:1}),s(o,{label:"状态"},{default:i((()=>[s(r,{type:H(j.value.status||"")},{default:i((()=>[p(m(j.value.status),1)])),_:1},8,["type"])])),_:1}),s(o,{label:"付款条件"},{default:i((()=>[p(m(j.value.payment_terms),1)])),_:1}),s(o,{label:"交货条件"},{default:i((()=>[p(m(j.value.delivery_terms),1)])),_:1}),s(o,{label:"总金额"},{default:i((()=>[p(m(j.value.total_amount)+" 元",1)])),_:1}),s(o,{label:"创建时间"},{default:i((()=>[p(m(j.value.created_at),1)])),_:1}),s(o,{label:"备注",span:2},{default:i((()=>[p(m(j.value.notes),1)])),_:1}),j.value.approval_comment?(n(),_(o,{key:0,label:"审批意见",span:2},{default:i((()=>[p(m(j.value.approval_comment),1)])),_:1})):f("",!0)])),_:1})])),_:1})])),_:1}),s(g,{label:"产品明细",name:"items"},{default:i((()=>[s(t,null,{default:i((()=>[s(x,{data:j.value.items,"span-method":B,style:{width:"100%"},border:"",stripe:"","show-summary":"","summary-method":F},{default:i((()=>[s(y,{prop:"product_name_snapshot",label:"产品名称","min-width":"160"}),s(y,{prop:"product_model_snapshot",label:"型号",width:"100"}),s(y,{prop:"product_spec_snapshot",label:"规格","min-width":"80"}),s(y,{prop:"product_unit_snapshot",label:"单位",width:"60"}),s(y,{prop:"quantity",label:"数量",width:"60"}),s(y,{prop:"unit_price",label:"单价",width:"110"},{default:i((({row:e})=>[p(m(v(C)(e.unit_price)),1)])),_:1}),s(y,{prop:"discount",label:"折扣(%)",width:"80"},{default:i((({row:e})=>[p(m(e.discount),1)])),_:1}),s(y,{prop:"tax_rate",label:"税率(%)",width:"80"},{default:i((({row:e})=>[p(m(e.tax_rate),1)])),_:1}),s(y,{prop:"total_price",label:"金额",width:"100"},{default:i((({row:e})=>[p(m(v(C)(e.total_price)),1)])),_:1}),s(y,{prop:"notes",label:"备注","min-width":"150","show-overflow-tooltip":""})])),_:1},8,["data"])])),_:1})])),_:1}),s(g,{label:"审批记录",name:"approval"},{default:i((()=>[s(t,null,{default:i((()=>[s(k,null,{default:i((()=>[(n(!0),d(b,null,h(j.value.approval_history,((e,l)=>{return n(),_(D,{key:l,timestamp:e.created_at,type:(a=e.status,H(a))},{default:i((()=>[c("h4",null,"状态变更为: "+m(e.status),1),c("p",null,m(e.comment),1),c("p",null,"操作人: "+m(e.operator),1)])),_:2},1032,["timestamp","type"]);var a})),128))])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"]),s(Z,{modelValue:A.value,"onUpdate:modelValue":l[6]||(l[6]=e=>A.value=e),title:"导出报价单配置",width:"500px"},{footer:i((()=>[c("span",I,[s(a,{onClick:l[5]||(l[5]=e=>A.value=!1)},{default:i((()=>l[15]||(l[15]=[p("取消")]))),_:1}),s(a,{type:"primary",onClick:T,loading:$.value},{default:i((()=>l[16]||(l[16]=[p(" 确认导出 ")]))),_:1},8,["loading"])])])),default:i((()=>[s(Y,{model:N,"label-width":"120px"},{default:i((()=>[s(W,{label:"导出格式"},{default:i((()=>[s(J,{modelValue:N.format,"onUpdate:modelValue":l[1]||(l[1]=e=>N.format=e)},{default:i((()=>[s(X,{label:"xlsx"},{default:i((()=>l[12]||(l[12]=[p("Excel (XLSX)")]))),_:1}),s(X,{label:"pdf"},{default:i((()=>l[13]||(l[13]=[p("PDF")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),s(W,{label:"包含表头"},{default:i((()=>[s(z,{modelValue:N.includeHeader,"onUpdate:modelValue":l[2]||(l[2]=e=>N.includeHeader=e)},null,8,["modelValue"])])),_:1}),s(W,{label:"导出列"},{default:i((()=>[s(G,{modelValue:N.selectAll,"onUpdate:modelValue":l[3]||(l[3]=e=>N.selectAll=e),onChange:L},{default:i((()=>l[14]||(l[14]=[p("全选")]))),_:1},8,["modelValue"]),s(K,{modelValue:N.selectedColumns,"onUpdate:modelValue":l[4]||(l[4]=e=>N.selectedColumns=e),class:"column-checkboxes"},{default:i((()=>[(n(),d(b,null,h(q,(e=>s(G,{key:e.prop,label:e.prop,onChange:S},{default:i((()=>[p(m(e.label),1)])),_:2},1032,["label"]))),64))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])):f("",!0)}}}),[["__scopeId","data-v-413149e1"]]);export{Q as default};
