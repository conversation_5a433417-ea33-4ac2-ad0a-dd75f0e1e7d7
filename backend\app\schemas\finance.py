"""
财务管理相关的序列化模式
基于原项目的Schema定义，确保与现有数据库结构100%兼容
"""
from marshmallow import Schema, fields, validate, validates, ValidationError
from typing import Dict, List, Optional, Any
from decimal import Decimal
from datetime import datetime, date


class PaymentRecordSchema(Schema):
    """收款记录序列化模式"""
    id = fields.Integer(dump_only=True)
    payment_number = fields.String(dump_only=True)
    order_id = fields.Integer(required=True)
    payment_date = fields.Date(required=True)
    amount = fields.Decimal(required=True, validate=validate.Range(min=Decimal('0.01')), as_string=True)
    payment_method = fields.String(
        required=True,
        validate=validate.OneOf(['bank_transfer', 'cash', 'wechat', 'alipay', 'other'])
    )
    reference_number = fields.String(allow_none=True)
    bank_account = fields.String(allow_none=True)
    status = fields.String(
        validate=validate.OneOf(['待确认', '已确认', '已取消']),
        load_default='待确认'
    )
    notes = fields.String(allow_none=True)
    created_by = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    
    # 关联信息
    order_number = fields.String(dump_only=True)
    customer_name = fields.String(dump_only=True)
    customer_id = fields.Integer(dump_only=True)
    bank_account_info = fields.String(dump_only=True)


class RefundRecordSchema(Schema):
    """退款记录序列化模式"""
    id = fields.Integer(dump_only=True)
    refund_no = fields.String(dump_only=True)
    return_order_id = fields.Integer(required=True)
    refund_date = fields.Date(required=True)
    amount = fields.Decimal(required=True, validate=validate.Range(min=Decimal('0.01')), as_string=True)
    refund_method = fields.String(
        required=True,
        validate=validate.OneOf(['bank_transfer', 'cash', 'original_channel', 'other'])
    )
    reference_number = fields.String(allow_none=True)
    bank_account = fields.String(allow_none=True)
    status = fields.String(
        validate=validate.OneOf(['待处理', '已退款', '已取消']),
        load_default='待处理'
    )
    notes = fields.String(allow_none=True)
    created_by = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    # 关联信息
    return_order_number = fields.String(dump_only=True)
    order_id = fields.Integer(dump_only=True)
    order_number = fields.String(dump_only=True)
    customer_id = fields.Integer(dump_only=True)
    customer_name = fields.String(dump_only=True)


class StatementRefundSchema(Schema):
    """对账单退款记录序列化模式"""
    id = fields.Integer(dump_only=True)
    refund_number = fields.String(dump_only=True)
    statement_id = fields.Integer(required=True)
    refund_date = fields.Date(required=True)
    amount = fields.Decimal(required=True, validate=validate.Range(min=Decimal('0.01')), as_string=True)
    refund_method = fields.String(
        required=True,
        validate=validate.OneOf(['bank_transfer', 'cash', 'balance', 'other'])
    )
    refund_target = fields.String(
        validate=validate.OneOf(['direct', 'balance']),
        load_default='direct'
    )
    reference_number = fields.String(allow_none=True)
    bank_account = fields.String(allow_none=True)
    balance_transaction_id = fields.Integer(dump_only=True)
    notes = fields.String(allow_none=True)
    voucher_files = fields.String(allow_none=True)
    status = fields.String(
        validate=validate.OneOf(['待处理', '已退款', '已取消']),
        load_default='待处理'
    )
    created_by = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    # 关联信息
    statement_number = fields.String(dump_only=True)
    customer_id = fields.Integer(dump_only=True)
    customer_name = fields.String(dump_only=True)
    bank_account_info = fields.String(dump_only=True)


class StatementSchema(Schema):
    """对账单序列化模式"""
    id = fields.Integer(dump_only=True)
    statement_number = fields.String(dump_only=True)
    customer_id = fields.Integer(required=True)
    statement_date = fields.Date(required=True)
    due_date = fields.Date(allow_none=True)
    status = fields.String(
        validate=validate.OneOf(['待确认', '已确认', '部分收款', '已结清', '已取消']),
        load_default='待确认'
    )
    total_amount = fields.Decimal(dump_only=True, as_string=True)
    discount_amount = fields.Decimal(as_string=True, allow_none=True, load_default=0.0)
    adjusted_total_amount = fields.Decimal(dump_only=True, as_string=True)
    paid_amount = fields.Decimal(dump_only=True, as_string=True, load_default=0.0)
    notes = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)

    # 关联信息
    customer = fields.Nested('CustomerSimpleSchema', only=('id', 'name', 'contact', 'phone'), dump_only=True)
    delivery_notes = fields.Method('get_delivery_notes', dump_only=True)
    return_orders = fields.Method('get_return_orders', dump_only=True)

    def get_delivery_notes(self, obj):
        """获取发货单信息"""
        if not obj.delivery_notes:
            return []

        from .order import DeliveryNoteSimpleSchema
        return DeliveryNoteSimpleSchema(many=True).dump(obj.delivery_notes)

    def get_return_orders(self, obj):
        """获取退货单信息"""
        if not obj.return_orders:
            return []

        return ReturnOrderForStatementSchema(many=True).dump(obj.return_orders)
    
    @validates('statement_date')
    def validate_statement_date(self, value):
        if isinstance(value, str):
            try:
                datetime.strptime(value, '%Y-%m-%d')
            except ValueError:
                raise ValidationError('日期格式无效，请使用 YYYY-MM-DD 格式')
        return value
    
    @validates('due_date')
    def validate_due_date(self, value):
        if value is None:
            return value
        if isinstance(value, str):
            try:
                datetime.strptime(value, '%Y-%m-%d')
            except ValueError:
                raise ValidationError('日期格式无效，请使用 YYYY-MM-DD 格式')
        return value


class StatementCreateSchema(Schema):
    """创建对账单序列化模式"""
    statement_number = fields.String(required=True, validate=validate.Length(min=1, max=50))
    customer_id = fields.Integer(required=True)
    statement_date = fields.Date(required=True)
    due_date = fields.Date(allow_none=True)
    status = fields.String(
        validate=validate.OneOf(['待确认', '已确认']),
        load_default='待确认'
    )
    total_amount = fields.Decimal(required=True, as_string=True)
    discount_amount = fields.Decimal(as_string=True, allow_none=True, load_default=0.0)
    adjusted_total_amount = fields.Decimal(required=True, as_string=True)
    notes = fields.String(allow_none=True)
    delivery_note_ids = fields.List(
        fields.Integer(),
        required=False,
        validate=validate.Length(min=0),
        load_default=[]
    )
    return_order_ids = fields.List(
        fields.Integer(),
        required=False,
        validate=validate.Length(min=0),
        load_default=[]
    )


class StatementUpdateSchema(Schema):
    """更新对账单序列化模式"""
    statement_date = fields.Date(required=False)
    due_date = fields.Date(allow_none=True)
    status = fields.String(
        validate=validate.OneOf(['待确认', '已确认', '已完成', '已取消']),
        required=False
    )
    total_amount = fields.Decimal(as_string=True, required=False)
    discount_amount = fields.Decimal(as_string=True, required=False)
    adjusted_total_amount = fields.Decimal(as_string=True, required=False)
    notes = fields.String(allow_none=True)
    delivery_note_ids = fields.List(fields.Integer(), required=False)
    return_order_ids = fields.List(fields.Integer(), required=False)


class ReturnOrderForStatementSchema(Schema):
    """对账单中的退货单信息序列化模式"""
    id = fields.Integer(dump_only=True)
    return_number = fields.String(dump_only=True)
    return_date = fields.Date(dump_only=True)
    status = fields.String(dump_only=True)
    reason = fields.String(dump_only=True)


class ReceivableSchema(Schema):
    """应收款序列化模式"""
    id = fields.Integer(dump_only=True)
    statement_id = fields.Integer(required=True)
    customer_id = fields.Integer(required=True)
    amount = fields.Decimal(required=True, as_string=True)
    paid_amount = fields.Decimal(dump_only=True, as_string=True, load_default=Decimal('0.00'))
    due_date = fields.Date(required=True)
    status = fields.String(
        validate=validate.OneOf(['未支付', '部分支付', '已支付', '逾期']),
        load_default='未支付'
    )
    last_payment_date = fields.Date(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    
    # 关联信息
    statement_number = fields.String(dump_only=True)
    customer_name = fields.String(dump_only=True)


class ReceivableStatisticsSchema(Schema):
    """应收款统计序列化模式"""
    customer_id = fields.Integer(dump_only=True)
    customer_name = fields.String(dump_only=True)
    total_amount = fields.Decimal(dump_only=True, as_string=True)
    paid_amount = fields.Decimal(dump_only=True, as_string=True)
    outstanding_amount = fields.Decimal(dump_only=True, as_string=True)
    overdue_amount = fields.Decimal(dump_only=True, as_string=True)
    order_count = fields.Integer(dump_only=True)


class FinancialReportSchema(Schema):
    """财务报表序列化模式"""
    id = fields.Integer(dump_only=True)
    report_number = fields.String(dump_only=True)
    report_type = fields.String(
        required=True,
        validate=validate.OneOf([
            'sales_summary', 'product_sales', 'customer_sales', 'sales_trend',
            'income_expense_summary', 'income_expense_detail', 'income_expense_trend', 'profit_analysis',
            'receivable_analysis', 'payable_analysis', 'aging_analysis', 'collection_forecast'
        ])
    )
    period_type = fields.String(
        required=True,
        validate=validate.OneOf(['monthly', 'quarterly', 'yearly', 'custom'])
    )
    start_date = fields.Date(required=True)
    end_date = fields.Date(required=True)
    report_data = fields.Dict(load_default=dict)
    created_by = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class CompanyBankAccountSchema(Schema):
    """公司银行账户序列化模式"""
    id = fields.Integer(dump_only=True)
    bank_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    account_name = fields.String(required=True, validate=validate.Length(min=1, max=100))
    account_number = fields.String(required=True, validate=validate.Length(min=1, max=50))
    branch = fields.String(validate=validate.Length(max=100), allow_none=True)
    is_default = fields.Boolean(load_default=False)
    notes = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class FinanceStatisticsSchema(Schema):
    """财务统计序列化模式"""
    total_payments = fields.Decimal(dump_only=True, as_string=True)
    payment_growth = fields.Float(dump_only=True)
    total_refunds = fields.Decimal(dump_only=True, as_string=True)
    refund_growth = fields.Float(dump_only=True)
    outstanding_amount = fields.Decimal(dump_only=True, as_string=True)
    period = fields.String(dump_only=True)
    start_date = fields.Date(dump_only=True)
    end_date = fields.Date(dump_only=True)


class PaymentStatisticsSchema(Schema):
    """收款统计序列化模式"""
    total_payment = fields.Decimal(dump_only=True, as_string=True)
    total_payment_growth = fields.Float(dump_only=True)
    payment_count = fields.Integer(dump_only=True)
    payment_count_growth = fields.Float(dump_only=True)
    average_payment = fields.Decimal(dump_only=True, as_string=True)
    average_payment_growth = fields.Float(dump_only=True)
    pending_count = fields.Integer(dump_only=True)


class AgingAnalysisSchema(Schema):
    """账龄分析序列化模式"""
    aging_range = fields.String(dump_only=True)
    amount = fields.Decimal(dump_only=True, as_string=True)
    count = fields.Integer(dump_only=True)
    percentage = fields.Float(dump_only=True)
