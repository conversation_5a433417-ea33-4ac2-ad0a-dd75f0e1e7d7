"""
产品管理API单元测试
"""
import pytest
import json
from app.models.product import Product, ProductCategory, ProductSpecification


class TestProductAPI:
    """产品API测试类"""
    
    def test_get_products_empty(self, client, db_session):
        """测试获取空产品列表"""
        response = client.get('/api/v1/products')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['message'] == '获取产品列表成功'
        assert data['data'] == []
    
    def test_get_products_with_data(self, client, db_session, sample_product):
        """测试获取有数据的产品列表"""
        response = client.get('/api/v1/products')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert len(data['data']) == 1
        assert data['data'][0]['name'] == '测试产品'
        assert data['data'][0]['model'] == 'TEST-001'
    
    def test_create_product_success(self, client, db_session, sample_category, test_data_generator):
        """测试成功创建产品"""
        product_data = test_data_generator.product_data(
            category_id=sample_category.id
        )
        
        response = client.post('/api/v1/products', 
                             json=product_data,
                             content_type='application/json')
        assert response.status_code == 201
        
        data = response.get_json()
        assert data['code'] == 201
        assert data['message'] == '产品创建成功'
        assert data['data']['name'] == product_data['name']
        assert data['data']['model'] == product_data['model']
        
        # 验证规格也被创建了
        assert len(data['data']['specifications']) == 1
        assert data['data']['specifications'][0]['specification'] == '标准规格'
    
    def test_create_product_validation_error(self, client, db_session):
        """测试创建产品时的验证错误"""
        invalid_data = {
            'model': 'TEST-001',
            'unit': '个'
            # 缺少 name 字段
        }
        
        response = client.post('/api/v1/products', 
                             json=invalid_data,
                             content_type='application/json')
        assert response.status_code == 400
        
        data = response.get_json()
        assert data['code'] == 400
        assert 'errors' in data
    
    def test_get_product_detail_success(self, client, db_session, sample_product):
        """测试成功获取产品详情"""
        response = client.get(f'/api/v1/products/{sample_product.id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['id'] == sample_product.id
        assert data['data']['name'] == sample_product.name
        assert data['data']['model'] == sample_product.model
    
    def test_update_product_success(self, client, db_session, sample_product):
        """测试成功更新产品"""
        update_data = {
            'name': '更新后的产品名称',
            'model': 'UPDATED-001',
            'description': '更新后的描述'
        }
        
        response = client.put(f'/api/v1/products/{sample_product.id}',
                            json=update_data,
                            content_type='application/json')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['name'] == update_data['name']
        assert data['data']['model'] == update_data['model']
    
    def test_delete_product_success(self, client, db_session, sample_product):
        """测试成功删除产品"""
        product_id = sample_product.id
        
        response = client.delete(f'/api/v1/products/{product_id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert '产品删除成功' in data['message']
        
        # 验证数据库中的产品确实被删除了
        deleted_product = Product.query.get(product_id)
        assert deleted_product is None


class TestProductCategoryAPI:
    """产品分类API测试类"""
    
    def test_get_categories_empty(self, client, db_session):
        """测试获取空分类列表"""
        response = client.get('/api/v1/products/categories')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data'] == []
    
    def test_create_category_success(self, client, db_session):
        """测试成功创建分类"""
        category_data = {
            'name': '新分类',
            'description': '新分类描述',
            'sort_order': 1
        }
        
        response = client.post('/api/v1/products/categories', 
                             json=category_data,
                             content_type='application/json')
        assert response.status_code == 201
        
        data = response.get_json()
        assert data['code'] == 201
        assert data['data']['name'] == category_data['name']
    
    def test_get_category_detail_success(self, client, db_session, sample_category):
        """测试成功获取分类详情"""
        response = client.get(f'/api/v1/products/categories/{sample_category.id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['id'] == sample_category.id
        assert data['data']['name'] == sample_category.name
    
    def test_update_category_success(self, client, db_session, sample_category):
        """测试成功更新分类"""
        update_data = {
            'name': '更新后的分类名称',
            'description': '更新后的描述'
        }
        
        response = client.put(f'/api/v1/products/categories/{sample_category.id}',
                            json=update_data,
                            content_type='application/json')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        assert data['data']['name'] == update_data['name']
    
    def test_delete_category_success(self, client, db_session, sample_category):
        """测试成功删除分类"""
        category_id = sample_category.id
        
        response = client.delete(f'/api/v1/products/categories/{category_id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['code'] == 200
        
        # 验证数据库中的分类确实被删除了
        deleted_category = ProductCategory.query.get(category_id)
        assert deleted_category is None


# Brand相关测试已移除，因为原项目中没有Brand模型
    
    def test_product_search_by_name(self, client, db_session, sample_product):
        """测试按名称搜索产品"""
        response = client.get('/api/v1/products?name=测试')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']) == 1
        assert data['data'][0]['name'] == '测试产品'
    
    def test_product_search_by_model(self, client, db_session, sample_product):
        """测试按型号搜索产品"""
        response = client.get('/api/v1/products?model=TEST')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']) == 1
        assert data['data'][0]['model'] == 'TEST-001'
    
    def test_product_filter_by_category(self, client, db_session, sample_product):
        """测试按分类筛选产品"""
        response = client.get(f'/api/v1/products?category_id={sample_product.category_id}')
        assert response.status_code == 200
        
        data = response.get_json()
        assert len(data['data']) == 1
        assert data['data'][0]['category_id'] == sample_product.category_id
    
# 品牌筛选测试已移除，因为原项目中没有Brand模型
