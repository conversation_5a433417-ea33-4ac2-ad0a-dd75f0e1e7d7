"""
全局错误处理器
处理各种HTTP错误和自定义异常
"""
from flask import Flask, current_app, request
from werkzeug.exceptions import HTTPException
from sqlalchemy.exc import SQLAlchemyError
from marshmallow import ValidationError as MarshmallowValidationError
import traceback
import logging

from app.utils.response import error_response, server_error_response
from app.utils.exceptions import APIException


def register_error_handlers(app: Flask) -> None:
    """
    注册全局错误处理器
    
    Args:
        app: Flask应用实例
    """
    
    @app.errorhandler(APIException)
    def handle_api_exception(e: APIException):
        """处理自定义API异常"""
        current_app.logger.warning(
            f'API Exception: {e.message} - '
            f'Code: {e.code} - '
            f'URL: {request.url} - '
            f'Method: {request.method}'
        )
        
        return error_response(
            message=e.message,
            errors=e.errors,
            code=e.code,
            data=e.data
        )
    
    @app.errorhandler(MarshmallowValidationError)
    def handle_marshmallow_validation_error(e: MarshmallowValidationError):
        """处理Marshmallow数据验证错误"""
        current_app.logger.warning(
            f'Validation Error: {e.messages} - '
            f'URL: {request.url} - '
            f'Method: {request.method}'
        )
        
        return error_response(
            message='数据验证失败',
            errors=e.messages,
            code=422
        )
    
    @app.errorhandler(SQLAlchemyError)
    def handle_sqlalchemy_error(e: SQLAlchemyError):
        """处理SQLAlchemy数据库错误"""
        current_app.logger.error(
            f'Database Error: {str(e)} - '
            f'URL: {request.url} - '
            f'Method: {request.method}',
            exc_info=True
        )
        
        # 在开发环境显示详细错误，生产环境隐藏
        if current_app.config.get('DEBUG', False):
            return error_response(
                message='数据库操作失败',
                errors=str(e),
                code=500
            )
        else:
            return server_error_response('数据库操作失败')
    
    @app.errorhandler(404)
    def handle_not_found(e):
        """处理404错误"""
        current_app.logger.info(
            f'404 Not Found: {request.url} - '
            f'Method: {request.method}'
        )
        
        return error_response(
            message='请求的资源不存在',
            code=404
        )
    
    @app.errorhandler(405)
    def handle_method_not_allowed(e):
        """处理405错误"""
        current_app.logger.warning(
            f'405 Method Not Allowed: {request.url} - '
            f'Method: {request.method}'
        )
        
        return error_response(
            message='请求方法不被允许',
            code=405
        )
    
    @app.errorhandler(400)
    def handle_bad_request(e):
        """处理400错误"""
        current_app.logger.warning(
            f'400 Bad Request: {request.url} - '
            f'Method: {request.method} - '
            f'Error: {e.description}'
        )
        
        return error_response(
            message='请求参数错误',
            errors=e.description,
            code=400
        )
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(e: HTTPException):
        """处理其他HTTP异常"""
        current_app.logger.warning(
            f'HTTP Exception {e.code}: {e.description} - '
            f'URL: {request.url} - '
            f'Method: {request.method}'
        )
        
        return error_response(
            message=e.description or f'HTTP错误 {e.code}',
            code=e.code
        )
    
    @app.errorhandler(Exception)
    def handle_general_exception(e: Exception):
        """处理未捕获的异常"""
        # 记录详细的错误信息
        error_id = id(e)  # 生成错误ID用于追踪
        
        current_app.logger.error(
            f'Unhandled Exception (ID: {error_id}): {str(e)} - '
            f'URL: {request.url} - '
            f'Method: {request.method} - '
            f'User-Agent: {request.headers.get("User-Agent", "Unknown")} - '
            f'IP: {request.remote_addr}',
            exc_info=True
        )
        
        # 尝试记录到错误日志表
        try:
            from app.models.error_log import ErrorLog
            from app import db
            
            error_log = ErrorLog(
                log_type='unhandled_exception',
                message=str(e),
                stack_trace=traceback.format_exc(),
                origin=f'{request.method} {request.url}',
                user_agent=request.headers.get('User-Agent'),
                ip_address=request.remote_addr,
                additional_info={
                    'error_id': error_id,
                    'request_data': request.get_json(silent=True),
                    'request_args': dict(request.args)
                }
            )
            db.session.add(error_log)
            db.session.commit()
        except Exception as log_error:
            current_app.logger.error(f'Failed to log error to database: {log_error}')
        
        # 在开发环境显示详细错误，生产环境隐藏
        if current_app.config.get('DEBUG', False):
            return error_response(
                message=f'服务器内部错误 (ID: {error_id})',
                errors=str(e),
                code=500
            )
        else:
            return server_error_response(f'服务器内部错误 (ID: {error_id})')


def setup_logging(app: Flask) -> None:
    """
    设置日志配置
    
    Args:
        app: Flask应用实例
    """
    if not app.debug and not app.testing:
        # 生产环境日志配置
        if not app.logger.handlers:
            file_handler = logging.FileHandler('logs/emb_system.log')
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info('EMB System startup')
