import{I as s,J as e,r as a,d as t,f as r,o as c,c as o,a as l,n as u,t as n,i,h as d,k as v,F as p,m as g,aq as m,U as P,j as I}from"./index-3d4c440c.js";import{_ as f}from"./_plugin-vue_export-helper-1b428a4d.js";const h=s.create({baseURL:"http://127.0.0.1:5001",timeout:3e3,headers:{"Content-Type":"application/json"}});h.interceptors.request.use((s=>{var e;return console.log(`[测试API] 发送请求: ${null==(e=s.method)?void 0:e.toUpperCase()} ${s.url}`),s}),(s=>(console.error("[测试API] 请求错误:",s),Promise.reject(s)))),h.interceptors.response.use((s=>(console.log(`[测试API] 收到响应: ${s.status}`,s.data),s)),(s=>(s.response?console.error(`[测试API] 响应错误: ${s.response.status}`,s.response.data):s.request?console.error("[测试API] 无响应:",s.message):console.error("[测试API] 请求设置错误:",s.message),Promise.reject(s))));const y={class:"test-page"},A={class:"test-section"},k={class:"button-group"},_={key:0,class:"results"},w={class:"result-header"},S={class:"result-name"},b={class:"result-status"},C={class:"result-time"},j={class:"result-content"},O={key:0,class:"status-code"},x={key:1},E={key:2,class:"error-message"},J={key:1,class:"no-results"},R=f(e({__name:"TestPage",setup(e){const f=a([]),R=a(!1),$=a({server:!1,api:!1,error:!1}),q=t((()=>$.value.server?"正在检测服务器...":R.value?"服务器连接正常":"服务器未响应或未启动")),N=t((()=>$.value.server?"status-checking":R.value?"status-online":"status-offline")),T=()=>(new Date).toLocaleTimeString(),U=async()=>{$.value.server=!0,R.value=!1;try{const s=await async function(){try{console.log("[测试API] 检查服务器是否运行...");const s=await h.get("/");return console.log("[测试API] 服务器正在运行:",s.data),{success:!0,data:s.data,status:s.status}}catch(s){return console.error("[测试API] 服务器检查失败:",s),{success:!1,error:s instanceof Error?s.message:String(s)}}}();R.value=s.success,f.value.unshift({name:"服务器连接测试",success:s.success,time:T(),status:s.status,data:s.success?JSON.stringify(s.data,null,2):void 0,error:s.success?void 0:s.error}),s.success?P.success("服务器连接测试成功"):P.error("服务器连接测试失败")}catch(s){R.value=!1,f.value.unshift({name:"服务器连接测试",success:!1,time:T(),error:s.message||"未知错误"}),P.error("服务器连接测试失败")}finally{$.value.server=!1}},L=async()=>{$.value.api=!0;try{const s=await async function(){try{console.log("[测试API] 测试API接口...");const s=await h.get("/api/hello");return console.log("[测试API] API测试成功:",s.data),{success:!0,data:s.data,status:s.status}}catch(s){return console.error("[测试API] API测试失败:",s),{success:!1,error:s instanceof Error?s.message:String(s)}}}();f.value.unshift({name:"API接口测试",success:s.success,time:T(),status:s.status,data:s.success?JSON.stringify(s.data,null,2):void 0,error:s.success?void 0:s.error}),s.success?P.success("API接口测试成功"):P.error("API接口测试失败")}catch(s){f.value.unshift({name:"API接口测试",success:!1,time:T(),error:s.message||"未知错误"}),P.error("API接口测试失败")}finally{$.value.api=!1}},D=async()=>{$.value.error=!0;try{const e=await async function(){try{console.log("[测试API] 测试错误响应...");const s=await h.get("/api/error");return console.log("[测试API] 错误测试结果:",s.data),{success:!0,data:s.data,status:s.status}}catch(e){return console.log("[测试API] 收到预期的错误响应"),s.isAxiosError(e)&&e.response?{success:!0,data:e.response.data,status:e.response.status}:(console.error("[测试API] 意外错误:",e),{success:!1,error:e instanceof Error?e.message:String(e)})}}();f.value.unshift({name:"错误响应测试",success:e.success,time:T(),status:e.status,data:e.success?JSON.stringify(e.data,null,2):void 0,error:e.success?void 0:e.error}),e.success?P.success("错误响应测试成功"):P.error("错误响应测试失败")}catch(e){f.value.unshift({name:"错误响应测试",success:!1,time:T(),error:e.message||"未知错误"}),P.error("错误响应测试失败")}finally{$.value.error=!1}},F=()=>{f.value=[],P.info("已清除测试结果")};return U(),(s,e)=>{const a=r("el-button");return c(),o("div",y,[e[8]||(e[8]=l("div",{class:"header"},[l("h1",null,"API 测试页面"),l("p",null,"用于测试后端API连接和CORS配置")],-1)),l("div",A,[l("div",{class:u(["status-bar",N.value])},[e[0]||(e[0]=l("div",{class:"status-indicator"},null,-1)),l("span",null,n(q.value),1)],2),e[6]||(e[6]=l("h2",null,"API测试",-1)),l("div",k,[i(a,{type:"primary",onClick:U,loading:$.value.server},{default:d((()=>e[1]||(e[1]=[v(" 测试服务器连接 ")]))),_:1},8,["loading"]),i(a,{type:"success",onClick:L,loading:$.value.api,disabled:!R.value},{default:d((()=>e[2]||(e[2]=[v(" 测试API接口 ")]))),_:1},8,["loading","disabled"]),i(a,{type:"warning",onClick:D,loading:$.value.error,disabled:!R.value},{default:d((()=>e[3]||(e[3]=[v(" 测试错误响应 ")]))),_:1},8,["loading","disabled"]),i(a,{type:"danger",onClick:F,plain:""},{default:d((()=>e[4]||(e[4]=[v(" 清除结果 ")]))),_:1})]),f.value.length>0?(c(),o("div",_,[e[5]||(e[5]=l("h3",null,"测试结果",-1)),(c(!0),o(p,null,g(f.value,((s,e)=>(c(),o("div",{key:e,class:u(["result-item",{success:s.success,error:!s.success}])},[l("div",w,[l("span",S,n(s.name),1),l("span",b,n(s.success?"成功":"失败"),1),l("span",C,n(s.time),1)]),l("div",j,[s.status?(c(),o("div",O," 状态码: "+n(s.status),1)):I("",!0),s.data?(c(),o("pre",x,n(s.data),1)):I("",!0),s.error?(c(),o("div",E,n(s.error),1)):I("",!0)])],2)))),128))])):(c(),o("div",J," 点击上方按钮开始测试 ")),e[7]||(e[7]=m('<div class="network-info" data-v-7e7670c5><h3 data-v-7e7670c5>网络调试信息</h3><p data-v-7e7670c5>后端服务器: <code data-v-7e7670c5>http://127.0.0.1:5001</code></p><p data-v-7e7670c5>前端服务器: <code data-v-7e7670c5>http://localhost:3001</code></p><p data-v-7e7670c5>请确保:</p><ul data-v-7e7670c5><li data-v-7e7670c5>后端服务器已启动且在监听端口5001</li><li data-v-7e7670c5>前端能访问后端IP和端口</li><li data-v-7e7670c5>没有防火墙或安全软件阻止连接</li><li data-v-7e7670c5>CORS配置正确允许跨域请求</li></ul><p data-v-7e7670c5>请查看浏览器控制台获取更多调试信息。</p></div>',1))])])}}}),[["__scopeId","data-v-7e7670c5"]]);export{R as default};
