"""
订单相关模型
包括订单、订单产品、订单状态历史、发货单、发货单项目
"""
from datetime import datetime
from typing import List, Optional
from decimal import Decimal
from flask import current_app
from sqlalchemy.orm import relationship
from app import db
from app.models.base import BaseModel


class Order(BaseModel):
    """
    订单表模型

    状态系统说明：
    - order_status: 物流状态，管理订单的物流进度
    - payment_status: 财务状态，根据收款金额自动计算
    - status: 已废弃字段，仅用于数据迁移兼容性

    状态流转规则：
    - 前期状态（待确认、已确认、生产中、待发货）可互相转换且可取消
    - 发货状态（部分发货、全部发货）只能往前流转，不可回退，不可取消
    """
    __tablename__ = 'orders'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    order_number = db.Column(db.String(50), nullable=False, unique=True, comment='订单编号')
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False, comment='客户ID')
    quotation_ids = db.Column(db.Text, nullable=True, comment='关联的报价单ID数组（JSON格式）')
    project_name = db.Column(db.String(100), nullable=False, comment='项目名称')
    project_address = db.Column(db.String(200), nullable=True, comment='项目地址')
    expected_date = db.Column(db.DateTime, nullable=True, comment='预计采购时间')
    payment_terms = db.Column(db.String(100), nullable=True, comment='付款条件')
    delivery_terms = db.Column(db.String(100), nullable=True, comment='交货条件')
    # 废弃字段已删除：status字段已从数据库中移除，使用order_status和payment_status双状态系统

    # 主要状态字段：双状态系统
    order_status = db.Column(db.String(20), nullable=False, default='待确认', comment='物流状态')
    payment_status = db.Column(db.String(20), nullable=False, default='未收款', comment='财务状态')
    total_amount = db.Column(db.Numeric(12, 2), nullable=False, default=0, comment='总金额')
    paid_amount = db.Column(db.Numeric(12, 2), nullable=True, default=0, comment='已付金额')
    notes = db.Column(db.Text, nullable=True, comment='备注')
    delivery_address_id = db.Column(db.Integer, db.ForeignKey('customer_delivery_addresses.id'), nullable=True, comment='送货地址ID')

    # 关联关系
    customer = db.relationship('Customer', back_populates='orders')
    delivery_address = db.relationship('CustomerDeliveryAddress')
    products = db.relationship('OrderProduct', back_populates='order', cascade='all, delete-orphan')
    status_history = db.relationship('OrderStatusHistory', back_populates='order', cascade='all, delete-orphan')

    def __repr__(self) -> str:
        return f"<Order {self.order_number}>"

    def get_quotations(self):
        """获取关联的报价单列表"""
        if not self.quotation_ids:
            return []

        try:
            import json
            quotation_ids = json.loads(self.quotation_ids)
            from .quotation import Quotation
            return Quotation.query.filter(Quotation.id.in_(quotation_ids)).all()
        except:
            return []

    def get_primary_quotation(self):
        """获取主报价单（第一个关联的报价单）"""
        quotations = self.get_quotations()
        return quotations[0] if quotations else None

    def calculate_total_amount(self) -> float:
        """计算订单总金额"""
        total = sum(product.total_price for product in self.products)
        return float(total)

    def update_status(self, status: str, comment: Optional[str] = None) -> None:
        """更新订单状态(兼容方法) - 映射到物流状态"""
        previous_status = self.order_status  # 使用order_status替代已删除的status字段
        # 更新物流状态以保持兼容性
        self.order_status = status
        self.updated_at = datetime.utcnow()

        # 记录状态变更的详细信息，包含之前的状态
        comment_with_transition = f"状态从 {previous_status} 变更为 {status}"
        if comment:
            comment_with_transition += f": {comment}"

        # 创建状态历史记录
        status_history = OrderStatusHistory(
            order_id=self.id,
            status=status,
            comment=comment_with_transition
        )
        db.session.add(status_history)

    def update_order_status(self, order_status: str, comment: Optional[str] = None) -> None:
        """更新发货状态"""
        previous_status = self.order_status
        self.order_status = order_status
        # 兼容字段status已删除，不再需要同步更新
        self.updated_at = datetime.utcnow()

        # 记录状态变更
        comment_with_transition = f"发货状态从 {previous_status} 变更为 {order_status}"
        if comment:
            comment_with_transition += f": {comment}"

        status_history = OrderStatusHistory(
            order_id=self.id,
            status=f"发货:{order_status}",
            comment=comment_with_transition
        )
        db.session.add(status_history)

    def update_order_status_by_delivery(self, order_status: str, comment: Optional[str] = None) -> None:
        """由发货单系统更新发货状态（绕过状态转换验证）"""
        previous_status = self.order_status
        self.order_status = order_status
        self.updated_at = datetime.utcnow()

        # 记录状态变更
        comment_with_transition = f"发货状态从 {previous_status} 变更为 {order_status} (由发货单系统自动更新)"
        if comment:
            comment_with_transition += f": {comment}"

        status_history = OrderStatusHistory(
            order_id=self.id,
            status=f"发货:{order_status}",
            comment=comment_with_transition
        )
        db.session.add(status_history)

    def update_payment_status(self, payment_status: str, comment: Optional[str] = None) -> None:
        """更新财务状态"""
        previous_status = self.payment_status
        self.payment_status = payment_status
        self.updated_at = datetime.utcnow()

        # 记录状态变更
        comment_with_transition = f"财务状态从 {previous_status} 变更为 {payment_status}"
        if comment:
            comment_with_transition += f": {comment}"

        status_history = OrderStatusHistory(
            order_id=self.id,
            status=f"财务:{payment_status}",
            comment=comment_with_transition
        )
        db.session.add(status_history)

    @property
    def status_compat(self) -> str:
        """兼容性属性：返回order_status作为主状态"""
        return self.order_status

    def get_status_display(self) -> str:
        """获取状态的显示文本(兼容方法)"""
        # 返回物流状态作为主要状态显示
        return self.order_status or '未知状态'

    def get_order_status_display(self) -> str:
        """获取物流状态显示文本"""
        return self.order_status or '未知状态'

    def get_payment_status_display(self) -> str:
        """获取财务状态显示文本"""
        return self.payment_status or '未知状态'

    def get_combined_status_display(self) -> str:
        """获取组合状态显示"""
        return f"{self.order_status} / {self.payment_status}"
    
    def is_editable(self) -> bool:
        """检查订单当前状态是否允许编辑"""
        # 只有待确认状态才允许编辑
        current_order_status = self.order_status
        result = current_order_status == '待确认'
        current_app.logger.info(f"[is_editable] Order ID: {self.id}, Order Status: '{current_order_status}', Result: {result}")
        return result

    def get_valid_next_statuses(self) -> List[str]:
        """获取当前状态下允许转换的目标状态列表(兼容方法)"""
        return self.get_valid_next_order_statuses()

    def get_valid_next_order_statuses(self) -> List[str]:
        """获取物流状态的有效转换列表"""
        order_status_transitions = {
            # 待确认：可以转换为已确认，可以取消
            '待确认': ['已确认', '已取消'],

            # 已确认：可以转换为待确认、生产中、待发货，可以取消
            # 注意：部分发货和全部发货由发货单系统自动更新，不在此列表中
            '已确认': ['待确认', '生产中', '待发货', '已取消'],

            # 生产中：可以转换为已确认、待发货，可以取消
            # 注意：部分发货和全部发货由发货单系统自动更新，不在此列表中
            '生产中': ['已确认', '待发货', '已取消'],

            # 待发货：可以转换为已确认、生产中，可以取消
            # 注意：部分发货和全部发货由发货单系统自动更新，不在此列表中
            '待发货': ['已确认', '生产中', '已取消'],

            # 发货状态：只能往后 + 不可取消
            '部分发货': ['全部发货'],        # 不可回退，不可取消
            '全部发货': ['已完成'],          # 不可回退，不可取消

            # 终态
            '已完成': [],                   # 终态
            '已取消': []                    # 终态
        }
        return order_status_transitions.get(self.order_status, [])

    def get_valid_next_payment_statuses(self) -> List[str]:
        """获取财务状态的有效转换列表"""
        payment_status_transitions = {
            '未收款': ['部分收款', '已收款'],
            '部分收款': ['已收款'],
            '已收款': [],
        }
        return payment_status_transitions.get(self.payment_status, [])

    def auto_update_payment_status(self) -> None:
        """根据发货单和退货单的结清状态自动更新财务状态"""
        # 获取所有发货单和退货单（转换为列表），排除已取消的单据
        delivery_notes = [dn for dn in (self.delivery_notes or []) if dn.status != '已取消']
        return_orders = [ro for ro in (self.return_orders or []) if ro.status != '已取消']

        # 如果没有有效的发货单和退货单，状态为未收款
        if not delivery_notes and not return_orders:
            new_status = '未收款'
        else:
            # 统计各种结清状态的单据数量（只计算未取消的单据）
            settled_delivery_count = sum(1 for dn in delivery_notes if dn.settlement_status == '已结清')
            settled_return_count = sum(1 for ro in return_orders if ro.settlement_status == '已结清')
            partial_delivery_count = sum(1 for dn in delivery_notes if dn.settlement_status == '部分结清')
            partial_return_count = sum(1 for ro in return_orders if ro.settlement_status == '部分结清')

            total_settled = settled_delivery_count + settled_return_count
            total_partial = partial_delivery_count + partial_return_count
            total_documents = len(delivery_notes) + len(return_orders)

            if total_settled == 0 and total_partial == 0:
                # 没有任何单据已结清或部分结清
                new_status = '未收款'
            elif self.order_status == '全部发货' and total_settled == total_documents:
                # 订单已全部发货且所有单据都已结清
                new_status = '已收款'
            elif total_settled > 0 or total_partial > 0:
                # 有单据已结清或部分结清
                new_status = '部分收款'
            else:
                new_status = '未收款'

        if new_status != self.payment_status:
            self.update_payment_status(new_status, "根据发货单和退货单结清状态自动更新")

    def can_cancel(self) -> bool:
        """检查订单是否可以取消"""
        # 只有前期状态可以取消，部分发货后不可取消
        cancellable_statuses = ['待确认', '已确认', '生产中', '待发货']
        return self.order_status in cancellable_statuses

    def can_edit(self) -> bool:
        """检查订单是否可以编辑（别名方法）"""
        return self.is_editable()

    def get_status_info(self) -> dict:
        """获取完整的状态信息"""
        return {
            'order_status': self.order_status,
            'payment_status': self.payment_status,
            'combined_status': self.get_combined_status_display(),
            'can_cancel': self.can_cancel(),
            'can_edit': self.can_edit(),
            'valid_order_transitions': self.get_valid_next_order_statuses(),
            'valid_payment_transitions': self.get_valid_next_payment_statuses()
        }

    def update_status_compat(self, new_status: str, comment: Optional[str] = None) -> None:
        """兼容性方法：更新状态（实际更新order_status）"""
        # 为了向后兼容，将status更新映射到order_status更新
        self.update_order_status(new_status, comment)


class OrderProduct(BaseModel):
    """订单产品表模型"""
    __tablename__ = 'order_products'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    product_specification_id = db.Column(db.Integer, db.ForeignKey('product_specifications.id'), nullable=False, comment='产品规格ID')
    quantity = db.Column(db.Float, nullable=False, comment='数量')
    unit_price = db.Column(db.Float, nullable=False, comment='下单时的单价（含税）')
    total_price = db.Column(db.Float, nullable=False, comment='总价 (quantity * unit_price)')
    delivered_quantity = db.Column(db.Float, nullable=False, default=0.0, comment='已发货数量')

    # 税率和折扣信息（下单时的快照）
    tax_rate = db.Column(db.Float, nullable=False, default=13.0, comment='税率（百分比）')
    discount = db.Column(db.Float, nullable=False, default=0.0, comment='折扣（百分比）')

    # 产品来源信息
    source_type = db.Column(db.String(20), nullable=False, default='manual', comment='产品来源类型：quotation/manual')
    source_id = db.Column(db.Integer, nullable=True, comment='来源报价单ID')
    source_display = db.Column(db.String(50), nullable=True, comment='来源显示名称')

    # 关联关系
    product_specification = db.relationship('ProductSpecification', backref=db.backref('order_products', lazy='dynamic'))
    order = db.relationship('Order', back_populates='products')

    def __repr__(self):
        return f'<OrderProduct {self.id} for Order {self.order_id}>'

    def calculate_total_price(self) -> float:
        """计算产品总价 (基于单价/数量/折扣/税率)"""
        price = float(self.unit_price)
        quantity = float(self.quantity)
        discount_percentage = float(self.discount) if self.discount is not None else 0.0
        tax_rate_percentage = float(self.tax_rate) if self.tax_rate is not None else 0.0

        # 计算基础金额
        base_amount = price * quantity

        # 应用折扣：折扣是打折力度，20%折扣意味着最终价格 = (1-0.2) × 基础金额
        if 0 <= discount_percentage <= 100:
            base_amount = base_amount * (1 - discount_percentage / 100)

        # 应用税率：税率是在折扣后金额基础上增加的
        if tax_rate_percentage > 0:
            base_amount = base_amount * (1 + tax_rate_percentage / 100)

        return base_amount

    def update_total_price(self) -> None:
        """更新产品总价"""
        self.total_price = self.calculate_total_price()


class OrderStatusHistory(BaseModel):
    """订单状态历史表模型"""
    __tablename__ = 'order_status_history'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    status = db.Column(db.String(20), nullable=False, comment='状态')
    comment = db.Column(db.Text, nullable=True, comment='说明')

    # 关联关系
    order = db.relationship('Order', back_populates='status_history')

    def __repr__(self) -> str:
        return f"<OrderStatusHistory {self.id}>"


class DeliveryNote(BaseModel):
    """发货单表模型"""
    __tablename__ = 'delivery_notes'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='关联订单ID')
    delivery_number = db.Column(db.String(50), nullable=False, unique=True, comment='发货单号')
    delivery_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, comment='发货日期')
    logistics_company = db.Column(db.String(100), nullable=True, comment='物流公司')
    tracking_number = db.Column(db.String(50), nullable=True, comment='运单号')

    # 收货信息快照
    recipient_name = db.Column(db.String(100), nullable=True, comment='收货人姓名(快照)')
    recipient_phone = db.Column(db.String(50), nullable=True, comment='收货人电话(快照)')
    delivery_address_snapshot = db.Column(db.String(255), nullable=True, comment='收货地址详情(快照)')

    status = db.Column(db.String(20), nullable=False, default='待发出', comment='发货单状态 (待发出, 已发出, 部分签收, 已签收, 已取消)')
    total_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0.00, comment='发货单总金额')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 结清状态字段
    settlement_status = db.Column(db.String(20), nullable=False, default='未结清', comment='结清状态 (未结清, 已结清)')
    settlement_date = db.Column(db.Date, nullable=True, comment='结清日期')

    # 关联关系
    order = db.relationship('Order', backref=db.backref('delivery_notes', lazy='dynamic'))
    items = db.relationship('DeliveryNoteItem', back_populates='delivery_note', cascade='all, delete-orphan')
    statement_delivery_notes = db.relationship('StatementDeliveryNote', back_populates='delivery_note', cascade='all, delete-orphan', overlaps="statements")
    statements = db.relationship('Statement', secondary='statement_delivery_notes', back_populates='delivery_notes', overlaps="statement_delivery_notes,delivery_note,statement")

    def __repr__(self) -> str:
        return f"<DeliveryNote {self.delivery_number}>"

    @property
    def total_items_quantity(self) -> int:
        """计算发货单中所有产品的总数量"""
        return sum(item.quantity for item in self.items) if self.items else 0

    def calculate_total_amount(self) -> float:
        """计算发货单总金额"""
        total = 0.0
        if self.items:
            for item in self.items:
                if item.order_product:
                    # 计算含税金额：单价 × (1 - 折扣率) × (1 + 税率) × 数量
                    unit_price = float(item.order_product.unit_price or 0)
                    discount_rate = float(item.order_product.discount or 0) / 100
                    tax_rate = float(item.order_product.tax_rate or 0) / 100
                    quantity = float(item.quantity or 0)

                    item_total = unit_price * (1 - discount_rate) * (1 + tax_rate) * quantity
                    total += item_total
        return total

    def update_total_amount(self):
        """更新发货单总金额"""
        self.total_amount = self.calculate_total_amount()

    def settle(self, settlement_date=None):
        """标记发货单为已结清"""
        if settlement_date is None:
            settlement_date = datetime.now().date()

        self.settlement_status = '已结清'
        self.settlement_date = settlement_date
        self.updated_at = datetime.now()

        # 更新关联订单的收款状态
        if self.order:
            self.order.auto_update_payment_status()

    def unsettle(self):
        """取消发货单结清状态"""
        self.settlement_status = '未结清'
        self.settlement_date = None
        self.updated_at = datetime.now()

        # 更新关联订单的收款状态
        if self.order:
            self.order.auto_update_payment_status()

    def update_status(self, new_status):
        """更新发货单状态，如果是签收状态则更新应收账款"""
        old_status = self.status
        self.status = new_status
        self.updated_at = datetime.now()

        # 注意：新逻辑下，应收账款不再在发货单签收时创建，而是在对账单确认时创建

    # 注意：新逻辑下，发货单不再直接管理应收账款，应收账款由对账单管理


class DeliveryNoteItem(BaseModel):
    """发货单项目表模型"""
    __tablename__ = 'delivery_note_items'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    delivery_note_id = db.Column(db.Integer, db.ForeignKey('delivery_notes.id'), nullable=False, comment='关联发货单ID')
    order_product_id = db.Column(db.Integer, db.ForeignKey('order_products.id'), nullable=True, comment='关联订单产品项ID (可选, 用于追溯)')
    product_specification_id = db.Column(db.Integer, db.ForeignKey('product_specifications.id'), nullable=False, comment='产品规格ID')
    quantity = db.Column(db.Integer, nullable=False, comment='本次发货数量')

    # 产品信息快照 (冗余存储，在发货时填充)
    product_name = db.Column(db.String(100), nullable=False, comment='产品名称(快照)')
    product_model = db.Column(db.String(50), nullable=False, comment='产品型号(快照)')
    product_unit = db.Column(db.String(20), nullable=False, comment='产品单位(快照)')
    specification_description = db.Column(db.String(100), nullable=True, comment='产品规格描述(快照)')
    notes = db.Column(db.Text, nullable=True, comment='明细备注')

    # 关联关系
    delivery_note = db.relationship('DeliveryNote', back_populates='items')
    product_specification = db.relationship('ProductSpecification')
    order_product = db.relationship('OrderProduct')

    def __repr__(self) -> str:
        return f"<DeliveryNoteItem {self.product_name} x {self.quantity}>"
