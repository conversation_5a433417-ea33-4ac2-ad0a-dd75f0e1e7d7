import{J as e,b as a,r as l,L as t,e as o,f as r,M as s,o as u,c as i,i as n,h as d,a as c,l as m,k as p,F as v,m as f,N as g,g as _,a8 as y,t as h,j as w,U as b,Z as x,X as k,Y as C,a2 as j,ag as q,$ as V,a4 as z,W as E}from"./index-3d4c440c.js";import{r as I,u as U}from"./xlsx-3e8bf635.js";import{C as R}from"./CustomerEditDialog-d8ba8163.js";import{i as S,f as B,h as D}from"./quotation-219e97aa.js";import{e as F}from"./quotationRequest-a49a4856.js";import{s as Y}from"./customer-471ca075.js";import{a as $}from"./format-552375ee.js";import{a as M}from"./apiAdapters-232bdd53.js";import{_ as L}from"./_plugin-vue_export-helper-1b428a4d.js";import"./CustomerForm-dce303b8.js";import"./CustomerDeliveryAddresses-9519c02d.js";const A={class:"quotation-request-list"},T={class:"flex-between"},W={class:"pagination-container"},O={style:{display:"flex","flex-direction":"column","align-items":"flex-start"}},N={class:"dialog-footer"},P=L(e({__name:"QuotationRequestList",setup(e){const L=a(),P=l(!1),Q=l(!1),X=l([]),Z=t({customer_id:void 0,project_name:"",status:""}),J=l(1),G=l(20),H=l(0),K=l([]),ee=l(!1),ae=l(!1),le=l(),te=l(null),oe=l(null),re=l(!1),se=l({}),ue=l(),ie=t({customerId:void 0}),ne=l(!1),de=l([]),ce=()=>{var e;te.value=null,null==(e=le.value)||e.clearFiles(),ie.customerId=void 0,oe.value=null,de.value=[],ue.value&&ue.value.resetFields()},me=async e=>{var a,l;if(oe.value=null,ie.customerId=void 0,de.value=[],e.raw){const o=e.name.endsWith(".xlsx")||e.name.endsWith(".xls"),r=(e.size||0)/1024/1024<10;if(!o)return b.error("只能上传 Excel 文件 (.xlsx, .xls)!"),null==(a=le.value)||a.clearFiles(),void(te.value=null);if(!r)return b.error("上传文件大小不能超过 10MB!"),null==(l=le.value)||l.clearFiles(),void(te.value=null);te.value=e;try{const a=new FileReader;a.onload=async e=>{var a;try{const l=null==(a=e.target)?void 0:a.result,t=I(l,{type:"binary",cellDates:!0}),o=t.SheetNames[0],r=t.Sheets[o],s=U.sheet_to_json(r,{header:1,defval:""});if(s&&s.length>0){const e=(e=>{const a=["客户名称","客户","公司名称","询价单位"];for(let l=0;l<Math.min(e.length,10);l++){const t=e[l];for(let e=0;e<t.length;e++){const l=String(t[e]||"").trim();if(a.some((e=>l.includes(e)))&&e+1<t.length&&String(t[e+1]||"").trim())return String(t[e+1]||"").trim()}}return null})(s);e?(oe.value=e,b.info(`从Excel中识别到客户名: ${e}，正在尝试匹配...`),await ve(e,!0)):b.warning("未能在Excel中自动识别客户名称。")}else b.error("无法读取Excel文件内容或文件为空。")}catch(l){console.error("Error parsing Excel for customer name:",l),b.error("解析Excel文件以提取客户名称失败。")}},a.readAsBinaryString(e.raw)}catch(t){console.error("Error reading file for customer name parsing:",t),b.error("读取文件以解析客户名称失败。")}}else te.value=null},pe=e=>{le.value.clearFiles();const a=e[0];le.value.handleStart(a);const l={name:a.name,size:a.size,raw:a,uid:a.uid,status:"ready"};me(l)},ve=async(e,a=!1)=>{if(e){ne.value=!0;try{const l=await Y({name_like:e,per_page:50});l&&l.data?(de.value=l.data,a&&1===l.data.length?(ie.customerId=l.data[0].id,b.success(`已自动选择匹配到的客户: ${l.data[0].name}`)):a&&l.data.length>1?b.info(`找到多个名为 "${e}" 的客户，请手动选择。`):a&&l.data.length):de.value=[]}catch(l){console.error("导入时搜索客户失败:",l),de.value=[]}finally{ne.value=!1}}else de.value=[]},fe=async()=>{var e,a;if(ue.value)try{if(await ue.value.validate(),!te.value||!te.value.raw)return void b.error("请先选择要上传的文件");if(!ie.customerId)return void b.error("请选择客户后再导入");ae.value=!0;const t=new FormData;t.append("file",te.value.raw),t.append("customer_id",String(ie.customerId));try{const e=await S(t);b.success(e.message||"导入成功"),ee.value=!1,ye()}catch(l){console.error("导入API调用失败:",l),b.error((null==(a=null==(e=l.response)?void 0:e.data)?void 0:a.message)||l.message||"导入失败")}finally{ae.value=!1}}catch(t){console.warn("导入表单验证失败:",t)}},ge=e=>({"草稿":"info","已提交":"primary","正式":"warning","已报价":"success","已处理":"success","已取消":"danger"}[e]||"info"),_e=async e=>{if(e){Q.value=!0;try{const a=await Y({name_like:e,per_page:50});a&&a.data?X.value=a.data:X.value=[]}catch(a){console.error("搜索客户失败:",a),X.value=[]}finally{Q.value=!1}}else X.value=[]},ye=async()=>{P.value=!0;try{const e={page:J.value,per_page:G.value,...Z},a=await B(e),l=M(a);console.log("适配后的报价需求数据:",l),l.items&&l.items.length>=0?(K.value=l.items.map((e=>({...e,status:je(e.status||""),ui:{statusType:ge(e.status||""),exporting:!1}}))),l.page_info?H.value=l.page_info.total_items||0:H.value=l.items.length):(K.value=[],H.value=0,console.warn("获取报价需求列表响应格式不符合预期"))}catch(e){console.error("获取报价需求列表失败:",e),b.error("获取报价需求列表失败"),K.value=[],H.value=0}finally{P.value=!1}},he=()=>{J.value=1,ye()},we=()=>{Object.assign(Z,{customer_id:void 0,project_name:"",status:""}),he()},be=e=>{G.value=e,ye()},xe=e=>{J.value=e,ye()},ke=()=>{L.push("/quotation-requests/new")},Ce={"草稿":"草稿","已提交":"已提交","正式":"正式","已报价":"已报价","已处理":"已处理","已取消":"已取消",draft:"草稿",submitted:"已提交",official:"正式",quoted:"已报价",processed:"已处理",cancelled:"已取消"},je=e=>Ce[e.toLowerCase()]||Ce[e]||e,qe=()=>{oe.value&&(se.value={name:oe.value},re.value=!0)},Ve=e=>{e&&e.id&&(de.value.unshift({id:e.id,name:e.name}),ie.customerId=e.id,b.success(`新客户 "${e.name}" 已创建并选中`),oe.value=null,re.value=!1)},ze=e=>{Ee(e)},Ee=e=>{e.id&&L.push({name:"QuotationRequestDetail",params:{id:e.id}})};return o((()=>{ye()})),(e,a)=>{const l=r("el-icon"),t=r("el-button"),o=r("el-card"),I=r("el-option"),U=r("el-select"),S=r("el-form-item"),B=r("el-input"),Y=r("el-form"),M=r("el-table-column"),te=r("el-link"),Ce=r("el-tag"),Ie=r("el-table"),Ue=r("el-pagination"),Re=r("el-text"),Se=r("el-upload"),Be=r("el-dialog"),De=s("loading");return u(),i("div",A,[n(o,{class:"header-card mb-20"},{default:d((()=>[c("div",T,[a[12]||(a[12]=c("h2",{class:"page-title"},"报价需求管理",-1)),c("div",null,[n(t,{type:"primary",onClick:ke},{default:d((()=>[n(l,null,{default:d((()=>[n(m(x))])),_:1}),a[10]||(a[10]=p(" 新增报价需求 "))])),_:1}),n(t,{type:"primary",onClick:a[0]||(a[0]=e=>ee.value=!0)},{default:d((()=>a[11]||(a[11]=[p("导入询价单")]))),_:1})])])])),_:1}),n(o,null,{default:d((()=>[n(Y,{model:Z,inline:"",class:"search-form"},{default:d((()=>[n(S,{label:"客户"},{default:d((()=>[n(U,{modelValue:Z.customer_id,"onUpdate:modelValue":a[1]||(a[1]=e=>Z.customer_id=e),filterable:"",remote:"",placeholder:"请选择客户","remote-method":_e,loading:Q.value,clearable:""},{default:d((()=>[(u(!0),i(v,null,f(X.value,(e=>(u(),_(I,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),n(S,{label:"项目名称"},{default:d((()=>[n(B,{modelValue:Z.project_name,"onUpdate:modelValue":a[2]||(a[2]=e=>Z.project_name=e),placeholder:"请输入项目名称",clearable:""},null,8,["modelValue"])])),_:1}),n(S,{label:"状态"},{default:d((()=>[n(U,{modelValue:Z.status,"onUpdate:modelValue":a[3]||(a[3]=e=>Z.status=e),placeholder:"请选择状态",clearable:""},{default:d((()=>[n(I,{label:"草稿",value:"草稿"}),n(I,{label:"已提交",value:"已提交"}),n(I,{label:"正式",value:"正式"}),n(I,{label:"已报价",value:"已报价"}),n(I,{label:"已处理",value:"已处理"}),n(I,{label:"已取消",value:"已取消"})])),_:1},8,["modelValue"])])),_:1}),n(S,null,{default:d((()=>[n(t,{type:"primary",onClick:he},{default:d((()=>[n(l,null,{default:d((()=>[n(m(k))])),_:1}),a[13]||(a[13]=p(" 搜索 "))])),_:1}),n(t,{onClick:we},{default:d((()=>[n(l,null,{default:d((()=>[n(m(C))])),_:1}),a[14]||(a[14]=p(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"]),g((u(),_(Ie,{data:K.value,border:"",style:{width:"100%"},onRowClick:ze},{default:d((()=>[n(M,{type:"index",label:"序号",width:"30"}),n(M,{prop:"request_number",label:"需求表编号",width:"180"},{default:d((({row:e})=>[n(te,{type:"primary",onClick:y((a=>Ee(e)),["stop"])},{default:d((()=>[p(h(e.request_number),1)])),_:2},1032,["onClick"])])),_:1}),n(M,{prop:"customer.name",label:"客户名称",width:"200"}),n(M,{prop:"project_name",label:"项目名称","min-width":"200"}),n(M,{prop:"created_at",label:"创建日期",width:"180"},{default:d((({row:e})=>[p(h(m($)(e.created_at)),1)])),_:1}),n(M,{prop:"status",label:"状态",width:"100"},{default:d((({row:e})=>[n(Ce,{type:ge(e.status)},{default:d((()=>[p(h(je(e.status)),1)])),_:2},1032,["type"])])),_:1}),n(M,{label:"操作",align:"center",width:"280"},{default:d((e=>["草稿"===e.row.status||"已提交"===e.row.status?(u(),_(t,{key:0,type:"primary",link:"",size:"small",onClick:y((a=>{return l=e.row,void L.push(`/quotation-requests/${l.id}/edit`);var l}),["stop"])},{default:d((()=>[n(l,null,{default:d((()=>[n(m(j))])),_:1}),a[15]||(a[15]=p(" 编辑 "))])),_:2},1032,["onClick"])):w("",!0),"正式"===e.row.status||"已报价"===e.row.status||"已处理"===e.row.status?(u(),_(t,{key:1,type:"primary",link:"",size:"small",onClick:y((a=>(async e=>{var a,l;if(e.id)try{E.prompt("请输入报价单有效期至 (YYYY-MM-DD):","创建报价单",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"date",inputValidator:e=>e?!!/^\d{4}-\d{2}-\d{2}$/.test(e)||"日期格式应为 YYYY-MM-DD":"有效期不能为空"}).then((async({value:a})=>{const l={valid_until:a},t=await D(e.id,l);b.success("报价单创建成功"),ye(),L.push(`/quotations/${t.id}/edit`)})).catch((()=>{b.info("已取消创建报价单")}))}catch(t){console.error("创建报价单失败:",t),b.error((null==(l=null==(a=t.response)?void 0:a.data)?void 0:l.message)||t.message||"创建报价单失败")}})(e.row)),["stop"])},{default:d((()=>[n(l,null,{default:d((()=>[n(m(q))])),_:1}),a[16]||(a[16]=p(" 创建报价单 "))])),_:2},1032,["onClick"])):w("",!0),n(t,{type:"info",link:"",size:"small",onClick:y((a=>(async e=>{if(e.id){e.exporting=!0;try{const l=await F(e.id);let t;if(l instanceof Blob)t=l;else if(l&&l.data instanceof Blob)t=l.data;else try{if(t=new Blob([l],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),0===t.size)throw new Error("Blob is empty after direct conversion")}catch(a){return console.error("导出文件类型未知或转换Blob失败:",l,a),b.error("导出失败: 文件类型不正确或无法处理响应"),void(e.exporting=!1)}if(0===t.size)return b.error("导出失败: 生成的文件为空"),void(e.exporting=!1);const o=window.URL.createObjectURL(t),r=document.createElement("a");r.href=o,r.download=`报价需求表_${e.request_number||"export"}.xlsx`,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o)}catch(l){console.error("导出失败:",l),b.error("导出失败")}finally{e.exporting=!1}}})(e.row)),["stop"]),loading:e.row.exporting},{default:d((()=>[n(l,null,{default:d((()=>[n(m(V))])),_:1}),a[17]||(a[17]=p(" 导出 "))])),_:2},1032,["onClick","loading"]),"草稿"===e.row.status||"已提交"===e.row.status?(u(),_(t,{key:2,type:"danger",link:"",size:"small",onClick:y((a=>{var l;void 0!==(l=e.row.id)&&(console.warn(`删除操作未实现，ID: ${l}`),b.info("删除功能暂未实现"))}),["stop"])},{default:d((()=>[n(l,null,{default:d((()=>[n(m(z))])),_:1}),a[18]||(a[18]=p(" 删除 "))])),_:2},1032,["onClick"])):w("",!0)])),_:1})])),_:1},8,["data"])),[[De,P.value]]),c("div",W,[n(Ue,{"current-page":J.value,"onUpdate:currentPage":a[4]||(a[4]=e=>J.value=e),"page-size":G.value,"onUpdate:pageSize":a[5]||(a[5]=e=>G.value=e),"page-sizes":[10,20,50,100],total:H.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:be,onCurrentChange:xe},null,8,["current-page","page-size","total"])])])),_:1}),n(Be,{modelValue:ee.value,"onUpdate:modelValue":a[8]||(a[8]=e=>ee.value=e),title:"导入报价需求表Excel",width:"450px",onClose:ce},{footer:d((()=>[c("span",N,[n(t,{onClick:a[7]||(a[7]=e=>ee.value=!1)},{default:d((()=>a[21]||(a[21]=[p("取消")]))),_:1}),n(t,{type:"primary",onClick:fe,loading:ae.value},{default:d((()=>a[22]||(a[22]=[p("确认导入")]))),_:1},8,["loading"])])])),default:d((()=>[n(Y,{ref_key:"importFormRef",ref:ue,model:ie,"label-width":"80px"},{default:d((()=>[n(S,{label:"选择客户",prop:"customerId",rules:[{required:!0,message:"请选择客户",trigger:"change"}]},{default:d((()=>[n(U,{modelValue:ie.customerId,"onUpdate:modelValue":a[6]||(a[6]=e=>ie.customerId=e),filterable:"",remote:"",placeholder:"请输入并选择客户","remote-method":ve,loading:ne.value,clearable:"",style:{width:"100%"}},{default:d((()=>[(u(!0),i(v,null,f(de.value,(e=>(u(),_(I,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),!oe.value||ie.customerId||ne.value||0!==de.value.length?w("",!0):(u(),_(S,{key:0,label:" "},{default:d((()=>[c("div",O,[n(Re,{type:"warning",style:{"margin-bottom":"8px"}},{default:d((()=>[p(' Excel中的客户 "'+h(oe.value)+'" 未在系统中找到。 ',1)])),_:1}),n(t,{type:"success",plain:"",onClick:qe},{default:d((()=>[n(l,null,{default:d((()=>[n(m(x))])),_:1}),p(" 创建新客户: "+h(oe.value),1)])),_:1})])])),_:1})),n(S,{label:"Excel文件",prop:"file"},{default:d((()=>[n(Se,{ref_key:"uploadRef",ref:le,"show-file-list":!0,limit:1,"on-exceed":pe,"on-change":me,"auto-upload":!1,accept:".xlsx,.xls"},{trigger:d((()=>[n(t,{type:"primary"},{default:d((()=>a[19]||(a[19]=[p("选择Excel文件")]))),_:1})])),tip:d((()=>a[20]||(a[20]=[c("div",{class:"el-upload__tip"}," 请上传 .xlsx 或 .xls 格式的文件，大小不超过10MB。 ",-1)]))),_:1},512)])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),n(R,{visible:re.value,"onUpdate:visible":a[9]||(a[9]=e=>re.value=e),"initial-data":se.value,onSuccess:Ve},null,8,["visible","initial-data"])])}}}),[["__scopeId","data-v-d4c145ad"]]);export{P as default};
