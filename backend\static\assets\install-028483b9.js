/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(e,n)};function e(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}var n=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},i=new function(){this.browser=new n,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(i.wxa=!0,i.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?i.worker=!0:!i.hasGlobalWindow||"Deno"in window?(i.node=!0,i.svgSupported=!0):function(t,e){var n=e.browser,i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(n.firefox=!0,n.version=i[1]);r&&(n.ie=!0,n.version=r[1]);o&&(n.edge=!0,n.version=o[1],n.newEdge=+o[1].split(".")[0]>18);a&&(n.weChat=!0);e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!n.ie&&!n.edge,e.pointerEventsSupported="onpointerdown"in window&&(n.edge||n.ie&&+n.version>=11),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(n.ie&&"transition"in s||n.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||n.ie&&+n.version>=9}(navigator.userAgent,i);const r=i;var o="sans-serif",a="12px "+o;var s,l,u=function(t){var e={};if("undefined"==typeof JSON)return e;for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),h={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(!s){var n=h.createCanvas();s=n&&n.getContext("2d")}if(s)return l!==e&&(l=s.font=e||a),s.measureText(t);t=t||"";var i=/((?:\d+)?\.?\d*)px/.exec(e=e||a),r=i&&+i[1]||12,o=0;if(e.indexOf("mono")>=0)o=r*t.length;else for(var c=0;c<t.length;c++){var p=u[t[c]];o+=null==p?r:p*r}return{width:o}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}},c=N(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],(function(t,e){return t["[object "+e+"]"]=!0,t}),{}),p=N(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],(function(t,e){return t["[object "+e+"Array]"]=!0,t}),{}),d=Object.prototype.toString,f=Array.prototype,g=f.forEach,y=f.filter,v=f.slice,m=f.map,_=function(){}.constructor,x=_?_.prototype:null,w="__proto__",b=2311;function S(){return b++}function M(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function T(t){if(null==t||"object"!=typeof t)return t;var e=t,n=d.call(t);if("[object Array]"===n){if(!lt(t)){e=[];for(var i=0,r=t.length;i<r;i++)e[i]=T(t[i])}}else if(p[n]){if(!lt(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!c[n]&&!lt(t)&&!j(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==w&&(e[a]=T(t[a]));return e}function C(t,e,n){if(!Y(e)||!Y(t))return n?T(e):t;for(var i in e)if(e.hasOwnProperty(i)&&i!==w){var r=t[i],o=e[i];!Y(o)||!Y(r)||H(o)||H(r)||j(o)||j(r)||q(o)||q(r)||lt(o)||lt(r)?!n&&i in t||(t[i]=T(e[i])):C(r,o,n)}return t}function I(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=C(n,t[i],e);return n}function k(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==w&&(t[n]=e[n]);return t}function D(t,e,n){for(var i=z(e),r=0,o=i.length;r<o;r++){var a=i[r];(n?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}function A(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function P(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else D(t,e,n)}function L(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function O(t,e,n){if(t&&e)if(t.forEach&&t.forEach===g)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function R(t,e,n){if(!t)return[];if(!e)return nt(t);if(t.map&&t.map===m)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function N(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function B(t,e,n){if(!t)return[];if(!e)return nt(t);if(t.filter&&t.filter===y)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function E(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]}function z(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}var F=x&&W(x.bind)?x.call.bind(x.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(v.call(arguments)))}};function V(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(v.call(arguments)))}}function H(t){return Array.isArray?Array.isArray(t):"[object Array]"===d.call(t)}function W(t){return"function"==typeof t}function G(t){return"string"==typeof t}function U(t){return"[object String]"===d.call(t)}function X(t){return"number"==typeof t}function Y(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function q(t){return!!c[d.call(t)]}function Z(t){return!!p[d.call(t)]}function j(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function K(t){return null!=t.colorStops}function $(t){return"[object RegExp]"===d.call(t)}function Q(t){return t!=t}function J(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function tt(t,e){return null!=t?t:e}function et(t,e,n){return null!=t?t:null!=e?e:n}function nt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return v.apply(t,e)}function it(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function rt(t,e){if(!t)throw new Error(e)}function ot(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var at="__ec_primitive__";function st(t){t[at]=!0}function lt(t){return t[at]}var ut=function(){function t(){this.data={}}return t.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return z(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},t}(),ht="function"==typeof Map;var ct=function(){function t(e){var n=H(e);this.data=ht?new Map:new ut;var i=this;function r(t,e){n?i.set(t,e):i.set(e,t)}e instanceof t?e.each(r):e&&O(e,r)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach((function(n,i){t.call(e,n,i)}))},t.prototype.keys=function(){var t=this.data.keys();return ht?Array.from(t):t},t.prototype.removeKey=function(t){this.data.delete(t)},t}();function pt(t){return new ct(t)}function dt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function ft(t,e){var n;if(Object.create)n=Object.create(t);else{var i=function(){};i.prototype=t,n=new i}return e&&k(n,e),n}function gt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function yt(t,e){return t.hasOwnProperty(e)}function vt(){}var mt=180/Math.PI;function _t(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function xt(t,e){return t[0]=e[0],t[1]=e[1],t}function wt(t){return[t[0],t[1]]}function bt(t,e,n){return t[0]=e,t[1]=n,t}function St(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Mt(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function Tt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Ct(t){return Math.sqrt(function(t){return t[0]*t[0]+t[1]*t[1]}(t))}function It(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function kt(t,e){var n=Ct(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Dt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var At=Dt;var Pt=function(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])};function Lt(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function Ot(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function Rt(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function Nt(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var Bt=function(t,e){this.target=t,this.topTarget=e&&e.topTarget};const Et=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Bt(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(r,o,t),this.handler.dispatchToElement(new Bt(e,t),"drag",t.event);var a=this.handler.findHover(n,i,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.handler.dispatchToElement(new Bt(s,t),"dragleave",t.event),a&&a!==s&&this.handler.dispatchToElement(new Bt(a,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Bt(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Bt(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}();const zt=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),!n||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;var s={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},l=r[t].length-1,u=r[t][l];return u&&u.callAtLast?r[t].splice(l,0,s):r[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},t.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t}();var Ft=Math.log(2);function Vt(t,e,n,i,r,o){var a=i+"-"+r,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/Ft);return t[n][l]}for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,d=0;p<s;p++){var f=1<<p;f&r||(c+=(d%2?-1:1)*t[n][p]*Vt(t,e-1,h,u,r|f,o),d++)}return o[a]=c,c}function Ht(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=Vt(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Vt(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var Wt="___zrEVENTSAVED",Gt=[];function Ut(t,e,n,i,o){if(e.getBoundingClientRect&&r.domSupported&&!Xt(e)){var a=e[Wt]||(e[Wt]={}),s=function(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=o%2,l=(o>>1)%2;a.style.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[s]+":0",r[l]+":0",i[1-s]+":auto",r[1-l]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}(e,a),l=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,d=h.top;a.push(p,d),l=l&&o&&p===o[c]&&d===o[c+1],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?Ht(s,a):Ht(a,s))}(s,a,o);if(l)return l(t,n,i),!0}return!1}function Xt(t){return"CANVAS"===t.nodeName.toUpperCase()}var Yt=/([&<>"'])/g,qt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Zt(t){return null==t?"":(t+"").replace(Yt,(function(t,e){return qt[e]}))}var jt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Kt=[],$t=r.browser.firefox&&+r.browser.version.split(".")[0]<39;function Qt(t,e,n,i){return n=n||{},i?Jt(t,e,n):$t&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Jt(t,e,n),n}function Jt(t,e,n){if(r.domSupported&&t.getBoundingClientRect){var i=e.clientX,o=e.clientY;if(Xt(t)){var a=t.getBoundingClientRect();return n.zrX=i-a.left,void(n.zrY=o-a.top)}if(Ut(Kt,t,i,o))return n.zrX=Kt[0],void(n.zrY=Kt[1])}n.zrX=n.zrY=0}function te(t){return t||window.event}function ee(t,e,n){if(null!=(e=te(e)).zrX)return e;var i=e.type;if(i&&i.indexOf("touch")>=0){var r="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];r&&Qt(t,r,e,n)}else{Qt(t,e,e,n);var o=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,i=t.deltaY;if(null==n||null==i)return e;return 3*(0!==i?Math.abs(i):Math.abs(n))*(i>0?-1:i<0?1:n>0?-1:1)}(e);e.zrDelta=o?o/120:-(e.detail||0)/3}var a=e.button;return null==e.which&&void 0!==a&&jt.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}function ne(t,e,n,i){t.addEventListener(e,n,i)}var ie=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0};function re(t){return 2===t.which||3===t.which}var oe=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=Qt(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},t.prototype._recognize=function(t){for(var e in se)if(se.hasOwnProperty(e)){var n=se[e](this._track,t);if(n)return n}},t}();function ae(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}var se={pinch:function(t,e){var n=t.length;if(n){var i,r=(t[n-1]||{}).points,o=(t[n-2]||{}).points||r;if(o&&o.length>1&&r&&r.length>1){var a=ae(r)/ae(o);!isFinite(a)&&(a=1),e.pinchScale=a;var s=[((i=r)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}};function le(){return[1,0,0,1,0,0]}function ue(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function he(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function ce(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function pe(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function de(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],u=e[5],h=Math.sin(n),c=Math.cos(n);return t[0]=r*c+s*h,t[1]=-r*h+s*c,t[2]=o*c+l*h,t[3]=-o*h+c*l,t[4]=c*(a-i[0])+h*(u-i[1])+i[0],t[5]=c*(u-i[1])-h*(a-i[0])+i[1],t}function fe(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function ge(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}function ye(t){var e=[1,0,0,1,0,0];return he(e,t),e}const ve=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,n=this.y-t.y;return e*e+n*n},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,n=this.y;return this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,n){t.x=e,t.y=n},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},t.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},t.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},t.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},t.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},t}();var me=Math.min,_e=Math.max,xe=new ve,we=new ve,be=new ve,Se=new ve,Me=new ve,Te=new ve,Ce=function(){function t(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}return t.prototype.union=function(t){var e=me(t.x,this.x),n=me(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=_e(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=_e(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=[1,0,0,1,0,0];return pe(r,r,[-e.x,-e.y]),fe(r,r,[n,i]),pe(r,r,[t.x,t.y]),r},t.prototype.intersect=function(e,n){if(!e)return!1;e instanceof t||(e=t.create(e));var i=this,r=i.x,o=i.x+i.width,a=i.y,s=i.y+i.height,l=e.x,u=e.x+e.width,h=e.y,c=e.y+e.height,p=!(o<l||u<r||s<h||c<a);if(n){var d=1/0,f=0,g=Math.abs(o-l),y=Math.abs(u-r),v=Math.abs(s-h),m=Math.abs(c-a),_=Math.min(g,y),x=Math.min(v,m);o<l||u<r?_>f&&(f=_,g<y?ve.set(Te,-g,0):ve.set(Te,y,0)):_<d&&(d=_,g<y?ve.set(Me,g,0):ve.set(Me,-y,0)),s<h||c<a?x>f&&(f=x,v<m?ve.set(Te,0,-v):ve.set(Te,0,m)):_<d&&(d=_,v<m?ve.set(Me,0,v):ve.set(Me,0,-m))}return n&&ve.copy(n,p?Me:Te),p},t.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,n,i){if(i){if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var r=i[0],o=i[3],a=i[4],s=i[5];return e.x=n.x*r+a,e.y=n.y*o+s,e.width=n.width*r,e.height=n.height*o,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}xe.x=be.x=n.x,xe.y=Se.y=n.y,we.x=Se.x=n.x+n.width,we.y=be.y=n.y+n.height,xe.transform(i),Se.transform(i),we.transform(i),be.transform(i),e.x=me(xe.x,we.x,be.x,Se.x),e.y=me(xe.y,we.y,be.y,Se.y);var l=_e(xe.x,we.x,be.x,Se.x),u=_e(xe.y,we.y,be.y,Se.y);e.width=l-e.x,e.height=u-e.y}else e!==n&&t.copy(e,n)},t}();const Ie=Ce;var ke="silent";function De(){ie(this.event)}var Ae=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return e(n,t),n.prototype.dispose=function(){},n.prototype.setCursor=function(){},n}(zt),Pe=function(t,e){this.x=t,this.y=e},Le=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Oe=new Ie(0,0,0,0),Re=function(t){function n(e,n,i,r,o){var a=t.call(this)||this;return a._hovered=new Pe(0,0),a.storage=e,a.painter=n,a.painterRoot=r,a._pointerSize=o,i=i||new Ae,a.proxy=null,a.setHandlerProxy(i),a._draggingMgr=new Et(a),a}return e(n,t),n.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(O(Le,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},n.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=Ee(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target);var a=this._hovered=i?new Pe(e,n):this.findHover(e,n),s=a.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},n.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},n.prototype.resize=function(){this._hovered=new Pe(0,0)},n.prototype.dispatch=function(t,e){var n=this[t];n&&n.call(this,e)},n.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},n.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},n.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o=function(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:De}}(e,t,n);i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget?i.__hostTarget:i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)})))}},n.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new Pe(t,e);if(Be(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new Ie(t-s,e-s,a,a),u=i.length-1;u>=0;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(Oe.copy(h.getBoundingRect()),h.transform&&Oe.applyTransform(h.transform),Oe.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,d=0;d<s;d+=4)for(var f=0;f<p;f+=c){if(Be(o,r,t+d*Math.cos(f),e+d*Math.sin(f),n),r.target)return r}}return r},n.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new oe);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r;var o=new Pe;o.target=i.target,this.dispatchToElement(o,r,i.event)}},n}(zt);function Ne(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0);var s=i.__hostTarget;i=s||i.parent}return!r||ke}return!1}function Be(t,e,n,i,r){for(var o=t.length-1;o>=0;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=Ne(a,n,i))&&(!e.topTarget&&(e.topTarget=a),s!==ke)){e.target=a;break}}}function Ee(t,e,n){var i=t.painter;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}O(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){Re.prototype[t]=function(e){var n,i,r=e.zrX,o=e.zrY,a=Ee(this,r,o);if("mouseup"===t&&a||(i=(n=this.findHover(r,o)).target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||At(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}));const ze=Re;function Fe(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;!function(t,e,n){n--;for(;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}(t,e,r)}else for(;r<n&&i(t[r],t[r-1])>=0;)r++;return r-e}function Ve(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=o+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=a}}function He(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])>0){for(s=i-r;l<s&&o(t,e[n+r+l])>0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])>0?a=h+1:l=h}return l}function We(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}else{for(s=i-r;l<s&&o(t,e[n+r+l])>=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function Ge(t,e){var n,i,r=7,o=0,a=[];function s(s){var l=n[s],u=i[s],h=n[s+1],c=i[s+1];i[s]=u+c,s===o-3&&(n[s+1]=n[s+2],i[s+1]=i[s+2]),o--;var p=We(t[h],t,l,u,0,e);l+=p,0!==(u-=p)&&0!==(c=He(t[l+u-1],t,h,c,c-1,e))&&(u<=c?function(n,i,o,s){var l=0;for(l=0;l<i;l++)a[l]=t[n+l];var u=0,h=o,c=n;if(t[c++]=t[h++],0===--s){for(l=0;l<i;l++)t[c+l]=a[u+l];return}if(1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];return void(t[c+s]=a[u])}var p,d,f,g=r;for(;;){p=0,d=0,f=!1;do{if(e(t[h],a[u])<0){if(t[c++]=t[h++],d++,p=0,0===--s){f=!0;break}}else if(t[c++]=a[u++],p++,d=0,1===--i){f=!0;break}}while((p|d)<g);if(f)break;do{if(0!==(p=We(t[h],a,u,i,0,e))){for(l=0;l<p;l++)t[c+l]=a[u+l];if(c+=p,u+=p,(i-=p)<=1){f=!0;break}}if(t[c++]=t[h++],0===--s){f=!0;break}if(0!==(d=He(a[u],t,h,s,0,e))){for(l=0;l<d;l++)t[c+l]=t[h+l];if(c+=d,h+=d,0===(s-=d)){f=!0;break}}if(t[c++]=a[u++],1===--i){f=!0;break}g--}while(p>=7||d>=7);if(f)break;g<0&&(g=0),g+=2}if((r=g)<1&&(r=1),1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];t[c+s]=a[u]}else{if(0===i)throw new Error;for(l=0;l<i;l++)t[c+l]=a[u+l]}}(l,u,h,c):function(n,i,o,s){var l=0;for(l=0;l<s;l++)a[l]=t[o+l];var u=n+i-1,h=s-1,c=o+s-1,p=0,d=0;if(t[c--]=t[u--],0===--i){for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l];return}if(1===s){for(d=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[d+l]=t[p+l];return void(t[c]=a[h])}var f=r;for(;;){var g=0,y=0,v=!1;do{if(e(a[h],t[u])<0){if(t[c--]=t[u--],g++,y=0,0===--i){v=!0;break}}else if(t[c--]=a[h--],y++,g=0,1===--s){v=!0;break}}while((g|y)<f);if(v)break;do{if(0!==(g=i-We(a[h],t,n,i,i-1,e))){for(i-=g,d=(c-=g)+1,p=(u-=g)+1,l=g-1;l>=0;l--)t[d+l]=t[p+l];if(0===i){v=!0;break}}if(t[c--]=a[h--],1===--s){v=!0;break}if(0!==(y=s-He(t[u],a,0,s,s-1,e))){for(s-=y,d=(c-=y)+1,p=(h-=y)+1,l=0;l<y;l++)t[d+l]=a[p+l];if(s<=1){v=!0;break}}if(t[c--]=t[u--],0===--i){v=!0;break}f--}while(g>=7||y>=7);if(v)break;f<0&&(f=0),f+=2}(r=f)<1&&(r=1);if(1===s){for(d=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[d+l]=t[p+l];t[c]=a[h]}else{if(0===s)throw new Error;for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l]}}(l,u,h,c))}return n=[],i=[],{mergeRuns:function(){for(;o>1;){var t=o-2;if(t>=1&&i[t-1]<=i[t]+i[t+1]||t>=2&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;s(t)}},forceMergeRuns:function(){for(;o>1;){var t=o-2;t>0&&i[t-1]<i[t+1]&&t--,s(t)}},pushRun:function(t,e){n[o]=t,i[o]=e,o+=1}}}function Ue(t,e,n,i){n||(n=0),i||(i=t.length);var r=i-n;if(!(r<2)){var o=0;if(r<32)Ve(t,n,i,n+(o=Fe(t,n,i,e)),e);else{var a=Ge(t,e),s=function(t){for(var e=0;t>=32;)e|=1&t,t>>=1;return t+e}(r);do{if((o=Fe(t,n,i,e))<s){var l=r;l>s&&(l=s),Ve(t,n,n+l,n+o,e),o=l}a.pushRun(n,o),a.mergeRuns(),r-=o,n+=o}while(0!==r);a.forceMergeRuns()}}}var Xe=!1;function Ye(){Xe||(Xe=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function qe(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}const Ze=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=qe}return t.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,Ue(n,qe)},t.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),o=r,r=r.getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=1),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{var u=t;e&&e.length?u.__clipPaths=e:u.__clipPaths&&u.__clipPaths.length>0&&(u.__clipPaths=[]),isNaN(u.z)&&(Ye(),u.z=0),isNaN(u.z2)&&(Ye(),u.z2=0),isNaN(u.zlevel)&&(Ye(),u.zlevel=0),this._displayList[this._displayListLen++]=u}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,n);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,n);var p=t.getTextContent();p&&this._updateAndAddDisplayable(p,e,n)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=A(this._roots,t);i>=0&&this._roots.splice(i,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}();const je=r.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)};var Ke={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-Ke.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*Ke.bounceIn(2*t):.5*Ke.bounceOut(2*t-1)+.5}};const $e=Ke;var Qe=Math.pow,Je=Math.sqrt,tn=1e-8,en=1e-4,nn=Je(3),rn=1/3,on=_t(),an=_t(),sn=_t();function ln(t){return t>-1e-8&&t<tn}function un(t){return t>tn||t<-1e-8}function hn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function cn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function pn(t,e,n,i,r,o){var a=i+3*(e-n)-t,s=3*(n-2*e+t),l=3*(e-t),u=t-r,h=s*s-3*a*l,c=s*l-9*a*u,p=l*l-3*s*u,d=0;if(ln(h)&&ln(c)){if(ln(s))o[0]=0;else(M=-l/s)>=0&&M<=1&&(o[d++]=M)}else{var f=c*c-4*h*p;if(ln(f)){var g=c/h,y=-g/2;(M=-s/a+g)>=0&&M<=1&&(o[d++]=M),y>=0&&y<=1&&(o[d++]=y)}else if(f>0){var v=Je(f),m=h*s+1.5*a*(-c+v),_=h*s+1.5*a*(-c-v);(M=(-s-((m=m<0?-Qe(-m,rn):Qe(m,rn))+(_=_<0?-Qe(-_,rn):Qe(_,rn))))/(3*a))>=0&&M<=1&&(o[d++]=M)}else{var x=(2*h*s-3*a*c)/(2*Je(h*h*h)),w=Math.acos(x)/3,b=Je(h),S=Math.cos(w),M=(-s-2*b*S)/(3*a),T=(y=(-s+b*(S+nn*Math.sin(w)))/(3*a),(-s+b*(S-nn*Math.sin(w)))/(3*a));M>=0&&M<=1&&(o[d++]=M),y>=0&&y<=1&&(o[d++]=y),T>=0&&T<=1&&(o[d++]=T)}}return d}function dn(t,e,n,i,r){var o=6*n-12*e+6*t,a=9*e+3*i-3*t-9*n,s=3*e-3*t,l=0;if(ln(a)){if(un(o))(h=-s/o)>=0&&h<=1&&(r[l++]=h)}else{var u=o*o-4*a*s;if(ln(u))r[0]=-o/(2*a);else if(u>0){var h,c=Je(u),p=(-o-c)/(2*a);(h=(-o+c)/(2*a))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}function fn(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-a)*r+a,h=(l-s)*r+s,c=(h-u)*r+u;o[0]=t,o[1]=a,o[2]=u,o[3]=c,o[4]=c,o[5]=h,o[6]=l,o[7]=i}function gn(t,e,n,i,r,o,a,s,l,u,h){var c,p,d,f,g,y=.005,v=1/0;on[0]=l,on[1]=u;for(var m=0;m<1;m+=.05)an[0]=hn(t,n,r,a,m),an[1]=hn(e,i,o,s,m),(f=Pt(on,an))<v&&(c=m,v=f);v=1/0;for(var _=0;_<32&&!(y<en);_++)p=c-y,d=c+y,an[0]=hn(t,n,r,a,p),an[1]=hn(e,i,o,s,p),f=Pt(an,on),p>=0&&f<v?(c=p,v=f):(sn[0]=hn(t,n,r,a,d),sn[1]=hn(e,i,o,s,d),g=Pt(sn,on),d<=1&&g<v?(c=d,v=g):y*=.5);return h&&(h[0]=hn(t,n,r,a,c),h[1]=hn(e,i,o,s,c)),Je(v)}function yn(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,d=1;d<=l;d++){var f=d*p,g=hn(t,n,r,a,f),y=hn(e,i,o,s,f),v=g-u,m=y-h;c+=Math.sqrt(v*v+m*m),u=g,h=y}return c}function vn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function mn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function _n(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function xn(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}function wn(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;on[0]=a,on[1]=s;for(var p=0;p<1;p+=.05){an[0]=vn(t,n,r,p),an[1]=vn(e,i,o,p),(y=Pt(on,an))<c&&(u=p,c=y)}c=1/0;for(var d=0;d<32&&!(h<en);d++){var f=u-h,g=u+h;an[0]=vn(t,n,r,f),an[1]=vn(e,i,o,f);var y=Pt(an,on);if(f>=0&&y<c)u=f,c=y;else{sn[0]=vn(t,n,r,g),sn[1]=vn(e,i,o,g);var v=Pt(sn,on);g<=1&&v<c?(u=g,c=v):h*=.5}}return l&&(l[0]=vn(t,n,r,u),l[1]=vn(e,i,o,u)),Je(c)}function bn(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,d=vn(t,n,r,p),f=vn(e,i,o,p),g=d-s,y=f-l;u+=Math.sqrt(g*g+y*y),s=d,l=f}return u}var Sn=/cubic-bezier\(([0-9,\.e ]+)\)/;function Mn(t){var e=t&&Sn.exec(t);if(e){var n=e[1].split(","),i=+ot(n[0]),r=+ot(n[1]),o=+ot(n[2]),a=+ot(n[3]);if(isNaN(i+r+o+a))return;var s=[];return function(t){return t<=0?0:t>=1?1:pn(0,i,o,1,t,s)&&hn(0,r,a,1,s[0])}}}const Tn=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||vt,this.ondestroy=t.ondestroy||vt,this.onrestart=t.onrestart||vt,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n;r<0&&(r=0),r=Math.min(r,1);var o=this.easingFunc,a=o?o(r):r;if(this.onframe(a),1===r){if(!this.loop)return!0;var s=i%n;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=W(t)?t:$e[t]||Mn(t)},t}();var Cn=function(t){this.value=t},In=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new Cn(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}();const kn=function(){function t(t){this._list=new In,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var n=this._list,i=this._map,r=null;if(null==i[t]){var o=n.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=n.head;n.remove(s),delete i[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new Cn(e),a.key=t,n.insertEntry(a),i[t]=a}return r},t.prototype.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}();var Dn={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function An(t){return(t=Math.round(t))<0?0:t>255?255:t}function Pn(t){return t<0?0:t>1?1:t}function Ln(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?An(parseFloat(e)/100*255):An(parseInt(e,10))}function On(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Pn(parseFloat(e)/100):Pn(parseFloat(e))}function Rn(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function Nn(t,e,n){return t+(e-t)*n}function Bn(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function En(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var zn=new kn(20),Fn=null;function Vn(t,e){Fn&&En(Fn,e),Fn=zn.put(t,Fn||e.slice())}function Hn(t,e){if(t){e=e||[];var n=zn.get(t);if(n)return En(e,n);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Dn)return En(e,Dn[i]),Vn(t,e),e;var r,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?(r=parseInt(i.slice(1,4),16))>=0&&r<=4095?(Bn(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===o?parseInt(i.slice(4),16)/15:1),Vn(t,e),e):void Bn(e,0,0,0,1):7===o||9===o?(r=parseInt(i.slice(1,7),16))>=0&&r<=16777215?(Bn(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===o?parseInt(i.slice(7),16)/255:1),Vn(t,e),e):void Bn(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var l=i.substr(0,a),u=i.substr(a+1,s-(a+1)).split(","),h=1;switch(l){case"rgba":if(4!==u.length)return 3===u.length?Bn(e,+u[0],+u[1],+u[2],1):Bn(e,0,0,0,1);h=On(u.pop());case"rgb":return u.length>=3?(Bn(e,Ln(u[0]),Ln(u[1]),Ln(u[2]),3===u.length?h:On(u[3])),Vn(t,e),e):void Bn(e,0,0,0,1);case"hsla":return 4!==u.length?void Bn(e,0,0,0,1):(u[3]=On(u[3]),Wn(u,e),Vn(t,e),e);case"hsl":return 3!==u.length?void Bn(e,0,0,0,1):(Wn(u,e),Vn(t,e),e);default:return}}Bn(e,0,0,0,1)}}function Wn(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=On(t[1]),r=On(t[2]),o=r<=.5?r*(i+1):r+i-r*i,a=2*r-o;return Bn(e=e||[],An(255*Rn(a,o,n+1/3)),An(255*Rn(a,o,n)),An(255*Rn(a,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Gn(t,e){var n=Hn(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:n[i]<0&&(n[i]=0);return Zn(n,4===n.length?"rgba":"rgb")}}function Un(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=e[r],s=e[o],l=i-r;return n[0]=An(Nn(a[0],s[0],l)),n[1]=An(Nn(a[1],s[1],l)),n[2]=An(Nn(a[2],s[2],l)),n[3]=Pn(Nn(a[3],s[3],l)),n}}function Xn(t,e,n){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=Hn(e[r]),s=Hn(e[o]),l=i-r,u=Zn([An(Nn(a[0],s[0],l)),An(Nn(a[1],s[1],l)),An(Nn(a[2],s[2],l)),Pn(Nn(a[3],s[3],l))],"rgba");return n?{color:u,leftIndex:r,rightIndex:o,value:i}:u}}function Yn(t,e,n,i){var r,o=Hn(t);if(t)return o=function(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),l=s-a,u=(s+a)/2;if(0===l)e=0,n=0;else{n=u<.5?l/(s+a):l/(2-s-a);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,p=((s-o)/6+l/2)/l;i===s?e=p-c:r===s?e=1/3+h-p:o===s&&(e=2/3+c-h),e<0&&(e+=1),e>1&&(e-=1)}var d=[360*e,n,u];return null!=t[3]&&d.push(t[3]),d}}(o),null!=e&&(o[0]=(r=e,(r=Math.round(r))<0?0:r>360?360:r)),null!=n&&(o[1]=On(n)),null!=i&&(o[2]=On(i)),Zn(Wn(o),"rgba")}function qn(t,e){var n=Hn(t);if(n&&null!=e)return n[3]=Pn(e),Zn(n,"rgba")}function Zn(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}function jn(t,e){var n=Hn(t);return n?(.299*n[0]+.587*n[1]+.114*n[2])*n[3]/255+(1-n[3])*e:0}var Kn=new kn(100);function $n(t){if(G(t)){var e=Kn.get(t);return e||(e=Gn(t,-.1),Kn.put(t,e)),e}if(K(t)){var n=k({},t);return n.colorStops=R(t.colorStops,(function(t){return{offset:t.offset,color:Gn(t.color,-.1)}})),n}return t}var Qn=Math.round;function Jn(t){var e;if(t&&"transparent"!==t){if("string"==typeof t&&t.indexOf("rgba")>-1){var n=Hn(t);n&&(t="rgb("+n[0]+","+n[1]+","+n[2]+")",e=n[3])}}else t="none";return{color:t,opacity:null==e?1:e}}var ti=1e-4;function ei(t){return t<ti&&t>-1e-4}function ni(t){return Qn(1e3*t)/1e3}function ii(t){return Qn(1e4*t)/1e4}function ri(t){return"matrix("+ni(t[0])+","+ni(t[1])+","+ni(t[2])+","+ni(t[3])+","+ii(t[4])+","+ii(t[5])+")"}var oi={left:"start",right:"end",center:"middle",middle:"middle"};function ai(t,e,n){return"top"===n?t+=e/2:"bottom"===n&&(t-=e/2),t}function si(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}function li(t){var e=t.style,n=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),n[0],n[1]].join(",")}function ui(t){return t&&!!t.image}function hi(t){return ui(t)||function(t){return t&&!!t.svgElement}(t)}function ci(t){return"linear"===t.type}function pi(t){return"radial"===t.type}function di(t){return t&&("linear"===t.type||"radial"===t.type)}function fi(t){return"url(#"+t+")"}function gi(t){var e=t.getGlobalScale(),n=Math.max(e[0],e[1]);return Math.max(Math.ceil(Math.log(n)/Math.log(10)),1)}function yi(t){var e=t.x||0,n=t.y||0,i=(t.rotation||0)*mt,r=tt(t.scaleX,1),o=tt(t.scaleY,1),a=t.skewX||0,s=t.skewY||0,l=[];return(e||n)&&l.push("translate("+e+"px,"+n+"px)"),i&&l.push("rotate("+i+")"),1===r&&1===o||l.push("scale("+r+","+o+")"),(a||s)&&l.push("skew("+Qn(a*mt)+"deg, "+Qn(s*mt)+"deg)"),l.join(" ")}var vi=r.hasGlobalWindow&&W(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:"undefined"!=typeof Buffer?function(t){return Buffer.from(t).toString("base64")}:function(t){return null},mi=Array.prototype.slice;function _i(t,e,n){return(e-t)*n+t}function xi(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=_i(e[o],n[o],i);return t}function wi(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function bi(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function Si(t,e){for(var n=t.length,i=e.length,r=n>i?e:t,o=Math.min(n,i),a=r[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(n,i);s++)r.push({offset:a.offset,color:a.color.slice()})}function Mi(t,e,n){var i=t,r=e;if(i.push&&r.push){var o=i.length,a=r.length;if(o!==a)if(o>a)i.length=a;else for(var s=o;s<a;s++)i.push(1===n?r[s]:mi.call(r[s]));var l=i[0]&&i[0].length;for(s=0;s<i.length;s++)if(1===n)isNaN(i[s])&&(i[s]=r[s]);else for(var u=0;u<l;u++)isNaN(i[s][u])&&(i[s][u]=r[s][u])}}function Ti(t){if(L(t)){var e=t.length;if(L(t[0])){for(var n=[],i=0;i<e;i++)n.push(mi.call(t[i]));return n}return mi.call(t)}return t}function Ci(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Ii(t){return 4===t||5===t}function ki(t){return 1===t||2===t}var Di=[0,0,0,0],Ai=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i=this.keyframes,r=i.length,o=!1,a=6,s=e;if(L(e)){var l=function(t){return L(t&&t[0])?2:1}(e);a=l,(1===l&&!X(e[0])||2===l&&!X(e[0][0]))&&(o=!0)}else if(X(e)&&!Q(e))a=0;else if(G(e))if(isNaN(+e)){var u=Hn(e);u&&(s=u,a=3)}else a=0;else if(K(e)){var h=k({},s);h.colorStops=R(e.colorStops,(function(t){return{offset:t.offset,color:Hn(t.color)}})),ci(e)?a=4:pi(e)&&(a=5),s=h}0===r?this.valType=a:a===this.valType&&6!==a||(o=!0),this.discrete=this.discrete||o;var c={time:t,value:s,rawValue:e,percent:0};return n&&(c.easing=n,c.easingFunc=W(n)?n:$e[n]||Mn(n)),i.push(c),c},t.prototype.prepare=function(t,e){var n=this.keyframes;this._needsSort&&n.sort((function(t,e){return t.time-e.time}));for(var i=this.valType,r=n.length,o=n[r-1],a=this.discrete,s=ki(i),l=Ii(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;h.percent=h.time/t,a||(s&&u!==r-1?Mi(c,p,i):l&&Si(c.colorStops,p.colorStops))}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;var d=n[0].value;for(u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-d:3===i?n[u].additiveValue=wi([],n[u].value,d,-1):ki(i)&&(n[u].additiveValue=1===i?wi([],n[u].value,d,-1):bi([],n[u].value,d,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o=null!=this._additiveTrack,a=o?"additiveValue":"value",s=this.valType,l=this.keyframes,u=l.length,h=this.propName,c=3===s,p=this._lastFr,d=Math.min;if(1===u)i=r=l[0];else{if(e<0)n=0;else if(e<this._lastFrP){for(n=d(p+1,u-1);n>=0&&!(l[n].percent<=e);n--);n=d(n,u-2)}else{for(n=p;n<u&&!(l[n].percent>e);n++);n=d(n-1,u-2)}r=l[n+1],i=l[n]}if(i&&r){this._lastFr=n,this._lastFrP=e;var f=r.percent-i.percent,g=0===f?1:d((e-i.percent)/f,1);r.easingFunc&&(g=r.easingFunc(g));var y=o?this._additiveValue:c?Di:t[h];if(!ki(s)&&!c||y||(y=this._additiveValue=[]),this.discrete)t[h]=g<1?i.rawValue:r.rawValue;else if(ki(s))1===s?xi(y,i[a],r[a],g):function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=_i(e[a][s],n[a][s],i)}}(y,i[a],r[a],g);else if(Ii(s)){var v=i[a],m=r[a],_=4===s;t[h]={type:_?"linear":"radial",x:_i(v.x,m.x,g),y:_i(v.y,m.y,g),colorStops:R(v.colorStops,(function(t,e){var n=m.colorStops[e];return{offset:_i(t.offset,n.offset,g),color:Ci(xi([],t.color,n.color,g))}})),global:m.global},_?(t[h].x2=_i(v.x2,m.x2,g),t[h].y2=_i(v.y2,m.y2,g)):t[h].r=_i(v.r,m.r,g)}else if(c)xi(y,i[a],r[a],g),o||(t[h]=Ci(y));else{var x=_i(i[a],r[a],g);o?this._additiveValue=x:t[h]=x}o&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(Hn(t[n],Di),wi(Di,Di,i,1),t[n]=Ci(Di)):1===e?wi(t[n],t[n],i,1):2===e&&bi(t[n],t[n],i,1)},t}(),Pi=function(){function t(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&i?M("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,z(e),n)},t.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o],s=r[a];if(!s){s=r[a]=new Ai(a);var l=void 0,u=this._getAdditiveTrack(a);if(u){var h=u.keyframes,c=h[h.length-1];l=c&&c.value,3===u.valType&&l&&(l=Ci(l))}else l=this._target[a];if(null==l)continue;t>0&&s.addKeyframe(0,Ti(l),i),this._trackKeys.push(a)}s.addKeyframe(t,Ti(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,n=[],i=this._maxTime||0,r=0;r<this._trackKeys.length;r++){var o=this._trackKeys[r],a=this._tracks[o],s=this._getAdditiveTrack(o),l=a.keyframes,u=l.length;if(a.prepare(i,s),a.needsAnimate())if(!this._allowDiscrete&&a.discrete){var h=l[u-1];h&&(e._target[a.propName]=h.rawValue),a.setFinished()}else n.push(a)}if(n.length||this._force){var c=new Tn({life:i,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var i=e._additiveAnimators;if(i){for(var r=!1,o=0;o<i.length;o++)if(i[o]._clip){r=!0;break}r||(e._additiveAnimators=null)}for(o=0;o<n.length;o++)n[o].step(e._target,t);var a=e._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return R(this._trackKeys,(function(e){return t._tracks[e]}))},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];if(o&&!o.isFinished()){var a=o.keyframes,s=a[n?0:a.length-1];s&&(t[r]=Ti(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||z(t);for(var n=0;n<e.length;n++){var i=e[n],r=this._tracks[i];if(r){var o=r.keyframes;if(o.length>1){var a=o.pop();r.addKeyframe(a.time,t[i]),r.prepare(this._maxTime,r.getAdditiveTrack())}}}},t}();function Li(){return(new Date).getTime()}var Oi=function(t){function n(e){var n=t.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,e=e||{},n.stage=e.stage||{},n}return e(n,t),n.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},n.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},n.prototype.removeClip=function(t){if(t.animation){var e=t.prev,n=t.next;e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},n.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},n.prototype.update=function(t){for(var e=Li()-this._pausedTime,n=e-this._time,i=this._head;i;){var r=i.next;i.step(e,n)?(i.ondestroy(),this.removeClip(i),i=r):i=r}this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},n.prototype._startLoop=function(){var t=this;this._running=!0,je((function e(){t._running&&(je(e),!t._paused&&t.update())}))},n.prototype.start=function(){this._running||(this._time=Li(),this._pausedTime=0,this._startLoop())},n.prototype.stop=function(){this._running=!1},n.prototype.pause=function(){this._paused||(this._pauseStart=Li(),this._paused=!0)},n.prototype.resume=function(){this._paused&&(this._pausedTime+=Li()-this._pauseStart,this._paused=!1)},n.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},n.prototype.isFinished=function(){return null==this._head},n.prototype.animate=function(t,e){e=e||{},this.start();var n=new Pi(t,e.loop);return this.addAnimator(n),n},n}(zt);const Ri=Oi;var Ni,Bi,Ei=r.domSupported,zi=(Bi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:Ni=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:R(Ni,(function(t){var e=t.replace("mouse","pointer");return Bi.hasOwnProperty(e)?e:t}))}),Fi=["mousemove","mouseup"],Vi=["pointermove","pointerup"],Hi=!1;function Wi(t){var e=t.pointerType;return"pen"===e||"touch"===e}function Gi(t){t&&(t.zrByTouch=!0)}function Ui(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var Xi=function(t,e){this.stopPropagation=vt,this.stopImmediatePropagation=vt,this.preventDefault=vt,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Yi={mousedown:function(t){t=ee(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=ee(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=ee(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){Ui(this,(t=ee(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Hi=!0,t=ee(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Hi||(t=ee(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Gi(t=ee(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Yi.mousemove.call(this,t),Yi.mousedown.call(this,t)},touchmove:function(t){Gi(t=ee(this.dom,t)),this.handler.processGesture(t,"change"),Yi.mousemove.call(this,t)},touchend:function(t){Gi(t=ee(this.dom,t)),this.handler.processGesture(t,"end"),Yi.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Yi.click.call(this,t)},pointerdown:function(t){Yi.mousedown.call(this,t)},pointermove:function(t){Wi(t)||Yi.mousemove.call(this,t)},pointerup:function(t){Yi.mouseup.call(this,t)},pointerout:function(t){Wi(t)||Yi.mouseout.call(this,t)}};O(["click","dblclick","contextmenu"],(function(t){Yi[t]=function(e){e=ee(this.dom,e),this.trigger(t,e)}}));var qi={pointermove:function(t){Wi(t)||qi.mousemove.call(this,t)},pointerup:function(t){qi.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function Zi(t,e){var n=e.domHandlers;r.pointerEventsSupported?O(zi.pointer,(function(i){Ki(e,i,(function(e){n[i].call(t,e)}))})):(r.touchEventsSupported&&O(zi.touch,(function(i){Ki(e,i,(function(r){n[i].call(t,r),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}(e)}))})),O(zi.mouse,(function(i){Ki(e,i,(function(r){r=te(r),e.touching||n[i].call(t,r)}))})))}function ji(t,e){function n(n){Ki(e,n,(function(i){i=te(i),Ui(t,i.target)||(i=function(t,e){return ee(t.dom,new Xi(t,e),!0)}(t,i),e.domHandlers[n].call(t,i))}),{capture:!0})}r.pointerEventsSupported?O(Vi,n):r.touchEventsSupported||O(Fi,n)}function Ki(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,ne(t.domTarget,e,n,i)}function $i(t){var e,n,i,r,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,n=a,i=o[a],r=t.listenerOpts[a],e.removeEventListener(n,i,r));t.mounted={}}var Qi=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e};const Ji=function(t){function n(e,n){var i=t.call(this)||this;return i.__pointerCapturing=!1,i.dom=e,i.painterRoot=n,i._localHandlerScope=new Qi(e,Yi),Ei&&(i._globalHandlerScope=new Qi(document,qi)),Zi(i,i._localHandlerScope),i}return e(n,t),n.prototype.dispose=function(){$i(this._localHandlerScope),Ei&&$i(this._globalHandlerScope)},n.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},n.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,Ei&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?ji(this,e):$i(e)}},n}(zt);var tr=1;r.hasGlobalWindow&&(tr=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var er=tr,nr="#333",ir="#ccc",rr=ue,or=5e-5;function ar(t){return t>or||t<-5e-5}var sr=[],lr=[],ur=[1,0,0,1,0,0],hr=Math.abs,cr=function(){function t(){}var e;return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return ar(this.rotation)||ar(this.x)||ar(this.y)||ar(this.scaleX-1)||ar(this.scaleY-1)||ar(this.skewX)||ar(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||[1,0,0,1,0,0],e?this.getLocalTransform(n):rr(n),t&&(e?ce(n,t,n):he(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(rr(n),this.invTransform=null)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(sr);var n=sr[0]<0?-1:1,i=sr[1]<0?-1:1,r=((sr[0]-n)*e+n)/sr[0]||0,o=((sr[1]-i)*e+i)/sr[1]||0;t[0]*=r,t[1]*=r,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||[1,0,0,1,0,0],ge(this.invTransform,t)},t.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),r=Math.PI/2+i-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(r),e=Math.sqrt(e),this.skewX=r,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=n,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||[1,0,0,1,0,0],ce(lr,t.invTransform,e),e=lr);var n=this.originX,i=this.originY;(n||i)&&(ur[4]=n,ur[5]=i,ce(lr,e,ur),lr[4]-=n,lr[5]-=i,e=lr),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&Ot(n,n,i),n},t.prototype.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&Ot(n,n,i),n},t.prototype.getLineScale=function(){var t=this.transform;return t&&hr(t[0]-1)>1e-10&&hr(t[3]-1)>1e-10?Math.sqrt(hr(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){dr(this,t)},t.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,p=t.skewY?Math.tan(-t.skewY):0;if(n||i||a||s){var d=n+a,f=i+s;e[4]=-d*r-c*f*o,e[5]=-f*o-p*d*r}else e[4]=e[5]=0;return e[0]=r,e[3]=o,e[1]=p*r,e[2]=c*o,l&&de(e,e,l),e[4]+=n+u,e[5]+=i+h,e},t.initDefaultProps=((e=t.prototype).scaleX=e.scaleY=e.globalScaleRatio=1,void(e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0)),t}(),pr=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function dr(t,e){for(var n=0;n<pr.length;n++){var i=pr[n];t[i]=e[i]}}const fr=cr;var gr={};function yr(t,e){var n=gr[e=e||a];n||(n=gr[e]=new kn(500));var i=n.get(t);return null==i&&(i=h.measureText(t,e).width,n.put(t,i)),i}function vr(t,e,n,i){var r=yr(t,e),o=wr(e),a=_r(0,r,n),s=xr(0,o,i);return new Ie(a,s,r,o)}function mr(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return vr(r[0],e,n,i);for(var o=new Ie(0,0,0,0),a=0;a<r.length;a++){var s=vr(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function _r(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function xr(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function wr(t){return yr("国",t)}function br(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Sr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=br(i[0],n.width),u+=br(i[1],n.height),h=null,c=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var Mr="__zr_normal__",Tr=pr.concat(["ignore"]),Cr=N(pr,(function(t,e){return t[e]=!0,t}),{ignore:!1}),Ir={},kr=new Ie(0,0,0,0),Dr=function(){function t(t){this.id=S(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var n=this.textConfig,i=n.local,r=e.innerTransformable,o=void 0,a=void 0,s=!1;r.parent=i?this:null;var l=!1;if(r.copyTransform(e),null!=n.position){var u=kr;n.layoutRect?u.copy(n.layoutRect):u.copy(this.getBoundingRect()),i||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Ir,n,u):Sr(Ir,n,u),r.x=Ir.x,r.y=Ir.y,o=Ir.align,a=Ir.verticalAlign;var h=n.origin;if(h&&null!=n.rotation){var c=void 0,p=void 0;"center"===h?(c=.5*u.width,p=.5*u.height):(c=br(h[0],u.width),p=br(h[1],u.height)),l=!0,r.originX=-r.x+c+(i?0:u.x),r.originY=-r.y+p+(i?0:u.y)}}null!=n.rotation&&(r.rotation=n.rotation);var d=n.offset;d&&(r.x+=d[0],r.y+=d[1],l||(r.originX=-d[0],r.originY=-d[1]));var f=null==n.inside?"string"==typeof n.position&&n.position.indexOf("inside")>=0:n.inside,g=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),y=void 0,v=void 0,m=void 0;f&&this.canBeInsideText()?(y=n.insideFill,v=n.insideStroke,null!=y&&"auto"!==y||(y=this.getInsideTextFill()),null!=v&&"auto"!==v||(v=this.getInsideTextStroke(y),m=!0)):(y=n.outsideFill,v=n.outsideStroke,null!=y&&"auto"!==y||(y=this.getOutsideFill()),null!=v&&"auto"!==v||(v=this.getOutsideStroke(y),m=!0)),(y=y||"#000")===g.fill&&v===g.stroke&&m===g.autoStroke&&o===g.align&&a===g.verticalAlign||(s=!0,g.fill=y,g.stroke=v,g.autoStroke=m,g.align=o,g.verticalAlign=a,e.setDefaultTextStyle(g)),e.__dirty|=1,s&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?ir:nr},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&Hn(e);n||(n=[255,255,255,1]);for(var i=n[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,Zn(n,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},k(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(Y(t))for(var n=z(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;if(!(i.getLoop()||r&&r!==Mr)){var o=i.targetName,a=o?e[o]:e;i.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Tr)},t.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(Mr,!1,t)},t.prototype.useState=function(t,e,n,i){var r=t===Mr;if(this.hasState()||!r){var o=this.currentStates,a=this.stateTransition;if(!(A(o,t)>=0)||!e&&1!==o.length){var s;if(this.stateProxy&&!r&&(s=this.stateProxy(t)),s||(s=this.states&&this.states[t]),s||r){r||this.saveCurrentToNormalState(s);var l=!!(s&&s.hoverLayer||i);l&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,s,this._normalState,e,!n&&!this.__inHover&&a&&a.duration>0,a);var u=this._textContent,h=this._textGuide;return u&&u.useState(t,e,n,l),h&&h.useState(t,e,n,l),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!l&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),s}M("State "+t+" not exists.")}}},t.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var l=t[s],u=void 0;this.stateProxy&&(u=this.stateProxy(l,t)),u||(u=this.states[l]),u&&i.push(u)}var h=i[o-1],c=!!(h&&h.hoverLayer||n);c&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(i),d=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var f=this._textContent,g=this._textGuide;f&&f.useStates(t,e,c),g&&g.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}else this.clearStates()},t.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=A(this.currentStates,t);if(e>=0){var n=this.currentStates.slice();n.splice(e,1),this.useStates(n)}},t.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),r=A(i,t),o=A(i,e)>=0;r>=0?o?i.splice(r,1):i[r]=e:n&&!o&&i.push(e),this.useStates(i)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];k(n,r),r.textConfig&&k(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},t.prototype._applyStateObj=function(t,e,n,i,r,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=k({},i?this.textConfig:n.textConfig),k(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig);for(var s={},l=!1,u=0;u<Tr.length;u++){var h=Tr[u],c=r&&Cr[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],d=p.targetName;p.getLoop()||p.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new fr,this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),k(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=1;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,n){var i=t?this[t]:this,r=new Pi(i,e,n);return t&&(r.targetName=t),this.addAnimator(r,t),r},t.prototype.addAnimator=function(t,e){var n=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,n=A(e,t);n>=0&&e.splice(n,1)})),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},t.prototype.animateTo=function(t,e,n){Ar(this,t,e,n)},t.prototype.animateFrom=function(t,e,n){Ar(this,t,e,n,!0)},t.prototype._transitionState=function(t,e,n,i){for(var r=Ar(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;function n(t,n,i,r){function o(t,e){Object.defineProperty(e,0,{get:function(){return t[i]},set:function(e){t[i]=e}}),Object.defineProperty(e,1,{get:function(){return t[r]},set:function(e){t[r]=e}})}Object.defineProperty(e,t,{get:function(){this[n]||o(this,this[n]=[]);return this[n]},set:function(t){this[i]=t[0],this[r]=t[1],this[n]=t,o(this,t)}})}e.type="element",e.name="",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=1,Object.defineProperty&&(n("position","_legacyPos","x","y"),n("scale","_legacyScale","scaleX","scaleY"),n("origin","_legacyOrigin","originX","originY"))}(),t}();function Ar(t,e,n,i,r){var o=[];Or(t,"",t,e,n=n||{},i,o,r);var a=o.length,s=!1,l=n.done,u=n.aborted,h=function(){s=!0,--a<=0&&(s?l&&l():u&&u())},c=function(){--a<=0&&(s?l&&l():u&&u())};a||l&&l(),o.length>0&&n.during&&o[0].during((function(t,e){n.during(e)}));for(var p=0;p<o.length;p++){var d=o[p];h&&d.done(h),c&&d.aborted(c),n.force&&d.duration(n.duration),d.start(n.easing)}return o}function Pr(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function Lr(t,e,n){if(L(e[n]))if(L(t[n])||(t[n]=[]),Z(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),Pr(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(L(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?Pr(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else Pr(o,r,a);o.length=r.length}else t[n]=e[n]}function Or(t,e,n,i,r,o,a,s){for(var l=z(i),u=r.duration,h=r.delay,c=r.additive,p=r.setToFinal,d=!Y(o),f=t.animators,g=[],y=0;y<l.length;y++){var v=l[y],m=i[v];if(null!=m&&null!=n[v]&&(d||o[v]))if(!Y(m)||L(m)||K(m))g.push(v);else{if(e){s||(n[v]=m,t.updateDuringAnimation(e));continue}Or(t,v,n[v],m,r,o&&o[v],a,s)}else s||(n[v]=m,t.updateDuringAnimation(e),g.push(v))}var _=g.length;if(!c&&_)for(var x=0;x<f.length;x++){if((b=f[x]).targetName===e)if(b.stopTracks(g)){var w=A(f,b);f.splice(w,1)}}if(r.force||(g=B(g,(function(t){return e=i[t],r=n[t],!(e===r||L(e)&&L(r)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(e,r));var e,r})),_=g.length),_>0||r.force&&!a.length){var b,S=void 0,M=void 0,T=void 0;if(s){M={},p&&(S={});for(x=0;x<_;x++){M[v=g[x]]=n[v],p?S[v]=i[v]:n[v]=i[v]}}else if(p){T={};for(x=0;x<_;x++){T[v=g[x]]=Ti(n[v]),Lr(n,i,v)}}(b=new Pi(n,!1,!1,c?B(f,(function(t){return t.targetName===e})):null)).targetName=e,r.scope&&(b.scope=r.scope),p&&S&&b.whenWithKeys(0,S,g),T&&b.whenWithKeys(0,T,g),b.whenWithKeys(null==u?500:u,s?M:i,g).delay(h||0),t.addAnimator(b,e),a.push(b)}}P(Dr,zt),P(Dr,fr);const Rr=Dr;var Nr=function(t){function n(e){var n=t.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(e),n}return e(n,t),n.prototype.childrenRef=function(){return this._children},n.prototype.children=function(){return this._children.slice()},n.prototype.childAt=function(t){return this._children[t]},n.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},n.prototype.childCount=function(){return this._children.length},n.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},n.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},n.prototype.replace=function(t,e){var n=A(this._children,t);return n>=0&&this.replaceAt(e,n),this},n.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];if(t&&t!==this&&t.parent!==this&&t!==i){n[e]=t,i.parent=null;var r=this.__zr;r&&i.removeSelfFromZr(r),this._doAdd(t)}return this},n.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},n.prototype.remove=function(t){var e=this.__zr,n=this._children,i=A(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},n.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},n.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},n.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},n.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].addSelfToZr(e)}},n.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].removeSelfFromZr(e)}},n.prototype.getBoundingRect=function(t){for(var e=new Ie(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a=n[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(i);l?(Ie.applyTransform(e,s,l),(r=r||e.clone()).union(e)):(r=r||s.clone()).union(s)}}return r||e},n}(Rr);Nr.prototype.type="group";const Br=Nr;
/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Er={},zr={};var Fr,Vr=function(){function t(t,e,n){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t;var o=new Ze,a=n.renderer||"canvas";Er[a]||(a=z(Er)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect;var s=new Er[a](e,o,n,t),l=n.ssr||s.ssrOnly;this.storage=o,this.painter=s;var u,h=r.node||r.worker||l?null:new Ji(s.getViewportRoot(),s.root),c=n.useCoarsePointer;(null==c||"auto"===c?r.touchEventsSupported:!!c)&&(u=tt(n.pointerSize,44)),this.handler=new ze(o,s,h,s.root,u),this.animation=new Ri({stage:{update:l?null:function(){return i._flush(!0)}}}),l||this.animation.start()}return t.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},t.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return jn(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=jn(e[r].color,1);return(n/=i)<.4}return!1}(t))},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},t.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},t.prototype.flush=function(){this._disposed||this._flush(!1)},t.prototype._flush=function(t){var e,n=Li();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=Li();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},t.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},t.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},t.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},t.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},t.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},t.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},t.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},t.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Br&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},t.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete zr[t])},t}();function Hr(t,e){var n=new Vr(S(),t,e);return zr[n.id]=n,n}function Wr(t){if("function"==typeof Fr)return Fr(t)}var Gr=1e-4;function Ur(t,e,n,i){var r=e[0],o=e[1],a=n[0],s=n[1],l=o-r,u=s-a;if(0===l)return 0===u?a:(a+s)/2;if(i)if(l>0){if(t<=r)return a;if(t>=o)return s}else{if(t>=r)return a;if(t<=o)return s}else{if(t===r)return a;if(t===o)return s}return(t-r)/l*u+a}function Xr(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return G(t)?(n=t,n.replace(/^\s+|\s+$/g,"")).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t;var n}function Yr(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function qr(t){return t.sort((function(t,e){return t-e})),t}function Zr(t){if(t=+t,isNaN(t))return 0;if(t>1e-14)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return function(t){var e=t.toString().toLowerCase(),n=e.indexOf("e"),i=n>0?+e.slice(n+1):0,r=n>0?n:e.length,o=e.indexOf("."),a=o<0?0:r-1-o;return Math.max(0,a-i)}(t)}function jr(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Math.abs(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function Kr(t,e){var n=Math.max(Zr(t),Zr(e)),i=t+e;return n>20?i:Yr(i,n)}var $r=9007199254740991;function Qr(t){var e=2*Math.PI;return(t%e+e)%e}function Jr(t){return t>-1e-4&&t<Gr}var to=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function eo(t){if(t instanceof Date)return t;if(G(t)){var e=to.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0)}return null==t?new Date(NaN):new Date(Math.round(t))}function no(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function io(t,e){var n=no(t),i=Math.pow(10,n),r=t/i;return t=(e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10)*i,n>=-20?+t.toFixed(n<0?-n:0):t}function ro(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],o=n-i;return o?r+o*(t[i]-r):r}function oo(t){t.sort((function(t,e){return s(t,e,0)?-1:1}));for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!==1?t.splice(i,1):i++}return t;function s(t,e,n){return t.interval[n]<e.interval[n]||t.interval[n]===e.interval[n]&&(t.close[n]-e.close[n]===(n?-1:1)||!n&&s(t,e,1))}}function ao(t){var e=parseFloat(t);return e==t&&(0!==e||!G(t)||t.indexOf("x")<=0)?e:NaN}function so(t){return!isNaN(ao(t))}function lo(){return Math.round(9*Math.random())}function uo(t,e){return 0===e?t:uo(e,t%e)}function ho(t,e){return null==t?e:null==e?t:t*e/uo(t,e)}var co={},po="undefined"!=typeof console&&console.warn&&console.log;function fo(t,e){!function(t,e,n){if(po){if(n){if(co[e])return;co[e]=!0}console[t]("[ECharts] "+e)}}("warn",t,e)}function go(t){throw new Error(t)}function yo(t,e,n){return(e-t)*n+t}var vo="series\0",mo="\0_ec_\0";function _o(t){return t instanceof Array?t:null==t?[]:[t]}function xo(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var wo=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function bo(t){return!Y(t)||H(t)||t instanceof Date?t:t.value}function So(t){return Y(t)&&!(t instanceof Array)}function Mo(t,e,n){var i="normalMerge"===n,r="replaceMerge"===n,o="replaceAll"===n;t=t||[],e=(e||[]).slice();var a=pt();O(e,(function(t,n){Y(t)||(e[n]=null)}));var s,l,u=function(t,e,n){var i=[];if("replaceAll"===n)return i;for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||Do(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,a,n);return(i||r)&&function(t,e,n,i){O(i,(function(r,o){if(r&&null!=r.id){var a=Co(r.id),s=n.get(a);if(null!=s){var l=t[s];rt(!l.newOption,'Duplicated option on id "'+a+'".'),l.newOption=r,l.existing=e[s],i[o]=null}}}))}(u,t,a,e),i&&function(t,e){O(e,(function(n,i){if(n&&null!=n.name)for(var r=0;r<t.length;r++){var o=t[r].existing;if(!t[r].newOption&&o&&(null==o.id||null==n.id)&&!Do(n)&&!Do(o)&&To("name",o,n))return t[r].newOption=n,void(e[i]=null)}}))}(u,e),i||r?function(t,e,n){O(e,(function(e){if(e){for(var i,r=0;(i=t[r])&&(i.newOption||Do(i.existing)||i.existing&&null!=e.id&&!To("id",e,i.existing));)r++;i?(i.newOption=e,i.brandNew=n):t.push({newOption:e,brandNew:n,existing:null,keyInfo:null}),r++}}))}(u,e,r):o&&function(t,e){O(e,(function(e){t.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})}))}(u,e),s=u,l=pt(),O(s,(function(t){var e=t.existing;e&&l.set(e.id,t)})),O(s,(function(t){var e=t.newOption;rt(!e||null==e.id||!l.get(e.id)||l.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&l.set(e.id,t),!t.keyInfo&&(t.keyInfo={})})),O(s,(function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(Y(i)){if(r.name=null!=i.name?Co(i.name):n?n.name:vo+e,n)r.id=Co(n.id);else if(null!=i.id)r.id=Co(i.id);else{var o=0;do{r.id="\0"+r.name+"\0"+o++}while(l.get(r.id))}l.set(r.id,t)}})),u}function To(t,e,n){var i=Io(e[t],null),r=Io(n[t],null);return null!=i&&null!=r&&i===r}function Co(t){return Io(t,"")}function Io(t,e){return null==t?e:G(t)?t:X(t)||U(t)?t+"":e}function ko(t){var e=t.name;return!(!e||!e.indexOf(vo))}function Do(t){return t&&null!=t.id&&0===Co(t.id).indexOf(mo)}function Ao(t){return mo+t}function Po(t,e){var n={},i={};return r(t||[],n),r(e||[],i,n),[o(n),o(i)];function r(t,e,n){for(var i=0,r=t.length;i<r;i++){var o=Io(t[i].seriesId,null);if(null==o)return;for(var a=_o(t[i].dataIndex),s=n&&n[o],l=0,u=a.length;l<u;l++){var h=a[l];s&&s[h]?s[h]=null:(e[o]||(e[o]={}))[h]=1}}}function o(t,e){var n=[];for(var i in t)if(t.hasOwnProperty(i)&&null!=t[i])if(e)n.push(+i);else{var r=o(t[i],!0);r.length&&n.push({seriesId:i,dataIndex:r})}return n}}function Lo(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?H(e.dataIndex)?R(e.dataIndex,(function(e){return t.indexOfRawIndex(e)})):t.indexOfRawIndex(e.dataIndex):null!=e.name?H(e.name)?R(e.name,(function(e){return t.indexOfName(e)})):t.indexOfName(e.name):void 0}function Oo(){var t="__ec_inner_"+Ro++;return function(e){return e[t]||(e[t]={})}}var Ro=lo();function No(t,e,n){var i=Bo(e,n),r=i.mainTypeSpecified,o=i.queryOptionMap,a=i.others,s=n?n.defaultMainType:null;return!r&&s&&o.set(s,{}),o.each((function(e,i){var r=Fo(t,i,e,{useDefault:s===i,enableAll:!n||null==n.enableAll||n.enableAll,enableNone:!n||null==n.enableNone||n.enableNone});a[i+"Models"]=r.models,a[i+"Model"]=r.models[0]})),a}function Bo(t,e){var n;if(G(t)){var i={};i[t+"Index"]=0,n=i}else n=t;var r=pt(),o={},a=!1;return O(n,(function(t,n){if("dataIndex"!==n&&"dataIndexInside"!==n){var i=n.match(/^(\w+)(Index|Id|Name)$/)||[],s=i[1],l=(i[2]||"").toLowerCase();if(s&&l&&!(e&&e.includeMainTypes&&A(e.includeMainTypes,s)<0))a=a||!!s,(r.get(s)||r.set(s,{}))[l]=t}else o[n]=t})),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var Eo={useDefault:!0,enableAll:!1,enableNone:!1},zo={useDefault:!1,enableAll:!0,enableNone:!0};function Fo(t,e,n,i){i=i||Eo;var r=n.index,o=n.id,a=n.name,s={models:null,specified:null!=r||null!=o||null!=a};if(!s.specified){var l=void 0;return s.models=i.useDefault&&(l=t.getComponent(e))?[l]:[],s}return"none"===r||!1===r?(rt(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):("all"===r&&(rt(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=a=null),s.models=t.queryComponents({mainType:e,index:r,id:o,name:a}),s)}function Vo(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function Ho(t,e){var n=pt(),i=[];return O(t,(function(t){var r=e(t);(n.get(r)||(i.push(r),n.set(r,[]))).push(t)})),{keys:i,buckets:n}}function Wo(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(X(i))return Yr(f=yo(n||0,i,r),o?Math.max(Zr(n||0),Zr(i)):e);if(G(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c=t.getDimensionInfo(h);if(c&&"ordinal"===c.type)a[h]=(r<1&&s?s:l)[h];else{var p=s&&s[h]?s[h]:0,d=l[h],f=yo(p,d,r);a[h]=Yr(f,o?Math.max(Zr(p),Zr(d)):e)}}return a}var Go="___EC__COMPONENT__CONTAINER___",Uo="___EC__EXTENDED_CLASS___";function Xo(t){var e={main:"",sub:""};if(t){var n=t.split(".");e.main=n[0]||"",e.sub=n[1]||""}return e}function Yo(t,n){t.$constructor=t,t.extend=function(t){var n,i,r=this;return W(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?n=function(t){function n(){return t.apply(this,arguments)||this}return e(n,t),n}(r):(n=function(){(t.$constructor||r).apply(this,arguments)},function(t,e){var n=t.prototype;function i(){}for(var r in i.prototype=e.prototype,t.prototype=new i,n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);t.prototype.constructor=t,t.superClass=e}(n,this)),k(n.prototype,t),n[Uo]=!0,n.extend=this.extend,n.superCall=jo,n.superApply=Ko,n.superClass=r,n}}function qo(t,e){t.extend=e.extend}var Zo=Math.round(10*Math.random());function jo(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function Ko(t,e,n){return this.superClass.prototype[e].apply(t,n)}function $o(t){var e={};t.registerClass=function(t){var n,i=t.type||t.prototype.type;if(i){rt(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(n=i),'componentType "'+n+'" illegal'),t.prototype.type=i;var r=Xo(i);if(r.sub){if(r.sub!==Go){var o=function(t){var n=e[t.main];n&&n[Go]||((n=e[t.main]={})[Go]=!0);return n}(r);o[r.sub]=t}}else e[r.main]=t}return t},t.getClass=function(t,n,i){var r=e[t];if(r&&r[Go]&&(r=n?r[n]:null),i&&!r)throw new Error(n?"Component "+t+"."+(n||"")+" is used but not imported.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){var n=Xo(t),i=[],r=e[n.main];return r&&r[Go]?O(r,(function(t,e){e!==Go&&i.push(t)})):i.push(r),i},t.hasClass=function(t){var n=Xo(t);return!!e[n.main]},t.getAllClassMainTypes=function(){var t=[];return O(e,(function(e,n){t.push(n)})),t},t.hasSubTypes=function(t){var n=Xo(t),i=e[n.main];return i&&i[Go]}}function Qo(t,e){for(var n=0;n<t.length;n++)t[n][1]||(t[n][1]=t[n][0]);return e=e||!1,function(n,i,r){for(var o={},a=0;a<t.length;a++){var s=t[a][1];if(!(i&&A(i,s)>=0||r&&A(r,s)<0)){var l=n.getShallow(s,e);null!=l&&(o[t[a][0]]=l)}}return o}}var Jo=Qo([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),ta=function(){function t(){}return t.prototype.getAreaStyle=function(t,e){return Jo(this,t,e)},t}(),ea=new kn(50);function na(t){if("string"==typeof t){var e=ea.get(t);return e&&e.image}return t}function ia(t,e,n,i,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var o=ea.get(t),a={hostEl:n,cb:i,cbPayload:r};return o?!oa(e=o.image)&&o.pending.push(a):((e=h.loadImage(t,ra,ra)).__zrImageSrc=t,ea.put(t,e.__cachedImgObj={image:e,pending:[a]})),e}return t}return e}function ra(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function oa(t){return t&&t.width&&t.height}var aa=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function sa(t,e,n,i,r,o){if(!n)return t.text="",void(t.isTruncated=!1);var a=(e+"").split("\n");o=la(n,i,r,o);for(var s=!1,l={},u=0,h=a.length;u<h;u++)ua(l,a[u],o),a[u]=l.textLine,s=s||l.isTruncated;t.text=a.join("\n"),t.isTruncated=s}function la(t,e,n,i){var r=k({},i=i||{});r.font=e,n=tt(n,"..."),r.maxIterations=tt(i.maxIterations,2);var o=r.minChar=tt(i.minChar,0);r.cnCharWidth=yr("国",e);var a=r.ascCharWidth=yr("a",e);r.placeholder=tt(i.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;l<o&&s>=a;l++)s-=a;var u=yr(n,e);return u>s&&(n="",u=0),s=t-u,r.ellipsis=n,r.ellipsisWidth=u,r.contentWidth=s,r.containerWidth=t,r}function ua(t,e,n){var i=n.containerWidth,r=n.font,o=n.contentWidth;if(!i)return t.textLine="",void(t.isTruncated=!1);var a=yr(e,r);if(a<=i)return t.textLine=e,void(t.isTruncated=!1);for(var s=0;;s++){if(a<=o||s>=n.maxIterations){e+=n.ellipsis;break}var l=0===s?ha(e,o,n.ascCharWidth,n.cnCharWidth):a>0?Math.floor(e.length*o/a):0;a=yr(e=e.substr(0,l),r)}""===e&&(e=n.placeholder),t.textLine=e,t.isTruncated=!0}function ha(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}var ca=function(){},pa=function(t){this.tokens=[],t&&(this.tokens=t)},da=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1};function fa(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;if(i){var p=l.padding,d=p?p[1]+p[3]:0;if(null!=l.width&&"auto"!==l.width){var f=br(l.width,i.width)+d;u.length>0&&f+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=f}else{var g=va(e,h,i.width,i.breakAll,i.accumWidth);i.accumWidth=g.accumWidth+d,a=g.linesWidths,o=g.lines}}else o=e.split("\n");for(var y=0;y<o.length;y++){var v=o[y],m=new ca;if(m.styleName=r,m.text=v,m.isLineHolder=!v&&!s,"number"==typeof l.width?m.width=l.width:m.width=a?a[y]:yr(v,h),y||c)u.push(new pa([m]));else{var _=(u[u.length-1]||(u[0]=new pa)).tokens,x=_.length;1===x&&_[0].isLineHolder?_[0]=m:(v||!x||s)&&_.push(m)}}}var ga=N(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function ya(t){return!function(t){var e=t.charCodeAt(0);return e>=32&&e<=591||e>=880&&e<=4351||e>=4608&&e<=5119||e>=7680&&e<=8303}(t)||!!ga[t]}function va(t,e,n,i,r){for(var o=[],a=[],s="",l="",u=0,h=0,c=0;c<t.length;c++){var p=t.charAt(c);if("\n"!==p){var d=yr(p,e),f=!i&&!ya(p);(o.length?h+d>n:r+h+d>n)?h?(s||l)&&(f?(s||(s=l,l="",h=u=0),o.push(s),a.push(h-u),l+=p,s="",h=u+=d):(l&&(s+=l,l="",u=0),o.push(s),a.push(h),s=p,h=d)):f?(o.push(l),a.push(u),l=p,u=d):(o.push(p),a.push(d)):(h+=d,f?(l+=p,u+=d):(l&&(s+=l,l="",u=0),s+=p))}else l&&(s+=l,h+=u),o.push(s),a.push(h),s="",l="",u=0,h=0}return o.length||s||(s=t,l="",u=0),l&&(s+=l),s&&(o.push(s),a.push(h)),1===o.length&&(h+=r),{accumWidth:h,lines:o,linesWidths:a}}var ma="__zr_style_"+Math.round(10*Math.random()),_a={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},xa={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};_a[ma]=!0;var wa=["z","z2","invisible"],ba=["invisible"],Sa=function(t){function n(e){return t.call(this,e)||this}var i;return e(n,t),n.prototype._init=function(e){for(var n=z(e),i=0;i<n.length;i++){var r=n[i];"style"===r?this.useStyle(e[r]):t.prototype.attrKV.call(this,r,e[r])}this.style||this.useStyle({})},n.prototype.beforeBrush=function(){},n.prototype.afterBrush=function(){},n.prototype.innerBeforeBrush=function(){},n.prototype.innerAfterBrush=function(){},n.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){Ma.copy(t.getBoundingRect()),t.transform&&Ma.applyTransform(t.transform);return Ta.width=e,Ta.height=n,!Ma.intersect(Ta)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},n.prototype.contain=function(t,e){return this.rectContain(t,e)},n.prototype.traverse=function(t,e){t.call(e,this)},n.prototype.rectContain=function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},n.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,n=this.getBoundingRect(),i=this.style,r=i.shadowBlur||0,o=i.shadowOffsetX||0,a=i.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new Ie(0,0,0,0)),e?Ie.applyTransform(t,n,e):t.copy(n),(r||o||a)&&(t.width+=2*r+Math.abs(o),t.height+=2*r+Math.abs(a),t.x=Math.min(t.x,t.x+o-r),t.y=Math.min(t.y,t.y+a-r));var s=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-s),t.y=Math.floor(t.y-s),t.width=Math.ceil(t.width+1+2*s),t.height=Math.ceil(t.height+1+2*s))}return t},n.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new Ie(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},n.prototype.getPrevPaintRect=function(){return this._prevPaintRect},n.prototype.animateStyle=function(t){return this.animate("style",t)},n.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},n.prototype.attrKV=function(e,n){"style"!==e?t.prototype.attrKV.call(this,e,n):this.style?this.setStyle(n):this.useStyle(n)},n.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:k(this.style,t),this.dirtyStyle(),this},n.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},n.prototype.dirty=function(){this.dirtyStyle()},n.prototype.styleChanged=function(){return!!(2&this.__dirty)},n.prototype.styleUpdated=function(){this.__dirty&=-3},n.prototype.createStyle=function(t){return ft(_a,t)},n.prototype.useStyle=function(t){t[ma]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},n.prototype.isStyleObject=function(t){return t[ma]},n.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,n,wa)},n.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.style?o?r?s=n.style:(s=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(s,n.style)):(s=this._mergeStyle(this.createStyle(),r?this.style:i.style),this._mergeStyle(s,n.style)):l&&(s=i.style),s)if(o){var u=this.style;if(this.style=this.createStyle(l?{}:u),l)for(var h=z(u),c=0;c<h.length;c++){(d=h[c])in s&&(s[d]=s[d],this.style[d]=u[d])}var p=z(s);for(c=0;c<p.length;c++){var d=p[c];this.style[d]=this.style[d]}this._transitionState(e,{style:s},a,this.getAnimationStyleProps())}else this.useStyle(s);var f=this.__inHover?ba:wa;for(c=0;c<f.length;c++){d=f[c];n&&null!=n[d]?this[d]=n[d]:l&&null!=i[d]&&(this[d]=i[d])}},n.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},n.prototype._mergeStyle=function(t,e){return k(t,e),t},n.prototype.getAnimationStyleProps=function(){return xa},n.initDefaultProps=((i=n.prototype).type="displayable",i.invisible=!1,i.z=0,i.z2=0,i.zlevel=0,i.culling=!1,i.cursor="pointer",i.rectHover=!1,i.incremental=!1,i._rect=null,i.dirtyRectTolerance=0,void(i.__dirty=3)),n}(Rr),Ma=new Ie(0,0,0,0),Ta=new Ie(0,0,0,0);const Ca=Sa;var Ia=Math.min,ka=Math.max,Da=Math.sin,Aa=Math.cos,Pa=2*Math.PI,La=_t(),Oa=_t(),Ra=_t();function Na(t,e,n){if(0!==t.length){for(var i=t[0],r=i[0],o=i[0],a=i[1],s=i[1],l=1;l<t.length;l++)i=t[l],r=Ia(r,i[0]),o=ka(o,i[0]),a=Ia(a,i[1]),s=ka(s,i[1]);e[0]=r,e[1]=a,n[0]=o,n[1]=s}}function Ba(t,e,n,i,r,o){r[0]=Ia(t,n),r[1]=Ia(e,i),o[0]=ka(t,n),o[1]=ka(e,i)}var Ea=[],za=[];function Fa(t,e,n,i,r,o,a,s,l,u){var h=dn,c=hn,p=h(t,n,r,a,Ea);l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0;for(var d=0;d<p;d++){var f=c(t,n,r,a,Ea[d]);l[0]=Ia(f,l[0]),u[0]=ka(f,u[0])}p=h(e,i,o,s,za);for(d=0;d<p;d++){var g=c(e,i,o,s,za[d]);l[1]=Ia(g,l[1]),u[1]=ka(g,u[1])}l[0]=Ia(t,l[0]),u[0]=ka(t,u[0]),l[0]=Ia(a,l[0]),u[0]=ka(a,u[0]),l[1]=Ia(e,l[1]),u[1]=ka(e,u[1]),l[1]=Ia(s,l[1]),u[1]=ka(s,u[1])}function Va(t,e,n,i,r,o,a,s){var l=_n,u=vn,h=ka(Ia(l(t,n,r),1),0),c=ka(Ia(l(e,i,o),1),0),p=u(t,n,r,h),d=u(e,i,o,c);a[0]=Ia(t,r,p),a[1]=Ia(e,o,d),s[0]=ka(t,r,p),s[1]=ka(e,o,d)}function Ha(t,e,n,i,r,o,a,s,l){var u=Rt,h=Nt,c=Math.abs(r-o);if(c%Pa<1e-4&&c>1e-4)return s[0]=t-n,s[1]=e-i,l[0]=t+n,void(l[1]=e+i);if(La[0]=Aa(r)*n+t,La[1]=Da(r)*i+e,Oa[0]=Aa(o)*n+t,Oa[1]=Da(o)*i+e,u(s,La,Oa),h(l,La,Oa),(r%=Pa)<0&&(r+=Pa),(o%=Pa)<0&&(o+=Pa),r>o&&!a?o+=Pa:r<o&&a&&(r+=Pa),a){var p=o;o=r,r=p}for(var d=0;d<o;d+=Math.PI/2)d>r&&(Ra[0]=Aa(d)*n+t,Ra[1]=Da(d)*i+e,u(s,Ra,s),h(l,Ra,l))}var Wa={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Ga=[],Ua=[],Xa=[],Ya=[],qa=[],Za=[],ja=Math.min,Ka=Math.max,$a=Math.cos,Qa=Math.sin,Ja=Math.abs,ts=Math.PI,es=2*ts,ns="undefined"!=typeof Float32Array,is=[];function rs(t){return Math.round(t/ts*1e8)/1e8%2*ts}function os(t,e){var n=rs(t[0]);n<0&&(n+=es);var i=n-t[0],r=t[1];r+=i,!e&&r-n>=es?r=n+es:e&&n-r>=es?r=n-es:!e&&n>r?r=n+(es-rs(n-r)):e&&n<r&&(r=n-(es-rs(r-n))),t[0]=n,t[1]=r}var as=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}var e;return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,n){(n=n||0)>0&&(this._ux=Ja(n/er/t)||0,this._uy=Ja(n/er/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Wa.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var n=Ja(t-this._xi),i=Ja(e-this._yi),r=n>this._ux||i>this._uy;if(this.addData(Wa.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=n*n+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},t.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Wa.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},t.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Wa.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},t.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),is[0]=i,is[1]=r,os(is,o),i=is[0];var a=(r=is[1])-i;return this.addData(Wa.A,t,e,n,n,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=$a(r)*n+t,this._yi=Qa(r)*n+e,this},t.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},t.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Wa.R,t,e,n,i),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(Wa.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!ns||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},t.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();ns&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},t.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,ns&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){Xa[0]=Xa[1]=qa[0]=qa[1]=Number.MAX_VALUE,Ya[0]=Ya[1]=Za[0]=Za[1]=-Number.MAX_VALUE;var t,e=this.data,n=0,i=0,r=0,o=0;for(t=0;t<this._len;){var a=e[t++],s=1===t;switch(s&&(r=n=e[t],o=i=e[t+1]),a){case Wa.M:n=r=e[t++],i=o=e[t++],qa[0]=r,qa[1]=o,Za[0]=r,Za[1]=o;break;case Wa.L:Ba(n,i,e[t],e[t+1],qa,Za),n=e[t++],i=e[t++];break;case Wa.C:Fa(n,i,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],qa,Za),n=e[t++],i=e[t++];break;case Wa.Q:Va(n,i,e[t++],e[t++],e[t],e[t+1],qa,Za),n=e[t++],i=e[t++];break;case Wa.A:var l=e[t++],u=e[t++],h=e[t++],c=e[t++],p=e[t++],d=e[t++]+p;t+=1;var f=!e[t++];s&&(r=$a(p)*h+l,o=Qa(p)*c+u),Ha(l,u,h,c,p,d,f,qa,Za),n=$a(d)*h+l,i=Qa(d)*c+u;break;case Wa.R:Ba(r=n=e[t++],o=i=e[t++],r+e[t++],o+e[t++],qa,Za);break;case Wa.Z:n=r,i=o}Rt(Xa,Xa,qa),Nt(Ya,Ya,Za)}return 0===t&&(Xa[0]=Xa[1]=Ya[0]=Ya[1]=0),new Ie(Xa[0],Xa[1],Ya[0]-Xa[0],Ya[1]-Xa[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,u=0,h=0,c=0;c<e;){var p=t[c++],d=1===c;d&&(a=r=t[c],s=o=t[c+1]);var f=-1;switch(p){case Wa.M:r=a=t[c++],o=s=t[c++];break;case Wa.L:var g=t[c++],y=(_=t[c++])-o;(Ja(D=g-r)>n||Ja(y)>i||c===e-1)&&(f=Math.sqrt(D*D+y*y),r=g,o=_);break;case Wa.C:var v=t[c++],m=t[c++],_=(g=t[c++],t[c++]),x=t[c++],w=t[c++];f=yn(r,o,v,m,g,_,x,w,10),r=x,o=w;break;case Wa.Q:f=bn(r,o,v=t[c++],m=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case Wa.A:var b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=t[c++],I=t[c++],k=I+C;c+=1,d&&(a=$a(C)*M+b,s=Qa(C)*T+S),f=Ka(M,T)*ja(es,Math.abs(I)),r=$a(k)*M+b,o=Qa(k)*T+S;break;case Wa.R:a=r=t[c++],s=o=t[c++],f=2*t[c++]+2*t[c++];break;case Wa.Z:var D=a-r;y=s-o;f=Math.sqrt(D*D+y*y),r=a,o=s}f>=0&&(l[h++]=f,u+=f)}return this._pathLen=u,u},t.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p=this.data,d=this._ux,f=this._uy,g=this._len,y=e<1,v=0,m=0,_=0;if(!y||(this._pathSegLen||this._calculateLength(),l=this._pathSegLen,u=e*this._pathLen))t:for(var x=0;x<g;){var w=p[x++],b=1===x;switch(b&&(n=r=p[x],i=o=p[x+1]),w!==Wa.L&&_>0&&(t.lineTo(h,c),_=0),w){case Wa.M:n=r=p[x++],i=o=p[x++],t.moveTo(r,o);break;case Wa.L:a=p[x++],s=p[x++];var S=Ja(a-r),M=Ja(s-o);if(S>d||M>f){if(y){if(v+(Z=l[m++])>u){var T=(u-v)/Z;t.lineTo(r*(1-T)+a*T,o*(1-T)+s*T);break t}v+=Z}t.lineTo(a,s),r=a,o=s,_=0}else{var C=S*S+M*M;C>_&&(h=a,c=s,_=C)}break;case Wa.C:var I=p[x++],k=p[x++],D=p[x++],A=p[x++],P=p[x++],L=p[x++];if(y){if(v+(Z=l[m++])>u){fn(r,I,D,P,T=(u-v)/Z,Ga),fn(o,k,A,L,T,Ua),t.bezierCurveTo(Ga[1],Ua[1],Ga[2],Ua[2],Ga[3],Ua[3]);break t}v+=Z}t.bezierCurveTo(I,k,D,A,P,L),r=P,o=L;break;case Wa.Q:I=p[x++],k=p[x++],D=p[x++],A=p[x++];if(y){if(v+(Z=l[m++])>u){xn(r,I,D,T=(u-v)/Z,Ga),xn(o,k,A,T,Ua),t.quadraticCurveTo(Ga[1],Ua[1],Ga[2],Ua[2]);break t}v+=Z}t.quadraticCurveTo(I,k,D,A),r=D,o=A;break;case Wa.A:var O=p[x++],R=p[x++],N=p[x++],B=p[x++],E=p[x++],z=p[x++],F=p[x++],V=!p[x++],H=N>B?N:B,W=Ja(N-B)>.001,G=E+z,U=!1;if(y)v+(Z=l[m++])>u&&(G=E+z*(u-v)/Z,U=!0),v+=Z;if(W&&t.ellipse?t.ellipse(O,R,N,B,F,E,G,V):t.arc(O,R,H,E,G,V),U)break t;b&&(n=$a(E)*N+O,i=Qa(E)*B+R),r=$a(G)*N+O,o=Qa(G)*B+R;break;case Wa.R:n=r=p[x],i=o=p[x+1],a=p[x++],s=p[x++];var X=p[x++],Y=p[x++];if(y){if(v+(Z=l[m++])>u){var q=u-v;t.moveTo(a,s),t.lineTo(a+ja(q,X),s),(q-=X)>0&&t.lineTo(a+X,s+ja(q,Y)),(q-=Y)>0&&t.lineTo(a+Ka(X-q,0),s+Y),(q-=X)>0&&t.lineTo(a,s+Ka(Y-q,0));break t}v+=Z}t.rect(a,s,X,Y);break;case Wa.Z:if(y){var Z;if(v+(Z=l[m++])>u){T=(u-v)/Z;t.lineTo(r*(1-T)+n*T,o*(1-T)+i*T);break t}v+=Z}t.closePath(),r=n,o=i}}},t.prototype.clone=function(){var e=new t,n=this.data;return e.data=n.slice?n.slice():Array.prototype.slice.call(n),e._len=this._len,e},t.CMD=Wa,t.initDefaultProps=((e=t.prototype)._saveData=!0,e._ux=0,e._uy=0,e._pendingPtDist=0,void(e._version=0)),t}();function ss(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,l=0;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>n+s||o<t-s&&o<n-s)return!1;if(t===n)return Math.abs(o-t)<=s/2;var u=(l=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n);return u*u/(l*l+1)<=s/2*s/2}function ls(t,e,n,i,r,o,a,s,l,u,h){if(0===l)return!1;var c=l;return!(h>e+c&&h>i+c&&h>o+c&&h>s+c||h<e-c&&h<i-c&&h<o-c&&h<s-c||u>t+c&&u>n+c&&u>r+c&&u>a+c||u<t-c&&u<n-c&&u<r-c&&u<a-c)&&gn(t,e,n,i,r,o,a,s,u,h,null)<=c/2}function us(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;return!(l>e+u&&l>i+u&&l>o+u||l<e-u&&l<i-u&&l<o-u||s>t+u&&s>n+u&&s>r+u||s<t-u&&s<n-u&&s<r-u)&&wn(t,e,n,i,r,o,s,l,null)<=u/2}var hs=2*Math.PI;function cs(t){return(t%=hs)<0&&(t+=hs),t}var ps=2*Math.PI;function ds(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;s-=t,l-=e;var h=Math.sqrt(s*s+l*l);if(h-u>n||h+u<n)return!1;if(Math.abs(i-r)%ps<1e-4)return!0;if(o){var c=i;i=cs(r),r=cs(c)}else i=cs(i),r=cs(r);i>r&&(r+=ps);var p=Math.atan2(l,s);return p<0&&(p+=ps),p>=i&&p<=r||p+ps>=i&&p+ps<=r}function fs(t,e,n,i,r,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!==a&&0!==a||(s=i<e?.5:-.5);var l=a*(n-t)+t;return l===r?1/0:l>r?s:0}var gs=as.CMD,ys=2*Math.PI;var vs=[-1,-1,-1],ms=[-1,-1];function _s(t,e,n,i,r,o,a,s,l,u){if(u>e&&u>i&&u>o&&u>s||u<e&&u<i&&u<o&&u<s)return 0;var h,c=pn(e,i,o,s,u,vs);if(0===c)return 0;for(var p=0,d=-1,f=void 0,g=void 0,y=0;y<c;y++){var v=vs[y],m=0===v||1===v?.5:1;hn(t,n,r,a,v)<l||(d<0&&(d=dn(e,i,o,s,ms),ms[1]<ms[0]&&d>1&&(h=void 0,h=ms[0],ms[0]=ms[1],ms[1]=h),f=hn(e,i,o,s,ms[0]),d>1&&(g=hn(e,i,o,s,ms[1]))),2===d?v<ms[0]?p+=f<e?m:-m:v<ms[1]?p+=g<f?m:-m:p+=s<g?m:-m:v<ms[0]?p+=f<e?m:-m:p+=s<f?m:-m)}return p}function xs(t,e,n,i,r,o,a,s){if(s>e&&s>i&&s>o||s<e&&s<i&&s<o)return 0;var l=function(t,e,n,i,r){var o=t-2*e+n,a=2*(e-t),s=t-i,l=0;if(ln(o))un(a)&&(h=-s/a)>=0&&h<=1&&(r[l++]=h);else{var u=a*a-4*o*s;if(ln(u))(h=-a/(2*o))>=0&&h<=1&&(r[l++]=h);else if(u>0){var h,c=Je(u),p=(-a-c)/(2*o);(h=(-a+c)/(2*o))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}(e,i,o,s,vs);if(0===l)return 0;var u=_n(e,i,o);if(u>=0&&u<=1){for(var h=0,c=vn(e,i,o,u),p=0;p<l;p++){var d=0===vs[p]||1===vs[p]?.5:1;vn(t,n,r,vs[p])<a||(vs[p]<u?h+=c<e?d:-d:h+=o<c?d:-d)}return h}d=0===vs[0]||1===vs[0]?.5:1;return vn(t,n,r,vs[0])<a?0:o<e?d:-d}function ws(t,e,n,i,r,o,a,s){if((s-=e)>n||s<-n)return 0;var l=Math.sqrt(n*n-s*s);vs[0]=-l,vs[1]=l;var u=Math.abs(i-r);if(u<1e-4)return 0;if(u>=ys-1e-4){i=0,r=ys;var h=o?1:-1;return a>=vs[0]+t&&a<=vs[1]+t?h:0}if(i>r){var c=i;i=r,r=c}i<0&&(i+=ys,r+=ys);for(var p=0,d=0;d<2;d++){var f=vs[d];if(f+t>a){var g=Math.atan2(s,f);h=o?1:-1;g<0&&(g=ys+g),(g>=i&&g<=r||g+ys>=i&&g+ys<=r)&&(g>Math.PI/2&&g<1.5*Math.PI&&(h=-h),p+=h)}}return p}function bs(t,e,n,i,r){for(var o,a,s,l,u=t.data,h=t.len(),c=0,p=0,d=0,f=0,g=0,y=0;y<h;){var v=u[y++],m=1===y;switch(v===gs.M&&y>1&&(n||(c+=fs(p,d,f,g,i,r))),m&&(f=p=u[y],g=d=u[y+1]),v){case gs.M:p=f=u[y++],d=g=u[y++];break;case gs.L:if(n){if(ss(p,d,u[y],u[y+1],e,i,r))return!0}else c+=fs(p,d,u[y],u[y+1],i,r)||0;p=u[y++],d=u[y++];break;case gs.C:if(n){if(ls(p,d,u[y++],u[y++],u[y++],u[y++],u[y],u[y+1],e,i,r))return!0}else c+=_s(p,d,u[y++],u[y++],u[y++],u[y++],u[y],u[y+1],i,r)||0;p=u[y++],d=u[y++];break;case gs.Q:if(n){if(us(p,d,u[y++],u[y++],u[y],u[y+1],e,i,r))return!0}else c+=xs(p,d,u[y++],u[y++],u[y],u[y+1],i,r)||0;p=u[y++],d=u[y++];break;case gs.A:var _=u[y++],x=u[y++],w=u[y++],b=u[y++],S=u[y++],M=u[y++];y+=1;var T=!!(1-u[y++]);o=Math.cos(S)*w+_,a=Math.sin(S)*b+x,m?(f=o,g=a):c+=fs(p,d,o,a,i,r);var C=(i-_)*b/w+_;if(n){if(ds(_,x,b,S,S+M,T,e,C,r))return!0}else c+=ws(_,x,b,S,S+M,T,C,r);p=Math.cos(S+M)*w+_,d=Math.sin(S+M)*b+x;break;case gs.R:if(f=p=u[y++],g=d=u[y++],o=f+u[y++],a=g+u[y++],n){if(ss(f,g,o,g,e,i,r)||ss(o,g,o,a,e,i,r)||ss(o,a,f,a,e,i,r)||ss(f,a,f,g,e,i,r))return!0}else c+=fs(o,g,o,a,i,r),c+=fs(f,a,f,g,i,r);break;case gs.Z:if(n){if(ss(p,d,f,g,e,i,r))return!0}else c+=fs(p,d,f,g,i,r);p=f,d=g}}return n||(s=d,l=g,Math.abs(s-l)<1e-4)||(c+=fs(p,d,f,g,i,r)||0),0!==c}var Ss=D({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},_a),Ms={style:D({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},xa.style)},Ts=pr.concat(["invisible","culling","z","z2","zlevel","parent"]);const Cs=function(t){function n(e){return t.call(this,e)||this}var i;return e(n,t),n.prototype.update=function(){var e=this;t.prototype.update.call(this);var i=this.style;if(i.decal){var r=this._decalEl=this._decalEl||new n;r.buildPath===n.prototype.buildPath&&(r.buildPath=function(t){e.buildPath(t,e.shape)}),r.silent=!0;var o=r.style;for(var a in i)o[a]!==i[a]&&(o[a]=i[a]);o.fill=i.fill?i.decal:null,o.decal=null,o.shadowColor=null,i.strokeFirst&&(o.stroke=null);for(var s=0;s<Ts.length;++s)r[Ts[s]]=this[Ts[s]];r.__dirty|=1}else this._decalEl&&(this._decalEl=null)},n.prototype.getDecalElement=function(){return this._decalEl},n.prototype._init=function(e){var n=z(e);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var r=0;r<n.length;r++){var o=n[r],a=e[o];"style"===o?this.style?k(this.style,a):this.useStyle(a):"shape"===o?k(this.shape,a):t.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},n.prototype.getDefaultStyle=function(){return null},n.prototype.getDefaultShape=function(){return{}},n.prototype.canBeInsideText=function(){return this.hasFill()},n.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(G(t)){var e=jn(t,0);return e>.5?nr:e>.2?"#eee":ir}if(t)return ir}return nr},n.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(G(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())===jn(t,0)<.4)return e}},n.prototype.buildPath=function(t,e,n){},n.prototype.pathUpdated=function(){this.__dirty&=-5},n.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},n.prototype.createPathProxy=function(){this.path=new as(!1)},n.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},n.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},n.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,n=!t;if(n){var i=!1;this.path||(i=!0,this.createPathProxy());var r=this.path;(i||4&this.__dirty)&&(r.beginPath(),this.buildPath(r,this.shape,!1),this.pathUpdated()),t=r.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||n){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var l=this.strokeContainThreshold;s=Math.max(s,null==l?4:l)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},n.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),function(t,e,n,i){return bs(t,e,!0,n,i)}(o,a/s,t,e)))return!0}if(this.hasFill())return function(t,e,n){return bs(t,0,!1,e,n)}(o,t,e)}return!1},n.prototype.dirtyShape=function(){this.__dirty|=4,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},n.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},n.prototype.animateShape=function(t){return this.animate("shape",t)},n.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},n.prototype.attrKV=function(e,n){"shape"===e?this.setShape(n):t.prototype.attrKV.call(this,e,n)},n.prototype.setShape=function(t,e){var n=this.shape;return n||(n=this.shape={}),"string"==typeof t?n[t]=e:k(n,t),this.dirtyShape(),this},n.prototype.shapeChanged=function(){return!!(4&this.__dirty)},n.prototype.createStyle=function(t){return ft(Ss,t)},n.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.shape&&!n.shape&&(n.shape=k({},this.shape))},n.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.shape?o?r?s=n.shape:(s=k({},i.shape),k(s,n.shape)):(s=k({},r?this.shape:i.shape),k(s,n.shape)):l&&(s=i.shape),s)if(o){this.shape=k({},this.shape);for(var u={},h=z(s),c=0;c<h.length;c++){var p=h[c];"object"==typeof s[p]?this.shape[p]=s[p]:u[p]=s[p]}this._transitionState(e,{shape:u},a)}else this.shape=s,this.dirtyShape()},n.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},n.prototype.getAnimationStyleProps=function(){return Ms},n.prototype.isZeroArea=function(){return!1},n.extend=function(t){var i=function(n){function i(e){var i=n.call(this,e)||this;return t.init&&t.init.call(i,e),i}return e(i,n),i.prototype.getDefaultStyle=function(){return T(t.style)},i.prototype.getDefaultShape=function(){return T(t.shape)},i}(n);for(var r in t)"function"==typeof t[r]&&(i.prototype[r]=t[r]);return i},n.initDefaultProps=((i=n.prototype).type="path",i.strokeContainThreshold=5,i.segmentIgnoreThreshold=0,i.subPixelOptimize=!1,i.autoBatch=!1,void(i.__dirty=7)),n}(Ca);var Is=D({strokeFirst:!0,font:a,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Ss),ks=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},n.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},n.prototype.createStyle=function(t){return ft(Is,t)},n.prototype.setBoundingRect=function(t){this._rect=t},n.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var n=mr(e,t.font,t.textAlign,t.textBaseline);if(n.x+=t.x||0,n.y+=t.y||0,this.hasStroke()){var i=t.lineWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect},n.initDefaultProps=void(n.prototype.dirtyRectTolerance=10),n}(Ca);ks.prototype.type="tspan";const Ds=ks;var As=D({x:0,y:0},_a),Ps={style:D({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},xa.style)};var Ls=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.createStyle=function(t){return ft(As,t)},n.prototype._getSize=function(t){var e=this.style,n=e[t];if(null!=n)return n;var i,r=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!r)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?r[t]:r[t]/r[o]*a},n.prototype.getWidth=function(){return this._getSize("width")},n.prototype.getHeight=function(){return this._getSize("height")},n.prototype.getAnimationStyleProps=function(){return Ps},n.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new Ie(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},n}(Ca);Ls.prototype.type="image";const Os=Ls;var Rs=Math.round;function Ns(t,e,n){if(e){var i=e.x1,r=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=r,t.y1=o,t.y2=a;var s=n&&n.lineWidth;return s?(Rs(2*i)===Rs(2*r)&&(t.x1=t.x2=Es(i,s,!0)),Rs(2*o)===Rs(2*a)&&(t.y1=t.y2=Es(o,s,!0)),t):t}}function Bs(t,e,n){if(e){var i=e.x,r=e.y,o=e.width,a=e.height;t.x=i,t.y=r,t.width=o,t.height=a;var s=n&&n.lineWidth;return s?(t.x=Es(i,s,!0),t.y=Es(r,s,!0),t.width=Math.max(Es(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(Es(r+a,s,!1)-t.y,0===a?0:1),t):t}}function Es(t,e,n){if(!e)return t;var i=Rs(2*t);return(i+Rs(e))%2==0?i/2:(i+(n?1:-1))/2}var zs=function(){this.x=0,this.y=0,this.width=0,this.height=0},Fs={},Vs=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultShape=function(){return new zs},n.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=Bs(Fs,e,this.style);n=a.x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a}else n=e.x,i=e.y,r=e.width,o=e.height;e.r?function(t,e){var n,i,r,o,a,s=e.x,l=e.y,u=e.width,h=e.height,c=e.r;u<0&&(s+=u,u=-u),h<0&&(l+=h,h=-h),"number"==typeof c?n=i=r=o=c:c instanceof Array?1===c.length?n=i=r=o=c[0]:2===c.length?(n=r=c[0],i=o=c[1]):3===c.length?(n=c[0],i=o=c[1],r=c[2]):(n=c[0],i=c[1],r=c[2],o=c[3]):n=i=r=o=0,n+i>u&&(n*=u/(a=n+i),i*=u/a),r+o>u&&(r*=u/(a=r+o),o*=u/a),i+r>h&&(i*=h/(a=i+r),r*=h/a),n+o>h&&(n*=h/(a=n+o),o*=h/a),t.moveTo(s+n,l),t.lineTo(s+u-i,l),0!==i&&t.arc(s+u-i,l+i,i,-Math.PI/2,0),t.lineTo(s+u,l+h-r),0!==r&&t.arc(s+u-r,l+h-r,r,0,Math.PI/2),t.lineTo(s+o,l+h),0!==o&&t.arc(s+o,l+h-o,o,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}(t,e):t.rect(n,i,r,o)},n.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},n}(Cs);Vs.prototype.type="rect";const Hs=Vs;var Ws={fill:"#000"},Gs={style:D({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},xa.style)},Us=function(t){function n(e){var n=t.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=Ws,n.attr(e),n}return e(n,t),n.prototype.childrenRef=function(){return this._children},n.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var n=this._children[e];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},n.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},n.prototype.getLocalTransform=function(e){var n=this.innerTransformable;return n?n.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},n.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},n.prototype._updateSubTexts=function(){var t;this._childCursor=0,$s(t=this.style),O(t.rich,$s),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},n.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=e},n.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},n.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new Ie(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),s=o.getLocalTransform(n);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},n.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Ws},n.prototype.setTextContent=function(t){},n.prototype._mergeStyle=function(t,e){if(!e)return t;var n=e.rich,i=t.rich||n&&{};return k(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i),t},n.prototype._mergeRich=function(t,e){for(var n=z(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},k(t[r],e[r])}},n.prototype.getAnimationStyleProps=function(){return Gs},n.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},n.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||a,n=t.padding,i=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=wr(o),l=tt(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=!1,p=e.width,d=(n=null==p||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?va(t,e.font,p,"breakAll"===i,0).lines:[]).length*l,f=tt(e.height,d);if(d>f&&h){var g=Math.floor(f/l);c=c||n.length>g,n=n.slice(0,g)}if(t&&a&&null!=p)for(var y=la(p,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),v={},m=0;m<n.length;m++)ua(v,n[m],y),n[m]=v.textLine,c=c||v.isTruncated;var _=f,x=0;for(m=0;m<n.length;m++)x=Math.max(yr(n[m],o),x);null==p&&(p=x);var w=x;return r&&(_+=r[0]+r[2],w+=r[1]+r[3],p+=r[1]+r[3]),u&&(w=p),{lines:n,height:f,outerWidth:w,outerHeight:_,lineHeight:l,calculatedLineHeight:s,contentWidth:x,contentHeight:d,width:p,isTruncated:c}}(el(t),t),r=nl(t),o=!!t.backgroundColor,s=i.outerHeight,l=i.outerWidth,u=i.contentWidth,h=i.lines,c=i.lineHeight,p=this._defaultStyle;this.isTruncated=!!i.isTruncated;var d=t.x||0,f=t.y||0,g=t.align||p.align||"left",y=t.verticalAlign||p.verticalAlign||"top",v=d,m=xr(f,i.contentHeight,y);if(r||n){var _=_r(d,l,g),x=xr(f,s,y);r&&this._renderBackground(t,t,_,x,l,s)}m+=c/2,n&&(v=tl(d,g,n),"top"===y?m+=n[0]:"bottom"===y&&(m-=n[2]));for(var w=0,b=!1,S=(Js("fill"in t?t.fill:(b=!0,p.fill))),M=(Qs("stroke"in t?t.stroke:o||p.autoStroke&&!b?null:(w=2,p.stroke))),T=t.textShadowBlur>0,C=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),I=i.calculatedLineHeight,k=0;k<h.length;k++){var D=this._getOrCreateChild(Ds),A=D.createStyle();D.useStyle(A),A.text=h[k],A.x=v,A.y=m,g&&(A.textAlign=g),A.textBaseline="middle",A.opacity=t.opacity,A.strokeFirst=!0,T&&(A.shadowBlur=t.textShadowBlur||0,A.shadowColor=t.textShadowColor||"transparent",A.shadowOffsetX=t.textShadowOffsetX||0,A.shadowOffsetY=t.textShadowOffsetY||0),A.stroke=M,A.fill=S,M&&(A.lineWidth=t.lineWidth||w,A.lineDash=t.lineDash,A.lineDashOffset=t.lineDashOffset||0),A.font=e,js(A,t),m+=c,C&&D.setBoundingRect(new Ie(_r(A.x,u,A.textAlign),xr(A.y,I,A.textBaseline),u,I))}},n.prototype._updateRichTexts=function(){var t=this.style,e=function(t,e){var n=new da;if(null!=t&&(t+=""),!t)return n;for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=aa.lastIndex=0;null!=(i=aa.exec(t));){var u=i.index;u>l&&fa(n,t.substring(l,u),e,s),fa(n,i[2],e,s,i[1]),l=aa.lastIndex}l<t.length&&fa(n,t.substring(l,t.length),e,s);var h=[],c=0,p=0,d=e.padding,f="truncate"===a,g="truncate"===e.lineOverflow,y={};function v(t,e,n){t.width=e,t.lineHeight=n,c+=n,p=Math.max(p,e)}t:for(var m=0;m<n.lines.length;m++){for(var _=n.lines[m],x=0,w=0,b=0;b<_.tokens.length;b++){var S=(R=_.tokens[b]).styleName&&e.rich[R.styleName]||{},M=R.textPadding=S.padding,T=M?M[1]+M[3]:0,C=R.font=S.font||e.font;R.contentHeight=wr(C);var I=tt(S.height,R.contentHeight);if(R.innerHeight=I,M&&(I+=M[0]+M[2]),R.height=I,R.lineHeight=et(S.lineHeight,e.lineHeight,I),R.align=S&&S.align||e.align,R.verticalAlign=S&&S.verticalAlign||"middle",g&&null!=o&&c+R.lineHeight>o){var k=n.lines.length;b>0?(_.tokens=_.tokens.slice(0,b),v(_,w,x),n.lines=n.lines.slice(0,m+1)):n.lines=n.lines.slice(0,m),n.isTruncated=n.isTruncated||n.lines.length<k;break t}var D=S.width,A=null==D||"auto"===D;if("string"==typeof D&&"%"===D.charAt(D.length-1))R.percentWidth=D,h.push(R),R.contentWidth=yr(R.text,C);else{if(A){var P=S.backgroundColor,L=P&&P.image;L&&oa(L=na(L))&&(R.width=Math.max(R.width,L.width*I/L.height))}var O=f&&null!=r?r-w:null;null!=O&&O<R.width?!A||O<T?(R.text="",R.width=R.contentWidth=0):(sa(y,R.text,O-T,C,e.ellipsis,{minChar:e.truncateMinChar}),R.text=y.text,n.isTruncated=n.isTruncated||y.isTruncated,R.width=R.contentWidth=yr(R.text,C)):R.contentWidth=yr(R.text,C)}R.width+=T,w+=R.width,S&&(x=Math.max(x,R.lineHeight))}v(_,w,x)}for(n.outerWidth=n.width=tt(r,p),n.outerHeight=n.height=tt(o,c),n.contentHeight=c,n.contentWidth=p,d&&(n.outerWidth+=d[1]+d[3],n.outerHeight+=d[0]+d[2]),m=0;m<h.length;m++){var R,N=(R=h[m]).percentWidth;R.width=parseInt(N,10)/100*n.width}return n}(el(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,h=t.verticalAlign||l.verticalAlign;this.isTruncated=!!e.isTruncated;var c=_r(a,i,u),p=xr(s,r,h),d=c,f=p;o&&(d+=o[3],f+=o[0]);var g=d+n;nl(t)&&this._renderBackground(t,t,c,p,i,r);for(var y=!!t.backgroundColor,v=0;v<e.lines.length;v++){for(var m=e.lines[v],_=m.tokens,x=_.length,w=m.lineHeight,b=m.width,S=0,M=d,T=g,C=x-1,I=void 0;S<x&&(!(I=_[S]).align||"left"===I.align);)this._placeToken(I,t,w,f,M,"left",y),b-=I.width,M+=I.width,S++;for(;C>=0&&"right"===(I=_[C]).align;)this._placeToken(I,t,w,f,T,"right",y),b-=I.width,T-=I.width,C--;for(M+=(n-(M-d)-(g-T)-b)/2;S<=C;)I=_[S],this._placeToken(I,t,w,f,M+I.width/2,"center",y),M+=I.width,S++;f+=w}},n.prototype._placeToken=function(t,e,n,i,r,o,s){var l=e.rich[t.styleName]||{};l.text=t.text;var u=t.verticalAlign,h=i+n/2;"top"===u?h=i+t.height/2:"bottom"===u&&(h=i+n-t.height/2),!t.isLineHolder&&nl(l)&&this._renderBackground(l,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,h-t.height/2,t.width,t.height);var c=!!l.backgroundColor,p=t.textPadding;p&&(r=tl(r,o,p),h-=t.height/2-p[0]-t.innerHeight/2);var d=this._getOrCreateChild(Ds),f=d.createStyle();d.useStyle(f);var g=this._defaultStyle,y=!1,v=0,m=Js("fill"in l?l.fill:"fill"in e?e.fill:(y=!0,g.fill)),_=Qs("stroke"in l?l.stroke:"stroke"in e?e.stroke:c||s||g.autoStroke&&!y?null:(v=2,g.stroke)),x=l.textShadowBlur>0||e.textShadowBlur>0;f.text=t.text,f.x=r,f.y=h,x&&(f.shadowBlur=l.textShadowBlur||e.textShadowBlur||0,f.shadowColor=l.textShadowColor||e.textShadowColor||"transparent",f.shadowOffsetX=l.textShadowOffsetX||e.textShadowOffsetX||0,f.shadowOffsetY=l.textShadowOffsetY||e.textShadowOffsetY||0),f.textAlign=o,f.textBaseline="middle",f.font=t.font||a,f.opacity=et(l.opacity,e.opacity,1),js(f,l),_&&(f.lineWidth=et(l.lineWidth,e.lineWidth,v),f.lineDash=tt(l.lineDash,e.lineDash),f.lineDashOffset=e.lineDashOffset||0,f.stroke=_),m&&(f.fill=m);var w=t.contentWidth,b=t.contentHeight;d.setBoundingRect(new Ie(_r(f.x,w,f.textAlign),xr(f.y,b,f.textBaseline),w,b))},n.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u=t.backgroundColor,h=t.borderWidth,c=t.borderColor,p=u&&u.image,d=u&&!p,f=t.borderRadius,g=this;if(d||t.lineHeight||h&&c){(a=this._getOrCreateChild(Hs)).useStyle(a.createStyle()),a.style.fill=null;var y=a.shape;y.x=n,y.y=i,y.width=r,y.height=o,y.r=f,a.dirtyShape()}if(d)(l=a.style).fill=u||null,l.fillOpacity=tt(t.fillOpacity,1);else if(p){(s=this._getOrCreateChild(Os)).onload=function(){g.dirtyStyle()};var v=s.style;v.image=u.image,v.x=n,v.y=i,v.width=r,v.height=o}h&&c&&((l=a.style).lineWidth=h,l.stroke=c,l.strokeOpacity=tt(t.strokeOpacity,1),l.lineDash=t.borderDash,l.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(l.strokeFirst=!0,l.lineWidth*=2));var m=(a||s).style;m.shadowBlur=t.shadowBlur||0,m.shadowColor=t.shadowColor||"transparent",m.shadowOffsetX=t.shadowOffsetX||0,m.shadowOffsetY=t.shadowOffsetY||0,m.opacity=et(t.opacity,e.opacity,1)},n.makeFont=function(t){var e="";return Ks(t)&&(e=[t.fontStyle,t.fontWeight,Zs(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&ot(e)||t.textFont||t.font},n}(Ca),Xs={left:!0,right:1,center:1},Ys={top:1,bottom:1,middle:1},qs=["fontStyle","fontWeight","fontSize","fontFamily"];function Zs(t){return"string"!=typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?"12px":t+"px":t}function js(t,e){for(var n=0;n<qs.length;n++){var i=qs[n],r=e[i];null!=r&&(t[i]=r)}}function Ks(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function $s(t){if(t){t.font=Us.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||Xs[e]?e:"left";var n=t.verticalAlign;"center"===n&&(n="middle"),t.verticalAlign=null==n||Ys[n]?n:"top",t.padding&&(t.padding=it(t.padding))}}function Qs(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Js(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function tl(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function el(t){var e=t.text;return null!=e&&(e+=""),e}function nl(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}const il=Us;var rl=Oo(),ol=function(t,e,n,i){if(i){var r=rl(i);r.dataIndex=n,r.dataType=e,r.seriesIndex=t,r.ssrType="chart","group"===i.type&&i.traverse((function(i){var r=rl(i);r.seriesIndex=t,r.dataIndex=n,r.dataType=e,r.ssrType="chart"}))}},al=1,sl={},ll=Oo(),ul=Oo(),hl=1,cl=["emphasis","blur","select"],pl=["normal","emphasis","blur","select"],dl=10,fl="highlight",gl="downplay",yl="select",vl="unselect",ml="toggleSelect";function _l(t){return null!=t&&"none"!==t}function xl(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function wl(t){xl(t,"emphasis",2)}function bl(t){2===t.hoverState&&xl(t,"normal",0)}function Sl(t){xl(t,"blur",1)}function Ml(t){1===t.hoverState&&xl(t,"normal",0)}function Tl(t){t.selected=!0}function Cl(t){t.selected=!1}function Il(t,e,n){e(t,n)}function kl(t,e,n){Il(t,e,n),t.isGroup&&t.traverse((function(t){Il(t,e,n)}))}function Dl(t,e){switch(e){case"emphasis":t.hoverState=2;break;case"normal":t.hoverState=0;break;case"blur":t.hoverState=1;break;case"select":t.selected=!0}}function Al(t,e){var n=this.states[t];if(this.style){if("emphasis"===t)return function(t,e,n,i){var r=n&&A(n,"select")>=0,o=!1;if(t instanceof Cs){var a=ll(t),s=r&&a.selectFill||a.normalFill,l=r&&a.selectStroke||a.normalStroke;if(_l(s)||_l(l)){var u=(i=i||{}).style||{};"inherit"===u.fill?(o=!0,i=k({},i),(u=k({},u)).fill=s):!_l(u.fill)&&_l(s)?(o=!0,i=k({},i),(u=k({},u)).fill=$n(s)):!_l(u.stroke)&&_l(l)&&(o||(i=k({},i),u=k({},u)),u.stroke=$n(l)),i.style=u}}if(i&&null==i.z2){o||(i=k({},i));var h=t.z2EmphasisLift;i.z2=t.z2+(null!=h?h:10)}return i}(this,0,e,n);if("blur"===t)return function(t,e,n){var i=A(t.currentStates,e)>=0,r=t.style.opacity,o=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),a=(n=n||{}).style||{};return null==a.opacity&&(n=k({},n),a=k({opacity:i?r:.1*o.opacity},a),n.style=a),n}(this,t,n);if("select"===t)return function(t,e,n){if(n&&null==n.z2){n=k({},n);var i=t.z2SelectLift;n.z2=t.z2+(null!=i?i:9)}return n}(this,0,n)}return n}function Pl(t){t.stateProxy=Al;var e=t.getTextContent(),n=t.getTextGuideLine();e&&(e.stateProxy=Al),n&&(n.stateProxy=Al)}function Ll(t,e){!Vl(t,e)&&!t.__highByOuter&&kl(t,wl)}function Ol(t,e){!Vl(t,e)&&!t.__highByOuter&&kl(t,bl)}function Rl(t,e){t.__highByOuter|=1<<(e||0),kl(t,wl)}function Nl(t,e){!(t.__highByOuter&=~(1<<(e||0)))&&kl(t,bl)}function Bl(t){kl(t,Sl)}function El(t){kl(t,Ml)}function zl(t){kl(t,Tl)}function Fl(t){kl(t,Cl)}function Vl(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Hl(t){var e=t.getModel(),n=[],i=[];e.eachComponent((function(e,r){var o=ul(r),a="series"===e,s=a?t.getViewOfSeriesModel(r):t.getViewOfComponentModel(r);!a&&i.push(s),o.isBlured&&(s.group.traverse((function(t){Ml(t)})),a&&n.push(r)),o.isBlured=!1})),O(i,(function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(n,!1,e)}))}function Wl(t,e,n,i){var r=i.getModel();function o(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&El(i)}}if(n=n||"coordinateSystem",null!=t&&e&&"none"!==e){var a=r.getSeriesByIndex(t),s=a.coordinateSystem;s&&s.master&&(s=s.master);var l=[];r.eachSeries((function(t){var r=a===t,u=t.coordinateSystem;if(u&&u.master&&(u=u.master),!("series"===n&&!r||"coordinateSystem"===n&&!(u&&s?u===s:r)||"series"===e&&r)){if(i.getViewOfSeriesModel(t).group.traverse((function(t){t.__highByOuter&&r&&"self"===e||Sl(t)})),L(e))o(t.getData(),e);else if(Y(e))for(var h=z(e),c=0;c<h.length;c++)o(t.getData(h[c]),e[h[c]]);l.push(t),ul(t).isBlured=!0}})),r.eachComponent((function(t,e){if("series"!==t){var n=i.getViewOfComponentModel(e);n&&n.toggleBlurSeries&&n.toggleBlurSeries(l,!0,r)}}))}}function Gl(t,e,n){if(null!=t&&null!=e){var i=n.getModel().getComponent(t,e);if(i){ul(i).isBlured=!0;var r=n.getViewOfComponentModel(i);r&&r.focusBlurEnabled&&r.group.traverse((function(t){Sl(t)}))}}}function Ul(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;var o=i.getModel().getComponent(t,e);if(!o)return r;var a=i.getViewOfComponentModel(o);if(!a||!a.findHighDownDispatchers)return r;for(var s,l=a.findHighDownDispatchers(n),u=0;u<l.length;u++)if("self"===rl(l[u]).focus){s=!0;break}return{focusSelf:s,dispatchers:l}}function Xl(t){O(t.getAllData(),(function(e){var n=e.data,i=e.type;n.eachItemGraphicEl((function(e,n){t.isSelected(n,i)?zl(e):Fl(e)}))}))}function Yl(t){var e=[];return t.eachSeries((function(t){O(t.getAllData(),(function(n){n.data;var i=n.type,r=t.getSelectedDataIndices();if(r.length>0){var o={dataIndex:r,seriesIndex:t.seriesIndex};null!=i&&(o.dataType=i),e.push(o)}}))})),e}function ql(t,e,n){Jl(t,!0),kl(t,Pl),jl(t,e,n)}function Zl(t,e,n,i){i?function(t){Jl(t,!1)}(t):ql(t,e,n)}function jl(t,e,n){var i=rl(t);null!=e?(i.focus=e,i.blurScope=n):i.focus&&(i.focus=null)}var Kl=["emphasis","blur","select"],$l={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Ql(t,e,n,i){n=n||"itemStyle";for(var r=0;r<Kl.length;r++){var o=Kl[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[$l[n]]()}}function Jl(t,e){var n=!1===e,i=t;t.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=t.highDownSilentOnTouch),n&&!i.__highDownDispatcher||(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!n)}function tu(t){return!(!t||!t.__highDownDispatcher)}function eu(t,e,n){var i=rl(t);i.componentMainType=e.mainType,i.componentIndex=e.componentIndex,i.componentHighDownName=n}function nu(t){var e=t.type;return e===yl||e===vl||e===ml}function iu(t){var e=t.type;return e===fl||e===gl}var ru=as.CMD,ou=[[],[],[]],au=Math.sqrt,su=Math.atan2;function lu(t,e){if(e){var n,i,r,o,a,s,l=t.data,u=t.len(),h=ru.M,c=ru.C,p=ru.L,d=ru.R,f=ru.A,g=ru.Q;for(r=0,o=0;r<u;){switch(n=l[r++],o=r,i=0,n){case h:case p:i=1;break;case c:i=3;break;case g:i=2;break;case f:var y=e[4],v=e[5],m=au(e[0]*e[0]+e[1]*e[1]),_=au(e[2]*e[2]+e[3]*e[3]),x=su(-e[1]/_,e[0]/m);l[r]*=m,l[r++]+=y,l[r]*=_,l[r++]+=v,l[r++]*=m,l[r++]*=_,l[r++]+=x,l[r++]+=x,o=r+=2;break;case d:s[0]=l[r++],s[1]=l[r++],Ot(s,s,e),l[o++]=s[0],l[o++]=s[1],s[0]+=l[r++],s[1]+=l[r++],Ot(s,s,e),l[o++]=s[0],l[o++]=s[1]}for(a=0;a<i;a++){var w=ou[a];w[0]=l[r++],w[1]=l[r++],Ot(w,w,e),l[o++]=w[0],l[o++]=w[1]}}t.increaseVersion()}}var uu=Math.sqrt,hu=Math.sin,cu=Math.cos,pu=Math.PI;function du(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function fu(t,e){return(t[0]*e[0]+t[1]*e[1])/(du(t)*du(e))}function gu(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(fu(t,e))}function yu(t,e,n,i,r,o,a,s,l,u,h){var c=l*(pu/180),p=cu(c)*(t-n)/2+hu(c)*(e-i)/2,d=-1*hu(c)*(t-n)/2+cu(c)*(e-i)/2,f=p*p/(a*a)+d*d/(s*s);f>1&&(a*=uu(f),s*=uu(f));var g=(r===o?-1:1)*uu((a*a*(s*s)-a*a*(d*d)-s*s*(p*p))/(a*a*(d*d)+s*s*(p*p)))||0,y=g*a*d/s,v=g*-s*p/a,m=(t+n)/2+cu(c)*y-hu(c)*v,_=(e+i)/2+hu(c)*y+cu(c)*v,x=gu([1,0],[(p-y)/a,(d-v)/s]),w=[(p-y)/a,(d-v)/s],b=[(-1*p-y)/a,(-1*d-v)/s],S=gu(w,b);if(fu(w,b)<=-1&&(S=pu),fu(w,b)>=1&&(S=0),S<0){var M=Math.round(S/pu*1e6)/1e6;S=2*pu+M%2*pu}h.addData(u,m,_,a,s,x,S,c,o)}var vu=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,mu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var _u=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.applyTransform=function(t){},n}(Cs);function xu(t){return null!=t.setData}function wu(t,e){var n=function(t){var e=new as;if(!t)return e;var n,i=0,r=0,o=i,a=r,s=as.CMD,l=t.match(vu);if(!l)return e;for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,d=h.match(mu)||[],f=d.length,g=0;g<f;g++)d[g]=parseFloat(d[g]);for(var y=0;y<f;){var v=void 0,m=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,I=void 0;switch(c){case"l":i+=d[y++],r+=d[y++],p=s.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=d[y++],r=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=d[y++],p=s.L,e.addData(p,i,r);break;case"H":i=d[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=s.L,e.addData(p,i,r);break;case"V":r=d[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=s.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":v=i,m=r,C=e.len(),I=e.data,n===s.C&&(v+=i-I[C-4],m+=r-I[C-3]),p=s.C,M=d[y++],T=d[y++],i=d[y++],r=d[y++],e.addData(p,v,m,M,T,i,r);break;case"s":v=i,m=r,C=e.len(),I=e.data,n===s.C&&(v+=i-I[C-4],m+=r-I[C-3]),p=s.C,M=i+d[y++],T=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,v,m,M,T,i,r);break;case"Q":M=d[y++],T=d[y++],i=d[y++],r=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=d[y++]+i,T=d[y++]+r,i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":v=i,m=r,C=e.len(),I=e.data,n===s.Q&&(v+=i-I[C-4],m+=r-I[C-3]),i=d[y++],r=d[y++],p=s.Q,e.addData(p,v,m,i,r);break;case"t":v=i,m=r,C=e.len(),I=e.data,n===s.Q&&(v+=i-I[C-4],m+=r-I[C-3]),i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,v,m,i,r);break;case"A":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],yu(M=i,T=r,i=d[y++],r=d[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],yu(M=i,T=r,i+=d[y++],r+=d[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}return e.toStatic(),e}(t),i=k({},e);return i.buildPath=function(t){if(xu(t)){t.setData(n.data),(e=t.getContext())&&t.rebuildPath(e,1)}else{var e=t;n.rebuildPath(e,1)}},i.applyTransform=function(t){lu(n,t),this.dirtyShape()},i}function bu(t,e){return new _u(wu(t,e))}function Su(t,e){e=e||{};var n=new Cs;return t.shape&&n.setShape(t.shape),n.setStyle(t.style),e.bakeTransform?lu(n.path,t.getComputedTransform()):e.toLocal?n.setLocalTransform(t.getComputedTransform()):n.copyTransform(t),n.buildPath=t.buildPath,n.applyTransform=n.applyTransform,n.z=t.z,n.z2=t.z2,n.zlevel=t.zlevel,n}var Mu=function(){this.cx=0,this.cy=0,this.r=0},Tu=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultShape=function(){return new Mu},n.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},n}(Cs);Tu.prototype.type="circle";const Cu=Tu;var Iu=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},ku=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultShape=function(){return new Iu},n.prototype.buildPath=function(t,e){var n=.5522848,i=e.cx,r=e.cy,o=e.rx,a=e.ry,s=o*n,l=a*n;t.moveTo(i-o,r),t.bezierCurveTo(i-o,r-l,i-s,r-a,i,r-a),t.bezierCurveTo(i+s,r-a,i+o,r-l,i+o,r),t.bezierCurveTo(i+o,r+l,i+s,r+a,i,r+a),t.bezierCurveTo(i-s,r+a,i-o,r+l,i-o,r),t.closePath()},n}(Cs);ku.prototype.type="ellipse";const Du=ku;var Au=Math.PI,Pu=2*Au,Lu=Math.sin,Ou=Math.cos,Ru=Math.acos,Nu=Math.atan2,Bu=Math.abs,Eu=Math.sqrt,zu=Math.max,Fu=Math.min,Vu=1e-4;function Hu(t,e,n,i,r,o,a){var s=t-n,l=e-i,u=(a?o:-o)/Eu(s*s+l*l),h=u*l,c=-u*s,p=t+h,d=e+c,f=n+h,g=i+c,y=(p+f)/2,v=(d+g)/2,m=f-p,_=g-d,x=m*m+_*_,w=r-o,b=p*g-f*d,S=(_<0?-1:1)*Eu(zu(0,w*w*x-b*b)),M=(b*_-m*S)/x,T=(-b*m-_*S)/x,C=(b*_+m*S)/x,I=(-b*m+_*S)/x,k=M-y,D=T-v,A=C-y,P=I-v;return k*k+D*D>A*A+P*P&&(M=C,T=I),{cx:M,cy:T,x0:-h,y0:-c,x1:M*(r/w-1),y1:T*(r/w-1)}}function Wu(t,e){var n,i=zu(e.r,0),r=zu(e.r0||0,0),o=i>0;if(o||r>0){if(o||(i=r,r=0),r>i){var a=i;i=r,r=a}var s=e.startAngle,l=e.endAngle;if(!isNaN(s)&&!isNaN(l)){var u=e.cx,h=e.cy,c=!!e.clockwise,p=Bu(l-s),d=p>Pu&&p%Pu;if(d>Vu&&(p=d),i>Vu)if(p>Pu-Vu)t.moveTo(u+i*Ou(s),h+i*Lu(s)),t.arc(u,h,i,s,l,!c),r>Vu&&(t.moveTo(u+r*Ou(l),h+r*Lu(l)),t.arc(u,h,r,l,s,c));else{var f=void 0,g=void 0,y=void 0,v=void 0,m=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=void 0,T=void 0,C=void 0,I=void 0,k=void 0,D=void 0,A=i*Ou(s),P=i*Lu(s),L=r*Ou(l),O=r*Lu(l),R=p>Vu;if(R){var N=e.cornerRadius;N&&(f=(n=function(t){var e;if(H(t)){var n=t.length;if(!n)return t;e=1===n?[t[0],t[0],0,0]:2===n?[t[0],t[0],t[1],t[1]]:3===n?t.concat(t[2]):t}else e=[t,t,t,t];return e}(N))[0],g=n[1],y=n[2],v=n[3]);var B=Bu(i-r)/2;if(m=Fu(B,y),_=Fu(B,v),x=Fu(B,f),w=Fu(B,g),M=b=zu(m,_),T=S=zu(x,w),(b>Vu||S>Vu)&&(C=i*Ou(l),I=i*Lu(l),k=r*Ou(s),D=r*Lu(s),p<Au)){var E=function(t,e,n,i,r,o,a,s){var l=n-t,u=i-e,h=a-r,c=s-o,p=c*l-h*u;if(!(p*p<Vu))return[t+(p=(h*(e-o)-c*(t-r))/p)*l,e+p*u]}(A,P,k,D,C,I,L,O);if(E){var z=A-E[0],F=P-E[1],V=C-E[0],W=I-E[1],G=1/Lu(Ru((z*V+F*W)/(Eu(z*z+F*F)*Eu(V*V+W*W)))/2),U=Eu(E[0]*E[0]+E[1]*E[1]);M=Fu(b,(i-U)/(G+1)),T=Fu(S,(r-U)/(G-1))}}}if(R)if(M>Vu){var X=Fu(y,M),Y=Fu(v,M),q=Hu(k,D,A,P,i,X,c),Z=Hu(C,I,L,O,i,Y,c);t.moveTo(u+q.cx+q.x0,h+q.cy+q.y0),M<b&&X===Y?t.arc(u+q.cx,h+q.cy,M,Nu(q.y0,q.x0),Nu(Z.y0,Z.x0),!c):(X>0&&t.arc(u+q.cx,h+q.cy,X,Nu(q.y0,q.x0),Nu(q.y1,q.x1),!c),t.arc(u,h,i,Nu(q.cy+q.y1,q.cx+q.x1),Nu(Z.cy+Z.y1,Z.cx+Z.x1),!c),Y>0&&t.arc(u+Z.cx,h+Z.cy,Y,Nu(Z.y1,Z.x1),Nu(Z.y0,Z.x0),!c))}else t.moveTo(u+A,h+P),t.arc(u,h,i,s,l,!c);else t.moveTo(u+A,h+P);if(r>Vu&&R)if(T>Vu){X=Fu(f,T),q=Hu(L,O,C,I,r,-(Y=Fu(g,T)),c),Z=Hu(A,P,k,D,r,-X,c);t.lineTo(u+q.cx+q.x0,h+q.cy+q.y0),T<S&&X===Y?t.arc(u+q.cx,h+q.cy,T,Nu(q.y0,q.x0),Nu(Z.y0,Z.x0),!c):(Y>0&&t.arc(u+q.cx,h+q.cy,Y,Nu(q.y0,q.x0),Nu(q.y1,q.x1),!c),t.arc(u,h,r,Nu(q.cy+q.y1,q.cx+q.x1),Nu(Z.cy+Z.y1,Z.cx+Z.x1),c),X>0&&t.arc(u+Z.cx,h+Z.cy,X,Nu(Z.y1,Z.x1),Nu(Z.y0,Z.x0),!c))}else t.lineTo(u+L,h+O),t.arc(u,h,r,l,s,c);else t.lineTo(u+L,h+O)}else t.moveTo(u,h);t.closePath()}}}var Gu=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},Uu=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultShape=function(){return new Gu},n.prototype.buildPath=function(t,e){Wu(t,e)},n.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},n}(Cs);Uu.prototype.type="sector";const Xu=Uu;var Yu=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},qu=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultShape=function(){return new Yu},n.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},n}(Cs);qu.prototype.type="ring";const Zu=qu;function ju(t,e,n){var i=e.smooth,r=e.points;if(r&&r.length>=2){if(i){var o=function(t,e,n,i){var r,o,a,s,l=[],u=[],h=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var p=0,d=t.length;p<d;p++)Rt(a,a,t[p]),Nt(s,s,t[p]);Rt(a,a,i[0]),Nt(s,s,i[1])}for(p=0,d=t.length;p<d;p++){var f=t[p];if(n)r=t[p?p-1:d-1],o=t[(p+1)%d];else{if(0===p||p===d-1){l.push(wt(t[p]));continue}r=t[p-1],o=t[p+1]}Tt(u,o,r),It(u,u,e);var g=Dt(f,r),y=Dt(f,o),v=g+y;0!==v&&(g/=v,y/=v),It(h,u,-g),It(c,u,y);var m=St([],f,h),_=St([],f,c);i&&(Nt(m,m,a),Rt(m,m,s),Nt(_,_,a),Rt(_,_,s)),l.push(m),l.push(_)}return n&&l.push(l.shift()),l}(r,i,n,e.smoothConstraint);t.moveTo(r[0][0],r[0][1]);for(var a=r.length,s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}}else{t.moveTo(r[0][0],r[0][1]);s=1;for(var c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}var Ku=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},$u=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultShape=function(){return new Ku},n.prototype.buildPath=function(t,e){ju(t,e,!0)},n}(Cs);$u.prototype.type="polygon";const Qu=$u;var Ju=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},th=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},n.prototype.getDefaultShape=function(){return new Ju},n.prototype.buildPath=function(t,e){ju(t,e,!1)},n}(Cs);th.prototype.type="polyline";const eh=th;var nh={},ih=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},rh=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},n.prototype.getDefaultShape=function(){return new ih},n.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=Ns(nh,e,this.style);n=a.x1,i=a.y1,r=a.x2,o=a.y2}else n=e.x1,i=e.y1,r=e.x2,o=e.y2;var s=e.percent;0!==s&&(t.moveTo(n,i),s<1&&(r=n*(1-s)+r*s,o=i*(1-s)+o*s),t.lineTo(r,o))},n.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},n}(Cs);rh.prototype.type="line";const oh=rh;var ah=[],sh=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function lh(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?cn:hn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?cn:hn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?mn:vn)(t.x1,t.cpx1,t.x2,e),(n?mn:vn)(t.y1,t.cpy1,t.y2,e)]}var uh=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},n.prototype.getDefaultShape=function(){return new sh},n.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,h=e.percent;0!==h&&(t.moveTo(n,i),null==l||null==u?(h<1&&(xn(n,a,r,h,ah),a=ah[1],r=ah[2],xn(i,s,o,h,ah),s=ah[1],o=ah[2]),t.quadraticCurveTo(a,s,r,o)):(h<1&&(fn(n,a,l,r,h,ah),a=ah[1],l=ah[2],r=ah[3],fn(i,s,u,o,h,ah),s=ah[1],u=ah[2],o=ah[3]),t.bezierCurveTo(a,s,l,u,r,o)))},n.prototype.pointAt=function(t){return lh(this.shape,t,!1)},n.prototype.tangentAt=function(t){var e=lh(this.shape,t,!0);return kt(e,e)},n}(Cs);uh.prototype.type="bezier-curve";const hh=uh;var ch=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},ph=function(t){function n(e){return t.call(this,e)||this}return e(n,t),n.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},n.prototype.getDefaultShape=function(){return new ch},n.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,o,a,!s)},n}(Cs);ph.prototype.type="arc";const dh=ph;const fh=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="compound",e}return e(n,t),n.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},n.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},n.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},n.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},n.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),Cs.prototype.getBoundingRect.call(this)},n}(Cs);const gh=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}();const yh=function(t){function n(e,n,i,r,o,a){var s=t.call(this,o)||this;return s.x=null==e?0:e,s.y=null==n?0:n,s.x2=null==i?1:i,s.y2=null==r?0:r,s.type="linear",s.global=a||!1,s}return e(n,t),n}(gh);const vh=function(t){function n(e,n,i,r,o){var a=t.call(this,r)||this;return a.x=null==e?.5:e,a.y=null==n?.5:n,a.r=null==i?.5:i,a.type="radial",a.global=o||!1,a}return e(n,t),n}(gh);var mh=[0,0],_h=[0,0],xh=new ve,wh=new ve;const bh=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new ve;for(n=0;n<2;n++)this._axes[n]=new ve;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,s=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,s),n[3].set(r,s),e)for(var l=0;l<4;l++)n[l].transform(e);ve.sub(i[0],n[1],n[0]),ve.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(l=0;l<2;l++)this._origin[l]=i[l].dot(n[0])},t.prototype.intersect=function(t,e){var n=!0,i=!e;return xh.set(1/0,1/0),wh.set(0,0),!this._intersectCheckOneSide(this,t,xh,wh,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,xh,wh,i,-1)&&(n=!1,i)||i||ve.copy(e,n?xh:wh),n},t.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,mh),this._getProjMinMaxOnAxis(s,e._corners,_h),mh[1]<_h[0]||mh[0]>_h[1]){if(a=!1,r)return a;var u=Math.abs(_h[0]-mh[1]),h=Math.abs(mh[0]-_h[1]);Math.min(u,h)>i.len()&&(u<h?ve.scale(i,l,-u*o):ve.scale(i,l,h*o))}else if(n){u=Math.abs(_h[0]-mh[1]),h=Math.abs(mh[0]-_h[1]);Math.min(u,h)<n.len()&&(u<h?ve.scale(n,l,u*o):ve.scale(n,l,-h*o))}}return a},t.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++){var u=e[l].dot(i)+r[t];a=Math.min(u,a),s=Math.max(u,s)}n[0]=a,n[1]=s},t}();var Sh=[];const Mh=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return e(n,t),n.prototype.traverse=function(t,e){t.call(e,this)},n.prototype.useStyle=function(){this.style={}},n.prototype.getCursor=function(){return this._cursor},n.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},n.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},n.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},n.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},n.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},n.prototype.getDisplayables=function(){return this._displayables},n.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},n.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},n.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},n.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Ie(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(Sh)),t.union(i)}this._rect=t}return this._rect},n.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++){if(this._displayables[i].contain(t,e))return!0}return!1},n}(Ca);var Th=Oo();function Ch(t,e,n,i,r){var o;if(e&&e.ecModel){var a=e.ecModel.getUpdatePayload();o=a&&a.animation}var s="update"===t;if(e&&e.isAnimationEnabled()){var l=void 0,u=void 0,h=void 0;return i?(l=tt(i.duration,200),u=tt(i.easing,"cubicOut"),h=0):(l=e.getShallow(s?"animationDurationUpdate":"animationDuration"),u=e.getShallow(s?"animationEasingUpdate":"animationEasing"),h=e.getShallow(s?"animationDelayUpdate":"animationDelay")),o&&(null!=o.duration&&(l=o.duration),null!=o.easing&&(u=o.easing),null!=o.delay&&(h=o.delay)),W(h)&&(h=h(n,r)),W(l)&&(l=l(n)),{duration:l||0,delay:h,easing:u}}return null}function Ih(t,e,n,i,r,o,a){var s,l=!1;W(r)?(a=o,o=r,r=null):Y(r)&&(o=r.cb,a=r.during,l=r.isFrom,s=r.removeOpt,r=r.dataIndex);var u="leave"===t;u||e.stopAnimation("leave");var h=Ch(t,i,r,u?s||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null);if(h&&h.duration>0){var c={duration:h.duration,delay:h.delay||0,easing:h.easing,done:o,force:!!o||!!a,setToFinal:!u,scope:t,during:a};l?e.animateFrom(n,c):e.animateTo(n,c)}else e.stopAnimation(),!l&&e.attr(n),a&&a(1),o&&o()}function kh(t,e,n,i,r,o){Ih("update",t,e,n,i,r,o)}function Dh(t,e,n,i,r,o){Ih("enter",t,e,n,i,r,o)}function Ah(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++){if("leave"===t.animators[e].scope)return!0}return!1}function Ph(t,e,n,i,r,o){Ah(t)||Ih("leave",t,e,n,i,r,o)}function Lh(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),Ph(t,{style:{opacity:0}},e,n,i)}function Oh(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse((function(t){t.isGroup||Lh(t,e,n,i)})):Lh(t,e,n,i)}function Rh(t){Th(t).oldStyle=t.style}function Nh(t){return Th(t).oldStyle}var Bh=Math.max,Eh=Math.min,zh={};var Fh=function(t,n){var i=wu(t,n);return function(t){function n(e){var n=t.call(this,e)||this;return n.applyTransform=i.applyTransform,n.buildPath=i.buildPath,n}return e(n,t),n}(_u)};function Vh(t,e){zh[t]=e}function Hh(t){if(zh.hasOwnProperty(t))return zh[t]}function Wh(t,e,n,i){var r=bu(t,e);return n&&("center"===i&&(n=Uh(n,r.getBoundingRect())),Yh(r,n)),r}function Gh(t,e,n){var i=new Os({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(Uh(e,r))}}});return i}function Uh(t,e){var n,i=e.width/e.height,r=t.height*i;return n=r<=t.width?t.height:(r=t.width)/i,{x:t.x+t.width/2-r/2,y:t.y+t.height/2-n/2,width:r,height:n}}var Xh=function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}var a=new Cs(e);return a.createPathProxy(),a.buildPath=function(t){if(xu(t)){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e,1)}},a};function Yh(t,e){if(t.applyTransform){var n=t.getBoundingRect().calculateTransform(e);t.applyTransform(n)}}function qh(t,e){return Ns(t,t,{lineWidth:e}),t}var Zh=Es;function jh(t,e){for(var n=ue([]);t&&t!==e;)ce(n,t.getLocalTransform(),n),t=t.parent;return n}function Kh(t,e,n){return e&&!L(e)&&(e=fr.getLocalTransform(e)),n&&(e=ge([],e)),Ot([],t,e)}function $h(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return o=Kh(o,e,n),Math.abs(o[0])>Math.abs(o[1])?o[0]>0?"right":"left":o[1]>0?"bottom":"top"}function Qh(t){return!t.isGroup}function Jh(t,e,n){if(t&&e){var i,r=(i={},t.traverse((function(t){Qh(t)&&t.anid&&(i[t.anid]=t)})),i);e.traverse((function(t){if(Qh(t)&&t.anid){var e=r[t.anid];if(e){var i=o(t);t.attr(o(e)),kh(t,i,n,rl(t).dataIndex)}}}))}function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return function(t){return null!=t.shape}(t)&&(e.shape=k({},t.shape)),e}}function tc(t,e){return R(t,(function(t){var n=t[0];n=Bh(n,e.x),n=Eh(n,e.x+e.width);var i=t[1];return i=Bh(i,e.y),[n,i=Eh(i,e.y+e.height)]}))}function ec(t,e,n){var i=k({rectHover:!0},e),r=i.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(r.image=t.slice(8),D(r,n),new Os(i)):Wh(t.replace("path://",""),i,n,"center")}function nc(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(ic(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}}function ic(t,e,n,i,r,o,a,s){var l,u=n-t,h=i-e,c=a-r,p=s-o,d=rc(c,p,u,h);if((l=d)<=1e-6&&l>=-1e-6)return!1;var f=t-r,g=e-o,y=rc(f,g,u,h)/d;if(y<0||y>1)return!1;var v=rc(f,g,c,p)/d;return!(v<0||v>1)}function rc(t,e,n,i){return t*i-n*e}function oc(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,r=G(e)?{formatter:e}:e,o=n.mainType,a=n.componentIndex,s={componentType:o,name:i,$vars:["name"]};s[o+"Index"]=a;var l=t.formatterParamsExtra;l&&O(z(l),(function(t){yt(s,t)||(s[t]=l[t],s.$vars.push(t))}));var u=rl(t.el);u.componentMainType=o,u.componentIndex=a,u.tooltipConfig={name:i,option:D({content:i,encodeHTMLContent:!0,formatterParams:s},r)}}function ac(t,e){var n;t.isGroup&&(n=e(t)),n||t.traverse(e)}function sc(t,e){if(t)if(H(t))for(var n=0;n<t.length;n++)ac(t[n],e);else ac(t,e)}Vh("circle",Cu),Vh("ellipse",Du),Vh("sector",Xu),Vh("ring",Zu),Vh("polygon",Qu),Vh("polyline",eh),Vh("rect",Hs),Vh("line",oh),Vh("bezierCurve",hh),Vh("arc",dh);const lc=Object.freeze(Object.defineProperty({__proto__:null,Arc:dh,BezierCurve:hh,BoundingRect:Ie,Circle:Cu,CompoundPath:fh,Ellipse:Du,Group:Br,Image:Os,IncrementalDisplayable:Mh,Line:oh,LinearGradient:yh,OrientedBoundingRect:bh,Path:Cs,Point:ve,Polygon:Qu,Polyline:eh,RadialGradient:vh,Rect:Hs,Ring:Zu,Sector:Xu,Text:il,applyTransform:Kh,clipPointsByRect:tc,clipRectByRect:function(t,e){var n=Bh(t.x,e.x),i=Eh(t.x+t.width,e.x+e.width),r=Bh(t.y,e.y),o=Eh(t.y+t.height,e.y+e.height);if(i>=n&&o>=r)return{x:n,y:r,width:i-n,height:o-r}},createIcon:ec,extendPath:function(t,e){return Fh(t,e)},extendShape:function(t){return Cs.extend(t)},getShapeClass:Hh,getTransform:jh,groupTransition:Jh,initProps:Dh,isElementRemoved:Ah,lineLineIntersect:ic,linePolygonIntersect:nc,makeImage:Gh,makePath:Wh,mergePath:Xh,registerShape:Vh,removeElement:Ph,removeElementWithFadeOut:Oh,resizePath:Yh,setTooltipConfig:oc,subPixelOptimize:Zh,subPixelOptimizeLine:qh,subPixelOptimizeRect:function(t){return Bs(t.shape,t.shape,t.style),t},transformDirection:$h,traverseElements:sc,updateProps:kh},Symbol.toStringTag,{value:"Module"}));var uc={};function hc(t,e){for(var n=0;n<cl.length;n++){var i=cl[n],r=e[i],o=t.ensureState(i);o.style=o.style||{},o.style.text=r}var a=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(a,!0)}function cc(t,e,n){var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal;r&&(i=r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null)),null==i&&(i=W(t.defaultText)?t.defaultText(o,t,n):t.defaultText);for(var l={normal:i},u=0;u<cl.length;u++){var h=cl[u],c=e[h];l[h]=tt(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function pc(t,e,n,i){n=n||uc;for(var r=t instanceof il,o=!1,a=0;a<pl.length;a++){if((p=e[pl[a]])&&p.getShallow("show")){o=!0;break}}var s=r?t:t.getTextContent();if(o){r||(s||(s=new il,t.setTextContent(s)),t.stateProxy&&(s.stateProxy=t.stateProxy));var l=cc(n,e),u=e.normal,h=!!u.getShallow("show"),c=fc(u,i&&i.normal,n,!1,!r);c.text=l.normal,r||t.setTextConfig(gc(u,n,!1));for(a=0;a<cl.length;a++){var p,d=cl[a];if(p=e[d]){var f=s.ensureState(d),g=!!tt(p.getShallow("show"),h);if(g!==h&&(f.ignore=!g),f.style=fc(p,i&&i[d],n,!0,!r),f.style.text=l[d],!r)t.ensureState(d).textConfig=gc(p,n,!0)}}s.silent=!!u.getShallow("silent"),null!=s.style.x&&(c.x=s.style.x),null!=s.style.y&&(c.y=s.style.y),s.ignore=!h,s.useStyle(c),s.dirty(),n.enableTextSetter&&(wc(s).setLabelText=function(t){var i=cc(n,e,t);hc(s,i)})}else s&&(s.ignore=!0);t.dirty()}function dc(t,e){e=e||"label";for(var n={normal:t.getModel(e)},i=0;i<cl.length;i++){var r=cl[i];n[r]=t.getModel([r,e])}return n}function fc(t,e,n,i,r){var o={};return function(t,e,n,i,r){n=n||uc;var o,a=e.ecModel,s=a&&a.option.textStyle,l=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||uc).rich;if(n){e=e||{};for(var i=z(n),r=0;r<i.length;r++){e[i[r]]=1}}t=t.parentModel}return e}(e);if(l)for(var u in o={},l)if(l.hasOwnProperty(u)){var h=e.getModel(["rich",u]);_c(o[u]={},h,s,n,i,r,!1,!0)}o&&(t.rich=o);var c=e.get("overflow");c&&(t.overflow=c);var p=e.get("minMargin");null!=p&&(t.margin=p);_c(t,e,s,n,i,r,!0,!1)}(o,t,n,i,r),e&&k(o,e),o}function gc(t,e,n){e=e||{};var i,r={},o=t.getShallow("rotate"),a=tt(t.getShallow("distance"),n?null:5),s=t.getShallow("offset");return"outside"===(i=t.getShallow("position")||(n?null:"inside"))&&(i=e.defaultOutsidePosition||"top"),null!=i&&(r.position=i),null!=s&&(r.offset=s),null!=o&&(o*=Math.PI/180,r.rotation=o),null!=a&&(r.distance=a),r.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",r}var yc=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],vc=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],mc=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function _c(t,e,n,i,r,o,a,s){n=!r&&n||uc;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=tt(e.getShallow("opacity"),n.opacity);"inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h);var p=tt(e.getShallow("textBorderWidth"),n.textBorderWidth);null!=p&&(t.lineWidth=p);var d=tt(e.getShallow("textBorderType"),n.textBorderType);null!=d&&(t.lineDash=d);var f=tt(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset);null!=f&&(t.lineDashOffset=f),r||null!=c||s||(c=i&&i.defaultOpacity),null!=c&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var g=0;g<yc.length;g++){var y=yc[g];null!=(m=tt(e.getShallow(y),n[y]))&&(t[y]=m)}for(g=0;g<vc.length;g++){y=vc[g];null!=(m=e.getShallow(y))&&(t[y]=m)}if(null==t.verticalAlign){var v=e.getShallow("baseline");null!=v&&(t.verticalAlign=v)}if(!a||!i.disableBox){for(g=0;g<mc.length;g++){var m;y=mc[g];null!=(m=e.getShallow(y))&&(t[y]=m)}var _=e.getShallow("borderType");null!=_&&(t.borderDash=_),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}function xc(t,e){var n=e&&e.getModel("textStyle");return ot([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}var wc=Oo();function bc(t,e,n,i){if(t){var r=wc(t);r.prevValue=r.value,r.value=n;var o=e.normal;r.valueAnimation=o.get("valueAnimation"),r.valueAnimation&&(r.precision=o.get("precision"),r.defaultInterpolatedText=i,r.statesModels=e)}}function Sc(t,e,n,i,r){var o=wc(t);if(o.valueAnimation&&o.prevValue!==o.value){var a=o.defaultInterpolatedText,s=tt(o.interpolatedValue,o.prevValue),l=o.value;t.percent=0,(null==o.prevValue?Dh:kh)(t,{percent:1},i,e,null,(function(i){var u=Wo(n,o.precision,s,l,i);o.interpolatedValue=1===i?null:u;var h=cc({labelDataIndex:e,labelFetcher:r,defaultText:a?a(u):u+""},o.statesModels,u);hc(t,h)}))}}var Mc=["textStyle","color"],Tc=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],Cc=new il;const Ic=function(){function t(){}return t.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(Mc):null)},t.prototype.getFont=function(){return xc({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},t.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<Tc.length;n++)e[Tc[n]]=this.getShallow(Tc[n]);return Cc.useStyle(e),Cc.update(),Cc.getBoundingRect()},t}();var kc,Dc,Ac=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],Pc=Qo(Ac),Lc=function(){function t(){}return t.prototype.getLineStyle=function(t){return Pc(this,t)},t}(),Oc=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],Rc=Qo(Oc),Nc=function(){function t(){}return t.prototype.getItemStyle=function(t,e){return Rc(this,t,e)},t}(),Bc=function(){function t(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}return t.prototype.init=function(t,e,n){},t.prototype.mergeOption=function(t,e){C(this.option,t,!0)},t.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},t.prototype.getShallow=function(t,e){var n=this.option,i=null==n?n:n[t];if(null==i&&!e){var r=this.parentModel;r&&(i=r.getShallow(t))}return i},t.prototype.getModel=function(e,n){var i=null!=e,r=i?this.parsePath(e):null;return new t(i?this._doGet(r):this.option,n=n||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(r)),this.ecModel)},t.prototype.isEmpty=function(){return null==this.option},t.prototype.restoreData=function(){},t.prototype.clone=function(){return new(0,this.constructor)(T(this.option))},t.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},t.prototype.resolveParentPath=function(t){return t},t.prototype.isAnimationEnabled=function(){if(!r.node&&this.option){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},t.prototype._doGet=function(t,e){var n=this.option;if(!t)return n;for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);return null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel)),n},t}();Yo(Bc),kc=Bc,Dc=["__\0is_clz",Zo++].join("_"),kc.prototype[Dc]=!0,kc.isInstance=function(t){return!(!t||!t[Dc])},P(Bc,Lc),P(Bc,Nc),P(Bc,ta),P(Bc,Ic);const Ec=Bc;var zc=Math.round(10*Math.random());function Fc(t){return[t||"",zc++].join("_")}function Vc(t,e){return C(C({},t,!0),e,!0)}var Hc="ZH",Wc="EN",Gc=Wc,Uc={},Xc={},Yc=r.domSupported&&(document.documentElement.lang||navigator.language||navigator.browserLanguage||Gc).toUpperCase().indexOf(Hc)>-1?Hc:Gc;function qc(t,e){t=t.toUpperCase(),Xc[t]=new Ec(e),Uc[t]=e}function Zc(t){return Xc[t]}qc(Wc,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),qc(Hc,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var jc=1e3,Kc=6e4,$c=36e5,Qc=864e5,Jc=31536e6,tp={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},ep="{yyyy}-{MM}-{dd}",np={year:"{yyyy}",month:"{yyyy}-{MM}",day:ep,hour:ep+" "+tp.hour,minute:ep+" "+tp.minute,second:ep+" "+tp.second,millisecond:tp.none},ip=["year","month","day","hour","minute","second","millisecond"],rp=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function op(t,e){return"0000".substr(0,e-(t+="").length)+t}function ap(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function sp(t){return t===ap(t)}function lp(t,e,n,i){var r=eo(t),o=r[cp(n)](),a=r[pp(n)]()+1,s=Math.floor((a-1)/3)+1,l=r[dp(n)](),u=r["get"+(n?"UTC":"")+"Day"](),h=r[fp(n)](),c=(h-1)%12+1,p=r[gp(n)](),d=r[yp(n)](),f=r[vp(n)](),g=h>=12?"pm":"am",y=g.toUpperCase(),v=(i instanceof Ec?i:Zc(i||Yc)||Xc[Gc]).getModel("time"),m=v.get("month"),_=v.get("monthAbbr"),x=v.get("dayOfWeek"),w=v.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,g+"").replace(/{A}/g,y+"").replace(/{yyyy}/g,o+"").replace(/{yy}/g,op(o%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,m[a-1]).replace(/{MMM}/g,_[a-1]).replace(/{MM}/g,op(a,2)).replace(/{M}/g,a+"").replace(/{dd}/g,op(l,2)).replace(/{d}/g,l+"").replace(/{eeee}/g,x[u]).replace(/{ee}/g,w[u]).replace(/{e}/g,u+"").replace(/{HH}/g,op(h,2)).replace(/{H}/g,h+"").replace(/{hh}/g,op(c+"",2)).replace(/{h}/g,c+"").replace(/{mm}/g,op(p,2)).replace(/{m}/g,p+"").replace(/{ss}/g,op(d,2)).replace(/{s}/g,d+"").replace(/{SSS}/g,op(f,3)).replace(/{S}/g,f+"")}function up(t,e){var n=eo(t),i=n[pp(e)]()+1,r=n[dp(e)](),o=n[fp(e)](),a=n[gp(e)](),s=n[yp(e)](),l=0===n[vp(e)](),u=l&&0===s,h=u&&0===a,c=h&&0===o,p=c&&1===r;return p&&1===i?"year":p?"month":c?"day":h?"hour":u?"minute":l?"second":"millisecond"}function hp(t,e,n){var i=X(t)?eo(t):t;switch(e=e||up(t,n)){case"year":return i[cp(n)]();case"half-year":return i[pp(n)]()>=6?1:0;case"quarter":return Math.floor((i[pp(n)]()+1)/4);case"month":return i[pp(n)]();case"day":return i[dp(n)]();case"half-day":return i[fp(n)]()/24;case"hour":return i[fp(n)]();case"minute":return i[gp(n)]();case"second":return i[yp(n)]();case"millisecond":return i[vp(n)]()}}function cp(t){return t?"getUTCFullYear":"getFullYear"}function pp(t){return t?"getUTCMonth":"getMonth"}function dp(t){return t?"getUTCDate":"getDate"}function fp(t){return t?"getUTCHours":"getHours"}function gp(t){return t?"getUTCMinutes":"getMinutes"}function yp(t){return t?"getUTCSeconds":"getSeconds"}function vp(t){return t?"getUTCMilliseconds":"getMilliseconds"}function mp(t){return t?"setUTCFullYear":"setFullYear"}function _p(t){return t?"setUTCMonth":"setMonth"}function xp(t){return t?"setUTCDate":"setDate"}function wp(t){return t?"setUTCHours":"setHours"}function bp(t){return t?"setUTCMinutes":"setMinutes"}function Sp(t){return t?"setUTCSeconds":"setSeconds"}function Mp(t){return t?"setUTCMilliseconds":"setMilliseconds"}function Tp(t){if(!so(t))return G(t)?t:"-";var e=(t+"").split(".");return e[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(e.length>1?"."+e[1]:"")}function Cp(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,(function(t,e){return e.toUpperCase()})),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}var Ip=it;function kp(t,e,n){function i(t){return t&&ot(t)?t:"-"}function r(t){return!(null==t||isNaN(t)||!isFinite(t))}var o="time"===e,a=t instanceof Date;if(o||a){var s=o?eo(t):t;if(!isNaN(+s))return lp(s,"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}",n);if(a)return"-"}if("ordinal"===e)return U(t)?i(t):X(t)&&r(t)?t+"":"-";var l=ao(t);return r(l)?Tp(l):U(t)?i(t):"boolean"==typeof t?t+"":"-"}var Dp=["a","b","c","d","e","f","g"],Ap=function(t,e){return"{"+t+(null==e?"":e)+"}"};function Pp(t,e,n){H(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=Dp[o];t=t.replace(Ap(a),Ap(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(Ap(Dp[l],s),n?Zt(u):u)}return t}function Lp(t,e,n){return O(e,(function(e,i){t=t.replace("{"+i+"}",n?Zt(e):e)})),t}function Op(t,e){return e=e||"transparent",G(t)?t:Y(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}function Rp(t,e){if("_blank"===e||"blank"===e){var n=window.open();n.opener=null,n.location.href=t}else window.open(t,e)}var Np=O,Bp=["left","right","top","bottom","width","height"],Ep=[["width","left","right"],["height","top","bottom"]];function zp(t,e,n,i,r){var o=0,a=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild((function(l,u){var h,c,p=l.getBoundingRect(),d=e.childAt(u+1),f=d&&d.getBoundingRect();if("horizontal"===t){var g=p.width+(f?-f.x+p.x:0);(h=o+g)>i||l.newline?(o=0,h=g,a+=s+n,s=p.height):s=Math.max(s,p.height)}else{var y=p.height+(f?-f.y+p.y:0);(c=a+y)>r||l.newline?(o+=s+n,a=0,c=y,s=p.width):s=Math.max(s,p.width)}l.newline||(l.x=o,l.y=a,l.markRedraw(),"horizontal"===t?o=h+n:a=c+n)}))}var Fp=zp;function Vp(t,e,n){var i=e.width,r=e.height,o=Xr(t.left,i),a=Xr(t.top,r),s=Xr(t.right,i),l=Xr(t.bottom,r);return(isNaN(o)||isNaN(parseFloat(t.left)))&&(o=0),(isNaN(s)||isNaN(parseFloat(t.right)))&&(s=i),(isNaN(a)||isNaN(parseFloat(t.top)))&&(a=0),(isNaN(l)||isNaN(parseFloat(t.bottom)))&&(l=r),n=Ip(n||0),{width:Math.max(s-o-n[1]-n[3],0),height:Math.max(l-a-n[0]-n[2],0)}}function Hp(t,e,n){n=Ip(n||0);var i=e.width,r=e.height,o=Xr(t.left,i),a=Xr(t.top,r),s=Xr(t.right,i),l=Xr(t.bottom,r),u=Xr(t.width,i),h=Xr(t.height,r),c=n[2]+n[0],p=n[1]+n[3],d=t.aspect;switch(isNaN(u)&&(u=i-s-p-o),isNaN(h)&&(h=r-l-c-a),null!=d&&(isNaN(u)&&isNaN(h)&&(d>i/r?u=.8*i:h=.8*r),isNaN(u)&&(u=d*h),isNaN(h)&&(h=u/d)),isNaN(o)&&(o=i-s-u-p),isNaN(a)&&(a=r-l-h-c),t.left||t.right){case"center":o=i/2-u/2-n[3];break;case"right":o=i-u-p}switch(t.top||t.bottom){case"middle":case"center":a=r/2-h/2-n[0];break;case"bottom":a=r-h-c}o=o||0,a=a||0,isNaN(u)&&(u=i-p-o-(s||0)),isNaN(h)&&(h=r-c-a-(l||0));var f=new Ie(o+n[3],a+n[0],u,h);return f.margin=n,f}function Wp(t,e,n,i,r,o){var a,s=!r||!r.hv||r.hv[0],l=!r||!r.hv||r.hv[1],u=r&&r.boundingMode||"all";if((o=o||t).x=t.x,o.y=t.y,!s&&!l)return!1;if("raw"===u)a="group"===t.type?new Ie(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(a=t.getBoundingRect(),t.needLocalTransform()){var h=t.getLocalTransform();(a=a.clone()).applyTransform(h)}var c=Hp(D({width:a.width,height:a.height},e),n,i),p=s?c.x-a.x:0,d=l?c.y-a.y:0;return"raw"===u?(o.x=p,o.y=d):(o.x+=p,o.y+=d),o===t&&t.markRedraw(),!0}function Gp(t,e){return null!=t[Ep[e][0]]||null!=t[Ep[e][1]]&&null!=t[Ep[e][2]]}function Up(t){var e=t.layoutMode||t.constructor.layoutMode;return Y(e)?e:e?{type:e}:null}function Xp(t,e,n){var i=n&&n.ignoreSize;!H(i)&&(i=[i,i]);var r=a(Ep[0],0),o=a(Ep[1],1);function a(n,r){var o={},a=0,u={},h=0;if(Np(n,(function(e){u[e]=t[e]})),Np(n,(function(t){s(e,t)&&(o[t]=u[t]=e[t]),l(o,t)&&a++,l(u,t)&&h++})),i[r])return l(e,n[1])?u[n[2]]=null:l(e,n[2])&&(u[n[1]]=null),u;if(2!==h&&a){if(a>=2)return o;for(var c=0;c<n.length;c++){var p=n[c];if(!s(o,p)&&s(t,p)){o[p]=t[p];break}}return o}return u}function s(t,e){return t.hasOwnProperty(e)}function l(t,e){return null!=t[e]&&"auto"!==t[e]}function u(t,e,n){Np(t,(function(t){e[t]=n[t]}))}u(Ep[0],t,r),u(Ep[1],t,o)}function Yp(t){return qp({},t)}function qp(t,e){return e&&t&&Np(Bp,(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t}V(zp,"vertical"),V(zp,"horizontal");var Zp=Oo(),jp=function(t){function n(e,n,i){var r=t.call(this,e,n,i)||this;return r.uid=Fc("ec_cpt_model"),r}var i;return e(n,t),n.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},n.prototype.mergeDefaultAndTheme=function(t,e){var n=Up(this),i=n?Yp(t):{};C(t,e.getTheme().get(this.mainType)),C(t,this.getDefaultOption()),n&&Xp(t,i,n)},n.prototype.mergeOption=function(t,e){C(this.option,t,!0);var n=Up(this);n&&Xp(this.option,t,n)},n.prototype.optionUpdated=function(t,e){},n.prototype.getDefaultOption=function(){var t=this.constructor;if(!function(t){return!(!t||!t[Uo])}(t))return t.defaultOption;var e=Zp(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;a>=0;a--)o=C(o,n[a],!0);e.defaultOption=o}return e.defaultOption},n.prototype.getReferringComponents=function(t,e){var n=t+"Index",i=t+"Id";return Fo(this.ecModel,t,{index:this.get(n,!0),id:this.get(i,!0)},e)},n.prototype.getBoxLayoutParams=function(){var t=this;return{left:t.get("left"),top:t.get("top"),right:t.get("right"),bottom:t.get("bottom"),width:t.get("width"),height:t.get("height")}},n.prototype.getZLevelKey=function(){return""},n.prototype.setZLevel=function(t){this.option.zlevel=t},n.protoInitialize=((i=n.prototype).type="component",i.id="",i.name="",i.mainType="",i.subType="",void(i.componentIndex=0)),n}(Ec);qo(jp,Ec),$o(jp),function(t){var e={};t.registerSubTypeDefaulter=function(t,n){var i=Xo(t);e[i.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=Xo(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r}}(jp),function(t,e){function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}t.topologicalTravel=function(t,i,r,o){if(t.length){var a=function(t){var i={},r=[];return O(t,(function(o){var a=n(i,o),s=function(t,e){var n=[];return O(t,(function(t){A(e,t)>=0&&n.push(t)})),n}(a.originalDeps=e(o),t);a.entryCount=s.length,0===a.entryCount&&r.push(o),O(s,(function(t){A(a.predecessor,t)<0&&a.predecessor.push(t);var e=n(i,t);A(e.successor,t)<0&&e.successor.push(o)}))})),{graph:i,noEntryList:r}}(i),s=a.graph,l=a.noEntryList,u={};for(O(t,(function(t){u[t]=!0}));l.length;){var h=l.pop(),c=s[h],p=!!u[h];p&&(r.call(o,h,c.originalDeps.slice()),delete u[h]),O(c.successor,p?f:d)}O(u,(function(){throw new Error("")}))}function d(t){s[t].entryCount--,0===s[t].entryCount&&l.push(t)}function f(t){u[t]=!0,d(t)}}}(jp,(function(t){var e=[];O(jp.getClassesByMainType(t),(function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])})),e=R(e,(function(t){return Xo(t).main})),"dataset"!==t&&A(e,"dataset")<=0&&e.unshift("dataset");return e}));const Kp=jp;var $p="";"undefined"!=typeof navigator&&($p=navigator.platform||"");var Qp="rgba(0, 0, 0, 0.2)";const Jp={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Qp,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Qp,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Qp,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Qp,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Qp,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Qp,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:$p.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var td=pt(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),ed="original",nd="arrayRows",id="objectRows",rd="keyedColumns",od="typedArray",ad="unknown",sd="column",ld="row",ud=1,hd=2,cd=3,pd=Oo();function dd(t,e,n){var i={},r=gd(e);if(!r||!t)return i;var o,a,s=[],l=[],u=e.ecModel,h=pd(u).datasetMap,c=r.uid+"_"+n.seriesLayoutBy;O(t=t.slice(),(function(e,n){var r=Y(e)?e:t[n]={name:e};"ordinal"===r.type&&null==o&&(o=n,a=f(r)),i[r.name]=[]}));var p=h.get(c)||h.set(c,{categoryWayDim:a,valueWayDim:0});function d(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function f(t){var e=t.dimsDef;return e?e.length:1}return O(t,(function(t,e){var n=t.name,r=f(t);if(null==o){var a=p.valueWayDim;d(i[n],a,r),d(l,a,r),p.valueWayDim+=r}else if(o===e)d(i[n],0,r),d(s,0,r);else{a=p.categoryWayDim;d(i[n],a,r),d(l,a,r),p.categoryWayDim+=r}})),s.length&&(i.itemName=s),l.length&&(i.seriesName=l),i}function fd(t,e,n){var i={};if(!gd(t))return i;var r,o=e.sourceFormat,a=e.dimensionsDefine;o!==id&&o!==rd||O(a,(function(t,e){"name"===(Y(t)?t.name:t)&&(r=e)}));var s=function(){for(var t={},i={},s=[],l=0,u=Math.min(5,n);l<u;l++){var h=vd(e.data,o,e.seriesLayoutBy,a,e.startIndex,l);s.push(h);var c=h===cd;if(c&&null==t.v&&l!==r&&(t.v=l),(null==t.n||t.n===t.v||!c&&s[t.n]===cd)&&(t.n=l),p(t)&&s[t.n]!==cd)return t;c||(h===hd&&null==i.v&&l!==r&&(i.v=l),null!=i.n&&i.n!==i.v||(i.n=l))}function p(t){return null!=t.v&&null!=t.n}return p(t)?t:p(i)?i:null}();if(s){i.value=[s.v];var l=null!=r?r:s.n;i.itemName=[l],i.seriesName=[l]}return i}function gd(t){if(!t.get("data",!0))return Fo(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},Eo).models[0]}function yd(t,e){return vd(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function vd(t,e,n,i,r,o){var a,s,l;if(Z(t))return cd;if(i){var u=i[o];Y(u)?(s=u.name,l=u.type):G(u)&&(s=u)}if(null!=l)return"ordinal"===l?ud:cd;if(e===nd){var h=t;if(n===ld){for(var c=h[o],p=0;p<(c||[]).length&&p<5;p++)if(null!=(a=m(c[r+p])))return a}else for(p=0;p<h.length&&p<5;p++){var d=h[r+p];if(d&&null!=(a=m(d[o])))return a}}else if(e===id){var f=t;if(!s)return cd;for(p=0;p<f.length&&p<5;p++){if((y=f[p])&&null!=(a=m(y[s])))return a}}else if(e===rd){if(!s)return cd;if(!(c=t[s])||Z(c))return cd;for(p=0;p<c.length&&p<5;p++)if(null!=(a=m(c[p])))return a}else if(e===ed){var g=t;for(p=0;p<g.length&&p<5;p++){var y,v=bo(y=g[p]);if(!H(v))return cd;if(null!=(a=m(v[o])))return a}}function m(t){var e=G(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?hd:cd:e&&"-"!==t?ud:void 0}return cd}var md=pt();function _d(t,e){rt(null==md.get(t)&&e),md.set(t,e)}var xd,wd,bd,Sd=Oo(),Md=Oo(),Td=function(){function t(){}return t.prototype.getColorFromPalette=function(t,e,n){var i=_o(this.get("color",!0)),r=this.get("colorLayer",!0);return Id(this,Sd,i,r,t,e,n)},t.prototype.clearColorPalette=function(){var t,e;(e=Sd)(t=this).paletteIdx=0,e(t).paletteNameMap={}},t}();function Cd(t,e,n,i){var r=_o(t.get(["aria","decal","decals"]));return Id(t,Md,r,null,e,n,i)}function Id(t,e,n,i,r,o,a){var s=e(o=o||t),l=s.paletteIdx||0,u=s.paletteNameMap=s.paletteNameMap||{};if(u.hasOwnProperty(r))return u[r];var h=null!=a&&i?function(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}(i,a):n;if((h=h||n)&&h.length){var c=h[l];return r&&(u[r]=c),s.paletteIdx=(l+1)%h.length,c}}var kd="\0_ec_inner",Dd=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new Ec(i),this._locale=new Ec(r),this._optionManager=o},n.prototype.setOption=function(t,e,n){var i=Ld(e);this._optionManager.setOption(t,n,i),this._resetOption(null,i)},n.prototype.resetOption=function(t,e){return this._resetOption(t,Ld(e))},n.prototype._resetOption=function(t,e){var n=!1,i=this._optionManager;if(!t||"recreate"===t){var r=i.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(r,e)):bd(this,r),n=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var o=i.getTimelineOption(this);o&&(n=!0,this._mergeOption(o,e))}if(!t||"recreate"===t||"media"===t){var a=i.getMediaOption(this);a.length&&O(a,(function(t){n=!0,this._mergeOption(t,e)}),this)}return n},n.prototype.mergeOption=function(t){this._mergeOption(t,null)},n.prototype._mergeOption=function(t,e){var n=this.option,i=this._componentsMap,r=this._componentsCount,o=[],a=pt(),s=e&&e.replaceMergeMainTypeMap;pd(this).datasetMap=pt(),O(t,(function(t,e){null!=t&&(Kp.hasClass(e)?e&&(o.push(e),a.set(e,!0)):n[e]=null==n[e]?T(t):C(n[e],t,!0))})),s&&s.each((function(t,e){Kp.hasClass(e)&&!a.get(e)&&(o.push(e),a.set(e,!0))})),Kp.topologicalTravel(o,Kp.getAllClassMainTypes(),(function(e){var o=function(t,e,n){var i=md.get(e);if(!i)return n;var r=i(t);return r?n.concat(r):n}(this,e,_o(t[e])),a=i.get(e),l=a?s&&s.get(e)?"replaceMerge":"normalMerge":"replaceAll",u=Mo(a,o,l);(function(t,e,n){O(t,(function(t){var i=t.newOption;Y(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=function(t,e,n,i){return e.type?e.type:n?n.subType:i.determineSubType(t,e)}(e,i,t.existing,n))}))})(u,e,Kp),n[e]=null,i.set(e,null),r.set(e,0);var h,c=[],p=[],d=0;O(u,(function(t,n){var i=t.existing,r=t.newOption;if(r){var o="series"===e,a=Kp.getClass(e,t.keyInfo.subType,!o);if(!a)return;if("tooltip"===e){if(h)return;h=!0}if(i&&i.constructor===a)i.name=t.keyInfo.name,i.mergeOption(r,this),i.optionUpdated(r,!1);else{var s=k({componentIndex:n},t.keyInfo);k(i=new a(r,this,this,s),s),t.brandNew&&(i.__requireNewView=!0),i.init(r,this,this),i.optionUpdated(null,!0)}}else i&&(i.mergeOption({},this),i.optionUpdated({},!1));i?(c.push(i.option),p.push(i),d++):(c.push(void 0),p.push(void 0))}),this),n[e]=c,i.set(e,p),r.set(e,d),"series"===e&&xd(this)}),this),this._seriesIndices||xd(this)},n.prototype.getOption=function(){var t=T(this.option);return O(t,(function(e,n){if(Kp.hasClass(n)){for(var i=_o(e),r=i.length,o=!1,a=r-1;a>=0;a--)i[a]&&!Do(i[a])?o=!0:(i[a]=null,!o&&r--);i.length=r,t[n]=i}})),delete t[kd],t},n.prototype.getTheme=function(){return this._theme},n.prototype.getLocaleModel=function(){return this._locale},n.prototype.setUpdatePayload=function(t){this._payload=t},n.prototype.getUpdatePayload=function(){return this._payload},n.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){var i=n[e||0];if(i)return i;if(null==e)for(var r=0;r<n.length;r++)if(n[r])return n[r]}},n.prototype.queryComponents=function(t){var e=t.mainType;if(!e)return[];var n,i=t.index,r=t.id,o=t.name,a=this._componentsMap.get(e);return a&&a.length?(null!=i?(n=[],O(_o(i),(function(t){a[t]&&n.push(a[t])}))):n=null!=r?Ad("id",r,a):null!=o?Ad("name",o,a):B(a,(function(t){return!!t})),Pd(n,t)):[]},n.prototype.findComponents=function(t){var e,n,i,r,o,a=t.query,s=t.mainType,l=(n=s+"Index",i=s+"Id",r=s+"Name",!(e=a)||null==e[n]&&null==e[i]&&null==e[r]?null:{mainType:s,index:e[n],id:e[i],name:e[r]}),u=l?this.queryComponents(l):B(this._componentsMap.get(s),(function(t){return!!t}));return o=Pd(u,t),t.filter?B(o,t.filter):o},n.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(W(t)){var r=e,o=t;i.each((function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}}))}else for(var a=G(t)?i.get(t):Y(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},n.prototype.getSeriesByName=function(t){var e=Io(t,null);return B(this._componentsMap.get("series"),(function(t){return!!t&&null!=e&&t.name===e}))},n.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},n.prototype.getSeriesByType=function(t){return B(this._componentsMap.get("series"),(function(e){return!!e&&e.subType===t}))},n.prototype.getSeries=function(){return B(this._componentsMap.get("series"),(function(t){return!!t}))},n.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},n.prototype.eachSeries=function(t,e){wd(this),O(this._seriesIndices,(function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)}),this)},n.prototype.eachRawSeries=function(t,e){O(this._componentsMap.get("series"),(function(n){n&&t.call(e,n,n.componentIndex)}))},n.prototype.eachSeriesByType=function(t,e,n){wd(this),O(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)}),this)},n.prototype.eachRawSeriesByType=function(t,e,n){return O(this.getSeriesByType(t),e,n)},n.prototype.isSeriesFiltered=function(t){return wd(this),null==this._seriesIndicesMap.get(t.componentIndex)},n.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},n.prototype.filterSeries=function(t,e){wd(this);var n=[];O(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];t.call(e,r,i)&&n.push(i)}),this),this._seriesIndices=n,this._seriesIndicesMap=pt(n)},n.prototype.restoreData=function(t){xd(this);var e=this._componentsMap,n=[];e.each((function(t,e){Kp.hasClass(e)&&n.push(e)})),Kp.topologicalTravel(n,Kp.getAllClassMainTypes(),(function(n){O(e.get(n),(function(e){!e||"series"===n&&function(t,e){if(e){var n=e.seriesIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}(e,t)||e.restoreData()}))}))},n.internalField=(xd=function(t){var e=t._seriesIndices=[];O(t._componentsMap.get("series"),(function(t){t&&e.push(t.componentIndex)})),t._seriesIndicesMap=pt(e)},wd=function(t){},void(bd=function(t,e){t.option={},t.option[kd]=1,t._componentsMap=pt({series:[]}),t._componentsCount=pt();var n,i,r,o=e.aria;Y(o)&&null==o.enabled&&(o.enabled=!0),n=e,i=t._theme.option,r=n.color&&!n.colorLayer,O(i,(function(t,e){"colorLayer"===e&&r||Kp.hasClass(e)||("object"==typeof t?n[e]=n[e]?C(n[e],t,!1):T(t):null==n[e]&&(n[e]=t))})),C(e,Jp,!1),t._mergeOption(e,null)})),n}(Ec);function Ad(t,e,n){if(H(e)){var i=pt();return O(e,(function(t){null!=t&&(null!=Io(t,null)&&i.set(t,!0))})),B(n,(function(e){return e&&i.get(e[t])}))}var r=Io(e,null);return B(n,(function(e){return e&&null!=r&&e[t]===r}))}function Pd(t,e){return e.hasOwnProperty("subType")?B(t,(function(t){return t&&t.subType===e.subType})):t}function Ld(t){var e=pt();return t&&O(_o(t.replaceMerge),(function(t){e.set(t,!0)})),{replaceMergeMainTypeMap:e}}P(Dd,Td);const Od=Dd;var Rd=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"];const Nd=function(t){O(Rd,(function(e){this[e]=F(t[e],t)}),this)};var Bd={};const Ed=function(){function t(){this._coordinateSystems=[]}return t.prototype.create=function(t,e){var n=[];O(Bd,(function(i,r){var o=i.create(t,e);n=n.concat(o||[])})),this._coordinateSystems=n},t.prototype.update=function(t,e){O(this._coordinateSystems,(function(n){n.update&&n.update(t,e)}))},t.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},t.register=function(t,e){Bd[t]=e},t.get=function(t){return Bd[t]},t}();var zd=/^(min|max)?(.+)$/;function Fd(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return O(t,(function(t,e){var n=e.match(zd);if(n&&n[1]&&n[2]){var o=n[1],a=n[2].toLowerCase();(function(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e})(i[a],t,o)||(r=!1)}})),r}const Vd=function(){function t(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return t.prototype.setOption=function(t,e,n){t&&(O(_o(t.series),(function(t){t&&t.data&&Z(t.data)&&st(t.data)})),O(_o(t.dataset),(function(t){t&&t.source&&Z(t.source)&&st(t.source)}))),t=T(t);var i=this._optionBackup,r=function(t,e,n){var i,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&H(u)&&O(u,(function(t){t&&t.option&&(t.query?o.push(t):i||(i=t))}));function p(t){O(e,(function(e){e(t,n)}))}return p(r),O(l,(function(t){return p(t)})),O(o,(function(t){return p(t.option)})),{baseOption:r,timelineOptions:l||[],mediaDefault:i,mediaList:o}}(t,e,!i);this._newBaseOption=r.baseOption,i?(r.timelineOptions.length&&(i.timelineOptions=r.timelineOptions),r.mediaList.length&&(i.mediaList=r.mediaList),r.mediaDefault&&(i.mediaDefault=r.mediaDefault)):this._optionBackup=r},t.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],T(t?e.baseOption:this._newBaseOption)},t.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=T(n[i.getCurrentIndex()]))}return e},t.prototype.getMediaOption=function(t){var e,n,i=this._api.getWidth(),r=this._api.getHeight(),o=this._mediaList,a=this._mediaDefault,s=[],l=[];if(!o.length&&!a)return l;for(var u=0,h=o.length;u<h;u++)Fd(o[u].query,i,r)&&s.push(u);return!s.length&&a&&(s=[-1]),s.length&&(e=s,n=this._currentMediaIndices,e.join(",")!==n.join(","))&&(l=R(s,(function(t){return T(-1===t?a.option:o[t].option)}))),this._currentMediaIndices=s,l},t}();var Hd=O,Wd=Y,Gd=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Ud(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=Gd.length;n<i;n++){var r=Gd[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?C(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?C(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function Xd(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,D(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r,r.focus&&(t.emphasis.focus=r.focus),r.blurScope&&(t.emphasis.blurScope=r.blurScope))}}function Yd(t){Xd(t,"itemStyle"),Xd(t,"lineStyle"),Xd(t,"areaStyle"),Xd(t,"label"),Xd(t,"labelLine"),Xd(t,"upperLabel"),Xd(t,"edgeLabel")}function qd(t,e){var n=Wd(t)&&t[e],i=Wd(n)&&n.textStyle;if(i)for(var r=0,o=wo.length;r<o;r++){var a=wo[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}function Zd(t){t&&(Yd(t),qd(t,"label"),t.emphasis&&qd(t.emphasis,"label"))}function jd(t){return H(t)?t:t?[t]:[]}function Kd(t){return(H(t)?t[0]:t)||{}}function $d(t,e){Hd(jd(t.series),(function(t){Wd(t)&&function(t){if(Wd(t)){Ud(t),Yd(t),qd(t,"label"),qd(t,"upperLabel"),qd(t,"edgeLabel"),t.emphasis&&(qd(t.emphasis,"label"),qd(t.emphasis,"upperLabel"),qd(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(Ud(e),Zd(e));var n=t.markLine;n&&(Ud(n),Zd(n));var i=t.markArea;i&&Zd(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!Z(o))for(var a=0;a<o.length;a++)Zd(o[a]);O(t.categories,(function(t){Yd(t)}))}if(r&&!Z(r))for(a=0;a<r.length;a++)Zd(r[a]);if((e=t.markPoint)&&e.data){var s=e.data;for(a=0;a<s.length;a++)Zd(s[a])}if((n=t.markLine)&&n.data){var l=n.data;for(a=0;a<l.length;a++)H(l[a])?(Zd(l[a][0]),Zd(l[a][1])):Zd(l[a])}"gauge"===t.type?(qd(t,"axisLabel"),qd(t,"title"),qd(t,"detail")):"treemap"===t.type?(Xd(t.breadcrumb,"itemStyle"),O(t.levels,(function(t){Yd(t)}))):"tree"===t.type&&Yd(t.leaves)}}(t)}));var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),Hd(n,(function(e){Hd(jd(t[e]),(function(t){t&&(qd(t,"axisLabel"),qd(t.axisPointer,"label"))}))})),Hd(jd(t.parallel),(function(t){var e=t&&t.parallelAxisDefault;qd(e,"axisLabel"),qd(e&&e.axisPointer,"label")})),Hd(jd(t.calendar),(function(t){Xd(t,"itemStyle"),qd(t,"dayLabel"),qd(t,"monthLabel"),qd(t,"yearLabel")})),Hd(jd(t.radar),(function(t){qd(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)})),Hd(jd(t.geo),(function(t){Wd(t)&&(Zd(t),Hd(jd(t.regions),(function(t){Zd(t)})))})),Hd(jd(t.timeline),(function(t){Zd(t),Xd(t,"label"),Xd(t,"itemStyle"),Xd(t,"controlStyle",!0);var e=t.data;H(e)&&O(e,(function(t){Y(t)&&(Xd(t,"label"),Xd(t,"itemStyle"))}))})),Hd(jd(t.toolbox),(function(t){Xd(t,"iconStyle"),Hd(t.feature,(function(t){Xd(t,"iconStyle")}))})),qd(Kd(t.axisPointer),"label"),qd(Kd(t.tooltip).axisPointer,"label")}function Qd(t){t&&O(Jd,(function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])}))}var Jd=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],tf=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],ef=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function nf(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<ef.length;n++){var i=ef[n][1],r=ef[n][0];null!=e[i]&&(e[r]=e[i])}}function rf(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function of(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function af(t,e){if(t)for(var n=0;n<t.length;n++)e(t[n]),t[n]&&af(t[n].children,e)}function sf(t,e){$d(t,e),t.series=_o(t.series),O(t.series,(function(t){if(Y(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e){if(null!=t.clockWise&&(t.clockwise=t.clockWise),rf(t.label),(r=t.data)&&!Z(r))for(var n=0;n<r.length;n++)rf(r[n]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},(t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset))}else if("gauge"===e){var i=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");null!=i&&function(t,e,n,i){for(var r,o=e.split(","),a=t,s=0;s<o.length-1;s++)null==a[r=o[s]]&&(a[r]={}),a=a[r];(i||null==a[o[s]])&&(a[o[s]]=n)}(t,"itemStyle.color",i)}else if("bar"===e){var r;if(nf(t),nf(t.backgroundStyle),nf(t.emphasis),(r=t.data)&&!Z(r))for(n=0;n<r.length;n++)"object"==typeof r[n]&&(nf(r[n]),nf(r[n]&&r[n].emphasis))}else if("sunburst"===e){var o=t.highlightPolicy;o&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=o)),of(t),af(t.data,of)}else"graph"===e||"sankey"===e?function(t){t&&null!=t.focusNodeAdjacency&&(t.emphasis=t.emphasis||{},null==t.emphasis.focus&&(t.emphasis.focus="adjacency"))}(t):"map"===e&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation&&D(t,t.mapLocation));null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation)),Qd(t)}})),t.dataRange&&(t.visualMap=t.dataRange),O(tf,(function(e){var n=t[e];n&&(H(n)||(n=[n]),O(n,(function(t){Qd(t)})))}))}function lf(t){O(t,(function(e,n){var i=[],r=[NaN,NaN],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex,l=e.seriesModel.get("stackStrategy")||"samesign";a.modify(o,(function(o,u,h){var c,p,d=a.get(e.stackedDimension,h);if(isNaN(d))return r;s?p=a.getRawIndex(h):c=a.get(e.stackedByDimension,h);for(var f=NaN,g=n-1;g>=0;g--){var y=t[g];if(s||(p=y.data.rawIndexOf(y.stackedByDimension,c)),p>=0){var v=y.data.getByRawIndex(y.stackResultDimension,p);if("all"===l||"positive"===l&&v>0||"negative"===l&&v<0||"samesign"===l&&d>=0&&v>0||"samesign"===l&&d<=0&&v<0){d=Kr(d,v),f=v;break}}}return i[0]=d,i[1]=f,i}))}))}var uf,hf,cf,pf,df,ff=function(t){this.data=t.data||(t.sourceFormat===rd?{}:[]),this.sourceFormat=t.sourceFormat||ad,this.seriesLayoutBy=t.seriesLayoutBy||sd,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&yd(this,n)===ud&&(i.type="ordinal")}};function gf(t){return t instanceof ff}function yf(t,e,n){n=n||mf(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:_f(r),startIndex:a,dimensionsDetectedCount:o};if(e===nd){var s=t;"auto"===i||null==i?xf((function(t){null!=t&&"-"!==t&&(G(t)?null==a&&(a=1):a=0)}),n,s,10):a=X(i)?i:i?1:0,r||1!==a||(r=[],xf((function(t,e){r[e]=null!=t?t+"":""}),n,s,1/0)),o=r?r.length:n===ld?s.length:s[0]?s[0].length:null}else if(e===id)r||(r=function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return z(e)}(t));else if(e===rd)r||(r=[],O(t,(function(t,e){r.push(e)})));else if(e===ed){var l=bo(t[0]);o=H(l)&&l.length||1}return{startIndex:a,dimensionsDefine:_f(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new ff({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:T(e)})}function vf(t){return new ff({data:t,sourceFormat:Z(t)?od:ed})}function mf(t){var e=ad;if(Z(t))e=od;else if(H(t)){0===t.length&&(e=nd);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(H(r)||Z(r)){e=nd;break}if(Y(r)){e=id;break}}}}else if(Y(t))for(var o in t)if(yt(t,o)&&L(t[o])){e=rd;break}return e}function _f(t){if(t){var e=pt();return R(t,(function(t,n){var i={name:(t=Y(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};if(null==i.name)return i;i.name+="",null==i.displayName&&(i.displayName=i.name);var r=e.get(i.name);return r?i.name+="-"+r.count++:e.set(i.name,{count:1}),i}))}}function xf(t,e,n,i){if(e===ld)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else{var o=n[0]||[];for(r=0;r<o.length&&r<i;r++)t(o[r],r)}}function wf(t){var e=t.sourceFormat;return e===id||e===rd}var bf=function(){function t(t,e){var n=gf(t)?t:vf(t);this._source=n;var i=this._data=n.data;n.sourceFormat===od&&(this._offset=0,this._dimSize=e,this._data=i),df(this,i,n)}var e;return t.prototype.getSource=function(){return this._source},t.prototype.count=function(){return 0},t.prototype.getItem=function(t,e){},t.prototype.appendData=function(t){},t.prototype.clean=function(){},t.protoInitialize=((e=t.prototype).pure=!1,void(e.persistent=!0)),t.internalField=function(){var t;df=function(t,r,o){var a=o.sourceFormat,s=o.seriesLayoutBy,l=o.startIndex,u=o.dimensionsDefine;if(k(t,pf[Lf(a,s)]),a===od)t.getItem=e,t.count=i,t.fillStorage=n;else{var h=Tf(a,s);t.getItem=F(h,null,r,l,u);var c=kf(a,s);t.count=F(c,null,r,l,u)}};var e=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},n=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var d=r[p*o+a];c[t+p]=d,d<l&&(l=d),d>u&&(u=d)}s[0]=l,s[1]=u}},i=function(){return this._data?this._data.length/this._dimSize:0};function r(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}(t={})[nd+"_"+sd]={pure:!0,appendData:r},t[nd+"_"+ld]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[id]={pure:!0,appendData:r},t[rd]={pure:!0,appendData:function(t){var e=this._data;O(t,(function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])}))}},t[ed]={appendData:r},t[od]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},pf=t}(),t}(),Sf=function(t,e,n,i){return t[i]},Mf=((uf={})[nd+"_"+sd]=function(t,e,n,i){return t[i+e]},uf[nd+"_"+ld]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},uf[id]=Sf,uf[rd]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[i]:null}return o},uf[ed]=Sf,uf);function Tf(t,e){return Mf[Lf(t,e)]}var Cf=function(t,e,n){return t.length},If=((hf={})[nd+"_"+sd]=function(t,e,n){return Math.max(0,t.length-e)},hf[nd+"_"+ld]=function(t,e,n){var i=t[0];return i?Math.max(0,i.length-e):0},hf[id]=Cf,hf[rd]=function(t,e,n){var i=t[n[0].name];return i?i.length:0},hf[ed]=Cf,hf);function kf(t,e){return If[Lf(t,e)]}var Df=function(t,e,n){return t[e]},Af=((cf={})[nd]=Df,cf[id]=function(t,e,n){return t[n]},cf[rd]=Df,cf[ed]=function(t,e,n){var i=bo(t);return i instanceof Array?i[e]:i},cf[od]=Df,cf);function Pf(t){return Af[t]}function Lf(t,e){return t===nd?t+"_"+e:t}function Of(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r=t.getStore(),o=r.getSource().sourceFormat;if(null!=n){var a=t.getDimensionIndex(n),s=r.getDimensionProperty(a);return Pf(o)(i,a,s)}var l=i;return o===ed&&(l=bo(i)),l}}}var Rf=/\{@(.+?)\}/g,Nf=function(){function t(){}return t.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),l=s&&s[n.getItemVisual(t,"drawType")||"fill"],u=s&&s.stroke,h=this.mainType,c="series"===h,p=n.userOutput&&n.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:l,borderColor:u,dimensionNames:p?p.fullDimensions:null,encode:p?p.encode:null,$vars:["seriesName","name","value"]}},t.prototype.getFormattedLabel=function(t,e,n,i,r,o){e=e||"normal";var a=this.getData(n),s=this.getDataParams(t,n);(o&&(s.value=o.interpolatedValue),null!=i&&H(s.value)&&(s.value=s.value[i]),r)||(r=a.getItemModel(t).get("normal"===e?["label","formatter"]:[e,"label","formatter"]));return W(r)?(s.status=e,s.dimensionIndex=i,r(s)):G(r)?Pp(r,s).replace(Rf,(function(e,n){var i=n.length,r=n;"["===r.charAt(0)&&"]"===r.charAt(i-1)&&(r=+r.slice(1,i-1));var s=Of(a,t,r);if(o&&H(o.interpolatedValue)){var l=a.getDimensionIndex(r);l>=0&&(s=o.interpolatedValue[l])}return null!=s?s+"":""})):void 0},t.prototype.getRawValue=function(t,e){return Of(this.getData(e),t)},t.prototype.formatTooltip=function(t,e,n){},t}();function Bf(t){var e,n;return Y(t)?t.type&&(n=t):e=t,{text:e,frag:n}}function Ef(t){return new zf(t)}var zf=function(){function t(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return t.prototype.perform=function(t){var e,n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var r=this.context;r.data=r.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!i&&(e=this._plan(this.context));var o,a=h(this._modBy),s=this._modDataCount||0,l=h(t&&t.modBy),u=t&&t.modDataCount||0;function h(t){return!(t>=1)&&(t=1),t}a===l&&s===u||(e="reset"),(this._dirty||"reset"===e)&&(this._dirty=!1,o=this._doReset(i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var p=this._dueIndex,d=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(o||p<d)){var f=this._progress;if(H(f))for(var g=0;g<f.length;g++)this._doProgress(f[g],p,d,l,u);else this._doProgress(f,p,d,l,u)}this._dueIndex=d;var y=null!=this._settedOutputEnd?this._settedOutputEnd:d;this._outputDueEnd=y}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},t.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},t.prototype._doProgress=function(t,e,n,i,r){Ff.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:Ff.next},this.context)},t.prototype._doReset=function(t){var e,n;this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),H(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var i=this._downstream;return i&&i.dirty(),n},t.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},t.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},t.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},t.prototype.getUpstream=function(){return this._upstream},t.prototype.getDownstream=function(){return this._downstream},t.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},t}(),Ff=function(){var t,e,n,i,r,o={reset:function(l,u,h,c){e=l,t=u,n=h,i=c,r=Math.ceil(i/n),o.next=n>1&&i>0?s:a}};return o;function a(){return e<t?e++:null}function s(){var o=e%r*n+Math.ceil(e/r),a=e>=t?null:o<i?o:e;return e++,a}}();function Vf(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"!==n||X(t)||null==t||"-"===t||(t=+eo(t)),null==t||""===t?NaN:Number(t))}var Hf=pt({number:function(t){return parseFloat(t)},time:function(t){return+eo(t)},trim:function(t){return G(t)?ot(t):t}});function Wf(t){return Hf.get(t)}var Gf={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return t>e},gte:function(t,e){return t>=e}},Uf=function(){function t(t,e){if(!X(e)){go("")}this._opFn=Gf[t],this._rvalFloat=ao(e)}return t.prototype.evaluate=function(t){return X(t)?this._opFn(t,this._rvalFloat):this._opFn(ao(t),this._rvalFloat)},t}(),Xf=function(){function t(t,e){var n="desc"===t;this._resultLT=n?1:-1,null==e&&(e=n?"min":"max"),this._incomparable="min"===e?-1/0:1/0}return t.prototype.evaluate=function(t,e){var n=X(t)?t:ao(t),i=X(e)?e:ao(e),r=isNaN(n),o=isNaN(i);if(r&&(n=this._incomparable),o&&(i=this._incomparable),r&&o){var a=G(t),s=G(e);a&&(n=s?t:0),s&&(i=a?e:0)}return n<i?this._resultLT:n>i?-this._resultLT:0},t}(),Yf=function(){function t(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=ao(e)}return t.prototype.evaluate=function(t){var e=t===this._rval;if(!e){var n=typeof t;n===this._rvalTypeof||"number"!==n&&"number"!==this._rvalTypeof||(e=ao(t)===this._rvalFloat)}return this._isEQ?e:!e},t}();function qf(t,e){return"eq"===t||"ne"===t?new Yf("eq"===t,e):yt(Gf,t)?new Uf(t,e):null}var Zf=function(){function t(){}return t.prototype.getRawData=function(){throw new Error("not supported")},t.prototype.getRawDataItem=function(t){throw new Error("not supported")},t.prototype.cloneRawData=function(){},t.prototype.getDimensionInfo=function(t){},t.prototype.cloneAllDimensionInfo=function(){},t.prototype.count=function(){},t.prototype.retrieveValue=function(t,e){},t.prototype.retrieveValueFromItem=function(t,e){},t.prototype.convertValue=function(t,e){return Vf(t,e)},t}();function jf(t){if(!eg(t.sourceFormat)){go("")}return t.data}function Kf(t){var e=t.sourceFormat,n=t.data;if(!eg(e)){go("")}if(e===nd){for(var i=[],r=0,o=n.length;r<o;r++)i.push(n[r].slice());return i}if(e===id){for(i=[],r=0,o=n.length;r<o;r++)i.push(k({},n[r]));return i}}function $f(t,e,n){if(null!=n)return X(n)||!isNaN(n)&&!yt(e,n)?t[n]:yt(e,n)?e[n]:void 0}function Qf(t){return T(t)}var Jf=pt();function tg(t,e,n,i){e.length||go(""),Y(t)||go("");var r=t.type,o=Jf.get(r);o||go("");var a=R(e,(function(t){return function(t,e){var n=new Zf,i=t.data,r=n.sourceFormat=t.sourceFormat,o=t.startIndex;t.seriesLayoutBy!==sd&&go("");var a=[],s={},l=t.dimensionsDefine;if(l)O(l,(function(t,e){var n=t.name,i={index:e,name:n,displayName:t.displayName};a.push(i),null!=n&&(yt(s,n)&&go(""),s[n]=i)}));else for(var u=0;u<t.dimensionsDetectedCount;u++)a.push({index:u});var h=Tf(r,sd);e.__isBuiltIn&&(n.getRawDataItem=function(t){return h(i,o,a,t)},n.getRawData=F(jf,null,t)),n.cloneRawData=F(Kf,null,t);var c=kf(r,sd);n.count=F(c,null,i,o,a);var p=Pf(r);n.retrieveValue=function(t,e){var n=h(i,o,a,t);return d(n,e)};var d=n.retrieveValueFromItem=function(t,e){if(null!=t){var n=a[e];return n?p(t,e,n.name):void 0}};return n.getDimensionInfo=F($f,null,a,s),n.cloneAllDimensionInfo=F(Qf,null,a),n}(t,o)}));return R(_o(o.transform({upstream:a[0],upstreamList:a,config:T(t.config)})),(function(t,n){var i;Y(t)||go(""),t.data||go(""),eg(mf(t.data))||go("");var r=e[0];if(r&&0===n&&!t.dimensions){var o=r.startIndex;o&&(t.data=r.data.slice(0,o).concat(t.data)),i={seriesLayoutBy:sd,sourceHeader:o,dimensions:r.metaRawOption.dimensions}}else i={seriesLayoutBy:sd,sourceHeader:0,dimensions:t.dimensions};return yf(t.data,i,null)}))}function eg(t){return t===nd||t===id}var ng,ig="undefined",rg=typeof Uint32Array===ig?Array:Uint32Array,og=typeof Uint16Array===ig?Array:Uint16Array,ag=typeof Int32Array===ig?Array:Int32Array,sg=typeof Float64Array===ig?Array:Float64Array,lg={float:sg,int:ag,ordinal:Array,number:Array,time:sg};function ug(t){return t>65535?rg:og}function hg(t,e,n,i,r){var o=lg[n||"float"];if(r){var a=t[e],s=a&&a.length;if(s!==i){for(var l=new o(i),u=0;u<s;u++)l[u]=a[u];t[e]=l}}else t[e]=new o(i)}var cg=function(){function t(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=pt()}return t.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=ng[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[],wf(i),this._dimensions=R(e,(function(t){return{type:t.type,property:t.property}})),this._initDataFromProvider(0,t.count())},t.prototype.getProvider=function(){return this._provider},t.prototype.getSource=function(){return this._provider.getSource()},t.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new lg[e||"float"](this._rawCount),this._rawExtent[r]=[1/0,-1/0],r},t.prototype.collectOrdinalMeta=function(t,e){var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length;0===o&&(r[t]=[1/0,-1/0]);for(var s=r[t],l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},t.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},t.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},t.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t);var i=e.count();return e.persistent||(i+=n),n<i&&this._initDataFromProvider(n,i,!0),[n,i]},t.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++){hg(n,l,(d=i[l]).type,s,!0)}for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var d=i[p],f=ng.arrayRows.call(this,t[c]||u,d.property,c,p);n[p][h]=f;var g=o[p];f<g[0]&&(g[0]=f),f>g[1]&&(g[1]=f)}return this._rawCount=this._count=s,{start:a,end:s}},t.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=R(o,(function(t){return t.property})),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=[1/0,-1/0]),hg(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++){c=i.getItem(p,c);for(var d=0;d<a;d++){var f=r[d],g=this._dimValueGetter(c,l[d],p,d);f[p]=g;var y=s[d];g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},t.prototype.count=function(){return this._count},t.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(e)]:NaN},t.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;r=0;for(var o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},t.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._chunks[t];return n?n[e]:NaN},t.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},t.prototype.getMedian=function(t){var e=[];this.each([t],(function(t){isNaN(t)||e.push(t)}));var n=e.sort((function(t,e){return t-e})),i=this.count();return 0===i?0:i%2==1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},t.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},t.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(!i)return r;null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&h>=0&&a<0)&&(o=c,a=h,s=0),h===a&&(r[s++]=l))}return r.length=s,r},t.prototype.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,i=this._count;if(n===Array){t=new n(i);for(var r=0;r<i;r++)t[r]=e[r]}else t=new n(e.buffer,0,i)}else{t=new(n=ug(this._rawCount))(this.count());for(r=0;r<t.length;r++)t[r]=r}return t},t.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(ug(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a){c=e(u[l][p],h)}else{for(var d=0;d<a;d++)o[d]=u[t[d]][p];o[d]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},t.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=z(t),r=i.length;if(!r)return this;var o=e.count(),a=new(ug(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,p=!1;if(!e._indices){var d=0;if(1===r){for(var f=c[i[0]],g=0;g<n;g++){((_=f[g])>=u&&_<=h||isNaN(_))&&(a[s++]=d),d++}p=!0}else if(2===r){f=c[i[0]];var y=c[i[1]],v=t[i[1]][0],m=t[i[1]][1];for(g=0;g<n;g++){var _=f[g],x=y[g];(_>=u&&_<=h||isNaN(_))&&(x>=v&&x<=m||isNaN(x))&&(a[s++]=d),d++}p=!0}}if(!p)if(1===r)for(g=0;g<o;g++){var w=e.getRawIndex(g);((_=c[i[0]][w])>=u&&_<=h||isNaN(_))&&(a[s++]=w)}else for(g=0;g<o;g++){for(var b=!0,S=(w=e.getRawIndex(g),0);S<r;S++){var M=i[S];((_=c[M][w])<t[M][0]||_>t[M][1])&&(b=!1)}b&&(a[s++]=e.getRawIndex(g))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},t.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},t.prototype.modify=function(t,e){this._updateDims(this,t,e)},t.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=[1/0,-1/0];for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var d=n&&n.apply(null,s);if(null!=d){"object"!=typeof d&&(r[0]=d,d=r);for(u=0;u<d.length;u++){var f=e[u],g=d[u],y=l[f],v=i[f];v&&(v[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},t.prototype.lttbDownSample=function(t,e){var n,i,r,o=this.clone([t],!0),a=o._chunks[t],s=this.count(),l=0,u=Math.floor(1/e),h=this.getRawIndex(0),c=new(ug(this._rawCount))(Math.min(2*(Math.ceil(s/u)+2),s));c[l++]=h;for(var p=1;p<s-1;p+=u){for(var d=Math.min(p+u,s-1),f=Math.min(p+2*u,s),g=(f+d)/2,y=0,v=d;v<f;v++){var m=a[T=this.getRawIndex(v)];isNaN(m)||(y+=m)}y/=f-d;var _=p,x=Math.min(p+u,s),w=p-1,b=a[h];n=-1,r=_;var S=-1,M=0;for(v=_;v<x;v++){var T;m=a[T=this.getRawIndex(v)];isNaN(m)?(M++,S<0&&(S=T)):(i=Math.abs((w-g)*(m-b)-(w-v)*(y-b)))>n&&(n=i,r=T)}M>0&&M<x-_&&(c[l++]=Math.min(S,r),r=Math.max(S,r)),c[l++]=r,h=r}return c[l++]=this.getRawIndex(s-1),o._count=l,o._indices=c,o.getRawIndex=this._getRawIdx,o},t.prototype.minmaxDownSample=function(t,e){for(var n=this.clone([t],!0),i=n._chunks,r=Math.floor(1/e),o=i[t],a=this.count(),s=new(ug(this._rawCount))(2*Math.ceil(a/r)),l=0,u=0;u<a;u+=r){var h=u,c=o[this.getRawIndex(h)],p=u,d=o[this.getRawIndex(p)],f=r;u+r>a&&(f=a-u);for(var g=0;g<f;g++){var y=o[this.getRawIndex(u+g)];y<c&&(c=y,h=u+g),y>d&&(d=y,p=u+g)}var v=this.getRawIndex(h),m=this.getRawIndex(p);h<p?(s[l++]=v,s[l++]=m):(s[l++]=m,s[l++]=v)}return n._count=l,n._indices=s,n._updateGetRawIdx(),n},t.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=[1/0,-1/0],c=new(ug(this._rawCount))(Math.ceil(u/s)),p=0,d=0;d<u;d+=s){s>u-d&&(s=u-d,a.length=s);for(var f=0;f<s;f++){var g=this.getRawIndex(d+f);a[f]=l[g]}var y=n(a),v=this.getRawIndex(Math.min(d+i(a,y)||0,u-1));l[v]=y,y<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=v}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},t.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},t.prototype.getDataExtent=function(t){var e=this._chunks[t],n=[1/0,-1/0];if(!e)return n;var i,r=this.count();if(!this._indices)return this._rawExtent[t].slice();if(i=this._extent[t])return i.slice();for(var o=(i=n)[0],a=i[1],s=0;s<r;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),l>a&&(a=l)}return i=[o,a],this._extent[t]=i,i},t.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},t.prototype.clone=function(e,n){var i,r,o=new t,a=this._chunks,s=e&&N(e,(function(t,e){return t[e]=!0,t}),{});if(s)for(var l=0;l<a.length;l++)o._chunks[l]=s[l]?(i=a[l],r=void 0,(r=i.constructor)===Array?i.slice():new r(i)):a[l];else o._chunks=a;return this._copyCommonProps(o),n||(o._indices=this._cloneIndices()),o._updateGetRawIdx(),o},t.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=T(this._extent),t._rawExtent=T(this._rawExtent)},t.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var n=this._indices.length;e=new t(n);for(var i=0;i<n;i++)e[i]=this._indices[i]}else e=new t(this._indices);return e}return null},t.prototype._getRawIdxIdentity=function(t){return t},t.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},t.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},t.internalField=function(){function t(t,e,n,i){return Vf(t[i],this._dimensions[i])}ng={arrayRows:t,objectRows:function(t,e,n,i){return Vf(t[e],this._dimensions[i])},keyedColumns:t,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return Vf(r instanceof Array?r[i]:r,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}}}(),t}(),pg=function(){function t(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return t.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},t.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},t.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},t.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},t.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n=this._sourceHost,i=this._getUpstreamSourceManagers(),r=!!i.length;if(fg(n)){var o=n,a=void 0,s=void 0,l=void 0;if(r){var u=i[0];u.prepareSource(),a=(l=u.getSource()).data,s=l.sourceFormat,e=[u._getVersionSign()]}else s=Z(a=o.get("data",!0))?od:ed,e=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},p=tt(h.seriesLayoutBy,c.seriesLayoutBy)||null,d=tt(h.sourceHeader,c.sourceHeader),f=tt(h.dimensions,c.dimensions);t=p!==c.seriesLayoutBy||!!d!=!!c.sourceHeader||f?[yf(a,{seriesLayoutBy:p,sourceHeader:d,dimensions:f},s)]:[]}else{var g=n;if(r){var y=this._applyTransform(i);t=y.sourceList,e=y.upstreamSignList}else{t=[yf(g.get("source",!0),this._getSourceMetaRawOption(),null)],e=[]}}this._setLocalSource(t,e)},t.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0);if(null!=r){1!==t.length&&gg("")}var o,a=[],s=[];return O(t,(function(t){t.prepareSource();var e=t.getSource(r||0);null==r||e||gg(""),a.push(e),s.push(t._getVersionSign())})),i?e=function(t,e){var n=_o(t),i=n.length;i||go("");for(var r=0,o=i;r<o;r++)e=tg(n[r],e),r!==o-1&&(e.length=Math.max(e.length,1));return e}(i,a,n.componentIndex):null!=r&&(e=[(o=a[0],new ff({data:o.data,sourceFormat:o.sourceFormat,seriesLayoutBy:o.seriesLayoutBy,dimensionsDefine:T(o.dimensionsDefine),startIndex:o.startIndex,dimensionsDetectedCount:o.dimensionsDetectedCount}))]),{sourceList:e,upstreamSignList:s}},t.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},t.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},t.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},t.prototype._innerGetDataStore=function(t,e,n){var i=this._storeList,r=i[0];r||(r=i[0]={});var o=r[n];if(!o){var a=this._getUpstreamSourceManagers()[0];fg(this._sourceHost)&&a?o=a._innerGetDataStore(t,e,n):(o=new cg).initData(new bf(e,t.length),t),r[n]=o}return o},t.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(fg(t)){var e=gd(t);return e?[e.getSourceManager()]:[]}return R(function(t){return t.get("transform",!0)||t.get("fromTransformResult",!0)?Fo(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},Eo).models:[]}(t),(function(t){return t.getSourceManager()}))},t.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;if(fg(i))t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var r=i;t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)}return{seriesLayoutBy:t,sourceHeader:e,dimensions:n}},t}();function dg(t){t.option.transform&&st(t.option.transform)}function fg(t){return"series"===t.mainType}function gg(t){throw new Error(t)}function yg(t){var e=t.lineHeight;return null==e?"line-height:1":"line-height:"+Zt(e+"")+"px"}function vg(t,e){var n=t.color||"#6e7079",i=t.fontSize||12,r=t.fontWeight||"400",o=t.color||"#464646",a=t.fontSize||14,s=t.fontWeight||"900";return"html"===e?{nameStyle:"font-size:"+Zt(i+"")+"px;color:"+Zt(n)+";font-weight:"+Zt(r+""),valueStyle:"font-size:"+Zt(a+"")+"px;color:"+Zt(o)+";font-weight:"+Zt(s+"")}:{nameStyle:{fontSize:i,fill:n,fontWeight:r},valueStyle:{fontSize:a,fill:o,fontWeight:s}}}var mg=[0,10,20,30],_g=["","\n","\n\n","\n\n\n"];function xg(t,e){return e.type=t,e}function wg(t){return"section"===t.type}function bg(t){return wg(t)?Mg:Tg}function Sg(t){if(wg(t)){var e=0,n=t.blocks.length,i=n>1||n>0&&!t.noHeader;return O(t.blocks,(function(t){var n=Sg(t);n>=e&&(e=n+ +(i&&(!n||wg(t)&&!t.noHeader)))})),e}return 0}function Mg(t,e,n,i){var r,o=e.noHeader,a=(r=Sg(e),{html:mg[r],richText:_g[r]}),s=[],l=e.blocks||[];rt(!l||H(l)),l=l||[];var u=t.orderMode;if(e.sortBlocks&&u){l=l.slice();var h={valueAsc:"asc",valueDesc:"desc"};if(yt(h,u)){var c=new Xf(h[u],null);l.sort((function(t,e){return c.evaluate(t.sortParam,e.sortParam)}))}else"seriesDesc"===u&&l.reverse()}O(l,(function(n,r){var o=e.valueFormatter,l=bg(n)(o?k(k({},t),{valueFormatter:o}):t,n,r>0?a.html:0,i);null!=l&&s.push(l)}));var p="richText"===t.renderMode?s.join(a.richText):Ig(i,s.join(""),o?n:a.html);if(o)return p;var d=kp(e.header,"ordinal",t.useUTC),f=vg(i,t.renderMode).nameStyle,g=yg(i);return"richText"===t.renderMode?kg(t,d,f)+a.richText+p:Ig(i,'<div style="'+f+";"+g+';">'+Zt(d)+"</div>"+p,n)}function Tg(t,e,n,i){var r=t.renderMode,o=e.noName,a=e.noValue,s=!e.markerType,l=e.name,u=t.useUTC,h=e.valueFormatter||t.valueFormatter||function(t){return R(t=H(t)?t:[t],(function(t,e){return kp(t,H(d)?d[e]:d,u)}))};if(!o||!a){var c=s?"":t.markupStyleCreator.makeTooltipMarker(e.markerType,e.markerColor||"#333",r),p=o?"":kp(l,"ordinal",u),d=e.valueType,f=a?[]:h(e.value,e.dataIndex),g=!s||!o,y=!s&&o,v=vg(i,r),m=v.nameStyle,_=v.valueStyle;return"richText"===r?(s?"":c)+(o?"":kg(t,p,m))+(a?"":function(t,e,n,i,r){var o=[r],a=i?10:20;return n&&o.push({padding:[0,0,0,a],align:"right"}),t.markupStyleCreator.wrapRichTextStyle(H(e)?e.join("  "):e,o)}(t,f,g,y,_)):Ig(i,(s?"":c)+(o?"":function(t,e,n){return'<span style="'+n+";"+(e?"margin-left:2px":"")+'">'+Zt(t)+"</span>"}(p,!s,m))+(a?"":function(t,e,n,i){var r=n?"10px":"20px",o=e?"float:right;margin-left:"+r:"";return t=H(t)?t:[t],'<span style="'+o+";"+i+'">'+R(t,(function(t){return Zt(t)})).join("&nbsp;&nbsp;")+"</span>"}(f,g,y,_)),n)}}function Cg(t,e,n,i,r,o){if(t)return bg(t)({useUTC:r,renderMode:n,orderMode:i,markupStyleCreator:e,valueFormatter:t.valueFormatter},t,0,o)}function Ig(t,e,n){return'<div style="'+("margin: "+n+"px 0 0")+";"+yg(t)+';">'+e+'<div style="clear:both"></div></div>'}function kg(t,e,n){return t.markupStyleCreator.wrapRichTextStyle(e,n)}function Dg(t,e){return Op(t.getData().getItemVisual(e,"style")[t.visualDrawType])}function Ag(t,e){var n=t.get("padding");return null!=n?n:"richText"===e?[8,10]:10}var Pg=function(){function t(){this.richTextStyles={},this._nextStyleNameId=lo()}return t.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},t.prototype.makeTooltipMarker=function(t,e,n){var i="richText"===n?this._generateStyleName():null,r=function(t,e){var n=G(t)?{color:t,extraCssText:e}:t||{},i=n.color,r=n.type;e=n.extraCssText;var o=n.renderMode||"html";return i?"html"===o?"subItem"===r?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Zt(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Zt(i)+";"+(e||"")+'"></span>':{renderMode:o,content:"{"+(n.markerId||"markerX")+"|}  ",style:"subItem"===r?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}:""}({color:e,type:t,renderMode:n,markerId:i});return G(r)?r:(this.richTextStyles[i]=r.style,r.content)},t.prototype.wrapRichTextStyle=function(t,e){var n={};H(e)?O(e,(function(t){return k(n,t)})):k(n,e);var i=this._generateStyleName();return this.richTextStyles[i]=n,"{"+i+"|"+t+"}"},t}();function Lg(t){var e,n,i,r,o=t.series,a=t.dataIndex,s=t.multipleSeries,l=o.getData(),u=l.mapDimensionsAll("defaultedTooltip"),h=u.length,c=o.getRawValue(a),p=H(c),d=Dg(o,a);if(h>1||p&&!h){var f=function(t,e,n,i,r){var o=e.getData(),a=N(t,(function(t,e,n){var i=o.getDimensionInfo(n);return t||i&&!1!==i.tooltip&&null!=i.displayName}),!1),s=[],l=[],u=[];function h(t,e){var n=o.getDimensionInfo(e);n&&!1!==n.otherDims.tooltip&&(a?u.push(xg("nameValue",{markerType:"subItem",markerColor:r,name:n.displayName,value:t,valueType:n.type})):(s.push(t),l.push(n.type)))}return i.length?O(i,(function(t){h(Of(o,n,t),t)})):O(t,h),{inlineValues:s,inlineValueTypes:l,blocks:u}}(c,o,a,u,d);e=f.inlineValues,n=f.inlineValueTypes,i=f.blocks,r=f.inlineValues[0]}else if(h){var g=l.getDimensionInfo(u[0]);r=e=Of(l,a,u[0]),n=g.type}else r=e=p?c[0]:c;var y=ko(o),v=y&&o.name||"",m=l.getName(a),_=s?v:m;return xg("section",{header:v,noHeader:s||!y,sortParam:r,blocks:[xg("nameValue",{markerType:"item",markerColor:d,name:_,noName:!ot(_),value:e,valueType:n,dataIndex:a})].concat(i||[])})}var Og=Oo();function Rg(t,e){return t.getName(e)||t.getId(e)}var Ng="__universalTransitionEnabled",Bg=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}var i;return e(n,t),n.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=Ef({count:zg,reset:Fg}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),(Og(this).sourceManager=new pg(this)).prepareSource();var i=this.getInitialData(t,n);Hg(i,this),this.dataTask.context.data=i,Og(this).dataBeforeProcessed=i,Eg(this),this._initSelectedMapFromData(i)},n.prototype.mergeDefaultAndTheme=function(t,e){var n=Up(this),i=n?Yp(t):{},r=this.subType;Kp.hasClass(r)&&(r+="Series"),C(t,e.getTheme().get(this.subType)),C(t,this.getDefaultOption()),xo(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&Xp(t,i,n)},n.prototype.mergeOption=function(t,e){t=C(this.option,t,!0),this.fillDataTextStyle(t.data);var n=Up(this);n&&Xp(this.option,t,n);var i=Og(this).sourceManager;i.dirty(),i.prepareSource();var r=this.getInitialData(t,e);Hg(r,this),this.dataTask.dirty(),this.dataTask.context.data=r,Og(this).dataBeforeProcessed=r,Eg(this),this._initSelectedMapFromData(r)},n.prototype.fillDataTextStyle=function(t){if(t&&!Z(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&xo(t[n],"label",e)},n.prototype.getInitialData=function(t,e){},n.prototype.appendData=function(t){this.getRawData().appendData(t.data)},n.prototype.getData=function(t){var e=Gg(this);if(e){var n=e.context.data;return null!=t&&n.getLinkedData?n.getLinkedData(t):n}return Og(this).data},n.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},n.prototype.setData=function(t){var e=Gg(this);if(e){var n=e.context;n.outputData=t,e!==this.dataTask&&(n.data=t)}Og(this).data=t},n.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return pt(t)},n.prototype.getSourceManager=function(){return Og(this).sourceManager},n.prototype.getSource=function(){return this.getSourceManager().getSource()},n.prototype.getRawData=function(){return Og(this).dataBeforeProcessed},n.prototype.getColorBy=function(){return this.get("colorBy")||"series"},n.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},n.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},n.prototype.formatTooltip=function(t,e,n){return Lg({series:this,dataIndex:t,multipleSeries:e})},n.prototype.isAnimationEnabled=function(){var t=this.ecModel;if(r.node&&(!t||!t.ssr))return!1;var e=this.getShallow("animation");return e&&this.getData().count()>this.getShallow("animationThreshold")&&(e=!1),!!e},n.prototype.restoreData=function(){this.dataTask.dirty()},n.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel,r=Td.prototype.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},n.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},n.prototype.getProgressive=function(){return this.get("progressive")},n.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},n.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},n.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)return this.option.selectedMap={},void(this._selectedDataIndicesMap={});for(var o=0;o<t.length;o++){var a=Rg(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},n.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},n.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=z(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];r>=0&&n.push(r)}return n},n.prototype.isSelected=function(t,e){var n=this.option.selectedMap;if(!n)return!1;var i=this.getData(e);return("all"===n||n[Rg(i,t)])&&!i.getItemModel(t).get(["select","disabled"])},n.prototype.isUniversalTransitionEnabled=function(){if(this[Ng])return!0;var t=this.option.universalTransition;return!!t&&(!0===t||t&&t.enabled)},n.prototype._innerSelect=function(t,e){var n,i,r=this.option,o=r.selectedMode,a=e.length;if(o&&a)if("series"===o)r.selectedMap="all";else if("multiple"===o){Y(r.selectedMap)||(r.selectedMap={});for(var s=r.selectedMap,l=0;l<a;l++){var u=e[l];s[c=Rg(t,u)]=!0,this._selectedDataIndicesMap[c]=t.getRawIndex(u)}}else if("single"===o||!0===o){var h=e[a-1],c=Rg(t,h);r.selectedMap=((n={})[c]=!0,n),this._selectedDataIndicesMap=((i={})[c]=t.getRawIndex(h),i)}},n.prototype._initSelectedMapFromData=function(t){if(!this.option.selectedMap){var e=[];t.hasItemOption&&t.each((function(n){var i=t.getRawDataItem(n);i&&i.selected&&e.push(n)})),e.length>0&&this._innerSelect(t,e)}},n.registerClass=function(t){return Kp.registerClass(t)},n.protoInitialize=((i=n.prototype).type="series.__base__",i.seriesIndex=0,i.ignoreStyleOnData=!1,i.hasSymbolVisual=!1,i.defaultSymbol="circle",i.visualStyleAccessPath="itemStyle",void(i.visualDrawType="fill")),n}(Kp);function Eg(t){var e=t.name;ko(t)||(t.name=function(t){var e=t.getRawData(),n=e.mapDimensionsAll("seriesName"),i=[];return O(n,(function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)})),i.join(" ")}(t)||e)}function zg(t){return t.model.getRawData().count()}function Fg(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),Vg}function Vg(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Hg(t,e){O(dt(t.CHANGABLE_METHODS,t.DOWNSAMPLE_METHODS),(function(n){t.wrapMethod(n,V(Wg,e))}))}function Wg(t,e){var n=Gg(t);return n&&n.setOutputEnd((e||this).count()),e}function Gg(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}P(Bg,Nf),P(Bg,Td),qo(Bg,Kp);const Ug=Bg;var Xg=function(){function t(){this.group=new Br,this.uid=Fc("viewComponent")}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){},t.prototype.updateLayout=function(t,e,n,i){},t.prototype.updateVisual=function(t,e,n,i){},t.prototype.toggleBlurSeries=function(t,e,n){},t.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},t}();Yo(Xg),$o(Xg);const Yg=Xg;function qg(){var t=Oo();return function(e){var n=t(e),i=e.pipelineContext,r=!!n.large,o=!!n.progressiveRender,a=n.large=!(!i||!i.large),s=n.progressiveRender=!(!i||!i.progressiveRender);return!(r===a&&o===s)&&"reset"}}var Zg=Oo(),jg=qg(),Kg=function(){function t(){this.group=new Br,this.uid=Fc("viewChart"),this.renderTask=Ef({plan:Jg,reset:ty}),this.renderTask.context={view:this}}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){},t.prototype.highlight=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&Qg(r,i,"emphasis")},t.prototype.downplay=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&Qg(r,i,"normal")},t.prototype.remove=function(t,e){this.group.removeAll()},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.eachRendered=function(t){sc(this.group,t)},t.markUpdateMethod=function(t,e){Zg(t).updateMethod=e},t.protoInitialize=void(t.prototype.type="chart"),t}();function $g(t,e,n){t&&tu(t)&&("emphasis"===e?Rl:Nl)(t,n)}function Qg(t,e,n){var i=Lo(t,e),r=e&&null!=e.highlightKey?function(t){var e=sl[t];return null==e&&al<=32&&(e=sl[t]=al++),e}(e.highlightKey):null;null!=i?O(_o(i),(function(e){$g(t.getItemGraphicEl(e),n,r)})):t.eachItemGraphicEl((function(t){$g(t,n,r)}))}function Jg(t){return jg(t.model)}function ty(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&Zg(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,n,i,r),ey[l]}Yo(Kg),$o(Kg);var ey={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};const ny=Kg;var iy="\0__throttleOriginMethod",ry="\0__throttleRate",oy="\0__throttleType";function ay(t,e,n){var i,r,o,a,s,l=0,u=0,h=null;function c(){u=(new Date).getTime(),h=null,t.apply(o,a||[])}e=e||0;var p=function(){for(var t=[],p=0;p<arguments.length;p++)t[p]=arguments[p];i=(new Date).getTime(),o=this,a=t;var d=s||e,f=s||n;s=null,r=i-(f?l:u)-d,clearTimeout(h),f?h=setTimeout(c,d):r>=0?c():h=setTimeout(c,-r),l=i};return p.clear=function(){h&&(clearTimeout(h),h=null)},p.debounceNextCall=function(t){s=t},p}function sy(t,e,n,i){var r=t[e];if(r){var o=r[iy]||r,a=r[oy];if(r[ry]!==n||a!==i){if(null==n||!i)return t[e]=o;(r=t[e]=ay(o,n,"debounce"===i))[iy]=o,r[oy]=i,r[ry]=n}return r}}function ly(t,e){var n=t[e];n&&n[iy]&&(n.clear&&n.clear(),t[e]=n[iy])}var uy=Oo(),hy={itemStyle:Qo(Oc,!0),lineStyle:Qo(Ac,!0)},cy={lineStyle:"stroke",itemStyle:"fill"};function py(t,e){var n=t.visualStyleMapper||hy[e];return n||(console.warn("Unknown style type '"+e+"'."),hy.itemStyle)}function dy(t,e){var n=t.visualDrawType||cy[e];return n||(console.warn("Unknown style type '"+e+"'."),"fill")}var fy={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=t.getModel(i),o=py(t,i)(r),a=r.getShallow("decal");a&&(n.setVisual("decal",a),a.dirty=!0);var s=dy(t,i),l=o[s],u=W(l)?l:null,h="auto"===o.fill||"auto"===o.stroke;if(!o[s]||u||h){var c=t.getColorFromPalette(t.name,null,e.getSeriesCount());o[s]||(o[s]=c,n.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||W(o.fill)?c:o.fill,o.stroke="auto"===o.stroke||W(o.stroke)?c:o.stroke}if(n.setVisual("style",o),n.setVisual("drawType",s),!e.isSeriesFiltered(t)&&u)return n.setVisual("colorFromPalette",!1),{dataEach:function(e,n){var i=t.getDataParams(n),r=k({},o);r[s]=u(i),e.setItemVisual(n,"style",r)}}}},gy=new Ec,yy={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t)){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=py(t,i),o=n.getVisual("drawType");return{dataEach:n.hasItemOption?function(t,e){var n=t.getRawDataItem(e);if(n&&n[i]){gy.option=n[i];var a=r(gy);k(t.ensureUniqueItemVisual(e,"style"),a),gy.option.decal&&(t.setItemVisual(e,"decal",gy.option.decal),gy.option.decal.dirty=!0),o in a&&t.setItemVisual(e,"colorFromPalette",!1)}}:null}}}},vy={performRawSeries:!0,overallReset:function(t){var e=pt();t.eachSeries((function(t){var n=t.getColorBy();if(!t.isColorBySeries()){var i=t.type+"-"+n,r=e.get(i);r||(r={},e.set(i,r)),uy(t).scope=r}})),t.eachSeries((function(e){if(!e.isColorBySeries()&&!t.isSeriesFiltered(e)){var n=e.getRawData(),i={},r=e.getData(),o=uy(e).scope,a=e.visualStyleAccessPath||"itemStyle",s=dy(e,a);r.each((function(t){var e=r.getRawIndex(t);i[e]=t})),n.each((function(t){var a=i[t];if(r.getItemVisual(a,"colorFromPalette")){var l=r.ensureUniqueItemVisual(a,"style"),u=n.getName(t)||t+"",h=n.count();l[s]=e.getColorFromPalette(u,o,h)}}))}}))}},my=Math.PI;var _y=function(){function t(t,e,n,i){this._stageTaskMap=pt(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}return t.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each((function(t){var e=t.overallTask;e&&e.dirty()}))},t.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,o=i&&i.modDataCount;return{step:r,modBy:null!=o?Math.ceil(o/r):null,modDataCount:o}}},t.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},t.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),r=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,o=t.get("large")&&i>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:r,modDataCount:a,large:o}},t.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=pt();t.eachSeries((function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),e._pipe(t,t.dataTask)}))},t.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;O(this._allHandlers,(function(i){var r=t.get(i.uid)||t.set(i.uid,{});rt(!(i.reset&&i.overallReset),""),i.reset&&this._createSeriesStageTask(i,r,e,n),i.overallReset&&this._createOverallStageTask(i,r,e,n)}),this)},t.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},t.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},t.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},t.prototype._performStageTasks=function(t,e,n,i){i=i||{};var r=!1,o=this;function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}O(t,(function(t,s){if(!i.visualType||i.visualType===t.visualType){var l=o._stageTaskMap.get(t.uid),u=l.seriesTaskMap,h=l.overallTask;if(h){var c,p=h.agentStubMap;p.each((function(t){a(i,t)&&(t.dirty(),c=!0)})),c&&h.dirty(),o.updatePayload(h,n);var d=o.getPerformArgs(h,i.block);p.each((function(t){t.perform(d)})),h.perform(d)&&(r=!0)}else u&&u.each((function(s,l){a(i,s)&&s.dirty();var u=o.getPerformArgs(s,i.block);u.skip=!t.performRawSeries&&e.isSeriesFiltered(s.context.model),o.updatePayload(s,n),s.perform(u)&&(r=!0)}))}})),this.unfinished=r||this.unfinished},t.prototype.performSeriesTasks=function(t){var e;t.eachSeries((function(t){e=t.dataTask.perform()||e})),this.unfinished=e||this.unfinished},t.prototype.plan=function(){this._pipelineMap.each((function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)}))},t.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},t.prototype._createSeriesStageTask=function(t,e,n,i){var r=this,o=e.seriesTaskMap,a=e.seriesTaskMap=pt(),s=t.seriesType,l=t.getTargetSeries;function u(e){var s=e.uid,l=a.set(s,o&&o.get(s)||Ef({plan:My,reset:Ty,count:ky}));l.context={model:e,ecModel:n,api:i,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:r},r._pipe(e,l)}t.createOnAllSeries?n.eachRawSeries(u):s?n.eachRawSeriesByType(s,u):l&&l(n,i).each(u)},t.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||Ef({reset:xy});o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r};var a=o.agentStubMap,s=o.agentStubMap=pt(),l=t.seriesType,u=t.getTargetSeries,h=!0,c=!1;function p(t){var e=t.uid,n=s.set(e,a&&a.get(e)||(c=!0,Ef({reset:wy,onDirty:Sy})));n.context={model:t,overallProgress:h},n.agent=o,n.__block=h,r._pipe(t,n)}rt(!t.createOnAllSeries,""),l?n.eachRawSeriesByType(l,p):u?u(n,i).each(p):(h=!1,O(n.getSeries(),p)),c&&o.dirty()},t.prototype._pipe=function(t,e){var n=t.uid,i=this._pipelineMap.get(n);!i.head&&(i.head=e),i.tail&&i.tail.pipe(e),i.tail=e,e.__idxInPipeline=i.count++,e.__pipeline=i},t.wrapStageHandler=function(t,e){return W(t)&&(t={overallReset:t,seriesType:Dy(t)}),t.uid=Fc("stageHandler"),e&&(t.visualType=e),t},t}();function xy(t){t.overallReset(t.ecModel,t.api,t.payload)}function wy(t){return t.overallProgress&&by}function by(){this.agent.dirty(),this.getDownstream().dirty()}function Sy(){this.agent&&this.agent.dirty()}function My(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function Ty(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=_o(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?R(e,(function(t,e){return Iy(e)})):Cy}var Cy=Iy(0);function Iy(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var o=e.start;o<e.end;o++)r.dataEach(i,o);else r&&r.progress&&r.progress(e,i)}}function ky(t){return t.data.count()}function Dy(t){Ay=null;try{t(Py,Ly)}catch(e){}return Ay}var Ay,Py={},Ly={};function Oy(t,e){for(var n in e.prototype)t[n]=vt}Oy(Py,Od),Oy(Ly,Nd),Py.eachSeriesByType=Py.eachRawSeriesByType=function(t){Ay=t},Py.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Ay=t.subType)};const Ry=_y;var Ny=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const By={color:Ny,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Ny]};var Ey="#B9B8CE",zy="#100C2A",Fy=function(){return{axisLine:{lineStyle:{color:Ey}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},Vy=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Hy={darkMode:!0,color:Vy,backgroundColor:zy,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Ey},pageTextStyle:{color:Ey}},textStyle:{color:Ey},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Ey}},dataZoom:{borderColor:"#71708A",textStyle:{color:Ey},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Ey}},timeline:{lineStyle:{color:Ey},label:{color:Ey},controlStyle:{color:Ey,borderColor:Ey}},calendar:{itemStyle:{color:zy},dayLabel:{color:Ey},monthLabel:{color:Ey},yearLabel:{color:Ey}},timeAxis:Fy(),logAxis:Fy(),valueAxis:Fy(),categoryAxis:Fy(),line:{symbol:"circle"},graph:{color:Vy},gauge:{title:{color:Ey},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Ey},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};Hy.categoryAxis.splitLine.show=!1;const Wy=Hy;var Gy=function(){function t(){}return t.prototype.normalizeQuery=function(t){var e={},n={},i={};if(G(t)){var r=Xo(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var o=["Index","Name","Id"],a={name:1,dataIndex:1,dataType:1};O(t,(function(t,r){for(var s=!1,l=0;l<o.length;l++){var u=o[l],h=r.lastIndexOf(u);if(h>0&&h===r.length-u.length){var c=r.slice(0,h);"data"!==c&&(e.mainType=c,e[u.toLowerCase()]=t,s=!0)}}a.hasOwnProperty(r)&&(n[r]=t,s=!0),s||(i[r]=t)}))}return{cptQuery:e,dataQuery:n,otherQuery:i}},t.prototype.filter=function(t,e){var n=this.eventInfo;if(!n)return!0;var i=n.targetEl,r=n.packedEvent,o=n.model,a=n.view;if(!o||!a)return!0;var s=e.cptQuery,l=e.dataQuery;return u(s,o,"mainType")&&u(s,o,"subType")&&u(s,o,"index","componentIndex")&&u(s,o,"name")&&u(s,o,"id")&&u(l,r,"name")&&u(l,r,"dataIndex")&&u(l,r,"dataType")&&(!a.filterForExposedEvent||a.filterForExposedEvent(t,e.otherQuery,i,r));function u(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},t.prototype.afterTrigger=function(){this.eventInfo=null},t}(),Uy=["symbol","symbolSize","symbolRotate","symbolOffset"],Xy=Uy.concat(["symbolKeepAspect"]),Yy={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData();if(t.legendIcon&&n.setVisual("legendIcon",t.legendIcon),t.hasSymbolVisual){for(var i={},r={},o=!1,a=0;a<Uy.length;a++){var s=Uy[a],l=t.get(s);W(l)?(o=!0,r[s]=l):i[s]=l}if(i.symbol=i.symbol||t.defaultSymbol,n.setVisual(k({legendIcon:t.legendIcon||i.symbol,symbolKeepAspect:t.get("symbolKeepAspect")},i)),!e.isSeriesFiltered(t)){var u=z(r);return{dataEach:o?function(e,n){for(var i=t.getRawValue(n),o=t.getDataParams(n),a=0;a<u.length;a++){var s=u[a];e.setItemVisual(n,s,r[s](i,o))}}:null}}}}},qy={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<Xy.length;i++){var r=Xy[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function Zy(t,e,n){switch(n){case"color":return t.getItemVisual(e,"style")[t.getVisual("drawType")];case"opacity":return t.getItemVisual(e,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getItemVisual(e,n)}}function jy(t,e){switch(e){case"color":return t.getVisual("style")[t.getVisual("drawType")];case"opacity":return t.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getVisual(e)}}function Ky(t,e,n,i){switch(n){case"color":t.ensureUniqueItemVisual(e,"style")[t.getVisual("drawType")]=i,t.setItemVisual(e,"colorFromPalette",!1);break;case"opacity":t.ensureUniqueItemVisual(e,"style").opacity=i;break;case"symbol":case"symbolSize":case"liftZ":t.setItemVisual(e,n,i)}}function $y(t,e){function n(e,n){var i=[];return e.eachComponent({mainType:"series",subType:t,query:n},(function(t){i.push(t.seriesIndex)})),i}O([[t+"ToggleSelect","toggleSelect"],[t+"Select","select"],[t+"UnSelect","unselect"]],(function(t){e(t[0],(function(e,i,r){e=k({},e),r.dispatchAction(k(e,{type:t[1],seriesIndex:n(i,e)}))}))}))}function Qy(t,e,n,i,r){var o=t+e;n.isSilent(o)||i.eachComponent({mainType:"series",subType:"pie"},(function(t){for(var e=t.seriesIndex,i=t.option.selectedMap,a=r.selected,s=0;s<a.length;s++)if(a[s].seriesIndex===e){var l=t.getData(),u=Lo(l,r.fromActionPayload);n.trigger(o,{type:o,seriesId:t.id,name:H(u)?l.getName(u[0]):l.getName(u),selected:G(i)?i:k({},i)})}}))}function Jy(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var tv=Math.round(9*Math.random()),ev="function"==typeof Object.defineProperty,nv=function(){function t(){this._id="__ec_inner_"+tv++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var n=this._guard(t);return ev?Object.defineProperty(n,this._id,{value:e,enumerable:!1,configurable:!0}):n[this._id]=e,this},t.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}();const iv=nv;var rv=Cs.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),ov=Cs.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),av=Cs.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,u=Math.asin(s/a),h=Math.cos(u)*a,c=Math.sin(u),p=Math.cos(u),d=.6*a,f=.7*a;t.moveTo(n-h,l+s),t.arc(n,l,a,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+h-c*d,l+s+p*d,n,i-f,n,i),t.bezierCurveTo(n,i-f,n-h+c*d,l+s+p*d,n-h,l+s),t.closePath()}}),sv=Cs.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),lv={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},uv={};O({line:oh,rect:Hs,roundRect:Hs,square:Hs,circle:Cu,diamond:ov,pin:av,arrow:sv,triangle:rv},(function(t,e){uv[e]=new t}));var hv=Cs.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=Sr(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.position&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=uv[i];r||(r=uv[i="rect"]),lv[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}});function cv(t,e){if("image"!==this.type){var n=this.style;this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw()}}function pv(t,e,n,i,r,o,a){var s,l=0===t.indexOf("empty");return l&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?Gh(t.slice(8),new Ie(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Wh(t.slice(7),{},new Ie(e,n,i,r),a?"center":"cover"):new hv({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=l,s.setColor=cv,o&&s.setColor(o),s}function dv(t){return H(t)||(t=[+t,+t]),[t[0]||0,t[1]||0]}function fv(t,e){if(null!=t)return H(t)||(t=[t,t]),[Xr(t[0],e[0])||0,Xr(tt(t[1],t[0]),e[1])||0]}function gv(t){return isFinite(t)}function yv(t,e,n){for(var i="radial"===e.type?function(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*i+n.x,s=s*r+n.y,l*=o),a=gv(a)?a:.5,s=gv(s)?s:.5,l=l>=0&&gv(l)?l:.5,t.createRadialGradient(a,s,0,a,s,l)}(t,e,n):function(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y),i=gv(i)?i:0,r=gv(r)?r:1,o=gv(o)?o:0,a=gv(a)?a:0,t.createLinearGradient(i,o,r,a)}(t,e,n),r=e.colorStops,o=0;o<r.length;o++)i.addColorStop(r[o].offset,r[o].color);return i}function vv(t){return parseInt(t,10)}function mv(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=n[i]&&"auto"!==n[i])return parseFloat(n[i]);var s=document.defaultView.getComputedStyle(t);return(t[r]||vv(s[i])||vv(t.style[i]))-(vv(s[o])||0)-(vv(s[a])||0)|0}function _v(t){var e,n,i=t.style,r=i.lineDash&&i.lineWidth>0&&(e=i.lineDash,n=i.lineWidth,e&&"solid"!==e&&n>0?"dashed"===e?[4*n,2*n]:"dotted"===e?[n]:X(e)?[e]:H(e)?e:null:null),o=i.lineDashOffset;if(r){var a=i.strokeNoScale&&t.getLineScale?t.getLineScale():1;a&&1!==a&&(r=R(r,(function(t){return t/a})),o/=a)}return[r,o]}var xv=new as(!0);function wv(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function bv(t){return"string"==typeof t&&"none"!==t}function Sv(t){var e=t.fill;return null!=e&&"none"!==e}function Mv(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var n=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n}else t.fill()}function Tv(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var n=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n}else t.stroke()}function Cv(t,e,n){var i=ia(e.image,e.__image,n);if(oa(i)){var r=t.createPattern(i,e.repeat||"repeat");if("function"==typeof DOMMatrix&&r&&r.setTransform){var o=new DOMMatrix;o.translateSelf(e.x||0,e.y||0),o.rotateSelf(0,0,(e.rotation||0)*mt),o.scaleSelf(e.scaleX||1,e.scaleY||1),r.setTransform(o)}return r}}var Iv=["shadowBlur","shadowOffsetX","shadowOffsetY"],kv=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Dv(t,e,n,i,r){var o=!1;if(!i&&e===(n=n||{}))return!1;if(i||e.opacity!==n.opacity){Lv(t,r),o=!0;var a=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(a)?_a.opacity:a}(i||e.blend!==n.blend)&&(o||(Lv(t,r),o=!0),t.globalCompositeOperation=e.blend||_a.blend);for(var s=0;s<Iv.length;s++){var l=Iv[s];(i||e[l]!==n[l])&&(o||(Lv(t,r),o=!0),t[l]=t.dpr*(e[l]||0))}return(i||e.shadowColor!==n.shadowColor)&&(o||(Lv(t,r),o=!0),t.shadowColor=e.shadowColor||_a.shadowColor),o}function Av(t,e,n,i,r){var o=Ov(e,r.inHover),a=i?null:n&&Ov(n,r.inHover)||{};if(o===a)return!1;var s=Dv(t,o,a,i,r);if((i||o.fill!==a.fill)&&(s||(Lv(t,r),s=!0),bv(o.fill)&&(t.fillStyle=o.fill)),(i||o.stroke!==a.stroke)&&(s||(Lv(t,r),s=!0),bv(o.stroke)&&(t.strokeStyle=o.stroke)),(i||o.opacity!==a.opacity)&&(s||(Lv(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var l=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==l&&(s||(Lv(t,r),s=!0),t.lineWidth=l)}for(var u=0;u<kv.length;u++){var h=kv[u],c=h[0];(i||o[c]!==a[c])&&(s||(Lv(t,r),s=!0),t[c]=o[c]||h[1])}return s}function Pv(t,e){var n=e.transform,i=t.dpr||1;n?t.setTransform(i*n[0],i*n[1],i*n[2],i*n[3],i*n[4],i*n[5]):t.setTransform(i,0,0,i,0,0)}function Lv(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function Ov(t,e){return e&&t.__hoverStyle||t.style}function Rv(t,e){Nv(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Nv(t,e,n,i){var r=e.transform;if(!e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1))return e.__dirty&=-2,void(e.__isRendered=!1);var o=e.__clipPaths,s=n.prevElClipPaths,l=!1,u=!1;if(s&&!function(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}(o,s)||(s&&s.length&&(Lv(t,n),t.restore(),u=l=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),o&&o.length&&(Lv(t,n),t.save(),function(t,e,n){for(var i=!1,r=0;r<t.length;r++){var o=t[r];i=i||o.isZeroArea(),Pv(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}n.allClipped=i}(o,t,n),l=!0),n.prevElClipPaths=o),n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var h=n.prevEl;h||(u=l=!0);var c,p,d=e instanceof Cs&&e.autoBatch&&function(t){var e=Sv(t),n=wv(t);return!(t.lineDash||!(+e^+n)||e&&"string"!=typeof t.fill||n&&"string"!=typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}(e.style);l||(c=r,p=h.transform,c&&p?c[0]!==p[0]||c[1]!==p[1]||c[2]!==p[2]||c[3]!==p[3]||c[4]!==p[4]||c[5]!==p[5]:c||p)?(Lv(t,n),Pv(t,e)):d||Lv(t,n);var f=Ov(e,n.inHover);e instanceof Cs?(1!==n.lastDrawType&&(u=!0,n.lastDrawType=1),Av(t,e,h,u,n),d&&(n.batchFill||n.batchStroke)||t.beginPath(),function(t,e,n,i){var r,o=wv(n),a=Sv(n),s=n.strokePercent,l=s<1,u=!e.path;e.silent&&!l||!u||e.createPathProxy();var h=e.path||xv,c=e.__dirty;if(!i){var p=n.fill,d=n.stroke,f=a&&!!p.colorStops,g=o&&!!d.colorStops,y=a&&!!p.image,v=o&&!!d.image,m=void 0,_=void 0,x=void 0,w=void 0,b=void 0;(f||g)&&(b=e.getBoundingRect()),f&&(m=c?yv(t,p,b):e.__canvasFillGradient,e.__canvasFillGradient=m),g&&(_=c?yv(t,d,b):e.__canvasStrokeGradient,e.__canvasStrokeGradient=_),y&&(x=c||!e.__canvasFillPattern?Cv(t,p,e):e.__canvasFillPattern,e.__canvasFillPattern=x),v&&(w=c||!e.__canvasStrokePattern?Cv(t,d,e):e.__canvasStrokePattern,e.__canvasStrokePattern=x),f?t.fillStyle=m:y&&(x?t.fillStyle=x:a=!1),g?t.strokeStyle=_:v&&(w?t.strokeStyle=w:o=!1)}var S,M,T=e.getGlobalScale();h.setScale(T[0],T[1],e.segmentIgnoreThreshold),t.setLineDash&&n.lineDash&&(S=(r=_v(e))[0],M=r[1]);var C=!0;(u||4&c)&&(h.setDPR(t.dpr),l?h.setContext(null):(h.setContext(t),C=!1),h.reset(),e.buildPath(h,e.shape,i),h.toStatic(),e.pathUpdated()),C&&h.rebuildPath(t,l?s:1),S&&(t.setLineDash(S),t.lineDashOffset=M),i||(n.strokeFirst?(o&&Tv(t,n),a&&Mv(t,n)):(a&&Mv(t,n),o&&Tv(t,n))),S&&t.setLineDash([])}(t,e,f,d),d&&(n.batchFill=f.fill||"",n.batchStroke=f.stroke||"")):e instanceof Ds?(3!==n.lastDrawType&&(u=!0,n.lastDrawType=3),Av(t,e,h,u,n),function(t,e,n){var i,r=n.text;if(null!=r&&(r+=""),r){t.font=n.font||a,t.textAlign=n.textAlign,t.textBaseline=n.textBaseline;var o=void 0,s=void 0;t.setLineDash&&n.lineDash&&(o=(i=_v(e))[0],s=i[1]),o&&(t.setLineDash(o),t.lineDashOffset=s),n.strokeFirst?(wv(n)&&t.strokeText(r,n.x,n.y),Sv(n)&&t.fillText(r,n.x,n.y)):(Sv(n)&&t.fillText(r,n.x,n.y),wv(n)&&t.strokeText(r,n.x,n.y)),o&&t.setLineDash([])}}(t,e,f)):e instanceof Os?(2!==n.lastDrawType&&(u=!0,n.lastDrawType=2),function(t,e,n,i,r){Dv(t,Ov(e,r.inHover),n&&Ov(n,r.inHover),i,r)}(t,e,h,u,n),function(t,e,n){var i=e.__image=ia(n.image,e.__image,e,e.onload);if(i&&oa(i)){var r=n.x||0,o=n.y||0,a=e.getWidth(),s=e.getHeight(),l=i.width/i.height;if(null==a&&null!=s?a=s*l:null==s&&null!=a?s=a/l:null==a&&null==s&&(a=i.width,s=i.height),n.sWidth&&n.sHeight){var u=n.sx||0,h=n.sy||0;t.drawImage(i,u,h,n.sWidth,n.sHeight,r,o,a,s)}else if(n.sx&&n.sy){var c=a-(u=n.sx),p=s-(h=n.sy);t.drawImage(i,u,h,c,p,r,o,a,s)}else t.drawImage(i,r,o,a,s)}}(t,e,f)):e.getTemporalDisplayables&&(4!==n.lastDrawType&&(u=!0,n.lastDrawType=4),function(t,e,n){var i=e.getDisplayables(),r=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:n.viewWidth,viewHeight:n.viewHeight,inHover:n.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(h=i[o]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),Nv(t,h,s,o===a-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}for(var l=0,u=r.length;l<u;l++){var h;(h=r[l]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),Nv(t,h,s,l===u-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,n)),d&&i&&Lv(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),n.prevEl=e,e.__dirty=0,e.__isRendered=!0}}var Bv=new iv,Ev=new kn(100),zv=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Fv(t,e){if("none"===t)return null;var n=e.getDevicePixelRatio(),i=e.getZr(),r="svg"===i.painter.type;t.dirty&&Bv.delete(t);var o=Bv.get(t);if(o)return o;var a=D(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});"none"===a.backgroundColor&&(a.backgroundColor=null);var s={repeat:"repeat"};return function(t){for(var e,o=[n],s=!0,l=0;l<zv.length;++l){var u=a[zv[l]];if(null!=u&&!H(u)&&!G(u)&&!X(u)&&"boolean"!=typeof u){s=!1;break}o.push(u)}if(s){e=o.join(",")+(r?"-svg":"");var c=Ev.get(e);c&&(r?t.svgElement=c:t.image=c)}var p,d=Hv(a.dashArrayX),f=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(X(t)){var e=Math.ceil(t);return[e,e]}var n=R(t,(function(t){return Math.ceil(t)}));return t.length%2?n.concat(n):n}(a.dashArrayY),g=Vv(a.symbol),y=(w=d,R(w,(function(t){return Wv(t)}))),v=Wv(f),m=!r&&h.createCanvas(),_=r&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=ho(t,y[e]);var i=1;for(e=0,n=g.length;e<n;++e)i=ho(i,g[e].length);t*=i;var r=v*y.length*g.length;return{width:Math.max(1,Math.min(t,a.maxTileWidth)),height:Math.max(1,Math.min(r,a.maxTileHeight))}}();var w;m&&(m.width=x.width*n,m.height=x.height*n,p=m.getContext("2d"));(function(){p&&(p.clearRect(0,0,m.width,m.height),a.backgroundColor&&(p.fillStyle=a.backgroundColor,p.fillRect(0,0,m.width,m.height)));for(var t=0,e=0;e<f.length;++e)t+=f[e];if(t<=0)return;var o=-v,s=0,l=0,u=0;for(;o<x.height;){if(s%2==0){for(var h=l/2%g.length,c=0,y=0,w=0;c<2*x.width;){var b=0;for(e=0;e<d[u].length;++e)b+=d[u][e];if(b<=0)break;if(y%2==0){var S=.5*(1-a.symbolSize),M=c+d[u][y]*S,T=o+f[s]*S,C=d[u][y]*a.symbolSize,I=f[s]*a.symbolSize,k=w/2%g[h].length;D(M,T,C,I,g[h][k])}c+=d[u][y],++w,++y===d[u].length&&(y=0)}++u===d.length&&(u=0)}o+=f[s],++l,++s===f.length&&(s=0)}function D(t,e,o,s,l){var u=r?1:n,h=pv(l,t*u,e*u,o*u,s*u,a.color,a.symbolKeepAspect);if(r){var c=i.painter.renderOneToVNode(h);c&&_.children.push(c)}else Rv(p,h)}})(),s&&Ev.put(e,m||_);t.image=m,t.svgElement=_,t.svgWidth=x.width,t.svgHeight=x.height}(s),s.rotation=a.rotation,s.scaleX=s.scaleY=r?1:1/n,Bv.set(t,s),t.dirty=!1,s}function Vv(t){if(!t||0===t.length)return[["rect"]];if(G(t))return[[t]];for(var e=!0,n=0;n<t.length;++n)if(!G(t[n])){e=!1;break}if(e)return Vv([t]);var i=[];for(n=0;n<t.length;++n)G(t[n])?i.push([t[n]]):i.push(t[n]);return i}function Hv(t){if(!t||0===t.length)return[[0,0]];if(X(t))return[[r=Math.ceil(t),r]];for(var e=!0,n=0;n<t.length;++n)if(!X(t[n])){e=!1;break}if(e)return Hv([t]);var i=[];for(n=0;n<t.length;++n)if(X(t[n])){var r=Math.ceil(t[n]);i.push([r,r])}else{(r=R(t[n],(function(t){return Math.ceil(t)}))).length%2==1?i.push(r.concat(r)):i.push(r)}return i}function Wv(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}const Gv=new zt;var Uv={};var Xv=2e3,Yv=4500,qv={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:Xv,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:Yv,ARIA:6e3,DECAL:7e3}},Zv="__flagInMainProcess",jv="__pendingUpdate",Kv="__needsUpdateStatus",$v=/^[a-zA-Z0-9_]+$/,Qv="__connectUpdateStatus";function Jv(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(!this.isDisposed())return em(this,t,e);this.id}}function tm(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return em(this,t,e)}}function em(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),zt.prototype[e].apply(t,n)}var nm,im,rm,om,am,sm,lm,um,hm,cm,pm,dm,fm,gm,ym,vm,mm,_m,xm=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n}(zt),wm=xm.prototype;wm.on=tm("on"),wm.off=tm("off");var bm=function(t){function n(e,n,i){var r=t.call(this,new Gy)||this;r._chartsViews=[],r._chartsMap={},r._componentsViews=[],r._componentsMap={},r._pendingActions=[],i=i||{},G(n)&&(n=Am[n]),r._dom=e;i.ssr&&(Fr=function(t){var e=rl(t),n=e.dataIndex;if(null!=n){var i=pt();return i.set("series_index",e.seriesIndex),i.set("data_index",n),e.ssrType&&i.set("ssr_type",e.ssrType),i}});var o=r._zr=Hr(e,{renderer:i.renderer||"canvas",devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height,ssr:i.ssr,useDirtyRect:tt(i.useDirtyRect,!1),useCoarsePointer:tt(i.useCoarsePointer,"auto"),pointerSize:i.pointerSize});r._ssr=i.ssr,r._throttledZrFlush=ay(F(o.flush,o),17),(n=T(n))&&sf(n,!0),r._theme=n,r._locale=function(t){if(G(t)){var e=Uc[t.toUpperCase()]||{};return t===Hc||t===Wc?T(e):C(T(e),T(Uc[Gc]),!1)}return C(T(t),T(Uc[Gc]),!1)}(i.locale||Yc),r._coordSysMgr=new Ed;var a=r._api=ym(r);function s(t,e){return t.__prio-e.__prio}return Ue(Dm,s),Ue(Im,s),r._scheduler=new Ry(r,a,Im,Dm),r._messageCenter=new xm,r._initEvents(),r.resize=F(r.resize,r),o.animation.on("frame",r._onframe,r),cm(o,r),pm(o,r),st(r),r}return e(n,t),n.prototype._onframe=function(){if(!this._disposed){_m(this);var t=this._scheduler;if(this[jv]){var e=this[jv].silent;this[Zv]=!0;try{nm(this),om.update.call(this,null,this[jv].updateParams)}catch(a){throw this[Zv]=!1,this[jv]=null,a}this._zr.flush(),this[Zv]=!1,this[jv]=null,um.call(this,e),hm.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),sm(this,i),t.performVisualTasks(i),gm(this,this._model,r,"remain",{}),n-=+new Date-o}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},n.prototype.getDom=function(){return this._dom},n.prototype.getId=function(){return this.id},n.prototype.getZr=function(){return this._zr},n.prototype.isSSR=function(){return this._ssr},n.prototype.setOption=function(t,e,n){if(!this[Zv])if(this._disposed)this.id;else{var i,r,o;if(Y(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[Zv]=!0,!this._model||e){var a=new Vd(this._api),s=this._theme,l=this._model=new Od;l.scheduler=this._scheduler,l.ssr=this._ssr,l.init(null,null,null,s,this._locale,a)}this._model.setOption(t,{replaceMerge:r},km);var u={seriesTransition:o,optionChanged:!0};if(n)this[jv]={silent:i,updateParams:u},this[Zv]=!1,this.getZr().wakeUp();else{try{nm(this),om.update.call(this,null,u)}catch(h){throw this[jv]=null,this[Zv]=!1,h}this._ssr||this._zr.flush(),this[jv]=null,this[Zv]=!1,um.call(this,i),hm.call(this,i)}}},n.prototype.setTheme=function(){},n.prototype.getModel=function(){return this._model},n.prototype.getOption=function(){return this._model&&this._model.getOption()},n.prototype.getWidth=function(){return this._zr.getWidth()},n.prototype.getHeight=function(){return this._zr.getHeight()},n.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||r.hasGlobalWindow&&window.devicePixelRatio||1},n.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},n.prototype.renderToCanvas=function(t){return t=t||{},this._zr.painter.getRenderedCanvas({backgroundColor:t.backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},n.prototype.renderToSVGString=function(t){return t=t||{},this._zr.painter.renderToString({useViewBox:t.useViewBox})},n.prototype.getSvgDataURL=function(){if(r.svgSupported){var t=this._zr;return O(t.storage.getDisplayList(),(function(t){t.stopAnimation(null,!0)})),t.painter.toDataURL()}},n.prototype.getDataURL=function(t){if(!this._disposed){var e=(t=t||{}).excludeComponents,n=this._model,i=[],r=this;O(e,(function(t){n.eachComponent({mainType:t},(function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)}))}));var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return O(i,(function(t){t.group.ignore=!1})),o}this.id},n.prototype.getConnectedDataURL=function(t){if(!this._disposed){var e="svg"===t.type,n=this.group,i=Math.min,r=Math.max,o=1/0;if(Om[n]){var a=o,s=o,l=-1/0,u=-1/0,c=[],p=t&&t.pixelRatio||this.getDevicePixelRatio();O(Lm,(function(o,h){if(o.group===n){var p=e?o.getZr().painter.getSvgDom().innerHTML:o.renderToCanvas(T(t)),d=o.getDom().getBoundingClientRect();a=i(d.left,a),s=i(d.top,s),l=r(d.right,l),u=r(d.bottom,u),c.push({dom:p,left:d.left,top:d.top})}}));var d=(l*=p)-(a*=p),f=(u*=p)-(s*=p),g=h.createCanvas(),y=Hr(g,{renderer:e?"svg":"canvas"});if(y.resize({width:d,height:f}),e){var v="";return O(c,(function(t){var e=t.left-a,n=t.top-s;v+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"})),y.painter.getSvgRoot().innerHTML=v,t.connectedBackgroundColor&&y.painter.setBackgroundColor(t.connectedBackgroundColor),y.refreshImmediately(),y.painter.toDataURL()}return t.connectedBackgroundColor&&y.add(new Hs({shape:{x:0,y:0,width:d,height:f},style:{fill:t.connectedBackgroundColor}})),O(c,(function(t){var e=new Os({style:{x:t.left*p-a,y:t.top*p-s,image:t.dom}});y.add(e)})),y.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}this.id},n.prototype.convertToPixel=function(t,e){return am(this,"convertToPixel",t,e)},n.prototype.convertFromPixel=function(t,e){return am(this,"convertFromPixel",t,e)},n.prototype.containPixel=function(t,e){var n;if(!this._disposed)return O(No(this._model,t),(function(t,i){i.indexOf("Models")>=0&&O(t,(function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n=n||!!r.containPoint(e);else if("seriesModels"===i){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n=n||o.containPoint(e,t))}}),this)}),this),!!n;this.id},n.prototype.getVisual=function(t,e){var n=No(this._model,t,{defaultMainType:"series"}),i=n.seriesModel.getData(),r=n.hasOwnProperty("dataIndexInside")?n.dataIndexInside:n.hasOwnProperty("dataIndex")?i.indexOfRawIndex(n.dataIndex):null;return null!=r?Zy(i,r,e):jy(i,e)},n.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},n.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},n.prototype._initEvents=function(){var t,e,n,i=this;O(Mm,(function(t){var e=function(e){var n,r=i.getModel(),o=e.target;if("globalout"===t?n={}:o&&Jy(o,(function(t){var e=rl(t);if(e&&null!=e.dataIndex){var i=e.dataModel||r.getSeriesByIndex(e.seriesIndex);return n=i&&i.getDataParams(e.dataIndex,e.dataType,o)||{},!0}if(e.eventData)return n=k({},e.eventData),!0}),!0),n){var a=n.componentType,s=n.componentIndex;"markLine"!==a&&"markPoint"!==a&&"markArea"!==a||(a="series",s=n.seriesIndex);var l=a&&null!=s&&r.getComponent(a,s),u=l&&i["series"===l.mainType?"_chartsMap":"_componentsMap"][l.__viewId];n.event=e,n.type=t,i._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:l,view:u},i.trigger(t,n)}};e.zrEventfulCallAtLast=!0,i._zr.on(t,e,i)})),O(Cm,(function(t,e){i._messageCenter.on(e,(function(t){this.trigger(e,t)}),i)})),O(["selectchanged"],(function(t){i._messageCenter.on(t,(function(e){this.trigger(t,e)}),i)})),t=this._messageCenter,e=this,n=this._api,t.on("selectchanged",(function(t){var i=n.getModel();t.isFromClick?(Qy("map","selectchanged",e,i,t),Qy("pie","selectchanged",e,i,t)):"select"===t.fromAction?(Qy("map","selected",e,i,t),Qy("pie","selected",e,i,t)):"unselect"===t.fromAction&&(Qy("map","unselected",e,i,t),Qy("pie","unselected",e,i,t))}))},n.prototype.isDisposed=function(){return this._disposed},n.prototype.clear=function(){this._disposed?this.id:this.setOption({series:[]},!0)},n.prototype.dispose=function(){if(this._disposed)this.id;else{this._disposed=!0,this.getDom()&&Vo(this.getDom(),Nm,"");var t=this,e=t._api,n=t._model;O(t._componentsViews,(function(t){t.dispose(n,e)})),O(t._chartsViews,(function(t){t.dispose(n,e)})),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete Lm[t.id]}},n.prototype.resize=function(t){if(!this[Zv])if(this._disposed)this.id;else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[jv]&&(null==i&&(i=this[jv].silent),n=!0,this[jv]=null),this[Zv]=!0;try{n&&nm(this),om.update.call(this,{type:"resize",animation:k({duration:0},t&&t.animation)})}catch(r){throw this[Zv]=!1,r}this[Zv]=!1,um.call(this,i),hm.call(this,i)}}},n.prototype.showLoading=function(t,e){if(this._disposed)this.id;else if(Y(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Pm[t]){var n=Pm[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},n.prototype.hideLoading=function(){this._disposed?this.id:(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},n.prototype.makeActionFromEvent=function(t){var e=k({},t);return e.type=Cm[t.type],e},n.prototype.dispatchAction=function(t,e){if(this._disposed)this.id;else if(Y(e)||(e={silent:!!e}),Tm[t.type]&&this._model)if(this[Zv])this._pendingActions.push(t);else{var n=e.silent;lm.call(this,t,n);var i=e.flush;i?this._zr.flush():!1!==i&&r.browser.weChat&&this._throttledZrFlush(),um.call(this,n),hm.call(this,n)}},n.prototype.updateLabelLayout=function(){Gv.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},n.prototype.appendData=function(t){if(this._disposed)this.id;else{var e=t.seriesIndex;this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp()}},n.internalField=function(){function t(t){t.clearColorPalette(),t.eachSeries((function(t){t.clearColorPalette()}))}function n(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),2===t.hoverState&&t.states.emphasis?e.push("emphasis"):1===t.hoverState&&t.states.blur&&e.push("blur"),t.useStates(e)}function i(t,e){if(!t.preventAutoZ){var n=t.get("z")||0,i=t.get("zlevel")||0;e.eachRendered((function(t){return o(t,n,i,-1/0),!0}))}}function o(t,e,n,i){var r=t.getTextContent(),a=t.getTextGuideLine();if(t.isGroup)for(var s=t.childrenRef(),l=0;l<s.length;l++)i=Math.max(o(s[l],e,n,i),i);else t.z=e,t.zlevel=n,i=Math.max(t.z2,i);if(r&&(r.z=e,r.zlevel=n,isFinite(i)&&(r.z2=i+2)),a){var u=t.textGuideLineConfig;a.z=e,a.zlevel=n,isFinite(i)&&(a.z2=i+(u&&u.showAbove?1:-1))}return i}function a(t,e){e.eachRendered((function(t){if(!Ah(t)){var e=t.getTextContent(),n=t.getTextGuideLine();t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null)}}))}function s(t,e){var i=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),o=i.get("duration"),a=o>0?{duration:o,delay:i.get("delay"),easing:i.get("easing")}:null;e.eachRendered((function(t){if(t.states&&t.states.emphasis){if(Ah(t))return;if(t instanceof Cs&&function(t){var e=ll(t);e.normalFill=t.style.fill,e.normalStroke=t.style.stroke;var n=t.states.select||{};e.selectFill=n.style&&n.style.fill||null,e.selectStroke=n.style&&n.style.stroke||null}(t),t.__dirty){var e=t.prevStates;e&&t.useStates(e)}if(r){t.stateTransition=a;var i=t.getTextContent(),o=t.getTextGuideLine();i&&(i.stateTransition=a),o&&(o.stateTransition=a)}t.__dirty&&n(t)}}))}nm=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),im(t,!0),im(t,!1),e.plan()},im=function(t,e){for(var n=t._model,i=t._scheduler,r=e?t._componentsViews:t._chartsViews,o=e?t._componentsMap:t._chartsMap,a=t._zr,s=t._api,l=0;l<r.length;l++)r[l].__alive=!1;function u(t){var l=t.__requireNewView;t.__requireNewView=!1;var u="_ec_"+t.id+"_"+t.type,h=!l&&o[u];if(!h){var c=Xo(t.type);(h=new(e?Yg.getClass(c.main,c.sub):ny.getClass(c.sub))).init(n,s),o[u]=h,r.push(h),a.add(h.group)}t.__viewId=h.__id=u,h.__alive=!0,h.__model=t,h.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!e&&i.prepareView(h,t,n,s)}e?n.eachComponent((function(t,e){"series"!==t&&u(e)})):n.eachSeries(u);for(l=0;l<r.length;){var h=r[l];h.__alive?l++:(!e&&h.renderTask.dispose(),a.remove(h.group),h.dispose(n,s),r.splice(l,1),o[h.__id]===h&&delete o[h.__id],h.__id=h.group.__ecComponentInfo=null)}},rm=function(t,e,n,i,r){var o=t._model;if(o.setUpdatePayload(n),i){var a={};a[i+"Id"]=n[i+"Id"],a[i+"Index"]=n[i+"Index"],a[i+"Name"]=n[i+"Name"];var s={mainType:i,query:a};r&&(s.subType=r);var l,u=n.excludeSeriesId;null!=u&&(l=pt(),O(_o(u),(function(t){var e=Io(t,null);null!=e&&l.set(e,!0)}))),o&&o.eachComponent(s,(function(e){if(!(l&&null!=l.get(e.id)))if(iu(n))if(e instanceof Ug)n.type!==fl||n.notBlur||e.get(["emphasis","disabled"])||function(t,e,n){var i=t.seriesIndex,r=t.getData(e.dataType);if(r){var o=Lo(r,e);o=(H(o)?o[0]:o)||0;var a=r.getItemGraphicEl(o);if(!a)for(var s=r.count(),l=0;!a&&l<s;)a=r.getItemGraphicEl(l++);if(a){var u=rl(a);Wl(i,u.focus,u.blurScope,n)}else{var h=t.get(["emphasis","focus"]),c=t.get(["emphasis","blurScope"]);null!=h&&Wl(i,h,c,n)}}}(e,n,t._api);else{var i=Ul(e.mainType,e.componentIndex,n.name,t._api),r=i.focusSelf,o=i.dispatchers;n.type===fl&&r&&!n.notBlur&&Gl(e.mainType,e.componentIndex,t._api),o&&O(o,(function(t){n.type===fl?Rl(t):Nl(t)}))}else nu(n)&&e instanceof Ug&&(!function(t,e){if(nu(e)){var n=e.dataType,i=Lo(t.getData(n),e);H(i)||(i=[i]),t[e.type===ml?"toggleSelect":e.type===yl?"select":"unselect"](i,n)}}(e,n,t._api),Xl(e),mm(t))}),t),o&&o.eachComponent(s,(function(e){l&&null!=l.get(e.id)||h(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])}),t)}else O([].concat(t._componentsViews).concat(t._chartsViews),h);function h(i){i&&i.__alive&&i[e]&&i[e](i.__model,o,t._api,n)}},om={prepareAndUpdate:function(t){nm(this),om.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(e,n){var i=this._model,r=this._api,o=this._zr,a=this._coordSysMgr,s=this._scheduler;if(i){i.setUpdatePayload(e),s.restoreData(i,e),s.performSeriesTasks(i),a.create(i,r),s.performDataProcessorTasks(i,e),sm(this,i),a.update(i,r),t(i),s.performVisualTasks(i,e),dm(this,i,r,e,n);var l=i.get("backgroundColor")||"transparent",u=i.get("darkMode");o.setBackgroundColor(l),null!=u&&"auto"!==u&&o.setDarkMode(u),Gv.trigger("afterupdate",i,r)}},updateTransform:function(e){var n=this,i=this._model,r=this._api;if(i){i.setUpdatePayload(e);var o=[];i.eachComponent((function(t,a){if("series"!==t){var s=n.getViewOfComponentModel(a);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(a,i,r,e);l&&l.update&&o.push(s)}else o.push(s)}}));var a=pt();i.eachSeries((function(t){var o=n._chartsMap[t.__viewId];if(o.updateTransform){var s=o.updateTransform(t,i,r,e);s&&s.update&&a.set(t.uid,1)}else a.set(t.uid,1)})),t(i),this._scheduler.performVisualTasks(i,e,{setDirty:!0,dirtyMap:a}),gm(this,i,r,e,{},a),Gv.trigger("afterupdate",i,r)}},updateView:function(e){var n=this._model;n&&(n.setUpdatePayload(e),ny.markUpdateMethod(e,"updateView"),t(n),this._scheduler.performVisualTasks(n,e,{setDirty:!0}),dm(this,n,this._api,e,{}),Gv.trigger("afterupdate",n,this._api))},updateVisual:function(e){var n=this,i=this._model;i&&(i.setUpdatePayload(e),i.eachSeries((function(t){t.getData().clearAllVisual()})),ny.markUpdateMethod(e,"updateVisual"),t(i),this._scheduler.performVisualTasks(i,e,{visualType:"visual",setDirty:!0}),i.eachComponent((function(t,r){if("series"!==t){var o=n.getViewOfComponentModel(r);o&&o.__alive&&o.updateVisual(r,i,n._api,e)}})),i.eachSeries((function(t){n._chartsMap[t.__viewId].updateVisual(t,i,n._api,e)})),Gv.trigger("afterupdate",i,this._api))},updateLayout:function(t){om.update.call(this,t)}},am=function(t,e,n,i){if(t._disposed)t.id;else for(var r,o=t._model,a=t._coordSysMgr.getCoordinateSystems(),s=No(o,n),l=0;l<a.length;l++){var u=a[l];if(u[e]&&null!=(r=u[e](o,s,i)))return r}},sm=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries((function(t){i.updateStreamModes(t,n[t.__viewId])}))},lm=function(t,e){var n=this,i=this.getModel(),r=t.type,o=t.escapeConnect,a=Tm[r],s=a.actionInfo,l=(s.update||"update").split(":"),u=l.pop(),h=null!=l[0]&&Xo(l[0]);this[Zv]=!0;var c=[t],p=!1;t.batch&&(p=!0,c=R(t.batch,(function(e){return(e=D(k({},e),t)).batch=null,e})));var d,f=[],g=nu(t),y=iu(t);if(y&&Hl(this._api),O(c,(function(e){if((d=(d=a.action(e,n._model,n._api))||k({},e)).type=s.event||d.type,f.push(d),y){var i=Bo(t),r=i.queryOptionMap,o=i.mainTypeSpecified?r.keys()[0]:"series";rm(n,u,e,o),mm(n)}else g?(rm(n,u,e,"series"),mm(n)):h&&rm(n,u,e,h.main,h.sub)})),"none"!==u&&!y&&!g&&!h)try{this[jv]?(nm(this),om.update.call(this,t),this[jv]=null):om[u].call(this,t)}catch(_){throw this[Zv]=!1,_}if(d=p?{type:s.event||r,escapeConnect:o,batch:f}:f[0],this[Zv]=!1,!e){var v=this._messageCenter;if(v.trigger(d.type,d),g){var m={type:"selectchanged",escapeConnect:o,selected:Yl(i),isFromClick:t.isFromClick||!1,fromAction:t.type,fromActionPayload:t};v.trigger(m.type,m)}}},um=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();lm.call(this,n,t)}},hm=function(t){!t&&this.trigger("updated")},cm=function(t,e){t.on("rendered",(function(n){e.trigger("rendered",n),!t.animation.isFinished()||e[jv]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")}))},pm=function(t,e){t.on("mouseover",(function(t){var n=Jy(t.target,tu);n&&(!function(t,e,n){var i=rl(t),r=Ul(i.componentMainType,i.componentIndex,i.componentHighDownName,n),o=r.dispatchers,a=r.focusSelf;o?(a&&Gl(i.componentMainType,i.componentIndex,n),O(o,(function(t){return Ll(t,e)}))):(Wl(i.seriesIndex,i.focus,i.blurScope,n),"self"===i.focus&&Gl(i.componentMainType,i.componentIndex,n),Ll(t,e))}(n,t,e._api),mm(e))})).on("mouseout",(function(t){var n=Jy(t.target,tu);n&&(!function(t,e,n){Hl(n);var i=rl(t),r=Ul(i.componentMainType,i.componentIndex,i.componentHighDownName,n).dispatchers;r?O(r,(function(t){return Ol(t,e)})):Ol(t,e)}(n,t,e._api),mm(e))})).on("click",(function(t){var n=Jy(t.target,(function(t){return null!=rl(t).dataIndex}),!0);if(n){var i=n.selected?"unselect":"select",r=rl(n);e._api.dispatchAction({type:i,dataType:r.dataType,dataIndexInside:r.dataIndex,seriesIndex:r.seriesIndex,isFromClick:!0})}}))},dm=function(t,e,n,i,r){!function(t){var e=[],n=[],i=!1;if(t.eachComponent((function(t,r){var o=r.get("zlevel")||0,a=r.get("z")||0,s=r.getZLevelKey();i=i||!!s,("series"===t?n:e).push({zlevel:o,z:a,idx:r.componentIndex,type:t,key:s})})),i){var r,o,a=e.concat(n);Ue(a,(function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel})),O(a,(function(e){var n=t.getComponent(e.type,e.idx),i=e.zlevel,a=e.key;null!=r&&(i=Math.max(r,i)),a?(i===r&&a!==o&&i++,o=a):o&&(i===r&&i++,o=""),r=i,n.setZLevel(i)}))}}(e),fm(t,e,n,i,r),O(t._chartsViews,(function(t){t.__alive=!1})),gm(t,e,n,i,r),O(t._chartsViews,(function(t){t.__alive||t.remove(e,n)}))},fm=function(t,e,n,r,o,l){O(l||t._componentsViews,(function(t){var o=t.__model;a(o,t),t.render(o,e,n,r),i(o,t),s(o,t)}))},gm=function(t,e,n,o,l,u){var h=t._scheduler;l=k(l||{},{updatedSeries:e.getSeries()}),Gv.trigger("series:beforeupdate",e,n,l);var c=!1;e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var i=n.renderTask;h.updatePayload(i,o),a(e,n),u&&u.get(e.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!e.get("silent"),function(t,e){var n=t.get("blendMode")||null;e.eachRendered((function(t){t.isGroup||(t.style.blend=n)}))}(e,n),Xl(e)})),h.unfinished=c||h.unfinished,Gv.trigger("series:layoutlabels",e,n,l),Gv.trigger("series:transition",e,n,l),e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];i(e,n),s(e,n)})),function(t,e){var n=t._zr,i=n.storage,o=0;i.traverse((function(t){t.isGroup||o++})),o>e.get("hoverLayerThreshold")&&!r.node&&!r.worker&&e.eachSeries((function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.eachRendered((function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)}))}}))}(t,e),Gv.trigger("series:afterupdate",e,n,l)},mm=function(t){t[Kv]=!0,t.getZr().wakeUp()},_m=function(t){t[Kv]&&(t.getZr().storage.traverse((function(t){Ah(t)||n(t)})),t[Kv]=!1)},ym=function(t){return new(function(n){function i(){return null!==n&&n.apply(this,arguments)||this}return e(i,n),i.prototype.getCoordinateSystems=function(){return t._coordSysMgr.getCoordinateSystems()},i.prototype.getComponentByElement=function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}},i.prototype.enterEmphasis=function(e,n){Rl(e,n),mm(t)},i.prototype.leaveEmphasis=function(e,n){Nl(e,n),mm(t)},i.prototype.enterBlur=function(e){Bl(e),mm(t)},i.prototype.leaveBlur=function(e){El(e),mm(t)},i.prototype.enterSelect=function(e){zl(e),mm(t)},i.prototype.leaveSelect=function(e){Fl(e),mm(t)},i.prototype.getModel=function(){return t.getModel()},i.prototype.getViewOfComponentModel=function(e){return t.getViewOfComponentModel(e)},i.prototype.getViewOfSeriesModel=function(e){return t.getViewOfSeriesModel(e)},i}(Nd))(t)},vm=function(t){function e(t,e){for(var n=0;n<t.length;n++){t[n][Qv]=e}}O(Cm,(function(n,i){t._messageCenter.on(i,(function(n){if(Om[t.group]&&0!==t[Qv]){if(n&&n.escapeConnect)return;var i=t.makeActionFromEvent(n),r=[];O(Lm,(function(e){e!==t&&e.group===t.group&&r.push(e)})),e(r,0),O(r,(function(t){1!==t[Qv]&&t.dispatchAction(i)})),e(r,2)}}))}))}}(),n}(zt),Sm=bm.prototype;Sm.on=Jv("on"),Sm.off=Jv("off"),Sm.one=function(t,e,n){var i=this;this.on.call(this,t,(function n(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e&&e.apply&&e.apply(this,r),i.off(t,n)}),n)};var Mm=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var Tm={},Cm={},Im=[],km=[],Dm=[],Am={},Pm={},Lm={},Om={},Rm=+new Date-0,Nm="_echarts_instance_";function Bm(t,e,n){var i=!(n&&n.ssr);if(i){var r=function(t){return Lm[function(t,e){return t.getAttribute?t.getAttribute(e):t[e]}(t,Nm)]}(t);if(r)return r}var o=new bm(t,e,n);return o.id="ec_"+Rm++,Lm[o.id]=o,i&&Vo(t,Nm,o.id),vm(o),Gv.trigger("afterinit",o),o}function Em(t,e){Am[t]=e}function zm(t){A(km,t)<0&&km.push(t)}function Fm(t,e){Um(Im,t,e,2e3)}function Vm(t,e){Gv.on(t,e)}function Hm(t,e,n){W(e)&&(n=e,e="");var i=Y(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,Cm[e]||(rt($v.test(i)&&$v.test(e)),Tm[i]||(Tm[i]={action:n,actionInfo:t}),Cm[e]=i)}function Wm(t,e){Um(Dm,t,e,3e3,"visual")}var Gm=[];function Um(t,e,n,i,r){if((W(e)||Y(e))&&(n=e,e=i),!(A(Gm,n)>=0)){Gm.push(n);var o=Ry.wrapStageHandler(n,r);o.__prio=e,o.__raw=n,t.push(o)}}function Xm(t,e){Pm[t]=e}var Ym=function(t){var e=(t=T(t)).type;e||go("");var n=e.split(":");2!==n.length&&go("");var i=!1;"echarts"===n[0]&&(e=n[1],i=!0),t.__isBuiltIn=i,Jf.set(e,t)};Wm(Xv,fy),Wm(Yv,yy),Wm(Yv,vy),Wm(Xv,Yy),Wm(Yv,qy),Wm(7e3,(function(t,e){t.eachRawSeries((function(n){if(!t.isSeriesFiltered(n)){var i=n.getData();i.hasItemVisual()&&i.each((function(t){var n=i.getItemVisual(t,"decal");n&&(i.ensureUniqueItemVisual(t,"style").decal=Fv(n,e))}));var r=i.getVisual("decal");if(r)i.getVisual("style").decal=Fv(r,e)}}))})),zm(sf),Fm(900,(function(t){var e=pt();t.eachSeries((function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),o={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!o.stackedDimension||!o.isStackedByIndex&&!o.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(o)}})),e.each(lf)})),Xm("default",(function(t,e){D(e=e||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var n=new Br,i=new Hs({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});n.add(i);var r,o=new il({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001}),a=new Hs({style:{fill:"none"},textContent:o,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});return n.add(a),e.showSpinner&&((r=new dh({shape:{startAngle:-my/2,endAngle:-my/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*my/2}).start("circularInOut"),r.animateShape(!0).when(1e3,{startAngle:3*my/2}).delay(300).start("circularInOut"),n.add(r)),n.resize=function(){var n=o.getBoundingRect().width,s=e.showSpinner?e.spinnerRadius:0,l=(t.getWidth()-2*s-(e.showSpinner&&n?10:0)-n)/2-(e.showSpinner&&n?0:5+n/2)+(e.showSpinner?0:n/2)+(n?0:s),u=t.getHeight()/2;e.showSpinner&&r.setShape({cx:l,cy:u}),a.setShape({x:l-s,y:u-s,width:2*s,height:2*s}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},n.resize(),n})),Hm({type:fl,event:fl,update:fl},vt),Hm({type:gl,event:gl,update:gl},vt),Hm({type:yl,event:yl,update:yl},vt),Hm({type:vl,event:vl,update:vl},vt),Hm({type:ml,event:ml,update:ml},vt),Em("light",By),Em("dark",Wy);var qm=[],Zm={registerPreprocessor:zm,registerProcessor:Fm,registerPostInit:function(t){Vm("afterinit",t)},registerPostUpdate:function(t){Vm("afterupdate",t)},registerUpdateLifecycle:Vm,registerAction:Hm,registerCoordinateSystem:function(t,e){Ed.register(t,e)},registerLayout:function(t,e){Um(Dm,t,e,1e3,"layout")},registerVisual:Wm,registerTransform:Ym,registerLoading:Xm,registerMap:function(t,e,n){var i=Uv["registerMap"];i&&i(t,e,n)},registerImpl:function(t,e){Uv[t]=e},PRIORITY:qv,ComponentModel:Kp,ComponentView:Yg,SeriesModel:Ug,ChartView:ny,registerComponentModel:function(t){Kp.registerClass(t)},registerComponentView:function(t){Yg.registerClass(t)},registerSeriesModel:function(t){Ug.registerClass(t)},registerChartView:function(t){ny.registerClass(t)},registerSubTypeDefaulter:function(t,e){Kp.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){var n;n=e,Er[t]=n}};function jm(t){H(t)?O(t,(function(t){jm(t)})):A(qm,t)>=0||(qm.push(t),W(t)&&(t={install:t}),t.install(Zm))}function Km(t){return null==t?0:t.length||1}function $m(t){return t}const Qm=function(){function t(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||$m,this._newKeyGetter=i||$m,this.context=r,this._diffModeMultiple="multiple"===o}return t.prototype.add=function(t){return this._add=t,this},t.prototype.update=function(t){return this._update=t,this},t.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},t.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},t.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},t.prototype.remove=function(t){return this._remove=t,this},t.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},t.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a=i[o],s=n[a],l=Km(s);if(l>1){var u=s.shift();1===s.length&&(n[a]=s[0]),this._update&&this._update(u,o)}else 1===l?(n[a]=null,this._update&&this._update(s,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},t.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=Km(l),c=Km(u);if(h>1&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&c>1)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(h>1)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},t.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=Km(r);if(o>1)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},t.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a="_ec_"+this[i](t[o],o);if(r||(n[o]=a),e){var s=e[a],l=Km(s);0===l?(e[a]=o,r&&n.push(a)):1===l?e[a]=[s,o]:s.push(o)}}},t}();var Jm=function(){function t(t,e){this._encode=t,this._schema=e}return t.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},t.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},t}();function t_(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function e_(t){return"category"===t?"ordinal":"time"===t?"time":"float"}const n_=function(t){this.otherDims={},null!=t&&k(this,t)};var i_=Oo(),r_={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},o_=function(){function t(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return t.prototype.isDimensionOmitted=function(){return this._dimOmitted},t.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=l_(this.source)))},t.prototype.getSourceDimensionIndex=function(t){return tt(this._dimNameMap.get(t),-1)},t.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},t.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=wf(this.source),n=!u_(t),i="",r=[],o=0,a=0;o<t;o++){var s=void 0,l=void 0,u=void 0,h=this.dimensions[a];if(h&&h.storeDimIndex===o)s=e?h.name:null,l=h.type,u=h.ordinalMeta,a++;else{var c=this.getSourceDimension(o);c&&(s=e?c.name:null,l=c.type)}r.push({property:s,type:l,ordinalMeta:u}),!e||null==s||h&&h.isCalculationCoord||(i+=n?s.replace(/\`/g,"`1").replace(/\$/g,"`2"):s),i+="$",i+=r_[l]||"f",u&&(i+=u.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},t.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];if(r&&r.storeDimIndex===e)r.isCalculationCoord||(i=r.name),n++;else{var o=this.getSourceDimension(e);o&&(i=o.name)}t.push(i)}return t},t.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},t}();function a_(t){return t instanceof o_}function s_(t){for(var e=pt(),n=0;n<(t||[]).length;n++){var i=t[n],r=Y(i)?i.name:i;null!=r&&null==e.get(r)&&e.set(r,n)}return e}function l_(t){var e=i_(t);return e.dimNameMap||(e.dimNameMap=s_(t.dimensionsDefine))}function u_(t){return t>30}var h_,c_,p_,d_,f_,g_,y_,v_=Y,m_=R,__="undefined"==typeof Int32Array?Array:Int32Array,x_=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],w_=["_approximateExtent"],b_=function(){function t(t,e){var n;this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var i=!1;a_(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(i=!0,n=t),n=n||["x","y"];for(var r={},o=[],a={},s=!1,l={},u=0;u<n.length;u++){var h=n[u],c=G(h)?new n_({name:h}):h instanceof n_?h:new n_(h),p=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0);var d=c.otherDims=c.otherDims||{};o.push(p),r[p]=c,null!=l[p]&&(s=!0),c.createInvertedIndices&&(a[p]=[]),0===d.itemName&&(this._nameDimIdx=u),0===d.itemId&&(this._idDimIdx=u),i&&(c.storeDimIndex=u)}if(this.dimensions=o,this._dimInfos=r,this._initGetDimensionInfo(s),this.hostModel=e,this._invertedIndicesMap=a,this._dimOmitted){var f=this._dimIdxToName=pt();O(o,(function(t){f.set(r[t].storeDimIndex,t)}))}}return t.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(null==e)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var n=this._dimIdxToName.get(e);if(null!=n)return n;var i=this._schema.getSourceDimension(e);return i?i.name:void 0},t.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(null!=e)return e;if(null==t)return-1;var n=this._getDimInfo(t);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},t.prototype._recognizeDimIndex=function(t){if(X(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},t.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},t.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},t.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},t.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},t.prototype.mapDimension=function(t,e){var n=this._dimSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return i?i[e]:null},t.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},t.prototype.getStore=function(){return this._store},t.prototype.initData=function(t,e,n){var i,r=this;if(t instanceof cg&&(i=t),!i){var o=this.dimensions,a=gf(t)||L(t)?new bf(t,o.length):t;i=new cg;var s=m_(o,(function(t){return{type:r._dimInfos[t].type,property:t}}));i.initData(a,s,n)}this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=function(t,e){var n={},i=n.encode={},r=pt(),o=[],a=[],s={};O(t.dimensions,(function(e){var n,l=t.getDimensionInfo(e),u=l.coordDim;if(u){var h=l.coordDimIndex;t_(i,u)[h]=e,l.isExtraCoord||(r.set(u,1),"ordinal"!==(n=l.type)&&"time"!==n&&(o[0]=e),t_(s,u)[h]=t.getDimensionIndex(l.name)),l.defaultTooltip&&a.push(e)}td.each((function(t,e){var n=t_(i,e),r=l.otherDims[e];null!=r&&!1!==r&&(n[r]=l.name)}))}));var l=[],u={};r.each((function(t,e){var n=i[e];u[e]=n[0],l=l.concat(n)})),n.dataDimsOnCoord=l,n.dataDimIndicesOnCoord=R(l,(function(e){return t.getDimensionInfo(e).storeDimIndex})),n.encodeFirstDimNotExtra=u;var h=i.label;h&&h.length&&(o=h.slice());var c=i.tooltip;return c&&c.length?a=c.slice():a.length||(a=o.slice()),i.defaultedLabel=o,i.defaultedTooltip=a,n.userOutput=new Jm(s,e),n}(this,this._schema),this.userOutput=this._dimSummary.userOutput},t.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},t.prototype.appendValues=function(t,e){var n=this._store.appendValues(t,e&&e.length),i=n.start,r=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var a=i;a<r;a++){var s=a-i;this._nameList[a]=e[s],o&&y_(this,a)}},t.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},t.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==od&&!t.fillStorage},t.prototype._doInit=function(t,e){if(!(t>=e)){var n=this._store.getProvider();this._updateOrdinalMeta();var i=this._nameList,r=this._idList;if(n.getSource().sourceFormat===ed&&!n.pure)for(var o=[],a=t;a<e;a++){var s=n.getItem(a,o);if(!this.hasItemOption&&So(s)&&(this.hasItemOption=!0),s){var l=s.name;null==i[a]&&null!=l&&(i[a]=Io(l,null));var u=s.id;null==r[a]&&null!=u&&(r[a]=Io(u,null))}}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)y_(this,a);h_(this)}},t.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},t.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},t.prototype.setCalculationInfo=function(t,e){v_(t)?k(this._calculationInfo,t):this._calculationInfo[t]=e},t.prototype.getName=function(t){var e=this.getRawIndex(t),n=this._nameList[e];return null==n&&null!=this._nameDimIdx&&(n=p_(this,this._nameDimIdx,e)),null==n&&(n=""),n},t.prototype._getCategory=function(t,e){var n=this._store.get(t,e),i=this._store.getOrdinalMeta(t);return i?i.categories[n]:n},t.prototype.getId=function(t){return c_(this,this.getRawIndex(t))},t.prototype.count=function(){return this._store.count()},t.prototype.get=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.get(i.storeDimIndex,e)},t.prototype.getByRawIndex=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.getByRawIndex(i.storeDimIndex,e)},t.prototype.getIndices=function(){return this._store.getIndices()},t.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},t.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},t.prototype.getValues=function(t,e){var n=this,i=this._store;return H(t)?i.getValues(m_(t,(function(t){return n._getStoreDimIndex(t)})),e):i.getValues(t)},t.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},t.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},t.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},t.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},t.prototype.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t],i=n&&n[e];return null==i||isNaN(i)?-1:i},t.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},t.prototype.each=function(t,e,n){W(t)&&(n=e,e=t,t=[]);var i=n||this,r=m_(d_(t),this._getStoreDimIndex,this);this._store.each(r,i?F(e,i):e)},t.prototype.filterSelf=function(t,e,n){W(t)&&(n=e,e=t,t=[]);var i=n||this,r=m_(d_(t),this._getStoreDimIndex,this);return this._store=this._store.filter(r,i?F(e,i):e),this},t.prototype.selectRange=function(t){var e=this,n={};return O(z(t),(function(i){var r=e._getStoreDimIndex(i);n[r]=t[i]})),this._store=this._store.selectRange(n),this},t.prototype.mapArray=function(t,e,n){W(t)&&(n=e,e=t,t=[]),n=n||this;var i=[];return this.each(t,(function(){i.push(e&&e.apply(this,arguments))}),n),i},t.prototype.map=function(t,e,n,i){var r=n||i||this,o=m_(d_(t),this._getStoreDimIndex,this),a=g_(this);return a._store=this._store.map(o,r?F(e,r):e),a},t.prototype.modify=function(t,e,n,i){var r=n||i||this,o=m_(d_(t),this._getStoreDimIndex,this);this._store.modify(o,r?F(e,r):e)},t.prototype.downSample=function(t,e,n,i){var r=g_(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},t.prototype.minmaxDownSample=function(t,e){var n=g_(this);return n._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),n},t.prototype.lttbDownSample=function(t,e){var n=g_(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},t.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},t.prototype.getItemModel=function(t){var e=this.hostModel,n=this.getRawDataItem(t);return new Ec(n,e,e&&e.ecModel)},t.prototype.diff=function(t){var e=this;return new Qm(t?t.getStore().getIndices():[],this.getStore().getIndices(),(function(e){return c_(t,e)}),(function(t){return c_(e,t)}))},t.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},t.prototype.setVisual=function(t,e){this._visual=this._visual||{},v_(t)?k(this._visual,t):this._visual[t]=e},t.prototype.getItemVisual=function(t,e){var n=this._itemVisuals[t],i=n&&n[e];return null==i?this.getVisual(e):i},t.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},t.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t];i||(i=n[t]={});var r=i[e];return null==r&&(H(r=this.getVisual(e))?r=r.slice():v_(r)&&(r=k({},r)),i[e]=r),r},t.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,v_(e)?k(i,e):i[e]=n},t.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},t.prototype.setLayout=function(t,e){v_(t)?k(this._layout,t):this._layout[t]=e},t.prototype.getLayout=function(t){return this._layout[t]},t.prototype.getItemLayout=function(t){return this._itemLayouts[t]},t.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?k(this._itemLayouts[t]||{},e):e},t.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},t.prototype.setItemGraphicEl=function(t,e){var n=this.hostModel&&this.hostModel.seriesIndex;ol(n,this.dataType,t,e),this._graphicEls[t]=e},t.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},t.prototype.eachItemGraphicEl=function(t,e){O(this._graphicEls,(function(n,i){n&&t&&t.call(e,n,i)}))},t.prototype.cloneShallow=function(e){return e||(e=new t(this._schema?this._schema:m_(this.dimensions,this._getDimInfo,this),this.hostModel)),f_(e,this),e._store=this._store,e},t.prototype.wrapMethod=function(t,e){var n=this[t];W(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(nt(arguments)))})},t.internalField=(h_=function(t){var e=t._invertedIndicesMap;O(e,(function(n,i){var r=t._dimInfos[i],o=r.ordinalMeta,a=t._store;if(o){n=e[i]=new __(o.categories.length);for(var s=0;s<n.length;s++)n[s]=-1;for(s=0;s<a.count();s++)n[a.get(r.storeDimIndex,s)]=s}}))},p_=function(t,e,n){return Io(t._getCategory(e,n),null)},c_=function(t,e){var n=t._idList[e];return null==n&&null!=t._idDimIdx&&(n=p_(t,t._idDimIdx,e)),null==n&&(n="e\0\0"+e),n},d_=function(t){return H(t)||(t=null!=t?[t]:[]),t},g_=function(e){var n=new t(e._schema?e._schema:m_(e.dimensions,e._getDimInfo,e),e.hostModel);return f_(n,e),n},f_=function(t,e){O(x_.concat(e.__wrappedMethods||[]),(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t.__wrappedMethods=e.__wrappedMethods,O(w_,(function(n){t[n]=T(e[n])})),t._calculationInfo=k({},e._calculationInfo)},void(y_=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];if(null==a&&null!=r&&(n[e]=a=p_(t,r,e)),null==s&&null!=o&&(i[e]=s=p_(t,o,e)),null==s&&null!=a){var l=t._nameRepeatCount,u=l[a]=(l[a]||0)+1;s=a,u>1&&(s+="__ec__"+u),i[e]=s}})),t}();const S_=b_;function M_(t,e){gf(t)||(t=vf(t));var n=(e=e||{}).coordDimensions||[],i=e.dimensionsDefine||t.dimensionsDefine||[],r=pt(),o=[],a=function(t,e,n,i){var r=Math.max(t.dimensionsDetectedCount||1,e.length,n.length,i||0);return O(e,(function(t){var e;Y(t)&&(e=t.dimsDef)&&(r=Math.max(r,e.length))})),r}(t,n,i,e.dimensionsCount),s=e.canOmitUnusedDimensions&&u_(a),l=i===t.dimensionsDefine,u=l?l_(t):s_(i),h=e.encodeDefine;!h&&e.encodeDefaulter&&(h=e.encodeDefaulter(t,a));for(var c=pt(h),p=new ag(a),d=0;d<p.length;d++)p[d]=-1;function f(t){var e=p[t];if(e<0){var n=i[t],r=Y(n)?n:{name:n},a=new n_,s=r.name;null!=s&&null!=u.get(s)&&(a.name=a.displayName=s),null!=r.type&&(a.type=r.type),null!=r.displayName&&(a.displayName=r.displayName);var l=o.length;return p[t]=l,a.storeDimIndex=t,o.push(a),a}return o[e]}if(!s)for(d=0;d<a;d++)f(d);c.each((function(t,e){var n=_o(t).slice();if(1===n.length&&!G(n[0])&&n[0]<0)c.set(e,!1);else{var i=c.set(e,[]);O(n,(function(t,n){var r=G(t)?u.get(t):t;null!=r&&r<a&&(i[n]=r,y(f(r),e,n))}))}}));var g=0;function y(t,e,n){null!=td.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,r.set(e,!0))}O(n,(function(t){var e,n,i,r;if(G(t))e=t,r={};else{e=(r=t).name;var o=r.ordinalMeta;r.ordinalMeta=null,(r=k({},r)).ordinalMeta=o,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null}var s=c.get(e);if(!1!==s){if(!(s=_o(s)).length)for(var u=0;u<(n&&n.length||1);u++){for(;g<a&&null!=f(g).coordDim;)g++;g<a&&s.push(g++)}O(s,(function(t,o){var a=f(t);if(l&&null!=r.type&&(a.type=r.type),y(D(a,r),e,o),null==a.name&&n){var s=n[o];!Y(s)&&(s={name:s}),a.name=a.displayName=s.name,a.defaultTooltip=s.defaultTooltip}i&&D(a.otherDims,i)}))}}));var v=e.generateCoord,m=e.generateCoordCount,_=null!=m;m=v?m||1:0;var x=v||"value";function w(t){null==t.name&&(t.name=t.coordDim)}if(s)O(o,(function(t){w(t)})),o.sort((function(t,e){return t.storeDimIndex-e.storeDimIndex}));else for(var b=0;b<a;b++){var S=f(b);null==S.coordDim&&(S.coordDim=T_(x,r,_),S.coordDimIndex=0,(!v||m<=0)&&(S.isExtraCoord=!0),m--),w(S),null!=S.type||yd(t,b)!==ud&&(!S.isExtraCoord||null==S.otherDims.itemName&&null==S.otherDims.seriesName)||(S.type="ordinal")}return function(t){for(var e=pt(),n=0;n<t.length;n++){var i=t[n],r=i.name,o=e.get(r)||0;o>0&&(i.name=r+(o-1)),o++,e.set(r,o)}}(o),new o_({source:t,dimensions:o,fullDimensionCount:a,dimensionOmitted:s})}function T_(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}var C_=function(t){this.coordSysDims=[],this.axisMap=pt(),this.categoryAxisMap=pt(),this.coordSysName=t};var I_={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",Eo).models[0],o=t.getReferringComponents("yAxis",Eo).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",o),k_(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),k_(o)&&(i.set("y",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis",Eo).models[0];e.coordSysDims=["single"],n.set("single",r),k_(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar",Eo).models[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),k_(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),k_(a)&&(i.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,o=r.getComponent("parallel",t.get("parallelIndex")),a=e.coordSysDims=o.dimensions.slice();O(o.parallelAxisIndex,(function(t,o){var s=r.getComponent("parallelAxis",t),l=a[o];n.set(l,s),k_(s)&&(i.set(l,s),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=o))}))}};function k_(t){return"category"===t.get("type")}function D_(t,e,n){var i,r,o,a=(n=n||{}).byIndex,s=n.stackedCoordDimension;!function(t){return!a_(t.schema)}(e)?(r=e.schema,i=r.dimensions,o=e.store):i=e;var l,u,h,c,p=!(!t||!t.get("stack"));if(O(i,(function(t,e){G(t)&&(i[e]=t={name:t}),p&&!t.isExtraCoord&&(a||l||!t.ordinalMeta||(l=t),u||"ordinal"===t.type||"time"===t.type||s&&s!==t.coordDim||(u=t))})),!u||a||l||(a=!0),u){h="__\0ecstackresult_"+t.id,c="__\0ecstackedover_"+t.id,l&&(l.createInvertedIndices=!0);var d=u.coordDim,f=u.type,g=0;O(i,(function(t){t.coordDim===d&&g++}));var y={name:h,coordDim:d,coordDimIndex:g,type:f,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},v={name:c,coordDim:c,coordDimIndex:g+1,type:f,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1};r?(o&&(y.storeDimIndex=o.ensureCalculationDimension(c,f),v.storeDimIndex=o.ensureCalculationDimension(h,f)),r.appendCalculationDimension(y),r.appendCalculationDimension(v)):(i.push(y),i.push(v))}return{stackedDimension:u&&u.name,stackedByDimension:l&&l.name,isStackedByIndex:a,stackedOverDimension:c,stackResultDimension:h}}function A_(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function P_(t,e){return A_(t,e)?t.getCalculationInfo("stackResultDimension"):e}function L_(t,e,n){n=n||{};var i,r=e.getSourceManager(),o=!1;t?(o=!0,i=vf(t)):o=(i=r.getSource()).sourceFormat===ed;var a=function(t){var e=t.get("coordinateSystem"),n=new C_(e),i=I_[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}(e),s=function(t,e){var n,i=t.get("coordinateSystem"),r=Ed.get(i);return e&&e.coordSysDims&&(n=R(e.coordSysDims,(function(t){var n={name:t},i=e.axisMap.get(t);if(i){var r=i.get("type");n.type=e_(r)}return n}))),n||(n=r&&(r.getDimensionsInfo?r.getDimensionsInfo():r.dimensions.slice())||["x","y"]),n}(e,a),l=n.useEncodeDefaulter,u=W(l)?l:l?V(dd,s,e):null,h=M_(i,{coordDimensions:s,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!o}),c=function(t,e,n){var i,r;return n&&O(t,(function(t,o){var a=t.coordDim,s=n.categoryAxisMap.get(a);s&&(null==i&&(i=o),t.ordinalMeta=s.getOrdinalMeta(),e&&(t.createInvertedIndices=!0)),null!=t.otherDims.itemName&&(r=!0)})),r||null==i||(t[i].otherDims.itemName=0),i}(h.dimensions,n.createInvertedIndices,a),p=o?null:r.getSharedDataStore(h),d=D_(e,{schema:h,store:p}),f=new S_(h,e);f.setCalculationInfo(d);var g=null!=c&&function(t){if(t.sourceFormat===ed){return!H(bo(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[])))}}(i)?function(t,e,n,i){return i===c?n:this.defaultDimValueGetter(t,e,n,i)}:null;return f.hasItemOption=!1,f.initData(o?i:p,null,g),f}var O_=function(){function t(t){this._setting=t||{},this._extent=[1/0,-1/0]}return t.prototype.getSetting=function(t){return this._setting[t]},t.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},t.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},t.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},t.prototype.isBlank=function(){return this._isBlank},t.prototype.setBlank=function(t){this._isBlank=t},t}();$o(O_);const R_=O_;var N_=0;function B_(t){return Y(t)&&null!=t.value?t.value:t+""}const E_=function(){function t(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++N_}return t.createByAxisModel=function(e){var n=e.option,i=n.data,r=i&&R(i,B_);return new t({categories:r,needCollect:!r,deduplication:!1!==n.dedplication})},t.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},t.prototype.parseAndCollect=function(t){var e,n=this._needCollect;if(!G(t)&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=this._getOrCreateMap();return null==(e=i.get(t))&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e},t.prototype._getOrCreateMap=function(){return this._map||(this._map=pt(this.categories))},t}();function z_(t){return"interval"===t.type||"log"===t.type}function F_(t,e,n,i){var r={},o=t[1]-t[0],a=r.interval=io(o/e,!0);null!=n&&a<n&&(a=r.interval=n),null!=i&&a>i&&(a=r.interval=i);var s=r.intervalPrecision=H_(a);return function(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),W_(t,0,e),W_(t,1,e),t[0]>t[1]&&(t[0]=t[1])}(r.niceTickExtent=[Yr(Math.ceil(t[0]/a)*a,s),Yr(Math.floor(t[1]/a)*a,s)],t),r}function V_(t){var e=Math.pow(10,no(t)),n=t/e;return n?2===n?n=3:3===n?n=5:n*=2:n=1,Yr(n*e)}function H_(t){return Zr(t)+2}function W_(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function G_(t,e){return t>=e[0]&&t<=e[1]}function U_(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function X_(t,e){return t*(e[1]-e[0])+e[0]}var Y_=function(t){function n(e){var n=t.call(this,e)||this;n.type="ordinal";var i=n.getSetting("ordinalMeta");return i||(i=new E_({})),H(i)&&(i=new E_({categories:R(i,(function(t){return Y(t)?t.value:t}))})),n._ordinalMeta=i,n._extent=n.getSetting("extent")||[0,i.categories.length-1],n}return e(n,t),n.prototype.parse=function(t){return null==t?NaN:G(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},n.prototype.contain=function(t){return G_(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},n.prototype.normalize=function(t){return U_(t=this._getTickNumber(this.parse(t)),this._extent)},n.prototype.scale=function(t){return t=Math.round(X_(t,this._extent)),this.getRawOrdinalNumber(t)},n.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},n.prototype.getMinorTicks=function(t){},n.prototype.setSortInfo=function(t){if(null!=t){for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];n[r]=s,i[s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}else this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null},n.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&t>=0&&t<e.length?e[t]:t},n.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&t>=0&&t<e.length?e[t]:t},n.prototype.getLabel=function(t){if(!this.isBlank()){var e=this.getRawOrdinalNumber(t.value),n=this._ordinalMeta.categories[e];return null==n?"":n+""}},n.prototype.count=function(){return this._extent[1]-this._extent[0]+1},n.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},n.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},n.prototype.getOrdinalMeta=function(){return this._ordinalMeta},n.prototype.calcNiceTicks=function(){},n.prototype.calcNiceExtent=function(){},n.type="ordinal",n}(R_);R_.registerClass(Y_);const q_=Y_;var Z_=Yr,j_=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return e(n,t),n.prototype.parse=function(t){return t},n.prototype.contain=function(t){return G_(t,this._extent)},n.prototype.normalize=function(t){return U_(t,this._extent)},n.prototype.scale=function(t){return X_(t,this._extent)},n.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},n.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},n.prototype.getInterval=function(){return this._interval},n.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=H_(t)},n.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(!e)return o;n[0]<i[0]&&(t?o.push({value:Z_(i[0]-e,r)}):o.push({value:n[0]}));for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=Z_(a+e,r))!==o[o.length-1].value);)if(o.length>1e4)return[];var s=o.length?o[o.length-1].value:i[1];return n[1]>s&&(t?o.push({value:Z_(s+e,r)}):o.push({value:n[1]})),o},n.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=Z_(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},n.prototype.getLabel=function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=Zr(t.value)||0:"auto"===n&&(n=this._intervalPrecision),Tp(Z_(t.value,n,!0))},n.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var o=F_(i,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},n.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=Math.abs(e[0]);t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=Z_(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=Z_(Math.ceil(e[1]/r)*r))},n.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},n.type="interval",n}(R_);R_.registerClass(j_);const K_=j_;var $_="undefined"!=typeof Float32Array,Q_=$_?Float32Array:Array;function J_(t){return H(t)?$_?new Float32Array(t):t:new Q_(t)}var tx="__ec_stack_";function ex(t){return t.get("stack")||tx+t.seriesIndex}function nx(t){return t.dim+t.index}function ix(t){var e=[],n=t.axis,i="axis0";if("category"===n.type){for(var r=n.getBandWidth(),o=0;o<t.count;o++)e.push(D({bandWidth:r,axisKey:i,stackId:tx+o},t));var a=ax(e),s=[];for(o=0;o<t.count;o++){var l=a[i][tx+o];l.offsetCenter=l.offset+l.width/2,s.push(l)}return s}}function rx(t,e){var n=[];return e.eachSeriesByType(t,(function(t){ux(t)&&n.push(t)})),n}function ox(t){var e=function(t){var e={};O(t,(function(t){var n=t.coordinateSystem.getBaseAxis();if("time"===n.type||"value"===n.type)for(var i=t.getData(),r=n.dim+"_"+n.index,o=i.getDimensionIndex(i.mapDimension(n.dim)),a=i.getStore(),s=0,l=a.count();s<l;++s){var u=a.get(o,s);e[r]?e[r].push(u):e[r]=[u]}}));var n={};for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(r){r.sort((function(t,e){return t-e}));for(var o=null,a=1;a<r.length;++a){var s=r[a]-r[a-1];s>0&&(o=null===o?s:Math.min(o,s))}n[i]=o}}return n}(t),n=[];return O(t,(function(t){var i,r=t.coordinateSystem.getBaseAxis(),o=r.getExtent();if("category"===r.type)i=r.getBandWidth();else if("value"===r.type||"time"===r.type){var a=r.dim+"_"+r.index,s=e[a],l=Math.abs(o[1]-o[0]),u=r.scale.getExtent(),h=Math.abs(u[1]-u[0]);i=s?l/h*s:l}else{var c=t.getData();i=Math.abs(o[1]-o[0])/c.count()}var p=Xr(t.get("barWidth"),i),d=Xr(t.get("barMaxWidth"),i),f=Xr(t.get("barMinWidth")||(hx(t)?.5:1),i),g=t.get("barGap"),y=t.get("barCategoryGap");n.push({bandWidth:i,barWidth:p,barMaxWidth:d,barMinWidth:f,barGap:g,barCategoryGap:y,axisKey:nx(r),stackId:ex(t)})})),ax(n)}function ax(t){var e={};O(t,(function(t,n){var i=t.axisKey,r=t.bandWidth,o=e[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},a=o.stacks;e[i]=o;var s=t.stackId;a[s]||o.autoWidthCount++,a[s]=a[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!a[s].width&&(a[s].width=l,l=Math.min(o.remainedWidth,l),o.remainedWidth-=l);var u=t.barMaxWidth;u&&(a[s].maxWidth=u);var h=t.barMinWidth;h&&(a[s].minWidth=h);var c=t.barGap;null!=c&&(o.gap=c);var p=t.barCategoryGap;null!=p&&(o.categoryGap=p)}));var n={};return O(e,(function(t,e){n[e]={};var i=t.stacks,r=t.bandWidth,o=t.categoryGap;if(null==o){var a=z(i).length;o=Math.max(35-4*a,15)+"%"}var s=Xr(o,r),l=Xr(t.gap,1),u=t.remainedWidth,h=t.autoWidthCount,c=(u-s)/(h+(h-1)*l);c=Math.max(c,0),O(i,(function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,u-=i+l*i,h--}else{var i=c;e&&e<i&&(i=Math.min(e,u)),n&&n>i&&(i=n),i!==c&&(t.width=i,u-=i+l*i,h--)}})),c=(u-s)/(h+(h-1)*l),c=Math.max(c,0);var p,d=0;O(i,(function(t,e){t.width||(t.width=c),p=t,d+=t.width*(1+l)})),p&&(d-=p.width*l);var f=-d/2;O(i,(function(t,i){n[e][i]=n[e][i]||{bandWidth:r,offset:f,width:t.width},f+=t.width*(1+l)}))})),n}function sx(t,e){var n=rx(t,e),i=ox(n);O(n,(function(t){var e=t.getData(),n=t.coordinateSystem.getBaseAxis(),r=ex(t),o=i[nx(n)][r],a=o.offset,s=o.width;e.setLayout({bandWidth:o.bandWidth,offset:a,size:s})}))}function lx(t){return{seriesType:t,plan:qg(),reset:function(t){if(ux(t)){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=n.getOtherAxis(i),o=e.getDimensionIndex(e.mapDimension(r.dim)),a=e.getDimensionIndex(e.mapDimension(i.dim)),s=t.get("showBackground",!0),l=e.mapDimension(r.dim),u=e.getCalculationInfo("stackResultDimension"),h=A_(e,l)&&!!e.getCalculationInfo("stackedOnSeries"),c=r.isHorizontal(),p=function(t,e){var n=e.model.get("startValue");n||(n=0);return e.toGlobalCoord(e.dataToCoord("log"===e.type?n>0?n:1:n))}(0,r),d=hx(t),f=t.get("barMinHeight")||0,g=u&&e.getDimensionIndex(u),y=e.getLayout("size"),v=e.getLayout("offset");return{progress:function(t,e){for(var i,r=t.count,l=d&&J_(3*r),u=d&&s&&J_(3*r),m=d&&J_(r),_=n.master.getRect(),x=c?_.width:_.height,w=e.getStore(),b=0;null!=(i=t.next());){var S=w.get(h?g:o,i),M=w.get(a,i),T=p,C=void 0;h&&(C=+S-w.get(o,i));var I=void 0,k=void 0,D=void 0,A=void 0;if(c){var P=n.dataToPoint([S,M]);if(h)T=n.dataToPoint([C,M])[0];I=T,k=P[1]+v,D=P[0]-T,A=y,Math.abs(D)<f&&(D=(D<0?-1:1)*f)}else{P=n.dataToPoint([M,S]);if(h)T=n.dataToPoint([M,C])[1];I=P[0]+v,k=T,D=y,A=P[1]-T,Math.abs(A)<f&&(A=(A<=0?-1:1)*f)}d?(l[b]=I,l[b+1]=k,l[b+2]=c?D:A,u&&(u[b]=c?_.x:I,u[b+1]=c?k:_.y,u[b+2]=x),m[i]=i):e.setItemLayout(i,{x:I,y:k,width:D,height:A}),b+=3}d&&e.setLayout({largePoints:l,largeDataIndices:m,largeBackgroundPoints:u,valueAxisHorizontal:c})}}}}}}function ux(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function hx(t){return t.pipelineContext&&t.pipelineContext.large}var cx=function(t){function n(e){var n=t.call(this,e)||this;return n.type="time",n}return e(n,t),n.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return lp(t.value,np[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(ap(this._minLevelUnit))]||np.second,e,this.getSetting("locale"))},n.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC");return function(t,e,n,i,r){var o=null;if(G(n))o=n;else if(W(n))o=n(t.value,e,{level:t.level});else{var a=k({},tp);if(t.level>0)for(var s=0;s<ip.length;++s)a[ip[s]]="{primary|"+a[ip[s]]+"}";var l=n?!1===n.inherit?n:D(n,a):a,u=up(t.value,r);if(l[u])o=l[u];else if(l.inherit){for(s=rp.indexOf(u)-1;s>=0;--s)if(l[u]){o=l[u];break}o=o||a.none}if(H(o)){var h=null==t.level?0:t.level>=0?t.level:o.length+t.level;o=o[h=Math.min(h,o.length-1)]}}return lp(new Date(t.value),o,r,i)}(t,e,n,this.getSetting("locale"),i)},n.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];if(!t)return n;n.push({value:e[0],level:0});var i=this.getSetting("useUTC"),r=function(t,e,n,i){var r=1e4,o=rp,a=0;function s(t,e,n,r,o,a,s){for(var l=new Date(e),u=e,h=l[r]();u<n&&u<=i[1];)s.push({value:u}),h+=t,l[o](h),u=l.getTime();s.push({value:u,notAdd:!0})}function l(t,r,o){var a=[],l=!r.length;if(!function(t,e,n,i){var r=eo(e),o=eo(n),a=function(t){return hp(r,t,i)===hp(o,t,i)},s=function(){return a("year")},l=function(){return s()&&a("month")},u=function(){return l()&&a("day")},h=function(){return u()&&a("hour")},c=function(){return h()&&a("minute")},p=function(){return c()&&a("second")},d=function(){return p()&&a("millisecond")};switch(t){case"year":return s();case"month":return l();case"day":return u();case"hour":return h();case"minute":return c();case"second":return p();case"millisecond":return d()}}(ap(t),i[0],i[1],n)){l&&(r=[{value:mx(new Date(i[0]),t,n)},{value:i[1]}]);for(var u=0;u<r.length-1;u++){var h=r[u].value,c=r[u+1].value;if(h!==c){var p=void 0,d=void 0,f=void 0,g=!1;switch(t){case"year":p=Math.max(1,Math.round(e/Qc/365)),d=cp(n),f=mp(n);break;case"half-year":case"quarter":case"month":p=fx(e),d=pp(n),f=_p(n);break;case"week":case"half-week":case"day":p=dx(e),d=dp(n),f=xp(n),g=!0;break;case"half-day":case"quarter-day":case"hour":p=gx(e),d=fp(n),f=wp(n);break;case"minute":p=yx(e,!0),d=gp(n),f=bp(n);break;case"second":p=yx(e,!1),d=yp(n),f=Sp(n);break;case"millisecond":p=vx(e),d=vp(n),f=Mp(n)}s(p,h,c,d,f,g,a),"year"===t&&o.length>1&&0===u&&o.unshift({value:o[0].value-p})}}for(u=0;u<a.length;u++)o.push(a[u]);return a}}for(var u=[],h=[],c=0,p=0,d=0;d<o.length&&a++<r;++d){var f=ap(o[d]);if(sp(o[d]))if(l(o[d],u[u.length-1]||[],h),f!==(o[d+1]?ap(o[d+1]):null)){if(h.length){p=c,h.sort((function(t,e){return t.value-e.value}));for(var g=[],y=0;y<h.length;++y){var v=h[y].value;0!==y&&h[y-1].value===v||(g.push(h[y]),v>=i[0]&&v<=i[1]&&c++)}var m=(i[1]-i[0])/e;if(c>1.5*m&&p>m/1.5)break;if(u.push(g),c>m||t===o[d])break}h=[]}}var _=B(R(u,(function(t){return B(t,(function(t){return t.value>=i[0]&&t.value<=i[1]&&!t.notAdd}))})),(function(t){return t.length>0})),x=[],w=_.length-1;for(d=0;d<_.length;++d)for(var b=_[d],S=0;S<b.length;++S)x.push({value:b[S].value,level:w-d});x.sort((function(t,e){return t.value-e.value}));var M=[];for(d=0;d<x.length;++d)0!==d&&x[d].value===x[d-1].value||M.push(x[d]);return M}(this._minLevelUnit,this._approxInterval,i,e);return(n=n.concat(r)).push({value:e[1],level:0}),n},n.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=Qc,e[1]+=Qc),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-Qc}this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},n.prototype.calcNiceTicks=function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0];this._approxInterval=r/t,null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n);var o=px.length,a=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n}(px,this._approxInterval,0,o),o-1);this._interval=px[a][1],this._minLevelUnit=px[Math.max(a-1,0)][0]},n.prototype.parse=function(t){return X(t)?t:+eo(t)},n.prototype.contain=function(t){return G_(this.parse(t),this._extent)},n.prototype.normalize=function(t){return U_(this.parse(t),this._extent)},n.prototype.scale=function(t){return X_(t,this._extent)},n.type="time",n}(K_),px=[["second",jc],["minute",Kc],["hour",$c],["quarter-day",216e5],["half-day",432e5],["day",10368e4],["half-week",3024e5],["week",6048e5],["month",26784e5],["quarter",8208e6],["half-year",Jc/2],["year",Jc]];function dx(t,e){return(t/=Qc)>16?16:t>7.5?7:t>3.5?4:t>1.5?2:1}function fx(t){return(t/=2592e6)>6?6:t>3?3:t>2?2:1}function gx(t){return(t/=$c)>12?12:t>6?6:t>3.5?4:t>2?2:1}function yx(t,e){return(t/=e?Kc:jc)>30?30:t>20?20:t>15?15:t>10?10:t>5?5:t>2?2:1}function vx(t){return io(t,!0)}function mx(t,e,n){var i=new Date(t);switch(ap(e)){case"year":case"month":i[_p(n)](0);case"day":i[xp(n)](1);case"hour":i[wp(n)](0);case"minute":i[bp(n)](0);case"second":i[Sp(n)](0),i[Mp(n)](0)}return i.getTime()}R_.registerClass(cx);const _x=cx;var xx=R_.prototype,bx=K_.prototype,Sx=Yr,Mx=Math.floor,Tx=Math.ceil,Cx=Math.pow,Ix=Math.log,kx=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new K_,e._interval=0,e}return e(n,t),n.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return R(bx.getTicks.call(this,t),(function(t){var e=t.value,r=Yr(Cx(this.base,e));return r=e===n[0]&&this._fixMin?Ax(r,i[0]):r,{value:r=e===n[1]&&this._fixMax?Ax(r,i[1]):r}}),this)},n.prototype.setExtent=function(t,e){var n=Ix(this.base);t=Ix(Math.max(0,t))/n,e=Ix(Math.max(0,e))/n,bx.setExtent.call(this,t,e)},n.prototype.getExtent=function(){var t=this.base,e=xx.getExtent.call(this);e[0]=Cx(t,e[0]),e[1]=Cx(t,e[1]);var n=this._originalScale.getExtent();return this._fixMin&&(e[0]=Ax(e[0],n[0])),this._fixMax&&(e[1]=Ax(e[1],n[1])),e},n.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=Ix(t[0])/Ix(e),t[1]=Ix(t[1])/Ix(e),xx.unionExtent.call(this,t)},n.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},n.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i,r=(i=n,Math.pow(10,no(i)));for(t/n*r<=.5&&(r*=10);!isNaN(r)&&Math.abs(r)<1&&Math.abs(r)>0;)r*=10;var o=[Yr(Tx(e[0]/r)*r),Yr(Mx(e[1]/r)*r)];this._interval=r,this._niceExtent=o}},n.prototype.calcNiceExtent=function(t){bx.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},n.prototype.parse=function(t){return t},n.prototype.contain=function(t){return G_(t=Ix(t)/Ix(this.base),this._extent)},n.prototype.normalize=function(t){return U_(t=Ix(t)/Ix(this.base),this._extent)},n.prototype.scale=function(t){return t=X_(t,this._extent),Cx(this.base,t)},n.type="log",n}(R_),Dx=kx.prototype;function Ax(t,e){return Sx(t,Zr(e))}Dx.getMinorTicks=bx.getMinorTicks,Dx.getLabel=bx.getLabel,R_.registerClass(kx);const Px=kx;var Lx=function(){function t(t,e,n){this._prepareParams(t,e,n)}return t.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type;this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero();var r=e.get("min",!0);null==r&&(r=e.get("startValue",!0));var o=this._modelMinRaw=r;W(o)?this._modelMinNum=Bx(t,o({min:n[0],max:n[1]})):"dataMin"!==o&&(this._modelMinNum=Bx(t,o));var a=this._modelMaxRaw=e.get("max",!0);if(W(a)?this._modelMaxNum=Bx(t,a({min:n[0],max:n[1]})):"dataMax"!==a&&(this._modelMaxNum=Bx(t,a)),i)this._axisDataLen=e.getCategories().length;else{var s=e.get("boundaryGap"),l=H(s)?s:[s||0,s||0];"boolean"==typeof l[0]||"boolean"==typeof l[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[br(l[0],1),br(l[1],1)]}},t.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s;null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),(null==a||!isFinite(a))&&(a=NaN),(null==s||!isFinite(s))&&(s=NaN);var h=Q(a)||Q(s)||t&&!i;this._needCrossZero&&(a>0&&s>0&&!l&&(a=0),a<0&&s<0&&!u&&(s=0));var c=this._determinedMin,p=this._determinedMax;return null!=c&&(a=c,l=!0),null!=p&&(s=p,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:h}},t.prototype.modifyDataMinMax=function(t,e){this[Rx[t]]=e},t.prototype.setDeterminedMinMax=function(t,e){this[Ox[t]]=e},t.prototype.freeze=function(){this.frozen=!0},t}(),Ox={min:"_determinedMin",max:"_determinedMax"},Rx={min:"_dataMin",max:"_dataMax"};function Nx(t,e,n){var i=t.rawExtentInfo;return i||(i=new Lx(t,e,n),t.rawExtentInfo=i,i)}function Bx(t,e){return null==e?null:Q(e)?NaN:t.parse(e)}function Ex(t,e){var n=t.type,i=Nx(t,e,t.getExtent()).calculate();t.setBlank(i.isBlank);var r=i.min,o=i.max,a=e.ecModel;if(a&&"time"===n){var s=rx("bar",a),l=!1;if(O(s,(function(t){l=l||t.getBaseAxis()===e.axis})),l){var u=ox(s),h=function(t,e,n,i){var r=n.axis.getExtent(),o=Math.abs(r[1]-r[0]),a=function(t,e,n){if(t&&e){var i=t[nx(e)];return null!=i&&null!=n?i[ex(n)]:i}}(i,n.axis);if(void 0===a)return{min:t,max:e};var s=1/0;O(a,(function(t){s=Math.min(t.offset,s)}));var l=-1/0;O(a,(function(t){l=Math.max(t.offset+t.width,l)})),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-t,c=h/(1-(s+l)/o)-h;return e+=c*(l/u),t-=c*(s/u),{min:t,max:e}}(r,o,e,u);r=h.min,o=h.max}}return{extent:[r,o],fixMin:i.minFixed,fixMax:i.maxFixed}}function zx(t,e){var n=e,i=Ex(t,n),r=i.extent,o=n.get("splitNumber");t instanceof Px&&(t.base=n.get("logBase"));var a=t.type,s=n.get("interval"),l="interval"===a||"time"===a;t.setExtent(r[0],r[1]),t.calcNiceExtent({splitNumber:o,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:l?n.get("minInterval"):null,maxInterval:l?n.get("maxInterval"):null}),null!=s&&t.setInterval&&t.setInterval(s)}function Fx(t,e){if(e=e||t.get("type"))switch(e){case"category":return new q_({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new _x({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(R_.getClass(e)||K_)}}function Vx(t){var e,n,i=t.getLabelModel().get("formatter"),r="category"===t.type?t.scale.getExtent()[0]:null;return"time"===t.scale.type?(n=i,function(e,i){return t.scale.getFormattedLabel(e,i,n)}):G(i)?function(e){return function(n){var i=t.scale.getLabel(n);return e.replace("{value}",null!=i?i:"")}}(i):W(i)?(e=i,function(n,i){return null!=r&&(i=n.value-r),e(Hx(t,n),i,null!=n.level?{level:n.level}:null)}):function(e){return t.scale.getLabel(e)}}function Hx(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}function Wx(t){var e=t.get("interval");return null==e?"auto":e}function Gx(t){return"category"===t.type&&0===Wx(t.getLabelModel())}function Ux(t,e){var n={};return O(t.mapDimensionsAll(e),(function(e){n[P_(t,e)]=!0})),z(n)}function Xx(t,e,n){e&&O(Ux(e,n),(function(n){var i=e.getApproximateExtent(n);i[0]<t[0]&&(t[0]=i[0]),i[1]>t[1]&&(t[1]=i[1])}))}var Yx=function(){function t(){}return t.prototype.getNeedCrossZero=function(){return!this.option.scale},t.prototype.getCoordSysModel=function(){},t}(),qx=Oo();function Zx(t,e){var n=R(e,(function(e){return t.scale.parse(e)}));return"time"===t.type&&n.length>0&&(n.sort(),n.unshift(n[0]),n.push(n[n.length-1])),n}function jx(t){var e=t.getLabelModel().get("customValues");if(e){var n=Vx(t),i=t.scale.getExtent();return{labels:R(B(Zx(t,e),(function(t){return t>=i[0]&&t<=i[1]})),(function(e){var i={value:e};return{formattedLabel:n(i),rawLabel:t.scale.getLabel(i),tickValue:e}}))}}return"category"===t.type?function(t){var e=t.getLabelModel(),n=$x(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}(t):function(t){var e=t.scale.getTicks(),n=Vx(t);return{labels:R(e,(function(e,i){return{level:e.level,formattedLabel:n(e,i),rawLabel:t.scale.getLabel(e),tickValue:e.value}}))}}(t)}function Kx(t,e){var n=t.getTickModel().get("customValues");if(n){var i=t.scale.getExtent();return{ticks:B(Zx(t,n),(function(t){return t>=i[0]&&t<=i[1]}))}}return"category"===t.type?function(t,e){var n,i,r=Qx(t,"ticks"),o=Wx(e),a=Jx(r,o);if(a)return a;e.get("show")&&!t.scale.isBlank()||(n=[]);if(W(o))n=nw(t,o,!0);else if("auto"===o){var s=$x(t,t.getLabelModel());i=s.labelCategoryInterval,n=R(s.labels,(function(t){return t.tickValue}))}else n=ew(t,i=o,!0);return tw(r,o,{ticks:n,tickCategoryInterval:i})}(t,e):{ticks:R(t.scale.getTicks(),(function(t){return t.value}))}}function $x(t,e){var n,i,r=Qx(t,"labels"),o=Wx(e),a=Jx(r,o);return a||(W(o)?n=nw(t,o):(i="auto"===o?function(t){var e=qx(t).autoInterval;return null!=e?e:qx(t).autoInterval=t.calculateCategoryInterval()}(t):o,n=ew(t,i)),tw(r,o,{labels:n,labelCategoryInterval:i}))}function Qx(t,e){return qx(t)[e]||(qx(t)[e]=[])}function Jx(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function tw(t,e,n){return t.push({key:e,value:n}),n}function ew(t,e,n){var i=Vx(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=o[0],h=r.count();0!==u&&l>1&&h/l>2&&(u=Math.round(Math.ceil(u/l)*l));var c=Gx(t),p=a.get("showMinLabel")||c,d=a.get("showMaxLabel")||c;p&&u!==o[0]&&g(o[0]);for(var f=u;f<=o[1];f+=l)g(f);function g(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return d&&f-l!==o[1]&&g(o[1]),s}function nw(t,e,n){var i=t.scale,r=Vx(t),o=[];return O(i.getTicks(),(function(t){var a=i.getLabel(t),s=t.value;e(t.value,a)&&o.push(n?s:{formattedLabel:r(t),rawLabel:a,tickValue:s})})),o}var iw=[0,1];function rw(t,e){var n=(t[1]-t[0])/e/2;t[0]+=n,t[1]-=n}const ow=function(){function t(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}return t.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},t.prototype.containData=function(t){return this.scale.contain(t)},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.getPixelPrecision=function(t){return jr(t||this.scale.getExtent(),this._extent)},t.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},t.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&rw(n=n.slice(),i.count()),Ur(t,iw,n,e)},t.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&rw(n=n.slice(),i.count());var r=Ur(t,n,iw,e);return this.scale.scale(r)},t.prototype.pointToData=function(t,e){},t.prototype.getTicksCoords=function(t){var e=(t=t||{}).tickModel||this.getTickModel(),n=R(Kx(this,e).ticks,(function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}}),this);return function(t,e,n,i){var r=e.length;if(!t.onBand||n||!r)return;var o,a,s=t.getExtent();if(1===r)e[0].coord=s[0],o=e[1]={coord:s[1],tickValue:e[0].tickValue};else{var l=e[r-1].tickValue-e[0].tickValue,u=(e[r-1].coord-e[0].coord)/l;O(e,(function(t){t.coord-=u/2}));var h=t.scale.getExtent();a=1+h[1]-e[r-1].tickValue,o={coord:e[r-1].coord+u*a,tickValue:h[1]+1},e.push(o)}var c=s[0]>s[1];p(e[0].coord,s[0])&&(i?e[0].coord=s[0]:e.shift());i&&p(s[0],e[0].coord)&&e.unshift({coord:s[0]});p(s[1],o.coord)&&(i?o.coord=s[1]:e.pop());i&&p(o.coord,s[1])&&e.push({coord:s[1]});function p(t,e){return t=Yr(t),e=Yr(e),c?t>e:t<e}}(this,n,e.get("alignWithLabel"),t.clamp),n},t.prototype.getMinorTicksCoords=function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");return t>0&&t<100||(t=5),R(this.scale.getMinorTicks(t),(function(t){return R(t,(function(t){return{coord:this.dataToCoord(t),tickValue:t}}),this)}),this)},t.prototype.getViewLabels=function(){return jx(this).labels},t.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},t.prototype.getTickModel=function(){return this.model.getModel("axisTick")},t.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},t.prototype.calculateCategoryInterval=function(){return function(t){var e=function(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}(t),n=Vx(t),i=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,o=r.getExtent(),a=r.count();if(o[1]-o[0]<1)return 0;var s=1;a>40&&(s=Math.max(1,Math.floor(a/40)));for(var l=o[0],u=t.dataToCoord(l+1)-t.dataToCoord(l),h=Math.abs(u*Math.cos(i)),c=Math.abs(u*Math.sin(i)),p=0,d=0;l<=o[1];l+=s){var f,g,y=mr(n({value:l}),e.font,"center","top");f=1.3*y.width,g=1.3*y.height,p=Math.max(p,f,7),d=Math.max(d,g,7)}var v=p/h,m=d/c;isNaN(v)&&(v=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(v,m))),x=qx(t.model),w=t.getExtent(),b=x.lastAutoInterval,S=x.lastTickCount;return null!=b&&null!=S&&Math.abs(b-_)<=1&&Math.abs(S-a)<=1&&b>_&&x.axisExtent0===w[0]&&x.axisExtent1===w[1]?_=b:(x.lastTickCount=a,x.lastAutoInterval=_,x.axisExtent0=w[0],x.axisExtent1=w[1]),_}(this)},t}();var aw=2*Math.PI,sw=as.CMD,lw=["top","right","bottom","left"];function uw(t,e,n,i,r){var o=n.width,a=n.height;switch(t){case"top":i.set(n.x+o/2,n.y-e),r.set(0,-1);break;case"bottom":i.set(n.x+o/2,n.y+a+e),r.set(0,1);break;case"left":i.set(n.x-e,n.y+a/2),r.set(-1,0);break;case"right":i.set(n.x+o+e,n.y+a/2),r.set(1,0)}}function hw(t,e,n,i,r,o,a,s,l){a-=t,s-=e;var u=Math.sqrt(a*a+s*s),h=(a/=u)*n+t,c=(s/=u)*n+e;if(Math.abs(i-r)%aw<1e-4)return l[0]=h,l[1]=c,u-n;if(o){var p=i;i=cs(r),r=cs(p)}else i=cs(i),r=cs(r);i>r&&(r+=aw);var d=Math.atan2(s,a);if(d<0&&(d+=aw),d>=i&&d<=r||d+aw>=i&&d+aw<=r)return l[0]=h,l[1]=c,u-n;var f=n*Math.cos(i)+t,g=n*Math.sin(i)+e,y=n*Math.cos(r)+t,v=n*Math.sin(r)+e,m=(f-a)*(f-a)+(g-s)*(g-s),_=(y-a)*(y-a)+(v-s)*(v-s);return m<_?(l[0]=f,l[1]=g,Math.sqrt(m)):(l[0]=y,l[1]=v,Math.sqrt(_))}function cw(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,h=n-t,c=i-e,p=Math.sqrt(h*h+c*c),d=(l*(h/=p)+u*(c/=p))/p;s&&(d=Math.min(Math.max(d,0),1)),d*=p;var f=a[0]=t+d*h,g=a[1]=e+d*c;return Math.sqrt((f-r)*(f-r)+(g-o)*(g-o))}function pw(t,e,n,i,r,o,a){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i);var s=t+n,l=e+i,u=a[0]=Math.min(Math.max(r,t),s),h=a[1]=Math.min(Math.max(o,e),l);return Math.sqrt((u-r)*(u-r)+(h-o)*(h-o))}var dw=[];function fw(t,e,n){var i=pw(e.x,e.y,e.width,e.height,t.x,t.y,dw);return n.set(dw[0],dw[1]),i}function gw(t,e,n){for(var i,r,o=0,a=0,s=0,l=0,u=1/0,h=e.data,c=t.x,p=t.y,d=0;d<h.length;){var f=h[d++];1===d&&(s=o=h[d],l=a=h[d+1]);var g=u;switch(f){case sw.M:o=s=h[d++],a=l=h[d++];break;case sw.L:g=cw(o,a,h[d],h[d+1],c,p,dw,!0),o=h[d++],a=h[d++];break;case sw.C:g=gn(o,a,h[d++],h[d++],h[d++],h[d++],h[d],h[d+1],c,p,dw),o=h[d++],a=h[d++];break;case sw.Q:g=wn(o,a,h[d++],h[d++],h[d],h[d+1],c,p,dw),o=h[d++],a=h[d++];break;case sw.A:var y=h[d++],v=h[d++],m=h[d++],_=h[d++],x=h[d++],w=h[d++];d+=1;var b=!!(1-h[d++]);i=Math.cos(x)*m+y,r=Math.sin(x)*_+v,d<=1&&(s=i,l=r),g=hw(y,v,_,x,x+w,b,(c-y)*_/m+y,p,dw),o=Math.cos(x+w)*m+y,a=Math.sin(x+w)*_+v;break;case sw.R:g=pw(s=o=h[d++],l=a=h[d++],h[d++],h[d++],c,p,dw);break;case sw.Z:g=cw(o,a,s,l,c,p,dw,!0),o=s,a=l}g<u&&(u=g,n.set(dw[0],dw[1]))}return u}var yw=new ve,vw=new ve,mw=new ve,_w=new ve,xw=new ve;function ww(t,e){if(t){var n=t.getTextGuideLine(),i=t.getTextContent();if(i&&n){var r=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=r.candidates||lw,s=i.getBoundingRect().clone();s.applyTransform(i.getComputedTransform());var l=1/0,u=r.anchor,h=t.getComputedTransform(),c=h&&ge([],h),p=e.get("length2")||0;u&&mw.copy(u);for(var d=0;d<a.length;d++){uw(a[d],0,s,yw,_w),ve.scaleAndAdd(vw,yw,_w,p),vw.transform(c);var f=t.getBoundingRect(),g=u?u.distance(vw):t instanceof Cs?gw(vw,t.path,mw):fw(vw,f,mw);g<l&&(l=g,vw.transform(h),mw.transform(h),mw.toArray(o[0]),vw.toArray(o[1]),yw.toArray(o[2]))}Mw(o,e.get("minTurnAngle")),n.setShape({points:o})}}}var bw=[],Sw=new ve;function Mw(t,e){if(e<=180&&e>0){e=e/180*Math.PI,yw.fromArray(t[0]),vw.fromArray(t[1]),mw.fromArray(t[2]),ve.sub(_w,yw,vw),ve.sub(xw,mw,vw);var n=_w.len(),i=xw.len();if(!(n<.001||i<.001)){_w.scale(1/n),xw.scale(1/i);var r=_w.dot(xw);if(Math.cos(e)<r){var o=cw(vw.x,vw.y,mw.x,mw.y,yw.x,yw.y,bw,!1);Sw.fromArray(bw),Sw.scaleAndAdd(xw,o/Math.tan(Math.PI-e));var a=mw.x!==vw.x?(Sw.x-vw.x)/(mw.x-vw.x):(Sw.y-vw.y)/(mw.y-vw.y);if(isNaN(a))return;a<0?ve.copy(Sw,vw):a>1&&ve.copy(Sw,mw),Sw.toArray(t[1])}}}}function Tw(t,e,n){if(n<=180&&n>0){n=n/180*Math.PI,yw.fromArray(t[0]),vw.fromArray(t[1]),mw.fromArray(t[2]),ve.sub(_w,vw,yw),ve.sub(xw,mw,vw);var i=_w.len(),r=xw.len();if(!(i<.001||r<.001))if(_w.scale(1/i),xw.scale(1/r),_w.dot(e)<Math.cos(n)){var o=cw(vw.x,vw.y,mw.x,mw.y,yw.x,yw.y,bw,!1);Sw.fromArray(bw);var a=Math.PI/2,s=a+Math.acos(xw.dot(e))-n;if(s>=a)ve.copy(Sw,mw);else{Sw.scaleAndAdd(xw,o/Math.tan(Math.PI/2-s));var l=mw.x!==vw.x?(Sw.x-vw.x)/(mw.x-vw.x):(Sw.y-vw.y)/(mw.y-vw.y);if(isNaN(l))return;l<0?ve.copy(Sw,vw):l>1&&ve.copy(Sw,mw)}Sw.toArray(t[1])}}}function Cw(t,e,n,i){var r="normal"===n,o=r?t:t.ensureState(n);o.ignore=e;var a=i.get("smooth");a&&!0===a&&(a=.3),o.shape=o.shape||{},a>0&&(o.shape.smooth=a);var s=i.getModel("lineStyle").getLineStyle();r?t.useStyle(s):o.style=s}function Iw(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),n>0&&i.length>=3){var r=At(i[0],i[1]),o=At(i[1],i[2]);if(!r||!o)return t.lineTo(i[1][0],i[1][1]),void t.lineTo(i[2][0],i[2][1]);var a=Math.min(r,o)*n,s=Lt([],i[1],i[0],a/r),l=Lt([],i[1],i[2],a/o),u=Lt([],s,l,.5);t.bezierCurveTo(s[0],s[1],s[0],s[1],u[0],u[1]),t.bezierCurveTo(l[0],l[1],l[0],l[1],i[2][0],i[2][1])}else for(var h=1;h<i.length;h++)t.lineTo(i[h][0],i[h][1])}function kw(t,e,n){var i=t.getTextGuideLine(),r=t.getTextContent();if(r){for(var o=e.normal,a=o.get("show"),s=r.ignore,l=0;l<pl.length;l++){var u=pl[l],h=e[u],c="normal"===u;if(h){var p=h.get("show");if((c?s:tt(r.states[u]&&r.states[u].ignore,s))||!tt(p,a)){var d=c?i:i&&i.states[u];d&&(d.ignore=!0),i&&Cw(i,!0,u,h);continue}i||(i=new eh,t.setTextGuideLine(i),c||!s&&a||Cw(i,!0,"normal",e.normal),t.stateProxy&&(i.stateProxy=t.stateProxy)),Cw(i,!1,u,h)}}if(i){D(i.style,n),i.style.fill=null;var f=o.get("showAbove");(t.textGuideLineConfig=t.textGuideLineConfig||{}).showAbove=f||!1,i.buildPath=Iw}}else i&&t.removeTextGuideLine()}function Dw(t,e){e=e||"labelLine";for(var n={normal:t.getModel(e)},i=0;i<cl.length;i++){var r=cl[i];n[r]=t.getModel([r,e])}return n}function Aw(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(!i.defaultAttr.ignore){var r=i.label,o=r.getComputedTransform(),a=r.getBoundingRect(),s=!o||o[1]<1e-5&&o[2]<1e-5,l=r.style.margin||0,u=a.clone();u.applyTransform(o),u.x-=l/2,u.y-=l/2,u.width+=l,u.height+=l;var h=s?new bh(a,o):null;e.push({label:r,labelLine:i.labelLine,rect:u,localRect:a,obb:h,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:o})}}return e}function Pw(t,e,n,i,r,o){var a=t.length;if(!(a<2)){t.sort((function(t,n){return t.rect[e]-n.rect[e]}));for(var s,l=0,u=!1,h=0,c=0;c<a;c++){var p=t[c],d=p.rect;(s=d[e]-l)<0&&(d[e]-=s,p.label[e]-=s,u=!0),h+=Math.max(-s,0),l=d[e]+d[n]}h>0&&o&&x(-h/a,0,a);var f,g,y=t[0],v=t[a-1];return m(),f<0&&w(-f,.8),g<0&&w(g,.8),m(),_(f,g,1),_(g,f,-1),m(),f<0&&b(-f),g<0&&b(g),u}function m(){f=y.rect[e]-i,g=r-v.rect[e]-v.rect[n]}function _(t,e,n){if(t<0){var i=Math.min(e,-t);if(i>0){x(i*n,0,a);var r=i+t;r<0&&w(-r*n,1)}else w(-t*n,1)}}function x(n,i,r){0!==n&&(u=!0);for(var o=i;o<r;o++){var a=t[o];a.rect[e]+=n,a.label[e]+=n}}function w(i,r){for(var o=[],s=0,l=1;l<a;l++){var u=t[l-1].rect,h=Math.max(t[l].rect[e]-u[e]-u[n],0);o.push(h),s+=h}if(s){var c=Math.min(Math.abs(i)/s,r);if(i>0)for(l=0;l<a-1;l++){x(o[l]*c,0,l+1)}else for(l=a-1;l>0;l--){x(-(o[l-1]*c),l,a)}}}function b(t){var e=t<0?-1:1;t=Math.abs(t);for(var n=Math.ceil(t/(a-1)),i=0;i<a-1;i++)if(e>0?x(n,0,i+1):x(-n,a-i-1,a),(t-=n)<=0)return}}function Lw(t,e,n,i){return Pw(t,"x","width",e,n,i)}function Ow(t,e,n,i){return Pw(t,"y","height",e,n,i)}function Rw(t){var e=[];t.sort((function(t,e){return e.priority-t.priority}));var n=new Ie(0,0,0,0);function i(t){if(!t.ignore){var e=t.ensureState("emphasis");null==e.ignore&&(e.ignore=!1)}t.ignore=!0}for(var r=0;r<t.length;r++){var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine;n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05;for(var c=o.obb,p=!1,d=0;d<e.length;d++){var f=e[d];if(n.intersect(f.rect)){if(a&&f.axisAligned){p=!0;break}if(f.obb||(f.obb=new bh(f.localRect,f.transform)),c||(c=new bh(s,l)),c.intersect(f.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}function Nw(t,e,n){var i=h.createCanvas(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}const Bw=function(t){function n(e,n,i){var r,o=t.call(this)||this;o.motionBlur=!1,o.lastFrameAlpha=.7,o.dpr=1,o.virtual=!1,o.config={},o.incremental=!1,o.zlevel=0,o.maxRepaintRectCount=5,o.__dirty=!0,o.__firstTimePaint=!0,o.__used=!1,o.__drawIndex=0,o.__startIndex=0,o.__endIndex=0,o.__prevStartIndex=null,o.__prevEndIndex=null,i=i||er,"string"==typeof e?r=Nw(e,n,i):Y(e)&&(e=(r=e).id),o.id=e,o.dom=r;var a=r.style;return a&&(gt(r),r.onselectstart=function(){return!1},a.padding="0",a.margin="0",a.borderWidth="0"),o.painter=n,o.dpr=i,o}return e(n,t),n.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},n.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},n.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},n.prototype.setUnpainted=function(){this.__firstTimePaint=!0},n.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=Nw("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},n.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var r,o=[],a=this.maxRepaintRectCount,s=!1,l=new Ie(0,0,0,0);function u(t){if(t.isFinite()&&!t.isZero())if(0===o.length){(e=new Ie(0,0,0,0)).copy(t),o.push(e)}else{for(var e,n=!1,i=1/0,r=0,u=0;u<o.length;++u){var h=o[u];if(h.intersect(t)){var c=new Ie(0,0,0,0);c.copy(h),c.union(t),o[u]=c,n=!0;break}if(s){l.copy(t),l.union(h);var p=t.width*t.height,d=h.width*h.height,f=l.width*l.height-p-d;f<i&&(i=f,r=u)}}if(s&&(o[r].union(t),n=!0),!n)(e=new Ie(0,0,0,0)).copy(t),o.push(e);s||(s=o.length>=a)}}for(var h=this.__startIndex;h<this.__endIndex;++h){if(d=t[h]){var c=d.shouldBePainted(n,i,!0,!0);(f=d.__isRendered&&(1&d.__dirty||!c)?d.getPrevPaintRect():null)&&u(f);var p=c&&(1&d.__dirty||!d.__isRendered)?d.getPaintRect():null;p&&u(p)}}for(h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var d,f;c=(d=e[h])&&d.shouldBePainted(n,i,!0,!0);if(d&&(!c||!d.__zr)&&d.__isRendered)(f=d.getPrevPaintRect())&&u(f)}do{r=!1;for(h=0;h<o.length;)if(o[h].isZero())o.splice(h,1);else{for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(r=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(r);return this._paintRects=o,o},n.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},n.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n&&this.ctxBack.scale(n,n))},n.prototype.clear=function(t,e,n){var i=this.dom,r=this.ctx,o=i.width,a=i.height;e=e||this.clearColor;var s=this.motionBlur&&!t,l=this.lastFrameAlpha,u=this.dpr,h=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/u,a/u));var c=this.domBack;function p(t,n,i,o){if(r.clearRect(t,n,i,o),e&&"transparent"!==e){var a=void 0;if(K(e))a=(e.global||e.__width===i&&e.__height===o)&&e.__canvasGradient||yv(r,e,{x:0,y:0,width:i,height:o}),e.__canvasGradient=a,e.__width=i,e.__height=o;else null!=e.image&&(e.scaleX=e.scaleX||u,e.scaleY=e.scaleY||u,a=Cv(r,e,{dirty:function(){h.setUnpainted(),h.painter.refresh()}}));r.save(),r.fillStyle=a||e,r.fillRect(t,n,i,o),r.restore()}s&&(r.save(),r.globalAlpha=l,r.drawImage(c,t,n,i,o),r.restore())}!n||s?p(0,0,o,a):n.length&&O(n,(function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)}))},n}(zt);var Ew=1e5,zw=314159,Fw=.01;const Vw=function(){function t(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=k({},n||{}),this.dpr=n.devicePixelRatio||er,this._singleCanvas=r,this.root=t,t.style&&(gt(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var a=this._layers;if(r){var s=t,l=s.width,u=s.height;null!=n.width&&(l=n.width),null!=n.height&&(u=n.height),this.dpr=n.devicePixelRatio||1,s.width=l*this.dpr,s.height=u*this.dpr,this._width=l,this._height=u;var h=new Bw(s,this,this.dpr);h.__builtin__=!0,h.initContext(),a[314159]=h,h.zlevel=zw,o.push(zw),this._domRoot=t}else{this._width=mv(t,0,n),this._height=mv(t,1,n);var c=this._domRoot=function(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}(this._width,this._height);t.appendChild(c)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o=i[r],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===r?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n||(n=this._hoverlayer=this.getLayer(Ew)),i||(i=n.ctx).save(),Nv(i,a,r,o===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(Ew)},t.prototype.paintOne=function(t,e){Rv(t,e)},t.prototype._paintList=function(t,e,n,i){if(this._redrawId===i){n=n||!1,this._updateLayerStatus(t);var r=this._doPaintList(t,e,n),o=r.finished,a=r.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;je((function(){s._paintList(t,e,n,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(zw).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)}))},t.prototype._doPaintList=function(t,e,n){for(var i=this,o=[],a=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s],u=this._layers[l];u.__builtin__&&u!==this._hoverlayer&&(u.__dirty||n)&&o.push(u)}for(var h=!0,c=!1,p=function(r){var s,l=o[r],u=l.ctx,p=a&&l.createRepaintRects(t,e,d._width,d._height),f=n?l.__startIndex:l.__drawIndex,g=!n&&l.incremental&&Date.now,y=g&&Date.now(),v=l.zlevel===d._zlevelList[0]?d._backgroundColor:null;if(l.__startIndex===l.__endIndex)l.clear(!1,v,p);else if(f===l.__startIndex){var m=t[f];m.incremental&&m.notClear&&!n||l.clear(!1,v,p)}-1===f&&(console.error("For some unknown reason. drawIndex is -1"),f=l.__startIndex);var _=function(e){var n={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(s=f;s<l.__endIndex;s++){var r=t[s];if(r.__inHover&&(c=!0),i._doPaintEl(r,l,a,e,n,s===l.__endIndex-1),g)if(Date.now()-y>15)break}n.prevElClipPaths&&u.restore()};if(p)if(0===p.length)s=l.__endIndex;else for(var x=d.dpr,w=0;w<p.length;++w){var b=p[w];u.save(),u.beginPath(),u.rect(b.x*x,b.y*x,b.width*x,b.height*x),u.clip(),_(b),u.restore()}else u.save(),_(),u.restore();l.__drawIndex=s,l.__drawIndex<l.__endIndex&&(h=!1)},d=this,f=0;f<o.length;f++)p(f);return r.wxa&&O(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:h,needsRefreshHover:c}},t.prototype._doPaintEl=function(t,e,n,i,r,o){var a=e.ctx;if(n){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&(Nv(a,t,r,o),t.setPrevPaintRect(s))}else Nv(a,t,r,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=zw);var n=this._layers[t];return n||((n=new Bw("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?C(n,this._layerConfig[t],!0):this._layerConfig[t-Fw]&&C(n,this._layerConfig[t-Fw],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},t.prototype.insertLayer=function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,o=this._domRoot,a=null,s=-1;if(!n[t]&&function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(r>0&&t>i[0]){for(s=0;s<r-1&&!(i[s]<t&&i[s+1]>t);s++);a=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},t.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},t.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},t.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){o&&(o.__endIndex!==t&&(o.__dirty=!0),o.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var n=1;n<t.length;n++){if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}}var i,r,o=null,a=0;for(r=0;r<t.length;r++){var s,l=(s=t[r]).zlevel,u=void 0;i!==l&&(i=l,a=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,a=1):u=this.getLayer(l+(a>0?Fw:0),this._needsManuallyCompositing),u.__builtin__||M("ZLevel "+l+" has been used by unkown layer "+u.id),u!==o&&(u.__used=!0,u.__startIndex!==r&&(u.__dirty=!0),u.__startIndex=r,u.incremental?u.__drawIndex=-1:u.__drawIndex=r,e(r),o=u),1&s.__dirty&&!s.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=r))}e(r),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,O(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?C(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];if(r===t||r===t+Fw)C(this._layers[r],n[t],!0)}}},t.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(A(n,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts,r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=mv(r,0,i),e=mv(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(zw).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new Bw("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var n=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer((function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];Nv(n,u,o,s===l-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}();function Hw(t){t.registerPainter("canvas",Vw)}function Ww(t,e){var n=t.mapDimensionsAll("defaultedLabel"),i=n.length;if(1===i){var r=Of(t,e,n[0]);return null!=r?r+"":null}if(i){for(var o=[],a=0;a<n.length;a++)o.push(Of(t,e,n[a]));return o.join(" ")}}function Gw(t,e){var n=t.mapDimensionsAll("defaultedLabel");if(!H(e))return e+"";for(var i=[],r=0;r<n.length;r++){var o=t.getDimensionIndex(n[r]);o>=0&&i.push(e[o])}return i.join(" ")}function Uw(t,e,n,i,r){var o=t.getArea(),a=o.x,s=o.y,l=o.width,u=o.height,h=n.get(["lineStyle","width"])||0;a-=h/2,s-=h/2,l+=h,u+=h,l=Math.ceil(l),a!==Math.floor(a)&&(a=Math.floor(a),l++);var c=new Hs({shape:{x:a,y:s,width:l,height:u}});if(e){var p=t.getBaseAxis(),d=p.isHorizontal(),f=p.inverse;d?(f&&(c.shape.x+=l),c.shape.width=0):(f||(c.shape.y+=u),c.shape.height=0);var g=W(r)?function(t){r(t,c)}:null;Dh(c,{shape:{width:l,height:u,x:a,y:s}},n,null,i,g)}return c}function Xw(t,e,n){var i=t.getArea(),r=Yr(i.r0,1),o=Yr(i.r,1),a=new Xu({shape:{cx:Yr(t.cx,1),cy:Yr(t.cy,1),r0:r,r:o,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});e&&("angle"===t.getBaseAxis().dim?a.shape.endAngle=i.startAngle:a.shape.r=r,Dh(a,{shape:{endAngle:i.endAngle,r:o}},n));return a}function Yw(t,e,n,i,r){return t?"polar"===t.type?Xw(t,e,n):"cartesian2d"===t.type?Uw(t,e,n,i,r):null:null}function qw(t,e){return t.type===e}var Zw={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},jw=function(t){return Math.round(t.length/2)};function Kw(t){return{seriesType:t,reset:function(t,e,n){var i=t.getData(),r=t.get("sampling"),o=t.coordinateSystem,a=i.count();if(a>10&&"cartesian2d"===o.type&&r){var s=o.getBaseAxis(),l=o.getOtherAxis(s),u=s.getExtent(),h=n.getDevicePixelRatio(),c=Math.abs(u[1]-u[0])*(h||1),p=Math.round(a/c);if(isFinite(p)&&p>1){"lttb"===r?t.setData(i.lttbDownSample(i.mapDimension(l.dim),1/p)):"minmax"===r&&t.setData(i.minmaxDownSample(i.mapDimension(l.dim),1/p));var d=void 0;G(r)?d=Zw[r]:W(r)&&(d=r),d&&t.setData(i.downSample(i.mapDimension(l.dim),1/p,d,jw))}}}}}var $w=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.getInitialData=function(t,e){return L_(null,this,{useEncodeDefaulter:!0})},n.prototype.getMarkerPosition=function(t,e,n){var i=this.coordinateSystem;if(i&&i.clampData){var r=i.clampData(t),o=i.dataToPoint(r);if(n)O(i.getAxes(),(function(t,n){if("category"===t.type&&null!=e){var i=t.getTicksCoords(),a=t.getTickModel().get("alignWithLabel"),s=r[n],l="x1"===e[n]||"y1"===e[n];if(l&&!a&&(s+=1),i.length<2)return;if(2===i.length)return void(o[n]=t.toGlobalCoord(t.getExtent()[l?1:0]));for(var u=void 0,h=void 0,c=1,p=0;p<i.length;p++){var d=i[p].coord,f=p===i.length-1?i[p-1].tickValue+c:i[p].tickValue;if(f===s){h=d;break}if(f<s)u=d;else if(null!=u&&f>s){h=(d+u)/2;break}1===p&&(c=f-i[0].tickValue)}null==h&&(u?u&&(h=i[i.length-1].coord):h=i[0].coord),o[n]=t.toGlobalCoord(h)}}));else{var a=this.getData(),s=a.getLayout("offset"),l=a.getLayout("size"),u=i.getBaseAxis().isHorizontal()?0:1;o[u]+=s+l/2}return o}return[NaN,NaN]},n.type="series.__base_bar__",n.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},n}(Ug);Ug.registerClass($w);const Qw=$w;const Jw=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.getInitialData=function(){return L_(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},n.prototype.getProgressive=function(){return!!this.get("large")&&this.get("progressive")},n.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},n.prototype.brushSelector=function(t,e,n){return n.rect(e.getItemLayout(t))},n.type="series.bar",n.dependencies=["grid","polar"],n.defaultOption=Vc(Qw.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),n}(Qw);var tb=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0};const eb=function(t){function n(e){var n=t.call(this,e)||this;return n.type="sausage",n}return e(n,t),n.prototype.getDefaultShape=function(){return new tb},n.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,h=e.clockwise,c=2*Math.PI,p=h?u-l<c:l-u<c;p||(l=u-(h?c:-c));var d=Math.cos(l),f=Math.sin(l),g=Math.cos(u),y=Math.sin(u);p?(t.moveTo(d*r+n,f*r+i),t.arc(d*s+n,f*s+i,a,-Math.PI+l,l,!h)):t.moveTo(d*o+n,f*o+i),t.arc(n,i,o,l,u,!h),t.arc(g*s+n,y*s+i,a,u-2*Math.PI,u-Math.PI,!h),0!==r&&t.arc(n,i,r,u,l,h)},n}(Cs);function nb(t,e,n){return e*Math.sin(t)*(n?-1:1)}function ib(t,e,n){return e*Math.cos(t)*(n?1:-1)}function rb(t,e,n){var i=t.get("borderRadius");if(null==i)return n?{cornerRadius:0}:null;H(i)||(i=[i,i,i,i]);var r=Math.abs(e.r||0-e.r0||0);return{cornerRadius:R(i,(function(t){return br(t,r)}))}}var ob=Math.max,ab=Math.min;var sb=function(t){function n(){var e=t.call(this)||this;return e.type=n.type,e._isFirstFrame=!0,e}return e(n,t),n.prototype.render=function(t,e,n,i){this._model=t,this._removeOnRenderedListener(n),this._updateDrawMode(t);var r=t.get("coordinateSystem");"cartesian2d"!==r&&"polar"!==r||(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n,i))},n.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},n.prototype.incrementalRender=function(t,e){this._progressiveEls=[],this._incrementalRenderLarge(t,e)},n.prototype.eachRendered=function(t){sc(this._progressiveEls||this.group,t)},n.prototype._updateDrawMode=function(t){var e=t.pipelineContext.large;null!=this._isLargeDraw&&e===this._isLargeDraw||(this._isLargeDraw=e,this._clear())},n.prototype._renderNormal=function(t,e,n,i){var r,o=this.group,a=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis();"cartesian2d"===l.type?r=u.isHorizontal():"polar"===l.type&&(r="angle"===u.dim);var h=t.isAnimationEnabled()?t:null,c=function(t,e){var n=t.get("realtimeSort",!0),i=e.getBaseAxis();if(n&&"category"===i.type&&"cartesian2d"===e.type)return{baseAxis:i,otherAxis:e.getOtherAxis(i)}}(t,l);c&&this._enableRealtimeSort(c,a,n);var p=t.get("clip",!0)||c,d=function(t,e){var n=t.getArea&&t.getArea();if(qw(t,"cartesian2d")){var i=t.getBaseAxis();if("category"!==i.type||!i.onBand){var r=e.getLayout("bandWidth");i.isHorizontal()?(n.x-=r,n.width+=2*r):(n.y-=r,n.height+=2*r)}}return n}(l,a);o.removeClipPath();var f=t.get("roundCap",!0),g=t.get("showBackground",!0),y=t.getModel("backgroundStyle"),v=y.get("borderRadius")||0,m=[],_=this._backgroundEls,x=i&&i.isInitSort,w=i&&"changeAxisOrder"===i.type;function b(t){var e=gb[l.type](a,t),n=function(t,e,n){var i="polar"===t.type?Xu:Hs;return new i({shape:bb(e,n,t),silent:!0,z2:0})}(l,r,e);return n.useStyle(y.getItemStyle()),"cartesian2d"===l.type?n.setShape("r",v):n.setShape("cornerRadius",v),m[t]=n,n}a.diff(s).add((function(e){var n=a.getItemModel(e),i=gb[l.type](a,e,n);if(g&&b(e),a.hasValue(e)&&fb[l.type](i)){var s=!1;p&&(s=lb[l.type](d,i));var y=ub[l.type](t,a,e,i,r,h,u.model,!1,f);c&&(y.forceLabelAnimation=!0),vb(y,a,e,n,i,t,r,"polar"===l.type),x?y.attr({shape:i}):c?hb(c,h,y,i,e,r,!1,!1):Dh(y,{shape:i},t,e),a.setItemGraphicEl(e,y),o.add(y),y.ignore=s}})).update((function(e,n){var i=a.getItemModel(e),S=gb[l.type](a,e,i);if(g){var M=void 0;0===_.length?M=b(n):((M=_[n]).useStyle(y.getItemStyle()),"cartesian2d"===l.type?M.setShape("r",v):M.setShape("cornerRadius",v),m[e]=M);var T=gb[l.type](a,e);kh(M,{shape:bb(r,T,l)},h,e)}var C=s.getItemGraphicEl(n);if(a.hasValue(e)&&fb[l.type](S)){var I=!1;if(p&&(I=lb[l.type](d,S))&&o.remove(C),C?Rh(C):C=ub[l.type](t,a,e,S,r,h,u.model,!!C,f),c&&(C.forceLabelAnimation=!0),w){var k=C.getTextContent();if(k){var D=wc(k);null!=D.prevValue&&(D.prevValue=D.value)}}else vb(C,a,e,i,S,t,r,"polar"===l.type);x?C.attr({shape:S}):c?hb(c,h,C,S,e,r,!0,w):kh(C,{shape:S},t,e,null),a.setItemGraphicEl(e,C),C.ignore=I,o.add(C)}else o.remove(C)})).remove((function(e){var n=s.getItemGraphicEl(e);n&&Oh(n,t,e)})).execute();var S=this._backgroundGroup||(this._backgroundGroup=new Br);S.removeAll();for(var M=0;M<m.length;++M)S.add(m[M]);o.add(S),this._backgroundEls=m,this._data=a},n.prototype._renderLarge=function(t,e,n){this._clear(),xb(t,this.group),this._updateLargeClip(t)},n.prototype._incrementalRenderLarge=function(t,e){this._removeBackground(),xb(e,this.group,this._progressiveEls,!0)},n.prototype._updateLargeClip=function(t){var e=t.get("clip",!0)&&Yw(t.coordinateSystem,!1,t),n=this.group;e?n.setClipPath(e):n.removeClipPath()},n.prototype._enableRealtimeSort=function(t,e,n){var i=this;if(e.count()){var r=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(e,t,n),this._isFirstFrame=!1;else{var o=function(t){var n=e.getItemGraphicEl(t),i=n&&n.shape;return i&&Math.abs(r.isHorizontal()?i.height:i.width)||0};this._onRendered=function(){i._updateSortWithinSameData(e,o,r,n)},n.getZr().on("rendered",this._onRendered)}}},n.prototype._dataSort=function(t,e,n){var i=[];return t.each(t.mapDimension(e.dim),(function(t,e){var r=n(e);r=null==r?NaN:r,i.push({dataIndex:e,mappedValue:r,ordinalNumber:t})})),i.sort((function(t,e){return e.mappedValue-t.mappedValue})),{ordinalNumbers:R(i,(function(t){return t.ordinalNumber}))}},n.prototype._isOrderChangedWithinSameData=function(t,e,n){for(var i=n.scale,r=t.mapDimension(n.dim),o=Number.MAX_VALUE,a=0,s=i.getOrdinalMeta().categories.length;a<s;++a){var l=t.rawIndexOf(r,i.getRawOrdinalNumber(a)),u=l<0?Number.MIN_VALUE:e(t.indexOfRawIndex(l));if(u>o)return!0;o=u}return!1},n.prototype._isOrderDifferentInView=function(t,e){for(var n=e.scale,i=n.getExtent(),r=Math.max(0,i[0]),o=Math.min(i[1],n.getOrdinalMeta().categories.length-1);r<=o;++r)if(t.ordinalNumbers[r]!==n.getRawOrdinalNumber(r))return!0},n.prototype._updateSortWithinSameData=function(t,e,n,i){if(this._isOrderChangedWithinSameData(t,e,n)){var r=this._dataSort(t,n,e);this._isOrderDifferentInView(r,n)&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",axisId:n.index,sortInfo:r}))}},n.prototype._dispatchInitSort=function(t,e,n){var i=e.baseAxis,r=this._dataSort(t,i,(function(n){return t.get(t.mapDimension(e.otherAxis.dim),n)}));n.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:r})},n.prototype.remove=function(t,e){this._clear(this._model),this._removeOnRenderedListener(e)},n.prototype.dispose=function(t,e){this._removeOnRenderedListener(e)},n.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},n.prototype._clear=function(t){var e=this.group,n=this._data;t&&t.isAnimationEnabled()&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl((function(e){Oh(e,t,rl(e).dataIndex)}))):e.removeAll(),this._data=null,this._isFirstFrame=!0},n.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},n.type="bar",n}(ny),lb={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height);var r=t.x+t.width,o=t.y+t.height,a=ob(e.x,t.x),s=ab(e.x+e.width,r),l=ob(e.y,t.y),u=ab(e.y+e.height,o),h=s<a,c=u<l;return e.x=h&&a>r?s:a,e.y=c&&l>o?u:l,e.width=h?0:s-a,e.height=c?0:u-l,n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),h||c},polar:function(t,e){var n=e.r0<=e.r?1:-1;if(n<0){var i=e.r;e.r=e.r0,e.r0=i}var r=ab(e.r,t.r),o=ob(e.r0,t.r0);e.r=r,e.r0=o;var a=r-o<0;if(n<0){i=e.r;e.r=e.r0,e.r0=i}return a}},ub={cartesian2d:function(t,e,n,i,r,o,a,s,l){var u=new Hs({shape:k({},i),z2:1});(u.__dataIndex=n,u.name="item",o)&&(u.shape[r?"height":"width"]=0);return u},polar:function(t,e,n,i,r,o,a,s,l){var u=!r&&l?eb:Xu,h=new u({shape:i,z2:1});h.name="item";var c,p,d=yb(r);if(h.calculateTextPosition=(c=d,p=({isRoundCap:u===eb}||{}).isRoundCap,function(t,e,n){var i=e.position;if(!i||i instanceof Array)return Sr(t,e,n);var r=c(i),o=null!=e.distance?e.distance:5,a=this.shape,s=a.cx,l=a.cy,u=a.r,h=a.r0,d=(u+h)/2,f=a.startAngle,g=a.endAngle,y=(f+g)/2,v=p?Math.abs(u-h)/2:0,m=Math.cos,_=Math.sin,x=s+u*m(f),w=l+u*_(f),b="left",S="top";switch(r){case"startArc":x=s+(h-o)*m(y),w=l+(h-o)*_(y),b="center",S="top";break;case"insideStartArc":x=s+(h+o)*m(y),w=l+(h+o)*_(y),b="center",S="bottom";break;case"startAngle":x=s+d*m(f)+nb(f,o+v,!1),w=l+d*_(f)+ib(f,o+v,!1),b="right",S="middle";break;case"insideStartAngle":x=s+d*m(f)+nb(f,-o+v,!1),w=l+d*_(f)+ib(f,-o+v,!1),b="left",S="middle";break;case"middle":x=s+d*m(y),w=l+d*_(y),b="center",S="middle";break;case"endArc":x=s+(u+o)*m(y),w=l+(u+o)*_(y),b="center",S="bottom";break;case"insideEndArc":x=s+(u-o)*m(y),w=l+(u-o)*_(y),b="center",S="top";break;case"endAngle":x=s+d*m(g)+nb(g,o+v,!0),w=l+d*_(g)+ib(g,o+v,!0),b="left",S="middle";break;case"insideEndAngle":x=s+d*m(g)+nb(g,-o+v,!0),w=l+d*_(g)+ib(g,-o+v,!0),b="right",S="middle";break;default:return Sr(t,e,n)}return(t=t||{}).x=x,t.y=w,t.align=b,t.verticalAlign=S,t}),o){var f=r?"r":"endAngle",g={};h.shape[f]=r?i.r0:i.startAngle,g[f]=i[f],(s?kh:Dh)(h,{shape:g},o)}return h}};function hb(t,e,n,i,r,o,a,s){var l,u;o?(u={x:i.x,width:i.width},l={y:i.y,height:i.height}):(u={y:i.y,height:i.height},l={x:i.x,width:i.width}),s||(a?kh:Dh)(n,{shape:l},e,r,null),(a?kh:Dh)(n,{shape:u},e?t.baseAxis.model:null,r)}function cb(t,e){for(var n=0;n<e.length;n++)if(!isFinite(t[e[n]]))return!0;return!1}var pb=["x","y","width","height"],db=["cx","cy","r","startAngle","endAngle"],fb={cartesian2d:function(t){return!cb(t,pb)},polar:function(t){return!cb(t,db)}},gb={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=n?function(t,e){var n=t.get(["itemStyle","borderColor"]);if(!n||"none"===n)return 0;var i=t.get(["itemStyle","borderWidth"])||0,r=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),o=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(i,r,o)}(n,i):0,o=i.width>0?1:-1,a=i.height>0?1:-1;return{x:i.x+o*r/2,y:i.y+a*r/2,width:i.width-o*r,height:i.height-a*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}}};function yb(t){return e=t?"Arc":"Angle",function(t){switch(t){case"start":case"insideStart":case"end":case"insideEnd":return t+e;default:return t}};var e}function vb(t,e,n,i,r,o,a,s){var l=e.getItemVisual(n,"style");if(s){if(!o.get("roundCap")){var u=t.shape;k(u,rb(i.getModel("itemStyle"),u,!0)),t.setShape(u)}}else{var h=i.get(["itemStyle","borderRadius"])||0;t.setShape("r",h)}t.useStyle(l);var c=i.getShallow("cursor");c&&t.attr("cursor",c);var p=s?a?r.r>=r.r0?"endArc":"startArc":r.endAngle>=r.startAngle?"endAngle":"startAngle":a?r.height>=0?"bottom":"top":r.width>=0?"right":"left",d=dc(i);pc(t,d,{labelFetcher:o,labelDataIndex:n,defaultText:Ww(o.getData(),n),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:p});var f=t.getTextContent();if(s&&f){var g=i.get(["label","position"]);t.textConfig.inside="middle"===g||null,function(t,e,n,i){if(X(i))t.setTextConfig({rotation:i});else if(H(e))t.setTextConfig({rotation:0});else{var r,o=t.shape,a=o.clockwise?o.startAngle:o.endAngle,s=o.clockwise?o.endAngle:o.startAngle,l=(a+s)/2,u=n(e);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":r=l;break;case"startAngle":case"insideStartAngle":r=a;break;case"endAngle":case"insideEndAngle":r=s;break;default:return void t.setTextConfig({rotation:0})}var h=1.5*Math.PI-r;"middle"===u&&h>Math.PI/2&&h<1.5*Math.PI&&(h-=Math.PI),t.setTextConfig({rotation:h})}}(t,"outside"===g?p:g,yb(a),i.get(["label","rotate"]))}bc(f,d,o.getRawValue(n),(function(t){return Gw(e,t)}));var y=i.getModel(["emphasis"]);Zl(t,y.get("focus"),y.get("blurScope"),y.get("disabled")),Ql(t,i),function(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}(r)&&(t.style.fill="none",t.style.stroke="none",O(t.states,(function(t){t.style&&(t.style.fill=t.style.stroke="none")})))}var mb=function(){},_b=function(t){function n(e){var n=t.call(this,e)||this;return n.type="largeBar",n}return e(n,t),n.prototype.getDefaultShape=function(){return new mb},n.prototype.buildPath=function(t,e){for(var n=e.points,i=this.baseDimIdx,r=1-this.baseDimIdx,o=[],a=[],s=this.barWidth,l=0;l<n.length;l+=3)a[i]=s,a[r]=n[l+2],o[i]=n[l+i],o[r]=n[l+r],t.rect(o[0],o[1],a[0],a[1])},n}(Cs);function xb(t,e,n,i){var r=t.getData(),o=r.getLayout("valueAxisHorizontal")?1:0,a=r.getLayout("largeDataIndices"),s=r.getLayout("size"),l=t.getModel("backgroundStyle"),u=r.getLayout("largeBackgroundPoints");if(u){var h=new _b({shape:{points:u},incremental:!!i,silent:!0,z2:0});h.baseDimIdx=o,h.largeDataIndices=a,h.barWidth=s,h.useStyle(l.getItemStyle()),e.add(h),n&&n.push(h)}var c=new _b({shape:{points:r.getLayout("largePoints")},incremental:!!i,ignoreCoarsePointer:!0,z2:1});c.baseDimIdx=o,c.largeDataIndices=a,c.barWidth=s,e.add(c),c.useStyle(r.getVisual("style")),c.style.stroke=null,rl(c).seriesIndex=t.seriesIndex,t.get("silent")||(c.on("mousedown",wb),c.on("mousemove",wb)),n&&n.push(c)}var wb=ay((function(t){var e=function(t,e,n){for(var i=t.baseDimIdx,r=1-i,o=t.shape.points,a=t.largeDataIndices,s=[],l=[],u=t.barWidth,h=0,c=o.length/3;h<c;h++){var p=3*h;if(l[i]=u,l[r]=o[p+2],s[i]=o[p+i],s[r]=o[p+r],l[r]<0&&(s[r]+=l[r],l[r]=-l[r]),e>=s[0]&&e<=s[0]+l[0]&&n>=s[1]&&n<=s[1]+l[1])return a[h]}return-1}(this,t.offsetX,t.offsetY);rl(this).dataIndex=e>=0?e:null}),30,!1);function bb(t,e,n){if(qw(n,"cartesian2d")){var i=e,r=n.getArea();return{x:t?i.x:r.x,y:t?r.y:i.y,width:t?i.width:r.width,height:t?r.height:i.height}}var o=e;return{cx:(r=n.getArea()).cx,cy:r.cy,r0:t?r.r0:o.r0,r:t?r.r:o.r,startAngle:t?o.startAngle:0,endAngle:t?o.endAngle:2*Math.PI}}const Sb=sb;function Mb(t){t.registerChartView(Sb),t.registerSeriesModel(Jw),t.registerLayout(t.PRIORITY.VISUAL.LAYOUT,V(sx,"bar")),t.registerLayout(t.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,lx("bar")),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,Kw("bar")),t.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},(function(t,e){var n=t.componentType||"series";e.eachComponent({mainType:n,query:t},(function(e){t.sortInfo&&e.axis.setCategorySortInfo(t.sortInfo)}))}))}var Tb=2*Math.PI,Cb=Math.PI/180;function Ib(t,e){return Hp(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function kb(t,e){var n=Ib(t,e),i=t.get("center"),r=t.get("radius");H(r)||(r=[0,r]);var o,a,s=Xr(n.width,e.getWidth()),l=Xr(n.height,e.getHeight()),u=Math.min(s,l),h=Xr(r[0],u/2),c=Xr(r[1],u/2),p=t.coordinateSystem;if(p){var d=p.dataToPoint(i);o=d[0]||0,a=d[1]||0}else H(i)||(i=[i,i]),o=Xr(i[0],s)+n.x,a=Xr(i[1],l)+n.y;return{cx:o,cy:a,r0:h,r:c}}function Db(t,e,n){e.eachSeriesByType(t,(function(t){var e=t.getData(),i=e.mapDimension("value"),r=Ib(t,n),o=kb(t,n),a=o.cx,s=o.cy,l=o.r,u=o.r0,h=-t.get("startAngle")*Cb,c=t.get("endAngle"),p=t.get("padAngle")*Cb;c="auto"===c?h-Tb:-c*Cb;var d=t.get("minAngle")*Cb+p,f=0;e.each(i,(function(t){!isNaN(t)&&f++}));var g=e.getSum(i),y=Math.PI/(g||f)*2,v=t.get("clockwise"),m=t.get("roseType"),_=t.get("stillShowZeroSum"),x=e.getDataExtent(i);x[0]=0;var w=v?1:-1,b=[h,c],S=w*p/2;os(b,!v),h=b[0],c=b[1];var M=Ab(t);M.startAngle=h,M.endAngle=c,M.clockwise=v;var T=Math.abs(c-h),C=T,I=0,k=h;if(e.setLayout({viewRect:r,r:l}),e.each(i,(function(t,n){var i;if(isNaN(t))e.setItemLayout(n,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:v,cx:a,cy:s,r0:u,r:m?NaN:l});else{(i="area"!==m?0===g&&_?y:t*y:T/f)<d?(i=d,C-=d):I+=t;var r=k+w*i,o=0,h=0;p>i?h=o=k+w*i/2:(o=k+S,h=r-S),e.setItemLayout(n,{angle:i,startAngle:o,endAngle:h,clockwise:v,cx:a,cy:s,r0:u,r:m?Ur(t,x,[u,l]):l}),k=r}})),C<Tb&&f)if(C<=.001){var D=T/f;e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n);i.angle=D;var r=0,o=0;D<p?o=r=h+w*(n+.5)*D:(r=h+w*n*D+S,o=h+w*(n+1)*D-S),i.startAngle=r,i.endAngle=o}}))}else y=C/I,k=h,e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n),r=i.angle===d?d:t*y,o=0,a=0;r<p?a=o=k+w*r/2:(o=k+S,a=k+w*r-S),i.startAngle=o,i.endAngle=a,k+=w*r}}))}))}var Ab=Oo();function Pb(t){return{seriesType:t,reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(n&&n.length){var i=t.getData();i.filterSelf((function(t){for(var e=i.getName(t),r=0;r<n.length;r++)if(!n[r].isSelected(e))return!1;return!0}))}}}}var Lb=Math.PI/180;function Ob(t,e,n,i,r,o,a,s,l,u){if(!(t.length<2)){for(var h=t.length,c=0;c<h;c++)if("outer"===t[c].position&&"labelLine"===t[c].labelAlignTo){var p=t[c].label.x-u;t[c].linePoints[1][0]+=p,t[c].label.x=u}Ow(t,l,l+a)&&function(t){for(var o={list:[],maxY:0},a={list:[],maxY:0},s=0;s<t.length;s++)if("none"===t[s].labelAlignTo){var l=t[s],u=l.label.y>n?a:o,h=Math.abs(l.label.y-n);if(h>=u.maxY){var c=l.label.x-e-l.len2*r,p=i+l.len,f=Math.abs(c)<p?Math.sqrt(h*h/(1-c*c/p/p)):p;u.rB=f,u.maxY=h}u.list.push(l)}d(o),d(a)}(t)}function d(t){for(var o=t.rB,a=o*o,s=0;s<t.list.length;s++){var l=t.list[s],u=Math.abs(l.label.y-n),h=i+l.len,c=h*h,p=Math.sqrt(Math.abs((1-u*u/a)*c)),d=e+(p+l.len2)*r,f=d-l.label.x;Rb(l,l.targetTextWidth-f*r,!0),l.label.x=d}}}function Rb(t,e,n){if(void 0===n&&(n=!1),null==t.labelStyleWidth){var i=t.label,r=i.style,o=t.rect,a=r.backgroundColor,s=r.padding,l=s?s[1]+s[3]:0,u=r.overflow,h=o.width+(a?0:l);if(e<h||n){var c=o.height;if(u&&u.match("break")){i.setStyle("backgroundColor",null),i.setStyle("width",e-l);var p=i.getBoundingRect();i.setStyle("width",Math.ceil(p.width)),i.setStyle("backgroundColor",a)}else{var d=e-l,f=e<h?d:n?d>t.unconstrainedWidth?null:d:null;i.setStyle("width",f)}var g=i.getBoundingRect();o.width=g.width;var y=(i.style.margin||0)+2.1;o.height=g.height+y,o.y-=(o.height-c)/2}}}function Nb(t){return"center"===t.position}function Bb(t){var e,n,i=t.getData(),r=[],o=!1,a=(t.get("minShowLabelAngle")||0)*Lb,s=i.getLayout("viewRect"),l=i.getLayout("r"),u=s.width,h=s.x,c=s.y,p=s.height;function d(t){t.ignore=!0}i.each((function(t){var s=i.getItemGraphicEl(t),c=s.shape,p=s.getTextContent(),f=s.getTextGuideLine(),g=i.getItemModel(t),y=g.getModel("label"),v=y.get("position")||g.get(["emphasis","label","position"]),m=y.get("distanceToLabelLine"),_=y.get("alignTo"),x=Xr(y.get("edgeDistance"),u),w=y.get("bleedMargin"),b=g.getModel("labelLine"),S=b.get("length");S=Xr(S,u);var M=b.get("length2");if(M=Xr(M,u),Math.abs(c.endAngle-c.startAngle)<a)return O(p.states,d),p.ignore=!0,void(f&&(O(f.states,d),f.ignore=!0));if(function(t){if(!t.ignore)return!0;for(var e in t.states)if(!1===t.states[e].ignore)return!0;return!1}(p)){var T,C,I,k,D=(c.startAngle+c.endAngle)/2,A=Math.cos(D),P=Math.sin(D);e=c.cx,n=c.cy;var L="inside"===v||"inner"===v;if("center"===v)T=c.cx,C=c.cy,k="center";else{var R=(L?(c.r+c.r0)/2*A:c.r*A)+e,N=(L?(c.r+c.r0)/2*P:c.r*P)+n;if(T=R+3*A,C=N+3*P,!L){var B=R+A*(S+l-c.r),E=N+P*(S+l-c.r),z=B+(A<0?-1:1)*M;T="edge"===_?A<0?h+x:h+u-x:z+(A<0?-m:m),C=E,I=[[R,N],[B,E],[z,E]]}k=L?"center":"edge"===_?A>0?"right":"left":A>0?"left":"right"}var F=Math.PI,V=0,H=y.get("rotate");if(X(H))V=H*(F/180);else if("center"===v)V=0;else if("radial"===H||!0===H){V=A<0?-D+F:-D}else if("tangential"===H&&"outside"!==v&&"outer"!==v){var W=Math.atan2(A,P);W<0&&(W=2*F+W),P>0&&(W=F+W),V=W-F}if(o=!!V,p.x=T,p.y=C,p.rotation=V,p.setStyle({verticalAlign:"middle"}),L){p.setStyle({align:k});var G=p.states.select;G&&(G.x+=p.x,G.y+=p.y)}else{var U=p.getBoundingRect().clone();U.applyTransform(p.getComputedTransform());var Y=(p.style.margin||0)+2.1;U.y-=Y/2,U.height+=Y,r.push({label:p,labelLine:f,position:v,len:S,len2:M,minTurnAngle:b.get("minTurnAngle"),maxSurfaceAngle:b.get("maxSurfaceAngle"),surfaceNormal:new ve(A,P),linePoints:I,textAlign:k,labelDistance:m,labelAlignTo:_,edgeDistance:x,bleedMargin:w,rect:U,unconstrainedWidth:U.width,labelStyleWidth:p.style.width})}s.setTextConfig({inside:L})}})),!o&&t.get("avoidLabelOverlap")&&function(t,e,n,i,r,o,a,s){for(var l=[],u=[],h=Number.MAX_VALUE,c=-Number.MAX_VALUE,p=0;p<t.length;p++){var d=t[p].label;Nb(t[p])||(d.x<e?(h=Math.min(h,d.x),l.push(t[p])):(c=Math.max(c,d.x),u.push(t[p])))}for(p=0;p<t.length;p++)if(!Nb(y=t[p])&&y.linePoints){if(null!=y.labelStyleWidth)continue;d=y.label;var f=y.linePoints,g=void 0;g="edge"===y.labelAlignTo?d.x<e?f[2][0]-y.labelDistance-a-y.edgeDistance:a+r-y.edgeDistance-f[2][0]-y.labelDistance:"labelLine"===y.labelAlignTo?d.x<e?h-a-y.bleedMargin:a+r-c-y.bleedMargin:d.x<e?d.x-a-y.bleedMargin:a+r-d.x-y.bleedMargin,y.targetTextWidth=g,Rb(y,g)}for(Ob(u,e,n,i,1,0,o,0,s,c),Ob(l,e,n,i,-1,0,o,0,s,h),p=0;p<t.length;p++){var y;if(!Nb(y=t[p])&&y.linePoints){d=y.label,f=y.linePoints;var v="edge"===y.labelAlignTo,m=d.style.padding,_=m?m[1]+m[3]:0,x=d.style.backgroundColor?0:_,w=y.rect.width+x,b=f[1][0]-f[2][0];v?d.x<e?f[2][0]=a+y.edgeDistance+w+y.labelDistance:f[2][0]=a+r-y.edgeDistance-w-y.labelDistance:(d.x<e?f[2][0]=d.x+y.labelDistance:f[2][0]=d.x-y.labelDistance,f[1][0]=f[2][0]+b),f[1][1]=f[2][1]=d.y}}}(r,e,n,l,u,p,h,c);for(var f=0;f<r.length;f++){var g=r[f],y=g.label,v=g.labelLine,m=isNaN(y.x)||isNaN(y.y);if(y){y.setStyle({align:g.textAlign}),m&&(O(y.states,d),y.ignore=!0);var _=y.states.select;_&&(_.x+=y.x,_.y+=y.y)}if(v){var x=g.linePoints;m||!x?(O(v.states,d),v.ignore=!0):(Mw(x,g.minTurnAngle),Tw(x,g.surfaceNormal,g.maxSurfaceAngle),v.setShape({points:x}),y.__hostTarget.textGuideLineConfig={anchor:new ve(x[0][0],x[0][1])})}}}var Eb=function(t){function n(e,n,i){var r=t.call(this)||this;r.z2=2;var o=new il;return r.setTextContent(o),r.updateData(e,n,i,!0),r}return e(n,t),n.prototype.updateData=function(t,e,n,i){var r=this,o=t.hostModel,a=t.getItemModel(e),s=a.getModel("emphasis"),l=t.getItemLayout(e),u=k(rb(a.getModel("itemStyle"),l,!0),l);if(isNaN(u.startAngle))r.setShape(u);else{if(i){r.setShape(u);var h=o.getShallow("animationType");o.ecModel.ssr?(Dh(r,{scaleX:0,scaleY:0},o,{dataIndex:e,isFrom:!0}),r.originX=u.cx,r.originY=u.cy):"scale"===h?(r.shape.r=l.r0,Dh(r,{shape:{r:l.r}},o,e)):null!=n?(r.setShape({startAngle:n,endAngle:n}),Dh(r,{shape:{startAngle:l.startAngle,endAngle:l.endAngle}},o,e)):(r.shape.endAngle=l.startAngle,kh(r,{shape:{endAngle:l.endAngle}},o,e))}else Rh(r),kh(r,{shape:u},o,e);r.useStyle(t.getItemVisual(e,"style")),Ql(r,a);var c=(l.startAngle+l.endAngle)/2,p=o.get("selectedOffset"),d=Math.cos(c)*p,f=Math.sin(c)*p,g=a.getShallow("cursor");g&&r.attr("cursor",g),this._updateLabel(o,t,e),r.ensureState("emphasis").shape=k({r:l.r+(s.get("scale")&&s.get("scaleSize")||0)},rb(s.getModel("itemStyle"),l)),k(r.ensureState("select"),{x:d,y:f,shape:rb(a.getModel(["select","itemStyle"]),l)}),k(r.ensureState("blur"),{shape:rb(a.getModel(["blur","itemStyle"]),l)});var y=r.getTextGuideLine(),v=r.getTextContent();y&&k(y.ensureState("select"),{x:d,y:f}),k(v.ensureState("select"),{x:d,y:f}),Zl(this,s.get("focus"),s.get("blurScope"),s.get("disabled"))}},n.prototype._updateLabel=function(t,e,n){var i=this,r=e.getItemModel(n),o=r.getModel("labelLine"),a=e.getItemVisual(n,"style"),s=a&&a.fill,l=a&&a.opacity;pc(i,dc(r),{labelFetcher:e.hostModel,labelDataIndex:n,inheritColor:s,defaultOpacity:l,defaultText:t.getFormattedLabel(n,"normal")||e.getName(n)});var u=i.getTextContent();i.setTextConfig({position:null,rotation:null}),u.attr({z2:10});var h=t.get(["label","position"]);if("outside"!==h&&"outer"!==h)i.removeTextGuideLine();else{var c=this.getTextGuideLine();c||(c=new eh,this.setTextGuideLine(c)),kw(this,Dw(r),{stroke:s,opacity:et(o.get(["lineStyle","opacity"]),l,1)})}},n}(Xu);const zb=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.ignoreLabelLineUpdate=!0,e}return e(n,t),n.prototype.render=function(t,e,n,i){var r,o=t.getData(),a=this._data,s=this.group;if(!a&&o.count()>0){for(var l=o.getItemLayout(0),u=1;isNaN(l&&l.startAngle)&&u<o.count();++u)l=o.getItemLayout(u);l&&(r=l.startAngle)}if(this._emptyCircleSector&&s.remove(this._emptyCircleSector),0===o.count()&&t.get("showEmptyCircle")){var h=Ab(t),c=new Xu({shape:k(kb(t,n),h)});c.useStyle(t.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=c,s.add(c)}o.diff(a).add((function(t){var e=new Eb(o,t,r);o.setItemGraphicEl(t,e),s.add(e)})).update((function(t,e){var n=a.getItemGraphicEl(e);n.updateData(o,t,r),n.off("click"),s.add(n),o.setItemGraphicEl(t,n)})).remove((function(e){Oh(a.getItemGraphicEl(e),t,e)})).execute(),Bb(t),"expansion"!==t.get("animationTypeUpdate")&&(this._data=o)},n.prototype.dispose=function(){},n.prototype.containPoint=function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,r=t[1]-n.cy,o=Math.sqrt(i*i+r*r);return o<=n.r&&o>=n.r0}},n.type="pie",n}(ny);function Fb(t,e,n){e=H(e)&&{coordDimensions:e}||k({encodeDefine:t.getEncode()},e);var i=t.getSource(),r=M_(i,e).dimensions,o=new S_(r,t);return o.initData(i,n),o}const Vb=function(){function t(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}return t.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},t.prototype.containName=function(t){return this._getRawData().indexOfName(t)>=0},t.prototype.indexOfName=function(t){return this._getDataWithEncodedVisual().indexOfName(t)},t.prototype.getItemVisual=function(t,e){return this._getDataWithEncodedVisual().getItemVisual(t,e)},t}();var Hb=Oo();const Wb=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new Vb(F(this.getData,this),F(this.getRawData,this)),this._defaultLabelLine(e)},n.prototype.mergeOption=function(){t.prototype.mergeOption.apply(this,arguments)},n.prototype.getInitialData=function(){return Fb(this,{coordDimensions:["value"],encodeDefaulter:V(fd,this)})},n.prototype.getDataParams=function(e){var n=this.getData(),i=Hb(n),r=i.seats;if(!r){var o=[];n.each(n.mapDimension("value"),(function(t){o.push(t)})),r=i.seats=function(t,e){var n=N(t,(function(t,e){return t+(isNaN(e)?0:e)}),0);if(0===n)return[];for(var i=Math.pow(10,e),r=R(t,(function(t){return(isNaN(t)?0:t)/n*i*100})),o=100*i,a=R(r,(function(t){return Math.floor(t)})),s=N(a,(function(t,e){return t+e}),0),l=R(r,(function(t,e){return t-a[e]}));s<o;){for(var u=Number.NEGATIVE_INFINITY,h=null,c=0,p=l.length;c<p;++c)l[c]>u&&(u=l[c],h=c);++a[h],l[h]=0,++s}return R(a,(function(t){return t/i}))}(o,n.hostModel.get("percentPrecision"))}var a=t.prototype.getDataParams.call(this,e);return a.percent=r[e]||0,a.$vars.push("percent"),a},n.prototype._defaultLabelLine=function(t){xo(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},n.type="series.pie",n.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},n}(Ug);function Gb(t){t.registerChartView(zb),t.registerSeriesModel(Wb),$y("pie",t.registerAction),t.registerLayout(V(Db,"pie")),t.registerProcessor(Pb("pie")),t.registerProcessor({seriesType:"pie",reset:function(t,e){var n=t.getData();n.filterSelf((function(t){var e=n.mapDimension("value"),i=n.get(e,t);return!(X(i)&&!isNaN(i)&&i<0)}))}})}const Ub=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.type="grid",n.dependencies=["xAxis","yAxis"],n.layoutMode="box",n.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},n}(Kp);var Xb=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Eo).models[0]},n.type="cartesian2dAxis",n}(Kp);P(Xb,Yx);var Yb={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},qb=C({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Yb),Zb=C({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Yb);const jb={category:qb,value:Zb,time:C({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Zb),log:D({logBase:10},Zb)};var Kb={value:1,category:1,time:1,log:1};function $b(t,n,i,r){O(Kb,(function(o,a){var s=C(C({},jb[a],!0),r,!0),l=function(t){function i(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n+"Axis."+a,e}return e(i,t),i.prototype.mergeDefaultAndTheme=function(t,e){var n=Up(this),i=n?Yp(t):{};C(t,e.getTheme().get(a+"Axis")),C(t,this.getDefaultOption()),t.type=Qb(t),n&&Xp(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=E_.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=n+"Axis."+a,i.defaultOption=s,i}(i);t.registerComponentModel(l)})),t.registerSubTypeDefaulter(n+"Axis",Qb)}function Qb(t){return t.type||(t.data?"category":"value")}var Jb=function(){function t(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return t.prototype.getAxis=function(t){return this._axes[t]},t.prototype.getAxes=function(){return R(this._dimList,(function(t){return this._axes[t]}),this)},t.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),B(this.getAxes(),(function(e){return e.scale.type===t}))},t.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},t}();var tS=["x","y"];function eS(t){return"interval"===t.type||"time"===t.type}var nS=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=tS,e}return e(n,t),n.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,e=this.getAxis("y").scale;if(eS(t)&&eS(e)){var n=t.getExtent(),i=e.getExtent(),r=this.dataToPoint([n[0],i[0]]),o=this.dataToPoint([n[1],i[1]]),a=n[1]-n[0],s=i[1]-i[0];if(a&&s){var l=(o[0]-r[0])/a,u=(o[1]-r[1])/s,h=r[0]-n[0]*l,c=r[1]-i[0]*u,p=this._transform=[l,0,0,u,h,c];this._invTransform=ge([],p)}}},n.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},n.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},n.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},n.prototype.containZone=function(t,e){var n=this.dataToPoint(t),i=this.dataToPoint(e),r=this.getArea(),o=new Ie(n[0],n[1],i[0]-n[0],i[1]-n[1]);return r.intersect(o)},n.prototype.dataToPoint=function(t,e,n){n=n||[];var i=t[0],r=t[1];if(this._transform&&null!=i&&isFinite(i)&&null!=r&&isFinite(r))return Ot(n,t,this._transform);var o=this.getAxis("x"),a=this.getAxis("y");return n[0]=o.toGlobalCoord(o.dataToCoord(i,e)),n[1]=a.toGlobalCoord(a.dataToCoord(r,e)),n},n.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),a=n.parse(t[0]),s=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},n.prototype.pointToData=function(t,e){var n=[];if(this._invTransform)return Ot(n,t,this._invTransform);var i=this.getAxis("x"),r=this.getAxis("y");return n[0]=i.coordToData(i.toLocalCoord(t[0]),e),n[1]=r.coordToData(r.toLocalCoord(t[1]),e),n},n.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},n.prototype.getArea=function(t){t=t||0;var e=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),i=Math.min(e[0],e[1])-t,r=Math.min(n[0],n[1])-t,o=Math.max(e[0],e[1])-i+t,a=Math.max(n[0],n[1])-r+t;return new Ie(i,r,o,a)},n}(Jb);const iS=function(t){function n(e,n,i,r,o){var a=t.call(this,e,n,i)||this;return a.index=0,a.type=r||"value",a.position=o||"bottom",a}return e(n,t),n.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},n.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},n.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},n.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},n}(ow);function rS(t,e,n){n=n||{};var i=t.coordinateSystem,r=e.axis,o={},a=r.getAxesOnZeroOf()[0],s=r.position,l=a?"onZero":s,u=r.dim,h=i.getRect(),c=[h.x,h.x+h.width,h.y,h.y+h.height],p={left:0,right:1,top:0,bottom:1,onZero:2},d=e.get("offset")||0,f="x"===u?[c[2]-d,c[3]+d]:[c[0]-d,c[1]+d];if(a){var g=a.toGlobalCoord(a.dataToCoord(0));f[p.onZero]=Math.max(Math.min(g,f[1]),f[0])}o.position=["y"===u?f[p[l]]:c[0],"x"===u?f[p[l]]:c[3]],o.rotation=Math.PI/2*("x"===u?0:1);o.labelDirection=o.tickDirection=o.nameDirection={top:-1,bottom:1,left:-1,right:1}[s],o.labelOffset=a?f[p[s]]-f[p.onZero]:0,e.get(["axisTick","inside"])&&(o.tickDirection=-o.tickDirection),J(n.labelInside,e.get(["axisLabel","inside"]))&&(o.labelDirection=-o.labelDirection);var y=e.get(["axisLabel","rotate"]);return o.labelRotate="top"===l?-y:y,o.z2=1,o}function oS(t){return"cartesian2d"===t.get("coordinateSystem")}function aS(t){var e={xAxisModel:null,yAxisModel:null};return O(e,(function(n,i){var r=i.replace(/Model$/,""),o=t.getReferringComponents(r,Eo).models[0];e[i]=o})),e}var sS=Math.log;function lS(t,e,n){var i=K_.prototype,r=i.getTicks.call(n),o=i.getTicks.call(n,!0),a=r.length-1,s=i.getInterval.call(n),l=Ex(t,e),u=l.extent,h=l.fixMin,c=l.fixMax;if("log"===t.type){var p=sS(t.base);u=[sS(u[0])/p,sS(u[1])/p]}t.setExtent(u[0],u[1]),t.calcNiceExtent({splitNumber:a,fixMin:h,fixMax:c});var d=i.getExtent.call(t);h&&(u[0]=d[0]),c&&(u[1]=d[1]);var f=i.getInterval.call(t),g=u[0],y=u[1];if(h&&c)f=(y-g)/a;else if(h)for(y=u[0]+f*a;y<u[1]&&isFinite(y)&&isFinite(u[1]);)f=V_(f),y=u[0]+f*a;else if(c)for(g=u[1]-f*a;g>u[0]&&isFinite(g)&&isFinite(u[0]);)f=V_(f),g=u[1]-f*a;else{t.getTicks().length-1>a&&(f=V_(f));var v=f*a;(g=Yr((y=Math.ceil(u[1]/f)*f)-v))<0&&u[0]>=0?(g=0,y=Yr(v)):y>0&&u[1]<=0&&(y=0,g=-Yr(v))}var m=(r[0].value-o[0].value)/s,_=(r[a].value-o[a].value)/s;i.setExtent.call(t,g+f*m,y+f*_),i.setInterval.call(t,f),(m||_)&&i.setNiceExtent.call(t,g+f,y-f)}var uS=function(){function t(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=tS,this._initCartesian(t,e,n),this.model=t}return t.prototype.getRect=function(){return this._rect},t.prototype.update=function(t,e){var n=this._axesMap;function i(t){var e,n=z(t),i=n.length;if(i){for(var r=[],o=i-1;o>=0;o--){var a=t[+n[o]],s=a.model,l=a.scale;z_(l)&&s.get("alignTicks")&&null==s.get("interval")?r.push(a):(zx(l,s),z_(l)&&(e=a))}r.length&&(e||zx((e=r.pop()).scale,e.model),O(r,(function(t){lS(t.scale,t.model,e.scale)})))}}this._updateScale(t,this.model),i(n.x),i(n.y);var r={};O(n.x,(function(t){cS(n,"y",t,r)})),O(n.y,(function(t){cS(n,"x",t,r)})),this.resize(this.model,e)},t.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),r=!n&&t.get("containLabel"),o=Hp(i,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var a=this._axesList;function s(){O(a,(function(t){var e=t.isHorizontal(),n=e?[0,o.width]:[0,o.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),function(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}(t,e?o.x:o.y)}))}s(),r&&(O(a,(function(t){if(!t.model.get(["axisLabel","inside"])){var e=function(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,r,o=n.getExtent();r=n instanceof q_?n.count():(i=n.getTicks()).length;var a,s,l,u,h,c,p,d=t.getLabelModel(),f=Vx(t),g=1;r>40&&(g=Math.ceil(r/40));for(var y=0;y<r;y+=g){var v=f(i?i[y]:{value:o[0]+y},y),m=(s=d.getTextRect(v),l=void 0,u=void 0,h=void 0,c=void 0,p=void 0,l=(d.get("rotate")||0)*Math.PI/180,u=s.width,h=s.height,c=u*Math.abs(Math.cos(l))+Math.abs(h*Math.sin(l)),p=u*Math.abs(Math.sin(l))+Math.abs(h*Math.cos(l)),new Ie(s.x,s.y,c,p));a?a.union(m):a=m}return a}}(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get(["axisLabel","margin"]);o[n]-=e[n]+i,"top"===t.position?o.y+=e.height+i:"left"===t.position&&(o.x+=e.width+i)}}})),s()),O(this._coordsList,(function(t){t.calcAffineTransform()}))},t.prototype.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n)return n[e||0]},t.prototype.getAxes=function(){return this._axesList.slice()},t.prototype.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}Y(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},t.prototype.getCartesians=function(){return this._coordsList.slice()},t.prototype.convertToPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},t.prototype.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},t.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",Eo).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",Eo).models[0],a=t.gridModel,s=this._coordsList;if(i)A(s,e=i.coordinateSystem)<0&&(e=null);else if(r&&o)e=this.getCartesian(r.componentIndex,o.componentIndex);else if(r)n=this.getAxis("x",r.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(a){a.coordinateSystem===this&&(e=this._coordsList[0])}return{cartesian:e,axis:n}},t.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},t.prototype._initCartesian=function(t,e,n){var i=this,r=this,o={left:!1,right:!1,top:!1,bottom:!1},a={x:{},y:{}},s={x:0,y:0};if(e.eachComponent("xAxis",l("x"),this),e.eachComponent("yAxis",l("y"),this),!s.x||!s.y)return this._axesMap={},void(this._axesList=[]);function l(e){return function(n,i){if(hS(n,t)){var l=n.get("position");"x"===e?"top"!==l&&"bottom"!==l&&(l=o.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=o.left?"right":"left"),o[l]=!0;var u=new iS(e,Fx(n),[0,0],n.get("type"),l),h="category"===u.type;u.onBand=h&&n.get("boundaryGap"),u.inverse=n.get("inverse"),n.axis=u,u.model=n,u.grid=r,u.index=i,r._axesList.push(u),a[e][i]=u,s[e]++}}}this._axesMap=a,O(a.x,(function(e,n){O(a.y,(function(r,o){var a="x"+n+"y"+o,s=new nS(a);s.master=i,s.model=t,i._coordsMap[a]=s,i._coordsList.push(s),s.addAxis(e),s.addAxis(r)}))}))},t.prototype._updateScale=function(t,e){function n(t,e){O(Ux(t,e.dim),(function(n){e.scale.unionExtentFromData(t,n)}))}O(this._axesList,(function(t){if(t.scale.setExtent(1/0,-1/0),"category"===t.type){var e=t.model.get("categorySortInfo");t.scale.setSortInfo(e)}})),t.eachSeries((function(t){if(oS(t)){var i=aS(t),r=i.xAxisModel,o=i.yAxisModel;if(!hS(r,e)||!hS(o,e))return;var a=this.getCartesian(r.componentIndex,o.componentIndex),s=t.getData(),l=a.getAxis("x"),u=a.getAxis("y");n(s,l),n(s,u)}}),this)},t.prototype.getTooltipAxes=function(t){var e=[],n=[];return O(this.getCartesians(),(function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),o=i.getOtherAxis(r);A(e,r)<0&&e.push(r),A(n,o)<0&&n.push(o)})),{baseAxes:e,otherAxes:n}},t.create=function(e,n){var i=[];return e.eachComponent("grid",(function(r,o){var a=new t(r,e,n);a.name="grid_"+o,a.resize(r,n,!0),r.coordinateSystem=a,i.push(a)})),e.eachSeries((function(t){if(oS(t)){var e=aS(t),n=e.xAxisModel,i=e.yAxisModel,r=n.getCoordSysModel().coordinateSystem;t.coordinateSystem=r.getCartesian(n.componentIndex,i.componentIndex)}})),i},t.dimensions=tS,t}();function hS(t,e){return t.getCoordSysModel()===e}function cS(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],a=n.model,s=a.get(["axisLine","onZero"]),l=a.get(["axisLine","onZeroAxisIndex"]);if(s){if(null!=l)pS(o[l])&&(r=o[l]);else for(var u in o)if(o.hasOwnProperty(u)&&pS(o[u])&&!i[h(o[u])]){r=o[u];break}r&&(i[h(r)]=!0)}function h(t){return t.dim+"_"+t.index}}function pS(t){return t&&"category"!==t.type&&"time"!==t.type&&function(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)}(t)}const dS=uS;var fS=Math.PI,gS=function(){function t(t,e){this.group=new Br,this.opt=e,this.axisModel=t,D(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var n=new Br({x:e.position[0],y:e.position[1],rotation:e.rotation});n.updateTransform(),this._transformGroup=n}return t.prototype.hasBuilder=function(t){return!!yS[t]},t.prototype.add=function(t){yS[t](this.opt,this.axisModel,this.group,this._transformGroup)},t.prototype.getGroup=function(){return this.group},t.innerTextLayout=function(t,e,n){var i,r,o=Qr(e-t);return Jr(o)?(r=n>0?"top":"bottom",i="center"):Jr(o-fS)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=o>0&&o<fS?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:i,textVerticalAlign:r}},t.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},t.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},t}(),yS={axisLine:function(t,e,n,i){var r=e.get(["axisLine","show"]);if("auto"===r&&t.handleAutoShown&&(r=t.handleAutoShown("axisLine")),r){var o=e.axis.getExtent(),a=i.transform,s=[o[0],0],l=[o[1],0],u=s[0]>l[0];a&&(Ot(s,s,a),Ot(l,l,a));var h=k({lineCap:"round"},e.getModel(["axisLine","lineStyle"]).getLineStyle()),c=new oh({shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:h,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1});qh(c.shape,c.style.lineWidth),c.anid="line",n.add(c);var p=e.get(["axisLine","symbol"]);if(null!=p){var d=e.get(["axisLine","symbolSize"]);G(p)&&(p=[p,p]),(G(d)||X(d))&&(d=[d,d]);var f=fv(e.get(["axisLine","symbolOffset"])||0,d),g=d[0],y=d[1];O([{rotate:t.rotation+Math.PI/2,offset:f[0],r:0},{rotate:t.rotation-Math.PI/2,offset:f[1],r:Math.sqrt((s[0]-l[0])*(s[0]-l[0])+(s[1]-l[1])*(s[1]-l[1]))}],(function(e,i){if("none"!==p[i]&&null!=p[i]){var r=pv(p[i],-g/2,-y/2,g,y,h.stroke,!0),o=e.r+e.offset,a=u?l:s;r.attr({rotation:e.rotate,x:a[0]+o*Math.cos(t.rotation),y:a[1]-o*Math.sin(t.rotation),silent:!0,z2:11}),n.add(r)}}))}}},axisTickLabel:function(t,e,n,i){var r=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(!a||r.scale.isBlank())return;for(var s=o.getModel("lineStyle"),l=i.tickDirection*o.get("length"),u=xS(r.getTicksCoords(),e.transform,l,D(s.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<u.length;h++)t.add(u[h]);return u}(n,i,e,t),o=function(t,e,n,i){var r=n.axis,o=J(i.axisLabelShow,n.get(["axisLabel","show"]));if(!o||r.scale.isBlank())return;var a=n.getModel("axisLabel"),s=a.get("margin"),l=r.getViewLabels(),u=(J(i.labelRotate,a.get("rotate"))||0)*fS/180,h=gS.innerTextLayout(i.rotation,u,i.labelDirection),c=n.getCategories&&n.getCategories(!0),p=[],d=gS.isLabelSilent(n),f=n.get("triggerEvent");return O(l,(function(o,u){var g="ordinal"===r.scale.type?r.scale.getRawOrdinalNumber(o.tickValue):o.tickValue,y=o.formattedLabel,v=o.rawLabel,m=a;if(c&&c[g]){var _=c[g];Y(_)&&_.textStyle&&(m=new Ec(_.textStyle,a,n.ecModel))}var x=m.getTextColor()||n.get(["axisLine","lineStyle","color"]),w=r.dataToCoord(g),b=m.getShallow("align",!0)||h.textAlign,S=tt(m.getShallow("alignMinLabel",!0),b),M=tt(m.getShallow("alignMaxLabel",!0),b),T=m.getShallow("verticalAlign",!0)||m.getShallow("baseline",!0)||h.textVerticalAlign,C=tt(m.getShallow("verticalAlignMinLabel",!0),T),I=tt(m.getShallow("verticalAlignMaxLabel",!0),T),k=new il({x:w,y:i.labelOffset+i.labelDirection*s,rotation:h.rotation,silent:d,z2:10+(o.level||0),style:fc(m,{text:y,align:0===u?S:u===l.length-1?M:b,verticalAlign:0===u?C:u===l.length-1?I:T,fill:W(x)?x("category"===r.type?v:"value"===r.type?g+"":g,u):x})});if(k.anid="label_"+g,oc({el:k,componentModel:n,itemName:y,formatterParamsExtra:{isTruncated:function(){return k.isTruncated},value:v,tickIndex:u}}),f){var D=gS.makeAxisEventDataBase(n);D.targetType="axisLabel",D.value=v,D.tickIndex=u,"category"===r.type&&(D.dataIndex=g),rl(k).eventData=D}e.add(k),k.updateTransform(),p.push(k),t.add(k),k.decomposeTransform()})),p}(n,i,e,t);(function(t,e,n){if(Gx(t.axis))return;var i=t.get(["axisLabel","showMinLabel"]),r=t.get(["axisLabel","showMaxLabel"]);e=e||[],n=n||[];var o=e[0],a=e[1],s=e[e.length-1],l=e[e.length-2],u=n[0],h=n[1],c=n[n.length-1],p=n[n.length-2];!1===i?(vS(o),vS(u)):mS(o,a)&&(i?(vS(a),vS(h)):(vS(o),vS(u)));!1===r?(vS(s),vS(c)):mS(l,s)&&(r?(vS(l),vS(p)):(vS(s),vS(c)))}(e,o,r),function(t,e,n,i){var r=n.axis,o=n.getModel("minorTick");if(!o.get("show")||r.scale.isBlank())return;var a=r.getMinorTicksCoords();if(!a.length)return;for(var s=o.getModel("lineStyle"),l=i*o.get("length"),u=D(s.getLineStyle(),D(n.getModel("axisTick").getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])})),h=0;h<a.length;h++)for(var c=xS(a[h],e.transform,l,u,"minorticks_"+h),p=0;p<c.length;p++)t.add(c[p])}(n,i,e,t.tickDirection),e.get(["axisLabel","hideOverlap"]))&&Rw(Aw(R(o,(function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}}))))},axisName:function(t,e,n,i){var r=J(t.axisName,e.get("name"));if(r){var o,a,s=e.get("nameLocation"),l=t.nameDirection,u=e.getModel("nameTextStyle"),h=e.get("nameGap")||0,c=e.axis.getExtent(),p=c[0]>c[1]?-1:1,d=["start"===s?c[0]-p*h:"end"===s?c[1]+p*h:(c[0]+c[1])/2,_S(s)?t.labelOffset+l*h:0],f=e.get("nameRotate");null!=f&&(f=f*fS/180),_S(s)?o=gS.innerTextLayout(t.rotation,null!=f?f:t.rotation,l):(o=function(t,e,n,i){var r,o,a=Qr(n-t),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;Jr(a-fS/2)?(o=l?"bottom":"top",r="center"):Jr(a-1.5*fS)?(o=l?"top":"bottom",r="center"):(o="middle",r=a<1.5*fS&&a>fS/2?l?"left":"right":l?"right":"left");return{rotation:a,textAlign:r,textVerticalAlign:o}}(t.rotation,s,f||0,c),null!=(a=t.axisNameAvailableWidth)&&(a=Math.abs(a/Math.sin(o.rotation)),!isFinite(a)&&(a=null)));var g=u.getFont(),y=e.get("nameTruncate",!0)||{},v=y.ellipsis,m=J(t.nameTruncateMaxWidth,y.maxWidth,a),_=new il({x:d[0],y:d[1],rotation:o.rotation,silent:gS.isLabelSilent(e),style:fc(u,{text:r,font:g,overflow:"truncate",width:m,ellipsis:v,fill:u.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:u.get("align")||o.textAlign,verticalAlign:u.get("verticalAlign")||o.textVerticalAlign}),z2:1});if(oc({el:_,componentModel:e,itemName:r}),_.__fullText=r,_.anid="name",e.get("triggerEvent")){var x=gS.makeAxisEventDataBase(e);x.targetType="axisName",x.name=r,rl(_).eventData=x}i.add(_),_.updateTransform(),n.add(_),_.decomposeTransform()}}};function vS(t){t&&(t.ignore=!0)}function mS(t,e){var n=t&&t.getBoundingRect().clone(),i=e&&e.getBoundingRect().clone();if(n&&i){var r=ue([]);return de(r,r,-t.rotation),n.applyTransform(ce([],r,t.getLocalTransform())),i.applyTransform(ce([],r,e.getLocalTransform())),n.intersect(i)}}function _S(t){return"middle"===t||"center"===t}function xS(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord;a[0]=u,a[1]=0,s[0]=u,s[1]=n,e&&(Ot(a,a,e),Ot(s,s,e));var h=new oh({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});qh(h.shape,h.style.lineWidth),h.anid=r+"_"+t[l].tickValue,o.push(h)}return o}const wS=gS;function bS(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return function(t,e,n){var i=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),o=r.get("link",!0)||[],a=[];O(n.getCoordinateSystems(),(function(n){if(n.axisPointerEnabled){var s=CS(n.model),l=t.coordSysAxesInfo[s]={};t.coordSysMap[s]=n;var u=n.model.getModel("tooltip",i);if(O(n.getAxes(),V(d,!1,null)),n.getTooltipAxes&&i&&u.get("show")){var h="axis"===u.get("trigger"),c="cross"===u.get(["axisPointer","type"]),p=n.getTooltipAxes(u.get(["axisPointer","axis"]));(h||c)&&O(p.baseAxes,V(d,!c||"cross",h)),c&&O(p.otherAxes,V(d,"cross",!1))}}function d(i,s,h){var c=h.model.getModel("axisPointer",r),p=c.get("show");if(p&&("auto"!==p||i||TS(c))){null==s&&(s=c.get("triggerTooltip")),c=i?function(t,e,n,i,r,o){var a=e.getModel("axisPointer"),s={};O(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],(function(t){s[t]=T(a.get(t))})),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");var l=s.label||(s.label={});if(null==l.show&&(l.show=!1),"cross"===r){var u=a.get(["label","show"]);if(l.show=null==u||u,!o){var h=s.lineStyle=a.get("crossStyle");h&&D(l,h.textStyle)}}return t.model.getModel("axisPointer",new Ec(s,n,i))}(h,u,r,e,i,s):c;var d=c.get("snap"),f=c.get("triggerEmphasis"),g=CS(h.model),y=s||d||"category"===h.type,v=t.axesInfo[g]={key:g,axis:h,coordSys:n,axisPointerModel:c,triggerTooltip:s,triggerEmphasis:f,involveSeries:y,snap:d,useHandle:TS(c),seriesModels:[],linkGroup:null};l[g]=v,t.seriesInvolved=t.seriesInvolved||y;var m=function(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(SS(o[i+"AxisId"],n.id)||SS(o[i+"AxisIndex"],n.componentIndex)||SS(o[i+"AxisName"],n.name))return r}}(o,h);if(null!=m){var _=a[m]||(a[m]={axesInfo:{}});_.axesInfo[g]=v,_.mapper=o[m].mapper,v.linkGroup=_}}}}))}(n,t,e),n.seriesInvolved&&function(t,e){e.eachSeries((function(e){var n=e.coordinateSystem,i=e.get(["tooltip","trigger"],!0),r=e.get(["tooltip","show"],!0);n&&"none"!==i&&!1!==i&&"item"!==i&&!1!==r&&!1!==e.get(["axisPointer","show"],!0)&&O(t.coordSysAxesInfo[CS(n.model)],(function(t){var i=t.axis;n.getAxis(i.dim)===i&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())}))}))}(n,t),n}function SS(t,e){return"all"===t||H(t)&&A(t,e)>=0||t===e}function MS(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[CS(t)]}function TS(t){return!!t.get(["handle","show"])}function CS(t){return t.type+"||"+t.id}var IS={};const kS=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(e,n,i,r){this.axisPointerClass&&function(t){var e=MS(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=TS(n);null==o&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}(e),t.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,i,!0)},n.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},n.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},n.prototype.dispose=function(e,n){this._disposeAxisPointer(n),t.prototype.dispose.apply(this,arguments)},n.prototype._doUpdateAxisPointerClass=function(t,e,i){var r=n.getAxisPointerClass(this.axisPointerClass);if(r){var o=function(t){var e=MS(t);return e&&e.axisPointerModel}(t);o?(this._axisPointer||(this._axisPointer=new r)).render(t,o,e,i):this._disposeAxisPointer(e)}},n.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},n.registerAxisPointerClass=function(t,e){IS[t]=e},n.getAxisPointerClass=function(t){return t&&IS[t]},n.type="axis",n}(Yg);var DS=Oo();function AS(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitArea"),a=o.getModel("areaStyle"),s=a.get("color"),l=i.coordinateSystem.getRect(),u=r.getTicksCoords({tickModel:o,clamp:!0});if(u.length){var h=s.length,c=DS(t).splitAreaColors,p=pt(),d=0;if(c)for(var f=0;f<u.length;f++){var g=c.get(u[f].tickValue);if(null!=g){d=(g+(h-1)*f)%h;break}}var y=r.toGlobalCoord(u[0].coord),v=a.getAreaStyle();s=H(s)?s:[s];for(f=1;f<u.length;f++){var m=r.toGlobalCoord(u[f].coord),_=void 0,x=void 0,w=void 0,b=void 0;r.isHorizontal()?(_=y,x=l.y,w=m-_,b=l.height,y=_+w):(_=l.x,x=y,w=l.width,y=x+(b=m-x));var S=u[f-1].tickValue;null!=S&&p.set(S,d),e.add(new Hs({anid:null!=S?"area_"+S:null,shape:{x:_,y:x,width:w,height:b},style:D({fill:s[d]},v),autoBatch:!0,silent:!0})),d=(d+1)%h}DS(t).splitAreaColors=p}}}function PS(t){DS(t).splitAreaColors=null}var LS=["axisLine","axisTickLabel","axisName"],OS=["splitArea","splitLine","minorSplitLine"],RS=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.axisPointerClass="CartesianAxisPointer",e}return e(n,t),n.prototype.render=function(e,n,i,r){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new Br,this.group.add(this._axisGroup),e.get("show")){var a=e.getCoordSysModel(),s=rS(a,e),l=new wS(e,k({handleAutoShown:function(t){for(var n=a.coordinateSystem.getCartesians(),i=0;i<n.length;i++)if(z_(n[i].getOtherAxis(e.axis).scale))return!0;return!1}},s));O(LS,l.add,l),this._axisGroup.add(l.getGroup()),O(OS,(function(t){e.get([t,"show"])&&NS[t](this,this._axisGroup,e,a)}),this),r&&"changeAxisOrder"===r.type&&r.isInitSort||Jh(o,this._axisGroup,e),t.prototype.render.call(this,e,n,i,r)}},n.prototype.remove=function(){PS(this)},n.type="cartesianAxis",n}(kS),NS={splitLine:function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitLine"),a=o.getModel("lineStyle"),s=a.get("color"),l=!1!==o.get("showMinLine"),u=!1!==o.get("showMaxLine");s=H(s)?s:[s];for(var h=i.coordinateSystem.getRect(),c=r.isHorizontal(),p=0,d=r.getTicksCoords({tickModel:o}),f=[],g=[],y=a.getLineStyle(),v=0;v<d.length;v++){var m=r.toGlobalCoord(d[v].coord);if((0!==v||l)&&(v!==d.length-1||u)){var _=d[v].tickValue;c?(f[0]=m,f[1]=h.y,g[0]=m,g[1]=h.y+h.height):(f[0]=h.x,f[1]=m,g[0]=h.x+h.width,g[1]=m);var x=p++%s.length,w=new oh({anid:null!=_?"line_"+_:null,autoBatch:!0,shape:{x1:f[0],y1:f[1],x2:g[0],y2:g[1]},style:D({stroke:s[x]},y),silent:!0});qh(w.shape,y.lineWidth),e.add(w)}}}},minorSplitLine:function(t,e,n,i){var r=n.axis,o=n.getModel("minorSplitLine").getModel("lineStyle"),a=i.coordinateSystem.getRect(),s=r.isHorizontal(),l=r.getMinorTicksCoords();if(l.length)for(var u=[],h=[],c=o.getLineStyle(),p=0;p<l.length;p++)for(var d=0;d<l[p].length;d++){var f=r.toGlobalCoord(l[p][d].coord);s?(u[0]=f,u[1]=a.y,h[0]=f,h[1]=a.y+a.height):(u[0]=a.x,u[1]=f,h[0]=a.x+a.width,h[1]=f);var g=new oh({anid:"minor_line_"+l[p][d].tickValue,autoBatch:!0,shape:{x1:u[0],y1:u[1],x2:h[0],y2:h[1]},style:c,silent:!0});qh(g.shape,c.lineWidth),e.add(g)}},splitArea:function(t,e,n,i){AS(t,e,n,i)}},BS=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="xAxis",n}(RS),ES=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=BS.type,e}return e(n,t),n.type="yAxis",n}(RS),zS=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="grid",e}return e(n,t),n.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new Hs({shape:t.coordinateSystem.getRect(),style:D({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},n.type="grid",n}(Yg),FS={offset:0};function VS(t){t.registerComponentView(zS),t.registerComponentModel(Ub),t.registerCoordinateSystem("cartesian2d",dS),$b(t,"x",Xb,FS),$b(t,"y",Xb,FS),t.registerComponentView(BS),t.registerComponentView(ES),t.registerPreprocessor((function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}))}var HS=Oo(),WS=T,GS=F;function US(t,e,n,i){XS(HS(n).lastProp,i)||(HS(n).lastProp=i,e?kh(n,i,t):(n.stopAnimation(),n.attr(i)))}function XS(t,e){if(Y(t)&&Y(e)){var n=!0;return O(e,(function(e,i){n=n&&XS(t[i],e)})),!!n}return t===e}function YS(t,e){t[e.get(["label","show"])?"show":"hide"]()}function qS(t){return{x:t.x||0,y:t.y||0,rotation:t.rotation||0}}function ZS(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse((function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)}))}const jS=function(){function t(){this._dragging=!1,this.animationThreshold=15}return t.prototype.render=function(t,e,n,i){var r=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,i||this._lastValue!==r||this._lastStatus!==o){this._lastValue=r,this._lastStatus=o;var a=this._group,s=this._handle;if(!o||"hide"===o)return a&&a.hide(),void(s&&s.hide());a&&a.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,n);var u=l.graphicKey;u!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=u;var h=this._moveAnimation=this.determineAnimation(t,e);if(a){var c=V(US,e,h);this.updatePointerEl(a,l,c),this.updateLabelEl(a,l,c,e)}else a=this._group=new Br,this.createPointerEl(a,l,t,e),this.createLabelEl(a,l,t,e),n.getZr().add(a);ZS(a,e,!0),this._renderHandle(r)}},t.prototype.remove=function(t){this.clear(t)},t.prototype.dispose=function(t){this.clear(t)},t.prototype.determineAnimation=function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,o=e.get("snap");if(!o&&!r)return!1;if("auto"===n||null==n){var a=this.animationThreshold;if(r&&i.getBandWidth()>a)return!0;if(o){var s=MS(t).seriesDataCount,l=i.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1}return!0===n},t.prototype.makeElOption=function(t,e,n,i,r){},t.prototype.createPointerEl=function(t,e,n,i){var r=e.pointer;if(r){var o=HS(t).pointerEl=new lc[r.type](WS(e.pointer));t.add(o)}},t.prototype.createLabelEl=function(t,e,n,i){if(e.label){var r=HS(t).labelEl=new il(WS(e.label));t.add(r),YS(r,i)}},t.prototype.updatePointerEl=function(t,e,n){var i=HS(t).pointerEl;i&&e.pointer&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},t.prototype.updateLabelEl=function(t,e,n,i){var r=HS(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{x:e.label.x,y:e.label.y}),YS(r,i))},t.prototype._renderHandle=function(t){if(!this._dragging&&this.updateHandleTransform){var e,n=this._axisPointerModel,i=this._api.getZr(),r=this._handle,o=n.getModel("handle"),a=n.get("status");if(!o.get("show")||!a||"hide"===a)return r&&i.remove(r),void(this._handle=null);this._handle||(e=!0,r=this._handle=ec(o.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){ie(t.event)},onmousedown:GS(this._onHandleDragMove,this,0,0),drift:GS(this._onHandleDragMove,this),ondragend:GS(this._onHandleDragEnd,this)}),i.add(r)),ZS(r,n,!1),r.setStyle(o.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var s=o.get("size");H(s)||(s=[s,s]),r.scaleX=s[0]/2,r.scaleY=s[1]/2,sy(this,"_doDispatchAxisPointer",o.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,e)}},t.prototype._moveHandleToValue=function(t,e){US(this._axisPointerModel,!e&&this._moveAnimation,this._handle,qS(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},t.prototype._onHandleDragMove=function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(qS(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(qS(i)),HS(n).lastProp=null,this._doDispatchAxisPointer()}},t.prototype._doDispatchAxisPointer=function(){if(this._handle){var t=this._payloadInfo,e=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]})}},t.prototype._onHandleDragEnd=function(){if(this._dragging=!1,this._handle){var t=this._axisPointerModel.get("value");this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"})}},t.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null),ly(this,"_doDispatchAxisPointer")},t.prototype.doClear=function(){},t.prototype.buildLabel=function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}},t}();function KS(t){var e,n=t.get("type"),i=t.getModel(n+"Style");return"line"===n?(e=i.getLineStyle()).fill=null:"shadow"===n&&((e=i.getAreaStyle()).stroke=null),e}function $S(t,e,n,i,r){var o=QS(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get(["label","precision"]),formatter:n.get(["label","formatter"])}),a=n.getModel("label"),s=Ip(a.get("padding")||0),l=a.getFont(),u=mr(o,l),h=r.position,c=u.width+s[1]+s[3],p=u.height+s[0]+s[2],d=r.align;"right"===d&&(h[0]-=c),"center"===d&&(h[0]-=c/2);var f=r.verticalAlign;"bottom"===f&&(h[1]-=p),"middle"===f&&(h[1]-=p/2),function(t,e,n,i){var r=i.getWidth(),o=i.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+n,o)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}(h,c,p,i);var g=a.get("backgroundColor");g&&"auto"!==g||(g=e.get(["axisLine","lineStyle","color"])),t.label={x:h[0],y:h[1],style:fc(a,{text:o,font:l,fill:a.getTextColor(),padding:s,backgroundColor:g}),z2:10}}function QS(t,e,n,i,r){t=e.scale.parse(t);var o=e.scale.getLabel({value:t},{precision:r.precision}),a=r.formatter;if(a){var s={value:Hx(e,{value:t}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};O(i,(function(t){var e=n.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,r=e&&e.getDataParams(i);r&&s.seriesData.push(r)})),G(a)?o=a.replace("{value}",o):W(a)&&(o=a(s))}return o}function JS(t,e,n){var i=[1,0,0,1,0,0];return de(i,i,n.rotation),pe(i,i,n.position),Kh([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}function tM(t,e,n,i,r,o){var a=wS.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=r.get(["label","margin"]),$S(e,i,r,o,{position:JS(i.axis,t,n),align:a.textAlign,verticalAlign:a.textVerticalAlign})}function eM(t,e,n){return{x1:t[n=n||0],y1:t[1-n],x2:e[n],y2:e[1-n]}}function nM(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}}function iM(t,e,n,i,r,o){return{cx:t,cy:e,r0:n,r:i,startAngle:r,endAngle:o,clockwise:!0}}var rM=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.makeElOption=function(t,e,n,i,r){var o=n.axis,a=o.grid,s=i.get("type"),l=oM(a,o).getOtherAxis(o).getGlobalExtent(),u=o.toGlobalCoord(o.dataToCoord(e,!0));if(s&&"none"!==s){var h=KS(i),c=aM[s](o,u,l);c.style=h,t.graphicKey=c.type,t.pointer=c}tM(e,t,rS(a.model,n),n,i,r)},n.prototype.getHandleTransform=function(t,e,n){var i=rS(e.axis.grid.model,e,{labelInside:!1});i.labelMargin=n.get(["handle","margin"]);var r=JS(e.axis,t,i);return{x:r[0],y:r[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},n.prototype.updateHandleTransform=function(t,e,n,i){var r=n.axis,o=r.grid,a=r.getGlobalExtent(!0),s=oM(o,r).getOtherAxis(r).getGlobalExtent(),l="x"===r.dim?0:1,u=[t.x,t.y];u[l]+=e[l],u[l]=Math.min(a[1],u[l]),u[l]=Math.max(a[0],u[l]);var h=(s[1]+s[0])/2,c=[h,h];c[l]=u[l];return{x:u[0],y:u[1],rotation:t.rotation,cursorPoint:c,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][l]}},n}(jS);function oM(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var aM={line:function(t,e,n){return{type:"Line",subPixelOptimize:!0,shape:eM([e,n[0]],[e,n[1]],sM(t))}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:nM([e-i/2,n[0]],[i,r],sM(t))}}};function sM(t){return"x"===t.dim?0:1}const lM=rM;const uM=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="axisPointer",n.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},n}(Kp);var hM=Oo(),cM=O;function pM(t,e,n){if(!r.node){var i=e.getZr();hM(i).records||(hM(i).records={}),function(t,e){if(hM(t).initialized)return;function n(n,i){t.on(n,(function(n){var r=function(t){var e={showTip:[],hideTip:[]},n=function(i){var r=e[i.type];r?r.push(i):(i.dispatchAction=n,t.dispatchAction(i))};return{dispatchAction:n,pendings:e}}(e);cM(hM(t).records,(function(t){t&&i(t,n,r.dispatchAction)})),function(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]);n&&(n.dispatchAction=null,e.dispatchAction(n))}(r.pendings,e)}))}hM(t).initialized=!0,n("click",V(fM,"click")),n("mousemove",V(fM,"mousemove")),n("globalout",dM)}(i,e),(hM(i).records[t]||(hM(i).records[t]={})).handler=n}}function dM(t,e,n){t.handler("leave",null,n)}function fM(t,e,n,i){e.handler(t,n,i)}function gM(t,e){if(!r.node){var n=e.getZr();(hM(n).records||{})[t]&&(hM(n).records[t]=null)}}const yM=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){var i=e.getComponent("tooltip"),r=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";pM("axisPointer",n,(function(t,e,n){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})}))},n.prototype.remove=function(t,e){gM("axisPointer",e)},n.prototype.dispose=function(t,e){gM("axisPointer",e)},n.type="axisPointer",n}(Yg);function vM(t,e){var n,i=[],r=t.seriesIndex;if(null==r||!(n=e.getSeriesByIndex(r)))return{point:[]};var o=n.getData(),a=Lo(o,t);if(null==a||a<0||H(a))return{point:[]};var s=o.getItemGraphicEl(a),l=n.coordinateSystem;if(n.getTooltipPosition)i=n.getTooltipPosition(a)||[];else if(l&&l.dataToPoint)if(t.isStacked){var u=l.getBaseAxis(),h=l.getOtherAxis(u).dim,c=u.dim,p="x"===h||"radius"===h?1:0,d=o.mapDimension(c),f=[];f[p]=o.get(d,a),f[1-p]=o.get(o.getCalculationInfo("stackResultDimension"),a),i=l.dataToPoint(f)||[]}else i=l.dataToPoint(o.getValues(R(l.dimensions,(function(t){return o.mapDimension(t)})),a))||[];else if(s){var g=s.getBoundingRect().clone();g.applyTransform(s.transform),i=[g.x+g.width/2,g.y+g.height/2]}return{point:i,el:s}}var mM=Oo();function _M(t,e,n){var i=t.currTrigger,r=[t.x,t.y],o=t,a=t.dispatchAction||F(n.dispatchAction,n),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){MM(r)&&(r=vM({seriesIndex:o.seriesIndex,dataIndex:o.dataIndex},e).point);var l=MM(r),u=o.axesInfo,h=s.axesInfo,c="leave"===i||MM(r),p={},d={},f={list:[],map:{}},g={showPointer:V(wM,d),showTooltip:V(bM,f)};O(s.coordSysMap,(function(t,e){var n=l||t.containPoint(r);O(s.coordSysAxesInfo[e],(function(t,e){var i=t.axis,o=function(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}(u,t);if(!c&&n&&(!u||o)){var a=o&&o.value;null!=a||l||(a=i.pointToData(r)),null!=a&&xM(t,a,g,!1,p)}}))}));var y={};return O(h,(function(t,e){var n=t.linkGroup;n&&!d[e]&&O(n.axesInfo,(function(e,i){var r=d[i];if(e!==t&&r){var o=r.value;n.mapper&&(o=t.axis.scale.parse(n.mapper(o,SM(e),SM(t)))),y[t.key]=o}}))})),O(y,(function(t,e){xM(h[e],t,g,!0,p)})),function(t,e,n){var i=n.axesInfo=[];O(e,(function(e,n){var r=e.axisPointerModel.option,o=t[n];o?(!e.useHandle&&(r.status="show"),r.value=o.value,r.seriesDataIndices=(o.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&i.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})}))}(d,h,p),function(t,e,n,i){if(MM(e)||!t.list.length)return void i({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}(f,r,t,a),function(t,e,n){var i=n.getZr(),r="axisPointerLastHighlights",o=mM(i)[r]||{},a=mM(i)[r]={};O(t,(function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&t.triggerEmphasis&&O(n.seriesDataIndices,(function(t){var e=t.seriesIndex+" | "+t.dataIndex;a[e]=t}))}));var s=[],l=[];O(o,(function(t,e){!a[e]&&l.push(t)})),O(a,(function(t,e){!o[e]&&s.push(t)})),l.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:l}),s.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}(h,0,n),p}}function xM(t,e,n,i,r){var o=t.axis;if(!o.scale.isBlank()&&o.containData(e))if(t.involveSeries){var a=function(t,e){var n=e.axis,i=n.dim,r=t,o=[],a=Number.MAX_VALUE,s=-1;return O(e.seriesModels,(function(e,l){var u,h,c=e.getData().mapDimensionsAll(i);if(e.getAxisTooltipData){var p=e.getAxisTooltipData(c,t,n);h=p.dataIndices,u=p.nestestValue}else{if(!(h=e.getData().indicesOfNearest(c[0],t,"category"===n.type?.5:null)).length)return;u=e.getData().get(c[0],h[0])}if(null!=u&&isFinite(u)){var d=t-u,f=Math.abs(d);f<=a&&((f<a||d>=0&&s<0)&&(a=f,s=d,r=u,o.length=0),O(h,(function(t){o.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})})))}})),{payloadBatch:o,snapToValue:r}}(e,t),s=a.payloadBatch,l=a.snapToValue;s[0]&&null==r.seriesIndex&&k(r,s[0]),!i&&t.snap&&o.containData(l)&&null!=l&&(e=l),n.showPointer(t,e,s),n.showTooltip(t,a,l)}else n.showPointer(t,e)}function wM(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function bM(t,e,n,i){var r=n.payloadBatch,o=e.axis,a=o.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,u=CS(l),h=t.map[u];h||(h=t.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(h)),h.dataByAxis.push({axisDim:o.dim,axisIndex:a.componentIndex,axisType:a.type,axisId:a.id,value:i,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:r.slice()})}}function SM(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function MM(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function TM(t){kS.registerAxisPointerClass("CartesianAxisPointer",lM),t.registerComponentModel(uM),t.registerComponentView(yM),t.registerPreprocessor((function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!H(e)&&(t.axisPointer.link=[e])}})),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,(function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=bS(t,e)})),t.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},_M)}function CM(t){jm(VS),jm(TM)}function IM(t,e,n){var i=e.getBoxLayoutParams(),r=e.get("padding"),o={width:n.getWidth(),height:n.getHeight()},a=Hp(i,o,r);Fp(e.get("orient"),t,e.get("itemGap"),a.width,a.height),Wp(t,i,o,r)}function kM(t,e){var n=Ip(e.get("padding")),i=e.getItemStyle(["color","opacity"]);return i.fill=e.get("backgroundColor"),t=new Hs({shape:{x:t.x-n[3],y:t.y-n[0],width:t.width+n[1]+n[3],height:t.height+n[0]+n[2],r:e.get("borderRadius")},style:i,silent:!0,z2:-1})}const DM=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.type="tooltip",n.dependencies=["axisPointer"],n.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},n}(Kp);function AM(t){var e=t.get("confine");return null!=e?!!e:"richText"===t.get("renderMode")}function PM(t){if(r.domSupported)for(var e=document.documentElement.style,n=0,i=t.length;n<i;n++)if(t[n]in e)return t[n]}var LM=PM(["transform","webkitTransform","OTransform","MozTransform","msTransform"]);function OM(t,e){if(!t)return e;e=Cp(e,!0);var n=t.indexOf(e);return(t=-1===n?e:"-"+t.slice(0,n)+"-"+e).toLowerCase()}var RM=OM(PM(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),"transition"),NM=OM(LM,"transform"),BM="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(r.transform3dSupported?"will-change:transform;":"");function EM(t,e,n){var i=t.toFixed(0)+"px",o=e.toFixed(0)+"px";if(!r.transformSupported)return n?"top:"+o+";left:"+i+";":[["top",o],["left",i]];var a=r.transform3dSupported,s="translate"+(a?"3d":"")+"("+i+","+o+(a?",0":"")+")";return n?"top:0;left:0;"+NM+":"+s+";":[["top",0],["left",0],[LM,s]]}function zM(t,e,n){var i=[],o=t.get("transitionDuration"),a=t.get("backgroundColor"),s=t.get("shadowBlur"),l=t.get("shadowColor"),u=t.get("shadowOffsetX"),h=t.get("shadowOffsetY"),c=t.getModel("textStyle"),p=Ag(t,"html"),d=u+"px "+h+"px "+s+"px "+l;return i.push("box-shadow:"+d),e&&o&&i.push(function(t,e){var n="cubic-bezier(0.23,1,0.32,1)",i=" "+t/2+"s "+n,o="opacity"+i+",visibility"+i;return e||(i=" "+t+"s "+n,o+=r.transformSupported?","+NM+i:",left"+i+",top"+i),RM+":"+o}(o,n)),a&&i.push("background-color:"+a),O(["width","color","radius"],(function(e){var n="border-"+e,r=Cp(n),o=t.get(r);null!=o&&i.push(n+":"+o+("color"===e?"":"px"))})),i.push(function(t){var e=[],n=t.get("fontSize"),i=t.getTextColor();i&&e.push("color:"+i),e.push("font:"+t.getFont());var r=tt(t.get("lineHeight"),Math.round(3*n/2));n&&e.push("line-height:"+r+"px");var o=t.get("textShadowColor"),a=t.get("textShadowBlur")||0,s=t.get("textShadowOffsetX")||0,l=t.get("textShadowOffsetY")||0;return o&&a&&e.push("text-shadow:"+s+"px "+l+"px "+a+"px "+o),O(["decoration","align"],(function(n){var i=t.get(n);i&&e.push("text-"+n+":"+i)})),e.join(";")}(c)),null!=p&&i.push("padding:"+Ip(p).join("px ")+"px"),i.join(";")+";"}function FM(t,e,n,i,r){var o=e&&e.painter;if(n){var a=o&&o.getViewportRoot();a&&function(t,e,n,i,r){Ut(Gt,e,i,r,!0)&&Ut(t,n,Gt[0],Gt[1])}(t,a,n,i,r)}else{t[0]=i,t[1]=r;var s=o&&o.getViewportRootOffset();s&&(t[0]+=s.offsetLeft,t[1]+=s.offsetTop)}t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}const VM=function(){function t(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,r.wxa)return null;var n=document.createElement("div");n.domBelongToZr=!0,this.el=n;var i=this._zr=t.getZr(),o=e.appendTo,a=o&&(G(o)?document.querySelector(o):j(o)?o:W(o)&&o(t.getDom()));FM(this._styleCoord,i,a,t.getWidth()/2,t.getHeight()/2),(a||t.getDom()).appendChild(n),this._api=t,this._container=a;var s=this;n.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},n.onmousemove=function(t){if(t=t||window.event,!s._enterable){var e=i.handler;ee(i.painter.getViewportRoot(),t,!0),e.dispatch("mousemove",t)}},n.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return t.prototype.update=function(t){if(!this._container){var e=this._api.getDom(),n=(o="position",(a=(r=e).currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r))?o?a[o]:a:null),i=e.style;"absolute"!==i.position&&"absolute"!==n&&(i.position="relative")}var r,o,a,s=t.get("alwaysShowContent");s&&this._moveIfResized(),this._alwaysShowContent=s,this.el.className=t.get("className")||""},t.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var n=this.el,i=n.style,r=this._styleCoord;n.innerHTML?i.cssText=BM+zM(t,!this._firstShow,this._longHide)+EM(r[0],r[1],!0)+"border-color:"+Op(e)+";"+(t.get("extraCssText")||"")+";pointer-events:"+(this._enterable?"auto":"none"):i.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},t.prototype.setContent=function(t,e,n,i,r){var o=this.el;if(null!=t){var a="";if(G(r)&&"item"===n.get("trigger")&&!AM(n)&&(a=function(t,e,n){if(!G(n)||"inside"===n)return"";var i=t.get("backgroundColor"),r=t.get("borderWidth");e=Op(e);var o,a,s="left"===(o=n)?"right":"right"===o?"left":"top"===o?"bottom":"top",l=Math.max(1.5*Math.round(r),6),u="",h=NM+":";A(["left","right"],s)>-1?(u+="top:50%",h+="translateY(-50%) rotate("+(a="left"===s?-225:-45)+"deg)"):(u+="left:50%",h+="translateX(-50%) rotate("+(a="top"===s?225:45)+"deg)");var c=a*Math.PI/180,p=l+r,d=p*Math.abs(Math.cos(c))+p*Math.abs(Math.sin(c)),f=e+" solid "+r+"px;";return'<div style="'+["position:absolute;width:"+l+"px;height:"+l+"px;z-index:-1;",(u+=";"+s+":-"+Math.round(100*((d-Math.SQRT2*r)/2+Math.SQRT2*r-(d-p)/2))/100+"px")+";"+h+";","border-bottom:"+f,"border-right:"+f,"background-color:"+i+";"].join("")+'"></div>'}(n,i,r)),G(t))o.innerHTML=t+a;else if(t){o.innerHTML="",H(t)||(t=[t]);for(var s=0;s<t.length;s++)j(t[s])&&t[s].parentNode!==o&&o.appendChild(t[s]);if(a&&o.childNodes.length){var l=document.createElement("div");l.innerHTML=a,o.appendChild(l)}}}else o.innerHTML=""},t.prototype.setEnterable=function(t){this._enterable=t},t.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},t.prototype.moveTo=function(t,e){if(this.el){var n=this._styleCoord;if(FM(n,this._zr,this._container,t,e),null!=n[0]&&null!=n[1]){var i=this.el.style;O(EM(n[0],n[1]),(function(t){i[t[0]]=t[1]}))}}},t.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},t.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",r.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout((function(){return t._longHide=!0}),500)},t.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||this._alwaysShowContent||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(F(this.hide,this),t)):this.hide())},t.prototype.isShow=function(){return this._show},t.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},t}();function HM(t){return Math.max(0,t)}function WM(t){var e=HM(t.shadowBlur||0),n=HM(t.shadowOffsetX||0),i=HM(t.shadowOffsetY||0);return{left:HM(e-n),right:HM(e+n),top:HM(e-i),bottom:HM(e+i)}}function GM(t,e,n,i){t[0]=n,t[1]=i,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}const UM=function(){function t(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),GM(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return t.prototype.update=function(t){var e=t.get("alwaysShowContent");e&&this._moveIfResized(),this._alwaysShowContent=e},t.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},t.prototype.setContent=function(t,e,n,i,r){var o=this;Y(t)&&go(""),this.el&&this._zr.remove(this.el);var a=n.getModel("textStyle");this.el=new il({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:i,textShadowColor:a.get("textShadowColor"),fill:n.get(["textStyle","color"]),padding:Ag(n,"richText"),verticalAlign:"top",align:"left"},z:n.get("z")}),O(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],(function(t){o.el.style[t]=n.get(t)})),O(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],(function(t){o.el.style[t]=a.get(t)||0})),this._zr.add(this.el);var s=this;this.el.on("mouseover",(function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0})),this.el.on("mouseout",(function(){s._enterable&&s._show&&s.hideLater(s._hideDelay),s._inContent=!1}))},t.prototype.setEnterable=function(t){this._enterable=t},t.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),n=WM(t.style);return[e.width+n.left+n.right,e.height+n.top+n.bottom]},t.prototype.moveTo=function(t,e){var n=this.el;if(n){var i=this._styleCoord;GM(i,this._zr,t,e),t=i[0],e=i[1];var r=n.style,o=HM(r.borderWidth||0),a=WM(r);n.x=t+o+a.left,n.y=e+o+a.top,n.markRedraw()}},t.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},t.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},t.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||this._alwaysShowContent||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(F(this.hide,this),t)):this.hide())},t.prototype.isShow=function(){return this._show},t.prototype.dispose=function(){this._zr.remove(this.el)},t}();var XM=new Hs({shape:{x:-1,y:-1,width:2,height:2}}),YM=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.init=function(t,e){if(!r.node&&e.getDom()){var n,i=t.getComponent("tooltip"),o=this._renderMode="auto"===(n=i.get("renderMode"))?r.domSupported?"html":"richText":n||"html";this._tooltipContent="richText"===o?new UM(e):new VM(e,{appendTo:i.get("appendToBody",!0)?"body":i.get("appendTo",!0)})}},n.prototype.render=function(t,e,n){if(!r.node&&n.getDom()){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n;var i=this._tooltipContent;i.update(t),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow(),"richText"!==this._renderMode&&t.get("transitionDuration")?sy(this,"_updatePosition",50,"fixRate"):ly(this,"_updatePosition")}},n.prototype._initGlobalListener=function(){var t=this._tooltipModel.get("triggerOn");pM("itemTooltip",this._api,F((function(e,n,i){"none"!==t&&(t.indexOf(e)>=0?this._tryShow(n,i):"leave"===e&&this._hide(i))}),this))},n.prototype._keepShow=function(){var t=this._tooltipModel,e=this._ecModel,n=this._api,i=t.get("triggerOn");if(null!=this._lastX&&null!=this._lastY&&"none"!==i&&"click"!==i){var r=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout((function(){!n.isDisposed()&&r.manuallyShowTip(t,e,n,{x:r._lastX,y:r._lastY,dataByCoordSys:r._lastDataByCoordSys})}))}},n.prototype.manuallyShowTip=function(t,e,n,i){if(i.from!==this.uid&&!r.node&&n.getDom()){var o=ZM(i,n);this._ticket="";var a=i.dataByCoordSys,s=function(t,e,n){var i=Bo(t).queryOptionMap,r=i.keys()[0];if(!r||"series"===r)return;var o=Fo(e,r,i.get(r),{useDefault:!1,enableAll:!1,enableNone:!1}),a=o.models[0];if(!a)return;var s,l=n.getViewOfComponentModel(a);if(l.group.traverse((function(e){var n=rl(e).tooltipConfig;if(n&&n.name===t.name)return s=e,!0})),s)return{componentMainType:r,componentIndex:a.componentIndex,el:s}}(i,e,n);if(s){var l=s.el.getBoundingRect().clone();l.applyTransform(s.el.transform),this._tryShow({offsetX:l.x+l.width/2,offsetY:l.y+l.height/2,target:s.el,position:i.position,positionDefault:"bottom"},o)}else if(i.tooltip&&null!=i.x&&null!=i.y){var u=XM;u.x=i.x,u.y=i.y,u.update(),rl(u).tooltipConfig={name:null,option:i.tooltip},this._tryShow({offsetX:i.x,offsetY:i.y,target:u},o)}else if(a)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:a,tooltipOption:i.tooltipOption},o);else if(null!=i.seriesIndex){if(this._manuallyAxisShowTip(t,e,n,i))return;var h=vM(i,e),c=h.point[0],p=h.point[1];null!=c&&null!=p&&this._tryShow({offsetX:c,offsetY:p,target:h.el,position:i.position,positionDefault:"bottom"},o)}else null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},o))}},n.prototype.manuallyHideTip=function(t,e,n,i){var r=this._tooltipContent;this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,i.from!==this.uid&&this._hide(ZM(i,n))},n.prototype._manuallyAxisShowTip=function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){var s=e.getSeriesByIndex(r);if(s)if("axis"===qM([s.getData().getItemModel(o),s,(s.coordinateSystem||{}).model],this._tooltipModel).get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}},n.prototype._tryShow=function(t,e){var n=t.target;if(this._tooltipModel){this._lastX=t.offsetX,this._lastY=t.offsetY;var i=t.dataByCoordSys;if(i&&i.length)this._showAxisTooltip(i,t);else if(n){var r,o;if("legend"===rl(n).ssrType)return;this._lastDataByCoordSys=null,Jy(n,(function(t){return null!=rl(t).dataIndex?(r=t,!0):null!=rl(t).tooltipConfig?(o=t,!0):void 0}),!0),r?this._showSeriesItemTooltip(t,r,e):o?this._showComponentItemTooltip(t,o,e):this._hide(e)}else this._lastDataByCoordSys=null,this._hide(e)}},n.prototype._showOrMove=function(t,e){var n=t.get("showDelay");e=F(e,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(e,n):e()},n.prototype._showAxisTooltip=function(t,e){var n=this._ecModel,i=this._tooltipModel,r=[e.offsetX,e.offsetY],o=qM([e.tooltipOption],i),a=this._renderMode,s=[],l=xg("section",{blocks:[],noHeader:!0}),u=[],h=new Pg;O(t,(function(t){O(t.dataByAxis,(function(t){var e=n.getComponent(t.axisDim+"Axis",t.axisIndex),r=t.value;if(e&&null!=r){var o=QS(r,e.axis,n,t.seriesDataIndices,t.valueLabelOpt),c=xg("section",{header:o,noHeader:!ot(o),sortBlocks:!0,blocks:[]});l.blocks.push(c),O(t.seriesDataIndices,(function(l){var p=n.getSeriesByIndex(l.seriesIndex),d=l.dataIndexInside,f=p.getDataParams(d);if(!(f.dataIndex<0)){f.axisDim=t.axisDim,f.axisIndex=t.axisIndex,f.axisType=t.axisType,f.axisId=t.axisId,f.axisValue=Hx(e.axis,{value:r}),f.axisValueLabel=o,f.marker=h.makeTooltipMarker("item",Op(f.color),a);var g=Bf(p.formatTooltip(d,!0,null)),y=g.frag;if(y){var v=qM([p],i).get("valueFormatter");c.blocks.push(v?k({valueFormatter:v},y):y)}g.text&&u.push(g.text),s.push(f)}}))}}))})),l.blocks.reverse(),u.reverse();var c=e.position,p=o.get("order"),d=Cg(l,h,a,p,n.get("useUTC"),o.get("textStyle"));d&&u.unshift(d);var f="richText"===a?"\n\n":"<br/>",g=u.join(f);this._showOrMove(o,(function(){this._updateContentNotChangedOnAxis(t,s)?this._updatePosition(o,c,r[0],r[1],this._tooltipContent,s):this._showTooltipContent(o,g,s,Math.random()+"",r[0],r[1],c,null,h)}))},n.prototype._showSeriesItemTooltip=function(t,e,n){var i=this._ecModel,r=rl(e),o=r.seriesIndex,a=i.getSeriesByIndex(o),s=r.dataModel||a,l=r.dataIndex,u=r.dataType,h=s.getData(u),c=this._renderMode,p=t.positionDefault,d=qM([h.getItemModel(l),s,a&&(a.coordinateSystem||{}).model],this._tooltipModel,p?{position:p}:null),f=d.get("trigger");if(null==f||"item"===f){var g=s.getDataParams(l,u),y=new Pg;g.marker=y.makeTooltipMarker("item",Op(g.color),c);var v=Bf(s.formatTooltip(l,!1,u)),m=d.get("order"),_=d.get("valueFormatter"),x=v.frag,w=x?Cg(_?k({valueFormatter:_},x):x,y,c,m,i.get("useUTC"),d.get("textStyle")):v.text,b="item_"+s.name+"_"+l;this._showOrMove(d,(function(){this._showTooltipContent(d,w,g,b,t.offsetX,t.offsetY,t.position,t.target,y)})),n({type:"showTip",dataIndexInside:l,dataIndex:h.getRawIndex(l),seriesIndex:o,from:this.uid})}},n.prototype._showComponentItemTooltip=function(t,e,n){var i="html"===this._renderMode,r=rl(e),o=r.tooltipConfig.option||{},a=o.encodeHTMLContent;if(G(o)){o={content:o,formatter:o},a=!0}a&&i&&o.content&&((o=T(o)).content=Zt(o.content));var s=[o],l=this._ecModel.getComponent(r.componentMainType,r.componentIndex);l&&s.push(l),s.push({formatter:o.content});var u=t.positionDefault,h=qM(s,this._tooltipModel,u?{position:u}:null),c=h.get("content"),p=Math.random()+"",d=new Pg;this._showOrMove(h,(function(){var n=T(h.get("formatterParams")||{});this._showTooltipContent(h,c,n,p,t.offsetX,t.offsetY,t.position,e,d)})),n({type:"showTip",from:this.uid})},n.prototype._showTooltipContent=function(t,e,n,i,r,o,a,s,l){if(this._ticket="",t.get("showContent")&&t.get("show")){var u=this._tooltipContent;u.setEnterable(t.get("enterable"));var h=t.get("formatter");a=a||t.get("position");var c=e,p=this._getNearestPoint([r,o],n,t.get("trigger"),t.get("borderColor")).color;if(h)if(G(h)){var d=t.ecModel.get("useUTC"),f=H(n)?n[0]:n;c=h,f&&f.axisType&&f.axisType.indexOf("time")>=0&&(c=lp(f.axisValue,c,d)),c=Pp(c,n,!0)}else if(W(h)){var g=F((function(e,i){e===this._ticket&&(u.setContent(i,l,t,p,a),this._updatePosition(t,a,r,o,u,n,s))}),this);this._ticket=i,c=h(n,i,g)}else c=h;u.setContent(c,l,t,p,a),u.show(t,p),this._updatePosition(t,a,r,o,u,n,s)}},n.prototype._getNearestPoint=function(t,e,n,i){return"axis"===n||H(e)?{color:i||("html"===this._renderMode?"#fff":"none")}:H(e)?void 0:{color:i||e.color||e.borderColor}},n.prototype._updatePosition=function(t,e,n,i,r,o,a){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var u=r.getSize(),h=t.get("align"),c=t.get("verticalAlign"),p=a&&a.getBoundingRect().clone();if(a&&p.applyTransform(a.transform),W(e)&&(e=e([n,i],o,r.el,p,{viewSize:[s,l],contentSize:u.slice()})),H(e))n=Xr(e[0],s),i=Xr(e[1],l);else if(Y(e)){var d=e;d.width=u[0],d.height=u[1];var f=Hp(d,{width:s,height:l});n=f.x,i=f.y,h=null,c=null}else if(G(e)&&a){var g=function(t,e,n,i){var r=n[0],o=n[1],a=Math.ceil(Math.SQRT2*i)+8,s=0,l=0,u=e.width,h=e.height;switch(t){case"inside":s=e.x+u/2-r/2,l=e.y+h/2-o/2;break;case"top":s=e.x+u/2-r/2,l=e.y-o-a;break;case"bottom":s=e.x+u/2-r/2,l=e.y+h+a;break;case"left":s=e.x-r-a,l=e.y+h/2-o/2;break;case"right":s=e.x+u+a,l=e.y+h/2-o/2}return[s,l]}(e,p,u,t.get("borderWidth"));n=g[0],i=g[1]}else{g=function(t,e,n,i,r,o,a){var s=n.getSize(),l=s[0],u=s[1];null!=o&&(t+l+o+2>i?t-=l+o:t+=o);null!=a&&(e+u+a>r?e-=u+a:e+=a);return[t,e]}(n,i,r,s,l,h?null:20,c?null:20);n=g[0],i=g[1]}if(h&&(n-=jM(h)?u[0]/2:"right"===h?u[0]:0),c&&(i-=jM(c)?u[1]/2:"bottom"===c?u[1]:0),AM(t)){g=function(t,e,n,i,r){var o=n.getSize(),a=o[0],s=o[1];return t=Math.min(t+a,i)-a,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}(n,i,r,s,l);n=g[0],i=g[1]}r.moveTo(n,i)},n.prototype._updateContentNotChangedOnAxis=function(t,e){var n=this._lastDataByCoordSys,i=this._cbParamsList,r=!!n&&n.length===t.length;return r&&O(n,(function(n,o){var a=n.dataByAxis||[],s=(t[o]||{}).dataByAxis||[];(r=r&&a.length===s.length)&&O(a,(function(t,n){var o=s[n]||{},a=t.seriesDataIndices||[],l=o.seriesDataIndices||[];(r=r&&t.value===o.value&&t.axisType===o.axisType&&t.axisId===o.axisId&&a.length===l.length)&&O(a,(function(t,e){var n=l[e];r=r&&t.seriesIndex===n.seriesIndex&&t.dataIndex===n.dataIndex})),i&&O(t.seriesDataIndices,(function(t){var n=t.seriesIndex,o=e[n],a=i[n];o&&a&&a.data!==o.data&&(r=!1)}))}))})),this._lastDataByCoordSys=t,this._cbParamsList=e,!!r},n.prototype._hide=function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},n.prototype.dispose=function(t,e){!r.node&&e.getDom()&&(ly(this,"_updatePosition"),this._tooltipContent.dispose(),gM("itemTooltip",e))},n.type="tooltip",n}(Yg);function qM(t,e,n){var i,r=e.ecModel;n?(i=new Ec(n,r,r),i=new Ec(e.option,i,r)):i=e;for(var o=t.length-1;o>=0;o--){var a=t[o];a&&(a instanceof Ec&&(a=a.get("tooltip",!0)),G(a)&&(a={formatter:a}),a&&(i=new Ec(a,i,r)))}return i}function ZM(t,e){return t.dispatchAction||F(e.dispatchAction,e)}function jM(t){return"center"===t||"middle"===t}const KM=YM;function $M(t){jm(TM),t.registerComponentModel(DM),t.registerComponentView(KM),t.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},vt),t.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},vt)}var QM=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.layoutMode={type:"box",ignoreSize:!0},e}return e(n,t),n.type="title",n.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},n}(Kp),JM=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.render=function(t,e,n){if(this.group.removeAll(),t.get("show")){var i=this.group,r=t.getModel("textStyle"),o=t.getModel("subtextStyle"),a=t.get("textAlign"),s=tt(t.get("textBaseline"),t.get("textVerticalAlign")),l=new il({style:fc(r,{text:t.get("text"),fill:r.getTextColor()},{disableBox:!0}),z2:10}),u=l.getBoundingRect(),h=t.get("subtext"),c=new il({style:fc(o,{text:h,fill:o.getTextColor(),y:u.height+t.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),p=t.get("link"),d=t.get("sublink"),f=t.get("triggerEvent",!0);l.silent=!p&&!f,c.silent=!d&&!f,p&&l.on("click",(function(){Rp(p,"_"+t.get("target"))})),d&&c.on("click",(function(){Rp(d,"_"+t.get("subtarget"))})),rl(l).eventData=rl(c).eventData=f?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(l),h&&i.add(c);var g=i.getBoundingRect(),y=t.getBoxLayoutParams();y.width=g.width,y.height=g.height;var v=Hp(y,{width:n.getWidth(),height:n.getHeight()},t.get("padding"));a||("middle"===(a=t.get("left")||t.get("right"))&&(a="center"),"right"===a?v.x+=v.width:"center"===a&&(v.x+=v.width/2)),s||("center"===(s=t.get("top")||t.get("bottom"))&&(s="middle"),"bottom"===s?v.y+=v.height:"middle"===s&&(v.y+=v.height/2),s=s||"top"),i.x=v.x,i.y=v.y,i.markRedraw();var m={align:a,verticalAlign:s};l.setStyle(m),c.setStyle(m),g=i.getBoundingRect();var _=v.margin,x=t.getItemStyle(["color","opacity"]);x.fill=t.get("backgroundColor");var w=new Hs({shape:{x:g.x-_[3],y:g.y-_[0],width:g.width+_[1]+_[3],height:g.height+_[0]+_[2],r:t.get("borderRadius")},style:x,subPixelOptimize:!0,silent:!0});i.add(w)}},n.type="title",n}(Yg);function tT(t){t.registerComponentModel(QM),t.registerComponentView(JM)}const eT=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.layoutMode={type:"box",ignoreSize:!0},e}return e(n,t),n.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},n.prototype.mergeOption=function(e,n){t.prototype.mergeOption.call(this,e,n),this._updateSelector(e)},n.prototype._updateSelector=function(t){var e=t.selector,n=this.ecModel;!0===e&&(e=t.selector=["all","inverse"]),H(e)&&O(e,(function(t,i){G(t)&&(t={type:t}),e[i]=C(t,function(t,e){return"all"===e?{type:"all",title:t.getLocaleModel().get(["legend","selector","all"])}:"inverse"===e?{type:"inverse",title:t.getLocaleModel().get(["legend","selector","inverse"])}:void 0}(n,t.type))}))},n.prototype.optionUpdated=function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}!e&&this.select(t[0].get("name"))}},n.prototype._updateData=function(t){var e=[],n=[];t.eachRawSeries((function(i){var r,o=i.name;if(n.push(o),i.legendVisualProvider){var a=i.legendVisualProvider.getAllNames();t.isSeriesFiltered(i)||(n=n.concat(a)),a.length?e=e.concat(a):r=!0}else r=!0;r&&ko(i)&&e.push(i.name)})),this._availableNames=n;var i=this.get("data")||e,r=pt(),o=R(i,(function(t){return(G(t)||X(t))&&(t={name:t}),r.get(t.name)?null:(r.set(t.name,!0),new Ec(t,this,this.ecModel))}),this);this._data=B(o,(function(t){return!!t}))},n.prototype.getData=function(){return this._data},n.prototype.select=function(t){var e=this.option.selected;"single"===this.get("selectedMode")&&O(this._data,(function(t){e[t.get("name")]=!1}));e[t]=!0},n.prototype.unSelect=function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},n.prototype.toggleSelected=function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},n.prototype.allSelect=function(){var t=this._data,e=this.option.selected;O(t,(function(t){e[t.get("name",!0)]=!0}))},n.prototype.inverseSelect=function(){var t=this._data,e=this.option.selected;O(t,(function(t){var n=t.get("name",!0);e.hasOwnProperty(n)||(e[n]=!0),e[n]=!e[n]}))},n.prototype.isSelected=function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&A(this._availableNames,t)>=0},n.prototype.getOrient=function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},n.type="legend.plain",n.dependencies=["series"],n.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},n}(Kp);var nT=V,iT=O,rT=Br;function oT(t,e,n,i){lT(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),sT(t,e,n,i)}function aT(t){for(var e,n=t.getZr().storage.getDisplayList(),i=0,r=n.length;i<r&&!(e=n[i].states.emphasis);)i++;return e&&e.hoverLayer}function sT(t,e,n,i){aT(n)||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function lT(t,e,n,i){aT(n)||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}const uT=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.newlineDisabled=!1,e}return e(n,t),n.prototype.init=function(){this.group.add(this._contentGroup=new rT),this.group.add(this._selectorGroup=new rT),this._isFirstRender=!0},n.prototype.getContentGroup=function(){return this._contentGroup},n.prototype.getSelectorGroup=function(){return this._selectorGroup},n.prototype.render=function(t,e,n){var i=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),t.get("show",!0)){var r=t.get("align"),o=t.get("orient");r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===o?"right":"left");var a=t.get("selector",!0),s=t.get("selectorPosition",!0);!a||s&&"auto"!==s||(s="horizontal"===o?"end":"start"),this.renderInner(r,t,e,n,a,o,s);var l=t.getBoxLayoutParams(),u={width:n.getWidth(),height:n.getHeight()},h=t.get("padding"),c=Hp(l,u,h),p=this.layoutInner(t,r,c,i,a,s),d=Hp(D({width:p.width,height:p.height},l),u,h);this.group.x=d.x-p.x,this.group.y=d.y-p.y,this.group.markRedraw(),this.group.add(this._backgroundEl=kM(p,t))}},n.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},n.prototype.renderInner=function(t,e,n,i,r,o,a){var s=this.getContentGroup(),l=pt(),u=e.get("selectedMode"),h=[];n.eachRawSeries((function(t){!t.get("legendHoverLink")&&h.push(t.id)})),iT(e.getData(),(function(r,o){var a=r.get("name");if(!this.newlineDisabled&&(""===a||"\n"===a)){var c=new rT;return c.newline=!0,void s.add(c)}var p=n.getSeriesByName(a)[0];if(!l.get(a))if(p){var d=p.getData(),f=d.getVisual("legendLineStyle")||{},g=d.getVisual("legendIcon"),y=d.getVisual("style"),v=this._createItem(p,a,o,r,e,t,f,y,g,u,i);v.on("click",nT(oT,a,null,i,h)).on("mouseover",nT(sT,p.name,null,i,h)).on("mouseout",nT(lT,p.name,null,i,h)),n.ssr&&v.eachChild((function(t){var e=rl(t);e.seriesIndex=p.seriesIndex,e.dataIndex=o,e.ssrType="legend"})),l.set(a,!0)}else n.eachRawSeries((function(s){if(!l.get(a)&&s.legendVisualProvider){var c=s.legendVisualProvider;if(!c.containName(a))return;var p=c.indexOfName(a),d=c.getItemVisual(p,"style"),f=c.getItemVisual(p,"legendIcon"),g=Hn(d.fill);g&&0===g[3]&&(g[3]=.2,d=k(k({},d),{fill:Zn(g,"rgba")}));var y=this._createItem(s,a,o,r,e,t,{},d,f,u,i);y.on("click",nT(oT,null,a,i,h)).on("mouseover",nT(sT,null,a,i,h)).on("mouseout",nT(lT,null,a,i,h)),n.ssr&&y.eachChild((function(t){var e=rl(t);e.seriesIndex=s.seriesIndex,e.dataIndex=o,e.ssrType="legend"})),l.set(a,!0)}}),this)}),this),r&&this._createSelector(r,e,i,o,a)},n.prototype._createSelector=function(t,e,n,i,r){var o=this.getSelectorGroup();iT(t,(function(t){var i=t.type,r=new il({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:"all"===i?"legendAllSelect":"legendInverseSelect",legendId:e.id})}});o.add(r),pc(r,{normal:e.getModel("selectorLabel"),emphasis:e.getModel(["emphasis","selectorLabel"])},{defaultText:t.title}),ql(r)}))},n.prototype._createItem=function(t,e,n,i,r,o,a,s,l,u,h){var c=t.visualDrawType,p=r.get("itemWidth"),d=r.get("itemHeight"),f=r.isSelected(e),g=i.get("symbolRotate"),y=i.get("symbolKeepAspect"),v=i.get("icon"),m=function(t,e,n,i,r,o,a){function s(t,e){"auto"===t.lineWidth&&(t.lineWidth=e.lineWidth>0?2:0),iT(t,(function(n,i){"inherit"===t[i]&&(t[i]=e[i])}))}var l=e.getModel("itemStyle"),u=l.getItemStyle(),h=0===t.lastIndexOf("empty",0)?"fill":"stroke",c=l.getShallow("decal");u.decal=c&&"inherit"!==c?Fv(c,a):i.decal,"inherit"===u.fill&&(u.fill=i[r]);"inherit"===u.stroke&&(u.stroke=i[h]);"inherit"===u.opacity&&(u.opacity=("fill"===r?i:n).opacity);s(u,i);var p=e.getModel("lineStyle"),d=p.getLineStyle();if(s(d,n),"auto"===u.fill&&(u.fill=i.fill),"auto"===u.stroke&&(u.stroke=i.fill),"auto"===d.stroke&&(d.stroke=i.fill),!o){var f=e.get("inactiveBorderWidth"),g=u[h];u.lineWidth="auto"===f?i.lineWidth>0&&g?2:0:u.lineWidth,u.fill=e.get("inactiveColor"),u.stroke=e.get("inactiveBorderColor"),d.stroke=p.get("inactiveColor"),d.lineWidth=p.get("inactiveWidth")}return{itemStyle:u,lineStyle:d}}(l=v||l||"roundRect",i,a,s,c,f,h),_=new rT,x=i.getModel("textStyle");if(!W(t.getLegendIcon)||v&&"inherit"!==v){var w="inherit"===v&&t.getData().getVisual("symbol")?"inherit"===g?t.getData().getVisual("symbolRotate"):g:0;_.add(function(t){var e=t.icon||"roundRect",n=pv(e,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill,t.symbolKeepAspect);n.setStyle(t.itemStyle),n.rotation=(t.iconRotate||0)*Math.PI/180,n.setOrigin([t.itemWidth/2,t.itemHeight/2]),e.indexOf("empty")>-1&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2);return n}({itemWidth:p,itemHeight:d,icon:l,iconRotate:w,itemStyle:m.itemStyle,lineStyle:m.lineStyle,symbolKeepAspect:y}))}else _.add(t.getLegendIcon({itemWidth:p,itemHeight:d,icon:l,iconRotate:g,itemStyle:m.itemStyle,lineStyle:m.lineStyle,symbolKeepAspect:y}));var b="left"===o?p+5:-5,S=o,M=r.get("formatter"),T=e;G(M)&&M?T=M.replace("{name}",null!=e?e:""):W(M)&&(T=M(e));var C=f?x.getTextColor():i.get("inactiveColor");_.add(new il({style:fc(x,{text:T,x:b,y:d/2,fill:C,align:S,verticalAlign:"middle"},{inheritColor:C})}));var I=new Hs({shape:_.getBoundingRect(),style:{fill:"transparent"}}),k=i.getModel("tooltip");return k.get("show")&&oc({el:I,componentModel:r,itemName:e,itemTooltipOption:k.option}),_.add(I),_.eachChild((function(t){t.silent=!0})),I.silent=!u,this.getContentGroup().add(_),ql(_),_.__legendDataIndex=n,_},n.prototype.layoutInner=function(t,e,n,i,r,o){var a=this.getContentGroup(),s=this.getSelectorGroup();Fp(t.get("orient"),a,t.get("itemGap"),n.width,n.height);var l=a.getBoundingRect(),u=[-l.x,-l.y];if(s.markRedraw(),a.markRedraw(),r){Fp("horizontal",s,t.get("selectorItemGap",!0));var h=s.getBoundingRect(),c=[-h.x,-h.y],p=t.get("selectorButtonGap",!0),d=t.getOrient().index,f=0===d?"width":"height",g=0===d?"height":"width",y=0===d?"y":"x";"end"===o?c[d]+=l[f]+p:u[d]+=h[f]+p,c[1-d]+=l[g]/2-h[g]/2,s.x=c[0],s.y=c[1],a.x=u[0],a.y=u[1];var v={x:0,y:0};return v[f]=l[f]+p+h[f],v[g]=Math.max(l[g],h[g]),v[y]=Math.min(0,h[y]+c[1-d]),v}return a.x=u[0],a.y=u[1],this.group.getBoundingRect()},n.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},n.type="legend.plain",n}(Yg);function hT(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries((function(t){for(var n=0;n<e.length;n++)if(!e[n].isSelected(t.name))return!1;return!0}))}function cT(t,e,n){var i="allSelect"===t||"inverseSelect"===t,r={},o=[];n.eachComponent({mainType:"legend",query:e},(function(n){i?n[t]():n[t](e.name),pT(n,r),o.push(n.componentIndex)}));var a={};return n.eachComponent("legend",(function(t){O(r,(function(e,n){t[e?"select":"unSelect"](n)})),pT(t,a)})),i?{selected:a,legendIndex:o}:{name:e.name,selected:a}}function pT(t,e){var n=e||{};return O(t.getData(),(function(e){var i=e.get("name");if("\n"!==i&&""!==i){var r=t.isSelected(i);yt(n,i)?n[i]=n[i]&&r:n[i]=r}})),n}function dT(t){t.registerComponentModel(eT),t.registerComponentView(uT),t.registerProcessor(t.PRIORITY.PROCESSOR.SERIES_FILTER,hT),t.registerSubTypeDefaulter("legend",(function(){return"plain"})),function(t){t.registerAction("legendToggleSelect","legendselectchanged",V(cT,"toggleSelected")),t.registerAction("legendAllSelect","legendselectall",V(cT,"allSelect")),t.registerAction("legendInverseSelect","legendinverseselect",V(cT,"inverseSelect")),t.registerAction("legendSelect","legendselected",V(cT,"select")),t.registerAction("legendUnSelect","legendunselected",V(cT,"unSelect"))}(t)}function fT(t,e,n){var i=[1,1];i[t.getOrient().index]=0,Xp(e,n,{type:"box",ignoreSize:!!i})}const gT=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return e(n,t),n.prototype.setScrollDataIndex=function(t){this.option.scrollDataIndex=t},n.prototype.init=function(e,n,i){var r=Yp(e);t.prototype.init.call(this,e,n,i),fT(this,e,r)},n.prototype.mergeOption=function(e,n){t.prototype.mergeOption.call(this,e,n),fT(this,this.option,e)},n.type="legend.scroll",n.defaultOption=Vc(eT.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),n}(eT);var yT=Br,vT=["width","height"],mT=["x","y"];const _T=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e.newlineDisabled=!0,e._currentIndex=0,e}return e(n,t),n.prototype.init=function(){t.prototype.init.call(this),this.group.add(this._containerGroup=new yT),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new yT)},n.prototype.resetInner=function(){t.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},n.prototype.renderInner=function(e,n,i,r,o,a,s){var l=this;t.prototype.renderInner.call(this,e,n,i,r,o,a,s);var u=this._controllerGroup,h=n.get("pageIconSize",!0),c=H(h)?h:[h,h];d("pagePrev",0);var p=n.getModel("pageTextStyle");function d(t,e){var i=t+"DataIndex",o=ec(n.get("pageIcons",!0)[n.getOrient().name][e],{onclick:F(l._pageGo,l,i,n,r)},{x:-c[0]/2,y:-c[1]/2,width:c[0],height:c[1]});o.name=t,u.add(o)}u.add(new il({name:"pageText",style:{text:"xx/xx",fill:p.getTextColor(),font:p.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),d("pageNext",1)},n.prototype.layoutInner=function(t,e,n,i,r,o){var a=this.getSelectorGroup(),s=t.getOrient().index,l=vT[s],u=mT[s],h=vT[1-s],c=mT[1-s];r&&Fp("horizontal",a,t.get("selectorItemGap",!0));var p=t.get("selectorButtonGap",!0),d=a.getBoundingRect(),f=[-d.x,-d.y],g=T(n);r&&(g[l]=n[l]-d[l]-p);var y=this._layoutContentAndController(t,i,g,s,l,h,c,u);if(r){if("end"===o)f[s]+=y[l]+p;else{var v=d[l]+p;f[s]-=v,y[u]-=v}y[l]+=d[l]+p,f[1-s]+=y[c]+y[h]/2-d[h]/2,y[h]=Math.max(y[h],d[h]),y[c]=Math.min(y[c],d[c]+f[1-s]),a.x=f[0],a.y=f[1],a.markRedraw()}return y},n.prototype._layoutContentAndController=function(t,e,n,i,r,o,a,s){var l=this.getContentGroup(),u=this._containerGroup,h=this._controllerGroup;Fp(t.get("orient"),l,t.get("itemGap"),i?n.width:null,i?null:n.height),Fp("horizontal",h,t.get("pageButtonItemGap",!0));var c=l.getBoundingRect(),p=h.getBoundingRect(),d=this._showController=c[r]>n[r],f=[-c.x,-c.y];e||(f[i]=l[s]);var g=[0,0],y=[-p.x,-p.y],v=tt(t.get("pageButtonGap",!0),t.get("itemGap",!0));d&&("end"===t.get("pageButtonPosition",!0)?y[i]+=n[r]-p[r]:g[i]+=p[r]+v);y[1-i]+=c[o]/2-p[o]/2,l.setPosition(f),u.setPosition(g),h.setPosition(y);var m={x:0,y:0};if(m[r]=d?n[r]:c[r],m[o]=Math.max(c[o],p[o]),m[a]=Math.min(0,p[a]+y[1-i]),u.__rectSize=n[r],d){var _={x:0,y:0};_[r]=Math.max(n[r]-p[r]-v,0),_[o]=m[o],u.setClipPath(new Hs({shape:_})),u.__rectSize=_[r]}else h.eachChild((function(t){t.attr({invisible:!0,silent:!0})}));var x=this._getPageInfo(t);return null!=x.pageIndex&&kh(l,{x:x.contentPosition[0],y:x.contentPosition[1]},d?t:null),this._updatePageInfoView(t,x),m},n.prototype._pageGo=function(t,e,n){var i=this._getPageInfo(e)[t];null!=i&&n.dispatchAction({type:"legendScroll",scrollDataIndex:i,legendId:e.id})},n.prototype._updatePageInfoView=function(t,e){var n=this._controllerGroup;O(["pagePrev","pageNext"],(function(i){var r=null!=e[i+"DataIndex"],o=n.childOfName(i);o&&(o.setStyle("fill",r?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),o.cursor=r?"pointer":"default")}));var i=n.childOfName("pageText"),r=t.get("pageFormatter"),o=e.pageIndex,a=null!=o?o+1:0,s=e.pageCount;i&&r&&i.setStyle("text",G(r)?r.replace("{current}",null==a?"":a+"").replace("{total}",null==s?"":s+""):r({current:a,total:s}))},n.prototype._getPageInfo=function(t){var e=t.get("scrollDataIndex",!0),n=this.getContentGroup(),i=this._containerGroup.__rectSize,r=t.getOrient().index,o=vT[r],a=mT[r],s=this._findTargetItemIndex(e),l=n.children(),u=l[s],h=l.length,c=h?1:0,p={contentPosition:[n.x,n.y],pageCount:c,pageIndex:c-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!u)return p;var d=m(u);p.contentPosition[r]=-d.s;for(var f=s+1,g=d,y=d,v=null;f<=h;++f)(!(v=m(l[f]))&&y.e>g.s+i||v&&!_(v,g.s))&&(g=y.i>g.i?y:v)&&(null==p.pageNextDataIndex&&(p.pageNextDataIndex=g.i),++p.pageCount),y=v;for(f=s-1,g=d,y=d,v=null;f>=-1;--f)(v=m(l[f]))&&_(y,v.s)||!(g.i<y.i)||(y=g,null==p.pagePrevDataIndex&&(p.pagePrevDataIndex=g.i),++p.pageCount,++p.pageIndex),g=v;return p;function m(t){if(t){var e=t.getBoundingRect(),n=e[a]+t[a];return{s:n,e:n+e[o],i:t.__legendDataIndex}}}function _(t,e){return t.e>=e&&t.s<=e+i}},n.prototype._findTargetItemIndex=function(t){return this._showController?(this.getContentGroup().eachChild((function(i,r){var o=i.__legendDataIndex;null==n&&null!=o&&(n=r),o===t&&(e=r)})),null!=e?e:n):0;var e,n},n.type="legend.scroll",n}(uT);function xT(t){jm(dT),t.registerComponentModel(gT),t.registerComponentView(_T),function(t){t.registerAction("legendScroll","legendscroll",(function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},(function(t){t.setScrollDataIndex(n)}))}))}(t)}function wT(t){jm(dT),jm(xT)}export{ii as $,A,Ie as B,kh as C,Sc as D,Oo as E,ei as F,Jn as G,_v as H,Ss as I,Zt as J,fh as K,G as L,Mn as M,dr as N,k as O,yi as P,gi as Q,X as R,as as S,fr as T,$n as U,Cs as V,Ds as W,ci as X,pi as Y,Os as Z,e as _,ge as a,ie as a$,fi as a0,ui as a1,rt as a2,ia as a3,T as a4,a as a5,ai as a6,wr as a7,oi as a8,Ks as a9,pc as aA,dc as aB,Zl as aC,lc as aD,dv as aE,Vb as aF,F as aG,Fb as aH,Dg as aI,xg as aJ,C as aK,Ec as aL,P as aM,Yx as aN,Kp as aO,jb as aP,wS as aQ,Cu as aR,Zu as aS,Xh as aT,Yg as aU,ow as aV,K_ as aW,lS as aX,Pb as aY,Hm as aZ,re as a_,Zs as aa,o as ab,di as ac,hi as ad,Wr as ae,ri as af,si as ag,li as ah,H as ai,Y as aj,mv as ak,vi as al,vt as am,L_ as an,Ug as ao,Br as ap,pv as aq,ny as ar,jm as as,VS as at,E as au,Qu as av,eh as aw,Rh as ax,D as ay,Ql as az,Ot as b,et as b$,zt as b0,Hs as b1,yt as b2,oh as b3,Du as b4,bu as b5,yh as b6,vh as b7,ot as b8,de as b9,hl as bA,hh as bB,Ph as bC,Io as bD,M_ as bE,S_ as bF,Cd as bG,_o as bH,Wp as bI,Vp as bJ,il as bK,fc as bL,Qo as bM,Qm as bN,Rp as bO,tu as bP,Jl as bQ,jl as bR,it as bS,Un as bT,Zn as bU,Ur as bV,Hn as bW,fo as bX,Yn as bY,qn as bZ,$r as b_,fe as ba,pe as bb,le as bc,pt as bd,Fc as be,Ca as bf,Pl as bg,Fv as bh,oc as bi,eu as bj,dl as bk,Dl as bl,V as bm,fd as bn,he as bo,xt as bp,Eo as bq,I as br,J as bs,Hp as bt,xo as bu,N as bv,Jy as bw,$y as bx,Na as by,dt as bz,Rt as c,pl as c$,wt as c0,kt as c1,It as c2,_t as c3,Tt as c4,Ct as c5,bt as c6,Mt as c7,cl as c8,Yr as c9,tc as cA,Jh as cB,$b as cC,Ho as cD,e_ as cE,dd as cF,ro as cG,nd as cH,go as cI,qg as cJ,Yw as cK,Zh as cL,J_ as cM,ye as cN,At as cO,mn as cP,ss as cQ,us as cR,h as cS,qw as cT,so as cU,Ww as cV,Vc as cW,Qw as cX,lx as cY,sx as cZ,rb as c_,Rl as ca,Nl as cb,fv as cc,sc as cd,xn as ce,vn as cf,Pt as cg,Ed as ch,Lg as ci,os as cj,ol as ck,bc as cl,eb as cm,Xu as cn,ve as co,Oh as cp,ao as cq,Q as cr,sy as cs,ly as ct,Fx as cu,zx as cv,Kh as cw,qr as cx,$h as cy,jh as cz,Nt as d,jy as d$,Jr as d0,Gn as d1,pr as d2,Ti as d3,L as d4,Ch as d5,gc as d6,ix as d7,xc as d8,Wh as d9,Bp as dA,zo as dB,jr as dC,Xx as dD,Nx as dE,ec as dF,IM as dG,kM as dH,r as dI,j as dJ,ne as dK,No as dL,_d as dM,Ao as dN,Zy as dO,Ky as dP,nc as dQ,bo as dR,Nf as dS,ql as dT,br as dU,_x as dV,q_ as dW,Zr as dX,Vf as dY,Bl as dZ,El as d_,Hh as da,KS as db,$S as dc,jS as dd,eM as de,iM as df,mr as dg,Ux as dh,kS as di,A_ as dj,TM as dk,PS as dl,qh as dm,AS as dn,tM as dp,JS as dq,nM as dr,Yp as ds,Gp as dt,Xp as du,Lp as dv,Zc as dw,eo as dx,Mo as dy,qp as dz,O as e,P_ as e0,uv as e1,Ip as e2,Po as e3,oo as e4,Fp as e5,Wf as e6,qf as e7,$ as e8,Xf as e9,Kw as eA,id as ea,pg as eb,dg as ec,sd as ed,fn as ee,Su as ef,Lt as eg,Ng as eh,Nh as ei,Hw as ej,Mb as ek,Gb as el,CM as em,$M as en,tT as eo,wT as ep,Bm as eq,pn as er,hn as es,Op as et,Lo as eu,Gw as ev,Wo as ew,Uw as ex,Xw as ey,Xn as ez,R as f,B as g,W as h,ue as i,rl as j,z as k,Aw as l,ce as m,cs as n,Ow as o,Xr as p,Rw as q,kw as r,Lw as s,Dw as t,ww as u,Ah as v,fs as w,wc as x,tt as y,Dh as z};
