<template>
  <div class="return-order-list">
    <div class="page-header">
      <h2>退货单管理</h2>
    </div>

    <!-- 搜索表单 -->
    <el-form :model="searchForm" inline class="search-form" @submit.prevent="handleSearch">
      <el-form-item label="退货单号">
        <el-input 
          v-model="searchForm.return_number" 
          placeholder="请输入退货单号" 
          clearable 
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
          <el-option label="待确认" value="待确认" />
          <el-option label="退货中" value="退货中" />
          <el-option label="已完成" value="已完成" />
          <el-option label="已拒绝" value="已拒绝" />
          <el-option label="已取消" value="已取消" />
        </el-select>
      </el-form-item>
      <el-form-item label="客户名称">
        <el-input 
          v-model="searchForm.customer_name" 
          placeholder="请输入客户名称" 
          clearable 
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-card class="operator-card mb-20">
      <div class="operator-content">
        <div class="left-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增退货单
          </el-button>
          <el-button type="success" @click="handleBatchExport" :disabled="selectedReturnOrders.length === 0">
            <el-icon><Download /></el-icon>
            批量导出
          </el-button>
          <el-button type="warning" @click="handleBatchPrint" :disabled="selectedReturnOrders.length === 0">
            <el-icon><Printer /></el-icon>
            批量打印
          </el-button>
        </div>

        <div class="right-actions">
          <!-- 卡片详细程度切换 -->
          <el-button-group>
            <el-button
              :type="cardMode === 'detailed' ? 'primary' : ''"
              @click="cardMode = 'detailed'"
              title="详细模式"
            >
              详细
            </el-button>
            <el-button
              :type="cardMode === 'simple' ? 'primary' : ''"
              @click="cardMode = 'simple'"
              title="简化模式"
            >
              简化
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 退货单列表 -->
    <el-card>
      <!-- 卡片视图 -->
      <div v-loading="loading">
        <!-- 批量选择工具栏 -->
        <div v-if="selectedReturnOrders.length > 0" class="batch-selection-bar">
          <span>已选择 {{ selectedReturnOrders.length }} 个退货单</span>
          <el-button size="small" @click="clearSelection">清空选择</el-button>
        </div>

        <!-- 卡片列表 -->
        <div class="return-cards-list">
          <div
            v-for="returnOrder in returnOrders"
            :key="returnOrder.id"
            :class="['return-card', { 'selected': isReturnOrderSelected(returnOrder) }]"
            @click="handleCardClick(returnOrder)"
          >
            <!-- 选择框 -->
            <div class="card-checkbox" @click.stop>
              <el-checkbox
                :model-value="isReturnOrderSelected(returnOrder)"
                @change="handleCardSelection(returnOrder, $event)"
              />
            </div>

            <!-- 详细模式卡片内容 -->
            <div v-if="cardMode === 'detailed'" class="card-content detailed">
              <!-- 第一行：退货单号、客户、项目、状态 -->
              <div class="card-row-1">
                <div class="left-info">
                  <div class="return-number">{{ returnOrder.return_number }}</div>
                  <div class="customer-project-status">
                    <span class="customer">{{ returnOrder.customer_name || 'N/A' }}</span>
                    <span class="separator">|</span>
                    <span class="project" :title="returnOrder.project_name">{{ returnOrder.project_name }}</span>
                    <div class="status-tags">
                      <el-tag :type="getStatusType(returnOrder.status)" size="default" class="status-tag-prominent return-status">
                        {{ returnOrder.status }}
                      </el-tag>
                      <el-tag :type="getSettlementStatusType(returnOrder.settlement_status)" size="default" class="status-tag-prominent settlement-status">
                        {{ returnOrder.settlement_status || '未结清' }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第二行：关联订单、金额和时间信息 -->
              <div class="card-row-2">
                <div class="left-details">
                  <div class="orders-info">
                    <div class="order-group">
                      <span class="order-label">关联订单</span>
                      <div v-if="returnOrder.order_number" class="order-links">
                        <el-tag
                          type="primary"
                          size="small"
                          class="order-tag"
                          @click.stop="handleViewOrder(returnOrder)"
                        >
                          {{ returnOrder.order_number }}
                        </el-tag>
                      </div>
                      <span v-else class="no-orders">暂无关联</span>
                    </div>
                  </div>
                  <div class="reason-info">
                    <div class="reason-group">
                      <span class="reason-label">退货原因</span>
                      <span class="reason-value" :title="returnOrder.reason">{{ returnOrder.reason }}</span>
                    </div>
                  </div>
                  <div class="amount-info">
                    <div class="amount-group">
                      <span class="amount-label">退货金额</span>
                      <span class="amount-value total">{{ formatCurrency(returnOrder.total_amount) }}</span>
                    </div>
                  </div>
                  <div class="dates-info">
                    <div class="date-group">
                      <span class="date-label">退货日期</span>
                      <span class="date-value">{{ returnOrder.return_date ? formatDate(returnOrder.return_date) : 'N/A' }}</span>
                    </div>
                    <div class="date-group">
                      <span class="date-label">创建时间</span>
                      <span class="date-value">{{ returnOrder.created_at ? formatDateTime(returnOrder.created_at) : 'N/A' }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第三行：操作按钮 -->
              <div class="card-actions detailed" @click.stop>
                <el-button v-if="returnOrder.status === '待确认'" type="warning" size="small" @click="handleEdit(returnOrder)" title="编辑退货单">
                  编辑
                </el-button>
                <el-button type="info" size="small" @click="handleExport(returnOrder)" title="导出退货单">
                  导出
                </el-button>
                <el-button v-if="returnOrder.status === '待确认'" type="success" size="small" @click="handleConfirm(returnOrder)" title="确认退货">
                  确认
                </el-button>
                <el-button v-if="returnOrder.status === '退货中'" type="primary" size="small" @click="handleComplete(returnOrder)" title="签收退货">
                  签收
                </el-button>
                <el-button v-if="returnOrder.status === '待确认'" type="danger" size="small" @click="handleReject(returnOrder)" title="拒绝退货">
                  拒绝
                </el-button>
                <el-button v-if="['待确认', '退货中'].includes(returnOrder.status)" type="warning" size="small" @click="handleCancel(returnOrder)" title="取消退货">
                  取消
                </el-button>
                <el-button v-if="returnOrder.status === '待确认'" type="danger" size="small" @click="handleDelete(returnOrder)" title="删除退货单">
                  删除
                </el-button>
              </div>
            </div>

            <!-- 简化模式卡片内容 -->
            <div v-else class="card-content simple">
              <!-- 紧凑单行信息展示 -->
              <div class="card-main simple">
                <!-- 退货单号 -->
                <div class="return-number">{{ returnOrder.return_number }}</div>

                <!-- 客户和项目 -->
                <div class="customer-project">
                  <span class="customer">{{ returnOrder.customer_name || 'N/A' }}</span>
                  <span class="project" :title="returnOrder.project_name">{{ returnOrder.project_name }}</span>
                </div>

                <!-- 退货原因 -->
                <div class="reason-info">
                  <span class="reason" :title="returnOrder.reason">{{ returnOrder.reason }}</span>
                </div>

                <!-- 退货金额 -->
                <div class="amount-info">
                  <span class="total-amount">{{ formatCurrency(returnOrder.total_amount) }}</span>
                </div>

                <!-- 状态 -->
                <div class="status-tags">
                  <el-tag :type="getStatusType(returnOrder.status)" size="default" class="status-tag-prominent return-status">
                    {{ returnOrder.status }}
                  </el-tag>
                  <el-tag :type="getSettlementStatusType(returnOrder.settlement_status)" size="default" class="status-tag-prominent settlement-status">
                    {{ returnOrder.settlement_status || '未结清' }}
                  </el-tag>
                </div>

                <!-- 操作按钮 -->
                <div class="card-actions simple" @click.stop>
                  <!-- 主要操作按钮 -->
                  <el-button type="info" size="small" @click="handleExport(returnOrder)" title="导出退货单">
                    导出
                  </el-button>
                  <el-button v-if="returnOrder.status === '待确认'" type="success" size="small" @click="handleConfirm(returnOrder)" title="确认退货">
                    确认
                  </el-button>
                  <el-button v-if="returnOrder.status === '退货中'" type="primary" size="small" @click="handleComplete(returnOrder)" title="签收退货">
                    签收
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.per_page"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 导出对话框 -->
    <ReturnOrderExportDialog
      v-model="exportDialogVisible"
      :return-order-id="currentExportReturnOrder?.id"
      :return-order-name="currentExportReturnOrder?.return_number"
      @success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, Printer } from '@element-plus/icons-vue'
import type { ReturnOrder, ReturnOrderStatus, ListReturnOrderParams } from '@/types/return'
import { returnApi } from '@/api/return'
import ReturnOrderExportDialog from '@/components/ReturnOrderExportDialog.vue'

const router = useRouter()

// 状态
const loading = ref(false)
const returnOrders = ref<ReturnOrder[]>([])
const selectedReturnOrders = ref<ReturnOrder[]>([])

// 卡片模式
const cardMode = ref('detailed') // 'detailed' | 'simple'

// 导出相关状态
const exportDialogVisible = ref(false)
const currentExportReturnOrder = ref<ReturnOrder | null>(null)

// 分页
const pagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

// 搜索表单
const searchForm = reactive<Omit<ListReturnOrderParams, 'page' | 'per_page'>>({
  return_number: '',
  status: undefined,
  customer_name: '',
  start_date: '',
  end_date: ''
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 格式化日期
const formatDate = (date: string | undefined) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (datetime: string | undefined) => {
  if (!datetime) return '-'
  return new Date(datetime).toLocaleString('zh-CN')
}

// 格式化货币
const formatCurrency = (amount: number | string | undefined) => {
  if (!amount) return '¥0.00'
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  return `¥${numAmount.toFixed(2)}`
}

// 获取退货单列表
const fetchReturnOrders = async () => {
  loading.value = true
  try {
    const params: ListReturnOrderParams = {
      ...searchForm,
      page: pagination.page,
      per_page: pagination.per_page
    }
    
    const response = await returnApi.getList(params)
    const responseData = response.data || response
    
    if (responseData.items) {
      returnOrders.value = responseData.items
      pagination.total = responseData.pagination?.total || 0
    } else {
      returnOrders.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取退货单列表失败:', error)
    ElMessage.error('获取退货单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  if (dateRange.value) {
    searchForm.start_date = dateRange.value[0]
    searchForm.end_date = dateRange.value[1]
  } else {
    searchForm.start_date = ''
    searchForm.end_date = ''
  }
  pagination.page = 1
  fetchReturnOrders()
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key as keyof typeof searchForm] = ''
  })
  dateRange.value = null
  pagination.page = 1
  fetchReturnOrders()
}

// 分页
const handleSizeChange = (val: number) => {
  pagination.per_page = val
  fetchReturnOrders()
}

const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchReturnOrders()
}

// 状态样式
const getStatusType = (status: ReturnOrderStatus | undefined) => {
  const statusMap: Record<ReturnOrderStatus, string> = {
    '待确认': 'warning',
    '退货中': 'primary',
    '已签收': 'success',
    '已拒绝': 'danger',
    '已取消': 'info'
  }
  return statusMap[status || '待确认']
}

// 结清状态样式
const getSettlementStatusType = (settlementStatus: string) => {
  const statusMap: Record<string, string> = {
    '未结清': 'warning',
    '已结清': 'success'
  }
  return statusMap[settlementStatus] || 'warning'
}

// 卡片选择相关函数
const isReturnOrderSelected = (returnOrder: ReturnOrder) => {
  return selectedReturnOrders.value.some(selected => selected.id === returnOrder.id)
}

const handleCardSelection = (returnOrder: ReturnOrder, checked: boolean) => {
  if (checked) {
    if (!isReturnOrderSelected(returnOrder)) {
      selectedReturnOrders.value.push(returnOrder)
    }
  } else {
    selectedReturnOrders.value = selectedReturnOrders.value.filter(selected => selected.id !== returnOrder.id)
  }
}

const clearSelection = () => {
  selectedReturnOrders.value = []
}

// 卡片点击事件
const handleCardClick = (returnOrder: ReturnOrder) => {
  // 延迟检查，确保文本选择操作完成
  setTimeout(() => {
    // 检查是否有文本被选中（用户正在选择文字）
    const selection = window.getSelection()
    if (selection && selection.toString().trim().length > 0) {
      return // 如果有文本被选中，不触发行点击
    }

    // 跳转到退货单详情页面，使用与查看按钮相同的路径
    router.push(`/returns/${returnOrder.id}`)
  }, 10) // 短暂延迟，让文本选择操作先完成
}

// 导出功能
const handleExport = (returnOrder: ReturnOrder) => {
  currentExportReturnOrder.value = returnOrder
  exportDialogVisible.value = true
}

// 导出成功处理
const handleExportSuccess = () => {
  currentExportReturnOrder.value = null
}

// 批量操作
const handleBatchExport = () => {
  if (selectedReturnOrders.value.length === 0) {
    ElMessage.warning('请选择要导出的退货单')
    return
  }

  if (selectedReturnOrders.value.length === 1) {
    handleExport(selectedReturnOrders.value[0])
  } else {
    // 处理多选导出逻辑
    ElMessage.info(`已选择${selectedReturnOrders.value.length}个退货单，当前版本仅支持单个导出`)
    handleExport(selectedReturnOrders.value[0])
  }
}

const handleBatchPrint = () => {
  ElMessage.info('批量打印功能开发中')
}

// 新增退货单
const handleAdd = () => {
  router.push('/returns/new')
}

// 查看关联订单
const handleViewOrder = (returnOrder: ReturnOrder) => {
  if (returnOrder.order?.id) {
    router.push(`/orders/view/${returnOrder.order.id}`)
  } else {
    ElMessage.warning('未找到关联订单信息')
  }
}

// 操作
const handleView = (row: ReturnOrder) => {
  router.push(`/returns/${row.id}`)
}

const handleEdit = (row: ReturnOrder) => {
  router.push(`/returns/${row.id}/edit`)
}

const handleConfirm = async (row: ReturnOrder) => {
  try {
    await ElMessageBox.confirm('确认要确认此退货单吗？', '提示', {
      type: 'warning'
    })
    await returnApi.updateStatus(row.id!, '退货中')
    ElMessage.success('退货单已确认，进入退货中状态')
    fetchReturnOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认退货单失败:', error)
      ElMessage.error('确认退货单失败')
    }
  }
}

const handleComplete = async (row: ReturnOrder) => {
  try {
    await ElMessageBox.confirm('确认要签收此退货单吗？', '提示', {
      type: 'warning'
    })
    await returnApi.updateStatus(row.id!, '已签收')
    ElMessage.success('退货单已签收')
    fetchReturnOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('签收退货单失败:', error)
      ElMessage.error('签收退货单失败')
    }
  }
}

const handleReject = async (row: ReturnOrder) => {
  try {
    await ElMessageBox.confirm('确认要拒绝此退货单吗？', '提示', {
      type: 'warning'
    })
    await returnApi.updateStatus(row.id!, '已拒绝')
    ElMessage.success('退货单已拒绝')
    fetchReturnOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('拒绝退货单失败:', error)
      ElMessage.error('拒绝退货单失败')
    }
  }
}

const handleCancel = async (row: ReturnOrder) => {
  try {
    await ElMessageBox.confirm('确认要取消此退货单吗？', '提示', {
      type: 'warning'
    })
    await returnApi.updateStatus(row.id!, '已取消')
    ElMessage.success('退货单已取消')
    fetchReturnOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消退货单失败:', error)
      ElMessage.error('取消退货单失败')
    }
  }
}

const handleDelete = async (row: ReturnOrder) => {
  try {
    await ElMessageBox.confirm('确认要删除此退货单吗？此操作不可恢复！', '警告', {
      type: 'warning'
    })
    await returnApi.delete(row.id!)
    ElMessage.success('退货单已删除')
    fetchReturnOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除退货单失败:', error)
      ElMessage.error('删除退货单失败')
    }
  }
}



onMounted(() => {
  fetchReturnOrders()
})
</script>

<style scoped lang="scss">
.return-order-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 操作按钮区域样式
.operator-card {
  .operator-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      gap: 12px;
    }

    .right-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
}

// 批量选择工具栏
.batch-selection-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #606266;
}

// 卡片列表布局
.return-cards-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

// 退货单卡片样式
.return-card {
  position: relative;
  width: 100%;
  border: 2px solid #e8eaed;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 4px;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
    transform: translateY(-1px);
  }

  &.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2), 0 4px 16px rgba(64, 158, 255, 0.1);
    transform: translateY(-1px);
  }

  .card-checkbox {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 2;
  }

  .card-content {
    padding: 12px 16px;
    padding-left: 44px;

    &.detailed {
      padding: 14px 16px;
      padding-left: 44px;
    }

    &.simple {
      padding: 10px 16px;
      padding-left: 44px;
    }
  }

  // 详细模式样式
  .card-content.detailed {
    .card-row-1 {
      display: flex;
      align-items: flex-start;
      margin-bottom: 8px;

      .left-info {
        flex: 1;
        min-width: 0;

        .return-number {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .customer-project-status {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-wrap: wrap;

          .customer {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            flex-shrink: 0;
          }

          .separator {
            color: #dcdfe6;
            font-size: 12px;
          }

          .project {
            font-size: 13px;
            color: #606266;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 200px;
            margin-right: 12px;
          }

          .status-tags {
            display: flex;
            gap: 8px;
            flex-shrink: 0;

            .status-tag-prominent {
              font-weight: 600;
              border-radius: 8px;
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
              transition: all 0.3s ease;
              border: 2px solid transparent;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
              }

              &.return-status, &.settlement-status {
                font-size: 13px;
                padding: 5px 12px;
                font-weight: 600;
                letter-spacing: 0.3px;

                &.el-tag--success {
                  background: linear-gradient(135deg, #67c23a, #85ce61);
                  border-color: #67c23a;
                  color: #fff;
                  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.25);
                }

                &.el-tag--warning {
                  background: linear-gradient(135deg, #e6a23c, #ebb563);
                  border-color: #e6a23c;
                  color: #fff;
                  box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25);
                }

                &.el-tag--danger {
                  background: linear-gradient(135deg, #f56c6c, #f78989);
                  border-color: #f56c6c;
                  color: #fff;
                  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.25);
                }

                &.el-tag--info {
                  background: linear-gradient(135deg, #909399, #a6a9ad);
                  border-color: #909399;
                  color: #fff;
                  box-shadow: 0 2px 6px rgba(144, 147, 153, 0.25);
                }

                &.el-tag--primary {
                  background: linear-gradient(135deg, #409eff, #66b1ff);
                  border-color: #409eff;
                  color: #fff;
                  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
                }
              }
            }
          }
        }
      }
    }

    .card-row-2 {
      margin-bottom: 12px;

      .left-details {
        display: flex;
        gap: 24px;
        align-items: flex-start;

        .orders-info {
          flex: 1;

          .order-group {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .order-label {
              font-size: 12px;
              color: #909399;
              font-weight: 500;
            }

            .order-links {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
            }

            .order-tag {
              cursor: pointer;
              transition: all 0.2s;

              &:hover {
                transform: scale(1.05);
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
              }
            }

            .no-orders {
              font-size: 13px;
              color: #c0c4cc;
              font-style: italic;
            }
          }
        }

        .reason-info {
          flex: 1;

          .reason-group {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .reason-label {
              font-size: 12px;
              color: #909399;
              font-weight: 500;
            }

            .reason-value {
              font-size: 14px;
              color: #303133;
              font-weight: 500;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 300px;
            }
          }
        }

        .amount-info {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .amount-group {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .amount-label {
              font-size: 12px;
              color: #909399;
              font-weight: 500;
            }

            .amount-value {
              font-size: 14px;
              font-weight: 600;

              &.total {
                color: #f56c6c;
                font-size: 16px;
              }
            }
          }
        }

        .dates-info {
          display: flex;
          gap: 16px;

          .date-group {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .date-label {
              font-size: 12px;
              color: #909399;
              font-weight: 500;
            }

            .date-value {
              font-size: 13px;
              color: #606266;
            }
          }
        }
      }
    }

    .card-actions.detailed {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      padding-top: 8px;
      border-top: 1px solid #f0f2f5;

      .el-button {
        font-size: 12px;

        .el-icon {
          margin-right: 2px;
        }
      }
    }
  }

  // 简化模式样式
  .card-content.simple {
    .card-main.simple {
      display: flex;
      align-items: center;
      gap: 16px;

      .return-number {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        min-width: 120px;
        flex-shrink: 0;
      }

      .customer-project {
        display: flex;
        flex-direction: column;
        gap: 2px;
        min-width: 150px;
        flex-shrink: 0;

        .customer {
          font-size: 13px;
          font-weight: 500;
          color: #303133;
        }

        .project {
          font-size: 12px;
          color: #606266;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 150px;
        }
      }

      .reason-info {
        flex: 1;
        min-width: 0;

        .reason {
          font-size: 13px;
          color: #606266;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .amount-info {
        min-width: 100px;
        flex-shrink: 0;
        text-align: right;

        .total-amount {
          font-size: 14px;
          font-weight: 600;
          color: #f56c6c;
        }
      }

      .status-tags {
        display: flex;
        gap: 6px;
        flex-shrink: 0;

        .status-tag-prominent {
          font-size: 12px;
          padding: 3px 8px;
          font-weight: 600;
          border-radius: 6px;
        }
      }

      .card-actions.simple {
        display: flex;
        gap: 6px;
        flex-shrink: 0;

        .el-button {
          font-size: 12px;
          padding: 4px 8px;
        }
      }
    }
  }
}

// 通用样式
.mb-20 {
  margin-bottom: 20px;
}
</style>
