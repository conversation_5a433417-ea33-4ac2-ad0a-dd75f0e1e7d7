<template>
  <div class="bank-account-list">
    <el-card class="header-card mb-20">
      <div class="card-header">
        <div class="left">
          <h2 class="page-title">银行账户管理</h2>
          <el-tag type="info">管理企业银行账户信息</el-tag>
        </div>
        <div class="right">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon> 新增账户
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 银行账户列表 -->
    <el-card>
      <div class="table-container" v-loading="loading">
        <el-table
          :data="bankAccountList"
          border
          stripe
          style="width: 100%"
          :row-class-name="getRowClass"
        >
          <el-table-column prop="bank_name" label="银行名称" min-width="150" />
          <el-table-column prop="account_name" label="账户名称" min-width="200" />
          <el-table-column prop="account_number" label="账户号码" min-width="200">
            <template #default="scope">
              <span class="account-number">{{ formatAccountNumber(scope.row.account_number) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="is_default" label="默认账户" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.is_default" type="success">默认</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" min-width="150">
            <template #default="scope">
              <span>{{ scope.row.notes || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button
                v-if="!scope.row.is_default"
                link
                type="success"
                size="small"
                @click="handleSetDefault(scope.row)"
              >
                设为默认
              </el-button>
              <el-button
                link
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
                :disabled="scope.row.is_default"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 银行账户编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑银行账户' : '新增银行账户'"
      width="600px"
      destroy-on-close
      @closed="handleDialogClosed"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="银行名称" prop="bank_name">
          <el-select
            v-model="form.bank_name"
            placeholder="请选择或输入银行名称"
            style="width: 100%"
            filterable
            allow-create
            default-first-option
            :reserve-keyword="false"
          >
            <el-option label="中国工商银行" value="中国工商银行" />
            <el-option label="中国农业银行" value="中国农业银行" />
            <el-option label="中国银行" value="中国银行" />
            <el-option label="中国建设银行" value="中国建设银行" />
            <el-option label="交通银行" value="交通银行" />
            <el-option label="招商银行" value="招商银行" />
            <el-option label="浦发银行" value="浦发银行" />
            <el-option label="中信银行" value="中信银行" />
            <el-option label="光大银行" value="光大银行" />
            <el-option label="华夏银行" value="华夏银行" />
            <el-option label="民生银行" value="民生银行" />
            <el-option label="广发银行" value="广发银行" />
            <el-option label="平安银行" value="平安银行" />
            <el-option label="兴业银行" value="兴业银行" />
            <el-option label="邮储银行" value="中国邮政储蓄银行" />
            <el-option label="北京银行" value="北京银行" />
            <el-option label="上海银行" value="上海银行" />
            <el-option label="江苏银行" value="江苏银行" />
            <el-option label="浙商银行" value="浙商银行" />
            <el-option label="渤海银行" value="渤海银行" />
          </el-select>
          <div style="margin-top: 5px; color: #999; font-size: 12px;">
            可以从列表中选择，也可以直接输入自定义银行名称
          </div>
        </el-form-item>

        <el-form-item label="账户名称" prop="account_name">
          <el-input v-model="form.account_name" placeholder="请输入账户名称" />
        </el-form-item>

        <el-form-item label="账户号码" prop="account_number">
          <el-input v-model="form.account_number" placeholder="请输入银行账户号码" />
        </el-form-item>

        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>

        <el-form-item label="设为默认" prop="is_default">
          <el-switch v-model="form.is_default" />
          <span style="margin-left: 10px; color: #999; font-size: 12px;">
            设为默认后，其他账户将自动取消默认状态
          </span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { bankAccountApi } from '@/api/system'

const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref(null)

// 银行账户列表
const bankAccountList = ref([])

// 表单数据
const form = reactive({
  id: null,
  bank_name: '',
  account_name: '',
  account_number: '',
  notes: '',
  is_default: false
})

// 表单验证规则
const rules = {
  bank_name: [
    { required: true, message: '请选择或输入银行名称', trigger: 'change' }
  ],
  account_name: [
    { required: true, message: '请输入账户名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  account_number: [
    { required: true, message: '请输入账户号码', trigger: 'blur' },
    { min: 10, max: 50, message: '账户号码长度在 10 到 50 个字符', trigger: 'blur' }
  ]
}

// 获取银行账户列表
const fetchBankAccounts = async () => {
  loading.value = true
  try {
    const response = await bankAccountApi.getList() as any
    
    // 处理响应数据
    if (Array.isArray(response)) {
      bankAccountList.value = response
    } else if (response && typeof response === 'object') {
      const data = response.data
      if (Array.isArray(data)) {
        bankAccountList.value = data
      } else if (data && typeof data === 'object' && Object.keys(data).length === 0) {
        bankAccountList.value = []
      } else {
        bankAccountList.value = []
      }
    } else {
      bankAccountList.value = []
    }
  } catch (error) {
    console.error('获取银行账户列表失败:', error)
    ElMessage.error('获取银行账户列表失败，使用模拟数据')
    
    // 使用模拟数据
    bankAccountList.value = [
      {
        id: 1,
        bank_name: '中国工商银行',
        account_name: '柏成物资配送有限公司',
        account_number: '1234567890123456789',
        is_default: true,
        notes: '主要收款账户',
        created_at: '2025-01-01 10:00:00'
      },
      {
        id: 2,
        bank_name: '招商银行',
        account_name: '柏成物资配送有限公司',
        account_number: '9876543210987654321',
        is_default: false,
        notes: '备用账户',
        created_at: '2025-01-02 14:30:00'
      }
    ]
  } finally {
    loading.value = false
  }
}

// 新增账户
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑账户
const handleEdit = (row: any) => {
  isEdit.value = true
  Object.assign(form, {
    id: row.id,
    bank_name: row.bank_name,
    account_name: row.account_name,
    account_number: row.account_number,
    notes: row.notes || '',
    is_default: row.is_default
  })
  dialogVisible.value = true
}

// 设为默认账户
const handleSetDefault = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 "${row.account_name}" 设为默认银行账户吗？`,
      '设为默认账户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await bankAccountApi.setDefault(row.id)
    ElMessage.success('设置默认账户成功')
    fetchBankAccounts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置默认账户失败:', error)
      ElMessage.error('设置默认账户失败')
    }
  }
}



// 删除账户
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除银行账户 "${row.account_name}" 吗？此操作不可恢复！`,
      '删除账户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await bankAccountApi.delete(row.id)
    ElMessage.success('删除账户成功')
    fetchBankAccounts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除账户失败:', error)
      ElMessage.error('删除账户失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitting.value = true
      try {
        const submitData = { ...form }
        delete submitData.id
        
        if (isEdit.value) {
          await bankAccountApi.update(form.id, submitData)
          ElMessage.success('更新银行账户成功')
        } else {
          await bankAccountApi.create(submitData)
          ElMessage.success('创建银行账户成功')
        }
        
        dialogVisible.value = false
        fetchBankAccounts()
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error(isEdit.value ? '更新银行账户失败' : '创建银行账户失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    bank_name: '',
    account_name: '',
    account_number: '',
    notes: '',
    is_default: false
  })
}

// 对话框关闭处理
const handleDialogClosed = () => {
  resetForm()
  formRef.value?.clearValidate()
}

// 格式化账户号码（隐藏中间部分）
const formatAccountNumber = (accountNumber: string) => {
  if (!accountNumber || accountNumber.length < 8) return accountNumber
  const start = accountNumber.substring(0, 4)
  const end = accountNumber.substring(accountNumber.length - 4)
  const middle = '*'.repeat(accountNumber.length - 8)
  return `${start}${middle}${end}`
}



// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

// 获取行类名
const getRowClass = ({ row }: { row: any }) => {
  if (row.is_default) {
    return 'default-row'
  }
  return ''
}

onMounted(() => {
  fetchBankAccounts()
})
</script>

<style lang="scss" scoped>
.bank-account-list {
  .header-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 22px;
          font-weight: bold;
        }
      }
    }
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .table-container {
    .account-number {
      font-family: 'Courier New', monospace;
      font-weight: bold;
    }
  }
}

:deep(.default-row) {
  background-color: #f0f9ff;
}

:deep(.inactive-row) {
  background-color: #fef0f0;
}
</style>
