<template>
  <el-dialog
    v-model="dialogVisible"
    title="上传备份文件并恢复"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="upload-container">
      <!-- 警告提示 -->
      <el-alert
        title="重要提示"
        type="warning"
        :closable="false"
        show-icon
        class="warning-alert"
      >
        <template #default>
          <p>上传备份文件将会<strong>完全替换</strong>当前系统数据，此操作不可逆！</p>
          <p>请确保备份文件来源可靠，建议在操作前先创建当前数据的备份。</p>
        </template>
      </el-alert>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="备份名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入备份名称"
            maxlength="200"
            show-word-limit
            clearable
          />
        </el-form-item>
        
        <el-form-item label="备份描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入备份描述（可选）"
            :rows="3"
            maxlength="500"
            show-word-limit
            clearable
          />
        </el-form-item>

        <el-form-item label="备份文件" prop="file">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            :auto-upload="false"
            :limit="1"
            accept=".db"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将备份文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 .db 格式的备份文件，文件大小不限
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <!-- 文件信息显示 -->
        <div v-if="selectedFile" class="file-info">
          <h4>文件信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">
              {{ selectedFile.name }}
            </el-descriptions-item>
            <el-descriptions-item label="文件大小">
              {{ formatFileSize(selectedFile.size) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后修改">
              {{ formatDateTime(selectedFile.lastModified) }}
            </el-descriptions-item>
            <el-descriptions-item label="文件类型">
              {{ selectedFile.type || 'application/octet-stream' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 确认复选框 -->
        <el-form-item prop="confirmed">
          <el-checkbox v-model="form.confirmed" size="large">
            我已了解风险，确认要上传此备份文件并恢复数据
          </el-checkbox>
        </el-form-item>
      </el-form>

      <!-- 进度显示 -->
      <div v-if="uploading" class="progress-container">
        <el-progress
          :percentage="progress"
          :status="progressStatus"
          :stroke-width="8"
          text-inside
        />
        <p class="progress-text">{{ progressText }}</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="uploading">取消</el-button>
        <el-button 
          type="danger" 
          @click="handleConfirm" 
          :loading="uploading"
          :disabled="uploading || !selectedFile || !form.confirmed"
        >
          {{ uploading ? '上传恢复中...' : '确认上传并恢复' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, FormInstance, FormRules, UploadFile } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { backupApi } from '@/api/system'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const uploadRef = ref()
const uploading = ref(false)
const progress = ref(0)
const progressStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const progressText = ref('')
const selectedFile = ref<File | null>(null)
const fileList = ref<UploadFile[]>([])

// 表单数据
const form = reactive({
  name: '',
  description: '',
  confirmed: false
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入备份名称', trigger: 'blur' },
    { min: 1, max: 200, message: '备份名称长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  file: [
    { required: true, message: '请选择备份文件', trigger: 'change' }
  ],
  confirmed: [
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请确认您已了解操作风险'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  form.name = ''
  form.description = ''
  form.confirmed = false
  selectedFile.value = null
  fileList.value = []
  uploading.value = false
  progress.value = 0
  progressStatus.value = ''
  progressText.value = ''
  
  // 清除表单验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  
  // 清除上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 处理文件选择
const handleFileChange = (file: UploadFile) => {
  if (!file.raw) return
  
  // 验证文件类型
  if (!file.name.toLowerCase().endsWith('.db')) {
    ElMessage.error('只能上传 .db 格式的备份文件')
    return false
  }
  
  selectedFile.value = file.raw
  
  // 自动生成备份名称
  if (!form.name) {
    const fileName = file.name.replace(/\.[^/.]+$/, '')
    form.name = `上传备份_${fileName}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}`
  }
  
  return true
}

// 处理文件移除
const handleFileRemove = () => {
  selectedFile.value = null
  form.name = ''
}

// 模拟进度更新
const updateProgress = () => {
  const steps = [
    { progress: 10, text: '正在上传文件...' },
    { progress: 30, text: '正在验证文件...' },
    { progress: 50, text: '正在备份当前数据...' },
    { progress: 70, text: '正在恢复数据...' },
    { progress: 90, text: '正在更新系统...' },
    { progress: 100, text: '恢复完成！' }
  ]
  
  let currentStep = 0
  
  const updateStep = () => {
    if (currentStep < steps.length && uploading.value) {
      const step = steps[currentStep]
      progress.value = step.progress
      progressText.value = step.text
      
      if (step.progress === 100) {
        progressStatus.value = 'success'
      }
      
      currentStep++
      
      if (currentStep < steps.length) {
        setTimeout(updateStep, 800 + Math.random() * 1200)
      }
    }
  }
  
  updateStep()
}

// 处理确认
const handleConfirm = async () => {
  if (!formRef.value || !selectedFile.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    // 二次确认
    await ElMessageBox.confirm(
      '此操作将完全替换当前系统数据，确定要继续吗？',
      '最终确认',
      {
        confirmButtonText: '确定恢复',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: '<p>⚠️ <strong>警告：此操作不可逆！</strong></p><p>建议在操作前先备份当前数据。</p>'
      }
    )
    
    uploading.value = true
    progressStatus.value = ''
    
    // 开始进度更新
    updateProgress()
    
    // 调用API上传并恢复
    await backupApi.uploadAndRestore(
      selectedFile.value,
      form.name,
      form.description || undefined
    )
    
    ElMessage.success('上传备份并恢复成功')
    emit('success')
    dialogVisible.value = false
    
  } catch (error) {
    if (error === 'cancel') return
    
    console.error('上传备份失败:', error)
    uploading.value = false
    progress.value = 0
    progressStatus.value = 'exception'
    progressText.value = '上传恢复失败'
    
    if (error && typeof error === 'object' && 'message' in error) {
      ElMessage.error(`上传备份失败: ${error.message}`)
    } else {
      ElMessage.error('上传备份失败')
    }
  }
}

// 处理取消
const handleCancel = () => {
  if (uploading.value) {
    ElMessage.warning('上传恢复正在进行中，无法取消')
    return
  }
  
  dialogVisible.value = false
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (timestamp: number) => {
  try {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return '-'
  }
}
</script>

<style scoped>
.upload-container {
  max-height: 70vh;
  overflow-y: auto;
}

.warning-alert {
  margin-bottom: 20px;
}

.warning-alert :deep(.el-alert__content) {
  line-height: 1.6;
}

.warning-alert p {
  margin: 4px 0;
}

.file-info {
  margin: 20px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.file-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.progress-container {
  margin: 20px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.progress-text {
  margin-top: 12px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-upload-dragger) {
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  transition: border-color 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}

:deep(.el-upload__text) {
  color: #606266;
  font-size: 14px;
}

:deep(.el-upload__text em) {
  color: #409eff;
  font-style: normal;
}

:deep(.el-upload__tip) {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}

:deep(.el-checkbox__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-descriptions) {
  margin-top: 12px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}
</style>
