import{I as t}from"./index-3d4c440c.js";async function o(o){return t({url:`/api/v1/quotations/${o}`,method:"GET"})}async function a(o){return t({url:"/api/v1/quotations",method:"post",data:o})}async function n(o,a){return t({url:`/api/v1/quotations/${o}`,method:"put",data:a})}async function e(o){return t({url:"/api/v1/quotations",method:"get",params:o})}async function r(o){return t({url:"/api/v1/quotation-requests",method:"get",params:o})}async function s(o){return t({url:`/api/v1/quotation-requests/${o}`,method:"get"})}async function u(o){return t({url:"/api/v1/quotation-requests",method:"post",data:o})}async function i(o,a){return t({url:`/api/v1/quotation-requests/${o}`,method:"put",data:a})}function c(o){return t({url:"/api/v1/quotation-requests/import",method:"post",data:o,headers:{"Content-Type":"multipart/form-data"}})}async function d(o,a={}){try{const n=await t({url:"/api/v1/orders",method:"post",data:{status:"draft",quotation_id:o,...a}});return console.log("创建订单响应:",n),n}catch(n){throw console.error("从报价单创建订单失败:",n),n}}function l(o,a){const n={};return a&&(a.format&&(n.format=a.format),void 0!==a.include_header&&(n.include_header=a.include_header),a.columns&&Array.isArray(a.columns)&&(n.columns=a.columns.join(","))),console.log("导出报价单参数:",n),t({url:`/api/v1/quotations/${o}/export`,method:"get",params:n,responseType:"blob",timeout:3e5,headers:{Accept:"application/octet-stream"}}).then((t=>{if(console.log("导出报价单响应:",t),t instanceof Blob||t.data instanceof Blob)return t instanceof Blob?t:t.data;throw console.error("导出报价单响应不是Blob类型:",t),new Error("服务器返回的不是有效的文件数据")}))}async function p(o,a){return console.log("[API] createQuotationFromRequest 请求参数:",{requestId:o,payload:a}),t({url:`/api/v1/quotation-requests/${o}/create-quotation`,method:"post",data:a})}async function m(o,a){return t({url:`/api/v1/quotations/${o}/status`,method:"put",data:{status:a}})}async function f(o){return t({url:`/api/v1/quotations/${o}`,method:"delete"})}export{a,n as b,d as c,f as d,l as e,r as f,o as g,p as h,c as i,s as j,i as k,e as l,u as m,m as u};
