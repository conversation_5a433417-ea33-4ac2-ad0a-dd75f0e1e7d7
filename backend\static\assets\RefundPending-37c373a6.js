import{b as e,r as a,L as l,e as r,f as t,M as o,o as n,c as u,i,h as d,U as s,a as c,k as p,l as f,N as _,t as v,Y as g}from"./index-3d4c440c.js";import{e as m,f as h}from"./finance-789ce3e6.js";import{_ as w}from"./_plugin-vue_export-helper-1b428a4d.js";const y={class:"refund-pending"},b={class:"card-header"},C={class:"left"},k={class:"right"},z={class:"table-container"},S={class:"pagination-container"},x={class:"dialog-footer"},V={class:"dialog-footer"},N=w({__name:"RefundPending",setup(w){const N=e(),U=a(!1),j=a(!1),$=a(!1),D=a(!1),I=a(!1),L=a(null),P=a([]),R=l({page:1,pageSize:20,total:0}),Y=async()=>{U.value=!0;try{const e={page:R.page,per_page:R.pageSize,status:"待处理"},a=await m(e);P.value=a.data,a.page_info&&(R.total=a.page_info.total||0)}catch(e){console.error("获取待处理退款记录失败:",e),s.error("获取待处理退款记录失败")}finally{U.value=!1}},F=e=>{Y()},M=e=>{R.pageSize=e,Y()},q=e=>{R.page=e,Y()},A=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN")},B=async()=>{if(L.value){D.value=!0;try{await h(L.value.id,"已退款"),s.success("退款已确认"),j.value=!1,Y()}catch(e){console.error("确认退款失败:",e),s.error("确认退款失败")}finally{D.value=!1}}},E=async()=>{if(L.value){I.value=!0;try{await h(L.value.id,"已取消"),s.success("退款已取消"),$.value=!1,Y()}catch(e){console.error("取消退款失败:",e),s.error("取消退款失败")}finally{I.value=!1}}},G=()=>{Y()};return r((()=>{Y()})),(e,a)=>{const l=t("el-tag"),r=t("el-icon"),s=t("el-button"),m=t("el-card"),h=t("el-table-column"),w=t("el-table"),Y=t("el-pagination"),H=t("el-dialog"),J=o("loading");return n(),u("div",y,[i(m,{class:"header-card mb-20"},{default:d((()=>[c("div",b,[c("div",C,[a[7]||(a[7]=c("h2",{class:"page-title"},"待处理退款",-1)),i(l,{type:"warning"},{default:d((()=>a[6]||(a[6]=[p("需要处理的退款申请")]))),_:1})]),c("div",k,[i(s,{type:"primary",onClick:G},{default:d((()=>[i(r,null,{default:d((()=>[i(f(g))])),_:1}),a[8]||(a[8]=p(" 刷新数据 "))])),_:1})])])])),_:1}),i(m,null,{default:d((()=>[_((n(),u("div",z,[i(w,{data:P.value,border:"",stripe:"",style:{width:"100%"},onSortChange:F},{default:d((()=>[i(h,{prop:"refund_no",label:"退款单号","min-width":"150",sortable:"custom",fixed:""},{default:d((e=>[i(s,{type:"primary",link:"",onClick:a=>{return l=e.row,void N.push(`/refund-record/edit/${l.id}?view=true`);var l}},{default:d((()=>[p(v(e.row.refund_no),1)])),_:2},1032,["onClick"])])),_:1}),i(h,{prop:"return_order_number",label:"退货单号","min-width":"150",sortable:"custom"},{default:d((e=>[i(s,{type:"success",link:"",onClick:a=>{return l=e.row.return_order_id,void N.push(`/return-orders/${l}`);var l}},{default:d((()=>[p(v(e.row.return_order_number),1)])),_:2},1032,["onClick"])])),_:1}),i(h,{prop:"order_number",label:"关联订单","min-width":"150"},{default:d((e=>[i(s,{type:"info",link:"",onClick:a=>{return l=e.row.order_id,void N.push(`/orders/detail/${l}`);var l}},{default:d((()=>[p(v(e.row.order_number),1)])),_:2},1032,["onClick"])])),_:1}),i(h,{prop:"customer_name",label:"客户名称","min-width":"200"},{default:d((e=>[i(s,{type:"info",link:"",onClick:a=>{return l=e.row.customer_id,void N.push(`/customers/detail/${l}`);var l}},{default:d((()=>[p(v(e.row.customer_name),1)])),_:2},1032,["onClick"])])),_:1}),i(h,{prop:"amount",label:"退款金额","min-width":"120",sortable:"custom"},{default:d((e=>{return[p(v((a=e.row.amount,null==a?"¥ 0.00":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY"}).format(a))),1)];var a})),_:1}),i(h,{prop:"refund_method",label:"退款方式",width:"120"},{default:d((e=>[i(l,null,{default:d((()=>{return[p(v((a=e.row.refund_method,{bank_transfer:"银行转账",cash:"现金",original_channel:"原支付渠道",other:"其他"}[a]||a)),1)];var a})),_:2},1024)])),_:1}),i(h,{prop:"refund_date",label:"申请日期",width:"120",sortable:"custom"},{default:d((e=>[p(v(A(e.row.refund_date)),1)])),_:1}),i(h,{label:"操作",width:"200",fixed:"right"},{default:d((e=>[i(s,{type:"success",size:"small",onClick:a=>{return l=e.row,L.value=l,void(j.value=!0);var l}},{default:d((()=>a[9]||(a[9]=[p("确认退款")]))),_:2},1032,["onClick"]),i(s,{type:"danger",size:"small",onClick:a=>{return l=e.row,L.value=l,void($.value=!0);var l}},{default:d((()=>a[10]||(a[10]=[p("取消退款")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),c("div",S,[i(Y,{"current-page":R.page,"onUpdate:currentPage":a[0]||(a[0]=e=>R.page=e),"page-size":R.pageSize,"onUpdate:pageSize":a[1]||(a[1]=e=>R.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:R.total,onSizeChange:M,onCurrentChange:q},null,8,["current-page","page-size","total"])])])),[[J,U.value]])])),_:1}),i(H,{modelValue:j.value,"onUpdate:modelValue":a[3]||(a[3]=e=>j.value=e),title:"确认退款",width:"30%"},{footer:d((()=>[c("span",x,[i(s,{onClick:a[2]||(a[2]=e=>j.value=!1)},{default:d((()=>a[11]||(a[11]=[p("取消")]))),_:1}),i(s,{type:"primary",onClick:B,loading:D.value},{default:d((()=>a[12]||(a[12]=[p("确认")]))),_:1},8,["loading"])])])),default:d((()=>[a[13]||(a[13]=c("p",null,"确认处理此笔退款吗？确认后将更新订单的已付款金额。",-1))])),_:1},8,["modelValue"]),i(H,{modelValue:$.value,"onUpdate:modelValue":a[5]||(a[5]=e=>$.value=e),title:"取消退款",width:"30%"},{footer:d((()=>[c("span",V,[i(s,{onClick:a[4]||(a[4]=e=>$.value=!1)},{default:d((()=>a[14]||(a[14]=[p("关闭")]))),_:1}),i(s,{type:"danger",onClick:E,loading:I.value},{default:d((()=>a[15]||(a[15]=[p("确认取消")]))),_:1},8,["loading"])])])),default:d((()=>[a[16]||(a[16]=c("p",null,"确认取消此笔退款申请吗？",-1))])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-a65c0f0c"]]);export{N as default};
