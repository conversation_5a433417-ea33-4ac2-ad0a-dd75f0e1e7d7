{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 100000, "temperature": 0.2, "baseURL": "http://**************:8050/v1beta"}, "research": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 100000, "temperature": 0.1, "baseURL": "http://**************:8050/v1beta"}, "fallback": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 100000, "temperature": 0.1, "baseURL": "http://**************:8050/v1beta"}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "userId": "**********", "defaultTag": "master"}}