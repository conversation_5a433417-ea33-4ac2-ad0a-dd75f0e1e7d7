"""
错误日志管理API
提供错误日志的记录、查询、删除等功能
基于原项目API实现，确保与现有系统100%兼容
"""
from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from datetime import datetime
import traceback

from app.models.error_log import ErrorLog
from app.schemas.error_log import ErrorLogSchema
from app.utils.response import (
    success_response,
    error_response,
    validation_error_response,
    not_found_response
)
from app.utils.pagination import PaginationHelper
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('error-logs', description='错误日志管理API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 自动从Marshmallow Schema生成API模型
error_log_model = create_input_model(api, ErrorLogSchema, 'ErrorLogInput')

# 手动定义的特殊模型（没有对应的Schema）
batch_delete_model = api.model('BatchDelete', {
    'ids': fields.List(fields.Integer, required=True, description='要删除的日志ID列表')
})

console_error_model = api.model('ConsoleError', {
    'message': fields.String(required=True, description='错误消息'),
    'stack': fields.String(description='堆栈信息'),
    'additional_info': fields.Raw(description='附加信息')
})


@api.route('')
class ErrorLogList(Resource):
    @api.doc('get_error_logs')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('type', '日志类型')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取错误日志列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            log_type = request.args.get('type', '').strip()
            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()
            
            # 构建查询
            query = ErrorLog.query
            
            # 应用过滤器
            if log_type:
                query = query.filter(ErrorLog.log_type == log_type)
            
            if start_date:
                try:
                    start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(ErrorLog.created_at >= start_datetime)
                except ValueError:
                    return make_response(error_response, "开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            if end_date:
                try:
                    end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                    # 设置结束日期为当天的最后一秒
                    end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                    query = query.filter(ErrorLog.created_at <= end_datetime)
                except ValueError:
                    return make_response(error_response, "结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            # 按创建时间倒序排序
            query = query.order_by(ErrorLog.created_at.desc())
            
            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query,
                serializer_func=lambda item: ErrorLogSchema().dump(item),
                page=page,
                per_page=per_page,
                message="获取错误日志列表成功"
            )
            
        except Exception as e:
            current_app.logger.error(f"获取错误日志列表失败: {str(e)}")
            return make_response(error_response, f"获取错误日志列表失败: {str(e)}")

    @api.doc('create_error_log',
             body=error_log_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建错误日志"""
        try:
            data = request.get_json() or {}
            
            # 处理前端可能发送的旧格式数据
            if 'level' in data and 'log_type' not in data:
                data['log_type'] = data.get('level')
            
            # 确保log_type存在，设置默认值为'general'
            if 'log_type' not in data and 'level' not in data:
                data['log_type'] = 'general'
                data['level'] = 'general'
            
            # 处理堆栈信息兼容性
            if 'stack' in data and 'stack_trace' not in data:
                data['stack_trace'] = data.pop('stack')
            
            # 处理details字段
            if 'details' in data and 'stack_trace' not in data:
                data['stack_trace'] = data.get('details')
            
            schema = ErrorLogSchema(exclude=('id', 'created_at', 'updated_at'))
            validated_data = schema.load(data)
            
            # 创建日志对象
            log = ErrorLog(**validated_data)
            
            # 确保level字段有值（兼容旧数据库结构）
            if not log.level and log.log_type:
                log.level = log.log_type
            
            db.session.add(log)
            db.session.commit()
            
            return make_response(success_response, ErrorLogSchema().dump(log), "错误日志创建成功", 201)
        
        except ValidationError as e:
            current_app.logger.error(f"错误日志验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建错误日志失败: {str(e)}")
            return make_response(error_response, f"创建错误日志失败: {str(e)}")

    @api.doc('batch_delete_error_logs',
             body=batch_delete_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def delete(self):
        """批量删除错误日志"""
        try:
            data = request.get_json() or {}
            log_ids = data.get('ids', [])
            
            if not log_ids:
                return make_response(error_response, "请提供要删除的日志ID列表", code=400)
            
            # 删除指定ID的日志
            deleted_count = ErrorLog.query.filter(ErrorLog.id.in_(log_ids)).delete(synchronize_session=False)
            db.session.commit()
            
            return make_response(success_response, {"deleted_count": deleted_count}, f"成功删除 {deleted_count} 条错误日志")
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"批量删除错误日志失败: {str(e)}")
            return make_response(error_response, f"批量删除错误日志失败: {str(e)}")


@api.route('/<int:log_id>')
class ErrorLogDetail(Resource):
    @api.doc('get_error_log')
    def get(self, log_id):
        """获取错误日志详情"""
        try:
            log = ErrorLog.query.get(log_id)
            if not log:
                return make_response(not_found_response, "错误日志不存在")
            
            log_data = ErrorLogSchema().dump(log)
            return make_response(success_response, log_data, "获取错误日志详情成功")
            
        except Exception as e:
            current_app.logger.error(f"获取错误日志详情失败: {str(e)}")
            return make_response(error_response, f"获取错误日志详情失败: {str(e)}")

    @api.doc('delete_error_log')
    def delete(self, log_id):
        """删除错误日志"""
        try:
            log = ErrorLog.query.get(log_id)
            if not log:
                return make_response(not_found_response, "错误日志不存在")
            
            db.session.delete(log)
            db.session.commit()
            
            return make_response(success_response, message="错误日志删除成功")
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除错误日志失败: {str(e)}")
            return make_response(error_response, f"删除错误日志失败: {str(e)}")


@api.route('/console-error')
class ConsoleError(Resource):
    @api.doc('log_console_error',
             body=console_error_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """记录前端控制台错误"""
        try:
            data = request.get_json() or {}
            
            error_message = data.get('message', '')
            log = ErrorLog(
                log_type='console_error',
                level='console_error',
                message=error_message,
                stack_trace=data.get('stack', ''),
                details=data.get('stack', ''),
                additional_info=data.get('additional_info', {})
            )
            
            db.session.add(log)
            db.session.commit()
            
            current_app.logger.error(f"console_error: {error_message}")
            
            return make_response(success_response, message="前端错误日志创建成功", code=201)
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"记录前端错误失败: {str(e)}")
            return make_response(error_response, f"记录前端错误失败: {str(e)}")


@api.route('/promise-unhandled')
class PromiseUnhandled(Resource):
    @api.doc('log_unhandled_promise',
             body=console_error_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """记录未处理的Promise错误"""
        try:
            data = request.get_json() or {}
            
            error_message = data.get('message', '')
            log = ErrorLog(
                log_type='promise_unhandled',
                level='promise_unhandled',
                message=error_message,
                stack_trace=data.get('stack', ''),
                details=data.get('stack', ''),
                additional_info=data.get('additional_info', {})
            )
            
            db.session.add(log)
            db.session.commit()
            
            current_app.logger.error(f"promise_unhandled: {error_message}")
            
            return make_response(success_response, message="未处理的Promise错误日志创建成功", code=201)
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"记录Promise错误失败: {str(e)}")
            return make_response(error_response, f"记录Promise错误失败: {str(e)}")


@api.route('/statistics')
class ErrorLogStatistics(Resource):
    @api.doc('get_error_log_statistics')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取错误日志统计信息"""
        try:
            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()
            
            query = ErrorLog.query
            
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(ErrorLog.created_at >= start)
                except ValueError:
                    return make_response(error_response, "开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            if end_date:
                try:
                    end = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(ErrorLog.created_at <= end)
                except ValueError:
                    return make_response(error_response, "结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            # 统计各类型错误数量
            type_stats = {}
            for log_type in ['general', 'console_error', 'promise_unhandled', 'api_error', 'database_error']:
                count = query.filter(ErrorLog.log_type == log_type).count()
                type_stats[log_type] = count
            
            # 总数统计
            total_count = query.count()
            
            statistics = {
                'total_count': total_count,
                'type_statistics': type_stats,
                'period': {
                    'start_date': start_date or '全部',
                    'end_date': end_date or '全部'
                }
            }
            
            return make_response(success_response, statistics, "获取错误日志统计信息成功")
            
        except Exception as e:
            current_app.logger.error(f"获取错误日志统计信息失败: {str(e)}")
            return make_response(error_response, f"获取错误日志统计信息失败: {str(e)}")
