import { ElMessage, ElNotification } from 'element-plus'
import type { AxiosError } from 'axios'

// 错误类型定义
export interface ApiError {
  code?: string | number
  message: string
  details?: any
}

// 业务错误类
export class BusinessError extends Error {
  code?: string | number
  details?: any

  constructor(message: string, code?: string | number, details?: any) {
    super(message)
    this.name = 'BusinessError'
    this.code = code
    this.details = details
  }
}

// 网络错误类
export class NetworkError extends Error {
  constructor(message: string = '网络连接失败') {
    super(message)
    this.name = 'NetworkError'
  }
}

// 权限错误类
export class AuthError extends Error {
  constructor(message: string = '权限不足') {
    super(message)
    this.name = 'AuthError'
  }
}

// 错误处理器
export class ErrorHandler {
  // 处理API错误
  static handleApiError(error: AxiosError | Error): void {
    if (error instanceof BusinessError) {
      ElMessage.error(error.message)
    } else if (error instanceof NetworkError) {
      ElNotification.error({
        title: '网络错误',
        message: error.message,
        duration: 5000,
      })
    } else if (error instanceof AuthError) {
      ElNotification.warning({
        title: '权限错误',
        message: error.message,
        duration: 5000,
      })
      // 可以在这里处理登录跳转
      // router.push('/login')
    } else {
      console.error('未知错误:', error)
      ElMessage.error('系统错误，请稍后重试')
    }
  }

  // 处理表单验证错误
  static handleValidationError(errors: Record<string, string[]>): void {
    const firstError = Object.values(errors)[0]?.[0]
    if (firstError) {
      ElMessage.error(firstError)
    }
  }

  // 处理文件上传错误
  static handleUploadError(error: any): void {
    if (error.response?.status === 413) {
      ElMessage.error('文件大小超出限制')
    } else if (error.response?.status === 415) {
      ElMessage.error('不支持的文件类型')
    } else {
      ElMessage.error('文件上传失败')
    }
  }

  // 全局错误处理
  static setupGlobalErrorHandler(): void {
    // 处理未捕获的Promise错误
    window.addEventListener('unhandledrejection', event => {
      console.error('未处理的Promise错误:', event.reason)
      this.handleApiError(event.reason)
      event.preventDefault()
    })

    // 处理JavaScript运行时错误
    window.addEventListener('error', event => {
      console.error('JavaScript错误:', event.error)
      ElMessage.error('页面出现错误，请刷新重试')
    })
  }
}

// 错误重试工具
export class RetryHandler {
  private static retryCount = new Map<string, number>()
  private static maxRetries = 3
  private static retryDelay = 1000

  // 带重试的请求
  static async withRetry<T>(
    requestFn: () => Promise<T>,
    key: string,
    maxRetries: number = this.maxRetries
  ): Promise<T> {
    const currentRetries = this.retryCount.get(key) || 0

    try {
      const result = await requestFn()
      // 成功后重置重试次数
      this.retryCount.delete(key)
      return result
    } catch (error) {
      if (currentRetries < maxRetries) {
        this.retryCount.set(key, currentRetries + 1)

        // 延迟后重试
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * (currentRetries + 1)))

        return this.withRetry(requestFn, key, maxRetries)
      } else {
        // 达到最大重试次数，清除计数并抛出错误
        this.retryCount.delete(key)
        throw error
      }
    }
  }

  // 清除重试计数
  static clearRetryCount(key?: string): void {
    if (key) {
      this.retryCount.delete(key)
    } else {
      this.retryCount.clear()
    }
  }
}

// 导出便捷方法
export const handleError = ErrorHandler.handleApiError
export const withRetry = RetryHandler.withRetry
