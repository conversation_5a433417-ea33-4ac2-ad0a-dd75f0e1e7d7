"""
系统管理API
提供系统设置、企业信息、银行账户、单据模板、备份记录和错误日志的管理
基于原项目API实现，确保与现有数据库结构100%兼容
"""
from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from sqlalchemy import desc
from typing import Dict, List, Optional, Any, Tuple
import os
import shutil
import json
import platform
import uuid
import mimetypes
from datetime import datetime, timedelta
from io import BytesIO
from werkzeug.utils import secure_filename

from app.models.system import SystemSetting, CompanyInfo, CompanyBankAccount, DocumentTemplate, BackupRecord
from app.models.error_log import ErrorLog
from app.schemas.system import (
    SystemSettingSchema,
    CompanyInfoSchema,
    CompanyBankAccountSchema,
    DocumentTemplateSchema,
    BackupRecordSchema,
    ErrorLogSchema,
    AutoBackupSettingsSchema
)
from app.utils.response import (
    success_response,
    error_response,
    paginated_response,
    validation_error_response,
    not_found_response
)
from app.utils.pagination import PaginationHelper
from app.utils.exceptions import ValidationError as CustomValidationError, NotFoundError
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('system', description='系统管理API')
from sqlalchemy import text


def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

def ensure_company_info_schema():
    """确保 company_info 表包含所有必要字段，若不存在则动态添加"""
    try:
        # 检查表是否存在
        result = db.session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='company_info'"))
        if not result.fetchone():
            # 表不存在，创建表
            db.session.execute(text("""
                CREATE TABLE company_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    tax_id VARCHAR(50) NOT NULL,
                    contact VARCHAR(50),
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    fax VARCHAR(20),
                    address VARCHAR(200),
                    logo VARCHAR(255),
                    license_image VARCHAR(255),
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """))
            db.session.commit()
            current_app.logger.info("创建 company_info 表成功")
            return

        # 表存在，检查并添加缺失的字段
        columns_to_check = [
            ('address', 'VARCHAR(200)'),
            ('logo', 'VARCHAR(255)'),
            ('license_image', 'VARCHAR(255)'),
            ('contact', 'VARCHAR(50)'),
            ('phone', 'VARCHAR(20)'),
            ('email', 'VARCHAR(100)'),
            ('fax', 'VARCHAR(20)')
        ]

        for column_name, column_type in columns_to_check:
            try:
                # 尝试查询该字段，如果失败说明字段不存在
                db.session.execute(text(f"SELECT {column_name} FROM company_info LIMIT 1"))
            except Exception:
                # 字段不存在，添加字段
                db.session.execute(text(f"ALTER TABLE company_info ADD COLUMN {column_name} {column_type}"))
                current_app.logger.info(f"添加字段 {column_name} 到 company_info 表")

        db.session.commit()
    except Exception as e:
        current_app.logger.error(f"确保 company_info 表结构失败: {str(e)}")
        db.session.rollback()
        # 不抛出异常，让业务逻辑继续

# 自动从Marshmallow Schema生成API模型
system_setting_model = create_input_model(api, SystemSettingSchema, 'SystemSettingInput')
company_info_model = create_input_model(api, CompanyInfoSchema, 'CompanyInfoInput')
bank_account_model = create_input_model(api, CompanyBankAccountSchema, 'CompanyBankAccountInput')
document_template_model = create_input_model(api, DocumentTemplateSchema, 'DocumentTemplateInput')

backup_record_model = api.model('BackupRecord', {
    'id': fields.Integer(description='备份记录ID'),
    'filename': fields.String(required=True, description='备份文件名'),
    'file_size': fields.Integer(required=True, description='文件大小'),
    'backup_type': fields.String(required=True, description='备份类型'),
    'description': fields.String(description='备份描述'),
    'created_at': fields.DateTime(description='创建时间')
})

error_log_model = api.model('ErrorLog', {
    'id': fields.Integer(description='日志ID'),
    'log_type': fields.String(description='日志类型'),
    'message': fields.String(required=True, description='错误消息'),
    'stack_trace': fields.String(description='堆栈跟踪'),
    'additional_info': fields.Raw(description='额外信息'),
    'created_at': fields.DateTime(description='创建时间')
})


# 系统设置相关API
@api.route('/settings')
class SystemSettingList(Resource):
    @api.doc('get_system_settings')
    def get(self):
        """获取系统设置列表"""
        try:
            settings = SystemSetting.query.all()
            setting_schema = SystemSettingSchema(many=True)
            result = setting_schema.dump(settings)
            return make_response(success_response, result, "获取系统设置成功")

        except Exception as e:
            current_app.logger.error(f"获取系统设置失败: {str(e)}")
            return make_response(error_response, f"获取系统设置失败: {str(e)}")

    @api.doc('batch_update_settings')
    def put(self):
        """批量更新系统设置"""
        try:
            data = request.get_json() or []

            if not isinstance(data, list):
                return make_response(error_response, "请求数据必须是一个数组", code=400)

            updated_settings = []

            for item in data:
                if 'key' not in item or 'value' not in item:
                    return make_response(error_response, "每个设置项必须包含 key 和 value", code=400)

                setting = SystemSetting.query.filter_by(key=item['key']).first()

                if not setting:
                    setting = SystemSetting(
                        key=item['key'],
                        value=item['value'],
                        description=item.get('description', '')
                    )
                    db.session.add(setting)
                else:
                    setting.value = item['value']
                    if 'description' in item:
                        setting.description = item['description']

                updated_settings.append(setting)

            db.session.commit()

            setting_schema = SystemSettingSchema(many=True)
            result = setting_schema.dump(updated_settings)

            return make_response(success_response, result, "批量更新设置成功")

        except Exception as e:
            current_app.logger.error(f"批量更新设置失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"批量更新设置失败: {str(e)}")


@api.route('/settings/<string:key>')
class SystemSettingDetail(Resource):
    @api.doc('get_system_setting')
    def get(self, key):
        """获取指定系统设置"""
        try:
            setting = SystemSetting.query.filter_by(key=key).first()
            if not setting:
                return make_response(not_found_response, f"设置 '{key}' 不存在")

            setting_schema = SystemSettingSchema()
            result = setting_schema.dump(setting)
            return make_response(success_response, result, "获取系统设置成功")

        except Exception as e:
            current_app.logger.error(f"获取系统设置失败: {str(e)}")
            return make_response(error_response, f"获取系统设置失败: {str(e)}")

    @api.doc('update_system_setting',
             body=system_setting_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Setting Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, key):
        """更新系统设置"""
        try:
            setting = SystemSetting.query.filter_by(key=key).first()

            if not setting:
                return make_response(not_found_response, f"设置 '{key}' 不存在")

            data = request.get_json() or {}

            if 'value' not in data:
                return make_response(error_response, "缺少必要的参数: value", code=400)

            setting.value = data['value']
            if 'description' in data:
                setting.description = data['description']

            db.session.commit()

            setting_schema = SystemSettingSchema()
            result = setting_schema.dump(setting)

            return make_response(success_response, result, "更新设置成功")

        except Exception as e:
            current_app.logger.error(f"更新设置失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新设置失败: {str(e)}")

    @api.doc('delete_system_setting')
    def delete(self, key):
        """删除系统设置"""
        try:
            setting = SystemSetting.query.filter_by(key=key).first()
            if not setting:
                return make_response(not_found_response, f"设置 '{key}' 不存在")

            db.session.delete(setting)
            db.session.commit()
            return make_response(success_response, message="删除系统设置成功")

        except Exception as e:
            current_app.logger.error(f"删除系统设置失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除系统设置失败: {str(e)}")


# 系统信息相关API
@api.route('/info')
class SystemInfo(Resource):
    @api.doc('get_system_info')
    def get(self):
        """获取系统信息"""
        try:
            system_info = {
                'os': platform.system(),
                'os_version': platform.version(),
                'python_version': platform.python_version(),
                'hostname': platform.node()
            }
            return make_response(success_response, system_info, "获取系统信息成功")

        except Exception as e:
            current_app.logger.error(f"获取系统信息失败: {str(e)}")
            return make_response(error_response, f"获取系统信息失败: {str(e)}")


@api.route('/app-config')
class AppConfig(Resource):
    @api.doc('get_app_config')
    def get(self):
        """获取应用配置"""
        try:
            # 仅返回安全的配置信息，不包含敏感信息
            safe_config = {
                'app_name': current_app.config.get('APP_NAME', '工程物资报价及订单管理系统'),
                'version': current_app.config.get('VERSION', '1.0.0'),
                'debug': current_app.config.get('DEBUG', False),
                'upload_folder': current_app.config.get('UPLOAD_FOLDER', 'uploads'),
                'allowed_extensions': current_app.config.get('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx']),
                'max_content_length': current_app.config.get('MAX_CONTENT_LENGTH', 10 * 1024 * 1024)
            }
            return make_response(success_response, safe_config, "获取应用配置成功")

        except Exception as e:
            current_app.logger.error(f"获取应用配置失败: {str(e)}")
            return make_response(error_response, f"获取应用配置失败: {str(e)}")


# 企业信息相关API
@api.route('/company-info')
class CompanyInfoResource(Resource):
    @api.doc('get_company_info')
    def get(self):
        """获取企业信息"""
        try:
            # 确保表结构正确
            ensure_company_info_schema()

            company_info = CompanyInfo.query.first()

            if not company_info:
                # 如果不存在企业信息，创建默认企业信息
                company_info = CompanyInfo(
                    name="默认公司名称",
                    tax_id="000000000000000000"
                )
                db.session.add(company_info)
                db.session.commit()

            company_info_schema = CompanyInfoSchema()
            result = company_info_schema.dump(company_info)

            return make_response(success_response, result, "获取企业信息成功")

        except Exception as e:
            current_app.logger.error(f"获取企业信息失败: {str(e)}")
            return make_response(error_response, f"获取企业信息失败: {str(e)}")

    @api.doc('update_company_info',
             body=company_info_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def put(self):
        """更新企业信息"""
        try:
            # 确保表结构正确
            ensure_company_info_schema()

            company_info = CompanyInfo.query.first()

            if not company_info:
                # 如果不存在企业信息，创建默认企业信息
                company_info = CompanyInfo(
                    name="默认公司名称",
                    tax_id="000000000000000000"
                )
                db.session.add(company_info)
                db.session.flush()

            data = request.get_json() or {}

            company_info_schema = CompanyInfoSchema()
            validated_data = company_info_schema.load(data, partial=True)

            for key, value in validated_data.items():
                setattr(company_info, key, value)

            db.session.commit()

            result = company_info_schema.dump(company_info)
            return make_response(success_response, result, "企业信息更新成功")

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新企业信息失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新企业信息失败: {str(e)}")


# 文件上传相关API
@api.route('/upload')
class SystemFileUpload(Resource):
    @api.doc('upload_system_file')
    def post(self):
        """系统文件上传接口"""
        try:
            # 检查是否有文件上传
            if 'file' not in request.files:
                return make_response(error_response, '未找到文件', code=400)

            file = request.files['file']

            # 如果用户未选择文件，浏览器也会提交一个空的文件部分
            if file.filename == '':
                return make_response(error_response, '未选择文件', code=400)

            # 允许的文件扩展名
            allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'docx', 'xlsx', 'txt', 'csv'}

            def allowed_file(filename):
                return '.' in filename and \
                       filename.rsplit('.', 1)[1].lower() in allowed_extensions

            if file and allowed_file(file.filename):
                # 生成安全的文件名
                filename = secure_filename(file.filename)
                # 使用UUID创建唯一文件名
                unique_filename = f"{uuid.uuid4().hex}_{filename}"

                # 确保上传目录存在
                upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
                if not os.path.isabs(upload_folder):
                    upload_folder = os.path.join(current_app.root_path, upload_folder)
                os.makedirs(upload_folder, exist_ok=True)

                # 保存文件
                file_path = os.path.join(upload_folder, unique_filename)
                file.save(file_path)

                # 获取文件信息
                file_size = os.path.getsize(file_path)
                file_type = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'

                # 构建文件URL路径
                file_url = f"/api/v1/uploads/files/{unique_filename}"

                response_data = {
                    'filename': unique_filename,
                    'original_filename': filename,
                    'url': file_url,
                    'size': file_size,
                    'type': file_type
                }

                return make_response(success_response, response_data, '文件上传成功', 201)
            else:
                allowed_types = ', '.join(allowed_extensions)
                return make_response(error_response, f'不允许的文件类型，支持的格式: {allowed_types}', code=400)

        except Exception as e:
            current_app.logger.error(f"文件上传失败: {str(e)}")
            return make_response(error_response, f'文件上传失败: {str(e)}', code=500)


# 企业银行账户相关API
@api.route('/company-bank-accounts')
class CompanyBankAccountList(Resource):
    @api.doc('get_company_bank_accounts')
    def get(self):
        """获取企业银行账户列表"""
        try:
            bank_accounts = CompanyBankAccount.query.all()
            bank_account_schema = CompanyBankAccountSchema(many=True)
            result = bank_account_schema.dump(bank_accounts)
            return make_response(success_response, result, "获取企业银行账户列表成功")

        except Exception as e:
            current_app.logger.error(f"获取企业银行账户列表失败: {str(e)}")
            return make_response(error_response, f"获取企业银行账户列表失败: {str(e)}")

    @api.doc('create_company_bank_account',
             body=bank_account_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建企业银行账户"""
        try:
            data = request.get_json() or {}

            bank_account_schema = CompanyBankAccountSchema()
            validated_data = bank_account_schema.load(data)

            # 如果设置为默认账户，则将其他账户设为非默认
            if validated_data.get('is_default', False):
                CompanyBankAccount.query.filter_by(is_default=True).update({'is_default': False})

            bank_account = CompanyBankAccount(**validated_data)
            db.session.add(bank_account)
            db.session.commit()

            result = bank_account_schema.dump(bank_account)
            return make_response(success_response, result, "创建企业银行账户成功", 201)

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建企业银行账户失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建企业银行账户失败: {str(e)}")


@api.route('/company-bank-accounts/<int:account_id>')
class CompanyBankAccountDetail(Resource):
    @api.doc('get_company_bank_account')
    def get(self, account_id):
        """获取企业银行账户详情"""
        try:
            bank_account = CompanyBankAccount.query.get(account_id)
            if not bank_account:
                return make_response(not_found_response, "企业银行账户不存在")

            bank_account_schema = CompanyBankAccountSchema()
            result = bank_account_schema.dump(bank_account)
            return make_response(success_response, result, "获取企业银行账户详情成功")

        except Exception as e:
            current_app.logger.error(f"获取企业银行账户详情失败: {str(e)}")
            return make_response(error_response, f"获取企业银行账户详情失败: {str(e)}")

    @api.doc('update_company_bank_account',
             body=bank_account_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Bank Account Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, account_id):
        """更新企业银行账户"""
        try:
            bank_account = CompanyBankAccount.query.get(account_id)
            if not bank_account:
                return make_response(not_found_response, "企业银行账户不存在")

            data = request.get_json() or {}

            bank_account_schema = CompanyBankAccountSchema()
            validated_data = bank_account_schema.load(data, partial=True)

            # 处理默认账户
            if validated_data.get('is_default') and not bank_account.is_default:
                CompanyBankAccount.query.filter_by(is_default=True).update({'is_default': False})

            for key, value in validated_data.items():
                setattr(bank_account, key, value)

            db.session.commit()

            result = bank_account_schema.dump(bank_account)
            return make_response(success_response, result, "更新企业银行账户成功")

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新企业银行账户失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新企业银行账户失败: {str(e)}")

    @api.doc('delete_company_bank_account')
    def delete(self, account_id):
        """删除企业银行账户"""
        try:
            bank_account = CompanyBankAccount.query.get(account_id)
            if not bank_account:
                return make_response(not_found_response, "企业银行账户不存在")

            db.session.delete(bank_account)
            db.session.commit()
            return make_response(success_response, message="删除企业银行账户成功")

        except Exception as e:
            current_app.logger.error(f"删除企业银行账户失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除企业银行账户失败: {str(e)}")


@api.route('/company-bank-accounts/<int:account_id>/set-default')
class CompanyBankAccountSetDefault(Resource):
    @api.doc('set_default_company_bank_account')
    def post(self, account_id):
        """设置企业银行账户为默认"""
        try:
            bank_account = CompanyBankAccount.query.get(account_id)
            if not bank_account:
                return make_response(not_found_response, "企业银行账户不存在")

            # 将其他账户设为非默认
            CompanyBankAccount.query.filter_by(is_default=True).update({'is_default': False})

            # 设置当前账户为默认
            bank_account.is_default = True
            db.session.commit()

            bank_account_schema = CompanyBankAccountSchema()
            result = bank_account_schema.dump(bank_account)
            return make_response(success_response, result, "设置默认账户成功")

        except Exception as e:
            current_app.logger.error(f"设置默认账户失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"设置默认账户失败: {str(e)}")


# 单据模板相关API
@api.route('/document-templates')
class DocumentTemplateList(Resource):
    @api.doc('get_document_templates')
    @api.param('type', '模板类型')
    def get(self):
        """获取单据模板列表"""
        try:
            template_type = request.args.get('type', '')

            query = DocumentTemplate.query

            if template_type:
                query = query.filter(DocumentTemplate.type == template_type)

            templates = query.all()

            template_schema = DocumentTemplateSchema(many=True)
            result = template_schema.dump(templates)

            return make_response(success_response, result, "获取单据模板列表成功")

        except Exception as e:
            current_app.logger.error(f"获取单据模板列表失败: {str(e)}")
            return make_response(error_response, f"获取单据模板列表失败: {str(e)}")

    @api.doc('create_document_template',
             body=document_template_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建单据模板"""
        try:
            data = request.get_json() or {}

            template_schema = DocumentTemplateSchema()
            validated_data = template_schema.load(data)

            # 如果设置为默认模板，取消同类型的其他默认模板
            if validated_data.get('is_default'):
                default_templates = DocumentTemplate.query.filter_by(
                    type=validated_data['type'],
                    is_default=True
                ).all()
                for template in default_templates:
                    template.is_default = False

            template = DocumentTemplate(**validated_data)
            db.session.add(template)
            db.session.commit()

            result = template_schema.dump(template)
            return make_response(success_response, result, "创建单据模板成功", 201)

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建单据模板失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建单据模板失败: {str(e)}")


@api.route('/document-templates/<int:template_id>')
class DocumentTemplateDetail(Resource):
    @api.doc('get_document_template')
    def get(self, template_id):
        """获取单据模板详情"""
        try:
            template = DocumentTemplate.query.get(template_id)
            if not template:
                return make_response(not_found_response, "单据模板不存在")

            template_schema = DocumentTemplateSchema()
            result = template_schema.dump(template)
            return make_response(success_response, result, "获取单据模板详情成功")

        except Exception as e:
            current_app.logger.error(f"获取单据模板详情失败: {str(e)}")
            return make_response(error_response, f"获取单据模板详情失败: {str(e)}")

    @api.doc('update_document_template',
             body=document_template_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Template Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, template_id):
        """更新单据模板"""
        try:
            template = DocumentTemplate.query.get(template_id)
            if not template:
                return make_response(not_found_response, "单据模板不存在")

            data = request.get_json() or {}

            template_schema = DocumentTemplateSchema()
            validated_data = template_schema.load(data, partial=True)

            # 如果设置为默认模板，取消同类型的其他默认模板
            if validated_data.get('is_default') and validated_data['is_default']:
                template_type = validated_data.get('type', template.type)
                default_templates = DocumentTemplate.query.filter(
                    DocumentTemplate.id != template_id,
                    DocumentTemplate.type == template_type,
                    DocumentTemplate.is_default == True
                ).all()
                for tpl in default_templates:
                    tpl.is_default = False

            for key, value in validated_data.items():
                setattr(template, key, value)

            db.session.commit()

            result = template_schema.dump(template)
            return make_response(success_response, result, "更新单据模板成功")

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新单据模板失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新单据模板失败: {str(e)}")

    @api.doc('delete_document_template')
    def delete(self, template_id):
        """删除单据模板"""
        try:
            template = DocumentTemplate.query.get(template_id)
            if not template:
                return make_response(not_found_response, "单据模板不存在")

            db.session.delete(template)
            db.session.commit()

            return make_response(success_response, message="删除单据模板成功")

        except Exception as e:
            current_app.logger.error(f"删除单据模板失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除单据模板失败: {str(e)}")


# 数据备份相关API
@api.route('/backup-records')
class BackupRecordList(Resource):
    @api.doc('get_backup_records')
    def get(self):
        """获取备份记录列表"""
        try:
            records = BackupRecord.query.order_by(BackupRecord.created_at.desc()).all()
            record_schema = BackupRecordSchema(many=True)
            result = record_schema.dump(records)
            return make_response(success_response, result, "获取备份记录列表成功")

        except Exception as e:
            current_app.logger.error(f"获取备份记录列表失败: {str(e)}")
            return make_response(error_response, f"获取备份记录列表失败: {str(e)}")


@api.route('/backups')
class BackupCreate(Resource):
    @api.doc('create_backup')
    def post(self):
        """创建数据库备份"""
        try:
            data = request.get_json() or {}
            description = data.get('description', '')

            # 确保备份目录存在
            backup_dir = os.path.join(current_app.instance_path, 'backups')
            os.makedirs(backup_dir, exist_ok=True)

            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            backup_filename = f"backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)

            # 找到正确的数据库文件
            db_files = [
                os.path.join(current_app.instance_path, 'project.db'),
                os.path.join(current_app.instance_path, 'app.db'),
                os.path.join(current_app.instance_path, 'dev_app.db'),
                os.path.join(current_app.instance_path, 'emb.sqlite'),
                'project.db',
                'app.db'
            ]

            source_db = None
            for db_file in db_files:
                if os.path.exists(db_file):
                    source_db = db_file
                    break

            if not source_db:
                # 如果找不到数据库文件，尝试使用SQLAlchemy获取数据库URI
                try:
                    db_uri = str(db.engine.url.database)
                    if db_uri and os.path.exists(db_uri):
                        source_db = db_uri
                except:
                    pass

            if not source_db:
                return make_response(error_response, "找不到数据库文件")

            current_app.logger.info(f"正在备份数据库: {source_db} 到 {backup_path}")

            # 创建备份
            shutil.copy2(source_db, backup_path)

            # 获取文件大小
            file_size = os.path.getsize(backup_path)

            # 记录备份信息
            backup_record = BackupRecord(
                filename=backup_filename,
                file_size=file_size,
                backup_type="手动",
                description=description
            )

            db.session.add(backup_record)
            db.session.commit()

            record_schema = BackupRecordSchema()
            result = record_schema.dump(backup_record)

            return make_response(success_response, result, "数据库备份成功", 201)

        except Exception as e:
            current_app.logger.error(f"数据库备份失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"数据库备份失败: {str(e)}")


@api.route('/backups/<int:backup_id>/restore')
class BackupRestore(Resource):
    @api.doc('restore_backup')
    def post(self, backup_id):
        """恢复数据库备份"""
        try:
            backup_record = BackupRecord.query.get(backup_id)
            if not backup_record:
                return make_response(not_found_response, "备份记录不存在")

            # 获取备份文件路径
            backup_dir = os.path.join(current_app.instance_path, 'backups')
            backup_path = os.path.join(backup_dir, backup_record.filename)

            # 检查备份文件是否存在
            if not os.path.exists(backup_path):
                return make_response(error_response, "备份文件不存在")

            # 找到当前使用的数据库文件
            db_files = [
                os.path.join(current_app.instance_path, 'project.db'),
                os.path.join(current_app.instance_path, 'app.db'),
                os.path.join(current_app.instance_path, 'dev_app.db'),
                'project.db',
                'app.db'
            ]

            current_db = None
            for db_file in db_files:
                if os.path.exists(db_file):
                    current_db = db_file
                    break

            if not current_db:
                return make_response(error_response, "找不到当前数据库文件")

            # 先创建当前数据库的临时备份
            temp_backup = f"{current_db}.{datetime.now().strftime('%Y%m%d%H%M%S')}.temp"
            shutil.copy2(current_db, temp_backup)

            try:
                # 关闭当前数据库连接
                db.session.remove()

                # 恢复备份
                shutil.copy2(backup_path, current_db)

                # 重新连接数据库
                db.create_all()

                return make_response(success_response, message="数据库恢复成功")

            except Exception as e:
                # 恢复失败，还原临时备份
                shutil.copy2(temp_backup, current_db)

                # 重新连接数据库
                db.create_all()

                raise e

            finally:
                # 删除临时备份
                if os.path.exists(temp_backup):
                    os.remove(temp_backup)

        except Exception as e:
            current_app.logger.error(f"数据库恢复失败: {str(e)}")
            return make_response(error_response, f"数据库恢复失败: {str(e)}")


@api.route('/backups/<int:backup_id>/download')
class BackupDownload(Resource):
    @api.doc('download_backup')
    def get(self, backup_id):
        """下载备份文件"""
        try:
            backup_record = BackupRecord.query.get(backup_id)
            if not backup_record:
                return make_response(not_found_response, "备份记录不存在")

            # 获取备份文件路径
            backup_dir = os.path.join(current_app.instance_path, 'backups')
            backup_path = os.path.join(backup_dir, backup_record.filename)

            # 检查备份文件是否存在
            if not os.path.exists(backup_path):
                return make_response(error_response, "备份文件不存在")

            return send_file(
                backup_path,
                as_attachment=True,
                download_name=backup_record.filename,
                mimetype='application/octet-stream'
            )

        except Exception as e:
            current_app.logger.error(f"下载备份文件失败: {str(e)}")
            return make_response(error_response, f"下载备份文件失败: {str(e)}")


@api.route('/backups/<int:backup_id>')
class BackupDetail(Resource):
    @api.doc('delete_backup')
    def delete(self, backup_id):
        """删除备份记录及文件"""
        try:
            backup_record = BackupRecord.query.get(backup_id)
            if not backup_record:
                return make_response(not_found_response, "备份记录不存在")

            # 获取备份文件路径
            backup_dir = os.path.join(current_app.instance_path, 'backups')
            backup_path = os.path.join(backup_dir, backup_record.filename)

            # 删除备份文件（如果存在）
            if os.path.exists(backup_path):
                os.remove(backup_path)

            # 删除备份记录
            db.session.delete(backup_record)
            db.session.commit()

            return make_response(success_response, message="备份记录删除成功")

        except Exception as e:
            current_app.logger.error(f"删除备份记录失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除备份记录失败: {str(e)}")


# 错误日志相关API
@api.route('/error-logs')
class ErrorLogList(Resource):
    @api.doc('get_error_logs')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('log_type', '日志类型')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取错误日志列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            log_type = request.args.get('log_type', '')
            start_date = request.args.get('start_date', '')
            end_date = request.args.get('end_date', '')

            query = ErrorLog.query

            # 应用过滤条件
            if log_type:
                query = query.filter(
                    (ErrorLog.log_type == log_type) | (ErrorLog.level == log_type)
                )

            if start_date:
                try:
                    start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(ErrorLog.created_at >= start_datetime)
                except ValueError:
                    return make_response(error_response, "开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            if end_date:
                try:
                    end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(ErrorLog.created_at <= end_datetime)
                except ValueError:
                    return make_response(error_response, "结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            # 排序
            query = query.order_by(ErrorLog.created_at.desc())

            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query,
                serializer_func=lambda item: ErrorLogSchema().dump(item),
                page=page,
                per_page=per_page,
                message="获取错误日志列表成功"
            )

        except Exception as e:
            current_app.logger.error(f"获取错误日志列表失败: {str(e)}")
            return make_response(error_response, f"获取错误日志列表失败: {str(e)}")

    @api.doc('clear_error_logs')
    def delete(self):
        """清空错误日志"""
        try:
            data = request.get_json() or {}
            days = data.get('days', 0)  # 保留最近几天的日志，0表示全部清空

            if days > 0:
                # 删除指定天数之前的日志
                cutoff_date = datetime.now() - timedelta(days=days)
                deleted_count = ErrorLog.query.filter(ErrorLog.created_at < cutoff_date).delete()
            else:
                # 删除所有日志
                deleted_count = ErrorLog.query.delete()

            db.session.commit()

            return make_response(success_response,
                               {'deleted_count': deleted_count},
                               f"成功清理了 {deleted_count} 条错误日志")

        except Exception as e:
            current_app.logger.error(f"清空错误日志失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"清空错误日志失败: {str(e)}")


@api.route('/error-logs/<int:log_id>')
class ErrorLogDetail(Resource):
    @api.doc('get_error_log')
    def get(self, log_id):
        """获取错误日志详情"""
        try:
            error_log = ErrorLog.query.get(log_id)
            if not error_log:
                return make_response(not_found_response, "错误日志不存在")

            error_log_schema = ErrorLogSchema()
            result = error_log_schema.dump(error_log)
            return make_response(success_response, result, "获取错误日志详情成功")

        except Exception as e:
            current_app.logger.error(f"获取错误日志详情失败: {str(e)}")
            return make_response(error_response, f"获取错误日志详情失败: {str(e)}")

    @api.doc('delete_error_log')
    def delete(self, log_id):
        """删除错误日志"""
        try:
            error_log = ErrorLog.query.get(log_id)
            if not error_log:
                return make_response(not_found_response, "错误日志不存在")

            db.session.delete(error_log)
            db.session.commit()
            return make_response(success_response, message="删除错误日志成功")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除错误日志失败: {str(e)}")
            return make_response(error_response, f"删除错误日志失败: {str(e)}")


@api.route('/auto-backup-settings')
class AutoBackupSettings(Resource):
    @api.doc('get_auto_backup_settings')
    def get(self):
        """获取自动备份设置"""
        try:
            # 获取自动备份相关设置
            backup_settings = SystemSetting.query.filter(
                SystemSetting.key.like('backup_%')
            ).all()

            settings_dict = {setting.key: setting.value for setting in backup_settings}

            # 默认设置
            default_settings = {
                'backup_enabled': settings_dict.get('backup_enabled', 'false'),
                'backup_frequency': settings_dict.get('backup_frequency', 'daily'),
                'backup_time': settings_dict.get('backup_time', '02:00'),
                'backup_retention_days': settings_dict.get('backup_retention_days', '30'),
                'backup_location': settings_dict.get('backup_location', './backups')
            }

            return make_response(success_response, default_settings, "获取自动备份设置成功")

        except Exception as e:
            current_app.logger.error(f"获取自动备份设置失败: {str(e)}")
            return make_response(error_response, f"获取自动备份设置失败: {str(e)}")

    @api.doc('update_auto_backup_settings')
    def put(self):
        """更新自动备份设置"""
        try:
            data = request.get_json() or {}

            # 更新备份设置
            backup_keys = ['backup_enabled', 'backup_frequency', 'backup_time',
                          'backup_retention_days', 'backup_location']

            for key in backup_keys:
                if key in data:
                    setting = SystemSetting.query.filter_by(key=key).first()
                    if setting:
                        setting.value = str(data[key])
                    else:
                        setting = SystemSetting(key=key, value=str(data[key]))
                        db.session.add(setting)

            db.session.commit()

            return make_response(success_response, message="更新自动备份设置成功")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新自动备份设置失败: {str(e)}")
            return make_response(error_response, f"更新自动备份设置失败: {str(e)}")


@api.route('/error-logs/stats')
class ErrorLogStats(Resource):
    @api.doc('get_error_log_stats')
    def get(self):
        """获取错误日志统计信息"""
        try:
            from sqlalchemy import func
            from datetime import timedelta

            # 总数统计
            total_count = ErrorLog.query.count()

            # 按类型统计
            type_stats = db.session.query(
                func.coalesce(ErrorLog.log_type, ErrorLog.level).label('type'),
                func.count(ErrorLog.id).label('count')
            ).group_by(func.coalesce(ErrorLog.log_type, ErrorLog.level)).all()

            # 最近7天统计
            seven_days_ago = datetime.now() - timedelta(days=7)
            recent_count = ErrorLog.query.filter(ErrorLog.created_at >= seven_days_ago).count()

            # 今日统计
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_count = ErrorLog.query.filter(ErrorLog.created_at >= today_start).count()

            stats = {
                'total_count': total_count,
                'recent_count': recent_count,
                'today_count': today_count,
                'type_stats': [{'type': stat.type or 'unknown', 'count': stat.count} for stat in type_stats]
            }

            return make_response(success_response, stats, "获取错误日志统计成功")

        except Exception as e:
            current_app.logger.error(f"获取错误日志统计失败: {str(e)}")
            return make_response(error_response, f"获取错误日志统计失败: {str(e)}")
