<template>
  <el-dialog
    v-model="dialogVisible"
    title="备份设置"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <!-- 自动备份开关 -->
      <el-form-item label="自动备份" prop="auto_backup_enabled">
        <el-switch
          v-model="form.auto_backup_enabled"
          size="large"
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-tip">
          启用后系统将按照设定的频率自动创建备份
        </div>
      </el-form-item>

      <!-- 备份频率 -->
      <el-form-item label="备份频率" prop="backup_frequency" v-if="form.auto_backup_enabled">
        <el-radio-group v-model="form.backup_frequency" size="large">
          <el-radio label="daily">每日备份</el-radio>
          <el-radio label="weekly">每周备份</el-radio>
          <el-radio label="monthly">每月备份</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 备份时间 -->
      <el-form-item label="备份时间" prop="backup_time" v-if="form.auto_backup_enabled">
        <el-time-picker
          v-model="backupTime"
          format="HH:mm:ss"
          value-format="HH:mm:ss"
          placeholder="选择备份时间"
          style="width: 200px"
        />
        <div class="form-tip">
          建议选择系统使用较少的时间段，如凌晨时间
        </div>
      </el-form-item>

      <!-- 备份日期（周备份或月备份时显示） -->
      <el-form-item 
        v-if="form.auto_backup_enabled && form.backup_frequency === 'weekly'"
        label="备份日期" 
        prop="backup_day"
      >
        <el-select v-model="form.backup_day" placeholder="选择星期几" style="width: 200px">
          <el-option label="星期一" :value="1" />
          <el-option label="星期二" :value="2" />
          <el-option label="星期三" :value="3" />
          <el-option label="星期四" :value="4" />
          <el-option label="星期五" :value="5" />
          <el-option label="星期六" :value="6" />
          <el-option label="星期日" :value="7" />
        </el-select>
      </el-form-item>

      <el-form-item 
        v-if="form.auto_backup_enabled && form.backup_frequency === 'monthly'"
        label="备份日期" 
        prop="backup_day"
      >
        <el-select v-model="form.backup_day" placeholder="选择几号" style="width: 200px">
          <el-option 
            v-for="day in 28" 
            :key="day" 
            :label="`${day}号`" 
            :value="day" 
          />
        </el-select>
        <div class="form-tip">
          为避免月份天数差异，建议选择28号之前的日期
        </div>
      </el-form-item>

      <!-- 保留数量 -->
      <el-form-item label="保留数量" prop="keep_count">
        <el-input-number
          v-model="form.keep_count"
          :min="1"
          :max="100"
          :step="1"
          style="width: 200px"
        />
        <div class="form-tip">
          系统将自动删除超出数量的旧备份，建议保留5-20个
        </div>
      </el-form-item>

      <!-- 备份路径 -->
      <el-form-item label="备份路径" prop="backup_path">
        <el-input
          v-model="form.backup_path"
          placeholder="备份文件存储路径"
          readonly
          style="width: 400px"
        />
        <div class="form-tip">
          备份文件的存储目录，由系统自动管理
        </div>
      </el-form-item>

      <!-- 上次备份时间 -->
      <el-form-item label="上次备份" v-if="form.last_backup_time">
        <el-tag type="info" size="large">
          {{ formatDateTime(form.last_backup_time) }}
        </el-tag>
      </el-form-item>
    </el-form>

    <!-- 自动备份状态说明 -->
    <el-card v-if="form.auto_backup_enabled" class="status-card">
      <template #header>
        <div class="status-header">
          <h4>自动备份计划</h4>
          <el-tag
            :type="schedulerStatus.scheduler_running ? 'success' : 'danger'"
            size="small"
          >
            {{ schedulerStatus.scheduler_running ? '运行中' : '已停止' }}
          </el-tag>
        </div>
      </template>
      <div class="backup-schedule">
        <p><strong>频率：</strong>{{ getFrequencyText() }}</p>
        <p><strong>时间：</strong>{{ form.backup_time || '未设置' }}</p>
        <p v-if="form.backup_day"><strong>日期：</strong>{{ getDayText() }}</p>
        <p><strong>保留：</strong>最近 {{ form.keep_count }} 个备份</p>
        <p><strong>路径：</strong>{{ form.backup_path }}</p>
        <p v-if="schedulerStatus.backup_job && schedulerStatus.backup_job.next_run_time">
          <strong>下次执行：</strong>{{ schedulerStatus.backup_job.next_run_time }}
        </p>
      </div>
    </el-card>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="saving">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSave" 
          :loading="saving"
        >
          保存设置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { backupApi } from '@/api/system'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const saving = ref(false)
const loading = ref(false)

// 表单数据
const form = reactive({
  auto_backup_enabled: false,
  backup_frequency: 'daily',
  backup_time: '02:00:00',
  backup_day: null as number | null,
  keep_count: 10,
  backup_path: '',
  last_backup_time: null as string | null
})

// 时间选择器的值
const backupTime = ref('')

// 调度器状态
const schedulerStatus = reactive({
  scheduler_running: false,
  backup_job: null,
  all_jobs: []
})

// 表单验证规则
const rules: FormRules = {
  backup_frequency: [
    { required: true, message: '请选择备份频率', trigger: 'change' }
  ],
  backup_time: [
    { required: true, message: '请选择备份时间', trigger: 'change' }
  ],
  backup_day: [
    { 
      validator: (rule, value, callback) => {
        if (form.auto_backup_enabled && 
            (form.backup_frequency === 'weekly' || form.backup_frequency === 'monthly') && 
            !value) {
          callback(new Error('请选择备份日期'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ],
  keep_count: [
    { required: true, message: '请设置保留数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '保留数量必须在1-100之间', trigger: 'blur' }
  ],
  backup_path: [
    { required: true, message: '备份路径不能为空', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    loadSettings()
  }
})

// 监听时间选择器变化
watch(backupTime, (newTime) => {
  form.backup_time = newTime
})

// 监听备份频率变化，重置备份日期
watch(() => form.backup_frequency, () => {
  form.backup_day = null
})

// 加载设置
const loadSettings = async () => {
  loading.value = true
  try {
    const response = await backupApi.getSettings() as any
    const data = response.data || response

    Object.assign(form, data)
    backupTime.value = form.backup_time

    // 加载调度器状态
    await loadSchedulerStatus()

  } catch (error) {
    console.error('加载备份设置失败:', error)
    ElMessage.error('加载备份设置失败')
  } finally {
    loading.value = false
  }
}

// 加载调度器状态
const loadSchedulerStatus = async () => {
  try {
    const response = await backupApi.getSchedulerStatus() as any
    const data = response.data || response
    Object.assign(schedulerStatus, data)
  } catch (error) {
    console.error('加载调度器状态失败:', error)
    // 不显示错误消息，因为这不是关键功能
  }
}

// 处理保存
const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    saving.value = true
    
    // 准备提交数据
    const submitData = { ...form }
    
    // 如果禁用自动备份，清除相关字段
    if (!form.auto_backup_enabled) {
      submitData.backup_day = null
    }
    
    // 调用API保存设置
    await backupApi.updateSettings(submitData)
    
    ElMessage.success('保存备份设置成功')

    // 重新加载调度器状态
    await loadSchedulerStatus()

    emit('success')
    dialogVisible.value = false
    
  } catch (error) {
    console.error('保存备份设置失败:', error)
    if (error && typeof error === 'object' && 'message' in error) {
      ElMessage.error(`保存失败: ${error.message}`)
    } else {
      ElMessage.error('保存备份设置失败')
    }
  } finally {
    saving.value = false
  }
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 获取频率文本
const getFrequencyText = () => {
  const frequencyMap = {
    'daily': '每日',
    'weekly': '每周',
    'monthly': '每月'
  }
  return frequencyMap[form.backup_frequency] || form.backup_frequency
}

// 获取日期文本
const getDayText = () => {
  if (!form.backup_day) return ''
  
  if (form.backup_frequency === 'weekly') {
    const dayMap = {
      1: '星期一', 2: '星期二', 3: '星期三', 4: '星期四',
      5: '星期五', 6: '星期六', 7: '星期日'
    }
    return dayMap[form.backup_day] || `星期${form.backup_day}`
  } else if (form.backup_frequency === 'monthly') {
    return `${form.backup_day}号`
  }
  
  return ''
}

// 格式化时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateStr
  }
}

// 组件挂载时加载设置
onMounted(() => {
  if (dialogVisible.value) {
    loadSettings()
  }
})
</script>

<style scoped>
.form-tip {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.status-card {
  margin-top: 20px;
  border-radius: 8px;
}

.status-card :deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-card h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.backup-schedule {
  padding: 4px 0;
}

.backup-schedule p {
  margin: 8px 0;
  color: #606266;
  line-height: 1.6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-switch__text) {
  font-weight: 500;
}

:deep(.el-radio__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 200px;
}

@media (max-width: 768px) {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .form-tip {
    margin-top: 8px;
  }
}
</style>
