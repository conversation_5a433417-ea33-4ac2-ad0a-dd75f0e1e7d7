import{J as e,u as t,b as a,r,d as l,L as d,e as o,f as u,o as n,c as s,i,h as c,a as _,g as p,k as m,j as f,t as v,l as y,U as b,W as g}from"./index-3d4c440c.js";import{b as h,h as w,c as $,d as k,e as N}from"./statement-2acbbb5b.js";import{b as A}from"./formatter-5775d610.js";import{_ as C}from"./_plugin-vue_export-helper-1b428a4d.js";const x={class:"statement-detail"},j={class:"card-header"},I={class:"header-actions"},U={class:"delivery-notes"},D={key:0,class:"return-orders"},R={class:"amount-summary"},V={class:"amount"},q={class:"dialog-footer"},L=C(e({__name:"StatementDetail",setup(e){const C=t(),L=a(),O=r({id:"",statement_number:"",customer_id:"",customer:{id:0,name:"",contact:"",phone:"",email:"",address:"",tax_id:"",created_at:"",updated_at:""},statement_date:"",due_date:null,total_amount:0,status:"draft",notes:null,created_at:"",updated_at:"",delivery_notes:[]}),T=r([]),Y=l((()=>O.value.delivery_notes.reduce(((e,t)=>e+Z(t)),0))),B=l((()=>T.value.reduce(((e,t)=>e+Q(t)),0))),J=l((()=>Y.value-B.value)),P=r(!1),S=r(),M=d({due_date:""}),E={due_date:[{required:!0,message:"请选择应付款日期",trigger:"change"}]},F=async()=>{try{const e=await h(C.params.id);O.value=e.data,await W()}catch(e){console.error("获取对账单详情失败:",e),b.error("获取对账单详情失败")}},W=async()=>{try{if(!O.value||!O.value.id)return void(T.value=[]);if(O.value.return_orders&&Array.isArray(O.value.return_orders))return T.value=O.value.return_orders,void console.log("从对账单数据中直接获取退货单:",T.value.length);console.log("尝试从API获取关联退货单");const e=await w(O.value.id);e&&e.data?(T.value=Array.isArray(e.data)?e.data:[],console.log("从API获取到关联退货单:",T.value.length)):T.value=[]}catch(e){console.error("获取退货单信息失败:",e),console.warn("由于API不存在或其他错误，无法获取退货单信息，这不会影响对账单主要功能"),T.value=[]}},z=()=>{L.push(`/statements/${C.params.id}/edit`)},G=()=>{O.value.due_date?M.due_date=O.value.due_date:M.due_date="",P.value=!0},H=async()=>{S.value&&await S.value.validate((async e=>{if(e)try{await $(C.params.id,{due_date:M.due_date}),b.success("确认对账单成功"),P.value=!1,await F()}catch(t){console.error("确认对账单失败:",t),b.error("确认对账单失败")}}))},K=()=>{g.confirm("确定要删除该对账单吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await k(C.params.id),b.success("删除成功"),L.push("/statements")}catch(e){console.error("删除对账单失败:",e),b.error("删除对账单失败")}}))},Q=e=>{if(e.total_amount)return Number(e.total_amount);let t=0;try{return console.log("计算退货单金额，退货单对象:",JSON.stringify(e)),e.items&&Array.isArray(e.items)&&(t=e.items.reduce(((t,a)=>{let r=0;const l=Number(a.quantity)||0;if(e.order&&e.order.products){a.product_specification_id||a.order_product_id;const t=e.order.products.filter((e=>e.id===a.order_product_id||e.product_specification_id===a.product_specification_id));if(t.length>0)r=Number(t[0].unit_price),console.log(`从订单产品中找到匹配的产品, 单价: ${r}`);else{const t=e.order.products.find((e=>e.product_name===a.product_name&&e.specification_description===a.specification_description));t&&(r=Number(t.unit_price),console.log(`通过产品名称和规格匹配到产品, 单价: ${r}`))}}if(!r){const e={"蝶阀":52,"单偏心对夹式蝶阀":65,"液压水位控制阀":5663,"旋流防止器":2570};a.product_name&&e[a.product_name]&&(r=e[a.product_name],console.log(`从硬编码映射中获取产品 ${a.product_name} 的单价: ${r}`))}const d=r*l;return console.log(`退货产品 ${a.product_name||"未知"}(${a.specification_description||""}) 行金额: ${d} = ${r} * ${l}`),t+d}),0)),console.log(`退货单 ${e.return_number||"未知"} 计算金额: ${t}`),t}catch(a){return console.error("计算退货单金额出错:",a),0}},X=e=>e?e.includes("T")?e.split("T")[0]:e:"",Z=e=>{if(e.total_amount)return Number(e.total_amount);if(e.net_amount)return Number(e.net_amount);let t=0;try{return console.log("计算发货单金额，发货单:",JSON.stringify(e)),e.items&&Array.isArray(e.items)&&(t=e.items.reduce(((t,a)=>{let r=0,l=Number(a.quantity)||0;if(a.unit_price)r=Number(a.unit_price);else if(a.amount&&a.quantity)r=Number(a.amount)/Number(a.quantity);else if(a.order_product&&a.order_product.unit_price)r=Number(a.order_product.unit_price);else{let t=a.order_product_id;if("object"==typeof a.order_product&&a.order_product&&(t=a.order_product.id||t),e.order&&e.order.products&&Array.isArray(e.order.products)){const d=e.order.products.find((e=>e.id===t||e.id===Number(a.order_product)));d&&d.unit_price&&(r=Number(d.unit_price),console.log(`找到产品 ${a.product_name||"未知"} 的单价: ${r}, 数量: ${l}`))}}const d=r*l;return console.log(`产品 ${a.product_name||"未知"}${a.specification_description?"("+a.specification_description+")":""} 行金额: ${d} = ${r} * ${l}`),t+d}),0)),console.log(`发货单 ${e.delivery_number} 计算金额: ${t}`),t}catch(a){return console.error("计算发货单金额出错:",a),0}},ee=()=>{L.back()},te=e=>({pending:"待发出",shipped:"已发出",completed:"已完成",cancelled:"已作废","待发出":"待发出","已发出":"已发出","已完成":"已完成","已作废":"已作废"}[e]||e),ae=e=>({pending:"待处理",processing:"处理中",completed:"已完成",cancelled:"已取消","待处理":"待处理","处理中":"处理中","已完成":"已完成","已取消":"已取消"}[e]||e),re=e=>({draft:"待确认",confirmed:"已确认",paid:"已付款",cancelled:"已取消","待确认":"待确认","已确认":"已确认","已完成":"已完成","已取消":"已取消"}[e]||e),le=async()=>{var e;if(null==(e=O.value)?void 0:e.id)try{const e=await N(O.value.id),t=window.URL.createObjectURL(new Blob([e.data])),a=document.createElement("a");a.href=t,a.setAttribute("download",`对账单_${O.value.statement_number}.xlsx`),document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(t),b.success("导出成功")}catch(t){console.error("导出失败:",t),b.error("导出失败")}};return o((()=>{F()})),(e,t)=>{const a=u("el-button"),r=u("el-descriptions-item"),l=u("el-tag"),d=u("el-descriptions"),o=u("el-table-column"),g=u("el-table"),h=u("el-card"),w=u("el-date-picker"),$=u("el-form-item"),k=u("el-form"),N=u("el-dialog");return n(),s("div",x,[i(h,null,{header:c((()=>[_("div",j,[t[8]||(t[8]=_("span",null,"对账单详情",-1)),_("div",I,["draft"===O.value.status?(n(),p(a,{key:0,type:"primary",onClick:z},{default:c((()=>t[3]||(t[3]=[m("编辑")]))),_:1})):f("",!0),"draft"===O.value.status?(n(),p(a,{key:1,type:"success",onClick:G},{default:c((()=>t[4]||(t[4]=[m("确认")]))),_:1})):f("",!0),"draft"===O.value.status?(n(),p(a,{key:2,type:"danger",onClick:K},{default:c((()=>t[5]||(t[5]=[m("删除")]))),_:1})):f("",!0),i(a,{type:"primary",onClick:le},{default:c((()=>t[6]||(t[6]=[m("打印")]))),_:1}),i(a,{onClick:ee},{default:c((()=>t[7]||(t[7]=[m("返回")]))),_:1})])])])),default:c((()=>[i(d,{title:"基本信息",column:2,border:""},{default:c((()=>[i(r,{label:"对账单号"},{default:c((()=>[m(v(O.value.statement_number),1)])),_:1}),i(r,{label:"客户名称"},{default:c((()=>{var e;return[m(v(null==(e=O.value.customer)?void 0:e.name),1)]})),_:1}),i(r,{label:"对账日期"},{default:c((()=>[m(v(O.value.statement_date),1)])),_:1}),i(r,{label:"状态"},{default:c((()=>{return[i(l,{type:(e=O.value.status,{draft:"warning",confirmed:"success",paid:"success",cancelled:"info","待确认":"warning","已确认":"success","已完成":"success","已取消":"info"}[e]||"info")},{default:c((()=>[m(v(re(O.value.status)),1)])),_:1},8,["type"])];var e})),_:1}),i(r,{label:"应付款日期"},{default:c((()=>[m(v(O.value.due_date),1)])),_:1}),i(r,{label:"总金额"},{default:c((()=>[m(v(y(A)(O.value.total_amount)),1)])),_:1}),i(r,{label:"备注",span:2},{default:c((()=>[m(v(O.value.notes),1)])),_:1})])),_:1}),_("div",U,[t[10]||(t[10]=_("h3",null,"发货单列表",-1)),i(g,{data:O.value.delivery_notes,border:"",style:{width:"100%"}},{default:c((()=>[i(o,{prop:"delivery_number",label:"发货单号",width:"180"}),i(o,{label:"发货日期",width:"120"},{default:c((({row:e})=>[m(v(X(e.delivery_date)),1)])),_:1}),i(o,{label:"金额",width:"120"},{default:c((({row:e})=>[m(v(y(A)(Z(e))),1)])),_:1}),i(o,{label:"状态",width:"100"},{default:c((({row:e})=>{return[i(l,{type:(t=e.status,{pending:"info",shipped:"warning",completed:"success",cancelled:"danger","待发出":"info","已发出":"warning","已完成":"success","已作废":"danger"}[t]||"info")},{default:c((()=>[m(v(te(e.status)),1)])),_:2},1032,["type"])];var t})),_:1}),i(o,{label:"操作",width:"120"},{default:c((({row:e})=>[i(a,{link:"",type:"primary",onClick:t=>(e=>{console.log("查看发货单:",e);let t=null;e&&e.id?t=e.id:e&&e.delivery_note_id&&(t=e.delivery_note_id),t?(console.log("跳转到发货单详情，ID:",t),L.push(`/delivery/${t}`)):b.warning("无法获取发货单ID")})(e)},{default:c((()=>t[9]||(t[9]=[m("查看")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])]),T.value.length>0?(n(),s("div",D,[t[12]||(t[12]=_("h3",null,"退货单列表",-1)),i(g,{data:T.value,border:"",style:{width:"100%"}},{default:c((()=>[i(o,{prop:"return_number",label:"退货单号",width:"180"}),i(o,{label:"退货日期",width:"120"},{default:c((({row:e})=>[m(v(X(e.return_date)),1)])),_:1}),i(o,{label:"退货金额",width:"120"},{default:c((({row:e})=>[m(v(y(A)(Q(e))),1)])),_:1}),i(o,{label:"状态",width:"100"},{default:c((({row:e})=>{return[i(l,{type:(t=e.status,{pending:"info",processing:"warning",completed:"success",cancelled:"danger","待处理":"info","处理中":"warning","已完成":"success","已取消":"danger"}[t]||"info")},{default:c((()=>[m(v(ae(e.status)),1)])),_:2},1032,["type"])];var t})),_:1}),i(o,{label:"操作",width:"120"},{default:c((({row:e})=>[i(a,{link:"",type:"primary",onClick:t=>(e=>{e&&e.id?(console.log("跳转到退货单详情，ID:",e.id),L.push(`/return-orders/${e.id}`)):b.warning("无法获取退货单ID")})(e)},{default:c((()=>t[11]||(t[11]=[m("查看")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])):f("",!0),_("div",R,[t[13]||(t[13]=_("h3",null,"金额核算",-1)),i(h,{shadow:"never"},{default:c((()=>[i(d,{column:1,border:""},{default:c((()=>[i(r,{label:"发货总金额"},{default:c((()=>[m(v(y(A)(Y.value)),1)])),_:1}),i(r,{label:"退货总金额"},{default:c((()=>[m(v(y(A)(B.value)),1)])),_:1}),i(r,{label:"结算总金额",class:"total-amount"},{default:c((()=>[_("span",V,v(y(A)(J.value)),1)])),_:1})])),_:1})])),_:1})])])),_:1}),i(N,{modelValue:P.value,"onUpdate:modelValue":t[2]||(t[2]=e=>P.value=e),title:"确认对账单",width:"500px"},{footer:c((()=>[_("span",q,[i(a,{onClick:t[1]||(t[1]=e=>P.value=!1)},{default:c((()=>t[14]||(t[14]=[m("取消")]))),_:1}),i(a,{type:"primary",onClick:H},{default:c((()=>t[15]||(t[15]=[m("确认")]))),_:1})])])),default:c((()=>[i(k,{ref_key:"confirmFormRef",ref:S,model:M,rules:E,"label-width":"100px"},{default:c((()=>[i($,{label:"应付款日期",prop:"due_date"},{default:c((()=>[i(w,{modelValue:M.due_date,"onUpdate:modelValue":t[0]||(t[0]=e=>M.due_date=e),type:"date",placeholder:"请选择应付款日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-177513df"]]);export{L as default};
