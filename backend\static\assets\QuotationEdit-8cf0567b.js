import{J as e,u as t,b as a,r as l,d as o,L as u,e as i,f as n,o as d,c as r,i as s,h as c,a as p,t as m,k as _,g as v,j as f,F as h,m as g,l as b,U as y,W as w,Z as V,a4 as x,X as q}from"./index-3d4c440c.js";import{a as E,b as j,g as k}from"./quotation-219e97aa.js";import{s as C}from"./customer-471ca075.js";import{l as U}from"./product-ab10366a.js";import{b as N,c as Q}from"./formatter-5775d610.js";import{c as I}from"./quotationTemplate-3cbc44ef.js";import{_ as $}from"./_plugin-vue_export-helper-1b428a4d.js";const A={class:"quotation-edit"},S={class:"flex-between"},P={class:"form-title"},T={class:"card-header flex-between"},D={class:"dialog-footer"},z=$(e({__name:"QuotationEdit",setup(e){const $=t(),z=a(),Y=l(null),B=l(null);l(null);const J=o((()=>!$.params.id)),M=o((()=>$.params.id?parseInt($.params.id,10):null)),O=l({id:null,quotation_number:"",customer_id:null,customer_name:"",project_name:"",project_address:"",valid_until:"",payment_terms:"",delivery_terms:"",status:"draft",notes:"",total_amount:0,items:[],request_id:null}),F=l([]),R={customer_id:[{required:!0,message:"请选择客户",trigger:"change"}],valid_until:[{required:!0,message:"请选择有效期至",trigger:"change"}],project_name:[{required:!0,message:"请输入项目名称",trigger:"blur"}],project_address:[{required:!0,message:"请输入项目地址",trigger:"blur"}]},W=l(!1),G=l([]),L=async e=>{var t;if(e){W.value=!0,console.log(`[QuotationEdit] 开始搜索客户: ${e}`);try{const a=await C({name_like:e,per_page:50});let l;console.log("[QuotationEdit] 收到API响应:",a),(null==(t=null==a?void 0:a.data)?void 0:t.items)&&Array.isArray(a.data.items)?(l=a.data.items,console.log("[QuotationEdit] 从 response.data.items 解析列表")):(null==a?void 0:a.data)&&Array.isArray(a.data)?(l=a.data,console.log("[QuotationEdit] 从 response.data 解析列表")):(null==a?void 0:a.items)&&Array.isArray(a.items)?(l=a.items,console.log("[QuotationEdit] 从 response.items 解析列表")):Array.isArray(a)&&(l=a,console.log("[QuotationEdit] 从 response (作为数组) 解析列表")),Array.isArray(l)?(console.log("[QuotationEdit] 客户列表有效, 准备映射:",l),G.value=l.map((e=>({label:e.name||`客户ID: ${e.id}`,value:e.id}))),console.log("[QuotationEdit] 客户选项更新完毕:",G.value)):(console.warn("[QuotationEdit] 无法从响应中解析出有效的客户列表，清空选项。响应:",a),G.value=[])}catch(a){console.error("[QuotationEdit] 搜索客户时发生严重错误:",a),G.value=[],y.error("搜索客户失败，请检查控制台日志。")}finally{W.value=!1,console.log("[QuotationEdit] 搜索流程结束")}}else G.value=[]};l(!1),l(-1),u({product_id:null,product_name:"",product_model:"",product_specification:"",unit:"",quantity:1,unit_price:0,discount:0,tax_rate:13,total_price:"0.00",notes:""}),l(!1),l([{value:1,label:"球阀",model:"Q-001",specification:"DN50",unit:"个",price:"220.00"},{value:2,label:"离心泵",model:"P-001",specification:"3kW",unit:"台",price:"3600.00"},{value:3,label:"不锈钢管",model:"G-304",specification:"DN25",unit:"米",price:"48.00"},{value:4,label:"压力表",model:"Y-100",specification:"0-1.6MPa",unit:"个",price:"120.00"}]);const X=l([]),Z=()=>{z.push("/quotations")},H=async()=>{var e,t,a;if(Y.value)try{if(await Y.value.validate()){if(0===F.value.length)return void y.warning("请至少添加一个报价项目");const e=F.value.map((e=>({product_id:e.product_id,product_specification_id:e.product_specification_id,quantity:Number(e.quantity),unit_price:Number(e.unit_price),discount:Number(e.discount),tax_rate:Number(e.tax_rate),notes:null===e.notes?void 0:e.notes,id:e.id}))),t=JSON.parse(JSON.stringify(O.value));if(t.valid_until){let e=t.valid_until;if(e.includes("T")&&(e=e.split("T")[0]),/^\d{4}-\d{2}-\d{2}$/.test(e))t.valid_until=e;else try{const e=new Date(t.valid_until);t.valid_until=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`}catch(l){return y.error("有效期日期格式不正确，无法自动转换。"),void console.error("Invalid date format for valid_until after attempting to parse:",t.valid_until)}}if(delete t.customer_name,delete t.customer,delete t.quotation_request,delete t.total_amount,delete t.quotation_number,delete t.created_at,delete t.updated_at,t.items=e,console.log("Cleaned data to save (QuotationEdit):",JSON.stringify(t,null,2)),J.value){const e={...t};delete e.id,await E(e),y.success("报价单创建成功")}else await j(M.value,t),y.success("报价单更新成功");Z()}else y.error("表单验证失败")}catch(o){console.error("保存报价单失败 (详细错误):",(null==(e=o.response)?void 0:e.data)||o.message||o),y.error(`保存报价单失败: ${(null==(a=null==(t=o.response)?void 0:t.data)?void 0:a.message)||o.message||"未知错误"}`)}},K=()=>{y.info("提交审批功能待实现")},ee=e=>{const t=Number(e.quantity)||0,a=Number(e.unit_price)||0,l=Number(e.discount)||0,o=Number(e.tax_rate)||0;let u=t*a;l>0&&l<=100&&(u*=l/100),o>0&&(u*=1+o/100),e.total_price=u,oe()},te=e=>{X.value=e},ae=()=>{0!==X.value.length?w.confirm(`确定要删除选中的 ${X.value.length} 个报价项目吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{const e=[];for(const t of F.value)X.value.some((e=>e.product_specification_id===t.product_specification_id&&e.id===t.id))||e.push(t);F.value=e,oe(),y.success("删除成功"),X.value=[]})).catch((()=>{})):y.warning("请选择要删除的项目")},le=e=>{const{columns:t,data:a}=e,l=[];return t.forEach(((e,t)=>{if(0===t)return void(l[t]="合计");const o=e.property;if(o&&["quantity","total_price"].includes(o)){const e=a.map((e=>{const t=Number(e[o]);return isNaN(t)?0:t})).reduce(((e,t)=>e+t),0);l[t]="total_price"===o?N(e):e.toString()}else l[t]=""})),l},oe=()=>{O.value.total_amount=F.value.reduce(((e,t)=>e+(Number(t.total_price)||0)),0)},ue=l(!1),ie=u({name:"",model:""}),ne=l([]),de=l(null),re=l([]),se=u({currentPage:1,pageSize:10,total:0}),ce=()=>{ie.name="",ie.model="",se.currentPage=1,ne.value=[],re.value=[],pe(),ue.value=!0},pe=async()=>{var e,t;try{const a={page:se.currentPage,per_page:se.pageSize,name:ie.name||void 0,model:ie.model||void 0,with_specifications:!0};Object.keys(a).forEach((e=>void 0===a[e]&&delete a[e]));const l=await U(a),o=[];(l.list||[]).forEach((e=>{e.specifications&&e.specifications.length>0&&e.specifications.forEach((t=>{o.push({productId:e.id,name:e.name,model:e.model,unit:e.unit,specId:t.id,specification:t.specification,suggested_price:t.suggested_price,tax_rate:t.tax_rate})}))})),ne.value=o,se.total=(null==(e=l.pagination)?void 0:e.total_items)??(null==(t=l.pagination)?void 0:t.total)??o.length}catch(a){console.error("搜索产品规格失败:",a),ne.value=[],se.total=0}},me=e=>{se.currentPage=e,pe()},_e=e=>{re.value=e},ve=()=>{re.value.length?(re.value.forEach((e=>{if(F.value.some((t=>t.product_specification_id===e.specId)))y.warning(`规格 "${e.specification}" 已存在于报价单中。`);else{const t={id:null,product_id:e.productId,product_specification_id:e.specId,name:e.name,model:e.model,specification:e.specification,unit:e.unit,quantity:1,unit_price:e.suggested_price||0,discount:0,tax_rate:e.tax_rate||0,total_price:0,notes:"",product_name_snapshot:e.name,product_model_snapshot:e.model,product_spec_snapshot:e.specification,product_unit_snapshot:e.unit};ee(t),F.value.push(t)}})),oe(),ue.value=!1,re.value=[],de.value&&de.value.clearSelection()):y.warning("请至少选择一个产品规格")},fe=l(!1);return i((async()=>{if(J.value){const e=$.query.template_id;e&&await(async e=>{fe.value=!0,y.info("正在从模板加载项目...");try{const t={},a=await I(e,t);O.value.id=a.id,O.value.quotation_number=a.quotation_number,O.value.customer_id=a.customer_id,O.value.project_name=a.project_name,O.value.valid_until=a.valid_until,O.value.status=a.status||"draft",O.value.notes=a.notes,O.value.project_address=a.project_address,O.value.payment_terms=a.payment_terms,O.value.delivery_terms=a.delivery_terms,O.value.request_id=a.request_id,O.value.customer=a.customer;const l=(a.items||[]).map((e=>({id:e.id,product_id:e.product_id,product_specification_id:e.product_specification_id,name:e.product_name_snapshot,model:e.product_model_snapshot||"",specification:e.product_spec_snapshot,unit:e.product_unit_snapshot,quantity:e.quantity,unit_price:e.unit_price,discount:e.discount,tax_rate:e.tax_rate,notes:e.notes||"",total_price:0,product_name_snapshot:e.product_name_snapshot,product_model_snapshot:e.product_model_snapshot,product_spec_snapshot:e.product_spec_snapshot,product_unit_snapshot:e.product_unit_snapshot})));l.forEach((e=>ee(e))),F.value=l,oe(),y.success("从模板加载项目成功！请检查并完善其他信息。")}catch(t){console.error("Failed to load quotation from template:",t),y.error("从模板加载项目失败。")}finally{fe.value=!1}})(Number(e)),O.value.status="draft"}else M.value&&await(async()=>{if(!J.value&&M.value)try{const e=await k(M.value);let t;if(console.log("[QuotationEdit.vue] 获取到报价单数据:",e),!e||"object"!=typeof e)throw new Error("无效的API响应");{const a=e;200!==a.code&&0!==a.code||!a.data?a.id?(t=e,console.log("[QuotationEdit.vue] 响应直接是数据对象")):(console.warn("[QuotationEdit.vue] 无法识别的API响应格式",e),t=e,y.warning("API响应格式不符合预期，数据可能不完整")):(t=a.data,console.log(`[QuotationEdit.vue] 使用标准API响应中的data字段，code=${a.code}`))}const a=(t.items||[]).map((e=>({id:e.id,product_id:e.product_id,product_specification_id:e.product_specification_id,name:e.product_name_snapshot,model:e.product_model_snapshot||"",specification:e.product_spec_snapshot,unit:e.product_unit_snapshot,quantity:Number(e.quantity||0),unit_price:Number(e.unit_price||0),discount:Number(e.discount||0),tax_rate:Number(e.tax_rate||0),total_price:0,notes:e.notes||"",product_name_snapshot:e.product_name_snapshot,product_model_snapshot:e.product_model_snapshot,product_spec_snapshot:e.product_spec_snapshot,product_unit_snapshot:e.product_unit_snapshot})));a.forEach((e=>ee(e))),F.value=a,O.value={...t,items:[],notes:t.notes||""},oe(),O.value.customer_id&&O.value.customer&&!G.value.some((e=>e.value===O.value.customer_id))&&G.value.unshift({label:O.value.customer.name||`客户ID: ${O.value.customer_id}`,value:O.value.customer_id}),console.log("[QuotationEdit.vue] 检查报价单是否有关联的报价需求表:",O.value.request_id),O.value.request_id&&y.info("此报价单来源于报价需求表，已加载相关信息")}catch(e){console.error("加载报价单失败:",e),y.error("加载报价单失败")}})()})),(e,t)=>{const a=n("el-button"),l=n("el-card"),o=n("el-input"),u=n("el-form-item"),i=n("el-col"),E=n("el-option"),j=n("el-select"),k=n("el-date-picker"),C=n("el-row"),U=n("el-icon"),I=n("el-table-column"),$=n("el-input-number"),z=n("el-table"),M=n("el-form"),re=n("el-pagination"),fe=n("el-dialog");return d(),r("div",A,[s(l,{class:"header-card mb-20"},{default:c((()=>[p("div",S,[p("h2",P,m(J.value?"新增报价单":"编辑报价单"),1),p("div",null,[s(a,{onClick:Z},{default:c((()=>t[12]||(t[12]=[_("返回")]))),_:1}),s(a,{type:"primary",onClick:H},{default:c((()=>t[13]||(t[13]=[_("保存")]))),_:1}),J.value||"draft"!==O.value.status?f("",!0):(d(),v(a,{key:0,type:"success",onClick:K},{default:c((()=>t[14]||(t[14]=[_("提交审批")]))),_:1}))])])])),_:1}),s(M,{ref_key:"formRef",ref:Y,model:O.value,rules:R,"label-width":"100px",class:"quotation-form"},{default:c((()=>[s(l,{class:"mb-20"},{header:c((()=>t[15]||(t[15]=[p("div",{class:"card-header"},[p("span",null,"基本信息")],-1)]))),default:c((()=>[s(C,{gutter:20},{default:c((()=>[s(i,{span:8},{default:c((()=>[s(u,{label:"报价单号",prop:"quotation_number"},{default:c((()=>[s(o,{modelValue:O.value.quotation_number,"onUpdate:modelValue":t[0]||(t[0]=e=>O.value.quotation_number=e),placeholder:"系统自动生成",disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),s(i,{span:8},{default:c((()=>[s(u,{label:"客户",prop:"customer_id"},{default:c((()=>[s(j,{modelValue:O.value.customer_id,"onUpdate:modelValue":t[1]||(t[1]=e=>O.value.customer_id=e),placeholder:"请选择客户",filterable:"",remote:"","remote-method":L,loading:W.value},{default:c((()=>[(d(!0),r(h,null,g(G.value,(e=>(d(),v(E,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})])),_:1}),s(i,{span:8},{default:c((()=>[s(u,{label:"有效期至",prop:"valid_until"},{default:c((()=>[s(k,{modelValue:O.value.valid_until,"onUpdate:modelValue":t[2]||(t[2]=e=>O.value.valid_until=e),type:"date",placeholder:"请选择有效期","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(C,{gutter:20},{default:c((()=>[s(i,{span:8},{default:c((()=>[s(u,{label:"项目名称",prop:"project_name"},{default:c((()=>[s(o,{modelValue:O.value.project_name,"onUpdate:modelValue":t[3]||(t[3]=e=>O.value.project_name=e),placeholder:"请输入项目名称"},null,8,["modelValue"])])),_:1})])),_:1}),s(i,{span:16},{default:c((()=>[s(u,{label:"项目地址",prop:"project_address"},{default:c((()=>[s(o,{modelValue:O.value.project_address,"onUpdate:modelValue":t[4]||(t[4]=e=>O.value.project_address=e),placeholder:"请输入项目地址"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(C,{gutter:20},{default:c((()=>[s(i,{span:12},{default:c((()=>[s(u,{label:"付款条件",prop:"payment_terms"},{default:c((()=>[s(o,{modelValue:O.value.payment_terms,"onUpdate:modelValue":t[5]||(t[5]=e=>O.value.payment_terms=e),placeholder:"请输入付款条件"},null,8,["modelValue"])])),_:1})])),_:1}),s(i,{span:12},{default:c((()=>[s(u,{label:"交货条件",prop:"delivery_terms"},{default:c((()=>[s(o,{modelValue:O.value.delivery_terms,"onUpdate:modelValue":t[6]||(t[6]=e=>O.value.delivery_terms=e),placeholder:"请输入交货条件"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(u,{label:"备注",prop:"notes"},{default:c((()=>[s(o,{modelValue:O.value.notes,"onUpdate:modelValue":t[7]||(t[7]=e=>O.value.notes=e),type:"textarea",rows:2,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1})])),_:1}),s(l,null,{header:c((()=>[p("div",T,[t[18]||(t[18]=p("span",null,"报价项目",-1)),p("div",null,[s(a,{type:"primary",onClick:ce},{default:c((()=>[s(U,null,{default:c((()=>[s(b(V))])),_:1}),t[16]||(t[16]=_(" 选择产品规格 "))])),_:1}),s(a,{type:"danger",disabled:!X.value.length,onClick:ae},{default:c((()=>[s(U,null,{default:c((()=>[s(b(x))])),_:1}),t[17]||(t[17]=_(" 删除选中 "))])),_:1},8,["disabled"])])])])),default:c((()=>[s(z,{ref_key:"itemsTable",ref:B,data:F.value,border:"","show-summary":"","summary-method":le,onSelectionChange:te},{default:c((()=>[s(I,{type:"selection",width:"55"}),s(I,{type:"index",label:"序号",width:"60"}),s(I,{prop:"name",label:"产品名称","min-width":"150","show-overflow-tooltip":""}),s(I,{prop:"model",label:"型号",width:"120"}),s(I,{prop:"specification",label:"规格",width:"150"}),s(I,{prop:"unit",label:"单位",width:"80"}),s(I,{prop:"quantity",label:"数量",width:"120"},{default:c((({row:e,$index:t})=>[s($,{modelValue:e.quantity,"onUpdate:modelValue":t=>e.quantity=t,min:1,precision:0,onChange:()=>ee(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),s(I,{prop:"unit_price",label:"单价",width:"120"},{default:c((({row:e,$index:t})=>[s($,{modelValue:e.unit_price,"onUpdate:modelValue":t=>e.unit_price=t,min:0,precision:2,step:.01,onChange:()=>ee(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),s(I,{prop:"discount",label:"折扣(%)",width:"100"},{default:c((({row:e,$index:t})=>[s($,{modelValue:e.discount,"onUpdate:modelValue":t=>e.discount=t,min:0,max:100,precision:2,step:.01,onChange:()=>ee(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),s(I,{prop:"tax_rate",label:"税率(%)",width:"100"},{default:c((({row:e,$index:t})=>[s($,{modelValue:e.tax_rate,"onUpdate:modelValue":t=>e.tax_rate=t,min:0,max:100,precision:2,step:.01,onChange:()=>ee(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),s(I,{prop:"total_price",label:"金额",width:"120"},{default:c((({row:e})=>[_(" ¥"+m(b(Q)(e.total_price)),1)])),_:1}),s(I,{prop:"notes",label:"备注","min-width":"150"},{default:c((({row:e})=>[s(o,{modelValue:e.notes,"onUpdate:modelValue":t=>e.notes=t,placeholder:"添加备注"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),s(I,{label:"操作",width:"80",fixed:"right"},{default:c((({$index:e})=>[s(a,{type:"danger",link:"",onClick:t=>{return a=e,void w.confirm("确定要删除该报价项目吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{F.value.splice(a,1),oe(),y.success("删除成功")})).catch((()=>{}));var a}},{default:c((()=>[s(U,null,{default:c((()=>[s(b(x))])),_:1}),t[19]||(t[19]=_(" 删除 "))])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1})])),_:1},8,["model"]),s(fe,{modelValue:ue.value,"onUpdate:modelValue":t[11]||(t[11]=e=>ue.value=e),title:"选择产品规格",width:"80%","append-to-body":"","destroy-on-close":""},{footer:c((()=>[p("span",D,[s(a,{onClick:t[10]||(t[10]=e=>ue.value=!1)},{default:c((()=>t[21]||(t[21]=[_("取消")]))),_:1}),s(a,{type:"primary",onClick:ve},{default:c((()=>t[22]||(t[22]=[_("确定添加")]))),_:1})])])),default:c((()=>[s(M,{inline:!0,model:ie,size:"small"},{default:c((()=>[s(u,{label:"产品名称"},{default:c((()=>[s(o,{modelValue:ie.name,"onUpdate:modelValue":t[8]||(t[8]=e=>ie.name=e),placeholder:"按名称搜索",clearable:""},null,8,["modelValue"])])),_:1}),s(u,{label:"产品型号"},{default:c((()=>[s(o,{modelValue:ie.model,"onUpdate:modelValue":t[9]||(t[9]=e=>ie.model=e),placeholder:"按型号搜索",clearable:""},null,8,["modelValue"])])),_:1}),s(u,null,{default:c((()=>[s(a,{type:"primary",icon:b(q),onClick:pe},{default:c((()=>t[20]||(t[20]=[_("搜索")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"]),s(z,{ref_key:"productSelectionTableRef",ref:de,data:ne.value,"max-height":"400px",border:"",stripe:"",onSelectionChange:_e,"row-key":"specId"},{default:c((()=>[s(I,{type:"selection",width:"45","reserve-selection":!0}),s(I,{prop:"name",label:"产品名称","min-width":"150"}),s(I,{prop:"model",label:"型号",width:"100"}),s(I,{prop:"specification",label:"规格","min-width":"80"}),s(I,{prop:"unit",label:"单位",width:"60"}),s(I,{prop:"suggested_price",label:"建议售价",width:"100"},{default:c((({row:e})=>[_(m(b(N)(e.suggested_price)),1)])),_:1}),s(I,{prop:"tax_rate",label:"税率(%)",width:"80"},{default:c((({row:e})=>[_(m(e.tax_rate),1)])),_:1})])),_:1},8,["data"]),se.total>0?(d(),v(re,{key:0,size:"small",layout:"prev, pager, next",total:se.total,"page-size":se.pageSize,"current-page":se.currentPage,onCurrentChange:me,class:"mt-10 flex-center"},null,8,["total","page-size","current-page"])):f("",!0)])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-8fa3a60b"]]);export{z as default};
