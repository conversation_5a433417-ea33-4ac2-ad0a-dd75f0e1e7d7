function a(a){var i,l,r,t;try{return console.log("适配器接收到的原始响应:",a),(null==(i=null==a?void 0:a.data)?void 0:i.items)&&(null==(l=null==a?void 0:a.data)?void 0:l.page_info)?a.data:Array.isArray(null==a?void 0:a.data)?{items:a.data,page_info:a.page||null}:(null==(r=null==a?void 0:a.data)?void 0:r.items)&&Array.isArray(a.data.items)?{items:a.data.items,page_info:a.data.page_info||a.page||null}:(null==(t=null==a?void 0:a.data)?void 0:t.list)&&Array.isArray(a.data.list)?{items:a.data.list,page_info:a.data.pagination||a.page||null}:(null==a?void 0:a.list)&&Array.isArray(a.list)?{items:a.list,page_info:a.pagination||null}:Array.isArray(a)?{items:a,page_info:null}:(console.warn("适配器无法识别的响应格式:",a),{items:[],page_info:null})}catch(n){return console.error("列表适配器处理失败:",n),{items:[],page_info:null}}}function i(a){var i;try{return console.log("树形数据适配器接收到的原始响应:",a),Array.isArray(null==a?void 0:a.data)?a.data:(null==(i=null==a?void 0:a.data)?void 0:i.items)&&Array.isArray(a.data.items)?a.data.items:(null==a?void 0:a.items)&&Array.isArray(a.items)?a.items:(null==a?void 0:a.list)&&Array.isArray(a.list)?a.list:Array.isArray(a)?a:(console.warn("树形数据适配器无法识别的响应格式:",a),[])}catch(l){return console.error("树形适配器处理失败:",l),[]}}export{a,i as b};
