<template>
  <div class="excel-test">
    <el-card>
      <template #header>
        <h3>Excel功能测试</h3>
      </template>
      
      <div class="test-section">
        <h4>下载模板测试</h4>
        <el-button type="primary" @click="downloadTemplate">
          <el-icon><Download /></el-icon>
          下载订单产品导入模板
        </el-button>
      </div>
      
      <div class="test-section">
        <h4>Excel导入测试</h4>
        <el-upload
          class="excel-uploader"
          action="#"
          :auto-upload="false"
          :on-change="handleExcelChange"
          :file-list="excelFileList"
          :limit="1"
          accept=".xlsx,.xls"
        >
          <el-button type="success">选择Excel文件</el-button>
        </el-upload>
        
        <el-button 
          type="warning" 
          @click="handleExcelImport" 
          :disabled="!excelFile"
          style="margin-left: 10px;"
        >
          解析Excel文件
        </el-button>
      </div>
      
      <div v-if="importedData.length > 0" class="test-section">
        <h4>导入结果</h4>
        <el-table :data="importedData" border>
          <el-table-column prop="product_name" label="产品名称" />
          <el-table-column prop="product_model" label="型号" />
          <el-table-column prop="specification" label="规格" />
          <el-table-column prop="unit" label="单位" />
          <el-table-column prop="quantity" label="数量" />
          <el-table-column prop="unit_price" label="单价" />
          <el-table-column prop="discount" label="折扣(%)" />
          <el-table-column prop="tax_rate" label="税率(%)" />
          <el-table-column prop="total_price" label="总价" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'

const excelFile = ref(null)
const excelFileList = ref([])
const importedData = ref([])

// 下载模板
const downloadTemplate = () => {
  const templateData = [
    ['产品名称*', '型号', '规格', '单位*', '数量*', '单价*', '折扣率(%)', '税率(%)', '备注'],
    ['示例产品A', 'MODEL-001', '规格A', '个', '10', '100.00', '5', '13', '这是示例备注'],
    ['示例产品B', 'MODEL-002', '规格B', '台', '2', '500.00', '10', '13', '']
  ]
  
  const worksheet = XLSX.utils.aoa_to_sheet(templateData)
  worksheet['!cols'] = [
    { wch: 20 }, // 产品名称
    { wch: 15 }, // 型号
    { wch: 20 }, // 规格
    { wch: 8 },  // 单位
    { wch: 8 },  // 数量
    { wch: 10 }, // 单价
    { wch: 12 }, // 折扣率
    { wch: 12 }, // 税率
    { wch: 30 }  // 备注
  ]
  
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, '产品明细模板')
  XLSX.writeFile(workbook, '订单产品导入模板.xlsx')
  ElMessage.success('模板文件已开始下载')
}

// Excel文件变化
const handleExcelChange = (file: any) => {
  excelFile.value = file.raw
}

// Excel导入处理
const handleExcelImport = () => {
  if (!excelFile.value) {
    ElMessage.warning('请先选择一个Excel文件')
    return
  }

  const reader = new FileReader()
  reader.onload = (e: ProgressEvent<FileReader>) => {
    try {
      const data = e.target?.result
      const workbook = XLSX.read(data, { type: 'binary' })
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

      if (jsonData.length < 2) {
        ElMessage.error('Excel文件内容为空或表头不正确')
        return
      }

      const header = jsonData[0].map(String)
      const columnMap: Record<string, string> = {
        '产品名称*': 'product_name',
        '型号': 'product_model',
        '规格': 'specification',
        '单位*': 'unit',
        '数量*': 'quantity',
        '单价*': 'unit_price',
        '折扣率(%)': 'discount',
        '税率(%)': 'tax_rate',
        '备注': 'notes'
      }

      const results: any[] = []

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i]
        if (!row || row.every(cell => cell === null || cell === undefined || String(cell).trim() === '')) {
          continue
        }

        const productItem: any = {}

        header.forEach((colName, index) => {
          const fieldName = columnMap[colName]
          if (fieldName) {
            let cellValue = row[index]
            if (['quantity', 'unit_price', 'discount', 'tax_rate'].includes(fieldName)) {
              cellValue = parseFloat(String(cellValue))
              if (isNaN(cellValue)) {
                cellValue = fieldName === 'discount' ? 0 : fieldName === 'tax_rate' ? 13 : undefined
              }
            }
            productItem[fieldName] = cellValue
          }
        })

        // 验证必填字段
        if (!productItem.product_name || !productItem.unit || 
            productItem.quantity === undefined || productItem.unit_price === undefined) {
          ElMessage.warning(`第 ${i + 1} 行数据不完整，已跳过`)
          continue
        }

        // 设置默认值
        productItem.product_model = productItem.product_model || ''
        productItem.specification = productItem.specification || ''
        productItem.discount = productItem.discount || 0
        productItem.tax_rate = productItem.tax_rate || 13
        productItem.notes = productItem.notes || ''

        // 计算总价
        const subtotal = productItem.quantity * productItem.unit_price
        const discountAmount = subtotal * (productItem.discount / 100)
        const afterDiscount = subtotal - discountAmount
        const taxAmount = afterDiscount * (productItem.tax_rate / 100)
        productItem.total_price = afterDiscount + taxAmount

        results.push(productItem)
      }

      importedData.value = results
      ElMessage.success(`成功解析 ${results.length} 个产品`)
    } catch (error) {
      console.error('Excel导入失败:', error)
      ElMessage.error('Excel文件解析失败，请检查文件格式')
    }
  }

  reader.readAsBinaryString(excelFile.value)
}
</script>

<style lang="scss" scoped>
.excel-test {
  padding: 20px;
  
  .test-section {
    margin-bottom: 30px;
    
    h4 {
      margin-bottom: 15px;
      color: #409eff;
    }
  }
}
</style>
