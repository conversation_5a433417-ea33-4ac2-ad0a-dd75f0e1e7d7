<template>
  <el-dialog
    v-model="dialogVisible"
    title="确认数据恢复"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div v-if="backup" class="restore-confirm">
      <!-- 警告提示 -->
      <el-alert
        title="重要警告"
        type="error"
        :closable="false"
        show-icon
        class="warning-alert"
      >
        <template #default>
          <p><strong>此操作将完全替换当前系统数据，无法撤销！</strong></p>
          <p>建议在恢复前先创建当前数据的备份。</p>
        </template>
      </el-alert>

      <!-- 备份信息 -->
      <div class="backup-info">
        <h4>即将恢复的备份信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="备份名称" :span="2">
            <el-tag size="large" type="primary">{{ backup.name }}</el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="备份类型">
            <el-tag :type="backup.backup_type === 'manual' ? 'primary' : 'success'">
              {{ backup.backup_type === 'manual' ? '手动备份' : '自动备份' }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="文件大小">
            {{ formatFileSize(backup.file_size) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(backup.created_at) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="完成时间">
            {{ formatDateTime(backup.completed_at) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="备份描述" :span="2" v-if="backup.description">
            <div class="description-content">
              {{ backup.description }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 恢复选项 -->
      <div class="restore-options">
        <h4>恢复选项</h4>
        <el-checkbox v-model="options.createBackupBefore" size="large">
          恢复前自动创建当前数据备份
        </el-checkbox>
        <p class="option-desc">
          强烈建议开启此选项，以便在恢复失败时能够回滚到当前状态。
        </p>
      </div>

      <!-- 确认复选框 -->
      <div class="confirm-section">
        <el-checkbox v-model="confirmed" size="large">
          <span class="confirm-text">
            我已了解风险，确认要从此备份恢复数据
          </span>
        </el-checkbox>
      </div>

      <!-- 进度显示 -->
      <div v-if="restoring" class="progress-container">
        <el-progress
          :percentage="progress"
          :status="progressStatus"
          :stroke-width="8"
          text-inside
        />
        <p class="progress-text">{{ progressText }}</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="restoring">取消</el-button>
        <el-button 
          type="danger" 
          @click="handleConfirm" 
          :loading="restoring"
          :disabled="restoring || !confirmed"
        >
          {{ restoring ? '恢复中...' : '确认恢复' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { backupApi } from '@/api/system'

// Props
interface Props {
  modelValue: boolean
  backup: any
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const restoring = ref(false)
const confirmed = ref(false)
const progress = ref(0)
const progressStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const progressText = ref('')

// 恢复选项
const options = reactive({
  createBackupBefore: true
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  confirmed.value = false
  restoring.value = false
  progress.value = 0
  progressStatus.value = ''
  progressText.value = ''
  options.createBackupBefore = true
}

// 模拟进度更新
const updateProgress = () => {
  const steps = options.createBackupBefore ? [
    { progress: 10, text: '正在创建当前数据备份...' },
    { progress: 30, text: '正在验证备份文件...' },
    { progress: 50, text: '正在停止相关服务...' },
    { progress: 70, text: '正在恢复数据库...' },
    { progress: 90, text: '正在重启服务...' },
    { progress: 100, text: '恢复完成！' }
  ] : [
    { progress: 20, text: '正在验证备份文件...' },
    { progress: 40, text: '正在停止相关服务...' },
    { progress: 70, text: '正在恢复数据库...' },
    { progress: 90, text: '正在重启服务...' },
    { progress: 100, text: '恢复完成！' }
  ]
  
  let currentStep = 0
  
  const updateStep = () => {
    if (currentStep < steps.length && restoring.value) {
      const step = steps[currentStep]
      progress.value = step.progress
      progressText.value = step.text
      
      if (step.progress === 100) {
        progressStatus.value = 'success'
      }
      
      currentStep++
      
      if (currentStep < steps.length) {
        setTimeout(updateStep, 1000 + Math.random() * 1500)
      }
    }
  }
  
  updateStep()
}

// 处理确认
const handleConfirm = async () => {
  if (!props.backup) return
  
  try {
    // 最终确认
    await ElMessageBox.confirm(
      '最后确认：此操作将完全替换当前系统数据，确定要继续吗？',
      '最终确认',
      {
        confirmButtonText: '确定恢复',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true,
        message: '<p>⚠️ <strong>警告：此操作不可逆！</strong></p>'
      }
    )
    
    restoring.value = true
    progressStatus.value = ''
    
    // 开始进度更新
    updateProgress()
    
    // 如果需要，先创建当前数据备份
    if (options.createBackupBefore) {
      try {
        await backupApi.createBackup({
          name: `恢复前自动备份_${new Date().toLocaleString('zh-CN').replace(/[/:]/g, '-')}`,
          description: `恢复备份"${props.backup.name}"前的自动备份`
        })
      } catch (error) {
        console.warn('创建恢复前备份失败:', error)
        // 继续恢复流程，不中断
      }
    }
    
    // 调用API恢复数据
    await backupApi.restoreData(props.backup.id)
    
    ElMessage.success('数据恢复成功')
    emit('confirm')
    dialogVisible.value = false
    
  } catch (error) {
    if (error === 'cancel') return
    
    console.error('数据恢复失败:', error)
    restoring.value = false
    progress.value = 0
    progressStatus.value = 'exception'
    progressText.value = '恢复失败'
    
    if (error && typeof error === 'object' && 'message' in error) {
      ElMessage.error(`数据恢复失败: ${error.message}`)
    } else {
      ElMessage.error('数据恢复失败')
    }
  }
}

// 处理取消
const handleCancel = () => {
  if (restoring.value) {
    ElMessage.warning('数据恢复正在进行中，无法取消')
    return
  }
  
  dialogVisible.value = false
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (!bytes || bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return dateStr
  }
}
</script>

<style scoped>
.restore-confirm {
  max-height: 70vh;
  overflow-y: auto;
}

.warning-alert {
  margin-bottom: 20px;
}

.warning-alert :deep(.el-alert__content) {
  line-height: 1.6;
}

.warning-alert p {
  margin: 4px 0;
}

.backup-info {
  margin: 20px 0;
}

.backup-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.description-content {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  color: #606266;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.restore-options {
  margin: 20px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.restore-options h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.option-desc {
  margin: 8px 0 0 24px;
  color: #909399;
  font-size: 13px;
  line-height: 1.5;
}

.confirm-section {
  margin: 20px 0;
  padding: 16px;
  background-color: #fff2e8;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
}

.confirm-text {
  font-weight: 600;
  color: #721c24;
}

.progress-container {
  margin: 20px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.progress-text {
  margin-top: 12px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
  background-color: #fafafa;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

:deep(.el-checkbox__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-progress-bar__outer) {
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
}
</style>
