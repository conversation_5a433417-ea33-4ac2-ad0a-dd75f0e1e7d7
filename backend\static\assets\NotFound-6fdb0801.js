import{J as t,b as e,f as a,o as s,c as o,i as r,h as n,k as u}from"./index-3d4c440c.js";import{_ as i}from"./_plugin-vue_export-helper-1b428a4d.js";const l={class:"not-found-container"},c=i(t({__name:"NotFound",setup(t){const i=e(),c=()=>{i.back()},p=()=>{i.push("/")};return(t,e)=>{const i=a("el-button"),d=a("el-result");return s(),o("div",l,[r(d,{icon:"error",title:"404 - 页面未找到","sub-title":"抱歉，您访问的页面不存在。"},{extra:n((()=>[r(i,{type:"primary",onClick:c},{default:n((()=>e[0]||(e[0]=[u("返回上一页")]))),_:1}),r(i,{onClick:p},{default:n((()=>e[1]||(e[1]=[u("返回首页")]))),_:1})])),_:1})])}}}),[["__scopeId","data-v-a1e5ed81"]]);export{c as default};
