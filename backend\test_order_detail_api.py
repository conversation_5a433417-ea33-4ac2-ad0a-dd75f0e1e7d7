#!/usr/bin/env python3
"""
测试订单详情API，检查发货单和退货单的结清状态
"""

import requests
import json

def test_order_detail_api():
    """测试订单详情API"""
    print("🧪 测试订单详情API")
    print("=" * 50)
    
    try:
        # 测试订单ID=5的详情
        url = "http://localhost:5001/api/v1/orders/5"
        response = requests.get(url)
        
        print(f"📡 API调用: {url}")
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            
            order_data = data.get('data', {})
            print(f"\n📋 订单信息:")
            print(f"   订单号: {order_data.get('order_number')}")
            print(f"   客户: {order_data.get('customer', {}).get('name')}")
            
            # 检查发货单信息
            delivery_notes = order_data.get('delivery_notes', [])
            print(f"\n🚚 发货单信息 ({len(delivery_notes)} 个):")
            for i, dn in enumerate(delivery_notes, 1):
                print(f"   {i}. 发货单号: {dn.get('delivery_number')}")
                print(f"      状态: {dn.get('status')}")
                print(f"      结清状态: {dn.get('settlement_status', '未知')}")
                print(f"      金额: ¥{dn.get('total_amount', 0):,.2f}")
                print(f"      完整数据: {dn}")
                print()
            
            # 检查退货单信息
            return_orders = order_data.get('return_orders', [])
            print(f"🔄 退货单信息 ({len(return_orders)} 个):")
            for i, ro in enumerate(return_orders, 1):
                print(f"   {i}. 退货单号: {ro.get('return_number')}")
                print(f"      状态: {ro.get('status')}")
                print(f"      结清状态: {ro.get('settlement_status', '未知')}")
                print(f"      金额: ¥{ro.get('total_amount', 0):,.2f}")
                print()
                
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == '__main__':
    test_order_detail_api()
