"""
测试配置文件
提供测试夹具和配置
"""
import pytest
import tempfile
import os
from app import create_app, db
from app.models.customer import Customer
from app.models.product import Product, ProductCategory, ProductSpecification
from app.models.quotation import QuotationRequest, Quotation, QuotationItem
from app.models.order import Order, OrderProduct
from app.models.finance import PaymentRecord, RefundRecord, Statement
from app.models.system import SystemSetting, CompanyInfo, CompanyBankAccount


@pytest.fixture(scope='session')
def app():
    """创建测试应用实例"""
    # 创建临时数据库文件
    db_fd, db_path = tempfile.mkstemp()

    # 设置测试环境变量
    os.environ['TESTING'] = 'True'
    os.environ['DATABASE_URL'] = f'sqlite:///{db_path}'
    os.environ['SECRET_KEY'] = 'test-secret-key'

    # 创建应用
    app = create_app()

    # 设置测试配置
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False

    with app.app_context():
        # 创建所有表
        db.create_all()
        yield app

        # 清理
        db.drop_all()

    # 关闭并删除临时数据库文件
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture(scope='function')
def client(app):
    """创建测试客户端"""
    return app.test_client()


@pytest.fixture(scope='function')
def db_session(app):
    """创建数据库会话"""
    with app.app_context():
        # 清理所有表
        db.session.remove()
        db.drop_all()
        db.create_all()
        
        yield db.session
        
        # 测试后清理
        db.session.remove()


@pytest.fixture
def sample_customer(db_session):
    """创建示例客户"""
    customer = Customer(
        name='测试公司',
        contact='张三',
        phone='13800138000',
        email='<EMAIL>',
        address='北京市朝阳区',
        tax_id='91110000000000000X',
        source='网络',
        level='A',
        status='active',
        notes='测试客户'
    )
    db_session.add(customer)
    db_session.commit()
    return customer


@pytest.fixture
def sample_category(db_session):
    """创建示例产品分类"""
    category = ProductCategory(
        name='测试分类',
        description='测试分类描述',
        level=1,
        sort_order=1
    )
    db_session.add(category)
    db_session.commit()
    return category


@pytest.fixture
def sample_product(db_session, sample_category):
    """创建示例产品"""
    product = Product(
        name='测试产品',
        model='TEST-001',
        unit='个',
        category_id=sample_category.id,
        description='测试产品描述',
        status='active'
    )
    db_session.add(product)
    db_session.commit()
    
    # 添加产品规格
    spec = ProductSpecification(
        product_id=product.id,
        specification='标准规格',
        cost_price=100.00,
        suggested_price=150.00,
        tax_rate=13.0,
        is_default=True
    )
    db_session.add(spec)
    db_session.commit()
    
    return product


@pytest.fixture
def sample_quotation_request(db_session, sample_customer):
    """创建示例报价需求"""
    request = QuotationRequest(
        request_number='REQ20240101001',
        customer_id=sample_customer.id,
        project_name='测试项目',
        project_address='测试项目地址',
        status='待处理',
        notes='测试报价需求'
    )
    db_session.add(request)
    db_session.commit()
    return request


@pytest.fixture
def sample_quotation(db_session, sample_customer, sample_quotation_request):
    """创建示例报价单"""
    quotation = Quotation(
        quotation_number='QUO20240101001',
        request_id=sample_quotation_request.id,
        customer_id=sample_customer.id,
        project_name='测试项目',
        project_address='测试项目地址',
        payment_terms='预付30%，货到付款70%',
        delivery_terms='7个工作日内发货',
        status='草稿',
        total_amount=1500.00,
        notes='测试报价单'
    )
    db_session.add(quotation)
    db_session.commit()
    return quotation


@pytest.fixture
def sample_order(db_session, sample_customer, sample_quotation):
    """创建示例订单"""
    order = Order(
        order_number='ORD20240101001',
        customer_id=sample_customer.id,
        quotation_id=sample_quotation.id,
        project_name='测试项目',
        project_address='测试项目地址',
        contact_person='李四',
        contact_phone='13900139000',
        payment_terms='预付30%，货到付款70%',
        delivery_terms='7个工作日内发货',
        status='待确认',
        total_amount=1500.00,
        notes='测试订单'
    )
    db_session.add(order)
    db_session.commit()
    return order


@pytest.fixture
def sample_system_setting(db_session):
    """创建示例系统设置"""
    setting = SystemSetting(
        key='test_setting',
        value='test_value',
        description='测试设置'
    )
    db_session.add(setting)
    db_session.commit()
    return setting


@pytest.fixture
def sample_company_info(db_session):
    """创建示例企业信息"""
    company = CompanyInfo(
        name='测试企业',
        tax_id='91110000000000000X',
        contact='王五',
        phone='010-12345678',
        email='<EMAIL>',
        address='北京市海淀区'
    )
    db_session.add(company)
    db_session.commit()
    return company


# 测试数据生成器
class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def customer_data(**kwargs):
        """生成客户测试数据"""
        default_data = {
            'name': '测试公司',
            'contact': '张三',
            'phone': '13800138000',
            'email': '<EMAIL>',
            'address': '北京市朝阳区',
            'tax_id': '91110000000000000X',
            'source': '网络',
            'level': 'A',
            'status': '正常',  # 修改为正确的状态值
            'notes': '测试客户'
        }
        default_data.update(kwargs)
        return default_data
    
    @staticmethod
    def product_data(**kwargs):
        """生成产品测试数据"""
        default_data = {
            'name': '测试产品',
            'model': 'TEST-001',
            'unit': '个',
            'description': '测试产品描述',
            'status': 'active',
            'specifications': [{
                'specification': '标准规格',
                'cost_price': 100.00,
                'suggested_price': 150.00,
                'tax_rate': 13.0,
                'is_default': True
            }]
        }
        default_data.update(kwargs)
        return default_data
    
    @staticmethod
    def order_data(**kwargs):
        """生成订单测试数据"""
        default_data = {
            'project_name': '测试项目',
            'project_address': '测试项目地址',
            'contact_person': '李四',
            'contact_phone': '13900139000',
            'payment_terms': '预付30%，货到付款70%',
            'delivery_terms': '7个工作日内发货',
            'notes': '测试订单'
        }
        default_data.update(kwargs)
        return default_data


@pytest.fixture
def test_data_generator():
    """测试数据生成器夹具"""
    return TestDataGenerator
