<template>
  <el-dialog
    v-model="visible"
    title="导出报价需求表配置"
    width="500px"
    @close="handleClose"
  >
    <el-form :model="exportConfig" label-width="120px">
      <el-form-item label="导出格式">
        <el-radio-group v-model="exportConfig.format">
          <el-radio value="xlsx">Excel (XLSX)</el-radio>
          <el-radio value="pdf">PDF</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="包含表头">
        <el-switch v-model="exportConfig.includeHeader" />
      </el-form-item>
      
      <el-form-item label="导出列">
        <div class="export-columns-container">
          <el-checkbox v-model="exportConfig.selectAll" @change="handleSelectAllChange" class="select-all-checkbox">全选</el-checkbox>

          <!-- 基本信息字段 -->
          <div class="column-category">
            <div class="category-title">基本信息</div>
            <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
              <el-checkbox v-for="column in basicColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                {{ column.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>

          <!-- 产品明细字段 -->
          <div class="column-category">
            <div class="category-title">产品明细</div>
            <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
              <el-checkbox v-for="column in itemColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                {{ column.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exporting">
          确认导出
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { quotationRequestApi } from '@/api/quotation'

interface Props {
  modelValue: boolean
  requestId?: number
  projectName?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'export-success'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  requestId: undefined,
  projectName: ''
})

const emit = defineEmits<Emits>()

const visible = ref(false)
const exporting = ref(false)

// 定义导出配置
const availableColumns = [
  // 基本信息字段
  { prop: 'request_number', label: '需求编号', category: 'basic' },
  { prop: 'project_name', label: '项目名称', category: 'basic' },
  { prop: 'project_address', label: '项目地址', category: 'basic' },
  { prop: 'customer_name', label: '客户名称', category: 'basic' },
  { prop: 'expected_date', label: '预期时间', category: 'basic' },
  { prop: 'status', label: '状态', category: 'basic' },
  { prop: 'request_notes', label: '需求备注', category: 'basic' },
  // 产品明细字段
  { prop: 'product_name', label: '需求产品名称', category: 'items' },
  { prop: 'product_model', label: '需求产品型号', category: 'items' },
  { prop: 'product_spec', label: '需求产品规格', category: 'items' },
  { prop: 'quantity', label: '数量', category: 'items' },
  { prop: 'unit', label: '单位', category: 'items' },
  { prop: 'match_status', label: '匹配状态', category: 'items' },
  { prop: 'matched_product_name', label: '匹配产品名称', category: 'items' },
  { prop: 'matched_product_model', label: '匹配产品型号', category: 'items' },
  { prop: 'matched_spec_name', label: '匹配规格', category: 'items' },
  { prop: 'suggested_price', label: '建议价格', category: 'items' },
  { prop: 'notes', label: '产品备注', category: 'items' }
]

// 分离基本信息和产品明细字段
const basicColumns = availableColumns.filter(col => col.category === 'basic')
const itemColumns = availableColumns.filter(col => col.category === 'items')

const exportConfig = reactive({
  format: 'xlsx' as 'xlsx' | 'pdf',
  includeHeader: true,
  selectedColumns: availableColumns.map(col => col.prop),
  selectAll: true
})

const handleSelectAllChange = (val: boolean) => {
  if (val) {
    exportConfig.selectedColumns = availableColumns.map(col => col.prop)
  } else {
    exportConfig.selectedColumns = []
  }
}

const handleColumnChange = () => {
  exportConfig.selectAll = exportConfig.selectedColumns.length === availableColumns.length
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleClose = () => {
  visible.value = false
}

// 导出报价需求表
const handleExport = async () => {
  if (!props.requestId) {
    ElMessage.error('缺少报价需求表ID')
    return
  }

  if (exportConfig.selectedColumns.length === 0) {
    ElMessage.warning('请至少选择一列进行导出')
    return
  }

  exporting.value = true
  try {
    const response = await quotationRequestApi.export(props.requestId, {
      format: exportConfig.format,
      columns: exportConfig.selectedColumns,
      include_header: exportConfig.includeHeader
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(response)
    const link = document.createElement('a')
    link.href = url
    link.download = `报价需求表_${props.projectName || props.requestId}.${exportConfig.format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
    emit('export-success')
    handleClose()
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped>
.export-columns-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.select-all-checkbox {
  margin-bottom: 8px;
}

.column-category {
  margin-bottom: 16px;
}

.category-title {
  font-weight: 600;
  color: #606266;
  margin-bottom: 8px;
  font-size: 14px;
}

.column-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 20px;
}

.column-checkboxes.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 16px;
  padding-left: 20px;
}

.column-checkboxes .el-checkbox {
  margin-right: 0;
}
</style>
