# EMB项目开发规范

## 📋 目录
- [前端开发规范](#前端开发规范)
  - [API接口规范](#api接口规范)
  - [代码风格规范](#代码风格规范)
  - [组件开发规范](#组件开发规范)
- [后端开发规范](#后端开发规范)
- [数据库规范](#数据库规范)
- [项目结构规范](#项目结构规范)

---

## 前端开发规范

### API接口规范

#### 1. **API文件组织形式**
**✅ 推荐使用API对象形式，禁止使用独立函数形式**

```typescript
// ✅ 正确示例 - API对象形式
export const customerApi = {
  getList: (params?: PaginationParams) => {
    return request.get<PaginatedResponse<Customer>>('/customers', { params })
  },
  
  getById: (id: number) => {
    return request.get<Customer>(`/customers/${id}`)
  },
  
  create: (data: Partial<Customer>) => {
    return request.post<Customer>('/customers', data)
  },
  
  update: (id: number, data: Partial<Customer>) => {
    return request.put<Customer>(`/customers/${id}`, data)
  },
  
  delete: (id: number) => {
    return request.delete(`/customers/${id}`)
  }
}

// 导出默认实例
export default customerApi
```

```typescript
// ❌ 错误示例 - 独立函数形式（不推荐）
export function getCustomers(params) {
  return request.get('/customers', { params })
}

export function getCustomer(id) {
  return request.get(`/customers/${id}`)
}

export function createCustomer(data) {
  return request.post('/customers', data)
}
```

#### 2. **API方法命名规范**
- `getList` - 获取列表数据
- `getById` - 根据ID获取单条数据
- `create` - 创建新数据
- `update` - 更新数据
- `delete` - 删除数据
- `updateStatus` - 更新状态
- 其他业务方法使用驼峰命名法

#### 3. **类型定义规范**
```typescript
// API接口类型定义
export interface CustomerApi {
  getList: (params?: CustomerQuery) => Promise<ListResponse<Customer>>
  getById: (id: number) => Promise<ApiResponse<Customer>>
  create: (data: CustomerCreate) => Promise<ApiResponse<Customer>>
  update: (id: number, data: CustomerUpdate) => Promise<ApiResponse<Customer>>
  delete: (id: number) => Promise<ApiResponse<void>>
}
```

#### 4. **API文件结构模板**
```typescript
/**
 * [模块名称]管理API
 * 包括[具体功能描述]
 */
import { http } from './request'
import type { ApiResponse, ListResponse } from '@/types/api'

// 类型定义
export interface [Entity] {
  id: number
  // 其他字段...
  created_at: string
  updated_at: string
}

// 请求参数类型
export interface [Entity]Query {
  page?: number
  per_page?: number
  // 其他查询参数...
}

export interface [Entity]Create {
  // 创建时需要的字段...
}

export interface [Entity]Update {
  // 更新时需要的字段...
}

/**
 * [模块名称]管理API类
 */
export class [Entity]Api {
  private baseUrl = '/[api-path]'

  /**
   * 获取[实体]列表
   */
  async getList(params?: [Entity]Query): Promise<ListResponse<[Entity]>> {
    return http.get(`${this.baseUrl}`, params)
  }

  /**
   * 获取[实体]详情
   */
  async getById(id: number): Promise<ApiResponse<[Entity]>> {
    return http.get(`${this.baseUrl}/${id}`)
  }

  /**
   * 创建[实体]
   */
  async create(data: [Entity]Create): Promise<ApiResponse<[Entity]>> {
    return http.post(`${this.baseUrl}`, data)
  }

  /**
   * 更新[实体]
   */
  async update(id: number, data: [Entity]Update): Promise<ApiResponse<[Entity]>> {
    return http.put(`${this.baseUrl}/${id}`, data)
  }

  /**
   * 删除[实体]
   */
  async delete(id: number): Promise<ApiResponse<void>> {
    return http.delete(`${this.baseUrl}/${id}`)
  }
}

// 导出API实例
export const [entity]Api = new [Entity]Api()

// 导出默认实例
export default [entity]Api
```

### 代码风格规范

#### 1. **导入顺序**
```typescript
// 1. Vue相关导入
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 2. 图标导入
import { Search, Plus, Edit, Delete } from '@element-plus/icons-vue'

// 3. API和类型导入
import { customerApi, type Customer } from '@/api/customer'
import type { ApiResponse } from '@/types/api'

// 4. 组件导入
import CustomDialog from './components/CustomDialog.vue'
```

#### 2. **变量命名规范**
- 使用驼峰命名法
- 布尔值以 `is`、`has`、`can` 开头
- 常量使用大写字母和下划线

#### 3. **注释规范**
```typescript
/**
 * 获取客户列表
 * @param params 查询参数
 * @returns 客户列表响应
 */
const loadCustomers = async (params?: CustomerQuery) => {
  // 实现逻辑...
}
```

### 组件开发规范

#### 1. **组件文件命名**
- 使用PascalCase命名法
- 文件名与组件名保持一致

#### 2. **Props定义**
```typescript
// 使用TypeScript接口定义Props
interface Props {
  modelValue: boolean
  customerId?: number
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '默认标题'
})
```

#### 3. **事件定义**
```typescript
// 明确定义事件类型
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: [data: Customer]
  error: [error: Error]
}>()
```

---

## 后端开发规范

### API响应格式规范
```python
# 统一响应格式
{
    "code": 200,
    "message": "操作成功",
    "data": {},  # 或 []
    "timestamp": "2025-01-01T00:00:00Z",
    "pagination": {  # 分页数据时包含
        "page": 1,
        "per_page": 20,
        "total": 100,
        "pages": 5
    }
}
```

---

## 数据库规范

### 表命名规范
- 使用复数形式：`customers`、`orders`、`products`
- 关联表使用下划线连接：`order_products`、`customer_addresses`

### 字段命名规范
- 使用下划线命名法：`created_at`、`updated_at`
- 外键字段：`customer_id`、`order_id`
- 布尔字段：`is_active`、`has_discount`

---

## 项目结构规范

### 前端目录结构
```
frontend/src/
├── api/              # API接口文件
├── components/       # 公共组件
├── views/           # 页面组件
├── types/           # TypeScript类型定义
├── utils/           # 工具函数
├── stores/          # Pinia状态管理
└── assets/          # 静态资源
```

### API文件命名规范
- 使用单数形式：`customer.ts`、`order.ts`、`product.ts`
- 特殊模块：`dashboard.ts`、`system.ts`、`payment.ts`

---


## 注意事项

1. **保持一致性**: 所有API文件必须使用统一的API对象形式
2. **类型安全**: 充分利用TypeScript的类型检查
3. **错误处理**: 统一的错误处理机制
4. **性能优化**: 合理使用分页、缓存等优化手段
5. **代码复用**: 提取公共组件和工具函数

---

## 更新记录

- **2025-01-11**: 初始版本，确立API对象形式规范
- **2025-01-11**: 完成 `return.ts` 重构，从独立函数形式改为API对象形式，更新所有相关组件
- **2025-01-11**: 完成 `payment.ts` 优化，按照项目现有格式重构，类型定义移至 `@/types/payment.ts`
- **2025-01-11**: 清理向后兼容代码，删除 `statement.ts` 和 `return.ts` 中的旧函数导出，保持代码简洁
- **待更新**: 根据项目发展持续完善规范

## 重构完成情况

### ✅ 已完成重构的API文件
- `return.ts` - 退货管理API（2025-01-11完成）
  - 重构为 `ReturnApi` 类
  - 统一方法命名：`getList`, `getById`, `create`, `update`, `delete`, `updateStatus`
  - 更新了所有相关组件：`ReturnOrderList.vue`, `ReturnOrderCreate.vue`, `ReturnOrderEdit.vue`, `ReturnOrderDetail.vue`
  - 已清理向后兼容代码，保持代码简洁

- `statement.ts` - 对账单管理API（2025-01-11完成）
  - 重构为 `StatementApi` 类
  - 统一方法命名：`getList`, `getById`, `create`, `update`, `delete`, `confirm`
  - 新增类型定义：`StatementQuery`, `AvailableDeliveryNotesQuery`, `AvailableReturnOrdersQuery`, `ExportStatementsQuery`
  - 更新了所有相关组件：`StatementList.vue`, `StatementCreate.vue`, `StatementDetail.vue`, `StatementEdit.vue`, `DirectPaymentDialog.vue`
  - 已清理向后兼容代码，保持代码简洁

- `payment.ts` - 收款管理API（2025-01-11完成）
  - 按照项目现有格式优化：使用对象字面量形式而非类形式
  - 类型定义分离：移至 `@/types/payment.ts` 文件
  - 使用 `request` 对象和 `PaginationParams` 基础类型
  - 内联查询参数类型：使用 `PaginationParams & { ... }` 形式
  - 更新了所有相关组件：`PaymentManagement.vue`, `CustomerBalanceList.vue`, `BalanceTransactionList.vue`, `StatementPaymentList.vue`, `AddBalanceDialog.vue`

### 🔄 待重构的API文件
- `finance.ts` - 已使用API对象形式，符合规范
- 其他API文件已符合规范

---

**注意**: 本规范为EMB项目的开发标准，所有开发人员必须严格遵守，确保代码质量和项目一致性。
