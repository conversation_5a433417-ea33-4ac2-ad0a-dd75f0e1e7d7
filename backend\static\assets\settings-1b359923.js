import{I as t}from"./index-3d4c440c.js";function a(){return t({url:"/api/v1/company-info",method:"get"})}function e(a){return t({url:"/api/v1/company-info",method:"put",data:a})}function n(){return t({url:"/api/v1/company-bank-accounts",method:"get"})}function u(a){const e=new FormData;return e.append("file",a),t({url:"/api/v1/uploads/upload",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"},timeout:3e4})}function o(){return t({url:"/api/v1/backup-records",method:"get"})}function r(a){return t({url:"/api/v1/backups",method:"post",data:a})}function i(a){return t({url:`/api/v1/backups/${a}/restore`,method:"post"})}function p(t){return`/api/v1/backups/${t}/download`}function s(a){return t({url:`/api/v1/backups/${a}`,method:"delete"})}function c(){return t({url:"/api/v1/auto-backup-settings",method:"get"}).catch((()=>({enabled:!1,frequency:"daily",time:new Date((new Date).setHours(2,0,0,0)),keepCount:7,path:"C:\\EMB\\Backup\\Auto"})))}function d(a){const e={...a};return e.time instanceof Date&&(e.time=e.time.toISOString()),t({url:"/api/v1/auto-backup-settings",method:"put",data:e})}export{a,u as b,o as c,c as d,r as e,p as f,n as g,s as h,i as r,d as s,e as u};
