import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'

// 用户信息类型
export interface UserInfo {
  id: number
  username: string
  email?: string
  avatar?: string
  roles: string[]
  permissions: string[]
  profile?: {
    nickname?: string
    phone?: string
    department?: string
    position?: string
  }
}

// 登录表单类型
export interface LoginForm {
  username: string
  password: string
  remember?: boolean
}

// 用户状态管理
export const useUserStore = defineStore(
  'user',
  () => {
    // 状态
    const token = ref<string>('')
    const refreshToken = ref<string>('')
    const userInfo = ref<UserInfo | null>(null)
    const isLoggedIn = ref<boolean>(false)

    // 计算属性
    const hasToken = computed(() => !!token.value)
    const userName = computed(() => userInfo.value?.username || '未知用户')
    const userRoles = computed(() => userInfo.value?.roles || [])
    const userPermissions = computed(() => userInfo.value?.permissions || [])
    const userAvatar = computed(() => userInfo.value?.avatar || '')

    // 检查是否有特定权限
    const hasPermission = computed(() => (permission: string) => {
      return userPermissions.value.includes(permission)
    })

    // 检查是否有特定角色
    const hasRole = computed(() => (role: string) => {
      return userRoles.value.includes(role)
    })

    // 登录
    const login = async (loginForm: LoginForm) => {
      try {
        // TODO: 调用登录API
        // const response = await loginApi(loginForm)
        
        // 模拟登录成功
        const mockResponse = {
          token: 'mock-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
          userInfo: {
            id: 1,
            username: loginForm.username,
            email: '<EMAIL>',
            avatar: '',
            roles: ['admin'],
            permissions: ['*'],
            profile: {
              nickname: '管理员',
              phone: '13800138000',
              department: '技术部',
              position: '系统管理员',
            },
          },
        }

        // 设置token和用户信息
        setToken(mockResponse.token, mockResponse.refreshToken)
        setUserInfo(mockResponse.userInfo)
        setLoginStatus(true)

        ElMessage.success('登录成功')
        return mockResponse
      } catch (error) {
        ElMessage.error('登录失败')
        throw error
      }
    }

    // 退出登录
    const logout = async () => {
      try {
        // TODO: 调用退出登录API
        // await logoutApi()

        // 清除状态
        clearUserData()
        ElMessage.success('已退出登录')
      } catch (error) {
        // 即使API调用失败，也要清除本地状态
        clearUserData()
        ElMessage.warning('退出登录')
      }
    }

    // 刷新token
    const refreshUserToken = async () => {
      try {
        if (!refreshToken.value) {
          throw new Error('No refresh token')
        }

        // TODO: 调用刷新token API
        // const response = await refreshTokenApi(refreshToken.value)
        
        // 模拟刷新成功
        const newToken = 'refreshed-token-' + Date.now()
        setToken(newToken, refreshToken.value)
        
        return newToken
      } catch (error) {
        // 刷新失败，清除用户数据
        clearUserData()
        throw error
      }
    }

    // 获取用户信息
    const fetchUserInfo = async () => {
      try {
        if (!token.value) {
          throw new Error('No token')
        }

        // TODO: 调用获取用户信息API
        // const response = await getUserInfoApi()
        
        // 模拟获取用户信息
        const mockUserInfo: UserInfo = {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          avatar: '',
          roles: ['admin'],
          permissions: ['*'],
          profile: {
            nickname: '管理员',
            phone: '13800138000',
            department: '技术部',
            position: '系统管理员',
          },
        }

        setUserInfo(mockUserInfo)
        return mockUserInfo
      } catch (error) {
        ElMessage.error('获取用户信息失败')
        throw error
      }
    }

    // 更新用户信息
    const updateUserInfo = async (updates: Partial<UserInfo>) => {
      try {
        // TODO: 调用更新用户信息API
        // const response = await updateUserInfoApi(updates)
        
        // 更新本地用户信息
        if (userInfo.value) {
          userInfo.value = { ...userInfo.value, ...updates }
        }
        
        ElMessage.success('用户信息更新成功')
      } catch (error) {
        ElMessage.error('用户信息更新失败')
        throw error
      }
    }

    // 设置token
    const setToken = (newToken: string, newRefreshToken?: string) => {
      token.value = newToken
      if (newRefreshToken) {
        refreshToken.value = newRefreshToken
      }
    }

    // 设置用户信息
    const setUserInfo = (info: UserInfo) => {
      userInfo.value = info
    }

    // 设置登录状态
    const setLoginStatus = (status: boolean) => {
      isLoggedIn.value = status
    }

    // 清除用户数据
    const clearUserData = () => {
      token.value = ''
      refreshToken.value = ''
      userInfo.value = null
      isLoggedIn.value = false
    }

    // 初始化用户状态
    const initUserState = () => {
      // 如果有token，设置为已登录状态
      if (token.value) {
        isLoggedIn.value = true
        // 可以在这里尝试获取用户信息
        // fetchUserInfo().catch(() => {
        //   // 如果获取失败，清除状态
        //   clearUserData()
        // })
      }
    }

    return {
      // 状态
      token,
      refreshToken,
      userInfo,
      isLoggedIn,
      
      // 计算属性
      hasToken,
      userName,
      userRoles,
      userPermissions,
      userAvatar,
      hasPermission,
      hasRole,
      
      // 方法
      login,
      logout,
      refreshUserToken,
      fetchUserInfo,
      updateUserInfo,
      setToken,
      setUserInfo,
      setLoginStatus,
      clearUserData,
      initUserState,
    }
  },
  {
    // 持久化配置
    persist: true,
  }
)
