// 收款管理相关类型定义

// 客户余额
export interface CustomerBalance {
  id: number
  customer_id: number
  customer_name: string
  balance: number
  frozen_balance: number
  created_at: string
  updated_at: string
}

// 余额交易记录
export interface BalanceTransaction {
  id: number
  customer_id: number
  customer_name: string
  transaction_type: string
  amount: number
  balance_before: number
  balance_after: number
  reference_type?: string
  reference_id?: number
  description?: string
  created_at: string
}

// 对账单收款记录
export interface StatementPayment {
  id: number
  statement_id: number
  statement_number: string
  payment_date: string
  amount: number
  payment_method: string
  payment_source: string
  reference_number?: string
  bank_account?: string
  notes?: string
  status: string
  created_by?: string
  created_at: string
}

// 收款统计数据
export interface PaymentStats {
  payment_stats: {
    direct_payment_count: number
    direct_payment_amount: number
    balance_payment_count: number
    balance_payment_amount: number
    total_payment_count: number
    total_payment_amount: number
  }
  balance_stats: {
    recharge_count: number
    total_recharge_amount: number
    total_customer_balance: number
    total_frozen_balance: number
  }
  period: {
    start_date?: string
    end_date?: string
    customer_id?: number
  }
}

// 余额充值请求
export interface AddBalanceRequest {
  customer_id: number
  amount: number
  description?: string
  reference_type?: string
  reference_id?: number
}

// 直接收款请求
export interface DirectPaymentRequest {
  statement_id: number
  amount: number
  payment_method: string
  payment_date?: string
  reference_number?: string
  bank_account?: string
  notes?: string
  created_by?: string
}

// 余额支付请求
export interface BalancePaymentRequest {
  statement_id: number
  customer_id: number
  amount: number
  payment_date?: string
  notes?: string
  created_by?: string
}
