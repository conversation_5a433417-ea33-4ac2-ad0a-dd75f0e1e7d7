import{d as e,f as t,o as a,c as l,i as r,h as n,g as o,p as s,j as d,a as u,n as i,t as c,b as p,r as m,w as f,M as v,l as _,k as y,a8 as g,a9 as b,F as h,m as x,N as w,W as k,U as C,Z as V,$ as S,X as D,Y as P,P as j,u as z,e as I}from"./index-3d4c440c.js";import{C as $,a as T}from"./CustomerDeliveryAddresses-9519c02d.js";import{_ as N}from"./_plugin-vue_export-helper-1b428a4d.js";import{d as U,l as A}from"./customer-471ca075.js";const M={class:"status-tag"},B=N({__name:"StatusTag",props:{status:{type:[String,Number,Boolean],required:!0},statusMap:{type:Object,default:()=>({})},preset:{type:String,default:"",validator:e=>["","common","order","approval","payment","activity"].includes(e)},effect:{type:String,default:"light",validator:e=>["dark","light","plain"].includes(e)},size:{type:String,default:"",validator:e=>["","large","default","small"].includes(e)},hit:{type:Boolean,default:!1},color:{type:String,default:""}},setup(p){const m=p,f={common:{active:{text:"启用",type:"success",icon:"Check"},inactive:{text:"停用",type:"danger",icon:"Close"},pending:{text:"待处理",type:"warning",icon:"Timer"},processing:{text:"处理中",type:"primary",icon:"Loading"},completed:{text:"已完成",type:"success",icon:"CircleCheck"},failed:{text:"失败",type:"danger",icon:"CircleClose"},default:{text:"未知",type:"info",icon:""}},order:{pending:{text:"待确认",type:"warning",icon:"Timer"},confirmed:{text:"已确认",type:"primary",icon:"Document"},producing:{text:"生产中",type:"",color:"#336699",icon:"Cpu"},shipped:{text:"已发货",type:"",color:"#009688",icon:"Van"},delivered:{text:"已送达",type:"success",icon:"Location"},completed:{text:"已完成",type:"success",icon:"CircleCheck"},cancelled:{text:"已取消",type:"danger",icon:"Close"},default:{text:"未知",type:"info",icon:""}},approval:{draft:{text:"草稿",type:"info",icon:"Edit"},submitted:{text:"已提交",type:"primary",icon:"Upload"},reviewing:{text:"审核中",type:"warning",icon:"Loading"},approved:{text:"已通过",type:"success",icon:"CircleCheck"},rejected:{text:"已拒绝",type:"danger",icon:"CircleClose"},default:{text:"未知",type:"info",icon:""}},payment:{unpaid:{text:"未支付",type:"warning",icon:"Timer"},partial:{text:"部分支付",type:"",color:"#FF9800",icon:"Money"},paid:{text:"已支付",type:"success",icon:"Check"},refunding:{text:"退款中",type:"warning",icon:"TurnOff"},refunded:{text:"已退款",type:"info",icon:"Refresh"},default:{text:"未知",type:"info",icon:""}},activity:{upcoming:{text:"即将开始",type:"",color:"#673AB7",icon:"Calendar"},active:{text:"进行中",type:"success",icon:"VideoPlay"},paused:{text:"已暂停",type:"warning",icon:"VideoPause"},ended:{text:"已结束",type:"info",icon:"VideoStop"},cancelled:{text:"已取消",type:"danger",icon:"Close"},default:{text:"未知",type:"info",icon:""}}},v=e((()=>{if(Object.keys(m.statusMap).length>0){return m.statusMap[m.status]||m.statusMap.default||{text:m.status,type:"info"}}if(m.preset&&f[m.preset]){const e=f[m.preset];return e[m.status]||e.default||{text:m.status,type:"info"}}const e=m.status.toString().toLowerCase();return["true","1","active","enabled","success","online","completed","pass","approved"].includes(e)?{text:"成功",type:"success",icon:""}:["pending","wait","waiting","processing","confirming","warning"].includes(e)?{text:"处理中",type:"warning",icon:""}:["false","0","inactive","disabled","error","failed","offline","rejected","cancelled"].includes(e)?{text:"失败",type:"danger",icon:""}:{text:m.status,type:"info",icon:""}})),_=e((()=>v.value.text||m.status)),y=e((()=>m.color?"":v.value.type||"")),g=e((()=>v.value.icon||"")),b=e((()=>({"status-tag":!0,"status-tag-with-icon":!!g.value})));return(e,m)=>{const f=t("el-icon"),v=t("el-tag");return a(),l("div",M,[r(v,{type:y.value,effect:p.effect,size:p.size,hit:p.hit,color:p.color,class:i(b.value)},{default:n((()=>[g.value?(a(),o(f,{key:0,class:"icon-margin"},{default:n((()=>[(a(),o(s(g.value)))])),_:1})):d("",!0),u("span",{class:i({"status-text":g.value})},c(_.value),3)])),_:1},8,["type","effect","size","hit","color","class"])])}}},[["__scopeId","data-v-8f33e056"]]),O={class:"customer-orders"},R={class:"card-header flex-between"},E={class:"search-form mb-20"},F={key:0,class:"pagination-container"},L=N({__name:"CustomerOrders",props:{customerId:{type:[Number,String],required:!0},canExport:{type:Boolean,default:!0}},emits:["create","refresh"],setup(e,{emit:s}){const i=e,z=s,I=p(),$=m(!1),T=m([]),N=m("暂无订单数据"),U=[{value:"pending",label:"待处理"},{value:"confirmed",label:"已确认"},{value:"delivered",label:"已发货"},{value:"completed",label:"已完成"},{value:"cancelled",label:"已取消"}],A={pending:"待处理",confirmed:"已确认",delivered:"已发货",completed:"已完成",cancelled:"已取消"},M=m({currentPage:1,pageSize:10,total:0}),L=m({orderNumber:"",status:"",dateRange:[]}),Y=async()=>{$.value=!0,N.value="加载中...";try{await new Promise((e=>setTimeout(e,500)));const e=[{id:1,order_number:"DD-2023001",project_name:"北京某办公楼项目",expected_date:"2023-08-15",total_amount:5e4,status:"completed",created_at:"2023-07-10 09:30:00"},{id:2,order_number:"DD-2023002",project_name:"上海某商场项目",expected_date:"2023-09-20",total_amount:35e3,status:"delivered",created_at:"2023-07-15 14:20:00"},{id:3,order_number:"DD-2023003",project_name:"广州某学校项目",expected_date:"2023-10-10",total_amount:28e3,status:"confirmed",created_at:"2023-08-01 10:45:00"},{id:4,order_number:"DD-2023004",project_name:"深圳某医院项目",expected_date:"2023-11-05",total_amount:42e3,status:"pending",created_at:"2023-08-10 16:30:00"}];T.value=e,M.value.total=e.length}catch(e){console.error("获取订单列表失败",e),C.error("获取订单列表失败"),N.value="加载失败，请重试"}finally{$.value=!1}},q=()=>{M.value.currentPage=1,Y()},W=()=>{L.value={orderNumber:"",status:"",dateRange:[]},M.value.currentPage=1,Y()},Z=e=>{M.value.currentPage=e,Y()},K=e=>{M.value.pageSize=e,M.value.currentPage=1,Y()},X=()=>{z("create"),I.push(`/order/edit?customer_id=${i.customerId}`)},G=async()=>{try{await new Promise((e=>setTimeout(e,500))),C.success("订单导出成功")}catch(e){console.error("导出订单失败",e),C.error("导出订单失败")}};return f((()=>i.customerId),(()=>{i.customerId&&Y()}),{immediate:!0}),(s,i)=>{const p=t("el-icon"),m=t("el-button"),f=t("el-input"),z=t("el-form-item"),H=t("el-option"),J=t("el-select"),Q=t("el-date-picker"),ee=t("el-form"),te=t("el-table-column"),ae=t("el-dropdown-item"),le=t("el-dropdown-menu"),re=t("el-dropdown"),ne=t("el-table"),oe=t("el-pagination"),se=t("el-card"),de=v("loading");return a(),l("div",O,[r(se,null,{header:n((()=>[u("div",R,[i[7]||(i[7]=u("h3",null,"订单列表",-1)),u("div",null,[r(m,{type:"primary",onClick:X},{default:n((()=>[r(p,null,{default:n((()=>[r(_(V))])),_:1}),i[5]||(i[5]=y(" 新建订单 "))])),_:1}),e.canExport?(a(),o(m,{key:0,onClick:G},{default:n((()=>[r(p,null,{default:n((()=>[r(_(S))])),_:1}),i[6]||(i[6]=y(" 导出订单 "))])),_:1})):d("",!0)])])])),default:n((()=>[u("div",E,[r(ee,{inline:!0,model:L.value,onSubmit:g(q,["prevent"])},{default:n((()=>[r(z,{label:"订单编号"},{default:n((()=>[r(f,{modelValue:L.value.orderNumber,"onUpdate:modelValue":i[0]||(i[0]=e=>L.value.orderNumber=e),placeholder:"请输入订单编号",clearable:"",onKeyup:b(q,["enter"])},null,8,["modelValue"])])),_:1}),r(z,{label:"订单状态"},{default:n((()=>[r(J,{modelValue:L.value.status,"onUpdate:modelValue":i[1]||(i[1]=e=>L.value.status=e),placeholder:"请选择状态",clearable:"",style:{width:"130px"}},{default:n((()=>[(a(),l(h,null,x(U,(e=>r(H,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),r(z,{label:"下单日期"},{default:n((()=>[r(Q,{modelValue:L.value.dateRange,"onUpdate:modelValue":i[2]||(i[2]=e=>L.value.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),r(z,null,{default:n((()=>[r(m,{type:"primary",onClick:q},{default:n((()=>[r(p,null,{default:n((()=>[r(_(D))])),_:1}),i[8]||(i[8]=y(" 搜索 "))])),_:1}),r(m,{onClick:W},{default:n((()=>[r(p,null,{default:n((()=>[r(_(P))])),_:1}),i[9]||(i[9]=y(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])]),w((a(),o(ne,{data:T.value,border:"",stripe:"",style:{width:"100%"},"empty-text":N.value},{default:n((()=>[r(te,{type:"index",width:"50",align:"center"}),r(te,{prop:"order_number",label:"订单编号",width:"150"}),r(te,{prop:"project_name",label:"项目名称","min-width":"180"}),r(te,{prop:"expected_date",label:"预计采购时间",width:"120"}),r(te,{prop:"total_amount",label:"订单金额",width:"120"},{default:n((({row:e})=>{return[u("span",null,"¥ "+c((t=e.total_amount,Number(t).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}))),1)];var t})),_:1}),r(te,{prop:"status",label:"订单状态",width:"100"},{default:n((({row:e})=>[r(B,{status:e.status,"text-map":A},null,8,["status"])])),_:1}),r(te,{prop:"created_at",label:"创建时间",width:"150"}),r(te,{fixed:"right",label:"操作",width:"180"},{default:n((({row:e})=>[r(m,{link:"",type:"primary",onClick:t=>(e=>{I.push(`/order/detail/${e.id}`)})(e)},{default:n((()=>i[10]||(i[10]=[y("查看")]))),_:2},1032,["onClick"]),r(re,null,{dropdown:n((()=>[r(le,null,{default:n((()=>[r(ae,{onClick:t=>(e=>{I.push(`/order/delivery/${e.id}`)})(e)},{default:n((()=>i[12]||(i[12]=[y("送货单")]))),_:2},1032,["onClick"]),r(ae,{onClick:t=>(e=>{I.push(`/order/statement/${e.id}`)})(e)},{default:n((()=>i[13]||(i[13]=[y("对账单")]))),_:2},1032,["onClick"]),r(ae,{onClick:t=>(e=>{I.push(`/order/payment/${e.id}`)})(e)},{default:n((()=>i[14]||(i[14]=[y("收款记录")]))),_:2},1032,["onClick"]),"pending"===e.status?(a(),o(ae,{key:0,onClick:t=>(e=>{k.confirm(`确定要取消订单 ${e.order_number} 吗？`,"取消确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await new Promise((e=>setTimeout(e,500))),C.success("订单已取消"),Y()}catch(e){console.error("取消订单失败",e),C.error("取消订单失败")}})).catch((()=>{}))})(e)},{default:n((()=>i[15]||(i[15]=[y("取消订单")]))),_:2},1032,["onClick"])):d("",!0)])),_:2},1024)])),default:n((()=>[r(m,{link:"",type:"primary"},{default:n((()=>[i[11]||(i[11]=y(" 更多")),r(p,{class:"el-icon--right"},{default:n((()=>[r(_(j))])),_:1})])),_:1})])),_:2},1024)])),_:1})])),_:1},8,["data","empty-text"])),[[de,$.value]]),T.value.length>0?(a(),l("div",F,[r(oe,{"current-page":M.value.currentPage,"onUpdate:currentPage":i[3]||(i[3]=e=>M.value.currentPage=e),"page-size":M.value.pageSize,"onUpdate:pageSize":i[4]||(i[4]=e=>M.value.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:M.value.total,onSizeChange:K,onCurrentChange:Z},null,8,["current-page","page-size","total"])])):d("",!0)])),_:1})])}}},[["__scopeId","data-v-093d2472"]]),Y={class:"customer-detail"},q={class:"flex-between"},W=N({__name:"CustomerDetail",setup(s){const d=z(),i=p(),f=e((()=>d.params.id)),v=m({id:null,name:"",tax_id:"",contact:"",phone:"",email:"",address:"",level:"normal",source:"",status:"active",notes:"",created_at:"",updated_at:"",bank_accounts:[],delivery_addresses:[]}),g=()=>{i.go(-1)},b=()=>{i.push(`/customer/edit/${f.value}`)},h=async e=>{switch(e){case"viewOrders":i.push(`/orders?customerId=${f.value}`);break;case"viewStatements":C.info("查看对账单功能开发中");break;case"exportInfo":C.info("客户信息导出功能暂未开放");break;case"disable":if("normal"!==v.value.status)return void C.info("客户已经是禁用状态");try{await k.confirm("确定要禁用该客户吗？禁用后可能影响与该客户相关的业务操作。","禁用确认",{type:"warning"}),await A(f.value,{status:"disabled"}),v.value.status="disabled",C.success("客户已禁用")}catch(t){"cancel"!==t&&(console.error("禁用客户失败",t),C.error("禁用客户失败"))}break;case"enable":if("normal"===v.value.status)return void C.info("客户已经是正常状态");try{await k.confirm("确定要启用该客户吗？","启用确认",{type:"info"}),await A(f.value,{status:"normal"}),v.value.status="normal",C.success("客户已启用")}catch(t){"cancel"!==t&&(console.error("启用客户失败",t),C.error("启用客户失败"))}}},x=e=>({normal:"普通客户",important:"重要客户",vip:"VIP客户"}[e]||e),w=()=>{};return I((()=>{(async()=>{try{console.log("开始获取客户详情，ID:",f.value);const e=await U(f.value);let t;if(console.log("客户API响应:",e),e&&e.data)t=e.data;else{if(!e||"object"!=typeof e)throw new Error("未能解析API返回的客户数据");t=e}if(!t||!t.id)throw new Error(`未找到ID为${f.value}的客户`);console.log("解析后的客户数据:",t),v.value={id:t.id,name:t.name||"",tax_id:t.tax_id||"",contact:t.contact||"",phone:t.phone||"",email:t.email||"",address:t.address||"",level:t.level||"normal",source:t.source||"",status:t.status||"active",notes:t.notes||"",created_at:t.created_at||"",updated_at:t.updated_at||"",bank_accounts:Array.isArray(t.bank_accounts)?t.bank_accounts:[],delivery_addresses:Array.isArray(t.delivery_addresses)?t.delivery_addresses:[]},console.log("客户数据已更新:",v.value)}catch(e){console.error("获取客户信息失败",e),C.error(`获取客户信息失败: ${e.message||"未知错误"}`)}})()})),(e,s)=>{const d=t("el-button"),i=t("el-icon"),p=t("el-dropdown-item"),m=t("el-dropdown-menu"),k=t("el-dropdown"),C=t("el-card"),V=t("el-descriptions-item"),S=t("el-tag"),D=t("el-descriptions");return a(),l("div",Y,[r(C,{class:"header-card mb-20"},{default:n((()=>[u("div",q,[s[10]||(s[10]=u("h2",{class:"page-title"},"客户详情",-1)),u("div",null,[r(d,{onClick:g},{default:n((()=>s[2]||(s[2]=[y("返回")]))),_:1}),r(d,{type:"primary",onClick:b},{default:n((()=>s[3]||(s[3]=[y("编辑")]))),_:1}),r(k,{trigger:"click",onCommand:h},{dropdown:n((()=>[r(m,null,{default:n((()=>[r(p,{command:"viewOrders"},{default:n((()=>s[5]||(s[5]=[y("查看订单")]))),_:1}),r(p,{command:"viewStatements"},{default:n((()=>s[6]||(s[6]=[y("查看对账单")]))),_:1}),r(p,{command:"exportInfo"},{default:n((()=>s[7]||(s[7]=[y("导出信息")]))),_:1}),"active"===v.value.status?(a(),o(p,{key:0,command:"disable"},{default:n((()=>s[8]||(s[8]=[y("禁用客户")]))),_:1})):(a(),o(p,{key:1,command:"enable"},{default:n((()=>s[9]||(s[9]=[y("启用客户")]))),_:1}))])),_:1})])),default:n((()=>[r(d,{type:"success"},{default:n((()=>[s[4]||(s[4]=y(" 更多操作")),r(i,{class:"el-icon--right"},{default:n((()=>[r(_(j))])),_:1})])),_:1})])),_:1})])])])),_:1}),r(C,{class:"mb-20"},{header:n((()=>s[11]||(s[11]=[u("div",{class:"card-header"},[u("h3",null,"基本信息")],-1)]))),default:n((()=>[r(D,{column:2,border:""},{default:n((()=>[r(V,{label:"公司名称"},{default:n((()=>[y(c(v.value.name),1)])),_:1}),r(V,{label:"统一社会代码"},{default:n((()=>[y(c(v.value.tax_id),1)])),_:1}),r(V,{label:"联系人"},{default:n((()=>[y(c(v.value.contact),1)])),_:1}),r(V,{label:"联系电话"},{default:n((()=>[y(c(v.value.phone),1)])),_:1}),r(V,{label:"电子邮箱"},{default:n((()=>[y(c(v.value.email||"未设置"),1)])),_:1}),r(V,{label:"客户等级"},{default:n((()=>{return[r(S,{type:(e=v.value.level,{normal:"info",important:"warning",vip:"danger"}[e]||"")},{default:n((()=>[y(c(x(v.value.level)),1)])),_:1},8,["type"])];var e})),_:1}),r(V,{label:"客户来源"},{default:n((()=>{return[y(c((e=v.value.source,{internet:"网络渠道",referral:"客户推荐",exhibition:"展会",visit:"销售拜访",old_customer:"老客户",other:"其他"}[e]||e)),1)];var e})),_:1}),r(V,{label:"客户状态"},{default:n((()=>[r(S,{type:"normal"===v.value.status?"success":"danger"},{default:n((()=>[y(c("normal"===v.value.status?"正常":"禁用"),1)])),_:1},8,["type"])])),_:1}),r(V,{label:"公司地址",span:2},{default:n((()=>[y(c(v.value.address),1)])),_:1}),r(V,{label:"备注",span:2},{default:n((()=>[y(c(v.value.notes||"无"),1)])),_:1}),r(V,{label:"创建时间"},{default:n((()=>[y(c(v.value.created_at),1)])),_:1}),r(V,{label:"更新时间"},{default:n((()=>[y(c(v.value.updated_at),1)])),_:1})])),_:1})])),_:1}),r($,{modelValue:v.value.bank_accounts,"onUpdate:modelValue":s[0]||(s[0]=e=>v.value.bank_accounts=e),"customer-name":v.value.name,disabled:!0},null,8,["modelValue","customer-name"]),r(T,{modelValue:v.value.delivery_addresses,"onUpdate:modelValue":s[1]||(s[1]=e=>v.value.delivery_addresses=e),disabled:!0},null,8,["modelValue"]),r(L,{"customer-id":f.value,"can-export":!0,onCreate:w},null,8,["customer-id"])])}}},[["__scopeId","data-v-cbf91071"]]);export{W as default};
