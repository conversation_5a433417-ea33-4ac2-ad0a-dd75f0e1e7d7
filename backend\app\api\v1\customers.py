"""
客户管理API
提供客户信息的CRUD操作以及客户银行账户和送货地址的管理
基于原项目API实现，确保与现有数据库结构100%兼容
"""
from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from sqlalchemy import or_
from sqlalchemy.orm import selectinload
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd
from io import BytesIO
from werkzeug.datastructures import FileStorage

from app.models.customer import Customer, CustomerBankAccount, CustomerDeliveryAddress
from app.models.finance import Receivable, ReceivableDetail, StatementReceivable
from app.schemas.customer import (
    CustomerSchema,
    CustomerSimpleSchema,
    CustomerBankAccountSchema,
    CustomerDeliveryAddressSchema
)
from app.utils.response import (
    success_response,
    error_response,
    paginated_response,
    validation_error_response,
    not_found_response
)
from app.utils.pagination import PaginationHelper
from app.utils.exceptions import ValidationError as CustomValidationError, NotFoundError
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('customers', description='客户管理API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 自动从Marshmallow Schema生成API模型（带缓存优化）
customer_model = create_input_model(api, CustomerSchema, 'CustomerInput')
bank_account_model = create_input_model(api, CustomerBankAccountSchema, 'CustomerBankAccountInput')
delivery_address_model = create_input_model(api, CustomerDeliveryAddressSchema, 'CustomerDeliveryAddressInput')


@api.route('')
class CustomerList(Resource):
    @api.doc('get_customers')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('name', '客户名称或联系人搜索')
    @api.param('name_like', '客户名称模糊搜索')
    @api.param('status', '客户状态')
    @api.param('level', '客户等级')
    def get(self):
        """获取客户列表"""
        try:
            # 查询参数处理
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            name_query = request.args.get('name', '')
            contact_query = request.args.get('contact', '')
            name_like_query = request.args.get('name_like', '')
            status = request.args.get('status', '')
            level = request.args.get('level', '')
            
            # 构建查询
            query = Customer.query
            
            # 应用过滤条件
            if name_query:
                query = query.filter(or_(
                    Customer.name.ilike(f'%{name_query}%'),
                    Customer.contact.ilike(f'%{name_query}%')
                ))
            
            if name_like_query:
                query = query.filter(Customer.name.ilike(f'%{name_like_query}%'))

            if status:
                query = query.filter(Customer.status == status)
            if level:
                query = query.filter(Customer.level == level)
            
            # 排序
            query = query.order_by(Customer.created_at.desc())
            
            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query,
                serializer_func=lambda item: CustomerSimpleSchema().dump(item),
                page=page,
                per_page=per_page,
                message="获取客户列表成功"
            )
            
        except Exception as e:
            current_app.logger.error(f"获取客户列表失败: {str(e)}")
            return make_response(error_response, f"获取客户列表失败: {str(e)}")

    @api.doc('create_customer',
             body=customer_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建新客户"""
        try:
            data = request.get_json() or {}

            # 预处理数据
            if data.get('email') == '':
                data['email'] = None

            if data.get('status') == 'normal':
                data['status'] = '正常'
            elif data.get('status') == 'disabled':
                data['status'] = '禁用'

            if not data.get('tax_id') or data.get('tax_id') == '未提供':
                data['tax_id'] = None

            # 数据验证
            customer_schema = CustomerSchema(only=(
                'name', 'contact', 'phone', 'email', 'address', 'tax_id', 
                'source', 'level', 'status', 'notes', 'bank_accounts', 'delivery_addresses'
            ))
            validated_data = customer_schema.load(data)

            # 分离出嵌套数据
            bank_accounts_data = validated_data.pop('bank_accounts', [])
            delivery_addresses_data = validated_data.pop('delivery_addresses', [])

            # 检查客户名称是否已存在
            if Customer.query.filter_by(name=validated_data['name']).first():
                return make_response(error_response, "客户名称已存在", errors={"name": "此客户名称已被使用"})

            # 统一社会信用代码的唯一性检查
            if validated_data.get('tax_id') is not None:
                if Customer.query.filter_by(tax_id=validated_data['tax_id']).first():
                    return make_response(error_response, "统一社会信用代码已存在", errors={"tax_id": "此统一社会信用代码已被使用"})

            # 创建客户
            customer = Customer(**validated_data)
            db.session.add(customer)
            db.session.flush()

            # 创建银行账户
            if bank_accounts_data:
                for ba_data in bank_accounts_data:
                    if ba_data:
                        ba_data['customer_id'] = customer.id
                        bank_account = CustomerBankAccount(**ba_data)
                        db.session.add(bank_account)

            # 创建送货地址
            if delivery_addresses_data:
                for addr_data in delivery_addresses_data:
                    if addr_data:
                        addr_data['customer_id'] = customer.id
                        delivery_address = CustomerDeliveryAddress(**addr_data)
                        db.session.add(delivery_address)

            db.session.commit()

            # 返回创建的客户信息
            created_customer = Customer.query.get(customer.id)
            if not created_customer:
                return make_response(error_response, "创建客户后未能找到记录", code=500)

            response_data = CustomerSchema().dump(created_customer)
            return make_response(success_response, response_data, "客户创建成功", 201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建客户失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建客户失败: {str(e)}")


@api.route('/<int:customer_id>')
class CustomerDetail(Resource):
    @api.doc('get_customer')
    def get(self, customer_id):
        """获取客户详情"""
        try:
            customer = Customer.query.get(customer_id)
            if not customer:
                return not_found_response("客户不存在")
            
            customer_data = CustomerSchema().dump(customer)
            return success_response(customer_data, "获取客户详情成功")
            
        except Exception as e:
            current_app.logger.error(f"获取客户详情失败: {str(e)}")
            return error_response(f"获取客户详情失败: {str(e)}")

    @api.doc('update_customer',
             body=customer_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Customer Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, customer_id):
        """更新客户信息"""
        try:
            customer = Customer.query.get(customer_id)
            if not customer:
                return not_found_response("客户不存在")
                
            data = request.get_json() or {}
            data.pop('id', None)  # 移除ID字段

            # 预处理数据
            if 'status' in data:
                if data['status'] == 'normal':
                    data['status'] = '正常'
                elif data['status'] == 'disabled':
                    data['status'] = '禁用'

            if 'tax_id' in data and (data['tax_id'] == '' or data['tax_id'] == '未提供'):
                data['tax_id'] = None

            # 数据验证
            customer_schema = CustomerSchema(only=(
                'name', 'contact', 'phone', 'email', 'address', 'tax_id',
                'source', 'level', 'status', 'notes'
            ), partial=True)
            validated_data = customer_schema.load(data)
            
            # 检查名称唯一性
            if 'name' in validated_data and validated_data['name'] != customer.name:
                if Customer.query.filter_by(name=validated_data['name']).first():
                    return error_response("客户名称已存在", errors={"name": "此客户名称已被使用"})
            
            # 检查税号唯一性
            if 'tax_id' in validated_data and validated_data['tax_id'] != customer.tax_id:
                if validated_data['tax_id'] != '未提供':
                    if Customer.query.filter(
                        Customer.id != customer_id, 
                        Customer.tax_id == validated_data['tax_id']
                    ).first():
                        return error_response("统一社会信用代码已存在", errors={"tax_id": "此统一社会信用代码已被使用"})
            
            # 更新客户信息
            for key, value in validated_data.items():
                setattr(customer, key, value)
                
            db.session.commit()
            
            # 返回更新后的客户信息
            updated_data = CustomerSchema().dump(customer)
            return success_response(updated_data, "客户信息更新成功")
        
        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return validation_error_response(e.messages)
        except Exception as e:
            current_app.logger.error(f"更新客户失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新客户失败: {str(e)}")

    @api.doc('delete_customer')
    def delete(self, customer_id):
        """删除客户"""
        try:
            customer = Customer.query.get(customer_id)
            if not customer:
                return not_found_response("客户不存在")

            # 检查是否有业务关联数据（报价单、订单等）
            from ...models.quotation import Quotation
            from ...models.order import Order

            quotation_count = Quotation.query.filter_by(customer_id=customer_id).count()
            order_count = Order.query.filter_by(customer_id=customer_id).count()

            if quotation_count > 0:
                return error_response("无法删除客户：该客户存在关联的报价单，请先处理相关报价单后再删除", code=400)

            if order_count > 0:
                return error_response("无法删除客户：该客户存在关联的订单，请先处理相关订单后再删除", code=400)

            # 删除非业务关联数据（应收账款、余额等）
            try:
                # 删除应收账款记录
                db.session.execute(
                    "DELETE FROM receivables WHERE customer_id = :customer_id",
                    {"customer_id": customer_id}
                )

                # 删除客户余额记录
                db.session.execute(
                    "DELETE FROM customer_balances WHERE customer_id = :customer_id",
                    {"customer_id": customer_id}
                )

                # 删除余额交易记录
                db.session.execute(
                    "DELETE FROM balance_transactions WHERE customer_id = :customer_id",
                    {"customer_id": customer_id}
                )

                # 删除应收明细记录
                db.session.execute(
                    "DELETE FROM receivable_details WHERE customer_id = :customer_id",
                    {"customer_id": customer_id}
                )

                # 删除对账单记录
                db.session.execute(
                    "DELETE FROM statements WHERE customer_id = :customer_id",
                    {"customer_id": customer_id}
                )

                # 删除对账单应收记录
                db.session.execute(
                    "DELETE FROM statement_receivables WHERE customer_id = :customer_id",
                    {"customer_id": customer_id}
                )

                current_app.logger.info(f"已清理客户ID {customer_id} 的关联财务数据")

            except Exception as cleanup_error:
                current_app.logger.warning(f"清理客户关联数据时出现警告: {str(cleanup_error)}")
                # 继续执行删除，因为这些表可能不存在或没有数据

            # 删除客户（银行账户和收货地址会通过cascade自动删除）
            db.session.delete(customer)
            db.session.commit()
            return success_response(message="客户删除成功")

        except Exception as e:
            current_app.logger.error(f"删除客户失败: {str(e)}")
            db.session.rollback()

            # 处理常见的数据库约束错误
            error_msg = str(e)
            if "IntegrityError" in error_msg or "FOREIGN KEY constraint failed" in error_msg:
                if "quotations" in error_msg:
                    return error_response("无法删除客户：该客户存在关联的报价单，请先处理相关报价单后再删除", code=400)
                elif "orders" in error_msg:
                    return error_response("无法删除客户：该客户存在关联的订单，请先处理相关订单后再删除", code=400)
                elif "delivery_notes" in error_msg:
                    return error_response("无法删除客户：该客户存在关联的发货单，请先处理相关发货单后再删除", code=400)
                else:
                    return error_response("无法删除客户：该客户存在关联数据，请先处理相关数据后再删除", code=400)
            else:
                return error_response("删除客户失败，请稍后重试", code=500)


@api.route('/batch')
class CustomerBatch(Resource):
    @api.doc('batch_delete_customers')
    def delete(self):
        """批量删除客户"""
        try:
            data = request.get_json() or {}
            ids = data.get('ids')

            if not ids or not isinstance(ids, list):
                return error_response("请求参数错误，需要提供客户ID列表", code=400)

            num_deleted = Customer.query.filter(Customer.id.in_(ids)).delete(synchronize_session=False)
            db.session.commit()
            return success_response(message=f"成功删除了 {num_deleted} 个客户")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"批量删除客户失败: {str(e)}")

            # 处理常见的数据库约束错误
            error_msg = str(e)
            if "IntegrityError" in error_msg:
                if "quotations.customer_id" in error_msg:
                    return error_response("批量删除失败：部分客户存在关联的报价单，请先处理相关报价单后再删除", code=400)
                elif "orders.customer_id" in error_msg:
                    return error_response("批量删除失败：部分客户存在关联的订单，请先处理相关订单后再删除", code=400)
                elif "delivery_notes.customer_id" in error_msg:
                    return error_response("批量删除失败：部分客户存在关联的发货单，请先处理相关发货单后再删除", code=400)
                else:
                    return error_response("批量删除失败：部分客户存在关联数据，请先处理相关数据后再删除", code=400)
            else:
                return error_response("批量删除客户失败，请稍后重试", code=500)





@api.route('/batch/export')
class CustomerBatchExport(Resource):
    @api.doc('batch_export_customers')
    def post(self):
        """导出指定ID的客户信息到Excel文件"""
        try:
            data = request.get_json() or {}
            ids = data.get('ids')

            if not ids or not isinstance(ids, list):
                return error_response("请求参数错误，需要提供客户ID列表", code=400)

            # 查询指定ID的客户
            customers = Customer.query.filter(Customer.id.in_(ids)).order_by(Customer.created_at.desc()).all()

            if not customers:
                return error_response("没有找到指定ID的客户", code=404)

            # 准备数据
            customer_schema = CustomerSchema(many=True, exclude=(
                'bank_accounts', 'delivery_addresses', 'default_bank_account', 'default_delivery_address'
            ))
            customers_data = customer_schema.dump(customers)

            df = pd.DataFrame(customers_data)

            df.rename(columns={
                'id': '客户编号',
                'name': '公司名称',
                'contact': '联系人',
                'phone': '联系电话',
                'email': '电子邮箱',
                'address': '公司地址',
                'tax_id': '统一社会代码',
                'source': '客户来源',
                'level': '客户等级',
                'status': '状态',
                'notes': '备注',
                'created_at': '创建时间',
                'updated_at': '更新时间'
            }, inplace=True)

            # 创建Excel文件到内存
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='客户列表')
            output.seek(0)

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name='customers_batch_export.xlsx'
            )

        except Exception as e:
            current_app.logger.error(f"批量导出客户失败: {str(e)}")
            return error_response(f"批量导出客户失败: {str(e)}", code=500)


@api.route('/import')
class CustomerImport(Resource):
    @api.doc('import_customers')
    def post(self):
        """从Excel文件导入客户信息"""
        try:
            if 'file' not in request.files:
                return error_response("没有找到文件部分", code=400)

            file: FileStorage = request.files['file']

            if file.filename == '':
                return error_response("没有选择文件", code=400)

            if file and (file.filename.endswith('.xlsx') or file.filename.endswith('.xls')):
                # 读取Excel文件，将所有列都当作字符串处理
                df = pd.read_excel(
                    file,
                    engine='openpyxl' if file.filename.endswith('.xlsx') else 'xlrd',
                    dtype=str  # 强制所有列为字符串类型
                )
                df = df.where(pd.notnull(df), None)

                imported_count = 0
                errors = []
                customers_to_add = []

                # 获取现有数据
                existing_names = set(c.name for c in Customer.query.with_entities(Customer.name).all())
                existing_tax_ids = set(c.tax_id for c in Customer.query.filter(
                    Customer.tax_id.isnot(None)  # 只查询非空税号
                ).with_entities(Customer.tax_id).all())

                column_mapping = {
                    '公司名称': 'name',
                    '联系人': 'contact',
                    '联系电话': 'phone',
                    '电子邮箱': 'email',
                    '公司地址': 'address',
                    '统一社会代码': 'tax_id',
                    '客户来源': 'source',
                    '客户等级': 'level',
                    '状态': 'status',
                    '备注': 'notes'
                }
                df.rename(columns=column_mapping, inplace=True)

                for index, row in df.iterrows():
                    row_number = index + 2  # Excel行号从2开始（第1行是标题）
                    row_data = row.to_dict()
                    customer_data = {k: v for k, v in row_data.items() if k in column_mapping.values()}

                    # 数据清理：处理字符串格式
                    for key, value in customer_data.items():
                        if value is not None:
                            # 将所有值转换为字符串并去除前后空格
                            customer_data[key] = str(value).strip()
                            # 处理 'nan' 字符串（pandas读取空值时可能产生）
                            if customer_data[key].lower() in ['nan', 'none', '']:
                                customer_data[key] = None

                    if not customer_data.get('name'):
                        errors.append(f"第 {row_number} 行：缺少公司名称")
                        continue

                    if not customer_data.get('contact'):
                        errors.append(f"第 {row_number} 行：缺少联系人")
                        continue

                    # 处理税号
                    if not customer_data.get('tax_id') or customer_data.get('tax_id').strip() == '':
                        customer_data['tax_id'] = None  # 空值存储为NULL
                    else:
                        customer_data['tax_id'] = customer_data['tax_id'].strip()

                    # 检查名称唯一性
                    if customer_data['name'] in existing_names or any(
                        c['name'] == customer_data['name'] for c in customers_to_add
                    ):
                        errors.append(f"第 {row_number} 行：公司名称 '{customer_data['name']}' 已存在")
                        continue

                    # 检查税号唯一性（只检查非空税号）
                    if customer_data['tax_id']:  # 只检查非空税号
                        if customer_data['tax_id'] in existing_tax_ids or any(
                            c.get('tax_id') == customer_data['tax_id'] and c.get('tax_id') is not None
                            for c in customers_to_add
                        ):
                            errors.append(f"第 {row_number} 行：统一社会代码 '{customer_data['tax_id']}' 已存在")
                            continue

                    # 处理状态
                    if 'status' not in customer_data or customer_data['status'] not in ['正常', '禁用']:
                        customer_data['status'] = '正常'

                    try:
                        schema = CustomerSchema(only=list(column_mapping.values()))
                        validated_data = schema.load(customer_data)
                        # 保存行号信息用于后续错误报告
                        validated_data['_row_number'] = row_number
                        customers_to_add.append(validated_data)
                    except ValidationError as e:
                        errors.append(f"第 {row_number} 行数据验证失败: {e.messages}")

                # 逐条插入以获取详细错误信息
                if customers_to_add:
                    for customer_data in customers_to_add:
                        try:
                            # 提取行号信息
                            row_number = customer_data.pop('_row_number', '未知')

                            customer = Customer(**customer_data)
                            db.session.add(customer)
                            db.session.commit()
                            imported_count += 1
                        except Exception as e:
                            db.session.rollback()
                            current_app.logger.error(f"插入客户失败: {str(e)}")

                            # 处理数据库约束错误
                            error_msg = str(e)
                            if "UNIQUE constraint failed: customers.tax_id" in error_msg:
                                errors.append(f"第 {row_number} 行：统一社会代码 '{customer_data.get('tax_id', '')}' 已存在")
                            elif "UNIQUE constraint failed: customers.name" in error_msg:
                                errors.append(f"第 {row_number} 行：公司名称 '{customer_data.get('name', '')}' 已存在")
                            else:
                                errors.append(f"第 {row_number} 行：数据插入失败")
                            continue

                if errors:
                    if imported_count > 0:
                        return error_response(
                            f"部分导入成功：成功导入 {imported_count} 条记录，跳过 {len(errors)} 条记录",
                            errors=errors,
                            code=207
                        )
                    else:
                        return error_response(
                            f"导入失败：所有 {len(errors)} 条记录都存在问题",
                            errors=errors,
                            code=400
                        )
                else:
                    return success_response(message=f"导入成功：共导入 {imported_count} 条客户记录")

            else:
                return error_response("无效的文件类型，请上传 .xlsx 或 .xls 文件", code=400)

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"导入客户失败: {str(e)}")
            return error_response(f"处理Excel文件失败: {str(e)}", code=500)


@api.route('/import/template')
class CustomerImportTemplate(Resource):
    @api.doc('download_customer_import_template')
    def get(self):
        """下载客户导入模板"""
        try:
            # 创建模板数据 - 包含重复数据用于测试
            template_data = [
                {
                    '公司名称': '示例公司名称',  # 这行会重复（如果之前导入过）
                    '联系人': '张三',
                    '联系电话': '13800138000',
                    '电子邮箱': '<EMAIL>',
                    '公司地址': '北京市朝阳区示例地址',
                    '统一社会代码': '91110000000000000X',
                    '客户来源': 'internet',
                    '客户等级': 'normal',
                    '状态': '正常',
                    '备注': '示例备注信息'
                },
                {
                    '公司名称': '新测试公司A',  # 这行应该能成功导入
                    '联系人': '李四',
                    '联系电话': '13800138001',
                    '电子邮箱': '<EMAIL>',
                    '公司地址': '上海市浦东新区测试地址A',
                    '统一社会代码': '91310000000000001A',
                    '客户来源': 'referral',
                    '客户等级': 'vip',
                    '状态': '正常',
                    '备注': '测试备注信息A'
                },
                {
                    '公司名称': '新测试公司B',  # 这行应该能成功导入
                    '联系人': '王五',
                    '联系电话': '13800138002',
                    '电子邮箱': '<EMAIL>',
                    '公司地址': '广州市天河区测试地址B',
                    '统一社会代码': '',  # 空税号示例
                    '客户来源': 'exhibition',
                    '客户等级': 'important',
                    '状态': '正常',
                    '备注': '测试备注信息B'
                }
            ]

            df = pd.DataFrame(template_data)

            # 创建Excel文件到内存
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # 确保所有数据都以文本格式写入
                df.to_excel(writer, index=False, sheet_name='客户导入模板')

                # 获取工作表并设置列格式为文本
                worksheet = writer.sheets['客户导入模板']
                for col in worksheet.columns:
                    for cell in col:
                        cell.number_format = '@'  # 设置为文本格式

                # 添加说明工作表
                instructions = pd.DataFrame({
                    '字段说明': [
                        '公司名称：必填，客户公司的名称',
                        '联系人：必填，主要联系人姓名',
                        '联系电话：可选，联系电话号码',
                        '电子邮箱：可选，邮箱地址',
                        '公司地址：可选，公司详细地址',
                        '统一社会代码：可选，企业统一社会信用代码',
                        '客户来源：可选，客户来源渠道（internet/referral/exhibition/visit/old_customer/other）',
                        '客户等级：可选，客户等级（normal/vip/important）',
                        '状态：可选，客户状态（正常/禁用），默认为正常',
                        '备注：可选，备注信息'
                    ]
                })
                instructions.to_excel(writer, index=False, sheet_name='字段说明')

            output.seek(0)

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'客户导入模板_{datetime.now().strftime("%Y%m%d")}.xlsx'
            )

        except Exception as e:
            current_app.logger.error(f"生成客户导入模板失败: {str(e)}")
            return error_response(f"生成导入模板失败: {str(e)}", code=500)


# 客户银行账户相关API
@api.route('/<int:customer_id>/bank-accounts')
class CustomerBankAccountList(Resource):
    @api.doc('get_customer_bank_accounts')
    def get(self, customer_id):
        """获取客户银行账户列表"""
        try:
            customer = Customer.query.get(customer_id)
            if not customer:
                return not_found_response("客户不存在")

            bank_account_schema = CustomerBankAccountSchema(many=True)
            bank_accounts_data = bank_account_schema.dump(customer.bank_accounts)

            return success_response(bank_accounts_data, "获取客户银行账户列表成功")

        except Exception as e:
            current_app.logger.error(f"获取银行账户列表失败: {str(e)}")
            return error_response(f"获取银行账户列表失败: {str(e)}")

    @api.doc('create_customer_bank_account',
             body=bank_account_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 404: 'Customer Not Found',
                 500: 'Internal Server Error'
             })
    def post(self, customer_id):
        """创建客户银行账户"""
        try:
            customer = Customer.query.get(customer_id)
            if not customer:
                return not_found_response("客户不存在")

            data = request.get_json() or {}
            data['customer_id'] = customer_id

            bank_account_schema = CustomerBankAccountSchema()
            validated_data = bank_account_schema.load(data)

            # 如果设为默认账户，则将其他账户设为非默认
            if validated_data.get('is_default', False):
                for account in customer.bank_accounts:
                    account.is_default = False

            bank_account = CustomerBankAccount(**validated_data)
            db.session.add(bank_account)
            db.session.commit()

            return success_response(
                bank_account_schema.dump(bank_account),
                "银行账户创建成功",
                201
            )

        except ValidationError as e:
            return validation_error_response(e.messages)
        except Exception as e:
            current_app.logger.error(f"创建银行账户失败: {str(e)}")
            db.session.rollback()
            return error_response(f"创建银行账户失败: {str(e)}")


@api.route('/bank-accounts/<int:account_id>')
class CustomerBankAccountDetail(Resource):
    @api.doc('update_customer_bank_account',
             body=bank_account_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Bank Account Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, account_id):
        """更新客户银行账户信息"""
        try:
            bank_account = CustomerBankAccount.query.get(account_id)
            if not bank_account:
                return not_found_response("银行账户不存在")

            data = request.get_json() or {}

            bank_account_schema = CustomerBankAccountSchema()
            validated_data = bank_account_schema.load(data, partial=True)

            # 如果设为默认账户，则将同一客户的其他账户设为非默认
            if validated_data.get('is_default', False) and not bank_account.is_default:
                customer = Customer.query.get(bank_account.customer_id)
                for account in customer.bank_accounts:
                    if account.id != bank_account.id:
                        account.is_default = False

            for key, value in validated_data.items():
                setattr(bank_account, key, value)

            db.session.commit()

            return success_response(
                bank_account_schema.dump(bank_account),
                "银行账户更新成功"
            )

        except ValidationError as e:
            return validation_error_response(e.messages)
        except Exception as e:
            current_app.logger.error(f"更新银行账户失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新银行账户失败: {str(e)}")

    @api.doc('delete_customer_bank_account')
    def delete(self, account_id):
        """删除客户银行账户"""
        try:
            bank_account = CustomerBankAccount.query.get(account_id)
            if not bank_account:
                return not_found_response("银行账户不存在")

            # 检查是否为默认账户并且是唯一账户
            customer = Customer.query.get(bank_account.customer_id)
            if bank_account.is_default and len(customer.bank_accounts) > 1:
                # 设置另一个账户为默认
                for account in customer.bank_accounts:
                    if account.id != bank_account.id:
                        account.is_default = True
                        break

            db.session.delete(bank_account)
            db.session.commit()
            return success_response(message="银行账户删除成功")

        except Exception as e:
            current_app.logger.error(f"删除银行账户失败: {str(e)}")
            db.session.rollback()
            return error_response(f"删除银行账户失败: {str(e)}")


# 客户送货地址相关API
@api.route('/<int:customer_id>/delivery-addresses')
class CustomerDeliveryAddressList(Resource):
    @api.doc('get_customer_delivery_addresses')
    def get(self, customer_id):
        """获取客户送货地址列表"""
        try:
            customer = Customer.query.get(customer_id)
            if not customer:
                return not_found_response("客户不存在")

            address_schema = CustomerDeliveryAddressSchema(many=True)
            addresses_data = address_schema.dump(customer.delivery_addresses)

            return success_response(addresses_data, "获取客户送货地址列表成功")

        except Exception as e:
            current_app.logger.error(f"获取送货地址列表失败: {str(e)}")
            return error_response(f"获取送货地址列表失败: {str(e)}")

    @api.doc('create_customer_delivery_address',
             body=delivery_address_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 404: 'Customer Not Found',
                 500: 'Internal Server Error'
             })
    def post(self, customer_id):
        """创建客户送货地址"""
        try:
            customer = Customer.query.get(customer_id)
            if not customer:
                return not_found_response("客户不存在")

            data = request.get_json() or {}
            data['customer_id'] = customer_id

            address_schema = CustomerDeliveryAddressSchema()
            validated_data = address_schema.load(data)

            # 如果设为默认地址，则将其他地址设为非默认
            if validated_data.get('is_default', False):
                for address in customer.delivery_addresses:
                    address.is_default = False

            delivery_address = CustomerDeliveryAddress(**validated_data)
            db.session.add(delivery_address)
            db.session.commit()

            return success_response(
                address_schema.dump(delivery_address),
                "送货地址创建成功",
                201
            )

        except ValidationError as e:
            return validation_error_response(e.messages)
        except Exception as e:
            current_app.logger.error(f"创建送货地址失败: {str(e)}")
            db.session.rollback()
            return error_response(f"创建送货地址失败: {str(e)}")


@api.route('/delivery-addresses/<int:address_id>')
class CustomerDeliveryAddressDetail(Resource):
    @api.doc('update_customer_delivery_address',
             body=delivery_address_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Delivery Address Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, address_id):
        """更新客户送货地址"""
        try:
            delivery_address = CustomerDeliveryAddress.query.get(address_id)
            if not delivery_address:
                return not_found_response("送货地址不存在")

            data = request.get_json() or {}

            address_schema = CustomerDeliveryAddressSchema()
            validated_data = address_schema.load(data, partial=True)

            # 如果设为默认地址，则将同一客户的其他地址设为非默认
            if validated_data.get('is_default', False) and not delivery_address.is_default:
                customer = Customer.query.get(delivery_address.customer_id)
                for address in customer.delivery_addresses:
                    if address.id != delivery_address.id:
                        address.is_default = False

            for key, value in validated_data.items():
                setattr(delivery_address, key, value)

            db.session.commit()

            return success_response(
                address_schema.dump(delivery_address),
                "送货地址更新成功"
            )

        except ValidationError as e:
            return validation_error_response(e.messages)
        except Exception as e:
            current_app.logger.error(f"更新送货地址失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新送货地址失败: {str(e)}")

    @api.doc('delete_customer_delivery_address')
    def delete(self, address_id):
        """删除客户送货地址"""
        try:
            delivery_address = CustomerDeliveryAddress.query.get(address_id)
            if not delivery_address:
                return not_found_response("送货地址不存在")

            # 检查是否为默认地址并且不是唯一地址
            customer = Customer.query.get(delivery_address.customer_id)
            if delivery_address.is_default and len(customer.delivery_addresses) > 1:
                # 设置另一个地址为默认
                for address in customer.delivery_addresses:
                    if address.id != delivery_address.id:
                        address.is_default = True
                        break

            db.session.delete(delivery_address)
            db.session.commit()
            return success_response(message="送货地址删除成功")

        except Exception as e:
            current_app.logger.error(f"删除送货地址失败: {str(e)}")
            db.session.rollback()
            return error_response(f"删除送货地址失败: {str(e)}")


# 客户导入导出相关API
@api.route('/export')
class CustomerExport(Resource):
    @api.doc('export_customers')
    @api.param('name', '客户名称搜索')
    @api.param('status', '客户状态')
    @api.param('level', '客户等级')
    def get(self):
        """导出客户信息到Excel文件"""
        try:
            # 获取查询参数
            name = request.args.get('name', '')
            status = request.args.get('status', '')
            level = request.args.get('level', '')

            # 构建查询
            query = Customer.query.options(
                selectinload(Customer.bank_accounts),
                selectinload(Customer.delivery_addresses)
            )

            if name:
                query = query.filter(Customer.name.ilike(f'%{name}%'))
            if status:
                query = query.filter(Customer.status == status)
            if level:
                query = query.filter(Customer.level == level)

            customers = query.order_by(Customer.created_at.desc()).all()

            if not customers:
                return error_response("没有符合条件的客户可导出", code=404)

            # 准备数据
            export_data = []
            for customer in customers:
                customer_data = {
                    '客户编号': customer.id,
                    '客户名称': customer.name,
                    '联系人': customer.contact,
                    '联系电话': customer.phone or '',
                    '邮箱地址': customer.email or '',
                    '公司地址': customer.address or '',
                    '统一社会信用代码': customer.tax_id or '',
                    '客户来源': customer.source or '',
                    '客户等级': customer.level,
                    '状态': customer.status,
                    '备注': customer.notes or '',
                    '创建时间': customer.created_at.strftime('%Y-%m-%d %H:%M:%S') if customer.created_at else '',
                    '更新时间': customer.updated_at.strftime('%Y-%m-%d %H:%M:%S') if customer.updated_at else ''
                }

                # 联系人信息已经在主数据中包含了
                # customer.contact, customer.phone, customer.email

                # 添加主要送货地址
                if customer.delivery_addresses:
                    main_address = next((addr for addr in customer.delivery_addresses if addr.is_default), customer.delivery_addresses[0])
                    customer_data['送货地址'] = f"{main_address.province}{main_address.city}{main_address.district}{main_address.detailed_address}"
                    customer_data['送货联系人'] = main_address.contact_person or ''
                    customer_data['送货联系电话'] = main_address.contact_phone or ''
                else:
                    customer_data['送货地址'] = ''
                    customer_data['送货联系人'] = ''
                    customer_data['送货联系电话'] = ''

                export_data.append(customer_data)

            df = pd.DataFrame(export_data)

            # 创建Excel文件到内存
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='客户列表')
            output.seek(0)

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'客户列表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            )

        except Exception as e:
            current_app.logger.error(f"导出客户失败: {str(e)}")
            return error_response(f"导出客户失败: {str(e)}")


@api.route('/statistics')
class CustomerStatistics(Resource):
    @api.doc('get_customer_statistics')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取客户统计信息"""
        try:
            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()

            # 构建查询
            query = Customer.query

            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(Customer.created_at >= start)
                except ValueError:
                    return error_response("开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            if end_date:
                try:
                    end = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(Customer.created_at <= end)
                except ValueError:
                    return error_response("结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            # 统计各等级客户数量
            level_stats = {}
            for level in ['normal', 'vip', 'premium', 'enterprise']:
                count = query.filter(Customer.level == level).count()
                level_stats[level] = count

            # 统计各状态客户数量
            status_stats = {}
            for status in ['正常', '禁用']:
                count = query.filter(Customer.status == status).count()
                status_stats[status] = count

            # 总统计
            total_count = query.count()

            # 按月统计新增客户（最近12个月）
            monthly_stats = []
            for i in range(12):
                month_start = datetime.now().replace(day=1) - timedelta(days=30*i)
                month_end = month_start.replace(day=1) + timedelta(days=32)
                month_end = month_end.replace(day=1) - timedelta(days=1)

                count = Customer.query.filter(
                    Customer.created_at >= month_start,
                    Customer.created_at <= month_end
                ).count()

                monthly_stats.append({
                    'month': month_start.strftime('%Y-%m'),
                    'count': count
                })

            monthly_stats.reverse()  # 按时间正序

            statistics = {
                'total_count': total_count,
                'level_statistics': level_stats,
                'status_statistics': status_stats,
                'monthly_statistics': monthly_stats,
                'period': {
                    'start_date': start_date or '全部',
                    'end_date': end_date or '全部'
                }
            }

            return success_response(statistics, "获取客户统计信息成功")

        except Exception as e:
            current_app.logger.error(f"获取客户统计信息失败: {str(e)}")
            return error_response(f"获取客户统计信息失败: {str(e)}")


@api.route('/search')
class CustomerSearch(Resource):
    @api.doc('search_customers')
    @api.param('q', '搜索关键词', required=True)
    @api.param('limit', '返回数量限制', type='integer', default=10)
    def get(self):
        """客户快速搜索（用于下拉选择等场景）"""
        try:
            query_text = request.args.get('q', '').strip()
            limit = request.args.get('limit', 10, type=int)

            if not query_text:
                return error_response("搜索关键词不能为空", code=400)

            # 构建搜索查询
            search_query = Customer.query.filter(
                or_(
                    Customer.name.ilike(f'%{query_text}%'),
                    Customer.credit_code.ilike(f'%{query_text}%'),
                    Customer.legal_representative.ilike(f'%{query_text}%')
                )
            ).filter(
                Customer.status == '正常'  # 只搜索正常状态的客户
            ).limit(limit)

            customers = search_query.all()

            # 简化的客户信息
            results = []
            for customer in customers:
                results.append({
                    'id': customer.id,
                    'name': customer.name,
                    'type': customer.type,
                    'level': customer.level,
                    'credit_code': customer.credit_code,
                    'legal_representative': customer.legal_representative
                })

            return success_response(results, f"搜索到 {len(results)} 个客户")

        except Exception as e:
            current_app.logger.error(f"客户搜索失败: {str(e)}")
            return error_response(f"客户搜索失败: {str(e)}")


# 客户应收账款相关API
@api.route('/<int:customer_id>/receivables')
class CustomerReceivableAPI(Resource):
    @api.doc('get_customer_receivables')
    def get(self, customer_id):
        """获取客户应收账款信息（基于新的对账单逻辑）"""
        try:
            customer = Customer.query.get(customer_id)
            if not customer:
                return not_found_response("客户不存在")

            # 获取基于对账单的应收账款记录
            statement_receivables = StatementReceivable.query.filter_by(customer_id=customer_id).all()

            # 计算总金额
            total_amount = sum(float(sr.amount) for sr in statement_receivables)
            unsettled_amount = total_amount  # 新逻辑：应收账款记录本身就是未结清的金额
            settled_amount = 0.0  # 新逻辑：已结清的对账单不会有应收账款记录

            # 构建响应数据
            data = {
                'customer_id': customer_id,
                'customer_name': customer.name,
                'total_amount': total_amount,
                'unsettled_amount': unsettled_amount,
                'settled_amount': settled_amount,
                'details': []
            }

            # 构建基于对账单的应收账款明细
            for sr in statement_receivables:
                detail_data = {
                    'id': sr.id,
                    'source_type': 'statement',  # 新逻辑：来源类型为对账单
                    'source_id': sr.statement_id,
                    'amount': float(sr.amount),
                    'settlement_status': '已支付' if sr.status == '已支付' else '未结清',
                    'settlement_date': sr.last_payment_date.isoformat() if sr.last_payment_date else None,
                    'created_at': sr.created_at.isoformat() if sr.created_at else None
                }

                # 添加对账单信息
                if sr.statement:
                    detail_data['source_info'] = {
                        'statement_number': sr.statement.statement_number,
                        'statement_date': sr.statement.statement_date.isoformat() if sr.statement.statement_date else None,
                        'status': sr.statement.status,
                        'adjusted_total_amount': float(sr.statement.adjusted_total_amount or 0),
                        'paid_amount': float(sr.statement.paid_amount or 0)
                    }

                data['details'].append(detail_data)

            return success_response(data, "获取客户应收账款信息成功")

        except Exception as e:
            current_app.logger.error(f"获取客户应收账款失败: {str(e)}")
            return error_response(f"获取客户应收账款失败: {str(e)}")
