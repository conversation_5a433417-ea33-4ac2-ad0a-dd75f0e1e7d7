/**
 * 订单状态类型
 */
export type OrderStatus = 
  | '待确认' 
  | '已确认' 
  | '生产中' 
  | '待发货' 
  | '已发货' 
  | '已完成' 
  | '已取消'

/**
 * 客户简化信息
 */
export interface CustomerSimple {
  id: number
  name: string
  contact?: string
  phone?: string
  email?: string
}

/**
 * 产品规格信息
 */
export interface ProductSpecification {
  id: number
  product_id: number
  specification: string
  cost_price?: number
  suggested_price?: number
  tax_rate?: number
}

/**
 * 产品信息
 */
export interface Product {
  id: number
  name: string
  model: string
  unit: string
  category_id?: number
  brand_id?: number
  status: string
  description?: string
  specifications?: ProductSpecification[]
}

/**
 * 订单产品项目
 */
export interface OrderProduct {
  id: number
  order_id: number
  product_id: number
  product_specification_id: number
  quantity: number
  unit_price: number
  total_price: number
  notes?: string

  // 快照字段
  product_name?: string
  product_model?: string
  product_unit?: string
  specification_description?: string

  // 发货相关
  delivered_quantity?: number
  remaining_quantity?: number

  // 产品来源信息
  source_type?: 'quotation' | 'manual'
  source_id?: number
  source_display?: string

  // 关联信息
  product?: Product
  product_specification?: ProductSpecification
}

/**
 * 订单接口
 */
export interface Order {
  id: number
  order_number: string
  customer_id: number
  project_name: string
  status: OrderStatus
  order_date: string
  delivery_date?: string
  total_amount: number
  notes?: string
  
  // 关联信息
  customer?: CustomerSimple
  products?: OrderProduct[]
  
  // 时间戳
  created_at: string
  updated_at: string
}

/**
 * 订单列表查询参数
 */
export interface ListOrderParams {
  page?: number
  per_page?: number
  order_number?: string
  customer_name?: string
  project_name?: string
  status?: OrderStatus
  start_date?: string
  end_date?: string
  [key: string]: any
}

/**
 * 创建订单数据
 */
export interface CreateOrderData {
  customer_id: number
  project_name: string
  order_date: string
  delivery_date?: string
  notes?: string
  products: Array<{
    product_id: number
    product_specification_id: number
    quantity: number
    unit_price: number
    notes?: string
  }>
}

/**
 * 更新订单数据
 */
export interface UpdateOrderData extends Partial<Omit<CreateOrderData, 'customer_id'>> {
  status?: OrderStatus
}
