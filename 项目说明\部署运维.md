# EMB项目部署运维指南

## 📋 部署概览

EMB工程物资报价及订单管理系统支持多种部署方式，从开发环境到生产环境的完整部署解决方案。

### 部署架构
- **前后端分离**: 前端静态文件 + 后端API服务
- **数据库**: SQLite(开发) / PostgreSQL(生产)
- **Web服务器**: Nginx + Gunicorn
- **容器化**: Docker + Docker Compose

## 🔧 环境配置

### 系统要求

#### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB可用空间
- **操作系统**: Linux/Windows/macOS

#### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **操作系统**: Ubuntu 20.04+ / CentOS 8+

### 软件依赖

#### 后端依赖
```bash
# Python环境
Python 3.8+
pip 20.0+

# 数据库（生产环境）
PostgreSQL 12+ / MySQL 8.0+

# Web服务器（生产环境）
Nginx 1.18+
Gunicorn 20.0+
```

#### 前端依赖
```bash
# Node.js环境
Node.js 16+
npm 8+ / yarn 1.22+

# 构建工具
Vite 6.0+
```

## 🚀 开发环境部署

### 1. 环境准备
```bash
# 克隆项目
git clone https://github.com/kkkdkk/EMB-new.git
cd EMB-new

# 检查Python版本
python --version  # 需要3.8+

# 检查Node.js版本
node --version     # 需要16+
npm --version      # 需要8+
```

### 2. 后端部署
```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 创建环境配置文件
cp .env.example .env

# 编辑环境配置
# 配置数据库连接、密钥等
```

### 3. 环境变量配置
```bash
# .env文件内容
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///dev_project.db
HOST=0.0.0.0
PORT=5001

# API配置
API_TITLE=EMB System API
API_VERSION=1.0
API_DESCRIPTION=工程物资报价及订单管理系统API文档

# 文件上传配置
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# 安全配置
WTF_CSRF_ENABLED=False
```

### 4. 数据库初始化
```bash
# 初始化数据库
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all(); print('数据库初始化完成')"

# 或者使用Flask-Migrate（推荐）
flask db init
flask db migrate -m "初始化数据库"
flask db upgrade
```

### 5. 启动后端服务
```bash
# 开发模式启动
python run.py

# 输出信息
🚀 启动EMB系统API服务...
📖 API文档地址: http://localhost:5001/docs/
🧪 测试端点: http://localhost:5001/test/health
🌍 环境: development
🔧 调试模式: 开启
```

### 6. 前端部署
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 输出信息
VITE v6.2.4  ready in 1234 ms
➜  Local:   http://localhost:3001/
➜  Network: use --host to expose
```

### 7. 验证部署
```bash
# 检查后端健康状态
curl http://localhost:5001/test/health

# 检查API文档
curl http://localhost:5001/docs/

# 检查前端应用
curl http://localhost:3001/
```

## 🏭 生产环境部署

### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y python3 python3-pip python3-venv
sudo apt install -y nodejs npm
sudo apt install -y nginx
sudo apt install -y postgresql postgresql-contrib

# 安装Docker（可选）
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### 2. 数据库配置
```bash
# PostgreSQL配置
sudo -u postgres psql

# 创建数据库和用户
CREATE DATABASE emb_system;
CREATE USER emb_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE emb_system TO emb_user;
\q
```

### 3. 后端生产部署
```bash
# 创建应用目录
sudo mkdir -p /opt/emb-system
sudo chown $USER:$USER /opt/emb-system
cd /opt/emb-system

# 克隆代码
git clone https://github.com/kkkdkk/EMB-new.git .

# 后端配置
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 安装生产依赖
pip install gunicorn psycopg2-binary

# 生产环境配置
cat > .env << EOF
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=$(openssl rand -hex 32)
DATABASE_URL=postgresql://emb_user:your_password@localhost/emb_system
HOST=127.0.0.1
PORT=5001
EOF

# 初始化数据库
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

### 4. Gunicorn配置
```bash
# 创建Gunicorn配置文件
cat > gunicorn.conf.py << EOF
# Gunicorn配置文件
bind = "127.0.0.1:5001"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
daemon = False
user = "www-data"
group = "www-data"
tmp_upload_dir = None
logfile = "/var/log/gunicorn/emb-api.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
EOF

# 创建日志目录
sudo mkdir -p /var/log/gunicorn
sudo chown www-data:www-data /var/log/gunicorn
```

### 5. 系统服务配置
```bash
# 创建systemd服务文件
sudo cat > /etc/systemd/system/emb-api.service << EOF
[Unit]
Description=EMB System API
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/emb-system/backend
Environment=PATH=/opt/emb-system/backend/venv/bin
ExecStart=/opt/emb-system/backend/venv/bin/gunicorn --config gunicorn.conf.py run:application
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable emb-api
sudo systemctl start emb-api
sudo systemctl status emb-api
```

### 6. 前端生产构建
```bash
cd /opt/emb-system/frontend

# 安装依赖
npm ci --only=production

# 生产构建
npm run build

# 构建结果在dist目录
ls -la dist/
```

### 7. Nginx配置
```bash
# 创建Nginx配置文件
sudo cat > /etc/nginx/sites-available/emb-system << EOF
server {
    listen 80;
    server_name your-domain.com;  # 替换为实际域名
    
    # 前端静态文件
    location / {
        root /opt/emb-system/frontend/dist;
        index index.html;
        try_files \$uri \$uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # API文档
    location /docs/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 文件上传大小限制
    client_max_body_size 16M;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/emb-system /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 8. SSL证书配置（推荐）
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🐳 Docker部署

### 1. Dockerfile配置

#### 后端Dockerfile
```dockerfile
# backend/Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 5001

# 启动命令
CMD ["gunicorn", "--config", "gunicorn.conf.py", "run:application"]
```

#### 前端Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app

# 复制依赖文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产镜像
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制Nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 2. Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 数据库
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: emb_system
      POSTGRES_USER: emb_user
      POSTGRES_PASSWORD: your_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  # 后端API
  api:
    build: ./backend
    environment:
      FLASK_ENV: production
      DATABASE_URL: *************************************************/emb_system
      SECRET_KEY: your-secret-key-here
    ports:
      - "5001:5001"
    depends_on:
      - postgres
    restart: unless-stopped
    volumes:
      - ./uploads:/app/uploads

  # 前端
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - api
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. Docker部署命令
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f api
docker-compose logs -f frontend

# 停止服务
docker-compose down

# 重新构建
docker-compose build --no-cache
docker-compose up -d
```

## 📊 监控和维护

### 1. 日志管理
```bash
# 应用日志
tail -f /var/log/gunicorn/emb-api.log

# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# 系统服务日志
sudo journalctl -u emb-api -f
```

### 2. 性能监控
```bash
# 系统资源监控
htop
iostat -x 1
free -h
df -h

# 数据库监控
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# 应用进程监控
ps aux | grep gunicorn
netstat -tlnp | grep :5001
```

### 3. 备份策略
```bash
# 数据库备份
sudo -u postgres pg_dump emb_system > backup_$(date +%Y%m%d_%H%M%S).sql

# 文件备份
tar -czf uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz /opt/emb-system/uploads/

# 自动备份脚本
cat > /opt/backup.sh << EOF
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p \$BACKUP_DIR

# 数据库备份
sudo -u postgres pg_dump emb_system > \$BACKUP_DIR/db_backup_\$DATE.sql

# 文件备份
tar -czf \$BACKUP_DIR/files_backup_\$DATE.tar.gz /opt/emb-system/uploads/

# 清理7天前的备份
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /opt/backup.sh

# 添加到crontab
echo "0 2 * * * /opt/backup.sh" | sudo crontab -
```

### 4. 更新部署
```bash
# 代码更新
cd /opt/emb-system
git pull origin main

# 后端更新
cd backend
source venv/bin/activate
pip install -r requirements.txt
sudo systemctl restart emb-api

# 前端更新
cd ../frontend
npm ci
npm run build
sudo systemctl reload nginx

# 数据库迁移（如有）
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

## 🔧 故障排除

### 常见问题

#### 1. 后端服务无法启动
```bash
# 检查服务状态
sudo systemctl status emb-api

# 查看详细日志
sudo journalctl -u emb-api -n 50

# 检查端口占用
sudo netstat -tlnp | grep :5001

# 手动启动测试
cd /opt/emb-system/backend
source venv/bin/activate
python run.py
```

#### 2. 数据库连接失败
```bash
# 检查数据库服务
sudo systemctl status postgresql

# 测试数据库连接
sudo -u postgres psql -c "SELECT version();"

# 检查数据库配置
cat backend/.env | grep DATABASE_URL
```

#### 3. 前端页面无法访问
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查Nginx配置
sudo nginx -t

# 查看Nginx日志
sudo tail -f /var/log/nginx/error.log
```

#### 4. API请求失败
```bash
# 检查API健康状态
curl http://localhost:5001/test/health

# 检查API文档
curl http://localhost:5001/docs/

# 检查代理配置
curl -I http://localhost/api/v1/customers
```

---

**部署指南版本**: v1.0  
**最后更新**: 2025年1月  
**适用环境**: 开发/测试/生产  
**维护状态**: 🔄 持续更新中
