import{I as e,J as a,u as t,b as l,r as i,d as r,e as o,f as s,o as n,c,i as u,h as d,a as p,t as m,k as g,F as _,m as f,l as v,a8 as h,U as b,W as y,g as x,Z as w,_ as V,V as $}from"./index-3d4c440c.js";import{a as U,u as A,c as k}from"./product-ab10366a.js";import{l as N}from"./brand-81076433.js";import{_ as j}from"./_plugin-vue_export-helper-1b428a4d.js";const C={class:"product-edit"},I={class:"flex-between"},E={class:"form-title"},P={class:"toolbar mb-20"},T={class:"toolbar mb-20"},O={class:"upload-area"},D=["src"],S={class:"dialog-footer"},R=j(a({__name:"ProductEdit",setup(a){const j=t(),R=l(),L=i(),B=i("basic"),W=r((()=>!j.params.id)),F=r((()=>j.params.id?parseInt(j.params.id,10):null)),J=i([]),z=i([]),M=i([]),q=i(""),G=i(!1),Z=i([]),H=i([]),K=i({id:null,name:"",model:"",category_id:null,brand_id:null,unit:"个",status:"active",description:"",notes:"",specifications:[],attributes:[],images:[]}),Q={name:[{required:!0,message:"请输入产品名称",trigger:"blur"}],model:[{required:!0,message:"请输入产品型号",trigger:"blur"}],category_id:[{required:!0,message:"请选择产品分类",trigger:"change"}],unit:[{required:!0,message:"请输入计量单位",trigger:"blur"}]},X=()=>{const e=0===K.value.specifications.length;K.value.specifications.push({specification:"",cost_price:0,min_price:0,max_price:0,suggested_price:0,tax_rate:13,is_default:e})},Y=()=>{K.value.attributes.push({attribute_name:"",attribute_value:""})},ee=e=>{q.value=e.url,G.value=!0},ae=(e,a)=>{console.log("Removing file:",e);if("success"===e.status&&e.id&&"number"==typeof e.id&&e.url&&!e.url.startsWith("blob:")){console.log(`Marking existing image for deletion: ID ${e.id}`),e.id&&!H.value.includes(e.id)&&H.value.push(e.id);const a=K.value.images.findIndex((a=>a.id===e.id));-1!==a&&K.value.images.splice(a,1)}else{const a=Z.value.findIndex((a=>a.uid===e.uid));if(-1!==a){console.log(`Removing temporary image: UID ${e.uid}`);const t=Z.value.splice(a,1)[0];t.url&&t.url.startsWith("blob:")&&URL.revokeObjectURL(t.url)}}},te=(e,a)=>{if("ready"===e.status&&e.raw){const a=e.raw;a.uid=Date.now()+Math.random().toString(36).substring(2,15);const t=URL.createObjectURL(e.raw);a.url=t,e.url=t,Z.value.push(a),console.log("Added to tempImageFiles:",a)}else"success"===e.status&&console.log("File already uploaded or not ready for temp storage:",e)},le=e=>{if(!["image/jpeg","image/png","image/jpg"].includes(e.type))return b.error("只能上传JPG/PNG格式图片!"),!1;return!!(e.size/1024/1024<2)||(b.error("图片大小不能超过2MB!"),!1)};function ie(e){return e?e&&"object"==typeof e&&void 0!==e.data?e.data:e:null}const re=async()=>{try{const t=ie(await(a={tree:!0},e({url:"/api/v1/product-categories",method:"get",params:a})));let l=[];const i=(e,a=0,t="")=>{if(!Array.isArray(e))return[];e.forEach((e=>{const r=a>0?"├─ ".repeat(a):"",o=t?`${r}${e.name}`:e.name;l.push({...e,name:o,_originalName:e.name,level:a}),e.children&&Array.isArray(e.children)&&e.children.length>0&&i(e.children,a+1,e.name)}))};Array.isArray(t)?i(t):t&&t.items&&Array.isArray(t.items)&&i(t.items),J.value=l,console.log("加载的分类数据:",J.value)}catch(t){console.error("获取分类失败:",t),J.value=[]}var a},oe=()=>{R.push("/products")},se=async()=>{if(L.value)try{if(await L.value.validate(),null===K.value.category_id||void 0===K.value.category_id)return void b.error("请选择产品分类");let l=K.value.category_id;if("number"!=typeof l){console.warn("分类ID不是数字类型，尝试转换:",l);const e=Number(l);if(isNaN(e))return void b.error("产品分类数据格式不正确");K.value.category_id=e}if(0===K.value.specifications.length)return b.warning("请至少添加一个产品规格"),void(B.value="specs");const i=K.value.specifications.filter((e=>{if(!e.specification||""===e.specification.trim())return!1;return["cost_price","suggested_price","tax_rate"].every((a=>"number"==typeof e[a]&&!isNaN(e[a])))}));if(0===i.length)return b.error("规格数据不完整，请确保填写了规格名称、成本价、建议售价和税率"),void(B.value="specs");!i.some((e=>!0===e.is_default))&&i.length>0&&(i[0].is_default=!0);const r={name:K.value.name,model:K.value.model,category_id:Number(K.value.category_id),brand_id:K.value.brand_id?Number(K.value.brand_id):null,unit:K.value.unit,status:"active"===K.value.status?"正常":"禁用",description:K.value.description||"",notes:K.value.notes||"",specifications:i.map((e=>{const a={specification:e.specification,cost_price:Number(e.cost_price),suggested_price:Number(e.suggested_price),tax_rate:Number(e.tax_rate),is_default:Boolean(e.is_default)};return e.id&&(a.id=e.id),null!==e.min_price&&void 0!==e.min_price?a.min_price=Number(e.min_price):a.min_price=Number(e.cost_price),null!==e.max_price&&void 0!==e.max_price?a.max_price=Number(e.max_price):a.max_price=Number(e.suggested_price),e.notes&&(a.notes=e.notes),a})),attributes:K.value.attributes.filter((e=>e.attribute_name&&""!==e.attribute_name.trim()&&e.attribute_value&&""!==e.attribute_value.trim())).map((e=>({attribute_name:e.attribute_name,attribute_value:e.attribute_value,...e.id&&{id:e.id}})))};let o;o=W.value?{...r,images:[]}:r,console.log("准备提交的产品数据:",o),console.log("规格数据详细信息:",JSON.stringify(o.specifications)),console.log("完整的JSON数据:",JSON.stringify(o));try{if(W.value){const e=await(async e=>{console.log("开始创建产品，数据:",JSON.stringify(e));try{const a=await k(e);console.log("创建产品API响应:",a);let t=null;if(a&&201===a.code&&a.data)t=a.data;else{if(!a||!(a.id||a.data&&a.data.id))throw new Error("API响应未包含预期的 product data 结构。响应详情: "+JSON.stringify(a));t=a.id?a:a.data}const l=t.id;if(!l)throw new Error("创建的产品没有返回有效的ID");if(Z.value.length>0){console.log(`开始上传${Z.value.length}个图片文件到新创建的产品ID: ${l}`);const e=Z.value.map((async e=>{const a=new FormData;a.append("file",e);try{const t=await fetch(`/api/v1/products/${l}/images`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")||""}`},body:a});if(!t.ok){const a=await t.json().catch((()=>({message:t.statusText})));throw new Error(`上传图片 ${e.name} 失败: ${a.message||t.statusText}`)}return await t.json()}catch(t){return console.error(`上传图片 ${e.name} 失败:`,t),b.warning(`图片 ${e.name} 上传失败: ${t.message}`),null}}));await Promise.all(e),Z.value=[]}return t}catch(a){throw console.error("创建产品及上传图片失败:",a),new Error("创建产品失败，API响应格式不正确")}})(o);console.log("产品创建成功，响应数据:",e),b.success("产品创建成功")}else{const t=await A(F.value,o);if(console.log("产品更新成功，响应数据:",t),H.value.length>0){console.log("开始删除标记的图片:",H.value);const t=H.value.map((a=>e({url:`/api/v1/product-images/${a}`,method:"DELETE"})));try{await Promise.all(t),b.success("标记的图片已删除"),H.value=[]}catch(a){console.error("删除部分标记图片失败:",a),b.error("部分已标记图片删除失败，请稍后检查"),H.value=[]}}if(Z.value.length>0){console.log(`开始上传${Z.value.length}个新图片文件到产品ID: ${F.value}`);const e=Z.value.map((async e=>{const a=new FormData;a.append("file",e);try{const t=await fetch(`/api/v1/products/${F.value}/images`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")||""}`},body:a});if(!t.ok){const a=await t.json().catch((()=>({message:t.statusText})));throw new Error(`上传图片 ${e.name} 失败: ${a.message||t.statusText}`)}return await t.json()}catch(t){return console.error(`上传图片 ${e.name} 失败:`,t),b.warning(`图片 ${e.name} 上传失败: ${t.message}`),null}}));await Promise.all(e),Z.value=[]}b.success("产品更新成功")}oe()}catch(t){throw console.error("保存或上传图片失败:",t),t}}catch(t){if(console.error("保存失败:",t),t.response){const e=t.response.status,a=t.response.data;console.error("API错误响应:",e,a);let l="保存失败";if(a&&a.message)l=`保存失败: ${a.message}`;else if(a&&a.errors){l=`数据验证失败: ${Object.entries(a.errors).map((([e,a])=>`${e}: ${Array.isArray(a)?a.join(", "):a}`)).join("; ")}`}else"string"==typeof a&&(l=`保存失败: ${a}`);b.error(l)}else b.error(`保存失败: ${t.message||"未知错误"}`)}},ne=e=>(e.cost_price=Number(e.cost_price||0),e.suggested_price=Number(e.suggested_price||0),e.min_price=Number(e.min_price||0),e.max_price=Number(e.max_price||0),e.suggested_price>0&&(0===e.min_price&&(e.min_price=e.suggested_price),0===e.max_price&&(e.max_price=e.suggested_price)),e.min_price<0&&(e.min_price=0),e.max_price<0&&(e.max_price=0),e.suggested_price<0&&(e.suggested_price=0),e.cost_price<0&&(e.cost_price=0),!0),ce=i(!1),ue=i(""),de=()=>{console.log("批量导入按钮被点击"),b.closeAll(),ce.value=!0,ue.value="",console.log("批量导入对话框可见性:",ce.value),$((()=>{console.log("nextTick后对话框可见性:",ce.value)}))},pe=()=>{if(!ue.value||!ue.value.trim())return void b.warning("请粘贴Excel数据");const e=ue.value.trim().replace(/\r\n/g,"\n").split("\n").filter((e=>""!==e.trim()));if(0===e.length)return void b.warning("未检测到有效数据行");console.log("检测到的数据行数:",e.length);let a="\t",t=!1;const l=e[0];let i;l.includes("\t")?(a="\t",t=!1):l.includes(",")?(a=",",t=!1):l.includes(";")?(a=";",t=!1):(a=/\s+/,t=!0),console.log("检测到的分隔符:","string"==typeof a?a:"空格"),i=t?l.trim().split(/\s+/).map((e=>e.trim().toLowerCase())):l.split(a).map((e=>e.trim().toLowerCase())),console.log("检测到的表头:",i);const r={specification:["规格","规格名称","specification","spec","名称","name"],cost_price:["成本价","成本","cost","cost price","cost_price","成本价格"],min_price:["最低售价","最低价格","最低价","min price","min","minimum price","min_price","最低","底价"],max_price:["最高售价","最高价格","最高价","max price","max","maximum price","max_price","最高","顶价"],suggested_price:["建议售价","市场价","建议价格","建议价","suggested price","suggested","market price","suggested_price","retail price","零售价","标价","单价","售价","价格","价","销售价","销售价格","报价","参考价","原价","定价","price","unit price","price"],tax_rate:["税率","tax","tax rate","tax_rate","增值税","税"]},o={},s=new Set;for(const[g,_]of Object.entries(r))for(let e=0;e<i.length;e++)if(!s.has(e)&&_.includes(i[e])){o[g]=e,s.add(e),console.log(`找到字段 ${g} 的精确匹配: ${i[e]}`);break}for(const[g,_]of Object.entries(r))if(void 0===o[g])for(let e=0;e<i.length;e++)if(!s.has(e)){if("suggested_price"===g&&(i[e].includes("市场")||i[e].includes("价格")||i[e].includes("单价")||i[e].includes("售价"))){o[g]=e,s.add(e),console.log(`找到字段 ${g} 的市场价特殊匹配: ${i[e]}`);break}if(_.some((a=>i[e].includes(a)))){o[g]=e,s.add(e),console.log(`找到字段 ${g} 的部分匹配: ${i[e]}`);break}}if(console.log("识别的列映射:",o),void 0===o.specification)return void b.error('无法识别表头中的"规格"列，请确保表头包含规格相关的关键词');const n=[],c=[],u=[],d=void 0!==o.suggested_price;console.log("是否找到建议售价字段:",d);for(let g=1;g<e.length;g++){const l=e[g].trim();if(!l)continue;let i;i=t?l.trim().split(/\s+/):l.split(a),i=i.map((e=>e.trim()));const r=Math.max(...[o.specification,o.cost_price,o.min_price,o.max_price,o.suggested_price,o.tax_rate].filter((e=>void 0!==e)))+1;if(i.length<r){c.push(`第 ${g+1} 行: 列数不足，需要至少 ${r} 列`);continue}const s=void 0!==o.specification?i[o.specification].trim():"";if(!s){c.push(`第 ${g+1} 行: 规格名称为空`);continue}(n.some((e=>e.specification===s))||K.value.specifications.some((e=>e.specification===s)))&&u.push(`第 ${g+1} 行: 规格名称 "${s}" 已存在，可能导致重复`);const d={specification:s,cost_price:0,min_price:0,max_price:0,suggested_price:0,tax_rate:13,is_default:!1},p=e=>{if(!e)return 0;try{const a=e.replace(/[¥$€,\s]/g,""),t=parseFloat(a);return isNaN(t)?0:t}catch(a){return console.warn("价格解析错误:",a),0}};let m=0;if(void 0!==o.suggested_price&&o.suggested_price<i.length){const e=i[o.suggested_price];d.suggested_price=p(e),m=d.suggested_price}if(void 0!==o.cost_price&&o.cost_price<i.length){const e=i[o.cost_price];d.cost_price=p(e)}if(void 0!==o.min_price&&o.min_price<i.length){const e=i[o.min_price];d.min_price=p(e)}else m>0&&(d.min_price=m);if(void 0!==o.max_price&&o.max_price<i.length){const e=i[o.max_price];d.max_price=p(e)}else m>0&&(d.max_price=m);if(void 0!==o.tax_rate&&o.tax_rate<i.length){const e=i[o.tax_rate],a=null==e?void 0:e.replace(/%/g,"").trim();d.tax_rate=parseFloat(a||"13")||13}d.cost_price<0&&(d.cost_price=0),d.min_price<0&&(d.min_price=0),d.max_price<0&&(d.max_price=0),d.suggested_price<0&&(d.suggested_price=0),d.tax_rate<0&&(d.tax_rate=0),d.tax_rate>100&&(d.tax_rate=100),0===d.suggested_price&&(d.max_price>0?d.suggested_price=d.max_price:d.min_price>0&&(d.suggested_price=d.min_price)),d.suggested_price>0&&(0===d.min_price&&(d.min_price=d.suggested_price),0===d.max_price&&(d.max_price=d.suggested_price)),n.push(d)}if(0===n.length)return void b.error("没有解析出有效的规格数据");!K.value.specifications.some((e=>e.is_default))&&n.length>0&&(n[0].is_default=!0),K.value.specifications=[...K.value.specifications,...n];let p=`成功导入 ${n.length} 个规格`,m=`识别到的字段：${Object.keys(o).map((e=>({specification:"规格名称",cost_price:"成本价",min_price:"最低售价",max_price:"最高售价",suggested_price:"建议售价/市场价",tax_rate:"税率"}[e]||e))).join(", ")}`;if(console.log(m),void 0!==o.suggested_price){const e=i[o.suggested_price];console.log(`市场价列识别结果: 表头 "${e}" 被识别为建议售价`),m+=`\n表头 "${e}" 已识别为建议售价`}else console.log("未找到市场价/建议售价相关列"),n.length>0&&n.some((e=>e.suggested_price>0))&&(m+="\n虽然未找到市场价/建议售价列，但已通过其他值推断设置");if(c.length>0&&(p+=`，${c.length} 行失败`),u.length>0&&(p+=`，${u.length} 个警告`),b.success(p),b.info({message:m,duration:5e3}),c.length>0){const e=`以下 ${c.length} 行导入失败:\n${c.join("\n")}`;y.alert(e,"部分行导入失败",{confirmButtonText:"确定"})}u.length>0&&setTimeout((()=>{const e=`以下是导入过程中的警告:\n${u.join("\n")}`;y.alert(e,"导入警告",{confirmButtonText:"确定",type:"warning"})}),c.length>0?1e3:0),ce.value=!1};return o((()=>{re(),(async()=>{try{const e=ie(await N({per_page:999}));e&&e.items&&Array.isArray(e.items)?z.value=e.items:Array.isArray(e)?z.value=e:z.value=[]}catch(e){console.error("获取品牌失败:",e),z.value=[]}})(),W.value||(async()=>{if(F.value)try{const e=await U(F.value);console.log("原始响应数据:",e);const a=ie(e);let t;if(console.log("解析后数据 (from getProduct):",a),a&&a.data)t=a.data;else{if(!a||"object"!=typeof a||!a.id)throw new Error("无效的API响应格式，未能找到产品数据");t=a}if(!t||!t.id)throw new Error("未找到有效的产品数据");console.log("产品数据:",t);let l=t.status||"active";if("正常"===l&&(l="active"),"禁用"===l&&(l="inactive"),Array.isArray(t.images)){const e=t.images.map((e=>{let a=e.url;if(a&&!a.startsWith("http")&&!a.startsWith("data:"))if(a.startsWith("/")||(a="/"+a),a.includes("uploads/products/")){const e={}.VITE_API_URL||window.location.origin;a.includes("/api/")||(a=`${e}/api/v1/static/${a.replace(/^\/+/,"")}`)}else{const e={}.VITE_API_URL||window.location.origin;a=e.endsWith("/")&&a.startsWith("/")?e+a.substring(1):e+a}return{...e,url:a}}));M.value=e.map((e=>({id:e.id,name:e.file_name||"产品图片",url:e.url,status:"success",uid:e.id}))),K.value.images=e.map((e=>({id:e.id,url:e.url,name:e.file_name||"产品图片",is_main:e.is_main}))),console.log("处理后的图片列表:",M.value)}else console.log("产品没有图片或图片数据格式不正确"),M.value=[],K.value.images=[];let i=[];Array.isArray(t.specifications)?i=t.specifications:t.specifications&&"object"==typeof t.specifications?i=[t.specifications]:(console.warn("未找到有效的规格数据，创建默认规格"),i=[{specification:"默认规格",cost_price:0,min_price:0,max_price:0,suggested_price:0,tax_rate:13,is_default:!0}]);let r=[];Array.isArray(t.attributes)?r=t.attributes:console.warn("未找到有效的属性数据"),K.value={id:t.id||null,name:t.name||"",model:t.model||"",category_id:t.category_id||null,brand_id:t.brand_id||null,unit:t.unit||"个",status:l,description:t.description||"",notes:t.notes||"",specifications:i,attributes:r,images:K.value.images}}catch(e){console.error("获取产品数据失败:",e),b.error(`获取产品数据失败: ${e.message||"未知错误"}`)}})()})),(e,a)=>{const t=s("el-button"),l=s("el-card"),i=s("el-input"),r=s("el-form-item"),o=s("el-col"),y=s("el-row"),$=s("el-option"),U=s("el-select"),A=s("el-radio"),k=s("el-radio-group"),N=s("el-tab-pane"),j=s("el-icon"),R=s("el-button-group"),F=s("el-table-column"),Z=s("el-input-number"),H=s("el-switch"),ie=s("el-table"),re=s("el-upload"),me=s("el-dialog"),ge=s("el-tabs"),_e=s("el-form"),fe=s("el-divider");return n(),c("div",C,[u(l,{class:"header-card mb-20"},{default:d((()=>[p("div",I,[p("h2",E,m(W.value?"新增产品":"编辑产品"),1),p("div",null,[u(t,{onClick:oe},{default:d((()=>a[13]||(a[13]=[g("返回")]))),_:1}),u(t,{type:"primary",onClick:se},{default:d((()=>a[14]||(a[14]=[g("保存")]))),_:1})])])])),_:1}),u(_e,{ref_key:"formRef",ref:L,model:K.value,rules:Q,"label-width":"120px",class:"product-form"},{default:d((()=>[u(ge,{modelValue:B.value,"onUpdate:modelValue":a[9]||(a[9]=e=>B.value=e)},{default:d((()=>[u(N,{label:"基本信息",name:"basic"},{default:d((()=>[u(l,null,{default:d((()=>[u(y,{gutter:20},{default:d((()=>[u(o,{span:12},{default:d((()=>[u(r,{label:"产品名称",prop:"name"},{default:d((()=>[u(i,{modelValue:K.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>K.value.name=e),placeholder:"请输入产品名称"},null,8,["modelValue"])])),_:1})])),_:1}),u(o,{span:12},{default:d((()=>[u(r,{label:"产品型号",prop:"model"},{default:d((()=>[u(i,{modelValue:K.value.model,"onUpdate:modelValue":a[1]||(a[1]=e=>K.value.model=e),placeholder:"请输入产品型号"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(y,{gutter:20},{default:d((()=>[u(o,{span:12},{default:d((()=>[u(r,{label:"产品分类",prop:"category_id"},{default:d((()=>[u(U,{modelValue:K.value.category_id,"onUpdate:modelValue":a[2]||(a[2]=e=>K.value.category_id=e),placeholder:"请选择产品分类",filterable:"",clearable:""},{default:d((()=>[(n(!0),c(_,null,f(J.value,(e=>(n(),x($,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),u(o,{span:12},{default:d((()=>[u(r,{label:"品牌",prop:"brand_id"},{default:d((()=>[u(U,{modelValue:K.value.brand_id,"onUpdate:modelValue":a[3]||(a[3]=e=>K.value.brand_id=e),placeholder:"请选择品牌",filterable:"",clearable:""},{default:d((()=>[(n(!0),c(_,null,f(z.value,(e=>(n(),x($,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(y,{gutter:20},{default:d((()=>[u(o,{span:12},{default:d((()=>[u(r,{label:"计量单位",prop:"unit"},{default:d((()=>[u(i,{modelValue:K.value.unit,"onUpdate:modelValue":a[4]||(a[4]=e=>K.value.unit=e),placeholder:"例如：个、台、米"},null,8,["modelValue"])])),_:1})])),_:1}),u(o,{span:12},{default:d((()=>[u(r,{label:"状态",prop:"status"},{default:d((()=>[u(k,{modelValue:K.value.status,"onUpdate:modelValue":a[5]||(a[5]=e=>K.value.status=e)},{default:d((()=>[u(A,{value:"active"},{default:d((()=>a[15]||(a[15]=[g("正常")]))),_:1}),u(A,{value:"inactive"},{default:d((()=>a[16]||(a[16]=[g("禁用")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(y,{gutter:20},{default:d((()=>[u(o,{span:24},{default:d((()=>[u(r,{label:"产品描述"},{default:d((()=>[u(i,{modelValue:K.value.description,"onUpdate:modelValue":a[6]||(a[6]=e=>K.value.description=e),type:"textarea",rows:3,placeholder:"请输入产品描述信息"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(y,{gutter:20},{default:d((()=>[u(o,{span:24},{default:d((()=>[u(r,{label:"备注"},{default:d((()=>[u(i,{modelValue:K.value.notes,"onUpdate:modelValue":a[7]||(a[7]=e=>K.value.notes=e),type:"textarea",rows:2,placeholder:"其他补充信息"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),u(N,{label:"规格价格",name:"specs"},{default:d((()=>[u(l,null,{default:d((()=>[p("div",P,[u(R,null,{default:d((()=>[u(t,{type:"primary",onClick:X},{default:d((()=>[u(j,null,{default:d((()=>[u(v(w))])),_:1}),a[17]||(a[17]=g("添加规格 "))])),_:1}),u(t,{type:"success",onClick:h(de,["prevent","stop"]),title:"从Excel复制数据并粘贴到对话框中"},{default:d((()=>[u(j,null,{default:d((()=>[u(v(V))])),_:1}),a[18]||(a[18]=g("批量导入 "))])),_:1})])),_:1})]),u(ie,{data:K.value.specifications,border:"",stripe:""},{default:d((()=>[u(F,{label:"规格名称",prop:"specification","min-width":"180"},{default:d((({row:e})=>[u(i,{modelValue:e.specification,"onUpdate:modelValue":a=>e.specification=a,placeholder:"请输入规格名称",onChange:a=>(e=>e.specification?!(K.value.specifications.filter((a=>a.specification===e.specification)).length>1&&(b.warning("规格名称不能重复"),1)):(b.warning("规格名称不能为空"),!1))(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),u(F,{label:"成本价(元)",prop:"cost_price",width:"120"},{default:d((({row:e})=>[u(Z,{modelValue:e.cost_price,"onUpdate:modelValue":a=>e.cost_price=a,min:0,precision:2,step:.1,"controls-position":"right",placeholder:"成本价",onChange:()=>ne(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),u(F,{label:"最低售价(元)",prop:"min_price",width:"120"},{default:d((({row:e})=>[u(Z,{modelValue:e.min_price,"onUpdate:modelValue":a=>e.min_price=a,min:0,precision:2,step:.1,"controls-position":"right",placeholder:"最低售价",onChange:()=>ne(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),u(F,{label:"最高售价(元)",prop:"max_price",width:"120"},{default:d((({row:e})=>[u(Z,{modelValue:e.max_price,"onUpdate:modelValue":a=>e.max_price=a,min:0,precision:2,step:.1,"controls-position":"right",placeholder:"最高售价",onChange:()=>ne(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),u(F,{label:"建议售价(元)",prop:"suggested_price",width:"120"},{default:d((({row:e})=>[u(Z,{modelValue:e.suggested_price,"onUpdate:modelValue":a=>e.suggested_price=a,min:0,precision:2,step:.1,"controls-position":"right",placeholder:"建议售价",onChange:()=>ne(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),u(F,{label:"税率(%)",prop:"tax_rate",width:"120"},{default:d((({row:e})=>[u(Z,{modelValue:e.tax_rate,"onUpdate:modelValue":a=>e.tax_rate=a,min:0,max:100,precision:2,step:1,"controls-position":"right",placeholder:"税率"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),u(F,{label:"默认规格",prop:"is_default",width:"100"},{default:d((({row:e,$index:a})=>[u(H,{modelValue:e.is_default,"onUpdate:modelValue":a=>e.is_default=a,onChange:e=>((e,a)=>{e&&K.value.specifications.forEach(((e,t)=>{t!==a&&(e.is_default=!1)}))})(e,a)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),u(F,{label:"操作",width:"100",fixed:"right"},{default:d((({$index:e})=>[u(t,{type:"danger",size:"small",icon:"Delete",circle:"",onClick:a=>{return t=e,void K.value.specifications.splice(t,1);var t}},null,8,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1})])),_:1}),u(N,{label:"产品属性",name:"attributes"},{default:d((()=>[u(l,null,{default:d((()=>[p("div",T,[u(t,{type:"primary",onClick:Y},{default:d((()=>a[19]||(a[19]=[g("添加属性")]))),_:1})]),u(ie,{data:K.value.attributes,border:"",stripe:""},{default:d((()=>[u(F,{label:"属性名称",prop:"attribute_name","min-width":"180"},{default:d((({row:e})=>[u(i,{modelValue:e.attribute_name,"onUpdate:modelValue":a=>e.attribute_name=a,placeholder:"如：材质、颜色等"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),u(F,{label:"属性值",prop:"attribute_value","min-width":"180"},{default:d((({row:e})=>[u(i,{modelValue:e.attribute_value,"onUpdate:modelValue":a=>e.attribute_value=a,placeholder:"如：不锈钢、红色等"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),u(F,{label:"操作",width:"100",fixed:"right"},{default:d((({$index:e})=>[u(t,{type:"danger",size:"small",icon:"Delete",circle:"",onClick:a=>{return t=e,void K.value.attributes.splice(t,1);var t}},null,8,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1})])),_:1}),u(N,{label:"产品图片",name:"images"},{default:d((()=>[u(l,null,{default:d((()=>[a[20]||(a[20]=p("div",{class:"toolbar mb-20"},[p("p",{class:"upload-tip"},"首个图片默认为主图，支持jpg、png、jpeg格式，单个文件不超过1MB")],-1)),p("div",O,[u(re,{action:"#","list-type":"picture-card","file-list":M.value,"on-preview":ee,"on-remove":ae,"on-change":te,"auto-upload":!1,"before-upload":le,name:"file",multiple:"","show-file-list":!0},{default:d((()=>[u(j,null,{default:d((()=>[u(v(w))])),_:1})])),_:1},8,["file-list"]),u(me,{modelValue:G.value,"onUpdate:modelValue":a[8]||(a[8]=e=>G.value=e),title:"图片预览"},{default:d((()=>[p("img",{"w-full":"",src:q.value,alt:"预览图",style:{"max-width":"100%"}},null,8,D)])),_:1},8,["modelValue"])])])),_:1})])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["model"]),u(me,{modelValue:ce.value,"onUpdate:modelValue":a[12]||(a[12]=e=>ce.value=e),title:"批量导入规格",width:"600px","destroy-on-close":""},{footer:d((()=>[p("div",S,[u(t,{onClick:a[11]||(a[11]=e=>ce.value=!1)},{default:d((()=>a[22]||(a[22]=[g("取消")]))),_:1}),u(t,{type:"primary",onClick:pe},{default:d((()=>a[23]||(a[23]=[g("确定导入")]))),_:1})])])),default:d((()=>[a[24]||(a[24]=p("div",{class:"example-tip mb-10"},[p("p",null,[p("strong",null,"示例数据格式：")]),p("p",{class:"import-tip"},"直接从Excel中选择并复制数据（包含表头行），然后粘贴到下方文本框。系统将自动识别Tab、逗号或空格分隔符。"),p("div",{class:"example-code"},"规格名称    成本价    最低售价    最高售价    建议售价/市场价    税率 规格1    100    120    150    130    13% 规格2    200    220    280    250    13% 16GB 黑色    300    350    400    380    13%"),p("p",{class:"import-tip"},'注意：表头需包含与"规格"、"成本价"、"最低售价"、"最高售价"、"建议售价"、"税率"相关的关键词，系统会自动匹配。'),p("p",{class:"import-tip",style:{color:"#E6A23C"}},[p("strong",null,"特别提示："),g(' "市场价"、"单价"、"售价"、"价格"等字段都会被识别为'),p("strong",null,"建议售价"),g("。")])],-1)),u(fe,{"content-position":"center"},{default:d((()=>a[21]||(a[21]=[g("在此粘贴")]))),_:1}),u(i,{modelValue:ue.value,"onUpdate:modelValue":a[10]||(a[10]=e=>ue.value=e),type:"textarea",rows:10,placeholder:"在此粘贴从Excel复制的内容（包含表头行）",spellcheck:!1},null,8,["modelValue"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-a99a8a14"]]);export{R as default};
