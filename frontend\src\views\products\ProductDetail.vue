<template>
  <div class="product-detail" v-loading="loading">
    <!-- 页面头部 -->
    <el-card class="mb-20">
      <div class="flex-between">
        <div>
          <el-button @click="$router.back()">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
        <div>
          <el-button type="primary" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑产品
          </el-button>
          <el-button type="danger" @click="handleDelete">
            <el-icon><Delete /></el-icon>
            删除产品
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 产品基本信息 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>产品基本信息</span>
        </div>
      </template>
      
      <el-descriptions :column="2" border v-if="product.id">
        <el-descriptions-item label="产品编号">{{ product.id }}</el-descriptions-item>
        <el-descriptions-item label="产品名称">{{ product.name }}</el-descriptions-item>
        <el-descriptions-item label="产品型号">{{ product.model }}</el-descriptions-item>
        <el-descriptions-item label="产品单位">{{ product.unit }}</el-descriptions-item>
        <el-descriptions-item label="产品分类">{{ product.category_name || '未分类' }}</el-descriptions-item>
        <el-descriptions-item label="产品品牌">{{ product.brand_name || '无品牌' }}</el-descriptions-item>
        <el-descriptions-item label="产品状态">
          <el-tag :type="product.status === '正常' ? 'success' : 'danger'">
            {{ product.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="价格区间">{{ product.price_range || '暂无价格' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(product.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(product.updated_at) }}</el-descriptions-item>
        <el-descriptions-item label="产品描述" :span="2">{{ product.description || '暂无描述' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ product.notes || '暂无备注' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 产品图片 -->
    <el-card class="mb-20" v-if="product.images && product.images.length > 0">
      <template #header>
        <div class="card-header">
          <span>产品图片</span>
        </div>
      </template>
      
      <div class="product-images">
        <el-image
          v-for="image in product.images"
          :key="image.id"
          :src="image.image_url"
          :preview-src-list="imagePreviewList"
          fit="cover"
          class="product-image"
        >
          <template #error>
            <div class="image-slot">
              <el-icon><Picture /></el-icon>
            </div>
          </template>
        </el-image>
      </div>
    </el-card>

    <!-- 产品规格信息 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>产品规格信息</span>
        </div>
      </template>

      <div v-if="!product.specifications || product.specifications.length === 0" class="text-center text-gray-500 py-8">
        暂无规格信息
      </div>

      <el-table v-else :data="product.specifications" border stripe>
        <el-table-column prop="specification" label="规格描述" min-width="150" show-overflow-tooltip />
        <el-table-column prop="cost_price" label="成本价" width="110" align="right">
          <template #default="{ row }">
            <span v-if="row.cost_price" style="color: #E6A23C; font-weight: 500;">¥{{ row.cost_price }}</span>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="suggested_price" label="建议售价" width="110" align="right">
          <template #default="{ row }">
            <span v-if="row.suggested_price" style="color: #67C23A; font-weight: 500;">¥{{ row.suggested_price }}</span>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="min_price" label="最低售价" width="100" align="right">
          <template #default="{ row }">
            <span v-if="row.min_price" style="color: #909399;">¥{{ row.min_price }}</span>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="max_price" label="最高售价" width="100" align="right">
          <template #default="{ row }">
            <span v-if="row.max_price" style="color: #909399;">¥{{ row.max_price }}</span>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="tax_rate" label="税率" width="80" align="center">
          <template #default="{ row }">
            <span v-if="row.tax_rate" style="color: #409EFF;">{{ row.tax_rate }}%</span>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="is_default" label="默认规格" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.is_default" type="success" size="small">默认</el-tag>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.notes" style="color: #606266;">{{ row.notes }}</span>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 产品属性信息 -->
    <el-card class="mb-20" v-if="product.attributes && product.attributes.length > 0">
      <template #header>
        <div class="card-header">
          <span>产品属性信息</span>
        </div>
      </template>
      
      <el-table :data="product.attributes" border stripe>
        <el-table-column prop="attribute_name" label="属性名称" />
        <el-table-column prop="attribute_value" label="属性值" />
        <el-table-column prop="notes" label="备注" />
      </el-table>
    </el-card>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  Delete,
  Picture
} from '@element-plus/icons-vue'
import { productApi } from '@/api/product'

const route = useRoute()
const router = useRouter()

// 数据状态
const loading = ref(false)
const product = ref<any>({})

// 计算属性
const imagePreviewList = computed(() => {
  if (!product.value.images) return []
  return product.value.images.map((img: any) => img.image_url)
})

// 获取产品详情
const getProductDetail = async () => {
  try {
    loading.value = true
    const productId = Number(route.params.id)
    const response = await productApi.getById(productId) as any
    product.value = response.data || response
  } catch (error) {
    console.error('获取产品详情失败:', error)
    ElMessage.error('获取产品详情失败')
  } finally {
    loading.value = false
  }
}

// 操作处理
const handleEdit = () => {
  router.push(`/products/edit/${product.value.id}`)
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除产品"${product.value.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await productApi.delete(product.value.id)
    ElMessage.success('删除成功')
    router.push('/products')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除产品失败:', error)
      ElMessage.error('删除产品失败')
    }
  }
}



// 工具函数
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 初始化
onMounted(() => {
  getProductDetail()
})
</script>

<style lang="scss" scoped>
.product-detail {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .product-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    
    .product-image {
      width: 150px;
      height: 150px;
      border-radius: 8px;
      overflow: hidden;
      
      .image-slot {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        color: #909399;
        font-size: 30px;
      }
    }
  }
}
</style>
