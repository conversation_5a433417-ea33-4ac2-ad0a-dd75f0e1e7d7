import{U as r}from"./index-3d4c440c.js";const e=(e,o="操作失败")=>{var s,n,a,t;console.error("API Error:",e);const l=e.message||(null==(n=null==(s=e.response)?void 0:s.data)?void 0:n.message)||o,c=e.errors||(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.errors);if(c){const e=Object.values(c)[0];if(Array.isArray(e)&&e.length>0)return void r.error(e[0])}r.error(l)},o=(r,e)=>{console.group(`Error in ${e}`),console.error("Error object:",r),console.error("Stack trace:",r.stack),r.response&&(console.error("Response data:",r.response.data),console.error("Response status:",r.response.status),console.error("Response headers:",r.response.headers)),console.groupEnd()};export{e as h,o as l};
