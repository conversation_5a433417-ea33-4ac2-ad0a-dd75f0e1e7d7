import{C as e}from"./CustomerForm-dce303b8.js";import{d as a,l,k as s}from"./customer-471ca075.js";import{J as t,r as o,d as i,w as r,f as u,o as c,g as n,h as d,a as m,i as v,k as p,U as f}from"./index-3d4c440c.js";import{_ as g}from"./_plugin-vue_export-helper-1b428a4d.js";const _={class:"dialog-footer"},y=g(t({__name:"CustomerEditDialog",props:{visible:{type:Boolean},customerId:{},initialData:{}},emits:["update:visible","success"],setup(t,{emit:g}){const y=t,b=g,w=o(null),C=o(!1),I=o(!1),k=i((()=>!!y.customerId)),h=i((()=>k.value?"编辑客户":"新增客户")),j=o(null);r((()=>y.visible),(async e=>{if(e)if(k.value&&y.customerId){C.value=!0;try{j.value=await a(y.customerId)}catch(l){f.error("获取客户信息失败"),console.error(l)}finally{C.value=!1}}else j.value=y.initialData||null}));const D=async()=>{if(!w.value)return;if(await w.value.validate()){I.value=!0;try{const e=w.value.getFormData();let a;k.value&&y.customerId?(a=await l(y.customerId,e),f.success("客户更新成功")):(a=await s(e),f.success("客户创建成功")),b("success",a),b("update:visible",!1)}catch(e){console.error("保存客户失败:",e),f.error(e.message||"保存客户失败")}finally{I.value=!1}}else f.warning("表单验证失败，请检查输入")},x=()=>{j.value=null};return(a,l)=>{const s=u("el-button"),t=u("el-dialog");return c(),n(t,{"model-value":a.visible,title:h.value,width:"60%","close-on-click-modal":!1,"onUpdate:modelValue":l[1]||(l[1]=e=>a.$emit("update:visible",e)),onClosed:x},{footer:d((()=>[m("span",_,[v(s,{onClick:l[0]||(l[0]=e=>a.$emit("update:visible",!1))},{default:d((()=>l[2]||(l[2]=[p("取消")]))),_:1}),v(s,{type:"primary",onClick:D,loading:I.value},{default:d((()=>l[3]||(l[3]=[p(" 保存 ")]))),_:1},8,["loading"])])])),default:d((()=>[v(e,{ref_key:"customerFormRef",ref:w,"customer-data":j.value,loading:C.value},null,8,["customer-data","loading"])])),_:1},8,["model-value","title"])}}}),[["__scopeId","data-v-ed59c418"]]);export{y as C};
