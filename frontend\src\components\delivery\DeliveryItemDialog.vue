<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择发货商品"
    width="800px"
    destroy-on-close
    @closed="handleClosed"
  >
    <div v-loading="loading">
      <el-table
        ref="tableRef"
        :data="orderItems"
        border
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="product_name" label="产品名称" min-width="150" />
        <el-table-column prop="product_model" label="产品型号" width="120" />
        <el-table-column prop="product_spec" label="规格" min-width="120" />
        <el-table-column prop="order_quantity" label="订单数量" width="100" align="center" />
        <el-table-column prop="delivered_quantity" label="已发货" width="100" align="center">
          <template #default="{ row }">
            {{ row.delivered_quantity || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="available_quantity" label="可发货" width="100" align="center">
          <template #default="{ row }">
            {{ (row.order_quantity || 0) - (row.delivered_quantity || 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column label="发货数量" width="120">
          <template #default="{ row }">
            <el-input-number 
              v-model="row.delivery_quantity" 
              :min="1" 
              :max="(row.order_quantity || 0) - (row.delivered_quantity || 0)"
              :precision="0"
              size="small"
              style="width: 100%"
            />
          </template>
        </el-table-column>
      </el-table>

      <div v-if="!orderItems.length && !loading" class="empty-data">
        <el-empty description="该订单暂无可发货的商品" />
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :disabled="!selectedItems.length">
        确定添加 ({{ selectedItems.length }})
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { orderApi } from '@/api/order'

// Props
interface Props {
  visible: boolean
  orderId?: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: undefined
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'submit': [items: any[]]
}>()

// 表格引用
const tableRef = ref()

// 状态
const loading = ref(false)
const orderItems = ref([])
const selectedItems = ref([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听订单ID变化，获取订单商品
watch(() => props.orderId, (newOrderId) => {
  if (newOrderId && props.visible) {
    getOrderItems()
  }
}, { immediate: true })

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.orderId) {
    getOrderItems()
  }
})

// 获取订单商品列表
const getOrderItems = async () => {
  if (!props.orderId) return
  
  loading.value = true
  try {
    const response = await orderApi.getItems(props.orderId) as any
    let items = []
    
    if (Array.isArray(response)) {
      items = response
    } else if (response && typeof response === 'object') {
      items = response.data || response.items || []
    }
    
    // 处理商品数据，添加发货数量字段
    orderItems.value = items.map((item: any) => ({
      ...item,
      delivery_quantity: Math.min(1, (item.order_quantity || 0) - (item.delivered_quantity || 0)),
      available_quantity: (item.order_quantity || 0) - (item.delivered_quantity || 0)
    })).filter((item: any) => item.available_quantity > 0) // 只显示可发货的商品
    
  } catch (error) {
    console.error('获取订单商品失败:', error)
    ElMessage.error('获取订单商品失败')
    orderItems.value = []
  } finally {
    loading.value = false
  }
}

// 多选变化
const handleSelectionChange = (rows: any[]) => {
  selectedItems.value = rows
  
  // 为新选中的商品设置默认发货数量
  rows.forEach(row => {
    if (!row.delivery_quantity || row.delivery_quantity <= 0) {
      row.delivery_quantity = Math.min(1, row.available_quantity)
    }
  })
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理关闭
const handleClosed = () => {
  // 重置数据
  orderItems.value = []
  selectedItems.value = []
  tableRef.value?.clearSelection()
}

// 处理提交
const handleSubmit = () => {
  if (!selectedItems.value.length) {
    ElMessage.warning('请选择要发货的商品')
    return
  }
  
  // 验证发货数量
  const invalidItems = selectedItems.value.filter((item: any) => 
    !item.delivery_quantity || 
    item.delivery_quantity <= 0 || 
    item.delivery_quantity > item.available_quantity
  )
  
  if (invalidItems.length > 0) {
    ElMessage.warning('请检查发货数量，确保数量大于0且不超过可发货数量')
    return
  }
  
  // 准备提交数据
  const submitItems = selectedItems.value.map((item: any) => ({
    order_item_id: item.id,
    product_id: item.product_id,
    product_name: item.product_name,
    product_model: item.product_model,
    product_spec: item.product_spec || item.specification_description,
    quantity: item.delivery_quantity,
    unit: item.unit || item.product_unit,
    notes: '',
    available_quantity: item.available_quantity
  }))
  
  // 触发提交事件
  emit('submit', submitItems)
  
  // 关闭对话框
  dialogVisible.value = false
  
  ElMessage.success(`已添加 ${submitItems.length} 个商品`)
}
</script>

<style lang="scss" scoped>
.empty-data {
  padding: 40px 0;
  text-align: center;
}

:deep(.el-table) {
  .el-input-number {
    .el-input__inner {
      text-align: center;
    }
  }
}
</style>
