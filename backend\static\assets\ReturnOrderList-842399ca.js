import{J as e,b as a,r as l,L as t,e as r,f as n,M as o,o as s,c as u,a as i,i as d,h as c,k as p,N as m,g as _,t as f,l as y,j as v,U as h,W as w}from"./index-3d4c440c.js";import{a as g,u as b,d as k}from"./return-614d16fd.js";import{f as C,a as V}from"./format-552375ee.js";import{_ as j}from"./_plugin-vue_export-helper-1b428a4d.js";const z={class:"return-order-list"},U={class:"page-header"},x={class:"pagination-container"},Y=j(e({__name:"ReturnOrderList",setup(e){const j=a(),Y=l(!1),M=l([]),D=l(1),L=l(20),O=l(0),R=t({return_number:"",status:void 0,start_date:"",end_date:""}),S=l(null),$=async()=>{var e;Y.value=!0;try{const a={...R,page:D.value,per_page:L.value},l=await g(a),t=l.data||l;M.value=t.list||[],O.value=(null==(e=t.pagination)?void 0:e.total)||0}catch(a){console.error("获取退货单列表失败:",a),h.error("获取退货单列表失败")}finally{Y.value=!1}},E=()=>{S.value?(R.start_date=S.value[0],R.end_date=S.value[1]):(R.start_date="",R.end_date=""),D.value=1,$()},I=()=>{Object.keys(R).forEach((e=>{R[e]=""})),S.value=null,D.value=1,$()},J=e=>{L.value=e,$()},N=e=>{D.value=e,$()},P=()=>{j.push("/return-orders/create")};return r((()=>{$()})),(e,a)=>{const l=n("el-button"),t=n("el-input"),r=n("el-form-item"),g=n("el-option"),W=n("el-select"),q=n("el-date-picker"),A=n("el-form"),B=n("el-table-column"),F=n("el-tag"),G=n("el-table"),H=n("el-pagination"),K=o("loading");return s(),u("div",z,[i("div",U,[a[6]||(a[6]=i("h2",null,"退货单管理",-1)),d(l,{type:"primary",onClick:P},{default:c((()=>a[5]||(a[5]=[p("新建退货单")]))),_:1})]),d(A,{model:R,inline:"",class:"search-form"},{default:c((()=>[d(r,{label:"退货单号"},{default:c((()=>[d(t,{modelValue:R.return_number,"onUpdate:modelValue":a[0]||(a[0]=e=>R.return_number=e),placeholder:"请输入退货单号",clearable:""},null,8,["modelValue"])])),_:1}),d(r,{label:"状态"},{default:c((()=>[d(W,{modelValue:R.status,"onUpdate:modelValue":a[1]||(a[1]=e=>R.status=e),placeholder:"请选择状态",clearable:""},{default:c((()=>[d(g,{label:"待确认",value:"待确认"}),d(g,{label:"已确认",value:"已确认"}),d(g,{label:"已完成",value:"已完成"}),d(g,{label:"已取消",value:"已取消"})])),_:1},8,["modelValue"])])),_:1}),d(r,{label:"日期范围"},{default:c((()=>[d(q,{modelValue:S.value,"onUpdate:modelValue":a[2]||(a[2]=e=>S.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),d(r,null,{default:c((()=>[d(l,{type:"primary",onClick:E},{default:c((()=>a[7]||(a[7]=[p("搜索")]))),_:1}),d(l,{onClick:I},{default:c((()=>a[8]||(a[8]=[p("重置")]))),_:1})])),_:1})])),_:1},8,["model"]),m((s(),_(G,{data:M.value,border:"",style:{width:"100%"}},{default:c((()=>[d(B,{prop:"return_number",label:"退货单号","min-width":"120"}),d(B,{prop:"delivery_number",label:"关联送货单号","min-width":"120"}),d(B,{prop:"project_name",label:"项目名称","min-width":"120","show-overflow-tooltip":""}),d(B,{prop:"return_date",label:"退货日期","min-width":"100"},{default:c((({row:e})=>[p(f(y(C)(e.return_date)),1)])),_:1}),d(B,{prop:"status",label:"状态","min-width":"80"},{default:c((({row:e})=>{return[d(F,{type:(a=e.status,{"待确认":"warning","已确认":"primary","已完成":"success","已取消":"info"}[a||"待确认"])},{default:c((()=>[p(f(e.status),1)])),_:2},1032,["type"])];var a})),_:1}),d(B,{prop:"reason",label:"退货原因","min-width":"150","show-overflow-tooltip":""}),d(B,{prop:"created_at",label:"创建时间","min-width":"150"},{default:c((({row:e})=>[p(f(y(V)(e.created_at)),1)])),_:1}),d(B,{label:"操作",fixed:"right",width:"200"},{default:c((({row:e})=>[d(l,{link:"",type:"primary",onClick:a=>(e=>{j.push(`/return-orders/${e.id}`)})(e)},{default:c((()=>a[9]||(a[9]=[p("查看")]))),_:2},1032,["onClick"]),"待确认"===e.status?(s(),_(l,{key:0,link:"",type:"primary",onClick:a=>(e=>{j.push(`/return-orders/${e.id}/edit`)})(e)},{default:c((()=>a[10]||(a[10]=[p("编辑")]))),_:2},1032,["onClick"])):v("",!0),"待确认"===e.status?(s(),_(l,{key:1,link:"",type:"primary",onClick:a=>(async e=>{try{await w.confirm("确认要确认此退货单吗？","提示",{type:"warning"}),await b(e.id,"已确认"),h.success("退货单已确认"),$()}catch(a){"cancel"!==a&&(console.error("确认退货单失败:",a),h.error("确认退货单失败"))}})(e)},{default:c((()=>a[11]||(a[11]=[p("确认")]))),_:2},1032,["onClick"])):v("",!0),"已确认"===e.status?(s(),_(l,{key:2,link:"",type:"primary",onClick:a=>(async e=>{try{await w.confirm("确认要完成此退货单吗？","提示",{type:"warning"}),await b(e.id,"已完成"),h.success("退货单已完成"),$()}catch(a){"cancel"!==a&&(console.error("完成退货单失败:",a),h.error("完成退货单失败"))}})(e)},{default:c((()=>a[12]||(a[12]=[p("完成")]))),_:2},1032,["onClick"])):v("",!0),["待确认","已确认"].includes(e.status)?(s(),_(l,{key:3,link:"",type:"danger",onClick:a=>(async e=>{try{await w.confirm("确认要取消此退货单吗？","提示",{type:"warning"}),await b(e.id,"已取消"),h.success("退货单已取消"),$()}catch(a){"cancel"!==a&&(console.error("取消退货单失败:",a),h.error("取消退货单失败"))}})(e)},{default:c((()=>a[13]||(a[13]=[p("取消")]))),_:2},1032,["onClick"])):v("",!0),"待确认"===e.status?(s(),_(l,{key:4,link:"",type:"danger",onClick:a=>(async e=>{try{await w.confirm("确认要删除此退货单吗？此操作不可恢复！","警告",{type:"warning"}),await k(e.id),h.success("退货单已删除"),$()}catch(a){"cancel"!==a&&(console.error("删除退货单失败:",a),h.error("删除退货单失败"))}})(e)},{default:c((()=>a[14]||(a[14]=[p("删除")]))),_:2},1032,["onClick"])):v("",!0)])),_:1})])),_:1},8,["data"])),[[K,Y.value]]),i("div",x,[d(H,{"current-page":D.value,"onUpdate:currentPage":a[3]||(a[3]=e=>D.value=e),"page-size":L.value,"onUpdate:pageSize":a[4]||(a[4]=e=>L.value=e),total:O.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:J,onCurrentChange:N},null,8,["current-page","page-size","total"])])])}}}),[["__scopeId","data-v-5702dedb"]]);export{Y as default};
