import{J as e,b as a,L as t,r as l,e as n,f as r,M as o,o as s,c as i,i as u,h as d,F as c,m as p,k as m,l as g,N as _,g as f,t as y,a8 as b,j as h,U as v,W as w,Z as k,$ as C,ad as S,a4 as N}from"./index-3d4c440c.js";import{a as x,b as z,d as T,u as $}from"./order-d6035a15.js";import{s as V}from"./customer-471ca075.js";import{h as j}from"./error-handler-b38ec056.js";import{_ as B}from"./_plugin-vue_export-helper-1b428a4d.js";const U={class:"order-list"},D={key:0},A={key:1},P={key:2},I=B(e({__name:"OrderList",setup(e){const B="draft",I="pending",L="confirmed",M="in_production",O="ready_to_ship",R="shipping",Y="partial_shipped",E="all_shipped",F="pending_statement",q="partial_statement",H="all_statement",W="pending_payment",G="partial_payment",J="completed",Z="cancelled",K=a(),Q=t({order_number:"",customer_id:void 0,project_name:"",status:void 0,date_range:[],page:1,per_page:10}),X=l(null),ee=l([]),ae=l(!1),te=l([]),le=l(!1),ne=l([]),re=t({currentPage:1,pageSize:10,total:0}),oe=async()=>{var e,a,t;le.value=!0;try{const l={...Q,start_date:null==(e=Q.date_range)?void 0:e[0],end_date:null==(a=Q.date_range)?void 0:a[1],page:re.currentPage,per_page:re.pageSize};delete l.date_range;const n=await x(l);200===n.code&&n.data?(te.value=n.data.list||[],re.total=(null==(t=n.data.pagination)?void 0:t.total_items)||0):(j(n,"加载订单列表失败"),te.value=[],re.total=0)}catch(l){console.error("获取订单列表失败:",l),j(l,"加载订单列表失败")}finally{le.value=!1}},se=async e=>{if(e){ae.value=!0;try{const a=await V(e);ee.value=a.list||[]}catch(a){console.error("搜索客户失败:",a),j(a,"搜索客户失败")}finally{ae.value=!1}}else ee.value=[]},ie=()=>{re.currentPage=1,oe()},ue=()=>{X.value&&(X.value.resetFields(),Q.customer_id=void 0,Q.date_range=[],re.currentPage=1,oe())},de=e=>{ne.value=e},ce=()=>{K.push({name:"OrderNew"})},pe=()=>{v.info("批量导出功能待实现"),console.log("Selected orders for batch export:",ne.value)},me=()=>{v.info("批量打印功能待实现"),console.log("Selected orders for batch print:",ne.value)},ge=async()=>{if(0!==ne.value.length)try{await w.confirm(`确定要删除选中的 ${ne.value.length} 个订单吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),le.value=!0;const e=ne.value.map((e=>e.id)),a=await z(e);200===a.code?(v.success("批量删除成功"),ne.value=[],oe()):j(a,"批量删除失败")}catch(e){"cancel"!==e&&(console.error("批量删除订单失败:",e),j(e,"批量删除失败"))}finally{le.value=!1}else v.warning("请选择要删除的订单")},_e=e=>{K.push({name:"OrderDetail",params:{id:e.id.toString()}})},fe=async e=>{le.value=!0,v.info("正在提交订单完成请求，请稍候...");try{const a=await $(e.id.toString(),{status:"已完成",comment:"从订单列表手动标记订单为已完成状态"});if(200===a.code||0===a.code)v.success("订单已标记为已完成"),oe();else{const t=a.message||"未知错误";if(t.includes("不允许的状态转换")){const a=t.match(/从【(.+?)】\s*->\s*到【(.+?)】/),l=a?a[1]:e.status,n=a?a[2]:"已完成";w.confirm(`系统检测到无法直接从【${l}】状态变更为【${n}】状态。<br><br>您可以选择强制完成订单，但这可能会绕过正常的业务流程检查。是否继续？`,"需要强制完成",{confirmButtonText:"强制完成",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then((async()=>{try{const a=await $(e.id.toString(),{status:"已完成",comment:"强制完成：从订单列表手动标记订单为已完成状态",force:!0});200===a.code||0===a.code?v.success("订单已强制标记为已完成"):v.error(`强制完成失败：${a.message||"未知错误"}`),oe()}catch(a){console.error("强制完成订单失败:",a),v.error(`强制完成失败：${a.message||"未知错误"}`),oe()}})).catch((()=>{v.info("已取消强制完成操作"),oe()}))}else v.error(`订单完成失败：${t}`),oe()}}catch(a){console.error("订单状态更新失败:",a);let e="未知错误";a.response&&a.response.data?e=a.response.data.message||a.message||"状态更新失败":a.message&&(e=a.message),w.alert(`订单完成失败：${e}<br><br>建议：请检查网络连接或联系系统管理员`,"错误",{confirmButtonText:"确定",type:"error",dangerouslyUseHTMLString:!0}),oe()}finally{le.value=!1}},ye=e=>{re.pageSize=e,oe()},be=e=>{re.currentPage=e,oe()},he=e=>{if(!e)return"info";return{[B]:"info",[I]:"warning",[L]:"success",[M]:"primary",[O]:"success",[R]:"warning",[Y]:"warning",[E]:"success",[F]:"info",[q]:"warning",[H]:"success",[W]:"info",[G]:"warning",[J]:"success",[Z]:"danger"}[e]??"info"},ve=e=>{if(!e)return"未知";return{[B]:"草稿",[I]:"待确认",[L]:"已确认",[M]:"生产中",[O]:"待发货",[R]:"发货中",[Y]:"部分发货",[E]:"已发货",[F]:"待对账",[q]:"部分对账",[H]:"已对账",[W]:"待付款",[G]:"部分付款",[J]:"已完成",[Z]:"已取消"}[e]??e},we=e=>{if(null==e)return"N/A";const a=Number(e);return isNaN(a)?"N/A":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY"}).format(a)},ke=e=>{if(!e)return"N/A";try{const a=new Date(e);return isNaN(a.getTime())?(console.warn("Invalid date string for formatting:",e),e):a.toLocaleDateString("zh-CN")}catch(a){return console.error("Error formatting date:",a),e}};return n((()=>{console.log("ORDER_STATUS.PENDING =",I),oe()})),(e,a)=>{const t=r("el-input"),l=r("el-form-item"),n=r("el-option"),x=r("el-select"),z=r("el-date-picker"),V=r("el-button"),B=r("el-form"),L=r("el-card"),M=r("el-icon"),O=r("el-table-column"),R=r("el-tag"),Y=r("el-button-group"),E=r("el-table"),F=r("el-pagination"),q=o("loading");return s(),i("div",U,[u(L,{class:"search-form-card mb-20"},{default:d((()=>[u(B,{ref_key:"searchFormRef",ref:X,model:Q,inline:!0,"label-width":"80px"},{default:d((()=>[u(l,{label:"订单号",prop:"order_number"},{default:d((()=>[u(t,{modelValue:Q.order_number,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.order_number=e),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])])),_:1}),u(l,{label:"客户名称",prop:"customer_id"},{default:d((()=>[u(x,{modelValue:Q.customer_id,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.customer_id=e),filterable:"",remote:"","reserve-keyword":"",placeholder:"请输入客户名称","remote-method":se,loading:ae.value,clearable:""},{default:d((()=>[(s(!0),i(c,null,p(ee.value,(e=>(s(),f(n,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),u(l,{label:"项目名称",prop:"project_name"},{default:d((()=>[u(t,{modelValue:Q.project_name,"onUpdate:modelValue":a[2]||(a[2]=e=>Q.project_name=e),placeholder:"请输入项目名称",clearable:""},null,8,["modelValue"])])),_:1}),u(l,{label:"订单状态",prop:"status"},{default:d((()=>[u(x,{modelValue:Q.status,"onUpdate:modelValue":a[3]||(a[3]=e=>Q.status=e),placeholder:"请选择订单状态",clearable:""},{default:d((()=>[u(n,{label:"草稿",value:"draft"}),u(n,{label:"待确认",value:"pending"}),u(n,{label:"已确认",value:"confirmed"}),u(n,{label:"生产中",value:"in_production"}),u(n,{label:"待发货",value:"ready_to_ship"}),u(n,{label:"发货中",value:"shipping"}),u(n,{label:"部分发货",value:"partial_shipped"}),u(n,{label:"已发货",value:"all_shipped"}),u(n,{label:"待对账",value:"pending_statement"}),u(n,{label:"部分对账",value:"partial_statement"}),u(n,{label:"已对账",value:"all_statement"}),u(n,{label:"待付款",value:"pending_payment"}),u(n,{label:"部分付款",value:"partial_payment"}),u(n,{label:"已完成",value:"completed"}),u(n,{label:"已取消",value:"cancelled"})])),_:1},8,["modelValue"])])),_:1}),u(l,{label:"订单日期",prop:"date_range"},{default:d((()=>[u(z,{modelValue:Q.date_range,"onUpdate:modelValue":a[4]||(a[4]=e=>Q.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])])),_:1}),u(l,null,{default:d((()=>[u(V,{type:"primary",onClick:ie},{default:d((()=>a[5]||(a[5]=[m("查询")]))),_:1}),u(V,{onClick:ue},{default:d((()=>a[6]||(a[6]=[m("重置")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),u(L,{class:"operator-card mb-20"},{default:d((()=>[u(V,{type:"primary",onClick:ce},{default:d((()=>[u(M,null,{default:d((()=>[u(g(k))])),_:1}),a[7]||(a[7]=m(" 新增订单 "))])),_:1}),u(V,{type:"success",onClick:pe,disabled:0===ne.value.length},{default:d((()=>[u(M,null,{default:d((()=>[u(g(C))])),_:1}),a[8]||(a[8]=m(" 批量导出 "))])),_:1},8,["disabled"]),u(V,{type:"warning",onClick:me,disabled:0===ne.value.length},{default:d((()=>[u(M,null,{default:d((()=>[u(g(S))])),_:1}),a[9]||(a[9]=m(" 批量打印 "))])),_:1},8,["disabled"]),u(V,{type:"danger",onClick:ge,disabled:0===ne.value.length},{default:d((()=>[u(M,null,{default:d((()=>[u(g(N))])),_:1}),a[10]||(a[10]=m(" 批量删除 "))])),_:1},8,["disabled"])])),_:1}),u(L,null,{default:d((()=>[_((s(),f(E,{data:te.value,stripe:"",border:"",onSelectionChange:de,onRowClick:_e,"row-key":"id"},{default:d((()=>[u(O,{type:"selection",width:"55","reserve-selection":""}),u(O,{prop:"order_number",label:"订单号",width:"180",sortable:""}),u(O,{prop:"customer.name",label:"客户名称","min-width":"160","show-overflow-tooltip":""},{default:d((({row:e})=>[e.customer?(s(),i("span",D,y(e.customer.name),1)):e.customer_name?(s(),i("span",A,y(e.customer_name),1)):(s(),i("span",P,"N/A"))])),_:1}),u(O,{prop:"project_name",label:"项目名称","min-width":"180","show-overflow-tooltip":""}),u(O,{label:"订单状态",width:"100"},{default:d((({row:e})=>[u(R,{type:he(e.status)},{default:d((()=>[m(y(ve(e.status)),1)])),_:2},1032,["type"])])),_:1}),u(O,{label:"订单金额",width:"110",sortable:""},{default:d((({row:e})=>[m(y(we(Number(e.total_amount))),1)])),_:1}),u(O,{label:"已收款",width:"100"},{default:d((({row:e})=>[m(y(we(e.paid_amount??0)),1)])),_:1}),u(O,{label:"待收款",width:"100"},{default:d((({row:e})=>[m(y(we((e.total_amount?Number(e.total_amount):0)-(e.paid_amount??0))),1)])),_:1}),u(O,{label:"预计交期",width:"120",sortable:""},{default:d((({row:e})=>[m(y(e.expected_date?ke(e.expected_date):"N/A"),1)])),_:1}),u(O,{label:"创建日期",width:"120",sortable:""},{default:d((({row:e})=>[m(y(e.created_at?ke(e.created_at):"N/A"),1)])),_:1}),u(O,{label:"操作",width:"160",fixed:"right"},{default:d((({row:e})=>[u(Y,null,{default:d((()=>["待确认"===e.status?(s(),f(V,{key:0,type:"warning",link:"",size:"small",onClick:b((a=>(e=>{K.push({name:"OrderEdit",params:{id:e.id.toString()}})})(e)),["stop"])},{default:d((()=>a[11]||(a[11]=[m("编辑")]))),_:2},1032,["onClick"])):h("",!0),"待确认"===e.status||"已取消"===e.status?(s(),f(V,{key:1,type:"danger",link:"",size:"small",onClick:b((a=>(async e=>{try{await w.confirm(`确定要删除订单号为 "${e.order_number}" 的订单吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),le.value=!0;const a=await T(e.id.toString());200===a.code?(v.success("订单删除成功"),oe()):j(a,"删除失败")}catch(a){"cancel"!==a&&(console.error("删除订单失败:",a),j(a,"删除失败"))}finally{le.value=!1}})(e)),["stop"])},{default:d((()=>a[12]||(a[12]=[m("删除")]))),_:2},1032,["onClick"])):h("",!0),u(V,{type:"success",link:"",size:"small",onClick:b((a=>(e=>{v.info(`打印订单 ${e.order_number} 功能待实现`),console.log("Printing order:",e)})(e)),["stop"])},{default:d((()=>a[13]||(a[13]=[m("打印")]))),_:2},1032,["onClick"]),"待确认"===e.status?(s(),f(V,{key:2,type:"info",link:"",size:"small",onClick:b((a=>(e=>{console.log("处理订单审核，状态为:",e.status),"待确认"===e.status||e.status===I?K.push({name:"OrderDetail",params:{id:e.id.toString()},query:{openApprove:"true"}}):v.warning(`只有待确认状态的订单可以进行审核，当前状态: ${e.status}`)})(e)),["stop"])},{default:d((()=>a[14]||(a[14]=[m("审核")]))),_:2},1032,["onClick"])):h("",!0),"已确认"===e.status?(s(),f(V,{key:3,type:"primary",link:"",size:"small",onClick:b((a=>(e=>{"已确认"===e.status?w.confirm(`确认将订单 ${e.order_number} 状态更新为"生产中"吗？`,"确认操作",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((async()=>{le.value=!0;try{const a=await $(e.id.toString(),{status:"生产中",comment:"从订单列表变更状态为生产中"});200===a.code?(v.success("订单状态已更新为生产中"),oe()):j(a,"状态更新失败")}catch(a){j(a,"状态更新失败")}finally{le.value=!1}})).catch((()=>{})):v.warning(`只有已确认状态的订单可以开始生产，当前状态: ${e.status}`)})(e)),["stop"])},{default:d((()=>a[15]||(a[15]=[m("开始生产")]))),_:2},1032,["onClick"])):h("",!0),["已确认","生产中","待发货","部分发货","发货中"].includes(e.status)?(s(),f(V,{key:4,type:"success",link:"",size:"small",onClick:b((a=>(e=>{["已确认","生产中","待发货","部分发货","发货中"].includes(e.status)?K.push({name:"DeliveryCreate",query:{orderId:e.id.toString()}}):v.warning(`当前订单状态不支持创建发货单，当前状态: ${e.status}`)})(e)),["stop"])},{default:d((()=>a[16]||(a[16]=[m("发货")]))),_:2},1032,["onClick"])):h("",!0),["部分发货","全部发货"].includes(e.status)?(s(),f(V,{key:5,type:"warning",link:"",size:"small",onClick:b((a=>(e=>{["部分发货","全部发货"].includes(e.status)?K.push({name:"StatementCreate",query:{orderId:e.id.toString()}}):v.warning(`只有发货后的订单才能生成对账单，当前状态: ${e.status}`)})(e)),["stop"])},{default:d((()=>a[17]||(a[17]=[m("生成对账单")]))),_:2},1032,["onClick"])):h("",!0),["已确认","生产中","待发货","部分发货","发货中","全部发货","待对账","部分对账","已对账","待付款","部分付款"].includes(e.status)?(s(),f(V,{key:6,type:"success",link:"",size:"small",onClick:b((a=>(e=>{["已确认","生产中","待发货","部分发货","发货中","全部发货","待对账","部分对账","已对账","待付款","部分付款"].includes(e.status)?w.confirm(`根据系统规则，订单需要按照以下流程处理：\n    1. 待确认 -> 已确认（通过审核按钮）\n    2. 已确认 -> 待发货/发货中/部分发货/全部发货（通过发货操作）\n    3. 全部发货 -> 待对账/已对账（通过对账操作）\n    4. 已对账 -> 待付款/部分付款（通过收款操作）\n    5. 部分付款/待付款 -> 已完成（通过完成按钮）\n    \n    当前订单状态为【${e.status}】，您确定要尝试标记为【已完成】吗？\n    注意：系统可能会根据状态转换规则拒绝此操作。`,"订单完成确认",{confirmButtonText:"确认尝试",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then((()=>{fe(e)})).catch((()=>{})):v.warning(`当前订单状态不支持完成操作，当前状态: ${e.status}`)})(e)),["stop"])},{default:d((()=>a[18]||(a[18]=[m(" 完成订单 ")]))),_:2},1032,["onClick"])):h("",!0)])),_:2},1024)])),_:1})])),_:1},8,["data"])),[[q,le.value]]),u(F,{class:"pagination-container",onSizeChange:ye,onCurrentChange:be,"current-page":re.currentPage,"page-sizes":[10,20,50,100],"page-size":re.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:re.total},null,8,["current-page","page-size","total"])])),_:1})])}}}),[["__scopeId","data-v-892549c2"]]);export{I as default};
