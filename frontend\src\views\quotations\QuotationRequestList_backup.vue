<template>
  <div class="quotation-request-list">
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="page-title">报价需求管理</h2>
        <div>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增报价需求
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card>
      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="客户">
          <el-select
            v-model="searchForm.customer_id"
            filterable
            remote
            placeholder="请选择客户"
            :remote-method="handleSearchCustomers"
            :loading="customerLoading"
            clearable
          >
            <el-option
              v-for="item in customerOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input
            v-model="searchForm.project_name"
            placeholder="请输入项目名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="草稿" value="草稿" />
            <el-option label="已提交" value="已提交" />
            <el-option label="正式" value="正式" />
            <el-option label="已报价" value="已报价" />
            <el-option label="已处理" value="已处理" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column type="index" label="序号" width="30" />
        <el-table-column prop="request_number" label="需求表编号" width="180">
          <template #default="{ row }">
            <el-link type="primary" @click.stop="handleViewDetails(row)">{{ row.request_number }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="customer.name" label="客户名称" width="200" />
        <el-table-column prop="project_name" label="项目名称" min-width="200" />
        <el-table-column prop="created_at" label="创建日期" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ translateStatus(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="280">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click.stop="handleEdit(scope.row)"
              v-if="scope.row.status === '草稿' || scope.row.status === '已提交'"
            >
              <el-icon><Edit /></el-icon> 编辑
              </el-button>
            <el-button
              v-if="scope.row.status === '正式' || scope.row.status === '已报价' || scope.row.status === '已处理'"
              type="primary"
              link
              size="small"
              @click.stop="handleCreateQuotation(scope.row)"
            >
              <el-icon><DocumentAdd /></el-icon> 创建报价单
              </el-button>
             <el-button
              type="info"
              link
              size="small"
              @click.stop="handleExportSingle(scope.row)"
              :loading="scope.row.exporting"
            >
              <el-icon><Download /></el-icon> 导出
              </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click.stop="handleDelete(scope.row.id)"
              v-if="scope.row.status === '草稿' || scope.row.status === '已提交'"
            >
              <el-icon><Delete /></el-icon> 删除
              </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>




  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search, Refresh, Edit, Delete } from '@element-plus/icons-vue';
import { listQuotationRequests as getQuotationRequests, generateQuotationFromRequest as apiCreateQuotationFromRequest } from '@/api/quotation';
import { formatDate, formatDateTime } from '@/utils/format';
import type { QuotationRequest, Customer } from '@/types/api';

const router = useRouter()
const loading = ref(false)
const customerLoading = ref(false)
const customerOptions = ref<Array<{ id: number; name: string }>>([])

// 搜索表单
const searchForm = reactive({
  customer_id: undefined as number | undefined,
  project_name: '',
  status: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const tableData = ref<(QuotationRequest & { exporting?: boolean })[]>([])





// 基础操作函数
const handleAdd = () => {
  router.push('/quotation-requests/new')
}

const handleEdit = (row: QuotationRequest) => {
  router.push(`/quotation-requests/edit/${row.id}`)
}

const handleView = (row: QuotationRequest) => {
  router.push(`/quotation-requests/view/${row.id}`)
}

const handleViewDetails = (row: QuotationRequest) => {
  if (row.id) {
    router.push(`/quotation-requests/view/${row.id}`);
  }
};

const handleDelete = async (row: QuotationRequest) => {
  importFormModel.customerId = undefined;
  importCustomerOptions.value = [];

  if (uploadFile.raw) {
    const isExcel = uploadFile.name.endsWith('.xlsx') || uploadFile.name.endsWith('.xls');
    const isLt10M = (uploadFile.size || 0) / 1024 / 1024 < 10;
    if (!isExcel) {
      ElMessage.error('只能上传 Excel 文件 (.xlsx, .xls)!');
      uploadRef.value?.clearFiles();
      fileToUpload.value = null;
      return;
    }
    if (!isLt10M) {
      ElMessage.error('上传文件大小不能超过 10MB!');
      uploadRef.value?.clearFiles();
      fileToUpload.value = null;
      return;
    }
    fileToUpload.value = uploadFile;

    // 新增：尝试从Excel中解析客户名称
    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = e.target?.result;
          const workbook = XLSX.read(data, { type: 'binary', cellDates: true });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          const sheetData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' });

          if (sheetData && sheetData.length > 1) {
            // 检查是否有产品数据
            const hasProductData = sheetData.some((row, index) =>
              index > 0 && row.length > 0 && row[0] // 第一列有数据（产品名称）
            );

            if (hasProductData) {
              ElMessage.success('Excel文件格式正确，包含产品数据');
            } else {
              ElMessage.warning('Excel文件中未检测到产品数据，请确认文件格式正确');
            }
          } else {
            ElMessage.error('Excel文件内容为空或格式不正确');
          }
        } catch (parseError) {
          console.error('解析Excel文件失败:', parseError);
          ElMessage.error('解析Excel文件失败，请检查文件格式');
        }
      };
      reader.readAsBinaryString(uploadFile.raw);
    } catch (error) {
      console.error('读取Excel文件失败:', error);
      ElMessage.error('读取Excel文件失败');
    }

  } else {
    fileToUpload.value = null;
  }
};

const handleUploadExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  // ElMessage.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，旧文件将被替换`)
  uploadRef.value!.handleStart(file)
  // Manually trigger change after replacing file for validation and tracking
  // This is a bit of a hack, ideally Element Plus would handle single file replacement better
  // Or, simply use on-success if :auto-upload="true"
  const newUploadFile: UploadFile = {
    name: file.name,
    size: file.size,
    raw: file,
    uid: file.uid,
    status: 'ready'
  }
  handleFileChange(newUploadFile, [newUploadFile])
}

// 新增或修改：为导入对话框搜索客户的方法
const handleSearchCustomersForImport = async (query: string, autoSelectIfOneMatch = false) => {
  if (query) {
    importCustomerLoading.value = true;
    try {
      const response = await searchCustomers({ name_like: query, per_page: 50 }) as any;
      console.log('客户搜索响应:', response);

      // 处理不同的响应格式
      let customers = [];
      if (Array.isArray(response)) {
        // 如果响应直接是数组
        customers = response;
      } else if (response && response.data) {
        // 如果响应是 { data: [...] } 格式
        if (Array.isArray(response.data)) {
          customers = response.data;
        } else if (response.data.items) {
          // 如果是分页格式 { data: { items: [...] } }
          customers = response.data.items;
        }
      } else if (response && response.items) {
        // 如果响应是 { items: [...] } 格式
        customers = response.items;
      }

      importCustomerOptions.value = customers;

      if (autoSelectIfOneMatch && customers.length === 1) {
        importFormModel.customerId = customers[0].id;
        ElMessage.success(`已自动选择匹配到的客户: ${customers[0].name}`);
      } else if (autoSelectIfOneMatch && customers.length > 1) {
        ElMessage.info(`找到多个名为 "${query}" 的客户，请手动选择。`);
      } else if (autoSelectIfOneMatch && customers.length === 0) {
        // 在这里不需要弹窗，因为外部 UI 会显示创建按钮
      }
    } catch (error) {
      console.error('导入时搜索客户失败:', error);
      importCustomerOptions.value = [];
    } finally {
      importCustomerLoading.value = false;
    }
  } else {
    importCustomerOptions.value = [];
  }
};

const submitImport = async () => {
  if (!importFormRef.value) return
  try {
    await importFormRef.value.validate(); // 验证失败会抛出错误

    // 验证通过后继续执行
    if (!fileToUpload.value || !fileToUpload.value.raw) {
      ElMessage.error('请先选择要上传的文件')
      return
    }
    if (!importFormModel.customerId) {
       ElMessage.error('请选择客户后再导入');
       return;
    }

    importLoading.value = true
    // TODO: 实现导入功能
    ElMessage.info('导入功能开发中...')
    importDialogVisible.value = false
    importLoading.value = false
  } catch (validationErrorFields) { // 捕获表单验证错误
    console.warn('导入表单验证失败:', validationErrorFields)
    importLoading.value = false
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMapUi: Record<string, string> = {
    '草稿': 'info',
    '已提交': 'primary',
    '正式': 'warning',
    '已报价': 'success',
    '已处理': 'success', // 已处理也用 success
    '已取消': 'danger'
  }
  return statusMapUi[status] || 'info' // 默认 info
}

// 搜索客户
const handleSearchCustomers = async (query: string) => {
  if (query) {
    customerLoading.value = true;
    try {
      const response = await searchCustomers({ name_like: query, per_page: 50 });
      if (response && response.data) {
        customerOptions.value = response.data;
      } else {
        customerOptions.value = [];
      }
    } catch (error) {
      console.error('搜索客户失败:', error);
      customerOptions.value = [];
    } finally {
      customerLoading.value = false;
    }
  } else {
    customerOptions.value = [];
  }
};

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      ...searchForm
    }
    const response = await getQuotationRequests(params) as any

    console.log('报价需求列表响应:', response)

    // 处理API响应数据
    if (Array.isArray(response)) {
      // 如果响应直接是数组（被request拦截器处理过）
      tableData.value = response.map(item => ({
        ...item,
        status: translateStatus(item.status || ''),
        exporting: false
      }))
      total.value = response.length
    } else if (response && typeof response === 'object') {
      // 如果响应是对象格式
      const data = response.data || response.items || []
      tableData.value = data.map((item: any) => ({
        ...item,
        status: translateStatus(item.status || ''),
        exporting: false
      }))
      total.value = response.pagination?.total || response.total || 0
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取报价需求列表失败:', error)
    ElMessage.error('获取报价需求列表失败')
    tableData.value = [] // 清空表格数据
    total.value = 0      // 重置总数
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    customer_id: undefined,
    project_name: '',
    status: ''
  })
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchData()
}

// 当前页改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchData()
}

// 新增
const handleAdd = () => {
  router.push('/quotation-requests/new')
}

// 编辑
const handleEdit = (row: QuotationRequest) => {
  router.push(`/quotation-requests/edit/${row.id}`)
}

// 查看
const handleView = (row: QuotationRequest) => { // 虽然有handleView，但实际表格行点击和编号点击用的是handleViewDetails
  router.push(`/quotation-requests/view/${row.id}`)
}

// 创建报价单
const handleCreateQuotation = async (row: QuotationRequest) => {
  if (!row.id) return;
  try {
    ElMessageBox.prompt('请输入报价单有效期至 (YYYY-MM-DD):', '创建报价单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'date',
      inputValidator: (val: string) => {
        if (!val) return '有效期不能为空';
        if (!/^\d{4}-\d{2}-\d{2}$/.test(val)) return '日期格式应为 YYYY-MM-DD';
        return true;
      },
    }).then(async ({ value }) => {
      const payload = { valid_until: value };
      const createdQuotation = await apiCreateQuotationFromRequest(row.id!, payload);
      ElMessage.success('报价单创建成功');
      fetchData(); // 刷新列表
      router.push(`/quotations/${createdQuotation.id}/edit`); // 跳转到新创建的报价单编辑页
    }).catch(() => {
      ElMessage.info('已取消创建报价单');
    });
  } catch (error: any) {
    console.error('创建报价单失败:', error);
    ElMessage.error(error.response?.data?.message || error.message || '创建报价单失败');
  }
};

const handleExportSingle = async (row: QuotationRequest & { exporting?: boolean }) => {
  if (!row.id) return;
  row.exporting = true;
  try {
    // TODO: 实现导出功能
    ElMessage.info('导出功能开发中...')
  } catch (e) {
    console.error('导出失败:', e);
    ElMessage.error('导出失败');
  } finally {
    row.exporting = false;
  }
};

// 下载询价单模板
const handleDownloadTemplate = async () => {
  try {
    const response = await quotationRequestApi.downloadTemplate();

    // 创建下载链接
    const blob = new Blob([response as any], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `询价单模板_${new Date().toISOString().slice(0, 10)}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success('模板下载成功');
  } catch (error) {
    console.error('下载模板失败:', error);
    ElMessage.error('下载模板失败');
  }
};

const statusMap: Record<string, string> = {
  '草稿': '草稿',
  '已提交': '已提交',
  '正式': '正式',
  '已报价': '已报价',
  '已处理': '已处理',
  '已取消': '已取消',
  // 兼容英文状态
  'draft': '草稿',
  'submitted': '已提交',
  'official': '正式',
  'quoted': '已报价',
  'processed': '已处理',
  'cancelled': '已取消'
};

const translateStatus = (status: string): string => {
  // 优先匹配后端可能返回的英文状态，然后是中文状态
  return statusMap[status.toLowerCase()] || statusMap[status] || status;
};

// 新增：为导入对话框创建客户 - 暂时禁用
// const handleCreateNewCustomerForImport = () => {
//   if (parsedCustomerNameFromExcel.value) {
//     customerInitialData.value = { name: parsedCustomerNameFromExcel.value }
//     customerDialogVisible.value = true
//   }
// }

// 新增：处理导入流程中客户创建成功的回调 - 暂时禁用
// const handleCustomerCreatedInImport = (newCustomer: Customer) => {
//   if (newCustomer && newCustomer.id) {
//     // 将新客户添加到导入弹窗的下拉选项中
//     importCustomerOptions.value.unshift({ id: newCustomer.id, name: newCustomer.name })
//     // 自动选中新客户
//     importFormModel.customerId = newCustomer.id

//     ElMessage.success(`新客户 "${newCustomer.name}" 已创建并选中`)

//     // 清理待创建状态
//     parsedCustomerNameFromExcel.value = null;
//     customerDialogVisible.value = false
//   }
// }

const handleRowClick = (row: QuotationRequest) => {
  handleViewDetails(row);
};

const handleViewDetails = (row: QuotationRequest) => {
  if (row.id) {
    router.push(`/quotation-requests/view/${row.id}`);
  }
};

const handleDelete = (id: number | undefined) => {
  if (id === undefined) return;
  // Implement delete logic
  // 例如:
  // ElMessageBox.confirm('确定删除此报价需求吗?', '提示', {
  //   confirmButtonText: '确定',
  //   cancelButtonText: '取消',
  //   type: 'warning',
  // }).then(async () => {
  //   try {
  //     // await deleteQuotationRequestApi(id); // 需要一个删除API
  //     ElMessage.success('删除成功');
  //     fetchData(); // 刷新列表
  //   } catch (error) {
  //     ElMessage.error('删除失败');
  //   }
  // }).catch(() => {
  //   ElMessage.info('已取消删除');
  // });
  console.warn(`删除操作未实现，ID: ${id}`);
  ElMessage.info('删除功能暂未实现');
};

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.quotation-request-list {
  padding: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-demo {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }

  :deep(.el-icon--upload) {
    font-size: 67px;
    color: var(--el-text-color-placeholder);
    margin: 40px 0 16px;
    line-height: 50px;
  }

  :deep(.el-upload__text) {
    color: var(--el-text-color-regular);
    font-size: 14px;
    text-align: center;

    em {
      color: var(--el-color-primary);
      font-style: normal;
    }
  }
}
</style>
