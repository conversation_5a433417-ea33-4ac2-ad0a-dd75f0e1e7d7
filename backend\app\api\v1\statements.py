"""
对账单管理API模块
提供对账单的完整CRUD操作、基于发货单生成对账单、状态管理、确认流程和PDF导出功能
确保与订单、发货单和退货单模块的协同工作
"""
from flask import request, current_app, make_response
from flask_restx import Resource, Namespace, fields
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, or_, desc, func
from marshmallow import ValidationError
from datetime import datetime, timedelta
import uuid
from decimal import Decimal
from io import BytesIO

from app.models.finance import Statement, StatementDeliveryNote, StatementReturnOrder
from app.models.order import Order, DeliveryNote, DeliveryNoteItem
from app.models.return_order import ReturnOrder, ReturnOrderItem
from app.models.customer import Customer
from app.models.product import ProductSpecification
from app.schemas.finance import (
    StatementSchema, StatementCreateSchema, StatementUpdateSchema
)
from app.utils.response import success_response, error_response
from app.utils.pagination import PaginationHelper
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('statements', description='对账单管理API')

# 自动从Marshmallow Schema生成API模型
statement_model = create_input_model(api, StatementSchema, 'StatementInput')

# 状态更新模型（手动定义，因为没有对应的Schema）
statement_status_model = api.model('StatementStatus', {
    'status': fields.String(required=True, description='新状态'),
    'notes': fields.String(description='备注')
})

# 对账单状态定义
STATEMENT_STATUSES = ['待确认', '已确认', '部分收款', '已结清', '已取消']

# 状态流转规则
STATUS_TRANSITIONS = {
    '待确认': ['已确认', '已取消'],
    '已确认': ['部分收款', '已结清', '已取消'],
    '部分收款': ['已结清'],  # 部分收款后不能取消，只能继续收款直到结清
    '已结清': [],
    '已取消': []
}


@api.route('')
class StatementList(Resource):
    @api.doc('get_statements')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('statement_number', '对账单编号搜索')
    @api.param('status', '对账单状态')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    @api.param('customer_name', '客户名称搜索')
    def get(self):
        """获取对账单列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 20, type=int), 100)
            customer_id = request.args.get('customer_id', type=int)
            statement_number = request.args.get('statement_number', '').strip()
            status = request.args.get('status', '').strip()
            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()
            customer_name = request.args.get('customer_name', '').strip()

            # 构建查询
            query = Statement.query.options(
                joinedload(Statement.customer)
            )

            # 应用过滤条件
            if customer_id:
                query = query.filter(Statement.customer_id == customer_id)
            
            if statement_number:
                query = query.filter(Statement.statement_number.ilike(f'%{statement_number}%'))
            
            if status:
                query = query.filter(Statement.status == status)
            
            if customer_name:
                query = query.join(Customer).filter(
                    Customer.name.ilike(f'%{customer_name}%')
                )
            
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d').date()
                    query = query.filter(Statement.statement_date >= start)
                except ValueError:
                    return error_response("开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            
            if end_date:
                try:
                    end = datetime.strptime(end_date, '%Y-%m-%d').date()
                    query = query.filter(Statement.statement_date <= end)
                except ValueError:
                    return error_response("结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            # 排序
            query = query.order_by(desc(Statement.created_at))

            # 分页
            pagination = query.paginate(
                page=page, 
                per_page=per_page, 
                error_out=False
            )

            # 序列化数据
            statements = []
            for statement in pagination.items:
                data = {
                    'id': statement.id,
                    'statement_number': statement.statement_number,
                    'customer_id': statement.customer_id,
                    'customer_name': statement.customer.name if statement.customer else '',
                    'statement_date': statement.statement_date.isoformat() if statement.statement_date else None,
                    'due_date': statement.due_date.isoformat() if statement.due_date else None,
                    'status': statement.status,
                    'original_amount': float(statement.total_amount) if statement.total_amount else 0.0,
                    'discount_amount': float(statement.discount_amount) if statement.discount_amount else 0.0,
                    'total_amount': float(statement.adjusted_total_amount) if statement.adjusted_total_amount else 0.0,
                    'paid_amount': float(statement.paid_amount) if statement.paid_amount else 0.0,
                    'notes': statement.notes,
                    'created_at': statement.created_at.isoformat() if statement.created_at else None,
                    'delivery_notes_count': len(statement.delivery_notes) if statement.delivery_notes else 0
                }
                statements.append(data)

            return success_response({
                'items': statements,
                'pagination': {
                    'page': pagination.page,
                    'per_page': pagination.per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                }
            }, '获取对账单列表成功')

        except Exception as e:
            current_app.logger.error(f"获取对账单列表失败: {str(e)}")
            return error_response(f"获取对账单列表失败: {str(e)}", code=500)

    @api.doc('create_statement',
             body=statement_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 404: 'Customer Not Found',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建对账单"""
        try:
            data = request.get_json() or {}
            delivery_note_ids = data.pop('delivery_note_ids', [])
            return_order_ids = data.pop('return_order_ids', [])

            # 验证必需字段 - 至少需要发货单或退货单之一
            if not delivery_note_ids and not return_order_ids:
                return error_response("发货单ID列表和退货单ID列表不能同时为空", code=400)

            # 检查客户是否存在
            customer = Customer.query.get(data.get('customer_id'))
            if not customer:
                return error_response("客户不存在", code=404)

            # 检查发货单（如果有）
            delivery_notes = []
            if delivery_note_ids:
                delivery_notes = DeliveryNote.query.options(
                    joinedload(DeliveryNote.order),
                    joinedload(DeliveryNote.items)
                ).filter(
                    DeliveryNote.id.in_(delivery_note_ids),
                    DeliveryNote.status == '已签收'  # 只有已签收的发货单可以对账
                ).all()

                if len(delivery_notes) != len(delivery_note_ids):
                    return error_response("部分发货单不存在或状态不允许对账", code=400)

                # 验证所有发货单属于同一客户
                for delivery_note in delivery_notes:
                    if delivery_note.order.customer_id != data.get('customer_id'):
                        return error_response(f"发货单 {delivery_note.delivery_number} 不属于指定客户", code=400)

            # 检查退货单（如果有）
            return_orders = []
            if return_order_ids:
                return_orders = ReturnOrder.query.options(
                    joinedload(ReturnOrder.order),
                    joinedload(ReturnOrder.items).joinedload(ReturnOrderItem.order_product)
                ).filter(
                    ReturnOrder.id.in_(return_order_ids),
                    ReturnOrder.status == '已签收'  # 只有已签收的退货单可以对账
                ).all()

                if len(return_orders) != len(return_order_ids):
                    return error_response("部分退货单不存在或状态不允许对账", code=400)

                # 验证所有退货单属于同一客户
                for return_order in return_orders:
                    if return_order.order.customer_id != data.get('customer_id'):
                        return error_response(f"退货单 {return_order.return_number} 不属于指定客户", code=400)

            # 检查发货单是否已经在其他对账单中
            if delivery_note_ids:
                existing_statements = db.session.query(StatementDeliveryNote).filter(
                    StatementDeliveryNote.delivery_note_id.in_(delivery_note_ids)
                ).all()

                if existing_statements:
                    existing_delivery_note_ids = [sdn.delivery_note_id for sdn in existing_statements]
                    return error_response(f"发货单ID {existing_delivery_note_ids} 已在其他对账单中", code=400)

            # 检查退货单是否已经在其他对账单中
            if return_order_ids:
                existing_return_statements = db.session.query(StatementReturnOrder).filter(
                    StatementReturnOrder.return_order_id.in_(return_order_ids)
                ).all()

                if existing_return_statements:
                    existing_return_order_ids = [sro.return_order_id for sro in existing_return_statements]
                    return error_response(f"退货单ID {existing_return_order_ids} 已在其他对账单中", code=400)

            # 生成对账单编号
            statement_number = f"ST{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"

            # 计算总金额（发货单金额 - 退货单金额）
            total_amount = Decimal('0.00')

            # 加上发货单金额
            for delivery_note in delivery_notes:
                total_amount += Decimal(str(delivery_note.total_amount or 0))

            # 减去退货单金额
            for return_order in return_orders:
                # 计算退货单金额
                return_amount = Decimal('0.00')
                try:
                    for item in return_order.items:
                        if item.order_product:
                            unit_price = Decimal(str(item.order_product.unit_price or 0))
                            discount_rate = Decimal(str(item.order_product.discount or 0)) / Decimal('100')
                            tax_rate = Decimal(str(item.order_product.tax_rate or 0)) / Decimal('100')
                            quantity = Decimal(str(item.quantity or 0))

                            actual_price = unit_price * (Decimal('1') - discount_rate) * (Decimal('1') + tax_rate)
                            item_amount = actual_price * quantity
                            return_amount += item_amount
                except Exception as e:
                    current_app.logger.error(f"计算退货单 {return_order.id} 金额时出错: {str(e)}")
                    # 如果计算失败，跳过这个退货单的金额计算
                    continue

                total_amount -= return_amount

            # 获取优惠金额和调整后总金额
            discount_amount = Decimal(str(data.get('discount_amount', 0)))
            adjusted_total_amount = total_amount - discount_amount

            # 创建对账单对象
            statement = Statement(
                statement_number=statement_number,
                customer_id=data.get('customer_id'),
                statement_date=datetime.strptime(data.get('statement_date'), '%Y-%m-%d').date() if data.get('statement_date') else datetime.now().date(),
                due_date=datetime.strptime(data.get('due_date'), '%Y-%m-%d').date() if data.get('due_date') else None,
                status=data.get('status', '待确认'),
                total_amount=total_amount,
                discount_amount=discount_amount,
                adjusted_total_amount=adjusted_total_amount,
                notes=data.get('notes', '')
            )
            db.session.add(statement)
            db.session.flush()  # 获取ID

            # 创建对账单-发货单关联
            for delivery_note_id in delivery_note_ids:
                statement_delivery_note = StatementDeliveryNote(
                    statement_id=statement.id,
                    delivery_note_id=delivery_note_id
                )
                db.session.add(statement_delivery_note)

            # 创建对账单-退货单关联
            for return_order_id in return_order_ids:
                statement_return_order = StatementReturnOrder(
                    statement_id=statement.id,
                    return_order_id=return_order_id
                )
                db.session.add(statement_return_order)

            db.session.commit()

            # 返回创建的对账单信息
            created_statement = Statement.query.options(
                joinedload(Statement.customer),
                joinedload(Statement.delivery_notes),
                joinedload(Statement.return_orders)
            ).get(statement.id)
            
            response_data = StatementSchema().dump(created_statement)
            return success_response(response_data, "对账单创建成功", code=201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            db.session.rollback()
            return error_response("数据验证失败", errors=e.messages, code=400)
        except Exception as e:
            current_app.logger.error(f"创建对账单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"创建对账单失败: {str(e)}", code=500)


@api.route('/<int:statement_id>')
class StatementDetail(Resource):
    @api.doc('get_statement')
    def get(self, statement_id):
        """获取对账单详情"""
        try:
            statement = Statement.query.options(
                joinedload(Statement.customer),
                joinedload(Statement.delivery_notes).joinedload(DeliveryNote.items),
                joinedload(Statement.delivery_notes).joinedload(DeliveryNote.order),
                joinedload(Statement.return_orders).joinedload(ReturnOrder.items).joinedload(ReturnOrderItem.order_product),
                joinedload(Statement.return_orders).joinedload(ReturnOrder.items).joinedload(ReturnOrderItem.product_specification).joinedload(ProductSpecification.product),
                joinedload(Statement.return_orders).joinedload(ReturnOrder.order)
            ).get(statement_id)

            if not statement:
                return error_response("对账单不存在", code=404)

            # 手动构建响应数据（临时方案，确保功能正常）
            statement_data = {
                'id': statement.id,
                'statement_number': statement.statement_number,
                'customer_id': statement.customer_id,
                'customer': {
                    'id': statement.customer.id,
                    'name': statement.customer.name,
                    'contact': statement.customer.contact,
                    'phone': statement.customer.phone
                } if statement.customer else None,
                'statement_date': statement.statement_date.isoformat() if statement.statement_date else None,
                'due_date': statement.due_date.isoformat() if statement.due_date else None,
                'status': statement.status,
                'total_amount': str(statement.total_amount) if statement.total_amount else '0.00',
                'discount_amount': str(statement.discount_amount) if statement.discount_amount else '0.00',
                'adjusted_total_amount': str(statement.adjusted_total_amount) if statement.adjusted_total_amount else '0.00',
                'paid_amount': str(statement.paid_amount) if statement.paid_amount else '0.00',
                'notes': statement.notes,
                'created_at': statement.created_at.isoformat() if statement.created_at else None,
                'updated_at': statement.updated_at.isoformat() if statement.updated_at else None,
                'delivery_notes': [],
                'return_orders': []
            }

            # 添加发货单信息
            for delivery_note in statement.delivery_notes:
                delivery_data = {
                    'id': delivery_note.id,
                    'delivery_number': delivery_note.delivery_number,
                    'order_id': delivery_note.order_id,
                    'order_number': delivery_note.order.order_number if delivery_note.order else '',
                    'delivery_date': delivery_note.delivery_date.isoformat() if delivery_note.delivery_date else None,
                    'status': delivery_note.status,
                    'total_amount': str(delivery_note.total_amount) if delivery_note.total_amount else '0.00'
                }
                statement_data['delivery_notes'].append(delivery_data)

            # 添加退货单信息
            for return_order in statement.return_orders:
                # 计算退货单总金额
                return_amount = Decimal('0.00')
                try:
                    for item in return_order.items:
                        if item.order_product:
                            unit_price = Decimal(str(item.order_product.unit_price or 0))
                            discount_rate = Decimal(str(item.order_product.discount or 0)) / Decimal('100')
                            tax_rate = Decimal(str(item.order_product.tax_rate or 0)) / Decimal('100')
                            quantity = Decimal(str(item.quantity or 0))

                            actual_price = unit_price * (Decimal('1') - discount_rate) * (Decimal('1') + tax_rate)
                            item_amount = actual_price * quantity
                            return_amount += item_amount
                except Exception as e:
                    current_app.logger.error(f"计算退货单 {return_order.id} 金额时出错: {str(e)}")
                    return_amount = Decimal('0.00')

                return_data = {
                    'id': return_order.id,
                    'return_number': return_order.return_number,
                    'order_id': return_order.order_id,
                    'order_number': return_order.order.order_number if return_order.order else '',
                    'return_date': return_order.return_date.isoformat() if return_order.return_date else None,
                    'status': return_order.status,
                    'total_amount': str(return_amount)
                }
                statement_data['return_orders'].append(return_data)

            return success_response(statement_data, "获取对账单详情成功")

        except Exception as e:
            current_app.logger.error(f"获取对账单详情失败: {str(e)}")
            return error_response(f"获取对账单详情失败: {str(e)}", code=500)

    @api.doc('update_statement',
             body=statement_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Statement Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, statement_id):
        """更新对账单"""
        try:
            statement = Statement.query.get(statement_id)
            if not statement:
                return error_response("对账单不存在", code=404)

            # 检查是否可以编辑
            if statement.status not in ['待确认']:
                return error_response("当前状态的对账单不可编辑", code=400)

            data = request.get_json() or {}
            delivery_note_ids = data.pop('delivery_note_ids', None)

            # 更新基本字段
            if 'statement_date' in data:
                statement.statement_date = datetime.strptime(data['statement_date'], '%Y-%m-%d').date()
            if 'due_date' in data:
                statement.due_date = datetime.strptime(data['due_date'], '%Y-%m-%d').date() if data['due_date'] else None
            if 'notes' in data:
                statement.notes = data['notes']

            # 处理发货单关联
            if delivery_note_ids is not None:
                # 删除现有关联
                StatementDeliveryNote.query.filter_by(statement_id=statement.id).delete()

                # 验证新的发货单
                if delivery_note_ids:
                    delivery_notes = DeliveryNote.query.options(
                        joinedload(DeliveryNote.order),
                        joinedload(DeliveryNote.items)
                    ).filter(
                        DeliveryNote.id.in_(delivery_note_ids),
                        DeliveryNote.status == '已签收'
                    ).all()

                    if len(delivery_notes) != len(delivery_note_ids):
                        db.session.rollback()
                        return error_response("部分发货单不存在或状态不允许对账", code=400)

                    # 验证所有发货单属于同一客户
                    for delivery_note in delivery_notes:
                        if delivery_note.order.customer_id != statement.customer_id:
                            db.session.rollback()
                            return error_response(f"发货单 {delivery_note.delivery_number} 不属于指定客户", code=400)

                    # 重新计算总金额
                    total_amount = Decimal('0.00')
                    for delivery_note in delivery_notes:
                        for item in delivery_note.items:
                            if item.order_product:
                                item_amount = Decimal(str(item.order_product.unit_price)) * Decimal(str(item.quantity))
                                total_amount += item_amount

                    statement.total_amount = total_amount

                    # 创建新的关联
                    for delivery_note_id in delivery_note_ids:
                        statement_delivery_note = StatementDeliveryNote(
                            statement_id=statement.id,
                            delivery_note_id=delivery_note_id
                        )
                        db.session.add(statement_delivery_note)

            db.session.commit()

            # 返回更新后的数据
            updated_statement = Statement.query.options(
                joinedload(Statement.customer),
                joinedload(Statement.delivery_notes)
            ).get(statement.id)

            response_data = StatementSchema().dump(updated_statement)
            return success_response(response_data, "对账单更新成功")

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            db.session.rollback()
            return error_response("数据验证失败", errors=e.messages, code=400)
        except Exception as e:
            current_app.logger.error(f"更新对账单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新对账单失败: {str(e)}", code=500)

    @api.doc('delete_statement')
    def delete(self, statement_id):
        """删除对账单"""
        try:
            statement = Statement.query.get(statement_id)
            if not statement:
                return error_response("对账单不存在", code=404)

            # 检查是否可以删除
            if statement.status not in ['待确认']:
                return error_response("只有待确认状态的对账单可以删除", code=400)

            db.session.delete(statement)
            db.session.commit()
            return success_response(message="对账单删除成功")

        except Exception as e:
            current_app.logger.error(f"删除对账单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"删除对账单失败: {str(e)}", code=500)


@api.route('/<int:statement_id>/status')
class StatementStatus(Resource):
    @api.doc('update_statement_status',
             body=statement_status_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Statement Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, statement_id):
        """更新对账单状态"""
        try:
            statement = Statement.query.get(statement_id)
            if not statement:
                return error_response("对账单不存在", code=404)

            data = request.get_json() or {}
            new_status = data.get('status', '').strip()
            notes = data.get('notes', '').strip()

            # 验证状态
            if new_status not in STATEMENT_STATUSES:
                return error_response(f"无效的状态值，允许的状态: {', '.join(STATEMENT_STATUSES)}", code=400)

            # 检查状态流转规则
            current_status = statement.status
            allowed_statuses = STATUS_TRANSITIONS.get(current_status, [])

            if new_status not in allowed_statuses:
                return error_response(f"不能从 '{current_status}' 状态转换到 '{new_status}' 状态", code=400)

            # 更新状态
            statement.status = new_status
            if notes:
                statement.notes = notes

            # 注意：新逻辑下，应收账款不在对账单确认时创建，而是在发货单/退货单签收时创建

            db.session.commit()

            # 返回更新后的数据
            response_data = StatementSchema().dump(statement)
            return success_response(response_data, f"对账单状态已更新为 '{new_status}'")

        except Exception as e:
            current_app.logger.error(f"更新对账单状态失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新对账单状态失败: {str(e)}", code=500)


@api.route('/<int:statement_id>/confirm')
class StatementConfirm(Resource):
    @api.doc('confirm_statement')
    def put(self, statement_id):
        """确认对账单"""
        try:
            statement = Statement.query.get(statement_id)
            if not statement:
                return error_response("对账单不存在", code=404)

            # 检查状态
            if statement.status != '待确认':
                return error_response("只有待确认状态的对账单可以确认", code=400)

            # 更新状态为已确认
            statement.status = '已确认'

            # 注意：新逻辑下，应收账款不在对账单确认时创建，而是在发货单/退货单签收时创建

            db.session.commit()

            # 返回更新后的数据
            response_data = StatementSchema().dump(statement)
            return success_response(response_data, "对账单确认成功")

        except Exception as e:
            current_app.logger.error(f"确认对账单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"确认对账单失败: {str(e)}", code=500)


@api.route('/<int:statement_id>/force-settle')
class StatementForceSettle(Resource):
    @api.doc('force_settle_statement')
    def put(self, statement_id):
        """强制结清对账单"""
        try:
            statement = Statement.query.get(statement_id)
            if not statement:
                return error_response("对账单不存在", code=404)

            # 检查状态
            if statement.status in ['已结清', '已取消']:
                return error_response(f"对账单状态为 '{statement.status}'，无法强制结清", code=400)

            # 强制结清：设置状态为已结清，设置结清日期
            statement.status = '已结清'
            if not statement.settlement_date:
                statement.settlement_date = datetime.now().date()

            # 结清相关单据并更新应收账款
            statement._settle_related_documents()

            db.session.commit()

            # 返回更新后的数据
            response_data = StatementSchema().dump(statement)
            return success_response(response_data, "对账单已强制结清")

        except Exception as e:
            current_app.logger.error(f"强制结清对账单失败: {str(e)}")
            db.session.rollback()
            return error_response(f"强制结清对账单失败: {str(e)}", code=500)


@api.route('/statuses')
class StatementStatuses(Resource):
    @api.doc('get_statement_statuses')
    def get(self):
        """获取对账单状态列表"""
        try:
            return success_response({
                'statuses': STATEMENT_STATUSES,
                'transitions': STATUS_TRANSITIONS,
                'descriptions': {
                    '待确认': '对账单待确认，可以编辑和删除',
                    '已确认': '对账单已确认，可以收款',
                    '部分收款': '已收到部分款项，可以继续收款',
                    '已结清': '对账单已完全收款结清',
                    '已取消': '对账单已取消'
                }
            }, "获取对账单状态列表成功")

        except Exception as e:
            current_app.logger.error(f"获取对账单状态列表失败: {str(e)}")
            return error_response(f"获取对账单状态列表失败: {str(e)}", code=500)


@api.route('/available-delivery-notes')
class AvailableDeliveryNotes(Resource):
    @api.doc('get_available_delivery_notes')
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取可用于对账的发货单"""
        try:
            customer_id = request.args.get('customer_id', type=int)
            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()

            # 构建查询 - 查询已签收的发货单（包括已对账的，用于显示状态）
            query = DeliveryNote.query.options(
                joinedload(DeliveryNote.order).joinedload(Order.customer),
                joinedload(DeliveryNote.items)
            ).filter(
                DeliveryNote.status == '已签收'
            )

            if customer_id:
                query = query.join(Order).filter(Order.customer_id == customer_id)

            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(DeliveryNote.delivery_date >= start)
                except ValueError:
                    return error_response("开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            if end_date:
                try:
                    end = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(DeliveryNote.delivery_date <= end)
                except ValueError:
                    return error_response("结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            delivery_notes = query.order_by(desc(DeliveryNote.delivery_date)).all()

            # 获取已关联的发货单ID列表
            linked_delivery_note_ids = set(
                row[0] for row in db.session.query(StatementDeliveryNote.delivery_note_id).all()
            )

            # 构建响应数据，只返回未关联的发货单
            available_delivery_notes = []
            for delivery_note in delivery_notes:
                # 跳过已关联到对账单的发货单
                if delivery_note.id in linked_delivery_note_ids:
                    continue

                # 直接使用数据库中已计算好的总金额（已考虑折扣和税率）
                total_amount = delivery_note.total_amount or 0.0
                items_data = []

                for item in delivery_note.items:
                    items_data.append({
                        'id': item.id,
                        'product_name': item.product_name,
                        'product_model': item.product_model,
                        'product_unit': item.product_unit,
                        'specification_description': item.specification_description,
                        'quantity': item.quantity,
                        'unit_price': float(item.order_product.unit_price) if item.order_product else 0.0
                    })

                delivery_data = {
                    'id': delivery_note.id,
                    'delivery_number': delivery_note.delivery_number,
                    'order_id': delivery_note.order_id,
                    'order_number': delivery_note.order.order_number if delivery_note.order else '',
                    'customer_id': delivery_note.order.customer_id if delivery_note.order else None,
                    'customer_name': delivery_note.order.customer.name if delivery_note.order and delivery_note.order.customer else '',
                    'delivery_date': delivery_note.delivery_date.isoformat() if delivery_note.delivery_date else None,
                    'status': delivery_note.status,
                    'total_amount': float(total_amount),
                    'items': items_data
                }
                available_delivery_notes.append(delivery_data)

            return success_response(available_delivery_notes, "获取可用发货单成功")

        except Exception as e:
            current_app.logger.error(f"获取可用发货单失败: {str(e)}")
            return error_response(f"获取可用发货单失败: {str(e)}", code=500)


@api.route('/available-return-orders')
class AvailableReturnOrders(Resource):
    @api.doc('get_available_return_orders')
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取可用的退货单列表（已完成且未对账的退货单）"""
        try:
            customer_id = request.args.get('customer_id', type=int)
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')
            return_number = request.args.get('return_number', '')
            order_number = request.args.get('order_number', '')

            if not customer_id:
                return error_response("客户ID不能为空", code=400)

            # 检查客户是否存在
            customer = Customer.query.get(customer_id)
            if not customer:
                return error_response("客户不存在", code=404)

            # 构建查询条件
            query = ReturnOrder.query.options(
                joinedload(ReturnOrder.order),
                joinedload(ReturnOrder.items).joinedload(ReturnOrderItem.order_product),
                joinedload(ReturnOrder.items).joinedload(ReturnOrderItem.product_specification)
            ).join(Order).filter(
                Order.customer_id == customer_id,
                ReturnOrder.status == '已签收'  # 只有已签收的退货单可以对账
            )

            # 添加日期范围过滤
            if start_date:
                query = query.filter(ReturnOrder.return_date >= start_date)
            if end_date:
                query = query.filter(ReturnOrder.return_date <= end_date)

            # 添加退货单号过滤
            if return_number:
                query = query.filter(ReturnOrder.return_number.like(f'%{return_number}%'))

            # 添加订单号过滤
            if order_number:
                query = query.filter(Order.order_number.like(f'%{order_number}%'))

            return_orders = query.order_by(ReturnOrder.return_date.desc()).all()

            available_return_orders = []
            for return_order in return_orders:
                # 检查是否已经在对账单中
                existing_statement = db.session.query(StatementReturnOrder).filter(
                    StatementReturnOrder.return_order_id == return_order.id
                ).first()

                # 跳过已关联到对账单的退货单
                if existing_statement is not None:
                    continue

                # 计算退货单金额
                total_amount = Decimal('0.00')
                items_data = []

                for item in return_order.items:
                    if item.order_product:
                        unit_price = Decimal(str(item.order_product.unit_price or 0))
                        discount_rate = Decimal(str(item.order_product.discount or 0)) / Decimal('100')
                        tax_rate = Decimal(str(item.order_product.tax_rate or 0)) / Decimal('100')
                        quantity = Decimal(str(item.quantity or 0))

                        actual_price = unit_price * (Decimal('1') - discount_rate) * (Decimal('1') + tax_rate)
                        item_amount = actual_price * quantity
                        total_amount += item_amount

                        items_data.append({
                            'product_name': item.product_specification.product.name if item.product_specification and item.product_specification.product else '',
                            'product_model': item.product_specification.product.model if item.product_specification and item.product_specification.product else '',
                            'quantity': float(item.quantity),
                            'unit_price': float(item.order_product.unit_price),
                            'discount': float(item.order_product.discount or 0),
                            'tax_rate': float(item.order_product.tax_rate or 0),
                            'total_price': float(item_amount)
                        })

                return_data = {
                    'id': return_order.id,
                    'return_number': return_order.return_number,
                    'order_id': return_order.order_id,
                    'order': {
                        'id': return_order.order.id,
                        'order_number': return_order.order.order_number
                    } if return_order.order else None,
                    'return_date': return_order.return_date.isoformat() if return_order.return_date else None,
                    'status': return_order.status,
                    'reason': return_order.reason,
                    'total_amount': float(total_amount),
                    'items': items_data
                }
                available_return_orders.append(return_data)

            return success_response(available_return_orders, "获取可用退货单成功")

        except Exception as e:
            current_app.logger.error(f"获取可用退货单失败: {str(e)}")
            return error_response(f"获取可用退货单失败: {str(e)}", code=500)


@api.route('/<int:statement_id>/pdf')
class StatementPDF(Resource):
    @api.doc('export_statement_pdf')
    def get(self, statement_id):
        """导出对账单PDF"""
        try:
            statement = Statement.query.options(
                joinedload(Statement.customer),
                joinedload(Statement.delivery_notes).joinedload(DeliveryNote.items),
                joinedload(Statement.delivery_notes).joinedload(DeliveryNote.order)
            ).get(statement_id)

            if not statement:
                return error_response("对账单不存在", code=404)

            # 生成PDF内容（简化版本，实际项目中应使用专业PDF库）
            pdf_content = f"""
对账单

对账单编号: {statement.statement_number}
客户名称: {statement.customer.name if statement.customer else ''}
对账日期: {statement.statement_date}
到期日期: {statement.due_date or ''}
状态: {statement.status}
总金额: ¥{statement.total_amount}
优惠金额: ¥{statement.discount_amount}
实际金额: ¥{statement.adjusted_total_amount}

发货单明细:
"""

            for delivery_note in statement.delivery_notes:
                pdf_content += f"\n发货单号: {delivery_note.delivery_number}\n"
                pdf_content += f"发货日期: {delivery_note.delivery_date}\n"
                pdf_content += "产品明细:\n"

                for item in delivery_note.items:
                    unit_price = float(item.order_product.unit_price) if item.order_product else 0.0
                    total_price = unit_price * float(item.quantity)
                    pdf_content += f"  {item.product_name} {item.product_model} x {item.quantity} {item.product_unit} @ ¥{unit_price} = ¥{total_price}\n"

            pdf_content += f"\n备注: {statement.notes or ''}\n"

            # 创建响应
            response = make_response(pdf_content)
            response.headers['Content-Type'] = 'application/pdf'
            response.headers['Content-Disposition'] = f'attachment; filename=statement_{statement.statement_number}.pdf'

            return response

        except Exception as e:
            current_app.logger.error(f"导出对账单PDF失败: {str(e)}")
            return error_response(f"导出对账单PDF失败: {str(e)}", code=500)


@api.route('/from-customer/<int:customer_id>')
class StatementFromCustomer(Resource):
    @api.doc('create_statement_from_customer')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def post(self, customer_id):
        """基于客户创建对账单模板"""
        try:
            # 检查客户是否存在
            customer = Customer.query.get(customer_id)
            if not customer:
                return error_response("客户不存在", code=404)

            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()

            # 获取客户的可对账发货单
            query = DeliveryNote.query.options(
                joinedload(DeliveryNote.order),
                joinedload(DeliveryNote.items)
            ).join(Order).filter(
                Order.customer_id == customer_id,
                DeliveryNote.status == '已签收'
            ).filter(
                ~DeliveryNote.id.in_(
                    db.session.query(StatementDeliveryNote.delivery_note_id)
                )
            )

            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(DeliveryNote.delivery_date >= start)
                except ValueError:
                    return error_response("开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            if end_date:
                try:
                    end = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(DeliveryNote.delivery_date <= end)
                except ValueError:
                    return error_response("结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            delivery_notes = query.order_by(DeliveryNote.delivery_date).all()

            if not delivery_notes:
                return error_response("该客户没有可对账的发货单", code=400)

            # 计算总金额
            total_amount = Decimal('0.00')
            delivery_note_ids = []
            delivery_notes_data = []

            for delivery_note in delivery_notes:
                delivery_note_ids.append(delivery_note.id)

                # 计算发货单金额
                delivery_amount = Decimal('0.00')
                items_data = []

                # 直接使用发货单的总金额（已考虑折扣和税率）
                delivery_amount = Decimal(str(delivery_note.total_amount or 0))

                for item in delivery_note.items:
                    items_data.append({
                        'product_name': item.product_name,
                        'product_model': item.product_model,
                        'quantity': item.quantity,
                        'unit_price': float(item.order_product.unit_price) if item.order_product else 0.0
                    })

                total_amount += delivery_amount

                delivery_notes_data.append({
                    'id': delivery_note.id,
                    'delivery_number': delivery_note.delivery_number,
                    'delivery_date': delivery_note.delivery_date.isoformat() if delivery_note.delivery_date else None,
                    'amount': float(delivery_amount),
                    'items': items_data
                })

            # 构建对账单模板
            template_data = {
                'customer_id': customer.id,
                'customer_name': customer.name,
                'statement_date': datetime.now().date().isoformat(),
                'due_date': (datetime.now().date() + timedelta(days=30)).isoformat(),
                'status': '待确认',
                'total_amount': float(total_amount),
                'notes': f'对账期间: {start_date or "开始"} 至 {end_date or "结束"}',
                'delivery_note_ids': delivery_note_ids,
                'delivery_notes': delivery_notes_data
            }

            return success_response(template_data, "获取对账单模板成功")

        except Exception as e:
            current_app.logger.error(f"基于客户创建对账单模板失败: {str(e)}")
            return error_response(f"基于客户创建对账单模板失败: {str(e)}", code=500)


@api.route('/statistics')
class StatementStatistics(Resource):
    @api.doc('get_statement_statistics')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取对账单统计信息"""
        try:
            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()

            # 构建查询
            query = Statement.query

            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d').date()
                    query = query.filter(Statement.statement_date >= start)
                except ValueError:
                    return error_response("开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            if end_date:
                try:
                    end = datetime.strptime(end_date, '%Y-%m-%d').date()
                    query = query.filter(Statement.statement_date <= end)
                except ValueError:
                    return error_response("结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            # 统计各状态的对账单数量和金额
            status_stats = {}
            for status in STATEMENT_STATUSES:
                count = query.filter(Statement.status == status).count()
                amount = query.filter(Statement.status == status).with_entities(
                    func.sum(Statement.adjusted_total_amount)
                ).scalar() or Decimal('0.00')

                status_stats[status] = {
                    'count': count,
                    'amount': float(amount)
                }

            # 总统计
            total_count = query.count()
            total_amount = query.with_entities(func.sum(Statement.adjusted_total_amount)).scalar() or Decimal('0.00')

            statistics = {
                'total_count': total_count,
                'total_amount': float(total_amount),
                'status_statistics': status_stats,
                'period': {
                    'start_date': start_date or '全部',
                    'end_date': end_date or '全部'
                }
            }

            return success_response(statistics, "获取对账单统计信息成功")

        except Exception as e:
            current_app.logger.error(f"获取对账单统计信息失败: {str(e)}")
            return error_response(f"获取对账单统计信息失败: {str(e)}", code=500)
