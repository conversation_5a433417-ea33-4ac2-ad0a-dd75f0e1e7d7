{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 100000, "temperature": 0.2, "baseURL": "http://**************:8050/v1beta"}, "research": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 100000, "temperature": 0.1, "baseURL": "http://**************:8050/v1beta"}, "fallback": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 100000, "temperature": 0.1, "baseURL": "http://**************:8050/v1beta"}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "userId": "**********"}, "mcpServers": {"task-master-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "ANTHROPIC_API_KEY_HERE", "PERPLEXITY_API_KEY": "PERPLEXITY_API_KEY_HERE", "OPENAI_API_KEY": "OPENAI_API_KEY_HERE", "GOOGLE_API_KEY": "GOOGLE_API_KEY_HERE", "XAI_API_KEY": "XAI_API_KEY_HERE", "OPENROUTER_API_KEY": "OPENROUTER_API_KEY_HERE", "MISTRAL_API_KEY": "MISTRAL_API_KEY_HERE", "AZURE_OPENAI_API_KEY": "AZURE_OPENAI_API_KEY_HERE", "OLLAMA_API_KEY": "OLLAMA_API_KEY_HERE"}}}}