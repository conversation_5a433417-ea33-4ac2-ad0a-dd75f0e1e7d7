import{b as e,r as a,L as t,e as l,f as o,M as n,o as r,c as s,i as u,h as d,U as i,a as c,k as m,l as p,a9 as y,F as _,m as f,t as h,g as v,N as g,j as w,Z as b,$ as k,X as C,Y as V,R as P,P as z}from"./index-3d4c440c.js";import{g as D,a as G,u as M}from"./finance-789ce3e6.js";import{s as N}from"./customer-471ca075.js";import{_ as x}from"./_plugin-vue_export-helper-1b428a4d.js";const S={class:"payment-record-list"},R={class:"card-header"},U={class:"left"},Y={class:"right"},I={class:"search-bar"},j={class:"statistic-value"},K={class:"statistic-footer"},L={class:"statistic-value"},$={class:"statistic-footer"},F={class:"statistic-value"},T={class:"statistic-footer"},A={class:"statistic-value"},W={class:"statistic-footer"},X={class:"table-container"},Z={class:"pagination-container"},q={class:"dialog-footer"},B={class:"dialog-footer"},E=x({__name:"PaymentRecordList",setup(x){const E=e(),H=a(!1),J=a(!1),O=a(!1),Q=a(!1),ee=a(!1),ae=a(!1),te=a(null),le=a([]),oe=t({paymentNo:"",orderNo:"",customerId:null,paymentMethod:"",status:"",dateRange:[]}),ne=t({page:1,pageSize:20,total:0}),re=a([]),se=t({totalPayment:0,totalPaymentGrowth:0,paymentCount:0,paymentCountGrowth:0,averagePayment:0,averagePaymentGrowth:0,pendingCount:0}),ue=[{text:"最近一周",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-6048e5),[a,e]}},{text:"最近一个月",value:()=>{const e=new Date,a=new Date;return a.setMonth(a.getMonth()-1),[a,e]}},{text:"最近三个月",value:()=>{const e=new Date,a=new Date;return a.setMonth(a.getMonth()-3),[a,e]}}],de=async()=>{H.value=!0;try{const a={page:ne.page,per_page:ne.pageSize,customer_id:oe.customerId,payment_method:oe.paymentMethod,status:oe.status};oe.dateRange&&2===oe.dateRange.length&&(a.start_date=oe.dateRange[0],a.end_date=oe.dateRange[1]);try{const e=await D(a);re.value=e.data,e.page_info&&(ne.total=e.page_info.total||0)}catch(e){console.error("获取收款记录失败:",e),i.error("获取收款记录失败，使用模拟数据"),re.value=[{id:1,payment_number:"SK20250601001",order_id:1,order_number:"DD20250501001",customer_name:"北京某某科技有限公司",amount:1e4,payment_method:"bank_transfer",payment_date:"2025-06-01",bank_account:"中国银行 6225xxxx8888",status:"已确认",created_at:"2025-06-01 10:00:00"},{id:2,payment_number:"SK20250601002",order_id:2,order_number:"DD20250502002",customer_name:"上海某某贸易有限公司",amount:5e3,payment_method:"cash",payment_date:"2025-06-01",bank_account:"",status:"待确认",created_at:"2025-06-01 14:30:00"}],ne.total=2}}finally{H.value=!1}},ie=()=>{oe.paymentNo="",oe.orderNo="",oe.customerId=null,oe.paymentMethod="",oe.status="",oe.dateRange=[],ne.page=1,de()},ce=e=>{de()},me=e=>{ne.pageSize=e,de()},pe=e=>{ne.page=e,de()},ye=async e=>{if(e&&e.length>=2){J.value=!0;try{const a=await N({name_like:e});le.value=a.items||[]}catch(a){console.error("搜索客户失败:",a)}finally{J.value=!1}}else le.value=[]},_e=e=>{if(!e)return"";try{return new Date(e).toLocaleDateString("zh-CN")}catch(a){return e}},fe=e=>{if(!e)return"";try{return new Date(e).toLocaleString("zh-CN")}catch(a){return e}},he=e=>null==e?"¥ 0.00":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY"}).format(e),ve=({row:e})=>"已取消"===e.status?"cancelled-row":"",ge=()=>{E.push("/payment-records/add")},we=e=>{console.log("查看收款记录:",e),E.push(`/payment-records/detail/${e.id}`)},be=async()=>{if(te.value){ee.value=!0;try{await M(te.value.id,"已确认"),i.success("收款记录已确认"),O.value=!1,de()}catch(e){console.error("确认收款失败:",e),i.error("确认收款失败")}finally{ee.value=!1}}},ke=async()=>{if(te.value){ae.value=!0;try{await M(te.value.id,"已取消"),i.success("收款记录已取消"),Q.value=!1,de()}catch(e){console.error("取消收款失败:",e),i.error("取消收款失败")}finally{ae.value=!1}}},Ce=()=>{i.info("导出功能开发中")};return l((()=>{de(),(async()=>{try{const e=await G();e&&e.data&&(se.totalPayment=e.data.total_payment||0,se.totalPaymentGrowth=e.data.total_payment_growth||0,se.paymentCount=e.data.payment_count||0,se.paymentCountGrowth=e.data.payment_count_growth||0,se.averagePayment=e.data.average_payment||0,se.averagePaymentGrowth=e.data.average_payment_growth||0,se.pendingCount=e.data.pending_count||0)}catch(e){console.error("获取收款统计数据失败:",e),i.warning("获取收款统计数据失败，使用模拟数据"),se.totalPayment=15e3,se.totalPaymentGrowth=5.2,se.paymentCount=2,se.paymentCountGrowth=100,se.averagePayment=7500,se.averagePaymentGrowth=-47.4,se.pendingCount=1}})()})),(e,a)=>{const t=o("el-tag"),l=o("el-icon"),i=o("el-button"),D=o("el-card"),G=o("el-input"),M=o("el-form-item"),N=o("el-option"),x=o("el-select"),Ve=o("el-date-picker"),Pe=o("el-form"),ze=o("el-col"),De=o("router-link"),Ge=o("el-row"),Me=o("el-table-column"),Ne=o("el-table"),xe=o("el-pagination"),Se=o("el-dialog"),Re=n("loading");return r(),s("div",S,[u(D,{class:"header-card mb-20"},{default:d((()=>[c("div",R,[c("div",U,[a[13]||(a[13]=c("h2",{class:"page-title"},"收款管理",-1)),u(t,{type:"info"},{default:d((()=>a[12]||(a[12]=[m("管理所有订单的收款记录")]))),_:1})]),c("div",Y,[u(i,{type:"primary",onClick:ge},{default:d((()=>[u(l,null,{default:d((()=>[u(p(b))])),_:1}),a[14]||(a[14]=m(" 新增收款记录 "))])),_:1}),u(i,{type:"success",onClick:Ce},{default:d((()=>[u(l,null,{default:d((()=>[u(p(k))])),_:1}),a[15]||(a[15]=m(" 导出记录 "))])),_:1})])])])),_:1}),u(D,{class:"mb-20"},{default:d((()=>[c("div",I,[u(Pe,{inline:!0,model:oe,class:"search-form"},{default:d((()=>[u(M,{label:"收款单号"},{default:d((()=>[u(G,{modelValue:oe.paymentNo,"onUpdate:modelValue":a[0]||(a[0]=e=>oe.paymentNo=e),placeholder:"输入收款单号",clearable:"",onKeyup:y(de,["enter"])},null,8,["modelValue"])])),_:1}),u(M,{label:"关联订单"},{default:d((()=>[u(G,{modelValue:oe.orderNo,"onUpdate:modelValue":a[1]||(a[1]=e=>oe.orderNo=e),placeholder:"输入订单编号",clearable:"",onKeyup:y(de,["enter"])},null,8,["modelValue"])])),_:1}),u(M,{label:"客户名称"},{default:d((()=>[u(x,{modelValue:oe.customerId,"onUpdate:modelValue":a[2]||(a[2]=e=>oe.customerId=e),placeholder:"选择客户",clearable:"",filterable:"",remote:"","remote-method":ye,loading:J.value},{default:d((()=>[(r(!0),s(_,null,f(le.value,(e=>(r(),v(N,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),u(M,{label:"收款方式"},{default:d((()=>[u(x,{modelValue:oe.paymentMethod,"onUpdate:modelValue":a[3]||(a[3]=e=>oe.paymentMethod=e),placeholder:"选择收款方式",clearable:""},{default:d((()=>[u(N,{label:"银行转账",value:"bank_transfer"}),u(N,{label:"现金",value:"cash"}),u(N,{label:"支票",value:"check"}),u(N,{label:"电子支付",value:"electronic"}),u(N,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1}),u(M,{label:"收款状态"},{default:d((()=>[u(x,{modelValue:oe.status,"onUpdate:modelValue":a[4]||(a[4]=e=>oe.status=e),placeholder:"选择状态",clearable:""},{default:d((()=>[u(N,{label:"待确认",value:"待确认"}),u(N,{label:"已确认",value:"已确认"}),u(N,{label:"已取消",value:"已取消"})])),_:1},8,["modelValue"])])),_:1}),u(M,{label:"收款日期"},{default:d((()=>[u(Ve,{modelValue:oe.dateRange,"onUpdate:modelValue":a[5]||(a[5]=e=>oe.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",shortcuts:ue},null,8,["modelValue"])])),_:1}),u(M,null,{default:d((()=>[u(i,{type:"primary",onClick:de},{default:d((()=>[u(l,null,{default:d((()=>[u(p(C))])),_:1}),a[16]||(a[16]=m(" 搜索 "))])),_:1}),u(i,{onClick:ie},{default:d((()=>[u(l,null,{default:d((()=>[u(p(V))])),_:1}),a[17]||(a[17]=m(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])])),_:1}),u(Ge,{gutter:20,class:"mb-20"},{default:d((()=>[u(ze,{span:6},{default:d((()=>[u(D,{class:"statistic-card"},{default:d((()=>[c("div",j,h(he(se.totalPayment)),1),a[20]||(a[20]=c("div",{class:"statistic-title"},"总收款金额",-1)),c("div",K,[se.totalPaymentGrowth>=0?(r(),v(t,{key:0,type:"success"},{default:d((()=>[a[18]||(a[18]=m(" 较上月 ")),u(l,null,{default:d((()=>[u(p(P))])),_:1}),m(" "+h(se.totalPaymentGrowth)+"% ",1)])),_:1})):(r(),v(t,{key:1,type:"danger"},{default:d((()=>[a[19]||(a[19]=m(" 较上月 ")),u(l,null,{default:d((()=>[u(p(z))])),_:1}),m(" "+h(Math.abs(se.totalPaymentGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),u(ze,{span:6},{default:d((()=>[u(D,{class:"statistic-card"},{default:d((()=>[c("div",L,h(se.paymentCount),1),a[23]||(a[23]=c("div",{class:"statistic-title"},"收款记录总数",-1)),c("div",$,[se.paymentCountGrowth>=0?(r(),v(t,{key:0,type:"success"},{default:d((()=>[a[21]||(a[21]=m(" 较上月 ")),u(l,null,{default:d((()=>[u(p(P))])),_:1}),m(" "+h(se.paymentCountGrowth)+"% ",1)])),_:1})):(r(),v(t,{key:1,type:"danger"},{default:d((()=>[a[22]||(a[22]=m(" 较上月 ")),u(l,null,{default:d((()=>[u(p(z))])),_:1}),m(" "+h(Math.abs(se.paymentCountGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),u(ze,{span:6},{default:d((()=>[u(D,{class:"statistic-card"},{default:d((()=>[c("div",F,h(he(se.averagePayment)),1),a[26]||(a[26]=c("div",{class:"statistic-title"},"平均收款金额",-1)),c("div",T,[se.averagePaymentGrowth>=0?(r(),v(t,{key:0,type:"success"},{default:d((()=>[a[24]||(a[24]=m(" 较上月 ")),u(l,null,{default:d((()=>[u(p(P))])),_:1}),m(" "+h(se.averagePaymentGrowth)+"% ",1)])),_:1})):(r(),v(t,{key:1,type:"danger"},{default:d((()=>[a[25]||(a[25]=m(" 较上月 ")),u(l,null,{default:d((()=>[u(p(z))])),_:1}),m(" "+h(Math.abs(se.averagePaymentGrowth))+"% ",1)])),_:1}))])])),_:1})])),_:1}),u(ze,{span:6},{default:d((()=>[u(D,{class:"statistic-card"},{default:d((()=>[c("div",A,h(se.pendingCount),1),a[28]||(a[28]=c("div",{class:"statistic-title"},"待确认收款数",-1)),c("div",W,[u(De,{to:"/payment-pending"},{default:d((()=>[u(i,{type:"primary",size:"small"},{default:d((()=>a[27]||(a[27]=[m("查看详情")]))),_:1})])),_:1})])])),_:1})])),_:1})])),_:1}),u(D,null,{default:d((()=>[g((r(),s("div",X,[u(Ne,{data:re.value,border:"",stripe:"",style:{width:"100%"},"row-class-name":ve,onSortChange:ce},{default:d((()=>[u(Me,{type:"selection",width:"55"}),u(Me,{prop:"paymentNo",label:"收款单号","min-width":"150",sortable:"custom",fixed:""},{default:d((e=>[u(i,{type:"primary",link:"",onClick:a=>we(e.row)},{default:d((()=>[m(h(e.row.payment_number||e.row.id),1)])),_:2},1032,["onClick"])])),_:1}),u(Me,{prop:"orderNo",label:"关联订单","min-width":"150",sortable:"custom"},{default:d((e=>[u(i,{type:"success",link:"",onClick:a=>{return t=e.row.order_id,void E.push(`/orders/detail/${t}`);var t}},{default:d((()=>[m(h(e.row.order_number),1)])),_:2},1032,["onClick"])])),_:1}),u(Me,{prop:"customerName",label:"客户名称","min-width":"200",sortable:"custom"},{default:d((e=>[u(i,{type:"info",link:"",onClick:a=>{return t=e.row.customer_id,void E.push(`/customers/detail/${t}`);var t}},{default:d((()=>[m(h(e.row.customer_name),1)])),_:2},1032,["onClick"])])),_:1}),u(Me,{prop:"amount",label:"收款金额","min-width":"120",sortable:"custom"},{default:d((e=>[m(h(he(e.row.amount)),1)])),_:1}),u(Me,{prop:"paymentMethod",label:"收款方式",width:"120"},{default:d((e=>[u(t,null,{default:d((()=>{return[m(h((a=e.row.payment_method,{bank_transfer:"银行转账",cash:"现金",check:"支票",electronic:"电子支付",online_payment:"在线支付",other:"其他"}[a]||a)),1)];var a})),_:2},1024)])),_:1}),u(Me,{prop:"paymentDate",label:"收款日期",width:"120",sortable:"custom"},{default:d((e=>[m(h(_e(e.row.payment_date)),1)])),_:1}),u(Me,{prop:"bankAccount",label:"收款账户","min-width":"200"},{default:d((e=>[m(h(e.row.bank_account||"-"),1)])),_:1}),u(Me,{prop:"status",label:"状态",width:"100"},{default:d((e=>{return[u(t,{type:(a=e.row.status,{"待确认":"warning","已确认":"success","已取消":"danger"}[a]||"info")},{default:d((()=>[m(h(e.row.status),1)])),_:2},1032,["type"])];var a})),_:1}),u(Me,{prop:"created_at",label:"录入时间",width:"180",sortable:"custom"},{default:d((e=>[m(h(fe(e.row.created_at)),1)])),_:1}),u(Me,{fixed:"right",label:"操作",width:"150"},{default:d((e=>["待确认"===e.row.status?(r(),v(i,{key:0,link:"",type:"primary",size:"small",onClick:a=>{return t=e.row,te.value=t,void(O.value=!0);var t}},{default:d((()=>a[29]||(a[29]=[m("确认")]))),_:2},1032,["onClick"])):w("",!0),"待确认"===e.row.status?(r(),v(i,{key:1,link:"",type:"danger",size:"small",onClick:a=>{return t=e.row,te.value=t,void(Q.value=!0);var t}},{default:d((()=>a[30]||(a[30]=[m("取消")]))),_:2},1032,["onClick"])):w("",!0),u(i,{link:"",type:"primary",size:"small",onClick:a=>we(e.row)},{default:d((()=>a[31]||(a[31]=[m("查看")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),c("div",Z,[u(xe,{"current-page":ne.page,"onUpdate:currentPage":a[6]||(a[6]=e=>ne.page=e),"page-size":ne.pageSize,"onUpdate:pageSize":a[7]||(a[7]=e=>ne.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:ne.total,onSizeChange:me,onCurrentChange:pe},null,8,["current-page","page-size","total"])])])),[[Re,H.value]])])),_:1}),u(Se,{modelValue:O.value,"onUpdate:modelValue":a[9]||(a[9]=e=>O.value=e),title:"确认收款",width:"30%"},{footer:d((()=>[c("span",q,[u(i,{onClick:a[8]||(a[8]=e=>O.value=!1)},{default:d((()=>a[32]||(a[32]=[m("取消")]))),_:1}),u(i,{type:"primary",onClick:be,loading:ee.value},{default:d((()=>a[33]||(a[33]=[m("确认")]))),_:1},8,["loading"])])])),default:d((()=>[a[34]||(a[34]=c("p",null,"确认此笔收款记录吗？确认后将更新订单的已付款金额。",-1))])),_:1},8,["modelValue"]),u(Se,{modelValue:Q.value,"onUpdate:modelValue":a[11]||(a[11]=e=>Q.value=e),title:"取消收款",width:"30%"},{footer:d((()=>[c("span",B,[u(i,{onClick:a[10]||(a[10]=e=>Q.value=!1)},{default:d((()=>a[35]||(a[35]=[m("关闭")]))),_:1}),u(i,{type:"danger",onClick:ke,loading:ae.value},{default:d((()=>a[36]||(a[36]=[m("确认取消")]))),_:1},8,["loading"])])])),default:d((()=>[a[37]||(a[37]=c("p",null,"确认取消此笔收款记录吗？",-1))])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-8cc688e9"]]);export{E as default};
