import request from './request'

// 发货单相关API
export const deliveryNoteApi = {
  // 获取发货单列表
  getList: (params: any) => {
    return request.get('/delivery-notes', { params })
  },

  // 获取发货单详情
  getById: (id: number) => {
    return request.get(`/delivery-notes/${id}`)
  },

  // 创建发货单
  create: (data: any) => {
    return request.post('/delivery-notes', data)
  },

  // 更新发货单
  update: (id: number, data: any) => {
    return request.put(`/delivery-notes/${id}`, data)
  },

  // 删除发货单
  delete: (id: number) => {
    return request.delete(`/delivery-notes/${id}`)
  },

  // 更新发货单状态
  updateStatus: (id: number, status: string, notes?: string) => {
    return request.put(`/delivery-notes/${id}/status`, { status, notes })
  },

  // 确认发货
  confirmShipping: (id: number, data?: any) => {
    return request.post(`/delivery-notes/${id}/confirm-shipping`, data)
  },

  // 确认完成
  confirmCompletion: (id: number, data?: any) => {
    return request.post(`/delivery-notes/${id}/confirm-completion`, data)
  },

  // 作废发货单
  cancel: (id: number, reason?: string) => {
    return request.post(`/delivery-notes/${id}/cancel`, { reason })
  },

  // 导出发货单
  export: (id: number, params: {
    format: 'xlsx' | 'pdf'
    columns?: string[]
    include_header?: boolean
  }) => {
    const queryParams = new URLSearchParams()
    queryParams.append('format', params.format)
    if (params.columns && params.columns.length > 0) {
      queryParams.append('columns', params.columns.join(','))
    }
    if (params.include_header !== undefined) {
      queryParams.append('include_header', params.include_header.toString())
    }

    return fetch(`/api/v1/delivery-export/${id}/export?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        'Content-Type': 'application/json'
      }
    }).then(response => {
      if (!response.ok) {
        throw new Error(`导出失败: ${response.statusText}`)
      }
      return response.blob()
    })
  },

  // 批量导出发货单
  batchExport: (ids: number[], format: string = 'pdf') => {
    return request.post('/delivery-notes/batch-export', {
      delivery_note_ids: ids,
      format
    }, {
      responseType: 'blob'
    })
  },

  // 打印发货单
  print: (id: number) => {
    return request.get(`/delivery-notes/${id}/print`, {
      responseType: 'blob'
    })
  },

  // 批量打印发货单
  batchPrint: (ids: number[]) => {
    return request.post('/delivery-notes/batch-print', {
      delivery_note_ids: ids
    }, {
      responseType: 'blob'
    })
  },

  // 获取发货单统计
  getStats: (params?: any) => {
    return request.get('/delivery-notes/stats', { params })
  }
}

// 发货单项目相关API
export const deliveryNoteItemApi = {
  // 获取发货单项目列表
  getList: (deliveryNoteId: number) => {
    return request.get(`/delivery-notes/${deliveryNoteId}/items`)
  },

  // 添加发货单项目
  add: (deliveryNoteId: number, data: any) => {
    return request.post(`/delivery-notes/${deliveryNoteId}/items`, data)
  },

  // 更新发货单项目
  update: (deliveryNoteId: number, itemId: number, data: any) => {
    return request.put(`/delivery-notes/${deliveryNoteId}/items/${itemId}`, data)
  },

  // 删除发货单项目
  delete: (deliveryNoteId: number, itemId: number) => {
    return request.delete(`/delivery-notes/${deliveryNoteId}/items/${itemId}`)
  }
}

// 物流跟踪相关API
export const logisticsTrackingApi = {
  // 获取物流跟踪信息
  getTracking: (deliveryNoteId: number) => {
    return request.get(`/delivery-notes/${deliveryNoteId}/tracking`)
  },

  // 更新物流信息
  updateLogistics: (deliveryNoteId: number, data: any) => {
    return request.put(`/delivery-notes/${deliveryNoteId}/logistics`, data)
  },

  // 添加物流跟踪记录
  addTrackingRecord: (deliveryNoteId: number, data: any) => {
    return request.post(`/delivery-notes/${deliveryNoteId}/tracking`, data)
  }
}

// 兼容原项目的API函数
export const getDeliveryNotes = deliveryNoteApi.getList
export const getDeliveryNote = deliveryNoteApi.getById
export const createDeliveryNote = deliveryNoteApi.create
export const updateDeliveryNote = deliveryNoteApi.update
export const deleteDeliveryNote = deliveryNoteApi.delete
export const updateDeliveryNoteStatus = deliveryNoteApi.updateStatus
export const confirmDeliveryShipping = deliveryNoteApi.confirmShipping
export const confirmDeliveryCompletion = deliveryNoteApi.confirmCompletion
export const cancelDeliveryNote = deliveryNoteApi.cancel
export const exportDeliveryNote = deliveryNoteApi.export
export const exportDeliveryNotes = deliveryNoteApi.batchExport
export const printDeliveryNote = deliveryNoteApi.print
export const printDeliveryNotes = deliveryNoteApi.batchPrint
export const getDeliveryNoteStats = deliveryNoteApi.getStats

export const getDeliveryNoteItems = deliveryNoteItemApi.getList
export const addDeliveryNoteItem = deliveryNoteItemApi.add
export const updateDeliveryNoteItem = deliveryNoteItemApi.update
export const deleteDeliveryNoteItem = deliveryNoteItemApi.delete

export const getLogisticsTracking = logisticsTrackingApi.getTracking
export const updateLogisticsInfo = logisticsTrackingApi.updateLogistics
export const addTrackingRecord = logisticsTrackingApi.addTrackingRecord
