import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// 主题类型
export type ThemeMode = 'light' | 'dark' | 'auto'

// 语言类型
export type Language = 'zh-CN' | 'en-US'

// 布局类型
export type LayoutMode = 'vertical' | 'horizontal' | 'mix'

// 应用设置接口
export interface AppSettings {
  // 主题设置
  theme: ThemeMode
  primaryColor: string
  
  // 布局设置
  layout: LayoutMode
  sidebarCollapsed: boolean
  sidebarWidth: number
  headerHeight: number
  
  // 功能设置
  showBreadcrumb: boolean
  showTabs: boolean
  showFooter: boolean
  enableWatermark: boolean
  
  // 语言设置
  language: Language
  
  // 其他设置
  enableTransition: boolean
  transitionName: string
  enableProgress: boolean
}

// 应用状态管理
export const useAppStore = defineStore(
  'app',
  () => {
    // 默认设置
    const defaultSettings: AppSettings = {
      theme: 'light',
      primaryColor: '#409EFF',
      layout: 'vertical',
      sidebarCollapsed: false,
      sidebarWidth: 200,
      headerHeight: 50,
      showBreadcrumb: true,
      showTabs: false,
      showFooter: false,
      enableWatermark: false,
      language: 'zh-CN',
      enableTransition: true,
      transitionName: 'fade',
      enableProgress: true,
    }

    // 状态
    const settings = ref<AppSettings>({ ...defaultSettings })
    const loading = ref<boolean>(false)
    const device = ref<'desktop' | 'tablet' | 'mobile'>('desktop')

    // 计算属性
    const isDark = computed(() => {
      if (settings.value.theme === 'auto') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches
      }
      return settings.value.theme === 'dark'
    })

    const sidebarWidth = computed(() => {
      return settings.value.sidebarCollapsed ? 64 : settings.value.sidebarWidth
    })

    const isMobile = computed(() => device.value === 'mobile')
    const isTablet = computed(() => device.value === 'tablet')
    const isDesktop = computed(() => device.value === 'desktop')

    // 切换侧边栏折叠状态
    const toggleSidebar = () => {
      settings.value.sidebarCollapsed = !settings.value.sidebarCollapsed
    }

    // 设置侧边栏折叠状态
    const setSidebarCollapsed = (collapsed: boolean) => {
      settings.value.sidebarCollapsed = collapsed
    }

    // 切换主题
    const toggleTheme = () => {
      settings.value.theme = settings.value.theme === 'light' ? 'dark' : 'light'
      applyTheme()
    }

    // 设置主题
    const setTheme = (theme: ThemeMode) => {
      settings.value.theme = theme
      applyTheme()
    }

    // 应用主题
    const applyTheme = () => {
      const html = document.documentElement
      if (isDark.value) {
        html.classList.add('dark')
      } else {
        html.classList.remove('dark')
      }
    }

    // 设置主色调
    const setPrimaryColor = (color: string) => {
      settings.value.primaryColor = color
      // 应用主色调到CSS变量
      document.documentElement.style.setProperty('--el-color-primary', color)
    }

    // 设置语言
    const setLanguage = (lang: Language) => {
      settings.value.language = lang
      // TODO: 这里可以集成i18n
      document.documentElement.lang = lang
    }

    // 设置布局模式
    const setLayout = (layout: LayoutMode) => {
      settings.value.layout = layout
    }

    // 设置设备类型
    const setDevice = (deviceType: 'desktop' | 'tablet' | 'mobile') => {
      device.value = deviceType
      
      // 移动设备自动折叠侧边栏
      if (deviceType === 'mobile') {
        setSidebarCollapsed(true)
      }
    }

    // 设置加载状态
    const setLoading = (isLoading: boolean) => {
      loading.value = isLoading
    }

    // 更新设置
    const updateSettings = (newSettings: Partial<AppSettings>) => {
      settings.value = { ...settings.value, ...newSettings }
      
      // 应用相关设置
      if (newSettings.theme) {
        applyTheme()
      }
      if (newSettings.primaryColor) {
        setPrimaryColor(newSettings.primaryColor)
      }
      if (newSettings.language) {
        setLanguage(newSettings.language)
      }
    }

    // 重置设置
    const resetSettings = () => {
      settings.value = { ...defaultSettings }
      applyTheme()
      setPrimaryColor(defaultSettings.primaryColor)
      setLanguage(defaultSettings.language)
    }

    // 初始化应用设置
    const initAppSettings = () => {
      // 应用主题
      applyTheme()
      
      // 应用主色调
      setPrimaryColor(settings.value.primaryColor)
      
      // 设置语言
      setLanguage(settings.value.language)
      
      // 检测设备类型
      const checkDevice = () => {
        const width = window.innerWidth
        if (width < 768) {
          setDevice('mobile')
        } else if (width < 1024) {
          setDevice('tablet')
        } else {
          setDevice('desktop')
        }
      }
      
      checkDevice()
      
      // 监听窗口大小变化
      window.addEventListener('resize', checkDevice)
      
      // 监听系统主题变化
      if (settings.value.theme === 'auto') {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        mediaQuery.addEventListener('change', applyTheme)
      }
    }

    return {
      // 状态
      settings,
      loading,
      device,
      
      // 计算属性
      isDark,
      sidebarWidth,
      isMobile,
      isTablet,
      isDesktop,
      
      // 方法
      toggleSidebar,
      setSidebarCollapsed,
      toggleTheme,
      setTheme,
      setPrimaryColor,
      setLanguage,
      setLayout,
      setDevice,
      setLoading,
      updateSettings,
      resetSettings,
      initAppSettings,
    }
  },
  {
    // 持久化配置
    persist: true,
  }
)
