"""
分页工具
提供统一的分页查询功能
"""
from flask import request
from sqlalchemy.orm import Query
from typing import Dict, Any, List, Optional, Tuple
import math

from app.utils.response import paginated_response
from app.utils.exceptions import ValidationError


class PaginationHelper:
    """分页助手类"""
    
    DEFAULT_PAGE = 1
    DEFAULT_PER_PAGE = 20
    MAX_PER_PAGE = 100
    
    @classmethod
    def get_pagination_params(cls) -> Tuple[int, int]:
        """
        从请求参数中获取分页参数
        
        Returns:
            tuple: (page, per_page)
        """
        try:
            page = int(request.args.get('page', cls.DEFAULT_PAGE))
            per_page = int(request.args.get('per_page', cls.DEFAULT_PER_PAGE))
        except (ValueError, TypeError):
            raise ValidationError(
                message='分页参数格式错误',
                errors={
                    'page': ['页码必须是正整数'],
                    'per_page': ['每页数量必须是正整数']
                }
            )
        
        # 验证参数范围
        if page < 1:
            raise ValidationError(
                message='分页参数错误',
                errors={'page': ['页码必须大于0']}
            )
        
        if per_page < 1:
            raise ValidationError(
                message='分页参数错误',
                errors={'per_page': ['每页数量必须大于0']}
            )
        
        if per_page > cls.MAX_PER_PAGE:
            raise ValidationError(
                message='分页参数错误',
                errors={'per_page': [f'每页数量不能超过{cls.MAX_PER_PAGE}']}
            )
        
        return page, per_page
    
    @classmethod
    def paginate_query(
        cls, 
        query: Query, 
        page: Optional[int] = None, 
        per_page: Optional[int] = None,
        error_out: bool = True
    ) -> Dict[str, Any]:
        """
        对查询进行分页
        
        Args:
            query: SQLAlchemy查询对象
            page: 页码
            per_page: 每页数量
            error_out: 是否在页码超出范围时抛出异常
            
        Returns:
            dict: 包含分页数据和元信息的字典
        """
        if page is None or per_page is None:
            page, per_page = cls.get_pagination_params()
        
        # 获取总记录数
        total = query.count()
        
        # 计算总页数
        total_pages = math.ceil(total / per_page) if per_page > 0 else 0
        
        # 验证页码范围
        if error_out and page > total_pages and total_pages > 0:
            raise ValidationError(
                message='页码超出范围',
                errors={'page': [f'页码不能超过{total_pages}']}
            )
        
        # 计算偏移量
        offset = (page - 1) * per_page
        
        # 执行分页查询
        items = query.offset(offset).limit(per_page).all()
        
        # 计算分页信息
        has_prev = page > 1
        has_next = page < total_pages
        
        return {
            'items': items,
            'total': total,
            'page': page,
            'per_page': per_page,
            'total_pages': total_pages,
            'has_prev': has_prev,
            'has_next': has_next,
            'prev_page': page - 1 if has_prev else None,
            'next_page': page + 1 if has_next else None
        }
    
    @classmethod
    def paginate_and_response(
        cls,
        query: Query,
        serializer_func: callable = None,
        page: Optional[int] = None,
        per_page: Optional[int] = None,
        message: str = '查询成功'
    ) -> Tuple[Any, int]:
        """
        分页查询并返回标准响应格式
        
        Args:
            query: SQLAlchemy查询对象
            serializer_func: 序列化函数，用于转换查询结果
            page: 页码
            per_page: 每页数量
            message: 响应消息
            
        Returns:
            tuple: (响应JSON, HTTP状态码)
        """
        pagination_data = cls.paginate_query(query, page, per_page)
        
        # 序列化数据
        if serializer_func:
            items = [serializer_func(item) for item in pagination_data['items']]
        else:
            # 如果模型有to_dict方法，使用它
            items = []
            for item in pagination_data['items']:
                if hasattr(item, 'to_dict'):
                    items.append(item.to_dict())
                else:
                    items.append(str(item))
        
        return paginated_response(
            data=items,
            total=pagination_data['total'],
            page=pagination_data['page'],
            per_page=pagination_data['per_page'],
            message=message
        )


def get_sort_params(allowed_fields: List[str]) -> List[str]:
    """
    从请求参数中获取排序参数
    
    Args:
        allowed_fields: 允许排序的字段列表
        
    Returns:
        list: 排序字段列表，格式为 ['field_name', '-field_name']
    """
    sort_param = request.args.get('sort', '')
    if not sort_param:
        return []
    
    sort_fields = []
    for field in sort_param.split(','):
        field = field.strip()
        if not field:
            continue
            
        # 检查是否为降序（以-开头）
        if field.startswith('-'):
            field_name = field[1:]
            direction = 'desc'
        else:
            field_name = field
            direction = 'asc'
        
        # 验证字段是否允许排序
        if field_name not in allowed_fields:
            raise ValidationError(
                message='排序参数错误',
                errors={'sort': [f'字段 {field_name} 不允许排序']}
            )
        
        sort_fields.append(f'{field_name}:{direction}')
    
    return sort_fields


def apply_filters(query: Query, filters: Dict[str, Any]) -> Query:
    """
    应用过滤条件到查询

    Args:
        query: SQLAlchemy查询对象
        filters: 过滤条件字典

    Returns:
        Query: 应用过滤条件后的查询对象
    """
    for field, value in filters.items():
        if value is not None and value != '':
            # 这里可以根据需要实现更复杂的过滤逻辑
            # 例如：模糊查询、范围查询等
            try:
                # 获取查询的模型类
                model_class = query.column_descriptions[0]['type'] if query.column_descriptions else None
                if model_class and hasattr(model_class, field):
                    query = query.filter(getattr(model_class, field) == value)
            except (IndexError, AttributeError):
                # 如果无法获取模型类，跳过此过滤条件
                continue

    return query
