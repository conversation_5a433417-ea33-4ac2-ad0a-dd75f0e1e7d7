/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
var e={version:"0.18.5"},t=1252,r=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],a={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},n=function(e){-1!=r.indexOf(e)&&(t=a[0]=e)};var s=function(e){n(e)};function i(){s(1200),n(1252)}function o(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function c(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var l,f=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return 255==t&&254==r?function(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}(e.slice(2)):254==t&&255==r?c(e.slice(2)):65279==t?e.slice(1):e},h=function(e){return String.fromCharCode(e)},u=function(e){return String.fromCharCode(e)},d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function p(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0,c=0,l=0;l<e.length;)s=(r=e.charCodeAt(l++))>>2,i=(3&r)<<4|(a=e.charCodeAt(l++))>>4,o=(15&a)<<2|(n=e.charCodeAt(l++))>>6,c=63&n,isNaN(a)?o=c=64:isNaN(n)&&(c=64),t+=d.charAt(s)+d.charAt(i)+d.charAt(o)+d.charAt(c);return t}function m(e){var t="",r=0,a=0,n=0,s=0,i=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var c=0;c<e.length;)r=d.indexOf(e.charAt(c++))<<2|(s=d.indexOf(e.charAt(c++)))>>4,t+=String.fromCharCode(r),a=(15&s)<<4|(i=d.indexOf(e.charAt(c++)))>>2,64!==i&&(t+=String.fromCharCode(a)),n=(3&i)<<6|(o=d.indexOf(e.charAt(c++))),64!==o&&(t+=String.fromCharCode(n));return t}var g=function(){return"undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node}(),v=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function b(e){return g?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function w(e){return g?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var T=function(e){return g?v(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function E(e){if("undefined"==typeof ArrayBuffer)return T(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=255&e.charCodeAt(a);return t}function S(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function y(e){if("undefined"==typeof ArrayBuffer)throw new Error("Unsupported");if(e instanceof ArrayBuffer)return y(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var k=g?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:v(e)})))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else{if("string"==typeof e[t])throw"wtf";a.set(new Uint8Array(e[t]),r)}return a}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};var _=/\u0000/g,A=/[\u0001-\u0006]/g;function x(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function C(e,t){var r=""+e;return r.length>=t?r:Ve("0",t-r.length)+r}function O(e,t){var r=""+e;return r.length>=t?r:Ve(" ",t-r.length)+r}function R(e,t){var r=""+e;return r.length>=t?r:r+Ve(" ",t-r.length)}var I=Math.pow(2,32);function N(e,t){return e>I||e<-I?function(e,t){var r=""+Math.round(e);return r.length>=t?r:Ve("0",t-r.length)+r}(e,t):function(e,t){var r=""+e;return r.length>=t?r:Ve("0",t-r.length)+r}(Math.round(e),t)}function D(e,t){return t=t||0,e.length>=7+t&&103==(32|e.charCodeAt(t))&&101==(32|e.charCodeAt(t+1))&&110==(32|e.charCodeAt(t+2))&&101==(32|e.charCodeAt(t+3))&&114==(32|e.charCodeAt(t+4))&&97==(32|e.charCodeAt(t+5))&&108==(32|e.charCodeAt(t+6))}var F=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],P=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];var L={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},M={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},U={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function B(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,o=0,c=1,l=0,f=0,h=Math.floor(n);l<t&&(o=(h=Math.floor(n))*i+s,f=h*l+c,!(n-h<5e-8));)n=1/(n-h),s=i,i=o,c=l,l=f;if(f>t&&(l>t?(f=c,o=s):(f=l,o=i)),!r)return[0,a*o,f];var u=Math.floor(a*o/f);return[u,a*o-u*f,f]}function W(e,t,r){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],o={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(a+=1462),o.u>.9999&&(o.u=0,86400==++n&&(o.T=n=0,++a,++o.D)),60===a)i=r?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var c=new Date(1900,0,1);c.setDate(c.getDate()+a-1),i=[c.getFullYear(),c.getMonth()+1,c.getDate()],s=c.getDay(),a<60&&(s=(s+6)%7),r&&(s=function(e,t){t[0]-=581;var r=e.getDay();e<60&&(r=(r+6)%7);return r}(c,i))}return o.y=i[0],o.m=i[1],o.d=i[2],o.S=n%60,n=Math.floor(n/60),o.M=n%60,n=Math.floor(n/60),o.H=n,o.q=s,o}var H=new Date(1899,11,31,0,0,0),V=H.getTime(),z=new Date(1900,2,1,0,0,0);function G(e,t){var r=e.getTime();return t?r-=1262304e5:e>=z&&(r+=864e5),(r-(V+6e4*(e.getTimezoneOffset()-H.getTimezoneOffset())))/864e5}function j(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function X(e){var t,r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return t=r>=-4&&r<=-1?e.toPrecision(10+r):Math.abs(r)<=9?function(e){var t=e<0?12:11,r=j(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)}(e):10===r?e.toFixed(10).substr(0,12):function(e){var t=j(e.toFixed(11));return t.length>(e<0?12:11)||"0"===t||"-0"===t?e.toPrecision(6):t}(e),j(function(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}(t.toUpperCase()))}function Y(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):X(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return me(14,G(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function K(e,t,r,a){var n,s="",i=0,o=0,c=r.y,l=0;switch(e){case 98:c=r.y+543;case 121:switch(t.length){case 1:case 2:n=c%100,l=2;break;default:n=c%1e4,l=4}break;case 109:switch(t.length){case 1:case 2:n=r.m,l=t.length;break;case 3:return P[r.m-1][1];case 5:return P[r.m-1][0];default:return P[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:n=r.d,l=t.length;break;case 3:return F[r.q][0];default:return F[r.q][1]}break;case 104:switch(t.length){case 1:case 2:n=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:n=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:n=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(o=a>=2?3===a?1e3:100:1===a?10:1,(i=Math.round(o*(r.S+r.u)))>=60*o&&(i=0),"s"===t?0===i?"0":""+i/o:(s=C(i,2+a),"ss"===t?s.substr(0,2):"."+s.substr(2,t.length-1))):C(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":n=24*r.D+r.H;break;case"[m]":case"[mm]":n=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":n=60*(60*(24*r.D+r.H)+r.M)+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=3===t.length?1:2;break;case 101:n=c,l=1}return l>0?C(n,l):""}function J(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var q=/%/g;function Z(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+Z(e,-t);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),-1===(r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).indexOf("e")){var i=Math.floor(Math.log(t)*Math.LOG10E);for(-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s);"0."===r.substr(0,2);)r=(r=r.charAt(0)+r.substr(2,n)+"."+r.substr(2+n)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,a){return t+r+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var Q=/# (\?+)( ?)\/( ?)(\d+)/;var ee=/^#*0*\.([0#]+)/,te=/\).*[0#]/,re=/\(###\) ###\\?-####/;function ae(e){for(var t,r="",a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function ne(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function se(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function ie(e,t,r){if(40===e.charCodeAt(0)&&!t.match(te)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?ie("n",a,r):"("+ie("n",a,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return le(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var a=t.replace(q,""),n=t.length-a.length;return le(e,a,r*Math.pow(10,2*n))+Ve("%",n)}(e,t,r);if(-1!==t.indexOf("E"))return Z(t,r);if(36===t.charCodeAt(0))return"$"+ie(e,t.substr(" "==t.charAt(1)?2:1),r);var n,s,i,o,c=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+N(c,t.length);if(t.match(/^[#?]+$/))return"0"===(n=N(r,0))&&(n=""),n.length>t.length?n:ae(t.substr(0,t.length-n.length))+n;if(s=t.match(Q))return function(e,t,r){var a=parseInt(e[4],10),n=Math.round(t*a),s=Math.floor(n/a),i=n-s*a,o=a;return r+(0===s?"":""+s)+" "+(0===i?Ve(" ",e[1].length+1+e[4].length):O(i,e[1].length)+e[2]+"/"+e[3]+C(o,e[4].length))}(s,c,l);if(t.match(/^#+0+$/))return l+N(c,t.length-t.indexOf("0"));if(s=t.match(ee))return n=ne(r,s[1].length).replace(/^([^\.]+)$/,"$1."+ae(s[1])).replace(/\.$/,"."+ae(s[1])).replace(/\.(\d*)$/,(function(e,t){return"."+t+Ve("0",ae(s[1]).length-t.length)})),-1!==t.indexOf("0.")?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+ne(c,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+J(N(c,0));if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+ie(e,t,-r):J(""+(Math.floor(r)+function(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}(r,s[1].length)))+"."+C(se(r,s[1].length),s[1].length);if(s=t.match(/^#,#*,#0/))return ie(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=x(ie(e,t.replace(/[\\-]/g,""),r)),i=0,x(x(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(t.match(re))return"("+(n=ie(e,"##########",r)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=B(c,Math.pow(10,i)-1,!1),n=""+l," "==(f=le("n",s[1],o[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=R(o[2],i)).length<s[4].length&&(f=ae(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((o=B(c,Math.pow(10,i)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?O(o[1],i)+s[2]+"/"+s[3]+R(o[2],i):Ve(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=N(r,0),t.length<=n.length?n:ae(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0?]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=t.indexOf(".")-i,u=t.length-n.length-h;return ae(t.substr(0,h)+n+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return i=se(r,s[1].length),r<0?"-"+ie(e,t,-r):J(function(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?C(0,3-e.length):"")+e}))+"."+C(i,s[1].length);switch(t){case"###,##0.00":return ie(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=J(N(c,0));return"0"!==d?l+d:"";case"###,###.00":return ie(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return ie(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function oe(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+oe(e,-t);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),!(r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).match(/[Ee]/)){var i=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,a){return t+r+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function ce(e,t,r){if(40===e.charCodeAt(0)&&!t.match(te)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?ce("n",a,r):"("+ce("n",a,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return le(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var a=t.replace(q,""),n=t.length-a.length;return le(e,a,r*Math.pow(10,2*n))+Ve("%",n)}(e,t,r);if(-1!==t.indexOf("E"))return oe(t,r);if(36===t.charCodeAt(0))return"$"+ce(e,t.substr(" "==t.charAt(1)?2:1),r);var n,s,i,o,c=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+C(c,t.length);if(t.match(/^[#?]+$/))return n=""+r,0===r&&(n=""),n.length>t.length?n:ae(t.substr(0,t.length-n.length))+n;if(s=t.match(Q))return function(e,t,r){return r+(0===t?"":""+t)+Ve(" ",e[1].length+2+e[4].length)}(s,c,l);if(t.match(/^#+0+$/))return l+C(c,t.length-t.indexOf("0"));if(s=t.match(ee))return n=(n=(""+r).replace(/^([^\.]+)$/,"$1."+ae(s[1])).replace(/\.$/,"."+ae(s[1]))).replace(/\.(\d*)$/,(function(e,t){return"."+t+Ve("0",ae(s[1]).length-t.length)})),-1!==t.indexOf("0.")?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+(""+c).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+J(""+c);if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+ce(e,t,-r):J(""+r)+"."+Ve("0",s[1].length);if(s=t.match(/^#,#*,#0/))return ce(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=x(ce(e,t.replace(/[\\-]/g,""),r)),i=0,x(x(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(t.match(re))return"("+(n=ce(e,"##########",r)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=B(c,Math.pow(10,i)-1,!1),n=""+l," "==(f=le("n",s[1],o[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=R(o[2],i)).length<s[4].length&&(f=ae(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((o=B(c,Math.pow(10,i)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?O(o[1],i)+s[2]+"/"+s[3]+R(o[2],i):Ve(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=""+r,t.length<=n.length?n:ae(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=t.indexOf(".")-i,u=t.length-n.length-h;return ae(t.substr(0,h)+n+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+ce(e,t,-r):J(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?C(0,3-e.length):"")+e}))+"."+C(0,s[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=J(""+c);return"0"!==d?l+d:"";default:if(t.match(/\.[0#?]*$/))return ce(e,t.slice(0,t.lastIndexOf(".")),r)+ae(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function le(e,t,r){return(0|r)===r?ce(e,t,r):ie(e,t,r)}var fe=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function he(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":D(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;if("上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(a=r;"]"!==e.charAt(t++)&&t<e.length;)a+=e.charAt(t);if(a.match(fe))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;default:++t}return!1}var ue=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function de(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function pe(e,t){var r=function(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[a,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var i=r[0].match(ue),o=r[1].match(ue);return de(t,i)?[a,r[0]]:de(t,o)?[a,r[1]]:[a,r[null!=i&&null!=o?2:1]]}return[a,s]}function me(e,t,r){null==r&&(r={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(a=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:L)[e])&&(a=r.table&&r.table[M[e]]||L[M[e]]),null==a&&(a=U[e]||"General")}if(D(a,0))return Y(t,r);t instanceof Date&&(t=G(t,r.date1904));var n=pe(a,t);if(D(n[1]))return Y(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,a){for(var n,s,i,o=[],c="",l=0,f="",h="t",u="H";l<e.length;)switch(f=e.charAt(l)){case"G":if(!D(e,l))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"G",v:"General"},l+=7;break;case'"':for(c="";34!==(i=e.charCodeAt(++l))&&l<e.length;)c+=String.fromCharCode(i);o[o.length]={t:"t",v:c},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++l;break;case"_":o[o.length]={t:"t",v:" "},l+=2;break;case"@":o[o.length]={t:"T",v:t},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==n&&null==(n=W(t,r,"2"===e.charAt(l+1))))return"";o[o.length]={t:"X",v:e.substr(l,2)},h=f,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==n&&null==(n=W(t,r)))return"";for(c=f;++l<e.length&&e.charAt(l).toLowerCase()===f;)c+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),o[o.length]={t:f,v:c},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=W(t,r)),"A/P"===e.substr(l,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"上午/下午"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==n&&"T"===m.t)return"";o[o.length]=m,h=f;break;case"[":for(c=f;"]"!==e.charAt(l++)&&l<e.length;)c+=e.charAt(l);if("]"!==c.slice(-1))throw'unterminated "[" block: |'+c+"|";if(c.match(fe)){if(null==n&&null==(n=W(t,r)))return"";o[o.length]={t:"Z",v:c.toLowerCase()},h=c.charAt(1)}else c.indexOf("$")>-1&&(c=(c.match(/\$([^-\[\]]*)/)||[])[1]||"$",he(e)||(o[o.length]={t:"t",v:c}));break;case".":if(null!=n){for(c=f;++l<e.length&&"0"===(f=e.charAt(l));)c+=f;o[o.length]={t:"s",v:c};break}case"0":case"#":for(c=f;++l<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(l))>-1;)c+=f;o[o.length]={t:"n",v:c};break;case"?":for(c=f;e.charAt(++l)===f;)c+=f;o[o.length]={t:f,v:c},h=f;break;case"*":++l," "!=e.charAt(l)&&"*"!=e.charAt(l)||++l;break;case"(":case")":o[o.length]={t:1===a?"t":f,v:f},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(c=f;l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1;)c+=e.charAt(l);o[o.length]={t:"D",v:c};break;case" ":o[o.length]={t:f,v:f},++l;break;case"$":o[o.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"t",v:f},++l}var g,v=0,b=0;for(l=o.length-1,h="t";l>=0;--l)switch(o[l].t){case"h":case"H":o[l].t=u,h="h",v<1&&(v=1);break;case"s":(g=o[l].v.match(/\.0+$/))&&(b=Math.max(b,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[l].t;break;case"m":"s"===h&&(o[l].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[l].v.match(/[Hh]/)&&(v=1),v<2&&o[l].v.match(/[Mm]/)&&(v=2),v<3&&o[l].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var w,T="";for(l=0;l<o.length;++l)switch(o[l].t){case"t":case"T":case" ":case"D":break;case"X":o[l].v="",o[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[l].v=K(o[l].t.charCodeAt(0),o[l].v,n,b),o[l].t="t";break;case"n":case"?":for(w=l+1;null!=o[w]&&("?"===(f=o[w].t)||"D"===f||(" "===f||"t"===f)&&null!=o[w+1]&&("?"===o[w+1].t||"t"===o[w+1].t&&"/"===o[w+1].v)||"("===o[l].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===o[w].v||" "===o[w].v&&null!=o[w+1]&&"?"==o[w+1].t));)o[l].v+=o[w].v,o[w]={v:"",t:";"},++w;T+=o[l].v,l=w-1;break;case"G":o[l].t="t",o[l].v=Y(t,r)}var E,S,y="";if(T.length>0){40==T.charCodeAt(0)?(E=t<0&&45===T.charCodeAt(0)?-t:t,S=le("n",T,E)):(S=le("n",T,E=t<0&&a>1?-t:t),E<0&&o[0]&&"t"==o[0].t&&(S=S.substr(1),o[0].v="-"+o[0].v)),w=S.length-1;var k=o.length;for(l=0;l<o.length;++l)if(null!=o[l]&&"t"!=o[l].t&&o[l].v.indexOf(".")>-1){k=l;break}var _=o.length;if(k===o.length&&-1===S.indexOf("E")){for(l=o.length-1;l>=0;--l)null!=o[l]&&-1!=="n?".indexOf(o[l].t)&&(w>=o[l].v.length-1?(w-=o[l].v.length,o[l].v=S.substr(w+1,o[l].v.length)):w<0?o[l].v="":(o[l].v=S.substr(0,w+1),w=-1),o[l].t="t",_=l);w>=0&&_<o.length&&(o[_].v=S.substr(0,w+1)+o[_].v)}else if(k!==o.length&&-1===S.indexOf("E")){for(w=S.indexOf(".")-1,l=k;l>=0;--l)if(null!=o[l]&&-1!=="n?".indexOf(o[l].t)){for(s=o[l].v.indexOf(".")>-1&&l===k?o[l].v.indexOf(".")-1:o[l].v.length-1,y=o[l].v.substr(s+1);s>=0;--s)w>=0&&("0"===o[l].v.charAt(s)||"#"===o[l].v.charAt(s))&&(y=S.charAt(w--)+y);o[l].v=y,o[l].t="t",_=l}for(w>=0&&_<o.length&&(o[_].v=S.substr(0,w+1)+o[_].v),w=S.indexOf(".")+1,l=k;l<o.length;++l)if(null!=o[l]&&(-1!=="n?(".indexOf(o[l].t)||l===k)){for(s=o[l].v.indexOf(".")>-1&&l===k?o[l].v.indexOf(".")+1:0,y=o[l].v.substr(0,s);s<o[l].v.length;++s)w<S.length&&(y+=S.charAt(w++));o[l].v=y,o[l].t="t",_=l}}}for(l=0;l<o.length;++l)null!=o[l]&&"n?".indexOf(o[l].t)>-1&&(E=a>1&&t<0&&l>0&&"-"===o[l-1].v?-t:t,o[l].v=le(o[l].t,o[l].v,E),o[l].t="t");var A="";for(l=0;l!==o.length;++l)null!=o[l]&&(A+=o[l].v);return A}(n[1],t,r,n[0])}function ge(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(null!=L[r]){if(L[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return L[t]=e,t}function ve(e){for(var t=0;392!=t;++t)void 0!==e[t]&&ge(e[t],t)}function be(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',L=e}var we={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},Te=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;var Ee=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=new Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}();var r=function(e){var t=0,r=0,a=0,n="undefined"!=typeof Int32Array?new Int32Array(4096):new Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(r=e[a],t=256+a;t<4096;t+=256)r=n[t]=r>>>8^e[255&r];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!=typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(t),a=r[0],n=r[1],s=r[2],i=r[3],o=r[4],c=r[5],l=r[6],f=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var a=-1^r,n=0,s=e.length;n<s;)a=a>>>8^t[255&(a^e.charCodeAt(n++))];return~a},e.buf=function(e,r){for(var b=-1^r,w=e.length-15,T=0;T<w;)b=v[e[T++]^255&b]^g[e[T++]^b>>8&255]^m[e[T++]^b>>16&255]^p[e[T++]^b>>>24]^d[e[T++]]^u[e[T++]]^h[e[T++]]^f[e[T++]]^l[e[T++]]^c[e[T++]]^o[e[T++]]^i[e[T++]]^s[e[T++]]^n[e[T++]]^a[e[T++]]^t[e[T++]];for(w+=15;T<w;)b=b>>>8^t[255&(b^e[T++])];return~b},e.str=function(e,r){for(var a=-1^r,n=0,s=e.length,i=0,o=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^t[255&(a^i)]:i<2048?a=(a=a>>>8^t[255&(a^(192|i>>6&31))])>>>8^t[255&(a^(128|63&i))]:i>=55296&&i<57344?(i=64+(1023&i),o=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^t[255&(a^(240|i>>8&7))])>>>8^t[255&(a^(128|i>>2&63))])>>>8^t[255&(a^(128|o>>6&15|(3&i)<<4))])>>>8^t[255&(a^(128|63&o))]):a=(a=(a=a>>>8^t[255&(a^(224|i>>12&15))])>>>8^t[255&(a^(128|i>>6&63))])>>>8^t[255&(a^(128|63&i))];return~a},e}(),Se=function(){var e,t={};function r(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:r(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function a(e){if("/"==e.charAt(e.length-1))return a(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function n(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var a=t.getFullYear()-1980;a=(a=a<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,a)}function s(e){zr(e,0);for(var t={},r=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};if(21589===a)1&(r=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&r&&(i.atime=e.read_shift(4)),4&r&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime));e.l=s,t[a]=i}return t}function i(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return pe(e,t);if(109==(32|e[0])&&105==(32|e[1]))return function(e,t){if("mime-version:"!=N(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var r=t&&t.root||"",a=(g&&Buffer.isBuffer(e)?e.toString("binary"):N(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if(s=a[n],/^Content-Location:/i.test(s)&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw new Error("MAD cannot find boundary");var o="--"+(i[1]||""),c=[],l=[],f={FileIndex:c,FullPaths:l};u(f);var h,d=0;for(n=0;n<a.length;++n){var p=a[n];p!==o&&p!==o+"--"||(d++&&Te(f,a.slice(h,n),r),h=n)}return f}(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r,a,n,s,i,o,d=512,p=[],m=e.slice(0,512);zr(m,0);var v=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(C,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=v[0]){case 3:d=512;break;case 4:d=4096;break;case 0:if(0==v[1])return pe(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+r)}512!==d&&zr(m=e.slice(0,d),28);var b=e.slice(0,d);!function(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(m,r);var w=m.read_shift(4,"i");if(3===r&&0!==w)throw new Error("# Directory Sectors: Expected 0 saw "+w);m.l+=4,s=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),i=m.read_shift(4,"i"),a=m.read_shift(4,"i"),o=m.read_shift(4,"i"),n=m.read_shift(4,"i");for(var T=-1,E=0;E<109&&!((T=m.read_shift(4,"i"))<0);++E)p[E]=T;var S=function(e,t){for(var r=Math.ceil(e.length/t)-1,a=[],n=1;n<r;++n)a[n-1]=e.slice(n*t,(n+1)*t);return a[r-1]=e.slice(r*t),a}(e,d);l(o,n,S,d,p);var y=function(e,t,r,a){var n=e.length,s=[],i=[],o=[],c=[],l=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(o=[],(u=f+t)>=n&&(u-=n),!i[u]){c=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,o[o.length]=h,c.push(e[h]);var m=r[Math.floor(4*h/a)];if(a<4+(d=4*h&l))throw new Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m])break;if(p[h=Pr(e[m],d)])break}s[u]={nodes:o,data:hr([c])}}return s}(S,s,p,d);y[s].name="!Directory",a>0&&i!==x&&(y[i].name="!MiniFAT"),y[p[0]].name="!FAT",y.fat_addrs=p,y.ssz=d;var k=[],_=[],A=[];!function(e,t,r,a,n,s,i,o){for(var l,u=0,d=a.length?2:0,p=t[e].data,m=0,g=0;m<p.length;m+=128){var v=p.slice(m,m+128);zr(v,64),g=v.read_shift(2),l=dr(v,0,g-d),a.push(l);var b={name:l,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(b.ct=h(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(b.mt=h(v,v.l-8)),b.start=v.read_shift(4,"i"),b.size=v.read_shift(4,"i"),b.size<0&&b.start<0&&(b.size=b.type=0,b.start=x,b.name=""),5===b.type?(u=b.start,n>0&&u!==x&&(t[u].name="!StreamData")):b.size>=4096?(b.storage="fat",void 0===t[b.start]&&(t[b.start]=f(r,b.start,t.fat_addrs,t.ssz)),t[b.start].name=b.name,b.content=t[b.start].data.slice(0,b.size)):(b.storage="minifat",b.size<0?b.size=0:u!==x&&b.start!==x&&t[u]&&(b.content=c(b,t[u].data,(t[o]||{}).data))),b.content&&zr(b.content,0),s[l]=b,i.push(b)}}(s,y,S,k,a,{},_,i),function(e,t,r){for(var a=0,n=0,s=0,i=0,o=0,c=r.length,l=[],f=[];a<c;++a)l[a]=f[a]=a,t[a]=r[a];for(;o<f.length;++o)n=e[a=f[o]].L,s=e[a].R,i=e[a].C,l[a]===a&&(-1!==n&&l[n]!==n&&(l[a]=l[n]),-1!==s&&l[s]!==s&&(l[a]=l[s])),-1!==i&&(l[i]=a),-1!==n&&a!=l[a]&&(l[n]=l[a],f.lastIndexOf(n)<o&&f.push(n)),-1!==s&&a!=l[a]&&(l[s]=l[a],f.lastIndexOf(s)<o&&f.push(s));for(a=1;a<c;++a)l[a]===a&&(-1!==s&&l[s]!==s?l[a]=l[s]:-1!==n&&l[n]!==n&&(l[a]=l[n]));for(a=1;a<c;++a)if(0!==e[a].type){if((o=a)!=l[o])do{o=l[o],t[a]=t[o]+"/"+t[a]}while(0!==o&&-1!==l[o]&&o!=l[o]);l[a]=-1}for(t[0]+="/",a=1;a<c;++a)2!==e[a].type&&(t[a]+="/")}(_,A,k),k.shift();var O={FileIndex:_,FullPaths:A};return t&&t.raw&&(O.raw={header:b,sectors:S}),O}function c(e,t,r){for(var a=e.start,n=e.size,s=[],i=a;r&&n>0&&i>=0;)s.push(t.slice(i*y,i*y+y)),n-=y,i=Pr(r,4*i);return 0===s.length?jr(0):k(s).slice(0,e.size)}function l(e,t,r,a,n){var s=x;if(e===x){if(0!==t)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var i=r[e],o=(a>>>2)-1;if(!i)return;for(var c=0;c<o&&(s=Pr(i,4*c))!==x;++c)n.push(s);l(Pr(i,a-4),t-1,r,a,n)}}function f(e,t,r,a,n){var s=[],i=[];n||(n=[]);var o=a-1,c=0,l=0;for(c=t;c>=0;){n[c]=!0,s[s.length]=c,i.push(e[c]);var f=r[Math.floor(4*c/a)];if(a<4+(l=4*c&o))throw new Error("FAT boundary crossed: "+c+" 4 "+a);if(!e[f])break;c=Pr(e[f],l)}return{nodes:s,data:hr([i])}}function h(e,t){return new Date(1e3*(Fr(e,t+4)/1e7*Math.pow(2,32)+Fr(e,t)/1e7-11644473600))}function u(e,t){var r=t||{},a=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="Sh33tJ5";if(Se.find(e,"/"+t))return;var r=jr(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),d(e)}(e)}function d(e,t){u(e);for(var n=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var o=e.FileIndex[i];switch(o.type){case 0:s?n=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(o.R*o.L*o.C)&&(n=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(n=!0);break;default:n=!0}}if(n||t){var c=new Date(1987,1,19),l=0,f=Object.create?Object.create(null):{},h=[];for(i=0;i<e.FullPaths.length;++i)f[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&h.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<h.length;++i){var d=r(h[i][0]);(s=f[d])||(h.push([d,{name:a(d).replace("/",""),type:1,clsid:R,ct:c,mt:c,content:null}]),f[d]=!0)}for(h.sort((function(e,t){return function(e,t){for(var r=e.split("/"),a=t.split("/"),n=0,s=0,i=Math.min(r.length,a.length);n<i;++n){if(s=r[n].length-a[n].length)return s;if(r[n]!=a[n])return r[n]<a[n]?-1:1}return r.length-a.length}(e[0],t[0])})),e.FullPaths=[],e.FileIndex=[],i=0;i<h.length;++i)e.FullPaths[i]=h[i][0],e.FileIndex[i]=h[i][1];for(i=0;i<h.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=a(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||R,0===i)p.C=h.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(l=i+1;l<h.length&&r(e.FullPaths[l])!=m;++l);for(p.C=l>=h.length?-1:l,l=i+1;l<h.length&&r(e.FullPaths[l])!=r(m);++l);p.R=l>=h.length?-1:l,p.type=1}else r(e.FullPaths[i+1]||"")==r(m)&&(p.R=i+1),p.type=2}}}function E(e,t){var r=t||{};if("mad"==r.fileType)return function(e,t){for(var r=t||{},a=r.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,o=e.FileIndex[0],c=1;c<e.FullPaths.length;++c)if(i=e.FullPaths[c].slice(s.length),(o=e.FileIndex[c]).size&&o.content&&"Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var l=o.content,f=g&&Buffer.isBuffer(l)?l.toString("binary"):N(l),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;n.push(a),n.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),n.push("Content-Type: "+ve(o,i)),n.push(""),n.push(m?we(f):be(f))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,r);if(d(e),"zip"===r.fileType)return function(e,t){var r=t||{},a=[],s=[],i=jr(1),o=r.compression?8:0,c=0,l=0,f=0,h=0,u=0,d=e.FullPaths[0],p=d,m=e.FileIndex[0],g=[],v=0;for(l=1;l<e.FullPaths.length;++l)if(p=e.FullPaths[l].slice(d.length),(m=e.FileIndex[l]).size&&m.content&&"Sh33tJ5"!=p){var b=h,w=jr(p.length);for(f=0;f<p.length;++f)w.write_shift(1,127&p.charCodeAt(f));w=w.slice(0,w.l),g[u]=Ee.buf(m.content,0);var T=m.content;8==o&&(T=D(T)),(i=jr(30)).write_shift(4,67324752),i.write_shift(2,20),i.write_shift(2,c),i.write_shift(2,o),m.mt?n(i,m.mt):i.write_shift(4,0),i.write_shift(-4,g[u]),i.write_shift(4,T.length),i.write_shift(4,m.content.length),i.write_shift(2,w.length),i.write_shift(2,0),h+=i.length,a.push(i),h+=w.length,a.push(w),h+=T.length,a.push(T),(i=jr(46)).write_shift(4,33639248),i.write_shift(2,0),i.write_shift(2,20),i.write_shift(2,c),i.write_shift(2,o),i.write_shift(4,0),i.write_shift(-4,g[u]),i.write_shift(4,T.length),i.write_shift(4,m.content.length),i.write_shift(2,w.length),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(4,0),i.write_shift(4,b),v+=i.l,s.push(i),v+=w.length,s.push(w),++u}return i=jr(22),i.write_shift(4,101010256),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,u),i.write_shift(2,u),i.write_shift(4,v),i.write_shift(4,h),i.write_shift(2,0),k([k(a),k(s),i])}(e,r);var a=function(e){for(var t=0,r=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,o=t+127>>7,c=(t+7>>3)+r+i+o,l=c+127>>7,f=l<=109?0:Math.ceil((l-109)/127);c+l+f+127>>7>l;)f=++l<=109?0:Math.ceil((l-109)/127);var h=[1,f,l,o,i,r,t,0];return e.FileIndex[0].size=t<<6,h[7]=(e.FileIndex[0].start=h[0]+h[1]+h[2]+h[3]+h[4]+h[5])+(h[6]+7>>3),h}(e),s=jr(a[7]<<9),i=0,o=0;for(i=0;i<8;++i)s.write_shift(1,O[i]);for(i=0;i<8;++i)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),i=0;i<3;++i)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,a[2]),s.write_shift(4,a[0]+a[1]+a[2]+a[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,a[3]?a[0]+a[1]+a[2]-1:x),s.write_shift(4,a[3]),s.write_shift(-4,a[1]?a[0]-1:x),s.write_shift(4,a[1]),i=0;i<109;++i)s.write_shift(-4,i<a[2]?a[1]+i:-1);if(a[1])for(o=0;o<a[1];++o){for(;i<236+127*o;++i)s.write_shift(-4,i<a[2]?a[1]+i:-1);s.write_shift(-4,o===a[1]-1?x:o+1)}var c=function(e){for(o+=e;i<o-1;++i)s.write_shift(-4,i+1);e&&(++i,s.write_shift(-4,x))};for(o=i=0,o+=a[1];i<o;++i)s.write_shift(-4,I.DIFSECT);for(o+=a[2];i<o;++i)s.write_shift(-4,I.FATSECT);c(a[3]),c(a[4]);for(var l=0,f=0,h=e.FileIndex[0];l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&((f=h.content.length)<4096||(h.start=o,c(f+511>>9)));for(c(a[6]+7>>3);511&s.l;)s.write_shift(-4,I.ENDOFCHAIN);for(o=i=0,l=0;l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&(!(f=h.content.length)||f>=4096||(h.start=o,c(f+63>>6)));for(;511&s.l;)s.write_shift(-4,I.ENDOFCHAIN);for(i=0;i<a[4]<<2;++i){var u=e.FullPaths[i];if(u&&0!==u.length){h=e.FileIndex[i],0===i&&(h.start=h.size?h.start-1:x);var p=0===i&&r.root||h.name;if(f=2*(p.length+1),s.write_shift(64,p,"utf16le"),s.write_shift(2,f),s.write_shift(1,h.type),s.write_shift(1,h.color),s.write_shift(-4,h.L),s.write_shift(-4,h.R),s.write_shift(-4,h.C),h.clsid)s.write_shift(16,h.clsid,"hex");else for(l=0;l<4;++l)s.write_shift(4,0);s.write_shift(4,h.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,h.start),s.write_shift(4,h.size),s.write_shift(4,0)}else{for(l=0;l<17;++l)s.write_shift(4,0);for(l=0;l<3;++l)s.write_shift(4,-1);for(l=0;l<12;++l)s.write_shift(4,0)}}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>=4096)if(s.l=h.start+1<<9,g&&Buffer.isBuffer(h.content))h.content.copy(s,s.l,0,h.size),s.l+=h.size+511&-512;else{for(l=0;l<h.size;++l)s.write_shift(1,h.content[l]);for(;511&l;++l)s.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>0&&h.size<4096)if(g&&Buffer.isBuffer(h.content))h.content.copy(s,s.l,0,h.size),s.l+=h.size+63&-64;else{for(l=0;l<h.size;++l)s.write_shift(1,h.content[l]);for(;63&l;++l)s.write_shift(1,0)}if(g)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}t.version="1.2.1";var S,y=64,x=-2,C="d0cf11e0a1b11ae1",O=[208,207,17,224,161,177,26,225],R="00000000000000000000000000000000",I={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:x,FREESECT:-1,HEADER_SIGNATURE:C,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:R,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function N(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function D(e){return S?S.deflateRawSync(e):se(e)}var F=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],P=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],L=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];for(var M,U,B="undefined"!=typeof Uint8Array,W=B?new Uint8Array(256):[],H=0;H<256;++H)W[H]=(U=void 0,255&((U=139536&((M=H)<<1|M<<11)|558144&(M<<5|M<<15))>>16|U>>8|U));function V(e,t){var r=W[255&e];return t<=8?r>>>8-t:(r=r<<8|W[e>>8&255],t<=16?r>>>16-t:(r=r<<8|W[e>>16&255])>>>24-t)}function z(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=6?0:e[a+1]<<8))>>>r&3}function G(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=5?0:e[a+1]<<8))>>>r&7}function j(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=3?0:e[a+1]<<8))>>>r&31}function X(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=1?0:e[a+1]<<8))>>>r&127}function $(e,t,r){var a=7&t,n=t>>>3,s=(1<<r)-1,i=e[n]>>>a;return r<8-a?i&s:(i|=e[n+1]<<8-a,r<16-a?i&s:(i|=e[n+2]<<16-a,r<24-a?i&s:(i|=e[n+3]<<24-a)&s))}function Y(e,t,r){var a=7&t,n=t>>>3;return a<=5?e[n]|=(7&r)<<a:(e[n]|=r<<a&255,e[n+1]=(7&r)>>8-a),t+3}function K(e,t,r){return r=(1&r)<<(7&t),e[t>>>3]|=r,t+1}function J(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function q(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function Z(e,t){var r=e.length,a=2*r>t?2*r:t+5,n=0;if(r>=t)return e;if(g){var s=w(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(B){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<r;++n)i[n]=e[n];return i}return e.length=a,e}function Q(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}function ee(e,t,r){var a=1,n=0,s=0,i=0,o=0,c=e.length,l=B?new Uint16Array(32):Q(32);for(s=0;s<32;++s)l[s]=0;for(s=c;s<r;++s)e[s]=0;c=e.length;var f=B?new Uint16Array(c):Q(c);for(s=0;s<c;++s)l[n=e[s]]++,a<n&&(a=n),f[s]=0;for(l[0]=0,s=1;s<=a;++s)l[s+16]=o=o+l[s-1]<<1;for(s=0;s<c;++s)0!=(o=e[s])&&(f[s]=l[o+16]++);var h=0;for(s=0;s<c;++s)if(0!=(h=e[s]))for(o=V(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)t[o|i<<h]=15&h|s<<4;return a}var te=B?new Uint16Array(512):Q(512),re=B?new Uint16Array(32):Q(32);if(!B){for(var ae=0;ae<512;++ae)te[ae]=0;for(ae=0;ae<32;++ae)re[ae]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);ee(e,re,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);ee(r,te,288)}();var ne=function(){for(var e=B?new Uint8Array(32768):[],t=0,r=0;t<L.length-1;++t)for(;r<L[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var a=B?new Uint8Array(259):[];for(t=0,r=0;t<P.length-1;++t)for(;r<P[t+1];++r)a[r]=t;return function(t,r){return t.length<8?function(e,t){for(var r=0;r<e.length;){var a=Math.min(65535,e.length-r),n=r+a==e.length;for(t.write_shift(1,+n),t.write_shift(2,a),t.write_shift(2,65535&~a);a-- >0;)t[t.l++]=e[r++]}return t.l}(t,r):function(t,r){for(var n=0,s=0,i=B?new Uint16Array(32768):[];s<t.length;){var o=Math.min(65535,t.length-s);if(o<10){for(7&(n=Y(r,n,+!(s+o!=t.length)))&&(n+=8-(7&n)),r.l=n/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[s++];n=8*r.l}else{n=Y(r,n,+!(s+o!=t.length)+2);for(var c=0;o-- >0;){var l=t[s],f=-1,h=0;if((f=i[c=32767&(c<<5^l)])&&((f|=-32768&s)>s&&(f-=32768),f<s))for(;t[f+h]==t[s+h]&&h<250;)++h;if(h>2){(l=a[h])<=22?n=J(r,n,W[l+1]>>1)-1:(J(r,n,3),J(r,n+=5,W[l-23]>>5),n+=3);var u=l<8?0:l-4>>2;u>0&&(q(r,n,h-P[l]),n+=u),l=e[s-f],n=J(r,n,W[l]>>3),n-=3;var d=l<4?0:l-2>>1;d>0&&(q(r,n,s-f-L[l]),n+=d);for(var p=0;p<h;++p)i[c]=32767&s,c=32767&(c<<5^t[s]),++s;o-=h-1}else l<=143?l+=48:n=K(r,n,1),n=J(r,n,W[l]),i[c]=32767&s,++s}n=J(r,n,0)-1}}return r.l=(n+7)/8|0,r.l}(t,r)}}();function se(e){var t=jr(50+Math.floor(1.1*e.length)),r=ne(e,t);return t.slice(0,r)}var ie=B?new Uint16Array(32768):Q(32768),oe=B?new Uint16Array(32768):Q(32768),ce=B?new Uint16Array(128):Q(128),le=1,fe=1;function he(e,t){var r=j(e,t)+257,a=j(e,t+=5)+1,n=function(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=4?0:e[a+1]<<8))>>>r&15}(e,t+=5)+4;t+=4;for(var s=0,i=B?new Uint8Array(19):Q(19),o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],c=1,l=B?new Uint8Array(8):Q(8),f=B?new Uint8Array(8):Q(8),h=i.length,u=0;u<n;++u)i[F[u]]=s=G(e,t),c<s&&(c=s),l[s]++,t+=3;var d=0;for(l[0]=0,u=1;u<=c;++u)f[u]=d=d+l[u-1]<<1;for(u=0;u<h;++u)0!=(d=i[u])&&(o[u]=f[d]++);var p=0;for(u=0;u<h;++u)if(0!=(p=i[u])){d=W[o[u]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)ce[d|m<<p]=7&p|u<<3}var g=[];for(c=1;g.length<r+a;)switch(t+=7&(d=ce[X(e,t)]),d>>>=3){case 16:for(s=3+z(e,t),t+=2,d=g[g.length-1];s-- >0;)g.push(d);break;case 17:for(s=3+G(e,t),t+=3;s-- >0;)g.push(0);break;case 18:for(s=11+X(e,t),t+=7;s-- >0;)g.push(0);break;default:g.push(d),c<d&&(c=d)}var v=g.slice(0,r),b=g.slice(r);for(u=r;u<286;++u)v[u]=0;for(u=a;u<30;++u)b[u]=0;return le=ee(v,ie,286),fe=ee(b,oe,30),t}function ue(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[b(t),2];for(var r=0,a=0,n=w(t||1<<18),s=0,i=n.length>>>0,o=0,c=0;!(1&a);)if(a=G(e,r),r+=3,a>>>1!=0)for(a>>1==1?(o=9,c=5):(r=he(e,r),o=le,c=fe);;){!t&&i<s+32767&&(i=(n=Z(n,s+32767)).length);var l=$(e,r,o),f=a>>>1==1?te[l]:ie[l];if(r+=15&f,(f>>>=4)>>>8&255){if(256==f)break;var h=(f-=257)<8?0:f-4>>2;h>5&&(h=0);var u=s+P[f];h>0&&(u+=$(e,r,h),r+=h),l=$(e,r,c),r+=15&(f=a>>>1==1?re[l]:oe[l]);var d=(f>>>=4)<4?0:f-2>>1,p=L[f];for(d>0&&(p+=$(e,r,d),r+=d),!t&&i<u&&(i=(n=Z(n,u+100)).length);s<u;)n[s]=n[s-p],++s}else n[s++]=f}else{7&r&&(r+=8-(7&r));var m=e[r>>>3]|e[(r>>>3)+1]<<8;if(r+=32,m>0)for(!t&&i<s+m&&(i=(n=Z(n,s+m)).length);m-- >0;)n[s++]=e[r>>>3],r+=8}return t?[n,r+7>>>3]:[n.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function de(e,t){if(!e)throw new Error(t);"undefined"!=typeof console&&console.error(t)}function pe(e,t){var r=e;zr(r,0);var a={FileIndex:[],FullPaths:[]};u(a,{root:t.root});for(var n=r.length-4;(80!=r[n]||75!=r[n+1]||5!=r[n+2]||6!=r[n+3])&&n>=0;)--n;r.l=n+4,r.l+=4;var i=r.read_shift(2);r.l+=6;var o=r.read_shift(4);for(r.l=o,n=0;n<i;++n){r.l+=20;var c=r.read_shift(4),l=r.read_shift(4),f=r.read_shift(2),h=r.read_shift(2),d=r.read_shift(2);r.l+=8;var p=r.read_shift(4),m=s(r.slice(r.l+f,r.l+f+h));r.l+=f+h+d;var g=r.l;r.l=p+4,me(r,c,l,a,m),r.l=g}return a}function me(e,t,r,a,n){e.l+=2;var i=e.read_shift(2),o=e.read_shift(2),c=function(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),a=new Date,n=31&r,s=15&(r>>>=5);r>>>=4,a.setMilliseconds(0),a.setFullYear(r+1980),a.setMonth(s-1),a.setDate(n);var i=31&t,o=63&(t>>>=5);return t>>>=6,a.setHours(t),a.setMinutes(o),a.setSeconds(i<<1),a}(e);if(8257&i)throw new Error("Unsupported ZIP encryption");e.read_shift(4);for(var l=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d="",p=0;p<h;++p)d+=String.fromCharCode(e[e.l++]);if(u){var m=s(e.slice(e.l,e.l+u));(m[21589]||{}).mt&&(c=m[21589].mt),((n||{})[21589]||{}).mt&&(c=n[21589].mt)}e.l+=u;var g=e.slice(e.l,e.l+l);switch(o){case 8:g=function(e,t){if(!S)return ue(e,t);var r=new(0,S.InflateRaw),a=r._processChunk(e.slice(e.l),r._finishFlushFlag);return e.l+=r.bytesRead,a}(e,f);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+o)}var v=!1;8&i&&(134695760==e.read_shift(4)&&(e.read_shift(4),v=!0),l=e.read_shift(4),f=e.read_shift(4)),l!=t&&de(v,"Bad compressed size: "+t+" != "+l),f!=r&&de(v,"Bad uncompressed size: "+r+" != "+f),ye(a,d,g,{unsafe:!0,mt:c})}var ge={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function ve(e,t){if(e.ctype)return e.ctype;var r=e.name||"",a=r.match(/\.([^\.]+)$/);return a&&ge[a[1]]||t&&(a=(r=t).match(/[\.\\]([^\.\\])+$/))&&ge[a[1]]?ge[a[1]]:"application/octet-stream"}function be(e){for(var t=p(e),r=[],a=0;a<t.length;a+=76)r.push(t.slice(a,a+76));return r.join("\r\n")+"\r\n"}function we(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)}));"\n"==(t=t.replace(/ $/gm,"=20").replace(/\t$/gm,"=09")).charAt(0)&&(t="=0D"+t.slice(1));for(var r=[],a=(t=t.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A")).split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0!=s.length)for(var i=0;i<s.length;){var o=76,c=s.slice(i,i+o);"="==c.charAt(o-1)?o--:"="==c.charAt(o-2)?o-=2:"="==c.charAt(o-3)&&(o-=3),c=s.slice(i,i+o),(i+=o)<s.length&&(c+="="),r.push(c)}else r.push("")}return r.join("\r\n")}function Te(e,t,r){for(var a,n="",s="",i="",o=0;o<10;++o){var c=t[o];if(!c||c.match(/^\s*$/))break;var l=c.match(/^(.*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":n=l[2].trim();break;case"content-type":i=l[2].trim();break;case"content-transfer-encoding":s=l[2].trim()}}switch(++o,s.toLowerCase()){case"base64":a=T(m(t.slice(o).join("")));break;case"quoted-printable":a=function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r];r<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++r];t.push(a)}for(var n=0;n<t.length;++n)t[n]=t[n].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return T(t.join("\r\n"))}(t.slice(o));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+s)}var f=ye(e,n.slice(r.length),a,{unsafe:!0});i&&(f.ctype=i)}function ye(e,t,r,n){var s=n&&n.unsafe;s||u(e);var i=!s&&Se.find(e,t);if(!i){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),i={name:a(t),type:2},e.FileIndex.push(i),e.FullPaths.push(o),s||Se.utils.cfb_gc(e)}return i.content=r,i.size=r?r.length:0,n&&(n.CLSID&&(i.clsid=n.CLSID),n.mt&&(i.mt=n.mt),n.ct&&(i.ct=n.ct)),i}return t.find=function(e,t){var r=e.FullPaths.map((function(e){return e.toUpperCase()})),a=r.map((function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]})),n=!1;47===t.charCodeAt(0)?(n=!0,t=r[0].slice(0,-1)+t):n=-1!==t.indexOf("/");var s=t.toUpperCase(),i=!0===n?r.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var o=!s.match(A);for(s=s.replace(_,""),o&&(s=s.replace(A,"!")),i=0;i<r.length;++i){if((o?r[i].replace(A,"!"):r[i]).replace(_,"")==s)return e.FileIndex[i];if((o?a[i].replace(A,"!"):a[i]).replace(_,"")==s)return e.FileIndex[i]}return null},t.read=function(t,r){var a=r&&r.type;switch(a||g&&Buffer.isBuffer(t)&&(a="buffer"),a||"base64"){case"file":return function(t,r){return i(),o(e.readFileSync(t),r)}(t,r);case"base64":return o(T(m(t)),r);case"binary":return o(T(t),r)}return o(t,r)},t.parse=o,t.write=function(t,r){var a=E(t,r);switch(r&&r.type||"buffer"){case"file":return i(),e.writeFileSync(r.filename,a),a;case"binary":return"string"==typeof a?a:N(a);case"base64":return p("string"==typeof a?a:N(a));case"buffer":if(g)return Buffer.isBuffer(a)?a:v(a);case"array":return"string"==typeof a?T(a):a}return a},t.writeFile=function(t,r,a){i();var n=E(t,a);e.writeFileSync(r,n)},t.utils={cfb_new:function(e){var t={};return u(t,e),t},cfb_add:ye,cfb_del:function(e,t){u(e);var r=Se.find(e,t);if(r)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==r)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0;return!1},cfb_mov:function(e,t,r){u(e);var n=Se.find(e,t);if(n)for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==n)return e.FileIndex[s].name=a(r),e.FullPaths[s]=r,!0;return!1},cfb_gc:function(e){d(e,!0)},ReadShift:Mr,CheckField:Vr,prep_blob:zr,bconcat:k,use_zlib:function(e){try{var t=new(0,e.InflateRaw);if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),!t.bytesRead)throw new Error("zlib does not expose bytesRead");S=e}catch(r){console.error("cannot use native zlib: "+(r.message||r))}},_deflateRaw:se,_inflateRaw:ue,consts:I},t}();function ye(e){return"string"==typeof e?E(e):Array.isArray(e)?function(e){if("undefined"==typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}(e):e}function ke(e,t,r){if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=E(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var a="utf8"==r?Ot(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(a,e);if("undefined"!=typeof Blob){var n=new Blob([ye(a)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if("undefined"!=typeof saveAs)return saveAs(n,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var s=URL.createObjectURL(n);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),chrome.downloads.download({url:s,filename:e,saveAs:!0});var i=document.createElement("a");if(null!=i.download)return i.download=e,i.href=s,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),s}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=S(t)),o.write(t),o.close(),t}catch(c){if(!c.message||!c.message.match(/onstruct/))throw c}throw new Error("cannot save file "+e)}function _e(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function Ae(e,t){for(var r=[],a=_e(e),n=0;n!==a.length;++n)null==r[e[a[n]][t]]&&(r[e[a[n]][t]]=a[n]);return r}function xe(e){for(var t=[],r=_e(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function Ce(e){for(var t=[],r=_e(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}var Oe=new Date(1899,11,30,0,0,0);function Re(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(Oe.getTime()+6e4*(e.getTimezoneOffset()-Oe.getTimezoneOffset())))/864e5}var Ie=new Date,Ne=Oe.getTime()+6e4*(Ie.getTimezoneOffset()-Oe.getTimezoneOffset()),De=Ie.getTimezoneOffset();function Fe(e){var t=new Date;return t.setTime(24*e*60*60*1e3+Ne),t.getTimezoneOffset()!==De&&t.setTime(t.getTime()+6e4*(t.getTimezoneOffset()-De)),t}function Pe(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(!a)throw new Error("Unsupported ISO Duration Field: M");r*=60}t+=r*parseInt(n[s],10)}return t}var Le=new Date("2017-02-19T19:06:09.000Z"),Me=isNaN(Le.getFullYear())?new Date("2/19/17"):Le,Ue=2017==Me.getFullYear();function Be(e,t){var r=new Date(e);if(Ue)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==Me.getFullYear()&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function We(e,t){if(g&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return Ot(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return Ot(c(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return Ot(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return Ot(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,(function(e){return r[e]||e}))}catch(s){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function He(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=He(e[r]));return t}function Ve(e,t){for(var r="";r.length<t;)r+=e;return r}function ze(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return r*=100,""}));return isNaN(t=Number(a))?(a=a.replace(/[(](.*)[)]/,(function(e,t){return r=-r,t})),isNaN(t=Number(a))?t:t/r):t/r}var Ge=["january","february","march","april","may","june","july","august","september","october","november","december"];function je(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==Ge.indexOf(i))return r}else if(i.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||s>1)&&101!=a?t:e.match(/[^-0-9:,\/\\]/)?r:t}var Xe=function(){var e=5=="abacaba".split(/(:?b)/i).length;return function(t,r,a){if(e||"string"==typeof r)return t.split(r);for(var n=t.split(r),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function $e(e){return e?e.content&&e.type?We(e.content,!0):e.data?f(e.data):e.asNodeBuffer&&g?f(e.asNodeBuffer().toString("binary")):e.asBinary?f(e.asBinary()):e._data&&e._data.getContent?f(We(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function Ye(e){if(!e)return null;if(e.data)return o(e.data);if(e.asNodeBuffer&&g)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return"string"==typeof t?o(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function Ke(e,t){for(var r=e.FullPaths||_e(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function Je(e,t){var r=Ke(e,t);if(null==r)throw new Error("Cannot find file "+t+" in zip");return r}function qe(e,t,r){if(!r)return(a=Je(e,t))&&".bin"===a.name.slice(-4)?Ye(a):$e(a);var a;if(!t)return null;try{return qe(e,t)}catch(n){return null}}function Ze(e,t,r){if(!r)return $e(Je(e,t));if(!t)return null;try{return Ze(e,t)}catch(a){return null}}function Qe(e,t,r){if(!r)return Ye(Je(e,t));if(!t)return null;try{return Qe(e,t)}catch(a){return null}}function et(e){for(var t=e.FullPaths||_e(e.files),r=[],a=0;a<t.length;++a)"/"!=t[a].slice(-1)&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function tt(e,t,r){if(e.FullPaths){var a;if("string"==typeof r)return a=g?v(r):function(e){for(var t=[],r=0,a=e.length+250,n=b(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|63&i;else if(i>=55296&&i<57344){i=64+(1023&i);var o=1023&e.charCodeAt(++s);n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|o>>6&15|(3&i)<<4,n[r++]=128|63&o}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|63&i;r>a&&(t.push(n.slice(0,r)),r=0,n=b(65535),a=65530)}return t.push(n.slice(0,r)),k(t)}(r),Se.utils.cfb_add(e,t,a);Se.utils.cfb_add(e,t,r)}else e.file(t,r)}function rt(){return Se.utils.cfb_new()}function at(e,t){switch(t.type){case"base64":return Se.read(e,{type:"base64"});case"binary":return Se.read(e,{type:"binary"});case"buffer":case"array":return Se.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function nt(e,t){if("/"==e.charAt(0))return e.slice(1);var r=t.split("/");"/"!=t.slice(-1)&&r.pop();for(var a=e.split("/");0!==a.length;){var n=a.shift();".."===n?r.pop():"."!==n&&r.push(n)}return r.join("/")}var st='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',it=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,ot=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/gm,ct=st.match(ot)?ot:/<[^>]*>/g,lt=/<\w*:/,ft=/<(\/?)\w+:/;function ht(e,t,r){for(var a={},n=0,s=0;n!==e.length&&(32!==(s=e.charCodeAt(n))&&10!==s&&13!==s);++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(it),o=0,c="",l=0,f="",h="",u=1;if(i)for(l=0;l!=i.length;++l){for(h=i[l],s=0;s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(u=34==(n=h.charCodeAt(s+1))||39==n?1:0,c=h.slice(s+1+u,h.length-u),o=0;o!=f.length&&58!==f.charCodeAt(o);++o);if(o===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=c,r||(a[f.toLowerCase()]=c);else{var d=(5===o&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(o+1);if(a[d]&&"ext"==f.slice(o-3,o))continue;a[d]=c,r||(a[d.toLowerCase()]=c)}}return a}function ut(e){return e.replace(ft,"<$1")}var dt={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},pt=xe(dt),mt=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/gi,t=/_x([\da-fA-F]{4})_/gi;return function r(a){var n=a+"",s=n.indexOf("<![CDATA[");if(-1==s)return n.replace(e,(function(e,t){return dt[e]||String.fromCharCode(parseInt(t,e.indexOf("x")>-1?16:10))||e})).replace(t,(function(e,t){return String.fromCharCode(parseInt(t,16))}));var i=n.indexOf("]]>");return r(n.slice(0,s))+n.slice(s+9,i)+r(n.slice(i+3))}}(),gt=/[&<>'"]/g,vt=/[\u0000-\u0008\u000b-\u001f]/g;function bt(e){return(e+"").replace(gt,(function(e){return pt[e]})).replace(vt,(function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"}))}function wt(e){return bt(e).replace(/ /g,"_x0020_")}var Tt=/[\u0000-\u001f]/g;function Et(e){return(e+"").replace(gt,(function(e){return pt[e]})).replace(/\n/g,"<br/>").replace(Tt,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))}var St=function(){var e=/&#(\d+);/g;function t(e,t){return String.fromCharCode(parseInt(t,10))}return function(r){return r.replace(e,t)}}();function yt(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function kt(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0;r<e.length;)(a=e.charCodeAt(r++))<128?t+=String.fromCharCode(a):(n=e.charCodeAt(r++),a>191&&a<224?(i=(31&a)<<6,i|=63&n,t+=String.fromCharCode(i)):(s=e.charCodeAt(r++),a<240?t+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s):(o=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&(i=e.charCodeAt(r++)))-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(1023&o)))));return t}function _t(e){var t,r,a,n=b(2*e.length),s=1,i=0,o=0;for(r=0;r<e.length;r+=s)s=1,(a=e.charCodeAt(r))<128?t=a:a<224?(t=64*(31&a)+(63&e.charCodeAt(r+1)),s=2):a<240?(t=4096*(15&a)+64*(63&e.charCodeAt(r+1))+(63&e.charCodeAt(r+2)),s=3):(s=4,t=262144*(7&a)+4096*(63&e.charCodeAt(r+1))+64*(63&e.charCodeAt(r+2))+(63&e.charCodeAt(r+3)),o=55296+((t-=65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(n[i++]=255&o,n[i++]=o>>>8,o=0),n[i++]=t%256,n[i++]=t>>>8;return n.slice(0,i).toString("ucs2")}function At(e){return v(e,"binary").toString("utf8")}var xt="foo bar bazâð£",Ct=g&&(At(xt)==kt(xt)&&At||_t(xt)==kt(xt)&&_t)||kt,Ot=g?function(e){return v(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,n=e.charCodeAt(r++)-56320+(a<<10),t.push(String.fromCharCode(240+(n>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)))}return t.join("")},Rt=function(){var e={};return function(t,r){var a=t+"|"+(r||"");return e[a]?e[a]:e[a]=new RegExp("<(?:\\w+:)?"+t+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+t+">",r||"")}}(),It=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)r=r.replace(e[a][0],e[a][1]);return r}}(),Nt=function(){var e={};return function(t){return void 0!==e[t]?e[t]:e[t]=new RegExp("<(?:vt:)?"+t+">([\\s\\S]*?)</(?:vt:)?"+t+">","g")}}(),Dt=/<\/?(?:vt:)?variant>/g,Ft=/<(?:vt:)([^>]*)>([\s\S]*)</;function Pt(e,t){var r=ht(e),a=e.match(Nt(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach((function(e){var t=e.replace(Dt,"").match(Ft);t&&n.push({v:Ct(t[2]),t:t[1]})})),n}var Lt=/(^\s|\s$|\n)/;function Mt(e,t){return"<"+e+(t.match(Lt)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Ut(e){return _e(e).map((function(t){return" "+t+'="'+e[t]+'"'})).join("")}function Bt(e,t,r){return"<"+e+(null!=r?Ut(r):"")+(null!=t?(t.match(Lt)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Wt(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function Ht(e){if(g&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return Ct(S(y(e)));throw new Error("Bad input format: expected Buffer or string")}var Vt=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/gm,zt="http://schemas.openxmlformats.org/package/2006/metadata/core-properties",Gt="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",jt="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",Xt="http://schemas.openxmlformats.org/package/2006/content-types",$t="http://schemas.openxmlformats.org/package/2006/relationships",Yt="http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",Kt="http://purl.org/dc/elements/1.1/",Jt="http://purl.org/dc/terms/",qt="http://purl.org/dc/dcmitype/",Zt="http://schemas.openxmlformats.org/officeDocument/2006/relationships",Qt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",er="http://www.w3.org/2001/XMLSchema-instance",tr="http://www.w3.org/2001/XMLSchema",rr=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],ar="urn:schemas-microsoft-com:office:office",nr="urn:schemas-microsoft-com:office:excel",sr="urn:schemas-microsoft-com:office:spreadsheet",ir="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",or="http://macVmlSchemaUri",cr="urn:schemas-microsoft-com:vml",lr="http://www.w3.org/TR/REC-html40";var fr=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var a=0,n=e[0][r].length;a<n;a+=10240)t.push.apply(t,e[0][r].slice(a,a+10240));return t},hr=g?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:v(e)}))):fr(e)}:fr,ur=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(Nr(e,n)));return a.join("").replace(_,"")},dr=g?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(_,""):ur(e,t,r)}:ur,pr=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},mr=g?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):pr(e,t,r)}:pr,gr=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(Ir(e,n)));return a.join("")},vr=g?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):gr(e,t,r)}:gr,br=function(e,t){var r=Fr(e,t);return r>0?vr(e,t+4,t+4+r-1):""},wr=br,Tr=function(e,t){var r=Fr(e,t);return r>0?vr(e,t+4,t+4+r-1):""},Er=Tr,Sr=function(e,t){var r=2*Fr(e,t);return r>0?vr(e,t+4,t+4+r-1):""},yr=Sr,kr=function(e,t){var r=Fr(e,t);return r>0?dr(e,t+4,t+4+r):""},_r=kr,Ar=function(e,t){var r=Fr(e,t);return r>0?vr(e,t+4,t+4+r):""},xr=Ar,Cr=function(e,t){return function(e,t){for(var r=1-2*(e[t+7]>>>7),a=((127&e[t+7])<<4)+(e[t+6]>>>4&15),n=15&e[t+6],s=5;s>=0;--s)n=256*n+e[t+s];return 2047==a?0==n?r*(1/0):NaN:(0==a?a=-1022:(a-=1023,n+=Math.pow(2,52)),r*Math.pow(2,a-52)*n)}(e,t)},Or=Cr,Rr=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};g&&(wr=function(e,t){if(!Buffer.isBuffer(e))return br(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Er=function(e,t){if(!Buffer.isBuffer(e))return Tr(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},yr=function(e,t){if(!Buffer.isBuffer(e))return Sr(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},_r=function(e,t){if(!Buffer.isBuffer(e))return kr(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},xr=function(e,t){if(!Buffer.isBuffer(e))return Ar(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},Or=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):Cr(e,t)},Rr=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array});var Ir=function(e,t){return e[t]},Nr=function(e,t){return 256*e[t+1]+e[t]},Dr=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-1*(65535-r+1)},Fr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Pr=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Lr=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Mr(e,t){var r,a,n,s,i,o,c="",l=[];switch(t){case"dbcs":if(o=this.l,g&&Buffer.isBuffer(this))c=this.slice(this.l,this.l+2*e).toString("utf16le");else for(i=0;i<e;++i)c+=String.fromCharCode(Nr(this,o)),o+=2;e*=2;break;case"utf8":c=vr(this,this.l,this.l+e);break;case"utf16le":e*=2,c=dr(this,this.l,this.l+e);break;case"wstr":return Mr.call(this,e,"dbcs");case"lpstr-ansi":c=wr(this,this.l),e=4+Fr(this,this.l);break;case"lpstr-cp":c=Er(this,this.l),e=4+Fr(this,this.l);break;case"lpwstr":c=yr(this,this.l),e=4+2*Fr(this,this.l);break;case"lpp4":e=4+Fr(this,this.l),c=_r(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+Fr(this,this.l),c=xr(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,c="";0!==(n=Ir(this,this.l+e++));)l.push(h(n));c=l.join("");break;case"_wstr":for(e=0,c="";0!==(n=Nr(this,this.l+e));)l.push(h(n)),e+=2;e+=2,c=l.join("");break;case"dbcs-cont":for(c="",o=this.l,i=0;i<e;++i){if(this.lens&&-1!==this.lens.indexOf(o))return n=Ir(this,o),this.l=o+1,s=Mr.call(this,e-i,n?"dbcs-cont":"sbcs-cont"),l.join("")+s;l.push(h(Nr(this,o))),o+=2}c=l.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(c="",o=this.l,i=0;i!=e;++i){if(this.lens&&-1!==this.lens.indexOf(o))return n=Ir(this,o),this.l=o+1,s=Mr.call(this,e-i,n?"dbcs-cont":"sbcs-cont"),l.join("")+s;l.push(h(Ir(this,o))),o+=1}c=l.join("");break;default:switch(e){case 1:return r=Ir(this,this.l),this.l++,r;case 2:return r=("i"===t?Dr:Nr)(this,this.l),this.l+=2,r;case 4:case-4:return"i"!==t&&128&this[this.l+3]?(a=Fr(this,this.l),this.l+=4,a):(r=(e>0?Pr:Lr)(this,this.l),this.l+=4,r);case 8:case-8:if("f"===t)return a=8==e?Or(this,this.l):Or([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:c=mr(this,this.l,e)}}return this.l+=e,c}var Ur=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Br=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Wr=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function Hr(e,t,r){var a=0,n=0;if("dbcs"===r){for(n=0;n!=t.length;++n)Wr(this,t.charCodeAt(n),this.l+2*n);a=2*t.length}else if("sbcs"===r){for(t=t.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=t.length;++n)this[this.l+n]=255&t.charCodeAt(n);a=t.length}else{if("hex"===r){for(;n<e;++n)this[this.l++]=parseInt(t.slice(2*n,2*n+2),16)||0;return this}if("utf16le"===r){var s=Math.min(this.l+e,this.length);for(n=0;n<Math.min(t.length,e);++n){var i=t.charCodeAt(n);this[this.l++]=255&i,this[this.l++]=i>>8}for(;this.l<s;)this[this.l++]=0;return this}switch(e){case 1:a=1,this[this.l]=255&t;break;case 2:a=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:a=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:a=4,Ur(this,t,this.l);break;case 8:if(a=8,"f"===r){!function(e,t,r){var a=(t<0||1/t==-1/0?1:0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?n=-1022:(s-=Math.pow(2,52),n+=1023)):(n=2047,s=isNaN(t)?26985:0);for(var o=0;o<=5;++o,s/=256)e[r+o]=255&s;e[r+6]=(15&n)<<4|15&s,e[r+7]=n>>4|a}(this,t,this.l);break}case 16:break;case-4:a=4,Br(this,t,this.l)}}return this.l+=a,this}function Vr(e,t){var r=mr(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function zr(e,t){e.l=t,e.read_shift=Mr,e.chk=Vr,e.write_shift=Hr}function Gr(e,t){e.l+=t}function jr(e){var t=b(e);return zr(t,0),t}function Xr(e,t,r){if(e){var a,n,s;zr(e,e.l||0);for(var i=e.length,o=0,c=0;e.l<i;){128&(o=e.read_shift(1))&&(o=(127&o)+((127&e.read_shift(1))<<7));var l=ql[o]||ql[65535];for(s=127&(a=e.read_shift(1)),n=1;n<4&&128&a;++n)s+=(127&(a=e.read_shift(1)))<<7*n;c=e.l+s;var f=l.f&&l.f(e,s,r);if(e.l=c,t(f,l,o))return}}}function $r(){var e=[],t=g?256:2048,r=function(e){var t=jr(e);return zr(t,0),t},a=r(t),n=function(){a&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=r(Math.max(e+1,t)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(t)},end:function(){return n(),k(e)},_bufs:e}}function Yr(e,t,r,a){var n,s=+t;if(!isNaN(s)){a||(a=ql[s].p||(r||[]).length||0),n=1+(s>=128?1:0)+1,a>=128&&++n,a>=16384&&++n,a>=2097152&&++n;var i=e.next(n);s<=127?i.write_shift(1,s):(i.write_shift(1,128+(127&s)),i.write_shift(1,s>>7));for(var o=0;4!=o;++o){if(!(a>=128)){i.write_shift(1,a);break}i.write_shift(1,128+(127&a)),a>>=7}a>0&&Rr(r)&&e.push(r)}}function Kr(e,t,r){var a=He(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function Jr(e,t,r){var a=He(e);return a.s=Kr(a.s,t.s,r),a.e=Kr(a.e,t.s,r),a}function qr(e,t){if(e.cRel&&e.c<0)for(e=He(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=He(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=na(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=function(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}(r)),r}function Zr(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?qr(e.s,t.biff)+":"+qr(e.e,t.biff):(e.s.rRel?"":"$")+ea(e.s.r)+":"+(e.e.rRel?"":"$")+ea(e.e.r):(e.s.cRel?"":"$")+ra(e.s.c)+":"+(e.e.cRel?"":"$")+ra(e.e.c)}function Qr(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function ea(e){return""+(e+1)}function ta(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function ra(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function aa(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function na(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function sa(e){var t=e.indexOf(":");return-1==t?{s:aa(e),e:aa(e)}:{s:aa(e.slice(0,t)),e:aa(e.slice(t+1))}}function ia(e,t){return void 0===t||"number"==typeof t?ia(e.s,e.e):("string"!=typeof e&&(e=na(e)),"string"!=typeof t&&(t=na(t)),e==t?e:e+":"+t)}function oa(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||10!=n)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;return t.e.r=--r,t}function ca(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=me(e.z,r?Re(t):t)}catch(a){}try{return e.w=me((e.XF||{}).numFmtId||(r?14:0),r?Re(t):t)}catch(a){return""+t}}function la(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t?Ya[e.v]||e.v:ca(e,null==t?e.v:t))}function fa(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function ha(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,s=e||(n?[]:{}),i=0,o=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var c="string"==typeof a.origin?aa(a.origin):a.origin;i=c.r,o=c.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=oa(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),-1==i&&(l.e.r=i=f.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if(void 0!==t[h][u]){var d={v:t[h][u]},p=i+h,m=o+u;if(l.s.r>p&&(l.s.r=p),l.s.c>m&&(l.s.c=m),l.e.r<p&&(l.e.r=p),l.e.c<m&&(l.e.c=m),!t[h][u]||"object"!=typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||L[14],a.cellDates?(d.t="d",d.w=me(d.z,Re(d.v))):(d.t="n",d.v=Re(d.v),d.w=me(d.z,d.v))):d.t="s";else d=t[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=na({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return l.s.c<1e7&&(s["!ref"]=ia(l)),s}function ua(e,t){return ha(null,e,t)}function da(e,t){return t||(t=jr(4)),t.write_shift(4,e),t}function pa(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function ma(e,t){var r=!1;return null==t&&(r=!0,t=jr(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function ga(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function va(e,t){var r=e.l,a=e.read_shift(1),n=pa(e),s=[],i={t:n,h:n};if(1&a){for(var o=e.read_shift(4),c=0;c!=o;++c)s.push(ga(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}var ba=va;function wa(e,t){var r=!1;return null==t&&(r=!0,t=jr(23+4*e.t.length)),t.write_shift(1,1),ma(e.t,t),t.write_shift(4,1),function(e,t){t||(t=jr(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0)}({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function Ta(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function Ea(e,t){return null==t&&(t=jr(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function Sa(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function ya(e,t){return null==t&&(t=jr(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var ka=pa,_a=ma;function Aa(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function xa(e,t){var r=!1;return null==t&&(r=!0,t=jr(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Ca=pa,Oa=Aa,Ra=xa;function Ia(e){var t=e.slice(e.l,e.l+4),r=1&t[0],a=2&t[0];e.l+=4;var n=0===a?Or([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):Pr(t,0)>>2;return r?n/100:n}function Na(e,t){null==t&&(t=jr(4));var r=0,a=0,n=100*e;if(e==(0|e)&&e>=-(1<<29)&&e<1<<29?a=1:n==(0|n)&&n>=-(1<<29)&&n<1<<29&&(a=1,r=1),!a)throw new Error("unsupported RkNumber "+e);t.write_shift(-4,((r?n:e)<<2)+(r+2))}function Da(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var Fa=Da,Pa=function(e,t){return t||(t=jr(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function La(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Ma(e,t){return(t||jr(8)).write_shift(8,e,"f")}function Ua(e,t){if(t||(t=jr(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var a=e.rgb||"FFFFFF";"number"==typeof a&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function Ba(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 4294967295:case 4294967294:return{2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"}[e.read_shift(4)]||""}if(r>400)throw new Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var Wa=80,Ha=[Wa,81],Va={1:{n:"CodePage",t:2},2:{n:"Category",t:Wa},3:{n:"PresentationFormat",t:Wa},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:Wa},15:{n:"Company",t:Wa},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:Wa},27:{n:"ContentStatus",t:Wa},28:{n:"Language",t:Wa},29:{n:"Version",t:Wa},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},za={1:{n:"CodePage",t:2},2:{n:"Title",t:Wa},3:{n:"Subject",t:Wa},4:{n:"Author",t:Wa},5:{n:"Keywords",t:Wa},6:{n:"Comments",t:Wa},7:{n:"Template",t:Wa},8:{n:"LastAuthor",t:Wa},9:{n:"RevNumber",t:Wa},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:Wa},19:{n:"DocSecurity",t:3},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},Ga={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},ja=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function Xa(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var $a=He(Xa([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),Ya={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Ka={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},Ja={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},qa={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function Za(e,t){var r,a=function(e){for(var t=[],r=_e(e),a=0;a!==r.length;++a)null==t[e[r[a]]]&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}(Ja),n=[];n[n.length]=st,n[n.length]=Bt("Types",null,{xmlns:Xt,"xmlns:xsd":tr,"xmlns:xsi":er}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map((function(e){return Bt("Default",null,{Extension:e[0],ContentType:e[1]})})));var s=function(a){e[a]&&e[a].length>0&&(r=e[a][0],n[n.length]=Bt("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:qa[a][t.bookType]||qa[a].xlsx}))},i=function(r){(e[r]||[]).forEach((function(e){n[n.length]=Bt("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:qa[r][t.bookType]||qa[r].xlsx})}))},o=function(t){(e[t]||[]).forEach((function(e){n[n.length]=Bt("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:a[t][0]})}))};return s("workbooks"),i("sheets"),i("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),i("metadata"),o("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var Qa={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function en(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function tn(e,t){var r={"!id":{}};if(!e)return r;"/"!==t.charAt(0)&&(t="/"+t);var a={};return(e.match(ct)||[]).forEach((function(e){var n=ht(e);if("<Relationship"===n[0]){var s={};s.Type=n.Type,s.Target=n.Target,s.Id=n.Id,n.TargetMode&&(s.TargetMode=n.TargetMode);var i="External"===n.TargetMode?n.Target:nt(n.Target,t);r[i]=s,a[n.Id]=s}})),r["!id"]=a,r}function rn(e){var t=[st,Bt("Relationships",null,{xmlns:$t})];return _e(e["!id"]).forEach((function(r){t[t.length]=Bt("Relationship",null,e["!id"][r])})),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function an(e,t,r,a,n,s){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,s?n.TargetMode=s:[Qa.HLINK,Qa.XPATH,Qa.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}function nn(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function sn(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+e.version+"</meta:generator></office:meta></office:document-meta>"}var on=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],cn=function(){for(var e=new Array(on.length),t=0;t<on.length;++t){var r=on[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function ln(e){var t={};e=Ct(e);for(var r=0;r<on.length;++r){var a=on[r],n=e.match(cn[r]);null!=n&&n.length>0&&(t[a[1]]=mt(n[1])),"date"===a[2]&&t[a[1]]&&(t[a[1]]=Be(t[a[1]]))}return t}function fn(e,t,r,a,n){null==n[e]&&null!=t&&""!==t&&(n[e]=t,t=bt(t),a[a.length]=r?Bt(e,t,r):Mt(e,t))}function hn(e,t){var r=t||{},a=[st,Bt("cp:coreProperties",null,{"xmlns:cp":zt,"xmlns:dc":Kt,"xmlns:dcterms":Jt,"xmlns:dcmitype":qt,"xmlns:xsi":er})],n={};if(!e&&!r.Props)return a.join("");e&&(null!=e.CreatedDate&&fn("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:Wt(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),null!=e.ModifiedDate&&fn("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:Wt(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var s=0;s!=on.length;++s){var i=on[s],o=r.Props&&null!=r.Props[i[1]]?r.Props[i[1]]:e?e[i[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&fn(i[0],o,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var un=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],dn=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function pn(e,t,r,a){var n=[];if("string"==typeof e)n=Pt(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map((function(e){return{v:e}})));var i="string"==typeof t?Pt(t,a).map((function(e){return e.v})):t,o=0,c=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(c=+n[l+1].v,n[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=c,r.SheetNames=i.slice(o,o+c);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=c,r.DefinedNames=i.slice(o,o+c);break;case"Charts":case"Diagramme":r.Chartsheets=c,r.ChartNames=i.slice(o,o+c)}o+=c}}function mn(e){var t=[],r=Bt;return e||(e={}),e.Application="SheetJS",t[t.length]=st,t[t.length]=Bt("Properties",null,{xmlns:jt,"xmlns:vt":Qt}),un.forEach((function(a){if(void 0!==e[a[1]]){var n;switch(a[2]){case"string":n=bt(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false"}void 0!==n&&(t[t.length]=r(a[0],n))}})),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map((function(e){return"<vt:lpstr>"+bt(e)+"</vt:lpstr>"})).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var gn=/<[^>]+>[^<]*/g;function vn(e){var t=[st,Bt("Properties",null,{xmlns:Gt,"xmlns:vt":Qt})];if(!e)return t.join("");var r=1;return _e(e).forEach((function(a){++r,t[t.length]=Bt("property",function(e,t){switch(typeof e){case"string":var r=Bt("vt:lpwstr",bt(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return Bt((0|e)==e?"vt:i4":"vt:r8",bt(String(e)));case"boolean":return Bt("vt:bool",e?"true":"false")}if(e instanceof Date)return Bt("vt:filetime",Wt(e));throw new Error("Unable to serialize "+e)}(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:bt(a)})})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var bn,wn={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function Tn(e,t,r){bn||(bn=xe(wn)),e[t=bn[t]||t]=r}function En(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date(1e3*(r/1e7*Math.pow(2,32)+t/1e7-11644473600)).toISOString().replace(/\.000/,"")}function Sn(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function yn(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function kn(e,t,r){return 31===t?yn(e):Sn(e,0,r)}function _n(e,t,r){return kn(e,t,!1===r?0:4)}function An(e){var t=e.l,r=On(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,On(e,3)]}function xn(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(_,"").replace(A,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function Cn(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function On(e,t,r){var a,n=e.read_shift(2),s=r||{};if(e.l+=2,12!==t&&n!==t&&-1===Ha.indexOf(t)&&(4126!=(65534&t)||4126!=(65534&n)))throw new Error("Expected type "+t+" saw "+n);switch(12===t?n:t){case 2:return a=e.read_shift(2,"i"),s.raw||(e.l+=2),a;case 3:return a=e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return a=e.read_shift(4);case 30:return Sn(e,0,4).replace(_,"");case 31:return yn(e);case 64:return En(e);case 65:return Cn(e);case 71:return function(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}(e);case 80:return _n(e,n,!s.raw).replace(_,"");case 81:return function(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return kn(e,t,0)}(e,n).replace(_,"");case 4108:return function(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(An(e));return r}(e);case 4126:case 4127:return 4127==n?function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(_,""),e.l-n&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(_,"");return r}(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+n)}}function Rn(e,t){var r=jr(4),a=jr(4);switch(r.write_shift(4,80==e?31:e),e){case 3:a.write_shift(-4,t);break;case 5:(a=jr(8)).write_shift(8,t,"f");break;case 11:a.write_shift(4,t?1:0);break;case 64:a=function(e){var t=("string"==typeof e?new Date(Date.parse(e)):e).getTime()/1e3+11644473600,r=t%Math.pow(2,32),a=(t-r)/Math.pow(2,32);a*=1e7;var n=(r*=1e7)/Math.pow(2,32)|0;n>0&&(r%=Math.pow(2,32),a+=n);var s=jr(8);return s.write_shift(4,r),s.write_shift(4,a),s}(t);break;case 31:case 80:for((a=jr(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),a.write_shift(0,t,"dbcs");a.l!=a.length;)a.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return k([r,a])}function In(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),i=[],o=0,c=0,l=-1,f={};for(o=0;o!=n;++o){var h=e.read_shift(4),u=e.read_shift(4);i[o]=[h,u+r]}i.sort((function(e,t){return e[1]-t[1]}));var d={};for(o=0;o!=n;++o){if(e.l!==i[o][1]){var p=!0;if(o>0&&t)switch(t[i[o-1][0]].t){case 2:e.l+2===i[o][1]&&(e.l+=2,p=!1);break;case 80:case 4108:e.l<=i[o][1]&&(e.l=i[o][1],p=!1)}if((!t||0==o)&&e.l<=i[o][1]&&(p=!1,e.l=i[o][1]),p)throw new Error("Read Error: Expected address "+i[o][1]+" at "+e.l+" :"+o)}if(t){var m=t[i[o][0]];if(d[m.n]=On(e,m.t,{raw:!0}),"version"===m.p&&(d[m.n]=String(d[m.n]>>16)+"."+("0000"+String(65535&d[m.n])).slice(-4)),"CodePage"==m.n)switch(d[m.n]){case 0:d[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:s(c=d[m.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+d[m.n])}}else if(1===i[o][0]){if(c=d.CodePage=On(e,2),s(c),-1!==l){var g=e.l;e.l=i[l][1],f=xn(e,c),e.l=g}}else if(0===i[o][0]){if(0===c){l=o,e.l=i[o+1][1];continue}f=xn(e,c)}else{var v,b=f[i[o][0]];switch(e[e.l]){case 65:e.l+=4,v=Cn(e);break;case 30:case 31:e.l+=4,v=_n(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,v=e.read_shift(4,"i");break;case 19:e.l+=4,v=e.read_shift(4);break;case 5:e.l+=4,v=e.read_shift(8,"f");break;case 11:e.l+=4,v=Un(e,4);break;case 64:e.l+=4,v=Be(En(e));break;default:throw new Error("unparsed value: "+e[e.l])}d[b]=v}}return e.l=r+a,d}var Nn=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function Dn(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return-1}function Fn(e,t,r){var a=jr(8),n=[],s=[],i=8,o=0,c=jr(8),l=jr(8);if(c.write_shift(4,2),c.write_shift(4,1200),l.write_shift(4,1),s.push(c),n.push(l),i+=8+c.length,!t){(l=jr(8)).write_shift(4,0),n.unshift(l);var f=[jr(4)];for(f[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];for((c=jr(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,o+2),c.write_shift(4,h.length+1),c.write_shift(0,h,"dbcs");c.l!=c.length;)c.write_shift(1,0);f.push(c)}c=k(f),s.unshift(c),i+=8+c.length}for(o=0;o<e.length;++o)if((!t||t[e[o][0]])&&!(Nn.indexOf(e[o][0])>-1||dn.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}c=Rn(p.t,u)}else{var g=Dn(u);-1==g&&(g=31,u=String(u)),c=Rn(g,u)}s.push(c),(l=jr(8)).write_shift(4,t?d:2+o),n.push(l),i+=8+c.length}var v=8*(s.length+1);for(o=0;o<s.length;++o)n[o].write_shift(4,v),v+=s[o].length;return a.write_shift(4,i),a.write_shift(4,s.length),k([a].concat(n).concat(s))}function Pn(e,t,r){var a=e.content;if(!a)return{};zr(a,0);var n,s,i,o,c=0;a.chk("feff","Byte Order: "),a.read_shift(2);var l=a.read_shift(4),f=a.read_shift(16);if(f!==Se.utils.consts.HEADER_CLSID&&f!==r)throw new Error("Bad PropertySet CLSID "+f);if(1!==(n=a.read_shift(4))&&2!==n)throw new Error("Unrecognized #Sets: "+n);if(s=a.read_shift(16),o=a.read_shift(4),1===n&&o!==a.l)throw new Error("Length mismatch: "+o+" !== "+a.l);2===n&&(i=a.read_shift(16),c=a.read_shift(4));var h,u=In(a,t),d={SystemIdentifier:l};for(var p in u)d[p]=u[p];if(d.FMTID=s,1===n)return d;if(c-a.l==2&&(a.l+=2),a.l!==c)throw new Error("Length mismatch 2: "+a.l+" !== "+c);try{h=In(a,null)}catch(m){}for(p in h)d[p]=h[p];return d.FMTID=[s,i],d}function Ln(e,t,r,a,n,s){var i=jr(n?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,842412599),i.write_shift(16,Se.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,n?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,n?68:48);var c=Fn(e,r,a);if(o.push(c),n){var l=Fn(n,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+c.length),o.push(l)}return k(o)}function Mn(e,t){return e.read_shift(t),null}function Un(e,t){return 1===e.read_shift(t)}function Bn(e,t){return t||(t=jr(2)),t.write_shift(2,+!!e),t}function Wn(e){return e.read_shift(2,"u")}function Hn(e,t){return t||(t=jr(2)),t.write_shift(2,e),t}function Vn(e,t){return function(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}(e,t,Wn)}function zn(e,t,r){return r||(r=jr(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,"e"==t?1:0),r}function Gn(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont";(r&&r.biff,r&&8!=r.biff)?12==r.biff&&(n="wstr"):e.read_shift(1)&&(n="dbcs-cont");return r.biff>=2&&r.biff<=5&&(n="cpstr"),a?e.read_shift(a,n):""}function jn(e){var t,r=e.read_shift(2),a=e.read_shift(1),n=4&a,s=8&a,i=1+(1&a),o=0,c={};s&&(o=e.read_shift(2)),n&&(t=e.read_shift(4));var l=2==i?"dbcs-cont":"sbcs-cont",f=0===r?"":e.read_shift(r,l);return s&&(e.l+=4*o),n&&(e.l+=t),c.t=f,s||(c.raw="<t>"+c.t+"</t>",c.r=c.t),c}function Xn(e){var t=e.t||"",r=jr(3);r.write_shift(2,t.length),r.write_shift(1,1);var a=jr(2*t.length);return a.write_shift(2*t.length,t,"utf16le"),k([r,a])}function $n(e,t,r){if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function Yn(e,t,r){var a=e.read_shift(r&&2==r.biff?1:2);return 0===a?(e.l++,""):$n(e,a,r)}function Kn(e,t,r){if(r.biff>5)return Yn(e,0,r);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function Jn(e,t,r){return r||(r=jr(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function qn(e,t){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return function(e){var t=e.read_shift(4),r=e.l,a=!1;t>24&&(e.l+=t-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(a=!0),e.l=r);var n=e.read_shift((a?t-24:t)>>1,"utf16le").replace(_,"");return a&&(e.l+=24),n}(e);case"0303000000000000c000000000000046":return function(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw new Error("Bad FileMoniker");if(0===e.read_shift(4))return r+a.replace(/\\/g,"/");var n=e.read_shift(4);if(3!=e.read_shift(2))throw new Error("Bad FileMoniker");return r+e.read_shift(n>>1,"utf16le").replace(_,"")}(e);default:throw new Error("Unsupported Moniker "+r)}}function Zn(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(_,""):""}function Qn(e,t){t||(t=jr(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function es(e){var t=jr(512),r=0,a=e.Target;"file://"==a.slice(0,7)&&(a=a.slice(7));var n=a.indexOf("#"),s=n>-1?31:23;switch(a.charAt(0)){case"#":s=28;break;case".":s&=-3}t.write_shift(4,2),t.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(r=0;r<i.length;++r)t.write_shift(4,i[r]);if(28==s)Qn(a=a.slice(1),t);else if(2&s){for(i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<i.length;++r)t.write_shift(1,parseInt(i[r],16));var o=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&s&&Qn(n>-1?a.slice(n+1):"",t)}else{for(i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<i.length;++r)t.write_shift(1,parseInt(i[r],16));for(var c=0;"../"==a.slice(3*c,3*c+3)||"..\\"==a.slice(3*c,3*c+3);)++c;for(t.write_shift(2,c),t.write_shift(4,a.length-3*c+1),r=0;r<a.length-3*c;++r)t.write_shift(1,255&a.charCodeAt(r+3*c));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function ts(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function rs(e,t){var r=ts(e);return r[3]=0,r}function as(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function ns(e,t,r,a){return a||(a=jr(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function ss(e,t,r){var a=r.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}function is(e){return[e.read_shift(2),Ia(e)]}function os(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function cs(e,t){return t||(t=jr(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function ls(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}var fs=ls;function hs(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function us(e){e.l+=2,e.l+=e.read_shift(2)}var ds={0:us,4:us,5:us,6:us,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:us,9:us,10:us,11:us,12:us,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:us,15:us,16:us,17:us,18:us,19:us,20:us,21:hs};function ps(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function ms(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;default:throw new Error("unsupported BIFF version")}var s=jr(n);return s.write_shift(2,a),s.write_shift(2,t),n>4&&s.write_shift(2,29282),n>6&&s.write_shift(2,1997),n>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function gs(e,t){var r=!t||t.biff>=8?2:1,a=jr(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}function vs(e,t,r){var a=0;r&&2==r.biff||(a=e.read_shift(2));var n=e.read_shift(2);return r&&2==r.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}function bs(e,t,r,a){var n=r&&5==r.biff;a||(a=jr(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var s=a.length>a.l?a.slice(0,a.l):a;return null==s.l&&(s.l=s.length),s}var ws=Kn;function Ts(e,t,r){var a=e.l+t,n=8!=r.biff&&r.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),o=e.read_shift(2),c=e.read_shift(2);return e.l=a,{s:{r:s,c:o},e:{r:i,c:c}}}function Es(e,t,r,a){var n=r&&5==r.biff;a||(a=jr(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&n&&(s|=1024),a.write_shift(4,s),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function Ss(e,t,r){var a=as(e);2!=r.biff&&9!=t||++e.l;var n=function(e){var t=e.read_shift(1);return 1===e.read_shift(1)?t:1===t}(e);return a.val=n,a.t=!0===n||!1===n?"b":"e",a}var ys=function(e,t,r){return 0===t?"":Kn(e,0,r)};function ks(e,t,r){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===r.sbcch&&(a=function(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=Gn(e,0,r),s=e.read_shift(2);if(s!==(a-=e.l))throw new Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,t-2,r)),s.body=a||e.read_shift(t-2),"string"==typeof a&&(s.Name=a),s}var _s=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function As(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(r&&2==r.biff?1:2),c=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),c=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var l=$n(e,i,r);32&n&&(l=_s[l.charCodeAt(0)]);var f=a-e.l;r&&2==r.biff&&--f;var h=a!=e.l&&0!==o&&f>0?function(e,t,r,a){var n,s=e.l+t,i=Ho(e,a,r);s!==e.l&&(n=Wo(e,s-e.l,i,r));return[i,n]}(e,f,r,o):[];return{chKey:s,Name:l,itab:c,rgce:h}}function xs(e,t,r){if(r.biff<8)return function(e,t,r){3==e[e.l+1]&&e[e.l]++;var a=Gn(e,0,r);return 3==a.charCodeAt(0)?a.slice(1):a}(e,0,r);for(var a=[],n=e.l+t,s=e.read_shift(r.biff>8?4:2);0!==s--;)a.push(ss(e,r.biff,r));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function Cs(e,t,r){var a=fs(e);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,$o(e,t,r)]}var Os={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function Rs(e){var t=jr(24),r=aa(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return k([t,es(e[1])])}function Is(e){var t=e[1].Tooltip,r=jr(10+2*(t.length+1));r.write_shift(2,2048);var a=aa(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}function Ns(e,t,r){if(!r.cellStyles)return Gr(e,t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),o=e.read_shift(a),c=e.read_shift(2);2==a&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:o,flags:c};return(r.biff>=5||!r.biff)&&(l.level=c>>8&7),l}var Ds=as,Fs=Vn,Ps=Yn;var Ls=[2,3,48,49,131,139,140,245],Ms=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},r=xe({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function a(t,r){var a=r||{};a.dateNF||(a.dateNF="yyyymmdd");var n=ua(function(t,r){var a=[],n=b(1);switch(r.type){case"base64":n=T(m(t));break;case"binary":n=T(t);break;case"buffer":case"array":n=t}zr(n,0);var s=n.read_shift(1),i=!!(136&s),o=!1,c=!1;switch(s){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:o=!0,i=!0;break;case 140:c=!0;break;default:throw new Error("DBF Unsupported Version: "+s.toString(16))}var f=0,h=521;2==s&&(f=n.read_shift(2)),n.l+=3,2!=s&&(f=n.read_shift(4)),f>1048576&&(f=1e6),2!=s&&(h=n.read_shift(2));var u=n.read_shift(2),d=r.codepage||1252;2!=s&&(n.l+=16,n.read_shift(1),0!==n[n.l]&&(d=e[n[n.l]]),n.l+=1,n.l+=2),c&&(n.l+=36);for(var p=[],g={},v=Math.min(n.length,2==s?521:h-10-(o?264:0)),w=c?32:11;n.l<v&&13!=n[n.l];)switch((g={}).name=l.utils.decode(d,n.slice(n.l,n.l+w)).replace(/[\u0000\r\n].*$/g,""),n.l+=w,g.type=String.fromCharCode(n.read_shift(1)),2==s||c||(g.offset=n.read_shift(4)),g.len=n.read_shift(1),2==s&&(g.offset=n.read_shift(2)),g.dec=n.read_shift(1),g.name.length&&p.push(g),2!=s&&(n.l+=c?13:14),g.type){case"B":o&&8==g.len||!r.WTF||console.log("Skipping "+g.name+":"+g.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+g.name+":"+g.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+g.type)}if(13!==n[n.l]&&(n.l=h-1),13!==n.read_shift(1))throw new Error("DBF Terminator not found "+n.l+" "+n[n.l]);n.l=h;var E=0,S=0;for(a[0]=[],S=0;S!=p.length;++S)a[0][S]=p[S].name;for(;f-- >0;)if(42!==n[n.l])for(++n.l,a[++E]=[],S=0,S=0;S!=p.length;++S){var y=n.slice(n.l,n.l+p[S].len);n.l+=p[S].len,zr(y,0);var k=l.utils.decode(d,y);switch(p[S].type){case"C":k.trim().length&&(a[E][S]=k.replace(/\s+$/,""));break;case"D":8===k.length?a[E][S]=new Date(+k.slice(0,4),+k.slice(4,6)-1,+k.slice(6,8)):a[E][S]=k;break;case"F":a[E][S]=parseFloat(k.trim());break;case"+":case"I":a[E][S]=c?2147483648^y.read_shift(-4,"i"):y.read_shift(4,"i");break;case"L":switch(k.trim().toUpperCase()){case"Y":case"T":a[E][S]=!0;break;case"N":case"F":a[E][S]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+k+"|")}break;case"M":if(!i)throw new Error("DBF Unexpected MEMO for type "+s.toString(16));a[E][S]="##MEMO##"+(c?parseInt(k.trim(),10):y.read_shift(4));break;case"N":(k=k.replace(/\u0000/g,"").trim())&&"."!=k&&(a[E][S]=+k||0);break;case"@":a[E][S]=new Date(y.read_shift(-8,"f")-621356832e5);break;case"T":a[E][S]=new Date(864e5*(y.read_shift(4)-2440588)+y.read_shift(4));break;case"Y":a[E][S]=y.read_shift(4,"i")/1e4+y.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":a[E][S]=-y.read_shift(-8,"f");break;case"B":if(o&&8==p[S].len){a[E][S]=y.read_shift(8,"f");break}case"G":case"P":y.l+=p[S].len;break;case"0":if("_NullFlags"===p[S].name)break;default:throw new Error("DBF Unsupported data type "+p[S].type)}}else n.l+=u;if(2!=s&&n.l<n.length&&26!=n[n.l++])throw new Error("DBF EOF Marker missing "+(n.l-1)+" of "+n.length+" "+n[n.l-1].toString(16));return r&&r.sheetRows&&(a=a.slice(0,r.sheetRows)),r.DBF=p,a}(t,a),a);return n["!cols"]=a.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete a.DBF,n}var n={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return fa(a(e,t),t)}catch(r){if(t&&t.WTF)throw r}return{SheetNames:[],Sheets:{}}},to_sheet:a,from_sheet:function(e,a){var i=a||{};if(+i.codepage>=0&&s(+i.codepage),"string"==i.type)throw new Error("Cannot write DBF to JS string");var o=$r(),c=mh(e,{header:1,raw:!0,cellDates:!0}),l=c[0],f=c.slice(1),h=e["!cols"]||[],u=0,d=0,p=0,m=1;for(u=0;u<l.length;++u)if(((h[u]||{}).DBF||{}).name)l[u]=h[u].DBF.name,++p;else if(null!=l[u]){if(++p,"number"==typeof l[u]&&(l[u]=l[u].toString(10)),"string"!=typeof l[u])throw new Error("DBF Invalid column name "+l[u]+" |"+typeof l[u]+"|");if(l.indexOf(l[u])!==u)for(d=0;d<1024;++d)if(-1==l.indexOf(l[u]+"_"+d)){l[u]+="_"+d;break}}var g=oa(e["!ref"]),v=[],b=[],w=[];for(u=0;u<=g.e.c-g.s.c;++u){var T="",E="",S=0,y=[];for(d=0;d<f.length;++d)null!=f[d][u]&&y.push(f[d][u]);if(0!=y.length&&null!=l[u]){for(d=0;d<y.length;++d){switch(typeof y[d]){case"number":E="B";break;case"string":default:E="C";break;case"boolean":E="L";break;case"object":E=y[d]instanceof Date?"D":"C"}S=Math.max(S,String(y[d]).length),T=T&&T!=E?"C":E}S>250&&(S=250),"C"==(E=((h[u]||{}).DBF||{}).type)&&h[u].DBF.len>S&&(S=h[u].DBF.len),"B"==T&&"N"==E&&(T="N",w[u]=h[u].DBF.dec,S=h[u].DBF.len),b[u]="C"==T||"N"==E?S:n[T]||0,m+=b[u],v[u]=T}else v[u]="?"}var k=o.next(32);for(k.write_shift(4,318902576),k.write_shift(4,f.length),k.write_shift(2,296+32*p),k.write_shift(2,m),u=0;u<4;++u)k.write_shift(4,0);for(k.write_shift(4,(+r[t]||3)<<8),u=0,d=0;u<l.length;++u)if(null!=l[u]){var _=o.next(32),A=(l[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);_.write_shift(1,A,"sbcs"),_.write_shift(1,"?"==v[u]?"C":v[u],"sbcs"),_.write_shift(4,d),_.write_shift(1,b[u]||n[v[u]]||0),_.write_shift(1,w[u]||0),_.write_shift(1,2),_.write_shift(4,0),_.write_shift(1,0),_.write_shift(4,0),_.write_shift(4,0),d+=b[u]||n[v[u]]||0}var x=o.next(264);for(x.write_shift(4,13),u=0;u<65;++u)x.write_shift(4,0);for(u=0;u<f.length;++u){var C=o.next(m);for(C.write_shift(1,0),d=0;d<l.length;++d)if(null!=l[d])switch(v[d]){case"L":C.write_shift(1,null==f[u][d]?63:f[u][d]?84:70);break;case"B":C.write_shift(8,f[u][d]||0,"f");break;case"N":var O="0";for("number"==typeof f[u][d]&&(O=f[u][d].toFixed(w[d]||0)),p=0;p<b[d]-O.length;++p)C.write_shift(1,32);C.write_shift(1,O,"sbcs");break;case"D":f[u][d]?(C.write_shift(4,("0000"+f[u][d].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(f[u][d].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+f[u][d].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var R=String(null!=f[u][d]?f[u][d]:"").slice(0,b[d]);for(C.write_shift(1,R,"sbcs"),p=0;p<b[d]-R.length;++p)C.write_shift(1,32)}}return o.next(1).write_shift(1,26),o.end()}}}(),Us=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("N("+_e(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var a=e[r];return"number"==typeof a?u(a):a},a=function(e,t,r){var a=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==a?e:u(a)};function n(e,n){var i,o=e.split(/[\n\r]+/),c=-1,l=-1,f=0,h=0,u=[],d=[],p=null,m={},g=[],v=[],b=[],w=0;for(+n.codepage>=0&&s(+n.codepage);f!==o.length;++f){w=0;var T,E=o[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),S=E.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),y=S[0];if(E.length>0)switch(y){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==S[1].charAt(0)&&d.push(E.slice(3).replace(/;;/g,";"));break;case"C":var k=!1,_=!1,A=!1,x=!1,C=-1,O=-1;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"A":case"G":break;case"X":l=parseInt(S[h].slice(1))-1,_=!0;break;case"Y":for(c=parseInt(S[h].slice(1))-1,_||(l=0),i=u.length;i<=c;++i)u[i]=[];break;case"K":'"'===(T=S[h].slice(1)).charAt(0)?T=T.slice(1,T.length-1):"TRUE"===T?T=!0:"FALSE"===T?T=!1:isNaN(ze(T))?isNaN(je(T).getDate())||(T=Be(T)):(T=ze(T),null!==p&&he(p)&&(T=Fe(T))),k=!0;break;case"E":x=!0;var R=mo(S[h].slice(1),{r:c,c:l});u[c][l]=[u[c][l],R];break;case"S":A=!0,u[c][l]=[u[c][l],"S5S"];break;case"R":C=parseInt(S[h].slice(1))-1;break;case"C":O=parseInt(S[h].slice(1))-1;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+E)}if(k&&(u[c][l]&&2==u[c][l].length?u[c][l][0]=T:u[c][l]=T,p=null),A){if(x)throw new Error("SYLK shared formula cannot have own formula");var I=C>-1&&u[C][O];if(!I||!I[1])throw new Error("SYLK shared formula cannot find base");u[c][l][1]=bo(I[1],{r:c-C,c:l-O})}break;case"F":var N=0;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"X":l=parseInt(S[h].slice(1))-1,++N;break;case"Y":for(c=parseInt(S[h].slice(1))-1,i=u.length;i<=c;++i)u[i]=[];break;case"M":w=parseInt(S[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":p=d[parseInt(S[h].slice(1))];break;case"W":for(b=S[h].slice(1).split(" "),i=parseInt(b[0],10);i<=parseInt(b[1],10);++i)w=parseInt(b[2],10),v[i-1]=0===w?{hidden:!0}:{wch:w},ki(v[i-1]);break;case"C":v[l=parseInt(S[h].slice(1))-1]||(v[l]={});break;case"R":g[c=parseInt(S[h].slice(1))-1]||(g[c]={}),w>0?(g[c].hpt=w,g[c].hpx=xi(w)):0===w&&(g[c].hidden=!0);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+E)}N<1&&(p=null);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+E)}}return g.length>0&&(m["!rows"]=g),v.length>0&&(m["!cols"]=v),n&&n.sheetRows&&(u=u.slice(0,n.sheetRows)),[u,m]}function i(e,t){var r=function(e,t){switch(t.type){case"base64":return n(m(e),t);case"binary":return n(e,t);case"buffer":return n(g&&Buffer.isBuffer(e)?e.toString("binary"):S(e),t);case"array":return n(We(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),a=r[0],s=r[1],i=ua(a,t);return _e(s).forEach((function(e){i[e]=s[e]})),i}function o(e,t,r,a){var n="C;Y"+(r+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+vo(e.f,{r:r,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return n}return e["|"]=254,{to_workbook:function(e,t){return fa(i(e,t),t)},to_sheet:i,from_sheet:function(e,t){var r,a,n=["ID;PWXL;N;E"],s=[],i=oa(e["!ref"]),c=Array.isArray(e),l="\r\n";n.push("P;PGeneral"),n.push("F;P0;DG0G8;M255"),e["!cols"]&&(a=n,e["!cols"].forEach((function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=wi(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=Ti(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&a.push(r)}))),e["!rows"]&&function(e,t){t.forEach((function(t,r){var a="F;";t.hidden?a+="M0;":t.hpt?a+="M"+20*t.hpt+";":t.hpx&&(a+="M"+20*Ai(t.hpx)+";"),a.length>2&&e.push(a+"R"+(r+1))}))}(n,e["!rows"]),n.push("B;Y"+(i.e.r-i.s.r+1)+";X"+(i.e.c-i.s.c+1)+";D"+[i.s.c,i.s.r,i.e.c,i.e.r].join(" "));for(var f=i.s.r;f<=i.e.r;++f)for(var h=i.s.c;h<=i.e.c;++h){var u=na({r:f,c:h});(r=c?(e[f]||[])[h]:e[u])&&(null!=r.v||r.f&&!r.F)&&s.push(o(r,0,f,h))}return n.join(l)+l+s.join(l)+l+"E"+l}}}(),Bs=function(){function e(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s)if("BOT"!==r[s].trim()){if(!(a<0)){for(var o=r[s].trim().split(","),c=o[0],l=o[1],f=r[++s]||"";1&(f.match(/["]/g)||[]).length&&s<r.length-1;)f+="\n"+r[++s];switch(f=f.trim(),+c){case-1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw new Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(ze(l))?isNaN(je(l).getDate())?i[a][n]=l:i[a][n]=Be(l):i[a][n]=ze(l),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}else i[++a]=[],n=0;return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}function t(t,r){return ua(function(t,r){switch(r.type){case"base64":return e(m(t),r);case"binary":return e(t,r);case"buffer":return e(g&&Buffer.isBuffer(t)?t.toString("binary"):S(t),r);case"array":return e(We(t),r)}throw new Error("Unrecognized type "+r.type)}(t,r),r)}return{to_workbook:function(e,r){return fa(t(e,r),r)},to_sheet:t,from_sheet:function(){var e=function(e,t,r,a,n){e.push(t),e.push(r+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},t=function(e,t,r,a){e.push(t+","+r),e.push(1==t?'"'+a.replace(/"/g,'""')+'"':a)};return function(r){var a,n=[],s=oa(r["!ref"]),i=Array.isArray(r);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var o=s.s.r;o<=s.e.r;++o){t(n,-1,0,"BOT");for(var c=s.s.c;c<=s.e.c;++c){var l=na({r:o,c:c});if(a=i?(r[o]||[])[c]:r[l])switch(a.t){case"n":var f=a.w;f||null==a.v||(f=a.v),null==f?a.f&&!a.F?t(n,1,0,"="+a.f):t(n,1,0,""):t(n,0,f,"V");break;case"b":t(n,0,a.v?1:0,a.v?"TRUE":"FALSE");break;case"s":t(n,1,0,isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=me(a.z||L[14],Re(Be(a.v)))),t(n,0,a.w,"V");break;default:t(n,1,0,"")}else t(n,1,0,"")}}t(n,-1,0,"EOD");return n.join("\r\n")}}()}}(),Ws=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return ua(function(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){var o=r[s].trim().split(":");if("cell"===o[0]){var c=aa(o[1]);if(i.length<=c.r)for(a=i.length;a<=c.r;++a)i[a]||(i[a]=[]);switch(a=c.r,n=c.c,o[2]){case"t":i[a][n]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+o[3];break;case"vtf":var l=o[o.length-1];case"vtc":"nl"===o[3]?i[a][n]=!!+o[4]:i[a][n]=+o[4],"vtf"==o[2]&&(i[a][n]=[i[a][n],l])}}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}(e,t),t)}var r=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),a=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",n=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),s="--SocialCalcSpreadsheetControlSave--";function i(t){if(!t||!t["!ref"])return"";for(var r,a=[],n=[],s="",i=sa(t["!ref"]),o=Array.isArray(t),c=i.s.r;c<=i.e.r;++c)for(var l=i.s.c;l<=i.e.c;++l)if(s=na({r:c,c:l}),(r=o?(t[c]||[])[l]:t[s])&&null!=r.v&&"z"!==r.t){switch(n=["cell",s,"t"],r.t){case"s":case"str":n.push(e(r.v));break;case"n":r.f?(n[2]="vtf",n[3]="n",n[4]=r.v,n[5]=e(r.f)):(n[2]="v",n[3]=r.v);break;case"b":n[2]="vt"+(r.f?"f":"c"),n[3]="nl",n[4]=r.v?"1":"0",n[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var f=Re(Be(r.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=r.w||me(r.z||L[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}return{to_workbook:function(e,r){return fa(t(e,r),r)},to_sheet:t,from_sheet:function(e){return[r,a,n,a,i(e),s].join("\n")}}}(),Hs=function(){function e(e,t,r,a,n){n.raw?t[r][a]=e:""===e||("TRUE"===e?t[r][a]=!0:"FALSE"===e?t[r][a]=!1:isNaN(ze(e))?isNaN(je(e).getDate())?t[r][a]=e:t[r][a]=Be(e):t[r][a]=ze(e))}var t={44:",",9:"\t",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function a(e){for(var a={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in t&&(a[i]=(a[i]||0)+1);for(s in i=[],a)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);if(!i.length)for(s in a=r)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);return i.sort((function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]})),t[i.pop()[1]]||44}function n(e,t){var r=t||{},n="",s=r.dense?[]:{},i={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(n=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(n=e.charAt(4),e=e.slice(6)):n=a(e.slice(0,1024)):n=r&&r.FS?r.FS:a(e.slice(0,1024));var o=0,c=0,l=0,f=0,h=0,u=n.charCodeAt(0),d=!1,p=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var g,v,b=null!=r.dateNF?(g=r.dateNF,v=(v="number"==typeof g?L[g]:g).replace(Te,"(\\d+)"),new RegExp("^"+v+"$")):null;function w(){var t=e.slice(f,h),a={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)a.t="z";else if(r.raw)a.t="s",a.v=t;else if(0===t.trim().length)a.t="s",a.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(a.t="s",a.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(a.t="n",a.f=t.slice(1)):(a.t="s",a.v=t);else if("TRUE"==t)a.t="b",a.v=!0;else if("FALSE"==t)a.t="b",a.v=!1;else if(isNaN(l=ze(t)))if(!isNaN(je(t).getDate())||b&&t.match(b)){a.z=r.dateNF||L[14];var n=0;b&&t.match(b)&&(t=function(e,t,r){var a=-1,n=-1,s=-1,i=-1,o=-1,c=-1;(t.match(Te)||[]).forEach((function(e,t){var l=parseInt(r[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=l;break;case"d":s=l;break;case"h":i=l;break;case"s":c=l;break;case"m":i>=0?o=l:n=l}})),c>=0&&-1==o&&n>=0&&(o=n,n=-1);var l=(""+(a>=0?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);7==l.length&&(l="0"+l),8==l.length&&(l="20"+l);var f=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2);return-1==i&&-1==o&&-1==c?l:-1==a&&-1==n&&-1==s?f:l+"T"+f}(0,r.dateNF,t.match(b)||[]),n=1),r.cellDates?(a.t="d",a.v=Be(t,n)):(a.t="n",a.v=Re(Be(t,n))),!1!==r.cellText&&(a.w=me(a.z,a.v instanceof Date?Re(a.v):a.v)),r.cellNF||delete a.z}else a.t="s",a.v=t;else a.t="n",!1!==r.cellText&&(a.w=t),a.v=l;if("z"==a.t||(r.dense?(s[o]||(s[o]=[]),s[o][c]=a):s[na({c:c,r:o})]=a),f=h+1,m=e.charCodeAt(f),i.e.c<c&&(i.e.c=c),i.e.r<o&&(i.e.r=o),p==u)++c;else if(c=0,++o,r.sheetRows&&r.sheetRows<=o)return!0}e:for(;h<e.length;++h)switch(p=e.charCodeAt(h)){case 34:34===m&&(d=!d);break;case u:case 10:case 13:if(!d&&w())break e}return h-f>0&&w(),s["!ref"]=ia(i),s}function s(t,r){return r&&r.PRN?r.FS||"sep="==t.slice(0,4)||t.indexOf("\t")>=0||t.indexOf(",")>=0||t.indexOf(";")>=0?n(t,r):ua(function(t,r){var a=r||{},n=[];if(!t||0===t.length)return n;for(var s=t.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var o=10,c=0,l=0;l<=i;++l)-1==(c=s[l].indexOf(" "))?c=s[l].length:c++,o=Math.max(o,c);for(l=0;l<=i;++l){n[l]=[];var f=0;for(e(s[l].slice(0,o).trim(),n,l,f,a),f=1;f<=(s[l].length-o)/10+1;++f)e(s[l].slice(o+10*(f-1),o+10*f).trim(),n,l,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(t,r),r):n(t,r)}function i(e,t){var r="",a="string"==t.type?[0,0,0,0]:nh(e,t);switch(t.type){case"base64":r=m(e);break;case"binary":case"string":r=e;break;case"buffer":65001==t.codepage?r=e.toString("utf8"):(t.codepage,r=g&&Buffer.isBuffer(e)?e.toString("binary"):S(e));break;case"array":r=We(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==a[0]&&187==a[1]&&191==a[2]?r=Ct(r.slice(3)):"string"!=t.type&&"buffer"!=t.type&&65001==t.codepage?r=Ct(r):t.type,"socialcalc:version:"==r.slice(0,19)?Ws.to_sheet("string"==t.type?r:Ct(r),t):s(r,t)}return{to_workbook:function(e,t){return fa(i(e,t),t)},to_sheet:i,from_sheet:function(e){for(var t,r=[],a=oa(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],o=a.s.c;o<=a.e.c;++o){var c=na({r:s,c:o});if((t=n?(e[s]||[])[o]:e[c])&&null!=t.v){for(var l=(t.w||(la(t),t.w)||"").slice(0,10);l.length<10;)l+=" ";i.push(l+(0===o?" ":""))}else i.push("          ")}r.push(i.join(""))}return r.join("\n")}}}();var Vs=function(){function e(e,t,r){if(e){zr(e,e.l||0);for(var a=r.Enum||w;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),o=e.l+i,c=s.f&&s.f(e,i,r);if(e.l=o,t(c,s,n))return}}}function t(t,r){if(!t)return t;var a=r||{},n=a.dense?[]:{},s="Sheet1",i="",o=0,c={},l=[],f=[],h={s:{r:0,c:0},e:{r:0,c:0}},u=a.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw new Error("Unsupported Works 3 for Mac file");if(2==t[2])a.Enum=w,e(t,(function(e,t,r){switch(r){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:h=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&!(112&~e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||L[14],a.cellDates&&(e[1].t="d",e[1].v=Fe(e[1].v))),a.qpro&&e[3]>o&&(n["!ref"]=ia(h),c[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s=i||"Sheet"+(o+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[na(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[na(e[0])]=e[1]}}),a);else{if(26!=t[2]&&14!=t[2])throw new Error("Unrecognized LOTUS BOF "+t[2]);a.Enum=E,14==t[2]&&(a.qpro=!0,t.l=0),e(t,(function(e,t,r){switch(r){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(n["!ref"]=ia(h),c[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s="Sheet"+(o+1)),u>0&&e[0].r>=u)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[na(e[0])]=e[1],h.e.c<e[0].c&&(h.e.c=e[0].c),h.e.r<e[0].r&&(h.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==o&&(s=e[1])}}),a)}if(n["!ref"]=ia(h),c[i||s]=n,l.push(i||s),!f.length)return{SheetNames:l,Sheets:c};for(var d={},p=[],m=0;m<f.length;++m)c[l[m]]?(p.push(f[m]||l[m]),d[f[m]]=c[f[m]]||c[l[m]]):(p.push(f[m]),d[f[m]]={"!ref":"A1"});return{SheetNames:p,Sheets:d}}function r(e,t,r){var a=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function a(e,t,a){var n=e.l+t,s=r(e,0,a);if(s[1].t="s",20768==a.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return a.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}function n(e,t,r){var a=jr(7+r.length);a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=r.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}function i(e,t,r){var a=jr(7);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(2,r,"i"),a}function o(e,t,r){var a=jr(13);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(8,r,"f"),a}function c(e,t,r){var a=32768&t;return t=(a?e:0)+((t&=-32769)>=8192?t-16384:t),(a?"":"$")+(r?ra(t):ea(t))}var l={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},f=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function h(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function u(e,t,r,a){var n=jr(6+a.length);n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function d(e,t){var r=h(e),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&3221225472===n?(r[1].t="e",r[1].v=15):0===a&&3489660928===n?(r[1].t="e",r[1].v=42):r[1].v=0,r;var i=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),r}function p(e,t,r,a){var n=jr(14);if(n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s,i=0,o=0,c=0;return a<0&&(i=1,a=-a),o=0|Math.log2(a),2147483648&(c=(a/=Math.pow(2,o-31))>>>0)||(++o,c=(a/=2)>>>0),a-=c,c|=2147483648,c>>>=0,s=(a*=Math.pow(2,32))>>>0,n.write_shift(4,s),n.write_shift(4,c),o+=16383+(i?32768:0),n.write_shift(2,o),n}function g(e,t){var r=h(e),a=e.read_shift(8,"f");return r[1].v=a,r}function v(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}function b(e,t){var r=jr(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);r[r.l++]=n>127?95:n}return r[r.l++]=0,r}var w={0:{n:"BOF",f:Wn},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2),a):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0),a)}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,a){var n=r(e,0,a);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,t,a){var n=r(e,0,a);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:a},16:{n:"FORMULA",f:function(e,t,a){var n=e.l+t,s=r(e,0,a);if(s[1].v=e.read_shift(8,"f"),a.qpro)e.l=n;else{var i=e.read_shift(2);!function(e,t){zr(e,0);var r=[],a=0,n="",s="",i="",o="";for(;e.l<e.length;){var h=e[e.l++];switch(h){case 0:r.push(e.read_shift(8,"f"));break;case 1:s=c(t[0].c,e.read_shift(2),!0),n=c(t[0].r,e.read_shift(2),!1),r.push(s+n);break;case 2:var u=c(t[0].c,e.read_shift(2),!0),d=c(t[0].r,e.read_shift(2),!1);s=c(t[0].c,e.read_shift(2),!0),n=c(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+s+n);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";h=e[e.l++];)p+=String.fromCharCode(h);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:o=r.pop(),i=r.pop(),r.push(["AND","OR"][h-20]+"("+i+","+o+")");break;default:if(h<32&&f[h])o=r.pop(),i=r.pop(),r.push(i+f[h]+o);else{if(!l[h])return h<=7?console.error("WK1 invalid opcode "+h.toString(16)):h<=24?console.error("WK1 unsupported op "+h.toString(16)):h<=30?console.error("WK1 invalid opcode "+h.toString(16)):h<=115?console.error("WK1 unsupported function opcode "+h.toString(16)):console.error("WK1 unrecognized opcode "+h.toString(16));if(69==(a=l[h][1])&&(a=e[e.l++]),a>r.length)return void console.error("WK1 bad formula parse 0x"+h.toString(16)+":|"+r.join("|")+"|");var m=r.slice(-a);r.length-=a,r.push(l[h][0]+"("+m.join(",")+")")}}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")}(e.slice(e.l,e.l+i),s),e.l+=i}return s}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:a},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:v},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var a="";a.length<r;)a+=String.fromCharCode(e[e.l++]);return a}},65535:{n:""}},E={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=h(e);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:d},24:{n:"NUMBER18",f:function(e,t){var r=h(e);r[1].v=e.read_shift(2);var a=r[1].v>>1;if(1&r[1].v)switch(7&a){case 0:a=5e3*(a>>3);break;case 1:a=500*(a>>3);break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return r[1].v=a,r}},25:{n:"FORMULA19",f:function(e,t){var r=d(e);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},a=e.l+t;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(r[n]=[0,""],r[n][0]=e.read_shift(2);e[e.l];)r[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=h(e),a=e.read_shift(4);return r[1].v=a>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:g},40:{n:"FORMULA28",f:function(e,t){var r=g(e);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:v},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r=t||{};if(+r.codepage>=0&&s(+r.codepage),"string"==r.type)throw new Error("Cannot write WK1 to JS string");var a,c,l=$r(),f=oa(e["!ref"]),h=Array.isArray(e),u=[];Ql(l,0,(a=1030,(c=jr(2)).write_shift(2,a),c)),Ql(l,6,function(e){var t=jr(8);return t.write_shift(2,e.s.c),t.write_shift(2,e.s.r),t.write_shift(2,e.e.c),t.write_shift(2,e.e.r),t}(f));for(var d=Math.min(f.e.r,8191),p=f.s.r;p<=d;++p)for(var m=ea(p),g=f.s.c;g<=f.e.c;++g){p===f.s.r&&(u[g]=ra(g));var v=u[g]+m,b=h?(e[p]||[])[g]:e[v];if(b&&"z"!=b.t)if("n"==b.t)(0|b.v)==b.v&&b.v>=-32768&&b.v<=32767?Ql(l,13,i(p,g,b.v)):Ql(l,14,o(p,g,b.v));else Ql(l,15,n(p,g,la(b).slice(0,239)))}return Ql(l,1),l.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&s(+r.codepage),"string"==r.type)throw new Error("Cannot write WK3 to JS string");var a=$r();Ql(a,0,function(e){var t=jr(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],o=e.Sheets[i];if(o&&o["!ref"]){++n;var c=sa(o["!ref"]);r<c.e.r&&(r=c.e.r),a<c.e.c&&(a=c.e.c)}}r>8191&&(r=8191);return t.write_shift(2,r),t.write_shift(1,n),t.write_shift(1,a),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var n=0,i=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&Ql(a,27,b(e.SheetNames[n],i++));var o=0;for(n=0;n<e.SheetNames.length;++n){var c=e.Sheets[e.SheetNames[n]];if(c&&c["!ref"]){for(var l=oa(c["!ref"]),f=Array.isArray(c),h=[],d=Math.min(l.e.r,8191),m=l.s.r;m<=d;++m)for(var g=ea(m),v=l.s.c;v<=l.e.c;++v){m===l.s.r&&(h[v]=ra(v));var w=h[v]+g,T=f?(c[m]||[])[v]:c[w];if(T&&"z"!=T.t)if("n"==T.t)Ql(a,23,p(m,v,o,T.v));else Ql(a,22,u(m,v,o,la(T).slice(0,239)))}++o}}return Ql(a,1),a.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(T(m(e)),r);case"binary":return t(T(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}();var zs=function(){var e=Rt("t"),t=Rt("rPr");function r(r){var n=r.match(e);if(!n)return{t:"s",v:""};var s={t:"s",v:mt(n[1])},i=r.match(t);return i&&(s.s=function(e){var t={},r=e.match(ct),n=0,s=!1;if(r)for(;n!=r.length;++n){var i=ht(r[n]);switch(i[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!i.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==i.val)break;t.cp=a[parseInt(i.val,10)];break;case"<outline":if(!i.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=i.val;break;case"<sz":t.sz=i.val;break;case"<strike":if(!i.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!i.val)break;switch(i.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting"}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if("0"==i.val)break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if("0"==i.val)break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":i.rgb&&(t.color=i.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=i.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=i.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(47!==i[0].charCodeAt(1)&&!s)throw new Error("Unrecognized rich format "+i[0])}}return t}(i[1])),s}var n=/<(?:\w+:)?r>/g,s=/<\/(?:\w+:)?r>/;return function(e){return e.replace(n,"").split(s).map(r).filter((function(e){return e.v}))}}(),Gs=function(){var e=/(\r\n|\n)/g;function t(t){var r=[[],t.v,[]];return t.v?(t.s&&function(e,t,r){var a=[];e.u&&a.push("text-decoration: underline;"),e.uval&&a.push("text-underline-style:"+e.uval+";"),e.sz&&a.push("font-size:"+e.sz+"pt;"),e.outline&&a.push("text-effect: outline;"),e.shadow&&a.push("text-shadow: auto;"),t.push('<span style="'+a.join("")+'">'),e.b&&(t.push("<b>"),r.push("</b>")),e.i&&(t.push("<i>"),r.push("</i>")),e.strike&&(t.push("<s>"),r.push("</s>"));var n=e.valign||"";"superscript"==n||"super"==n?n="sup":"subscript"==n&&(n="sub"),""!=n&&(t.push("<"+n+">"),r.push("</"+n+">")),r.push("</span>")}(t.s,r[0],r[2]),r[0].join("")+r[1].replace(e,"<br/>")+r[2].join("")):""}return function(e){return e.map(t).join("")}}(),js=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Xs=/<(?:\w+:)?r>/,$s=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function Ys(e,t){var r=!t||t.cellHTML,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=mt(Ct(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=Ct(e),r&&(a.h=Et(a.t))):e.match(Xs)&&(a.r=Ct(e),a.t=mt(Ct((e.replace($s,"").match(js)||[]).join("").replace(ct,""))),r&&(a.h=Gs(zs(a.r)))),a):{t:""}}var Ks=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Js=/<(?:\w+:)?(?:si|sstItem)>/g,qs=/<\/(?:\w+:)?(?:si|sstItem)>/;var Zs=/^\s|\s$|[\t\n\r]/;function Qs(e,t){if(!t.bookSST)return"";var r=[st];r[r.length]=Bt("sst",null,{xmlns:rr[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(null!=e[a]){var n=e[a],s="<si>";n.r?s+=n.r:(s+="<t",n.t||(n.t=""),n.t.match(Zs)&&(s+=' xml:space="preserve"'),s+=">"+bt(n.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var ei=function(e,t){var r=!1;return null==t&&(r=!0,t=jr(15+4*e.t.length)),t.write_shift(1,0),ma(e.t,t),r?t.slice(0,t.l):t};function ti(e){var t=$r();Yr(t,159,function(e,t){return t||(t=jr(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}(e));for(var r=0;r<e.length;++r)Yr(t,19,ei(e[r]));return Yr(t,160),t.end()}function ri(e){for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function ai(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function ni(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}function si(e){var t=function(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=ai(e,4),t.U=ai(e,4),t.W=ai(e,4),t}(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),4!=e.read_shift(4))throw new Error("Bad !Primary record");return t}function ii(e,t){var r=e.l+t,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function oi(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function ci(e){if(36!=(63&e.read_shift(4)))throw new Error("EncryptionInfo mismatch");var t=e.read_shift(4);return{t:"Std",h:ii(e,t),v:oi(e,e.length-e.l)}}function li(){throw new Error("File is password-protected: ECMA-376 Extensible")}function fi(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),a={};return r.replace(ct,(function(e){var r=ht(e);switch(ut(r[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":t.forEach((function(e){a[e]=r[e]}));break;case"<dataIntegrity":a.encryptedHmacKey=r.encryptedHmacKey,a.encryptedHmacValue=r.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"<keyEncryptor":a.uri=r.uri;break;case"<encryptedKey":a.encs.push(r);break;default:throw r[0]}})),a}function hi(e){var t,r,a=0,n=ri(e),s=n.length+1;for((t=b(s))[0]=n.length,r=1;r!=s;++r)t[r]=n[r-1];for(r=s-1;r>=0;--r)a=((16384&a?1:0)|a<<1&32767)^t[r];return 52811^a}var ui=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,t){return 255&((r=e^t)/2|128*r);var r};return function(n){for(var s,i,o,c=ri(n),l=function(e){for(var a=t[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],o=0;7!=o;++o)64&i&&(a^=r[n]),i*=2,--n;return a}(c),f=c.length,h=b(16),u=0;16!=u;++u)h[u]=0;for(1&~f||(s=l>>8,h[f]=a(e[0],s),--f,s=255&l,i=c[c.length-1],h[f]=a(i,s));f>0;)s=l>>8,h[--f]=a(c[f],s),s=255&l,h[--f]=a(c[f],s);for(f=15,o=15-c.length;o>0;)s=l>>8,h[f]=a(e[o],s),--o,s=255&l,h[--f]=a(c[f],s),--f,--o;return h}}(),di=function(e){var t=0,r=ui(e);return function(e){var a=function(e,t,r,a,n){var s,i;for(n||(n=t),a||(a=ui(e)),s=0;s!=t.length;++s)i=t[s],i=255&((i^=a[r])>>5|i<<3),n[s]=i,++r;return[n,r,a]}("",e,t,r);return t=a[1],a[0]}};function pi(e,t,r){var a=r||{};return a.Info=e.read_shift(2),e.l-=2,1===a.Info?a.Data=function(e){var t={},r=t.EncryptionVersionInfo=ai(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e):a.Data=function(e,t){var r={},a=r.EncryptionVersionInfo=ai(e,4);if(t-=4,2!=a.Minor)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=ii(e,n),t-=n,r.EncryptionVerifier=oi(e,t),r}(e,t),a}var mi=function(){function e(e,r){switch(r.type){case"base64":return t(m(e),r);case"binary":return t(e,r);case"buffer":return t(g&&Buffer.isBuffer(e)?e.toString("binary"):S(e),r);case"array":return t(We(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw new Error("RTF missing table");var n={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach((function(e,t){Array.isArray(r)&&(r[t]=[]);for(var a,s=/\\\w+\b/g,i=0,o=-1;a=s.exec(e);){if("\\cell"===a[0]){var c=e.slice(i,s.lastIndex-a[0].length);if(" "==c[0]&&(c=c.slice(1)),++o,c.length){var l={v:c,t:"s"};Array.isArray(r)?r[t][o]=l:r[na({r:t,c:o})]=l}}i=s.lastIndex}o>n.e.c&&(n.e.c=o)})),r["!ref"]=ia(n),r}return{to_workbook:function(t,r){return fa(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],a=oa(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){r.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)r.push("\\cellx"+(i+1));for(r.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var o=na({r:s,c:i});(t=n?(e[s]||[])[i]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(la(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function gi(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function vi(e,t){if(0===t)return e;var r,a,n=function(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),s=Math.min(t,r,a),i=n-s;if(0===i)return[0,0,t];var o,c=0,l=n+s;switch(o=i/(l>1?2-l:l),n){case t:c=((r-a)/i+6)%6;break;case r:c=(a-t)/i+2;break;case a:c=(t-r)/i+4}return[c/6,o,l/2]}((a=(r=e).slice("#"===r[0]?1:0).slice(0,6),[parseInt(a.slice(0,2),16),parseInt(a.slice(2,4),16),parseInt(a.slice(4,6),16)]));return n[2]=t<0?n[2]*(1+t):1-(1-n[2])*(1-t),gi(function(e){var t,r=e[0],a=e[1],n=e[2],s=2*a*(n<.5?n:1-n),i=n-s/2,o=[i,i,i],c=6*r;if(0!==a)switch(0|c){case 0:case 6:t=s*c,o[0]+=s,o[1]+=t;break;case 1:t=s*(2-c),o[0]+=t,o[1]+=s;break;case 2:t=s*(c-2),o[1]+=s,o[2]+=t;break;case 3:t=s*(4-c),o[1]+=t,o[2]+=s;break;case 4:t=s*(c-4),o[2]+=s,o[0]+=t;break;case 5:t=s*(6-c),o[2]+=t,o[0]+=s}for(var l=0;3!=l;++l)o[l]=Math.round(255*o[l]);return o}(n))}var bi=6;function wi(e){return Math.floor((e+Math.round(128/bi)/256)*bi)}function Ti(e){return Math.floor((e-5)/bi*100+.5)/100}function Ei(e){return Math.round((e*bi+5)/bi*256)/256}function Si(e){return Ei(Ti(wi(e)))}function yi(e){var t=Math.abs(e-Si(e)),r=bi;if(t>.005)for(bi=1;bi<15;++bi)Math.abs(e-Si(e))<=t&&(t=Math.abs(e-Si(e)),r=bi);bi=r}function ki(e){e.width?(e.wpx=wi(e.width),e.wch=Ti(e.wpx),e.MDW=bi):e.wpx?(e.wch=Ti(e.wpx),e.width=Ei(e.wch),e.MDW=bi):"number"==typeof e.wch&&(e.width=Ei(e.wch),e.wpx=wi(e.width),e.MDW=bi),e.customWidth&&delete e.customWidth}var _i=96;function Ai(e){return 96*e/_i}function xi(e){return e*_i/96}var Ci={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};var Oi=["numFmtId","fillId","fontId","borderId","xfId"],Ri=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];var Ii=function(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,t=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,r=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,n=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,s=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(i,o,c){var l,f={};return i?((l=(i=i.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(e))&&function(e,t,r){t.NumberFmt=[];for(var a=_e(L),n=0;n<a.length;++n)t.NumberFmt[a[n]]=L[a[n]];var s=e[0].match(ct);if(s)for(n=0;n<s.length;++n){var i=ht(s[n]);switch(ut(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var o=mt(Ct(i.formatCode)),c=parseInt(i.numFmtId,10);if(t.NumberFmt[c]=o,c>0){if(c>392){for(c=392;c>60&&null!=t.NumberFmt[c];--c);t.NumberFmt[c]=o}ge(o,c)}break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}(l,f,c),(l=i.match(n))&&function(e,t,r,n){t.Fonts=[];var s={},i=!1;(e[0].match(ct)||[]).forEach((function(e){var o=ht(e);switch(ut(o[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":t.Fonts.push(s),s={};break;case"<name":o.val&&(s.name=Ct(o.val));break;case"<b":s.bold=o.val?yt(o.val):1;break;case"<b/>":s.bold=1;break;case"<i":s.italic=o.val?yt(o.val):1;break;case"<i/>":s.italic=1;break;case"<u":switch(o.val){case"none":s.underline=0;break;case"single":s.underline=1;break;case"double":s.underline=2;break;case"singleAccounting":s.underline=33;break;case"doubleAccounting":s.underline=34}break;case"<u/>":s.underline=1;break;case"<strike":s.strike=o.val?yt(o.val):1;break;case"<strike/>":s.strike=1;break;case"<outline":s.outline=o.val?yt(o.val):1;break;case"<outline/>":s.outline=1;break;case"<shadow":s.shadow=o.val?yt(o.val):1;break;case"<shadow/>":s.shadow=1;break;case"<condense":s.condense=o.val?yt(o.val):1;break;case"<condense/>":s.condense=1;break;case"<extend":s.extend=o.val?yt(o.val):1;break;case"<extend/>":s.extend=1;break;case"<sz":o.val&&(s.sz=+o.val);break;case"<vertAlign":o.val&&(s.vertAlign=o.val);break;case"<family":o.val&&(s.family=parseInt(o.val,10));break;case"<scheme":o.val&&(s.scheme=o.val);break;case"<charset":if("1"==o.val)break;o.codepage=a[parseInt(o.val,10)];break;case"<color":if(s.color||(s.color={}),o.auto&&(s.color.auto=yt(o.auto)),o.rgb)s.color.rgb=o.rgb.slice(-6);else if(o.indexed){s.color.index=parseInt(o.indexed,10);var c=$a[s.color.index];81==s.color.index&&(c=$a[1]),c||(c=$a[1]),s.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else o.theme&&(s.color.theme=parseInt(o.theme,10),o.tint&&(s.color.tint=parseFloat(o.tint)),o.theme&&r.themeElements&&r.themeElements.clrScheme&&(s.color.rgb=vi(r.themeElements.clrScheme[s.color.theme].rgb,s.color.tint||0)));break;case"<AlternateContent":case"<ext":i=!0;break;case"</AlternateContent>":case"</ext>":i=!1;break;default:if(n&&n.WTF&&!i)throw new Error("unrecognized "+o[0]+" in fonts")}}))}(l,f,o,c),(l=i.match(r))&&function(e,t,r,a){t.Fills=[];var n={},s=!1;(e[0].match(ct)||[]).forEach((function(e){var r=ht(e);switch(ut(r[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":n={},t.Fills.push(n);break;case"<gradientFill":case"</gradientFill>":t.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":r.patternType&&(n.patternType=r.patternType);break;case"<bgColor":n.bgColor||(n.bgColor={}),r.indexed&&(n.bgColor.indexed=parseInt(r.indexed,10)),r.theme&&(n.bgColor.theme=parseInt(r.theme,10)),r.tint&&(n.bgColor.tint=parseFloat(r.tint)),r.rgb&&(n.bgColor.rgb=r.rgb.slice(-6));break;case"<fgColor":n.fgColor||(n.fgColor={}),r.theme&&(n.fgColor.theme=parseInt(r.theme,10)),r.tint&&(n.fgColor.tint=parseFloat(r.tint)),null!=r.rgb&&(n.fgColor.rgb=r.rgb.slice(-6));break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+r[0]+" in fills")}}))}(l,f,0,c),(l=i.match(s))&&function(e,t,r,a){t.Borders=[];var n={},s=!1;(e[0].match(ct)||[]).forEach((function(e){var r=ht(e);switch(ut(r[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":n={},r.diagonalUp&&(n.diagonalUp=yt(r.diagonalUp)),r.diagonalDown&&(n.diagonalDown=yt(r.diagonalDown)),t.Borders.push(n);break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+r[0]+" in borders")}}))}(l,f,0,c),(l=i.match(t))&&function(e,t,r){var a;t.CellXf=[];var n=!1;(e[0].match(ct)||[]).forEach((function(e){var s=ht(e),i=0;switch(ut(s[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":for(delete(a=s)[0],i=0;i<Oi.length;++i)a[Oi[i]]&&(a[Oi[i]]=parseInt(a[Oi[i]],10));for(i=0;i<Ri.length;++i)a[Ri[i]]&&(a[Ri[i]]=yt(a[Ri[i]]));if(t.NumberFmt&&a.numFmtId>392)for(i=392;i>60;--i)if(t.NumberFmt[a.numFmtId]==t.NumberFmt[i]){a.numFmtId=i;break}t.CellXf.push(a);break;case"<alignment":case"<alignment/>":var o={};s.vertical&&(o.vertical=s.vertical),s.horizontal&&(o.horizontal=s.horizontal),null!=s.textRotation&&(o.textRotation=s.textRotation),s.indent&&(o.indent=s.indent),s.wrapText&&(o.wrapText=yt(s.wrapText)),a.alignment=o;break;case"<AlternateContent":case"<ext":n=!0;break;case"</AlternateContent>":case"</ext>":n=!1;break;default:if(r&&r.WTF&&!n)throw new Error("unrecognized "+s[0]+" in cellXfs")}}))}(l,f,c),f):f}}();function Ni(e,t){var r,a=[st,Bt("styleSheet",null,{xmlns:rr[0],"xmlns:vt":Qt})];return e.SSF&&null!=(r=function(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var a=r[0];a<=r[1];++a)null!=e[a]&&(t[t.length]=Bt("numFmt",null,{numFmtId:a,formatCode:bt(e[a])}))})),1===t.length?"":(t[t.length]="</numFmts>",t[0]=Bt("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(e.SSF))&&(a[a.length]=r),a[a.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',a[a.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',a[a.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',a[a.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(r=function(e){var t=[];return t[t.length]=Bt("cellXfs",null),e.forEach((function(e){t[t.length]=Bt("xf",null,e)})),t[t.length]="</cellXfs>",2===t.length?"":(t[0]=Bt("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(t.cellXfs))&&(a[a.length]=r),a[a.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',a[a.length]='<dxfs count="0"/>',a[a.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',a.length>2&&(a[a.length]="</styleSheet>",a[1]=a[1].replace("/>",">")),a.join("")}function Di(e,t,r){r||(r=jr(6+4*t.length)),r.write_shift(2,e),ma(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),a}function Fi(e,t){t||(t=jr(153)),t.write_shift(2,20*e.sz),function(e,t){t||(t=jr(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);t.write_shift(1,r),t.write_shift(1,0)}(e,t),t.write_shift(2,e.bold?700:400);var r=0;"superscript"==e.vertAlign?r=1:"subscript"==e.vertAlign&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Ua(e.color,t);var a=0;return"major"==e.scheme&&(a=1),"minor"==e.scheme&&(a=2),t.write_shift(1,a),ma(e.name,t),t.length>t.l?t.slice(0,t.l):t}var Pi,Li=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Mi=Gr;function Ui(e,t){t||(t=jr(84)),Pi||(Pi=xe(Li));var r=Pi[e.patternType];null==r&&(r=40),t.write_shift(4,r);var a=0;if(40!=r)for(Ua({auto:1},t),Ua({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function Bi(e,t,r){r||(r=jr(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);return r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function Wi(e,t){return t||(t=jr(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var Hi=Gr;function Vi(e){var t;Yr(e,613,da(1)),Yr(e,46,(t||(t=jr(51)),t.write_shift(1,0),Wi(0,t),Wi(0,t),Wi(0,t),Wi(0,t),Wi(0,t),t.length>t.l?t.slice(0,t.l):t)),Yr(e,614)}function zi(e){var t,r;Yr(e,619,da(1)),Yr(e,48,(t={xfId:0,builtinId:0,name:"Normal"},r||(r=jr(52)),r.write_shift(4,t.xfId),r.write_shift(2,1),r.write_shift(1,+t.builtinId),r.write_shift(1,0),xa(t.name||"",r),r.length>r.l?r.slice(0,r.l):r)),Yr(e,620)}function Gi(e){Yr(e,508,function(e,t,r){var a=jr(2052);return a.write_shift(4,e),xa(t,a),xa(r,a),a.length>a.l?a.slice(0,a.l):a}(0,"TableStyleMedium9","PivotStyleMedium4")),Yr(e,509)}function ji(e,t){var r=$r();return Yr(r,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach((function(e){for(var a=e[0];a<=e[1];++a)null!=t[a]&&++r})),0!=r&&(Yr(e,615,da(r)),[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var a=r[0];a<=r[1];++a)null!=t[a]&&Yr(e,44,Di(a,t[a]))})),Yr(e,616))}}(r,e.SSF),function(e){Yr(e,611,da(1)),Yr(e,43,Fi({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),Yr(e,612)}(r),function(e){Yr(e,603,da(2)),Yr(e,45,Ui({patternType:"none"})),Yr(e,45,Ui({patternType:"gray125"})),Yr(e,604)}(r),Vi(r),function(e){Yr(e,626,da(1)),Yr(e,47,Bi({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),Yr(e,627)}(r),function(e,t){Yr(e,617,da(t.length)),t.forEach((function(t){Yr(e,47,Bi(t,0))})),Yr(e,618)}(r,t.cellXfs),zi(r),function(e){Yr(e,505,da(0)),Yr(e,506)}(r),Gi(r),Yr(r,279),r.end()}var Xi=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function $i(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(ct)||[]).forEach((function(e){var n=ht(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(t.themeElements.clrScheme[Xi.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+n[0]+" in clrScheme")}}))}function Yi(){}function Ki(){}var Ji=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,qi=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,Zi=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;var Qi=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function eo(e,t){var r;e&&0!==e.length||(e=to());var a={};if(!(r=e.match(Qi)))throw new Error("themeElements not found in theme");return function(e,t,r){var a;t.themeElements={},[["clrScheme",Ji,$i],["fontScheme",qi,Yi],["fmtScheme",Zi,Ki]].forEach((function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,t,r)}))}(r[0],a,t),a.raw=e,a}function to(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[st];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function ro(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:case 4:e.l+=4;break;case 1:t.xclrValue=function(e,t){return Gr(e,t)}(e,4);break;case 2:t.xclrValue=ts(e);break;case 3:t.xclrValue=function(e){return e.read_shift(4)}(e)}return e.l+=8,t}function ao(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=ro(e);break;case 6:a[1]=function(e,t){return Gr(e,t)}(e,r);break;case 14:case 15:a[1]=e.read_shift(1===r?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return a}function no(){var e,t,r=$r();return Yr(r,332),Yr(r,334,da(1)),Yr(r,335,((t=jr(12+2*(e={name:"XLDAPR",version:12e4,flags:3496657072}).name.length)).write_shift(4,e.flags),t.write_shift(4,e.version),ma(e.name,t),t.slice(0,t.l))),Yr(r,336),Yr(r,339,function(e,t){var r=jr(8+2*t.length);return r.write_shift(4,e),ma(t,r),r.slice(0,r.l)}(1,"XLDAPR")),Yr(r,52),Yr(r,35,da(514)),Yr(r,4096,da(0)),Yr(r,4097,Hn(1)),Yr(r,36),Yr(r,53),Yr(r,340),Yr(r,337,function(e,t){var r=jr(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}(1,!0)),Yr(r,51,function(e){var t=jr(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),Yr(r,338),Yr(r,333),r.end()}function so(){var e=[st];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var io=1024;function oo(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[Bt("xml",null,{"xmlns:v":cr,"xmlns:o":ar,"xmlns:x":nr,"xmlns:mv":or}).replace(/\/>/,">"),Bt("o:shapelayout",Bt("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Bt("v:shapetype",[Bt("v:stroke",null,{joinstyle:"miter"}),Bt("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];io<1e3*e;)io+=1e3;return t.forEach((function(e){var t=aa(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var a="gradient"==r.type?Bt("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,s=Bt("v:fill",a,r);++io,n=n.concat(["<v:shape"+Ut({id:"_x0000_s"+io,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",s,Bt("v:shadow",null,{on:"t",obscured:"t"}),Bt("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Mt("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),Mt("x:AutoFill","False"),Mt("x:Row",String(t.r)),Mt("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])})),n.push("</xml>"),n.join("")}function co(e,t,r,a){var n,s=Array.isArray(e);t.forEach((function(t){var i=aa(t.ref);if(s?(e[i.r]||(e[i.r]=[]),n=e[i.r][i.c]):n=e[t.ref],!n){n={t:"z"},s?e[i.r][i.c]=n:e[t.ref]=n;var o=oa(e["!ref"]||"BDWGO1000001:A1");o.s.r>i.r&&(o.s.r=i.r),o.e.r<i.r&&(o.e.r=i.r),o.s.c>i.c&&(o.s.c=i.c),o.e.c<i.c&&(o.e.c=i.c);var c=ia(o);c!==e["!ref"]&&(e["!ref"]=c)}n.c||(n.c=[]);var l={a:t.author,t:t.t,r:t.r,T:r};t.h&&(l.h=t.h);for(var f=n.c.length-1;f>=0;--f){if(!r&&n.c[f].T)return;r&&!n.c[f].T&&n.c.splice(f,1)}if(r&&a)for(f=0;f<a.length;++f)if(l.a==a[f].id){l.a=a[f].name||l.a;break}n.c.push(l)}))}function lo(e){var t=[st,Bt("comments",null,{xmlns:rr[0]})],r=[];return t.push("<authors>"),e.forEach((function(e){e[1].forEach((function(e){var a=bt(e.a);-1==r.indexOf(a)&&(r.push(a),t.push("<author>"+a+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))}))})),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach((function(e){var a=0,n=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?a=r.indexOf("tc="+e[1][0].ID):e[1].forEach((function(e){e.a&&(a=r.indexOf(bt(e.a))),n.push(e.t||"")})),t.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>'),n.length<=1)t.push(Mt("t",bt(n[0]||"")));else{for(var s="Comment:\n    "+n[0]+"\n",i=1;i<n.length;++i)s+="Reply:\n    "+n[i]+"\n";t.push(Mt("t",bt(s)))}t.push("</text></comment>")})),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function fo(e,t,r){var a=[st,Bt("ThreadedComments",null,{xmlns:Yt}).replace(/[\/]>/,">")];return e.forEach((function(e){var n="";(e[1]||[]).forEach((function(s,i){if(s.T){s.a&&-1==t.indexOf(s.a)&&t.push(s.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==i?n=o.id:o.parentId=n,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),a.push(Bt("threadedComment",Mt("text",s.t||""),o))}else delete s.ID}))})),a.push("</ThreadedComments>"),a.join("")}var ho=pa;function uo(e){var t=$r(),r=[];return Yr(t,628),Yr(t,630),e.forEach((function(e){e[1].forEach((function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),Yr(t,632,function(e){return ma(e.slice(0,54))}(e.a)))}))})),Yr(t,631),Yr(t,633),e.forEach((function(e){e[1].forEach((function(a){a.iauthor=r.indexOf(a.a);var n={s:aa(e[0]),e:aa(e[0])};Yr(t,635,function(e,t){return null==t&&(t=jr(36)),t.write_shift(4,e[1].iauthor),Pa(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}([n,a])),a.t&&a.t.length>0&&Yr(t,637,wa(a)),Yr(t,636),delete a.iauthor}))})),Yr(t,634),Yr(t,629),t.end()}var po=["xlsb","xlsm","xlam","biff8","xla"];var mo=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var o=a.length>0?0|parseInt(a,10):0,c=n.length>0?0|parseInt(n,10):0;return s?c+=t.c:--c,i?o+=t.r:--o,r+(s?"":"$")+ra(c)+(i?"":"$")+ea(o)}return function(a,n){return t=n,a.replace(e,r)}}(),go=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,vo=function(){return function(e,t){return e.replace(go,(function(e,r,a,n,s,i){var o=ta(n)-(a?0:t.c),c=Qr(i)-(s?0:t.r);return r+"R"+(0==c?"":s?c+1:"["+c+"]")+"C"+(0==o?"":a?o+1:"["+o+"]")}))}}();function bo(e,t){return e.replace(go,(function(e,r,a,n,s,i){return r+("$"==a?a+n:ra(ta(n)+t.c))+("$"==s?s+i:ea(Qr(i)+t.r))}))}function wo(e,t,r){var a=sa(t).s,n=aa(r);return bo(e,{r:n.r-a.r,c:n.c-a.c})}function To(e){return e.replace(/_xlfn\./g,"")}function Eo(e){e.l+=1}function So(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function yo(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return ko(e);12==r.biff&&(a=4)}var n=e.read_shift(a),s=e.read_shift(a),i=So(e,2),o=So(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function ko(e){var t=So(e,2),r=So(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function _o(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return function(e){var t=So(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}(e);var a=e.read_shift(r&&12==r.biff?4:2),n=So(e,2);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function Ao(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:255&r,fQuoted:!!(16384&r),cRel:r>>15,rRel:r>>15}}function xo(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function Co(e){return[e.read_shift(1),e.read_shift(1)]}function Oo(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=Un(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=Ya[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=La(e);break;case 2:r[1]=Kn(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Ro(e,t,r){for(var a=e.read_shift(12==r.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==r.biff?Fa:os)(e));return n}function Io(e,t,r){var a=0,n=0;12==r.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var o=0;o!=n;++o)i[s][o]=Oo(e,r.biff);return i}function No(e,t,r){return e.l+=2,[Ao(e)]}function Do(e){return e.l+=6,[]}function Fo(e){return e.l+=2,[Wn(e),1&e.read_shift(2)]}var Po=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];var Lo={1:{n:"PtgExp",f:function(e,t,r){return e.l++,r&&12==r.biff?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:Gr},3:{n:"PtgAdd",f:Eo},4:{n:"PtgSub",f:Eo},5:{n:"PtgMul",f:Eo},6:{n:"PtgDiv",f:Eo},7:{n:"PtgPower",f:Eo},8:{n:"PtgConcat",f:Eo},9:{n:"PtgLt",f:Eo},10:{n:"PtgLe",f:Eo},11:{n:"PtgEq",f:Eo},12:{n:"PtgGe",f:Eo},13:{n:"PtgGt",f:Eo},14:{n:"PtgNe",f:Eo},15:{n:"PtgIsect",f:Eo},16:{n:"PtgUnion",f:Eo},17:{n:"PtgRange",f:Eo},18:{n:"PtgUplus",f:Eo},19:{n:"PtgUminus",f:Eo},20:{n:"PtgPercent",f:Eo},21:{n:"PtgParen",f:Eo},22:{n:"PtgMissArg",f:Eo},23:{n:"PtgStr",f:function(e,t,r){return e.l++,Gn(e,0,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,Ya[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,La(e)}},32:{n:"PtgArray",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[nc[n],ac[n],a]}},34:{n:"PtgFuncVar",f:function(e,t,r){var a=e[e.l++],n=e.read_shift(1),s=r&&r.biff<=3?[88==a?-1:0,e.read_shift(1)]:function(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}(e);return[n,(0===s[0]?ac:rc)[s[1]]]}},35:{n:"PtgName",f:function(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,_o(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,yo(e,r.biff>=2&&r.biff,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[a,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:Gr},40:{n:"PtgMemNoMem",f:Gr},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=function(e,t,r){var a=r&&r.biff?r.biff:8;if(a>=2&&a<=5)return function(e){var t=e.read_shift(2),r=e.read_shift(1),a=(32768&t)>>15,n=(16384&t)>>14;return t&=16383,1==a&&t>=8192&&(t-=16384),1==n&&r>=128&&(r-=256),{r:t,c:r,cRel:n,rRel:a}}(e);var n=e.read_shift(a>=12?4:2),s=e.read_shift(2),i=(16384&s)>>14,o=(32768&s)>>15;if(s&=16383,1==o)for(;n>524287;)n-=1048576;if(1==i)for(;s>8191;)s-=16384;return{r:n,c:s,cRel:i,rRel:o}}(e,0,r);return[a,n]}},45:{n:"PtgAreaN",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=function(e,t,r){if(r.biff<8)return ko(e);var a=e.read_shift(12==r.biff?4:2),n=e.read_shift(12==r.biff?4:2),s=So(e,2),i=So(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,0,r);return[a,n]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){return 5==r.biff?function(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[t,r,a]}(e):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[a,n,_o(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i");if(r)switch(r.biff){case 5:e.l+=12;break;case 12:0}return[a,n,yo(e,0,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},Mo={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Uo={1:{n:"PtgElfLel",f:Fo},2:{n:"PtgElfRw",f:No},3:{n:"PtgElfCol",f:No},6:{n:"PtgElfRwV",f:No},7:{n:"PtgElfColV",f:No},10:{n:"PtgElfRadical",f:No},11:{n:"PtgElfRadicalS",f:Do},13:{n:"PtgElfColS",f:Do},15:{n:"PtgElfColSV",f:Do},16:{n:"PtgElfRadicalLel",f:Fo},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2);return{ixti:t,coltype:3&r,rt:Po[r>>2&31],idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},Bo={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var a=e.read_shift(r&&2==r.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&2==r.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:xo},33:{n:"PtgAttrBaxcel",f:xo},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),Co(e)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),Co(e)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function Wo(e,t,r,a){if(a.biff<8)return Gr(e,t);for(var n=e.l+t,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=Io(e,0,a),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=Ro(e,r[i][1],a),s.push(r[i][2]);break;case"PtgExp":a&&12==a.biff&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return 0!==(t=n-e.l)&&s.push(Gr(e,t)),s}function Ho(e,t,r){for(var a,n,s=e.l+t,i=[];s!=e.l;)t=s-e.l,n=e[e.l],a=Lo[n]||Lo[Mo[n]],24!==n&&25!==n||(a=(24===n?Uo:Bo)[e[e.l+1]]),a&&a.f?i.push([a.n,a.f(e,t,r)]):Gr(e,t);return i}function Vo(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];if(i)if(2===i[0])n.push('"'+i[1].replace(/"/g,'""')+'"');else n.push(i[1]);else n.push("")}t.push(n.join(","))}return t.join(";")}var zo={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Go(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[a[0]][0][3]?(n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function jo(e,t,r){var a=Go(e,t,r);return"#REF"==a?a:function(e,t){if(!(e||t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(a,r)}function Xo(e,t,r,a,n){var s,i,o,c,l=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,b=e[0].length;v<b;++v){var w=e[0][v];switch(w[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=Ve(" ",e[0][m][1][1]);break;case 1:g=Ve("\r",e[0][m][1][1]);break;default:if(g="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=g,m=-1}h.push(i+zo[w[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=Kr(w[1][1],f,n),h.push(qr(o,l));break;case"PtgRefN":o=r?Kr(w[1][1],r,n):w[1][1],h.push(qr(o,l));break;case"PtgRef3d":u=w[1][1],o=Kr(w[1][2],f,n),p=jo(a,u,n),h.push(p+"!"+qr(o,l));break;case"PtgFunc":case"PtgFuncVar":var T=w[1][0],E=w[1][1];T||(T=0);var S=0==(T&=127)?[]:h.slice(-T);h.length-=T,"User"===E&&(E=S.shift()),h.push(E+"("+S.join(",")+")");break;case"PtgBool":h.push(w[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(w[1]);break;case"PtgNum":h.push(String(w[1]));break;case"PtgStr":h.push('"'+w[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":c=Jr(w[1][1],r?{s:r}:f,n),h.push(Zr(c,n));break;case"PtgArea":c=Jr(w[1][1],f,n),h.push(Zr(c,n));break;case"PtgArea3d":u=w[1][1],c=w[1][2],p=jo(a,u,n),h.push(p+"!"+Zr(c,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=w[1][2];var y=(a.names||[])[d-1]||(a[0]||[])[d],k=y?y.Name:"SH33TJSNAME"+String(d);k&&"_xlfn."==k.slice(0,6)&&!n.xlfn&&(k=k.slice(6)),h.push(k);break;case"PtgNameX":var _,A=w[1][1];if(d=w[1][2],!(n.biff<=5)){var x="";if(14849==((a[A]||[])[0]||[])[0]||(1025==((a[A]||[])[0]||[])[0]?a[A][d]&&a[A][d].itab>0&&(x=a.SheetNames[a[A][d].itab-1]+"!"):x=a.SheetNames[d-1]+"!"),a[A]&&a[A][d])x+=a[A][d].Name;else if(a[0]&&a[0][d])x+=a[0][d].Name;else{var C=(Go(a,A,n)||"").split(";;");C[d-1]?x=C[d-1]:x+="SH33TJSERRX"}h.push(x);break}A<0&&(A=-A),a[A]&&(_=a[A][d]),_||(_={Name:"SH33TJSERRY"}),h.push(_.Name);break;case"PtgParen":var O="(",R=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:O=Ve(" ",e[0][m][1][1])+O;break;case 3:O=Ve("\r",e[0][m][1][1])+O;break;case 4:R=Ve(" ",e[0][m][1][1])+R;break;case 5:R=Ve("\r",e[0][m][1][1])+R;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(O+h.pop()+R);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":o={c:w[1][1],r:w[1][0]};var I={c:r.c,r:r.r};if(a.sharedf[na(o)]){var N=a.sharedf[na(o)];h.push(Xo(N,f,I,a,n))}else{var D=!1;for(s=0;s!=a.arrayf.length;++s)if(i=a.arrayf[s],!(o.c<i[0].s.c||o.c>i[0].e.c||o.r<i[0].s.r||o.r>i[0].e.r)){h.push(Xo(i[1],f,I,a,n)),D=!0;break}D||h.push(w[1])}break;case"PtgArray":h.push("{"+Vo(w[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+w[1].idx+"[#"+w[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");default:throw new Error("Unrecognized Formula Token: "+String(w))}if(3!=n.biff&&m>=0&&-1==["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"].indexOf(e[0][v][0])){var F=!0;switch((w=e[0][m])[1][0]){case 4:F=!1;case 0:g=Ve(" ",w[1][1]);break;case 5:F=!1;case 1:g=Ve("\r",w[1][1]);break;default:if(g="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+w[1][0])}h.push((F?g:"")+h.pop()+(F?"":g)),m=-1}}if(h.length>1&&n.WTF)throw new Error("bad formula stack");return h[0]}function $o(e,t,r){var a,n=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],Gr(e,t-2)];var o=Ho(e,i,r);return t!==i+s&&(a=Wo(e,t-i-s,o,r)),e.l=n,[o,a]}function Yo(e,t,r){var a,n=e.l+t,s=e.read_shift(2),i=Ho(e,s,r);return 65535==s?[[],Gr(e,t-2)]:(t!==s+2&&(a=Wo(e,n-s-2,i,r)),[i,a])}function Ko(e,t,r){var a=e.l+t,n=as(e);2==r.biff&&++e.l;var s=function(e){var t;if(65535!==Nr(e,e.l+6))return[La(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e),i=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var o=function(e,t,r){var a,n=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],Gr(e,t-2)];var o=Ho(e,i,r);return t!==i+s&&(a=Wo(e,t-i-s,o,r)),e.l=n,[o,a]}(e,a-e.l,r);return{cell:n,val:s[0],formula:o,shared:i>>3&1,tt:s[1]}}function Jo(e,t,r,a,n){var s=ns(t,r,n),i=function(e){if(null==e){var t=jr(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return Ma("number"==typeof e?e:0)}(e.v),o=jr(6);o.write_shift(2,33),o.write_shift(4,0);for(var c=jr(e.bf.length),l=0;l<e.bf.length;++l)c[l]=e.bf[l];return k([s,i,o,c])}function qo(e,t,r){var a=e.read_shift(4),n=Ho(e,a,r),s=e.read_shift(4);return[n,s>0?Wo(e,s,n,r):null]}var Zo=qo,Qo=qo,ec=qo,tc=qo,rc={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},ac={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},nc={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function sc(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,(function(e,t){return t.replace(/\./g,"")}))).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function ic(e){var t=e.split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var oc={},cc={},lc="undefined"!=typeof Map;function fc(e,t,r){var a=0,n=e.length;if(r){if(lc?r.has(t):Object.prototype.hasOwnProperty.call(r,t))for(var s=lc?r.get(t):r[t];a<s.length;++a)if(e[s[a]].t===t)return e.Count++,s[a]}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t:t},e.Count++,e.Unique++,r&&(lc?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function hc(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(bi=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?a=Ti(t.wpx):null!=t.wch&&(a=t.wch),a>-1?(r.width=Ei(a),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function uc(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function dc(e,t,r){var a=r.revssf[null!=t.z?t.z:"General"],n=60,s=e.length;if(null==a&&r.ssf)for(;n<392;++n)if(null==r.ssf[n]){ge(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}for(n=0;n!=s;++n)if(e[n].numFmtId===a)return n;return e[s]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}function pc(e,t,r,a,n,s){try{a.cellNF&&(e.z=L[t])}catch(o){if(a.WTF)throw o}if("z"!==e.t||a.cellStyles){if("d"===e.t&&"string"==typeof e.v&&(e.v=Be(e.v)),(!a||!1!==a.cellText)&&"z"!==e.t)try{if(null==L[t]&&ge(we[t]||"General",t),"e"===e.t)e.w=e.w||Ya[e.v];else if(0===t)if("n"===e.t)(0|e.v)===e.v?e.w=e.v.toString(10):e.w=X(e.v);else if("d"===e.t){var i=Re(e.v);e.w=(0|i)===i?i.toString(10):X(i)}else{if(void 0===e.v)return"";e.w=Y(e.v,cc)}else"d"===e.t?e.w=me(t,Re(e.v),cc):e.w=me(t,e.v,cc)}catch(o){if(a.WTF)throw o}if(a.cellStyles&&null!=r)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=vi(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=vi(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(o){if(a.WTF&&s.Fills)throw o}}}function mc(e,t,r){if(e&&e["!ref"]){var a=oa(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}var gc=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,vc=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,bc=/<(?:\w:)?hyperlink [^>]*>/gm,wc=/"(\w*:\w*)"/,Tc=/<(?:\w:)?col\b[^>]*[\/]?>/g,Ec=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,Sc=/<(?:\w:)?pageMargins[^>]*\/>/g,yc=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,kc=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,_c=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Ac(e,t,r,a,n,s,i){if(!e)return e;a||(a={"!id":{}});var o=t.dense?[]:{},c={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",h=e.match(vc);h?(l=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):l=f=e;var u=l.match(yc);u?xc(u[0],o,n,r):(u=l.match(kc))&&function(e,t,r,a,n){xc(e.slice(0,e.indexOf(">")),r,a,n)}(u[0],u[1],o,n,r);var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p=l.slice(d,d+50).match(wc);p&&function(e,t){var r=oa(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=ia(r))}(o,p[1])}var m=l.match(_c);m&&m[1]&&function(e,t){t.Views||(t.Views=[{}]);(e.match(Rc)||[]).forEach((function(e,r){var a=ht(e);t.Views[r]||(t.Views[r]={}),+a.zoomScale&&(t.Views[r].zoom=+a.zoomScale),yt(a.rightToLeft)&&(t.Views[r].RTL=!0)}))}(m[1],n);var g=[];if(t.cellStyles){var v=l.match(Tc);v&&function(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=ht(t[a],!0);n.hidden&&(n.hidden=yt(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,yi(n.width)),ki(n);s<=i;)e[s++]=He(n)}}(g,v)}h&&Nc(h[1],o,t,c,s,i);var b=f.match(Ec);b&&(o["!autofilter"]=function(e){var t={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return t}(b[0]));var w=[],T=f.match(gc);if(T)for(d=0;d!=T.length;++d)w[d]=oa(T[d].slice(T[d].indexOf('"')+1));var E=f.match(bc);E&&function(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var s=ht(Ct(t[n]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+mt(s.location))):(s.Target="#"+mt(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var o=oa(s.ref),c=o.s.r;c<=o.e.r;++c)for(var l=o.s.c;l<=o.e.c;++l){var f=na({c:l,r:c});a?(e[c]||(e[c]=[]),e[c][l]||(e[c][l]={t:"z",v:void 0}),e[c][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}(o,E,a);var S,y,k=f.match(Sc);if(k&&(o["!margins"]=(S=ht(k[0]),y={},["left","right","top","bottom","header","footer"].forEach((function(e){S[e]&&(y[e]=parseFloat(S[e]))})),y)),!o["!ref"]&&c.e.c>=c.s.c&&c.e.r>=c.s.r&&(o["!ref"]=ia(c)),t.sheetRows>0&&o["!ref"]){var _=oa(o["!ref"]);t.sheetRows<=+_.e.r&&(_.e.r=t.sheetRows-1,_.e.r>c.e.r&&(_.e.r=c.e.r),_.e.r<_.s.r&&(_.s.r=_.e.r),_.e.c>c.e.c&&(_.e.c=c.e.c),_.e.c<_.s.c&&(_.s.c=_.e.c),o["!fullref"]=o["!ref"],o["!ref"]=ia(_))}return g.length>0&&(o["!cols"]=g),w.length>0&&(o["!merges"]=w),o}function xc(e,t,r,a){var n=ht(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=mt(Ct(n.codeName)))}var Cc=["objects","scenarios","selectLockedCells","selectUnlockedCells"],Oc=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];var Rc=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function Ic(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!=typeof e.f||"z"===e.t&&!e.f)return"";var n="",s=e.t,i=e.v;if("z"!==e.t)switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=Ya[e.v];break;case"d":a&&a.cellDates?n=Be(e.v,-1).toISOString():((e=He(e)).t="n",n=""+(e.v=Re(Be(e.v)))),void 0===e.z&&(e.z=L[14]);break;default:n=e.v}var o=Mt("v",bt(n)),c={r:t},l=dc(a.cellXfs,e,a);switch(0!==l&&(c.s=l),e.t){case"n":case"z":break;case"d":c.t="d";break;case"b":c.t="b";break;case"e":c.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){o=Mt("v",""+fc(a.Strings,e.v,a.revStrings)),c.t="s";break}c.t="str"}if(e.t!=s&&(e.t=s,e.v=i),"string"==typeof e.f&&e.f){var f=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=Bt("f",bt(e.f),f)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(c.cm=1),Bt("c",o,c)}var Nc=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=Rt("v"),i=Rt("f");return function(o,c,l,f,h,u){for(var d,p,m,g,v,b=0,w="",T=[],E=[],S=0,y=0,k=0,_="",A=0,x=0,C=0,O=0,R=Array.isArray(u.CellXf),I=[],N=[],D=Array.isArray(c),F=[],P={},M=!1,U=!!l.sheetStubs,B=o.split(t),W=0,H=B.length;W!=H;++W){var V=(w=B[W].trim()).length;if(0!==V){var z=0;e:for(b=0;b<V;++b)switch(w[b]){case">":if("/"!=w[b-1]){++b;break e}if(l&&l.cellStyles){if(A=null!=(p=ht(w.slice(z,b),!0)).r?parseInt(p.r,10):A+1,x=-1,l.sheetRows&&l.sheetRows<A)continue;P={},M=!1,p.ht&&(M=!0,P.hpt=parseFloat(p.ht),P.hpx=xi(P.hpt)),"1"==p.hidden&&(M=!0,P.hidden=!0),null!=p.outlineLevel&&(M=!0,P.level=+p.outlineLevel),M&&(F[A-1]=P)}break;case"<":z=b}if(z>=b)break;if(A=null!=(p=ht(w.slice(z,b),!0)).r?parseInt(p.r,10):A+1,x=-1,!(l.sheetRows&&l.sheetRows<A)){f.s.r>A-1&&(f.s.r=A-1),f.e.r<A-1&&(f.e.r=A-1),l&&l.cellStyles&&(P={},M=!1,p.ht&&(M=!0,P.hpt=parseFloat(p.ht),P.hpx=xi(P.hpt)),"1"==p.hidden&&(M=!0,P.hidden=!0),null!=p.outlineLevel&&(M=!0,P.level=+p.outlineLevel),M&&(F[A-1]=P)),T=w.slice(b).split(e);for(var G=0;G!=T.length&&"<"==T[G].trim().charAt(0);++G);for(T=T.slice(G),b=0;b!=T.length;++b)if(0!==(w=T[b].trim()).length){if(E=w.match(r),S=b,y=0,k=0,w="<c "+("<"==w.slice(0,1)?">":"")+w,null!=E&&2===E.length){for(S=0,_=E[1],y=0;y!=_.length&&!((k=_.charCodeAt(y)-64)<1||k>26);++y)S=26*S+k;x=--S}else++x;for(y=0;y!=w.length&&62!==w.charCodeAt(y);++y);if(++y,(p=ht(w.slice(0,y),!0)).r||(p.r=na({r:A-1,c:x})),d={t:""},null!=(E=(_=w.slice(y)).match(s))&&""!==E[1]&&(d.v=mt(E[1])),l.cellFormula){if(null!=(E=_.match(i))&&""!==E[1]){if(d.f=mt(Ct(E[1])).replace(/\r\n/g,"\n"),l.xlfn||(d.f=To(d.f)),E[0].indexOf('t="array"')>-1)d.F=(_.match(n)||[])[1],d.F.indexOf(":")>-1&&I.push([oa(d.F),d.F]);else if(E[0].indexOf('t="shared"')>-1){g=ht(E[0]);var j=mt(Ct(E[1]));l.xlfn||(j=To(j)),N[parseInt(g.si,10)]=[g,j,p.r]}}else(E=_.match(/<f[^>]*\/>/))&&N[(g=ht(E[0])).si]&&(d.f=wo(N[g.si][1],N[g.si][2],p.r));var X=aa(p.r);for(y=0;y<I.length;++y)X.r>=I[y][0].s.r&&X.r<=I[y][0].e.r&&X.c>=I[y][0].s.c&&X.c<=I[y][0].e.c&&(d.F=I[y][1])}if(null==p.t&&void 0===d.v)if(d.f||d.F)d.v=0,d.t="n";else{if(!U)continue;d.t="z"}else d.t=p.t||"n";switch(f.s.c>x&&(f.s.c=x),f.e.c<x&&(f.e.c=x),d.t){case"n":if(""==d.v||null==d.v){if(!U)continue;d.t="z"}else d.v=parseFloat(d.v);break;case"s":if(void 0===d.v){if(!U)continue;d.t="z"}else m=oc[parseInt(d.v,10)],d.v=m.t,d.r=m.r,l.cellHTML&&(d.h=m.h);break;case"str":d.t="s",d.v=null!=d.v?Ct(d.v):"",l.cellHTML&&(d.h=Et(d.v));break;case"inlineStr":E=_.match(a),d.t="s",null!=E&&(m=Ys(E[1]))?(d.v=m.t,l.cellHTML&&(d.h=m.h)):d.v="";break;case"b":d.v=yt(d.v);break;case"d":l.cellDates?d.v=Be(d.v,1):(d.v=Re(Be(d.v,1)),d.t="n");break;case"e":l&&!1===l.cellText||(d.w=d.v),d.v=Ka[d.v]}if(C=O=0,v=null,R&&void 0!==p.s&&null!=(v=u.CellXf[p.s])&&(null!=v.numFmtId&&(C=v.numFmtId),l.cellStyles&&null!=v.fillId&&(O=v.fillId)),pc(d,C,O,l,h,u),l.cellDates&&R&&"n"==d.t&&he(L[C])&&(d.t="d",d.v=Fe(d.v)),p.cm&&l.xlmeta){var $=(l.xlmeta.Cell||[])[+p.cm-1];$&&"XLDAPR"==$.type&&(d.D=!0)}if(D){var Y=aa(p.r);c[Y.r]||(c[Y.r]=[]),c[Y.r][Y.c]=d}else c[p.r]=d}}}}F.length>0&&(c["!rows"]=F)}}();function Dc(e,t,r,a){var n,s=[st,Bt("worksheet",null,{xmlns:rr[0],"xmlns:r":Zt})],i=r.SheetNames[e],o="",c=r.Sheets[i];null==c&&(c={});var l=c["!ref"]||"A1",f=oa(l);if(f.e.c>16383||f.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575),l=ia(f)}a||(a={}),c["!comments"]=[];var h=[];!function(e,t,r,a,n){var s=!1,i={},o=null;if("xlsx"!==a.bookType&&t.vbaraw){var c=t.SheetNames[r];try{t.Workbook&&(c=t.Workbook.Sheets[r].CodeName||c)}catch(f){}s=!0,i.codeName=Ot(bt(c))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),o=(o||"")+Bt("outlinePr",null,l)}(s||o)&&(n[n.length]=Bt("sheetPr",o,i))}(c,r,e,t,s),s[s.length]=Bt("dimension",null,{ref:l}),s[s.length]=function(e,t,r,a){var n={workbookViewId:"0"};return(((a||{}).Workbook||{}).Views||[])[0]&&(n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0"),Bt("sheetViews",Bt("sheetView",null,n),{})}(0,0,0,r),t.sheetFormat&&(s[s.length]=Bt("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=c["!cols"]&&c["!cols"].length>0&&(s[s.length]=function(e,t){for(var r,a=["<cols>"],n=0;n!=t.length;++n)(r=t[n])&&(a[a.length]=Bt("col",null,hc(n,r)));return a[a.length]="</cols>",a.join("")}(0,c["!cols"])),s[n=s.length]="<sheetData/>",c["!links"]=[],null!=c["!ref"]&&(o=function(e,t){var r,a,n=[],s=[],i=oa(e["!ref"]),o="",c="",l=[],f=0,h=0,u=e["!rows"],d=Array.isArray(e),p={r:c},m=-1;for(h=i.s.c;h<=i.e.c;++h)l[h]=ra(h);for(f=i.s.r;f<=i.e.r;++f){for(s=[],c=ea(f),h=i.s.c;h<=i.e.c;++h){r=l[h]+c;var g=d?(e[f]||[])[h]:e[r];void 0!==g&&null!=(o=Ic(g,r,e,t))&&s.push(o)}(s.length>0||u&&u[f])&&(p={r:c},u&&u[f]&&((a=u[f]).hidden&&(p.hidden=1),m=-1,a.hpx?m=Ai(a.hpx):a.hpt&&(m=a.hpt),m>-1&&(p.ht=m,p.customHeight=1),a.level&&(p.outlineLevel=a.level)),n[n.length]=Bt("row",s.join(""),p))}if(u)for(;f<u.length;++f)u&&u[f]&&(p={r:f+1},(a=u[f]).hidden&&(p.hidden=1),m=-1,a.hpx?m=Ai(a.hpx):a.hpt&&(m=a.hpt),m>-1&&(p.ht=m,p.customHeight=1),a.level&&(p.outlineLevel=a.level),n[n.length]=Bt("row","",p));return n.join("")}(c,t),o.length>0&&(s[s.length]=o)),s.length>n+1&&(s[s.length]="</sheetData>",s[n]=s[n].replace("/>",">")),c["!protect"]&&(s[s.length]=function(e){var t={sheet:1};return Cc.forEach((function(r){null!=e[r]&&e[r]&&(t[r]="1")})),Oc.forEach((function(r){null==e[r]||e[r]||(t[r]="0")})),e.password&&(t.password=hi(e.password).toString(16).toUpperCase()),Bt("sheetProtection",null,t)}(c["!protect"])),null!=c["!autofilter"]&&(s[s.length]=function(e,t,r,a){var n="string"==typeof e.ref?e.ref:ia(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,i=sa(n);i.s.r==i.e.r&&(i.e.r=sa(t["!ref"]).e.r,n=ia(i));for(var o=0;o<s.length;++o){var c=s[o];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==a){c.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),Bt("autoFilter",null,{ref:n})}(c["!autofilter"],c,r,e)),null!=c["!merges"]&&c["!merges"].length>0&&(s[s.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+ia(e[r])+'"/>';return t+"</mergeCells>"}(c["!merges"]));var u,d,p=-1,m=-1;return c["!links"].length>0&&(s[s.length]="<hyperlinks>",c["!links"].forEach((function(e){e[1].Target&&(u={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(m=an(a,-1,bt(e[1].Target).replace(/#.*$/,""),Qa.HLINK),u["r:id"]="rId"+m),(p=e[1].Target.indexOf("#"))>-1&&(u.location=bt(e[1].Target.slice(p+1))),e[1].Tooltip&&(u.tooltip=bt(e[1].Tooltip)),s[s.length]=Bt("hyperlink",null,u))})),s[s.length]="</hyperlinks>"),delete c["!links"],null!=c["!margins"]&&(s[s.length]=(uc(d=c["!margins"]),Bt("pageMargins",null,d))),t&&!t.ignoreEC&&null!=t.ignoreEC||(s[s.length]=Mt("ignoredErrors",Bt("ignoredError",null,{numberStoredAsText:1,sqref:l}))),h.length>0&&(m=an(a,-1,"../drawings/drawing"+(e+1)+".xml",Qa.DRAW),s[s.length]=Bt("drawing",null,{"r:id":"rId"+m}),c["!drawing"]=h),c["!comments"].length>0&&(m=an(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Qa.VML),s[s.length]=Bt("legacyDrawing",null,{"r:id":"rId"+m}),c["!legacy"]=m),s.length>1&&(s[s.length]="</worksheet>",s[1]=s[1].replace("/>",">")),s.join("")}function Fc(e,t,r,a){var n=function(e,t,r){var a=jr(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var s=320;n.hpx?s=20*Ai(n.hpx):n.hpt&&(s=20*n.hpt),a.write_shift(2,s),a.write_shift(1,0);var i=0;n.level&&(i|=n.level),n.hidden&&(i|=16),(n.hpx||n.hpt)&&(i|=32),a.write_shift(1,i),a.write_shift(1,0);var o=0,c=a.l;a.l+=4;for(var l={r:e,c:0},f=0;f<16;++f)if(!(t.s.c>f+1<<10||t.e.c<f<<10)){for(var h=-1,u=-1,d=f<<10;d<f+1<<10;++d)l.c=d,(Array.isArray(r)?(r[l.r]||[])[l.c]:r[na(l)])&&(h<0&&(h=d),u=d);h<0||(++o,a.write_shift(4,h),a.write_shift(4,u))}var p=a.l;return a.l=c,a.write_shift(4,o),a.l=p,a.length>a.l?a.slice(0,a.l):a}(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&Yr(e,0,n)}var Pc=Fa,Lc=Pa;function Mc(e){return[Sa(e),La(e),"n"]}var Uc=Fa,Bc=Pa;var Wc=["left","right","top","bottom","header","footer"];function Hc(e,t,r,a,n,s,i){if(void 0===t.v)return!1;var o="";switch(t.t){case"b":o=t.v?"1":"0";break;case"d":(t=He(t)).z=t.z||L[14],t.v=Re(Be(t.v)),t.t="n";break;case"n":case"e":o=""+t.v;break;default:o=t.v}var c={r:r,c:a};switch(c.s=dc(n.cellXfs,t,n),t.l&&s["!links"].push([na(c),t.l]),t.c&&s["!comments"].push([na(c),t.c]),t.t){case"s":case"str":return n.bookSST?(o=fc(n.Strings,t.v,n.revStrings),c.t="s",c.v=o,i?Yr(e,18,function(e,t,r){return null==r&&(r=jr(8)),ya(t,r),r.write_shift(4,t.v),r}(0,c)):Yr(e,7,function(e,t,r){return null==r&&(r=jr(12)),Ea(t,r),r.write_shift(4,t.v),r}(0,c))):(c.t="str",i?Yr(e,17,function(e,t,r){return null==r&&(r=jr(8+4*e.v.length)),ya(t,r),ma(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,c)):Yr(e,6,function(e,t,r){return null==r&&(r=jr(12+4*e.v.length)),Ea(t,r),ma(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,c))),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?i?Yr(e,13,function(e,t,r){return null==r&&(r=jr(8)),ya(t,r),Na(e.v,r),r}(t,c)):Yr(e,2,function(e,t,r){return null==r&&(r=jr(12)),Ea(t,r),Na(e.v,r),r}(t,c)):i?Yr(e,16,function(e,t,r){return null==r&&(r=jr(12)),ya(t,r),Ma(e.v,r),r}(t,c)):Yr(e,5,function(e,t,r){return null==r&&(r=jr(16)),Ea(t,r),Ma(e.v,r),r}(t,c)),!0;case"b":return c.t="b",i?Yr(e,15,function(e,t,r){return null==r&&(r=jr(5)),ya(t,r),r.write_shift(1,e.v?1:0),r}(t,c)):Yr(e,4,function(e,t,r){return null==r&&(r=jr(9)),Ea(t,r),r.write_shift(1,e.v?1:0),r}(t,c)),!0;case"e":return c.t="e",i?Yr(e,14,function(e,t,r){return null==r&&(r=jr(8)),ya(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}(t,c)):Yr(e,3,function(e,t,r){return null==r&&(r=jr(9)),Ea(t,r),r.write_shift(1,e.v),r}(t,c)),!0}return i?Yr(e,12,function(e,t,r){return null==r&&(r=jr(4)),ya(t,r)}(0,c)):Yr(e,1,function(e,t,r){return null==r&&(r=jr(8)),Ea(t,r)}(0,c)),!0}function Vc(e,t){var r,a;t&&t["!merges"]&&(Yr(e,177,(r=t["!merges"].length,null==a&&(a=jr(4)),a.write_shift(4,r),a)),t["!merges"].forEach((function(t){Yr(e,176,Bc(t))})),Yr(e,178))}function zc(e,t){t&&t["!cols"]&&(Yr(e,390),t["!cols"].forEach((function(t,r){t&&Yr(e,60,function(e,t,r){null==r&&(r=jr(18));var a=hc(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,256*(a.width||10)),r.write_shift(4,0);var n=0;return t.hidden&&(n|=1),"number"==typeof a.width&&(n|=2),t.level&&(n|=t.level<<8),r.write_shift(2,n),r}(r,t))})),Yr(e,391))}function Gc(e,t){var r,a;t&&t["!ref"]&&(Yr(e,648),Yr(e,649,(r=oa(t["!ref"]),(a=jr(24)).write_shift(4,4),a.write_shift(4,1),Pa(r,a),a)),Yr(e,650))}function jc(e,t,r){t["!links"].forEach((function(t){if(t[1].Target){var a=an(r,-1,t[1].Target.replace(/#.*$/,""),Qa.HLINK);Yr(e,494,function(e,t){var r=jr(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));Pa({s:aa(e[0]),e:aa(e[0])},r),Ra("rId"+t,r);var a=e[1].Target.indexOf("#");return ma((-1==a?"":e[1].Target.slice(a+1))||"",r),ma(e[1].Tooltip||"",r),ma("",r),r.slice(0,r.l)}(t,a))}})),delete t["!links"]}function Xc(e,t,r){Yr(e,133),Yr(e,137,function(e,t,r){null==r&&(r=jr(30));var a=924;return(((t||{}).Views||[])[0]||{}).RTL&&(a|=32),r.write_shift(2,a),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}(0,r)),Yr(e,138),Yr(e,134)}function $c(e,t){var r,a;t["!protect"]&&Yr(e,535,(r=t["!protect"],null==a&&(a=jr(66)),a.write_shift(2,r.password?hi(r.password):0),a.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach((function(e){e[1]?a.write_shift(4,null==r[e[0]]||r[e[0]]?0:1):a.write_shift(4,null!=r[e[0]]&&r[e[0]]?0:1)})),a))}function Yc(e,t,r,a){var n=$r(),s=r.SheetNames[e],i=r.Sheets[s]||{},o=s;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch(h){}var c,l,f=oa(i["!ref"]||"A1");if(f.e.c>16383||f.e.r>1048575){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575)}return i["!links"]=[],i["!comments"]=[],Yr(n,129),(r.vbaraw||i["!outline"])&&Yr(n,147,function(e,t,r){null==r&&(r=jr(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return Ua({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),_a(e,r),r.slice(0,r.l)}(o,i["!outline"])),Yr(n,148,Lc(f)),Xc(n,0,r.Workbook),zc(n,i),function(e,t,r,a){var n,s=oa(t["!ref"]||"A1"),i="",o=[];Yr(e,145);var c=Array.isArray(t),l=s.e.r;t["!rows"]&&(l=Math.max(s.e.r,t["!rows"].length-1));for(var f=s.s.r;f<=l;++f){i=ea(f),Fc(e,t,s,f);var h=!1;if(f<=s.e.r)for(var u=s.s.c;u<=s.e.c;++u){f===s.s.r&&(o[u]=ra(u)),n=o[u]+i;var d=c?(t[f]||[])[u]:t[n];h=!!d&&Hc(e,d,f,u,a,t,h)}}Yr(e,146)}(n,i,0,t),$c(n,i),function(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],s="string"==typeof n.ref?n.ref:ia(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,o=sa(s);o.s.r==o.e.r&&(o.e.r=sa(t["!ref"]).e.r,s=ia(o));for(var c=0;c<i.length;++c){var l=i[c];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==a){l.Ref="'"+r.SheetNames[a]+"'!"+s;break}}c==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+s}),Yr(e,161,Pa(oa(s))),Yr(e,162)}}(n,i,r,e),Vc(n,i),jc(n,i,a),i["!margins"]&&Yr(n,476,(c=i["!margins"],null==l&&(l=jr(48)),uc(c),Wc.forEach((function(e){Ma(c[e],l)})),l)),t&&!t.ignoreEC&&null!=t.ignoreEC||Gc(n,i),function(e,t,r,a){if(t["!comments"].length>0){var n=an(a,-1,"../drawings/vmlDrawing"+(r+1)+".vml",Qa.VML);Yr(e,551,Ra("rId"+n)),t["!legacy"]=n}}(n,i,e,a),Yr(n,130),n.end()}function Kc(e,t,r,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var o=0,c=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach((function(e){var t=function(e){var t,r=[],a=e.match(/^<c:numCache>/);(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm)||[]).forEach((function(e){var t=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);t&&(r[+t[1]]=a?+t[2]:t[2])}));var n=mt((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/gm)||[]).forEach((function(e){t=e.replace(/<.*?>/g,"")})),[r,n,t]}(e);f.s.r=f.s.c=0,f.e.c=o,l=ra(o),t[0].forEach((function(e,r){i[l+ea(r)]={t:"n",v:e,z:t[1]},c=r})),f.e.r<c&&(f.e.r=c),++o})),o>0&&(i["!ref"]=ia(f)),i}var Jc=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],qc=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],Zc=[],Qc=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function el(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=yt(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function tl(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=yt(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}function rl(e){tl(e.WBProps,Jc),tl(e.CalcPr,Qc),el(e.WBView,qc),el(e.Sheets,Zc),cc.date1904=yt(e.WBProps.date1904)}var al="][*?/\\".split("");function nl(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return al.forEach((function(a){if(-1!=e.indexOf(a)){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}})),r}function sl(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t,r,a,n=e.Workbook&&e.Workbook.Sheets||[];t=e.SheetNames,r=n,a=!!e.vbaraw,t.forEach((function(e,n){nl(e);for(var s=0;s<n;++s)if(e==t[s])throw new Error("Duplicate Sheet Name: "+e);if(a){var i=r&&r[n]&&r[n].CodeName||e;if(95==i.charCodeAt(0)&&i.length>22)throw new Error("Bad Code Name: Worksheet"+i)}}));for(var s=0;s<e.SheetNames.length;++s)mc(e.Sheets[e.SheetNames[s]],e.SheetNames[s],s)}var il=/<\w+:workbook/;function ol(e){var t=[st];t[t.length]=Bt("workbook",null,{xmlns:rr[0],"xmlns:r":Zt});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(Jc.forEach((function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(a[t[0]]=e.Workbook.WBProps[t[0]])})),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=Bt("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],s=0;if(n&&n[0]&&n[0].Hidden){for(t[t.length]="<bookViews>",s=0;s!=e.SheetNames.length&&n[s]&&n[s].Hidden;++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",s=0;s!=e.SheetNames.length;++s){var i={name:bt(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),n[s])switch(n[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden"}t[t.length]=Bt("sheet",null,i)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach((function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=Bt("definedName",bt(e.Ref),r))})),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function cl(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function ll(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,a,n=t.Workbook.Sheets,s=0,i=-1,o=-1;s<n.length;++s)!n[s]||!n[s].Hidden&&-1==i?i=s:1==n[s].Hidden&&-1==o&&(o=s);if(!(o>i))Yr(e,135),Yr(e,158,(r=i,a||(a=jr(29)),a.write_shift(-4,0),a.write_shift(-4,460),a.write_shift(4,28800),a.write_shift(4,17600),a.write_shift(4,500),a.write_shift(4,r),a.write_shift(4,r),a.write_shift(1,120),a.length>a.l?a.slice(0,a.l):a)),Yr(e,136)}}function fl(t,r){var a=$r();return Yr(a,131),Yr(a,128,function(t,r){r||(r=jr(127));for(var a=0;4!=a;++a)r.write_shift(4,0);return ma("SheetJS",r),ma(e.version,r),ma(e.version,r),ma("7262",r),r.length>r.l?r.slice(0,r.l):r}()),Yr(a,153,function(e,t){t||(t=jr(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),_a(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}(t.Workbook&&t.Workbook.WBProps||null)),ll(a,t),function(e,t){Yr(e,143);for(var r=0;r!=t.SheetNames.length;++r){Yr(e,156,(a={Hidden:t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]},(n=void 0)||(n=jr(127)),n.write_shift(4,a.Hidden),n.write_shift(4,a.iTabID),Ra(a.strRelID,n),ma(a.name.slice(0,31),n),n.length>n.l?n.slice(0,n.l):n))}var a,n;Yr(e,144)}(a,t),Yr(a,132),a.end()}function hl(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;t||(t={}),t.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],ql[16]={n:"BrtFRTArchID$",f:cl},Xr(e,(function(e,o,c){switch(c){case 156:i.SheetNames.push(e.name),r.Sheets.push(e);break;case 153:r.WBProps=e;break;case 39:null!=e.Sheet&&(t.SID=e.Sheet),e.Ref=Xo(e.Ptg,0,null,i,t),delete t.SID,delete e.Ptg,s.push(e);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:i[0].length?i.push([c,e]):i[0]=[c,e],i[i.length-1].XTI=[];break;case 362:0===i.length&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(e),i.XTI=i.XTI.concat(e);break;case 35:case 37:a.push(c),n=!0;break;case 36:case 38:a.pop(),n=!1;break;default:if(o.T);else if(!n||t.WTF&&37!=a[a.length-1]&&35!=a[a.length-1])throw new Error("Unexpected record 0x"+c.toString(16))}}),t),rl(r),r.Names=s,r.supbooks=i,r}(e,r):function(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(ct,(function(o,c){var l=ht(o);switch(ut(l[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":o.match(il)&&(n="xmlns"+o.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<workbookPr":case"<workbookPr/>":Jc.forEach((function(e){if(null!=l[e[0]])switch(e[2]){case"bool":r.WBProps[e[0]]=yt(l[e[0]]);break;case"int":r.WBProps[e[0]]=parseInt(l[e[0]],10);break;default:r.WBProps[e[0]]=l[e[0]]}})),l.codeName&&(r.WBProps.CodeName=Ct(l.codeName));break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=mt(Ct(l.name)),delete l[0],r.Sheets.push(l);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":a=!1;break;case"<definedName":(s={}).Name=Ct(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),yt(l.hidden||"0")&&(s.Hidden=!0),i=c+o.length;break;case"</definedName>":s.Ref=mt(Ct(e.slice(i,c))),r.Names.push(s);break;case"<calcPr":case"<calcPr/>":delete l[0],r.CalcPr=l;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return o})),-1===rr.indexOf(r.xmlns))throw new Error("Unknown Namespace: "+r.xmlns);return rl(r),r}(e,r)}function ul(e,t,r,a,n,s,i,o){return".bin"===t.slice(-4)?function(e,t,r,a,n,s,i){if(!e)return e;var o=t||{};a||(a={"!id":{}});var c,l,f,h,u,d,p,m,g,v,b=o.dense?[]:{},w={s:{r:2e6,c:2e6},e:{r:0,c:0}},T=!1,E=!1,S=[];o.biff=12,o["!row"]=0;var y=0,k=!1,_=[],A={},x=o.supbooks||n.supbooks||[[]];if(x.sharedf=A,x.arrayf=_,x.SheetNames=n.SheetNames||n.Sheets.map((function(e){return e.name})),!o.supbooks&&(o.supbooks=x,n.Names))for(var C=0;C<n.Names.length;++C)x[0][C+1]=n.Names[C];var O,R=[],I=[],N=!1;if(ql[16]={n:"BrtShortReal",f:Mc},Xr(e,(function(e,t,C){if(!E)switch(C){case 148:c=e;break;case 0:l=e,o.sheetRows&&o.sheetRows<=l.r&&(E=!0),g=ea(u=l.r),o["!row"]=l.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=xi(e.hpt)),I[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(f={t:e[2]},e[2]){case"n":f.v=e[1];break;case"s":m=oc[e[1]],f.v=m.t,f.r=m.r;break;case"b":f.v=!!e[1];break;case"e":f.v=e[1],!1!==o.cellText&&(f.w=Ya[f.v]);break;case"str":f.t="s",f.v=e[1];break;case"is":f.t="s",f.v=e[1].t}if((h=i.CellXf[e[0].iStyleRef])&&pc(f,h.numFmtId,null,o,s,i),d=-1==e[0].c?d+1:e[0].c,o.dense?(b[u]||(b[u]=[]),b[u][d]=f):b[ra(d)+g]=f,o.cellFormula){for(k=!1,y=0;y<_.length;++y){var D=_[y];l.r>=D[0].s.r&&l.r<=D[0].e.r&&d>=D[0].s.c&&d<=D[0].e.c&&(f.F=ia(D[0]),k=!0)}!k&&e.length>3&&(f.f=e[3])}if(w.s.r>l.r&&(w.s.r=l.r),w.s.c>d&&(w.s.c=d),w.e.r<l.r&&(w.e.r=l.r),w.e.c<d&&(w.e.c=d),o.cellDates&&h&&"n"==f.t&&he(L[h.numFmtId])){var F=W(f.v);F&&(f.t="d",f.v=new Date(F.y,F.m-1,F.d,F.H,F.M,F.S,F.u))}O&&("XLDAPR"==O.type&&(f.D=!0),O=void 0);break;case 1:case 12:if(!o.sheetStubs||T)break;f={t:"z",v:void 0},d=-1==e[0].c?d+1:e[0].c,o.dense?(b[u]||(b[u]=[]),b[u][d]=f):b[ra(d)+g]=f,w.s.r>l.r&&(w.s.r=l.r),w.s.c>d&&(w.s.c=d),w.e.r<l.r&&(w.e.r=l.r),w.e.c<d&&(w.e.c=d),O&&("XLDAPR"==O.type&&(f.D=!0),O=void 0);break;case 176:S.push(e);break;case 49:O=((o.xlmeta||{}).Cell||[])[e-1];break;case 494:var P=a["!id"][e.relId];for(P?(e.Target=P.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=P):""==e.relId&&(e.Target="#"+e.loc),u=e.rfx.s.r;u<=e.rfx.e.r;++u)for(d=e.rfx.s.c;d<=e.rfx.e.c;++d)o.dense?(b[u]||(b[u]=[]),b[u][d]||(b[u][d]={t:"z",v:void 0}),b[u][d].l=e):(p=na({c:d,r:u}),b[p]||(b[p]={t:"z",v:void 0}),b[p].l=e);break;case 426:if(!o.cellFormula)break;_.push(e),(v=o.dense?b[u][d]:b[ra(d)+g]).f=Xo(e[1],0,{r:l.r,c:d},x,o),v.F=ia(e[0]);break;case 427:if(!o.cellFormula)break;A[na(e[0].s)]=e[1],(v=o.dense?b[u][d]:b[ra(d)+g]).f=Xo(e[1],0,{r:l.r,c:d},x,o);break;case 60:if(!o.cellStyles)break;for(;e.e>=e.s;)R[e.e--]={width:e.w/256,hidden:!!(1&e.flags),level:e.level},N||(N=!0,yi(e.w/256)),ki(R[e.e+1]);break;case 161:b["!autofilter"]={ref:ia(e)};break;case 476:b["!margins"]=e;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name),(e.above||e.left)&&(b["!outline"]={above:e.above,left:e.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),e.RTL&&(n.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:case 37:T=!0;break;case 36:case 38:T=!1;break;default:if(t.T);else if(!T||o.WTF)throw new Error("Unexpected record 0x"+C.toString(16))}}),o),delete o.supbooks,delete o["!row"],!b["!ref"]&&(w.s.r<2e6||c&&(c.e.r>0||c.e.c>0||c.s.r>0||c.s.c>0))&&(b["!ref"]=ia(c||w)),o.sheetRows&&b["!ref"]){var D=oa(b["!ref"]);o.sheetRows<=+D.e.r&&(D.e.r=o.sheetRows-1,D.e.r>w.e.r&&(D.e.r=w.e.r),D.e.r<D.s.r&&(D.s.r=D.e.r),D.e.c>w.e.c&&(D.e.c=w.e.c),D.e.c<D.s.c&&(D.s.c=D.e.c),b["!fullref"]=b["!ref"],b["!ref"]=ia(D))}return S.length>0&&(b["!merges"]=S),R.length>0&&(b["!cols"]=R),I.length>0&&(b["!rows"]=I),b}(e,a,r,n,s,i,o):Ac(e,a,r,n,s,i,o)}function dl(e,t,r,a,n,s,i,o){return".bin"===t.slice(-4)?function(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=!1;return Xr(e,(function(e,a,o){switch(o){case 550:s["!rel"]=e;break;case 651:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:case 37:case 38:break;case 35:i=!0;break;case 36:i=!1;break;default:if(a.T>0);else if(a.T<0);else if(!i||t.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}}),t),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}(e,a,r,n,s):function(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s,i={"!type":"chart","!drawel":null,"!rel":""},o=e.match(yc);return o&&xc(o[0],0,n,r),(s=e.match(/drawing r:id="(.*?)"/))&&(i["!rel"]=s[1]),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}(e,0,r,n,s)}function pl(e,t,r,a){return".bin"===t.slice(-4)?function(e,t,r){var a={NumberFmt:[]};for(var n in L)a.NumberFmt[n]=L[n];a.CellXf=[],a.Fonts=[];var s=[],i=!1;return Xr(e,(function(e,n,o){switch(o){case 44:a.NumberFmt[e[0]]=e[1],ge(e[1],e[0]);break;case 43:a.Fonts.push(e),null!=e.color.theme&&t&&t.themeElements&&t.themeElements.clrScheme&&(e.color.rgb=vi(t.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==s[s.length-1]&&a.CellXf.push(e);break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(o),i=!0;break;case 38:s.pop(),i=!1;break;default:if(n.T>0)s.push(o);else if(n.T<0)s.pop();else if(!i||r.WTF&&37!=s[s.length-1])throw new Error("Unexpected record 0x"+o.toString(16))}})),a}(e,r,a):Ii(e,r,a)}function ml(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r=[],a=!1;return Xr(e,(function(e,n,s){switch(s){case 159:r.Count=e[0],r.Unique=e[1];break;case 19:r.push(e);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(n.T,!a||t.WTF)throw new Error("Unexpected record 0x"+s.toString(16))}})),r}(e,r):function(e,t){var r=[],a="";if(!e)return r;var n=e.match(Ks);if(n){a=n[2].replace(Js,"").split(qs);for(var s=0;s!=a.length;++s){var i=Ys(a[s].trim(),t);null!=i&&(r[r.length]=i)}n=ht(n[1]),r.Count=n.count,r.Unique=n.uniqueCount}return r}(e,r)}function gl(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r=[],a=[],n={},s=!1;return Xr(e,(function(e,i,o){switch(o){case 632:a.push(e);break;case 635:n=e;break;case 637:n.t=e.t,n.h=e.h,n.r=e.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,t.sheetRows&&n.rfx&&t.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,r.push(n);break;case 3072:case 37:case 38:break;case 35:s=!0;break;case 36:s=!1;break;default:if(i.T);else if(!s||t.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}})),r}(e,r):function(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach((function(e){if(""!==e&&""!==e.trim()){var t=e.match(/<(?:\w+:)?author[^>]*>(.*)/);t&&r.push(t[1])}}));var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach((function(e){if(""!==e&&""!==e.trim()){var n=e.match(/<(?:\w+:)?comment[^>]*>/);if(n){var s=ht(n[0]),i={author:s.authorId&&r[s.authorId]||"sheetjsghost",ref:s.ref,guid:s.guid},o=aa(s.ref);if(!(t.sheetRows&&t.sheetRows<=o.r)){var c=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),l=!!c&&!!c[1]&&Ys(c[1])||{r:"",t:"",h:""};i.r=l.r,"<t></t>"==l.r&&(l.t=l.h=""),i.t=(l.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),t.cellHTML&&(i.h=l.h),a.push(i)}}}})),a}(e,r)}function vl(e,t,r){return".bin"===t.slice(-4)?function(e){var t=[];return Xr(e,(function(e,r,a){if(63===a)t.push(e);else if(!r.T)throw new Error("Unexpected record 0x"+a.toString(16))})),t}(e):function(e){var t=[];if(!e)return t;var r=1;return(e.match(ct)||[]).forEach((function(e){var a=ht(e);switch(a[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete a[0],a.i?r=a.i:a.i=r,t.push(a)}})),t}(e)}function bl(e,t,r,a){if(".bin"===r.slice(-4))return function(e,t,r,a){if(!e)return e;var n=a||{},s=!1;Xr(e,(function(e,t,r){switch(r){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(t.T);else if(!s||n.WTF)throw new Error("Unexpected record 0x"+r.toString(16))}}),n)}(e,0,0,a)}function wl(e,t,r){return".bin"===t.slice(-4)?function(e,t,r){var a={Types:[],Cell:[],Value:[]},n=r||{},s=[],i=!1,o=2;return Xr(e,(function(e,t,r){switch(r){case 335:a.Types.push({name:e.name});break;case 51:e.forEach((function(e){1==o?a.Cell.push({type:a.Types[e[0]-1].name,index:e[1]}):0==o&&a.Value.push({type:a.Types[e[0]-1].name,index:e[1]})}));break;case 337:o=e?1:0;break;case 338:o=2;break;case 35:s.push(r),i=!0;break;case 36:s.pop(),i=!1;break;default:if(t.T);else if(!i||n.WTF&&35!=s[s.length-1])throw new Error("Unexpected record 0x"+r.toString(16))}})),a}(e,0,r):function(e,t,r){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n,s=!1,i=2;return e.replace(ct,(function(e){var t=ht(e);switch(ut(t[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":a.Types.push({name:t.name});break;case"<futureMetadata":for(var o=0;o<a.Types.length;++o)a.Types[o].name==t.name&&(n=a.Types[o]);break;case"<rc":1==i?a.Cell.push({type:a.Types[t.t-1].name,index:+t.v}):0==i&&a.Value.push({type:a.Types[t.t-1].name,index:+t.v});break;case"<cellMetadata":i=1;break;case"</cellMetadata>":case"</valueMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"<ext":s=!0;break;case"</ext>":s=!1;break;case"<rvb":if(!n)break;n.offsets||(n.offsets=[]),n.offsets.push(+t.i);break;default:if(!s&&r.WTF)throw new Error("unrecognized "+t[0]+" in metadata")}return e})),a}(e,0,r)}function Tl(e,t,r,a,n){return(".bin"===t.slice(-4)?Yc:Dc)(e,r,a,n)}function El(e,t,r){return(".bin"===t.slice(-4)?uo:lo)(e)}var Sl,yl=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,kl=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function _l(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),1===r.length)return a;var n,s,i,o=e.match(yl);if(o)for(i=0;i!=o.length;++i)-1===(s=(n=o[i].match(kl))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function Al(e){var t={};if(1===e.split(/\s+/).length)return t;var r,a,n,s=e.match(yl);if(s)for(n=0;n!=s.length;++n)-1===(a=(r=s[n].match(kl))[1].indexOf(":"))?t[r[1]]=r[2].slice(1,r[2].length-1):t["xmlns:"===r[1].slice(0,6)?"xmlns"+r[1].slice(6):r[1].slice(a+1)]=r[2].slice(1,r[2].length-1);return t}function xl(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=yt(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=Be(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[mt(t)]=n}function Cl(e,t,r){if("z"!==e.t){if(!r||!1!==r.cellText)try{"e"===e.t?e.w=e.w||Ya[e.v]:"General"===t?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=X(e.v):e.w=Y(e.v):e.w=(a=t||"General",n=e.v,"General"===(s=Sl[a]||mt(a))?Y(n):me(s,n))}catch(c){if(r.WTF)throw c}var a,n,s;try{var i=Sl[t]||t||"General";if(r.cellNF&&(e.z=i),r.cellDates&&"n"==e.t&&he(i)){var o=W(e.v);o&&(e.t="d",e.v=new Date(o.y,o.m-1,o.d,o.H,o.M,o.S,o.u))}}catch(c){if(r.WTF)throw c}}}function Ol(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=Ci[a.Pattern]||a.Pattern)}e[t.ID]=t}function Rl(e,t,r,a,n,s,i,o,c,l){var f="General",h=a.StyleID,u={};l=l||{};var d=[],p=0;for(void 0===h&&o&&(h=o.StyleID),void 0===h&&i&&(h=i.StyleID);void 0!==s[h]&&(s[h].nf&&(f=s[h].nf),s[h].Interior&&d.push(s[h].Interior),s[h].Parent);)h=s[h].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=yt(e);break;case"String":a.t="s",a.r=St(mt(e)),a.v=e.indexOf("<")>-1?mt(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=(Be(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!=a.v?a.v=mt(e):a.v<60&&(a.v=a.v-1),f&&"General"!=f||(f="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=Ka[e],!1!==l.cellText&&(a.w=e);break;default:""==e&&""==t?a.t="z":(a.t="s",a.v=St(t||e))}if(Cl(a,f,l),!1!==l.cellFormula)if(a.Formula){var m=mt(a.Formula);61==m.charCodeAt(0)&&(m=m.slice(1)),a.f=mo(m,n),delete a.Formula,"RC"==a.ArrayRange?a.F=mo("RC:RC",n):a.ArrayRange&&(a.F=mo(a.ArrayRange,n),c.push([oa(a.F),a.F]))}else for(p=0;p<c.length;++p)n.r>=c[p][0].s.r&&n.r<=c[p][0].e.r&&n.c>=c[p][0].s.c&&n.c<=c[p][0].e.c&&(a.F=c[p][1]);l.cellStyles&&(d.forEach((function(e){!u.patternType&&e.patternType&&(u.patternType=e.patternType)})),a.s=u),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}function Il(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),e.v=e.w=e.ixfe=void 0}function Nl(e,t){var r=t||{};be();var a=f(Ht(e));"binary"!=r.type&&"array"!=r.type&&"base64"!=r.type||(a=Ct(a));var n,s=a.slice(0,1024).toLowerCase(),i=!1;if((1023&(s=s.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&s.indexOf(","),1023&s.indexOf(";"))){var o=He(r);return o.type="string",Hs.to_workbook(a,o)}if(-1==s.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach((function(e){s.indexOf("<"+e)>=0&&(i=!0)})),i)return function(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||0==r.length)throw new Error("Invalid HTML: could not find <table>");if(1==r.length)return fa(ff(r[0],t),t);var a={SheetNames:[],Sheets:{}};return r.forEach((function(e,r){yh(a,ff(e,t),"Sheet"+(r+1))})),a}(a,r);Sl={"General Number":"General","General Date":L[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":L[15],"Short Date":L[14],"Long Time":L[19],"Medium Time":L[18],"Short Time":L[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:L[2],Standard:L[4],Percent:L[10],Scientific:L[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var c,l,h=[],u={},d=[],p=r.dense?[]:{},m="",g={},v={},b=_l('<Data ss:Type="String">'),w=0,T=0,E=0,S={s:{r:2e6,c:2e6},e:{r:0,c:0}},y={},k={},_="",A=0,x=[],C={},O={},R=0,I=[],N=[],D={},F=[],P=!1,M=[],U=[],B={},W=0,H=0,V={Sheets:[],WBProps:{date1904:!1}},z={};Vt.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/gm,"");for(var G="";n=Vt.exec(a);)switch(n[3]=(G=n[3]).toLowerCase()){case"data":if("data"==G){if("/"===n[1]){if((c=h.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&h.push([n[3],!0]);break}if(h[h.length-1][1])break;"/"===n[1]?Rl(a.slice(w,n.index),_,b,"comment"==h[h.length-1][0]?D:g,{c:T,r:E},y,F[T],v,M,r):(_="",b=_l(n[0]),w=n.index+n[0].length);break;case"cell":if("/"===n[1])if(N.length>0&&(g.c=N),(!r.sheetRows||r.sheetRows>E)&&void 0!==g.v&&(r.dense?(p[E]||(p[E]=[]),p[E][T]=g):p[ra(T)+ea(E)]=g),g.HRef&&(g.l={Target:mt(g.HRef)},g.HRefScreenTip&&(g.l.Tooltip=g.HRefScreenTip),delete g.HRef,delete g.HRefScreenTip),(g.MergeAcross||g.MergeDown)&&(W=T+(0|parseInt(g.MergeAcross,10)),H=E+(0|parseInt(g.MergeDown,10)),x.push({s:{c:T,r:E},e:{c:W,r:H}})),r.sheetStubs)if(g.MergeAcross||g.MergeDown){for(var j=T;j<=W;++j)for(var X=E;X<=H;++X)(j>T||X>E)&&(r.dense?(p[X]||(p[X]=[]),p[X][j]={t:"z"}):p[ra(j)+ea(X)]={t:"z"});T=W+1}else++T;else g.MergeAcross?T=W+1:++T;else(g=Al(n[0])).Index&&(T=+g.Index-1),T<S.s.c&&(S.s.c=T),T>S.e.c&&(S.e.c=T),"/>"===n[0].slice(-2)&&++T,N=[];break;case"row":"/"===n[1]||"/>"===n[0].slice(-2)?(E<S.s.r&&(S.s.r=E),E>S.e.r&&(S.e.r=E),"/>"===n[0].slice(-2)&&(v=_l(n[0])).Index&&(E=+v.Index-1),T=0,++E):((v=_l(n[0])).Index&&(E=+v.Index-1),B={},("0"==v.AutoFitHeight||v.Height)&&(B.hpx=parseInt(v.Height,10),B.hpt=Ai(B.hpx),U[E]=B),"1"==v.Hidden&&(B.hidden=!0,U[E]=B));break;case"worksheet":if("/"===n[1]){if((c=h.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"));d.push(m),S.s.r<=S.e.r&&S.s.c<=S.e.c&&(p["!ref"]=ia(S),r.sheetRows&&r.sheetRows<=S.e.r&&(p["!fullref"]=p["!ref"],S.e.r=r.sheetRows-1,p["!ref"]=ia(S))),x.length&&(p["!merges"]=x),F.length>0&&(p["!cols"]=F),U.length>0&&(p["!rows"]=U),u[m]=p}else S={s:{r:2e6,c:2e6},e:{r:0,c:0}},E=T=0,h.push([n[3],!1]),c=_l(n[0]),m=mt(c.Name),p=r.dense?[]:{},x=[],M=[],U=[],z={name:m,Hidden:0},V.Sheets.push(z);break;case"table":if("/"===n[1]){if((c=h.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else{if("/>"==n[0].slice(-2))break;h.push([n[3],!1]),F=[],P=!1}break;case"style":"/"===n[1]?Ol(y,k,r):k=_l(n[0]);break;case"numberformat":k.nf=mt(_l(n[0]).Format||"General"),Sl[k.nf]&&(k.nf=Sl[k.nf]);for(var $=0;392!=$&&L[$]!=k.nf;++$);if(392==$)for($=57;392!=$;++$)if(null==L[$]){ge(k.nf,$);break}break;case"column":if("table"!==h[h.length-1][0])break;if((l=_l(n[0])).Hidden&&(l.hidden=!0,delete l.Hidden),l.Width&&(l.wpx=parseInt(l.Width,10)),!P&&l.wpx>10){P=!0,bi=6;for(var Y=0;Y<F.length;++Y)F[Y]&&ki(F[Y])}P&&ki(l),F[l.Index-1||F.length]=l;for(var K=0;K<+l.Span;++K)F[F.length]=He(l);break;case"namedrange":if("/"===n[1])break;V.Names||(V.Names=[]);var J=ht(n[0]),q={Name:J.Name,Ref:mo(J.RefersTo.slice(1),{r:0,c:0})};V.Sheets.length>0&&(q.Sheet=V.Sheets.length-1),V.Names.push(q);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":if("/>"===n[0].slice(-2))break;"/"===n[1]?_+=a.slice(A,n.index):A=n.index+n[0].length;break;case"interior":if(!r.cellStyles)break;k.Interior=_l(n[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if("/>"===n[0].slice(-2))break;"/"===n[1]?Tn(C,G,a.slice(R,n.index)):R=n.index+n[0].length;break;case"styles":case"workbook":if("/"===n[1]){if((c=h.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else h.push([n[3],!1]);break;case"comment":if("/"===n[1]){if((c=h.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"));Il(D),N.push(D)}else h.push([n[3],!1]),D={a:(c=_l(n[0])).Author};break;case"autofilter":if("/"===n[1]){if((c=h.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else if("/"!==n[0].charAt(n[0].length-2)){var Z=_l(n[0]);p["!autofilter"]={ref:mo(Z.Range).replace(/\$/g,"")},h.push([n[3],!0])}break;case"datavalidation":if("/"===n[1]){if((c=h.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&h.push([n[3],!0]);break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===n[1]){if((c=h.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&h.push([n[3],!0]);break;default:if(0==h.length&&"document"==n[3])return wf(a,r);if(0==h.length&&"uof"==n[3])return wf(a,r);var Q=!0;switch(h[h.length-1][0]){case"officedocumentsettings":switch(n[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:Q=!1}break;case"componentoptions":switch(n[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:Q=!1}break;case"excelworkbook":switch(n[3]){case"date1904":V.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:Q=!1}break;case"workbookoptions":switch(n[3]){case"owcversion":case"height":case"width":break;default:Q=!1}break;case"worksheetoptions":switch(n[3]){case"visible":if("/>"===n[0].slice(-2));else if("/"===n[1])switch(a.slice(R,n.index)){case"SheetHidden":z.Hidden=1;break;case"SheetVeryHidden":z.Hidden=2}else R=n.index+n[0].length;break;case"header":p["!margins"]||uc(p["!margins"]={},"xlml"),isNaN(+ht(n[0]).Margin)||(p["!margins"].header=+ht(n[0]).Margin);break;case"footer":p["!margins"]||uc(p["!margins"]={},"xlml"),isNaN(+ht(n[0]).Margin)||(p["!margins"].footer=+ht(n[0]).Margin);break;case"pagemargins":var ee=ht(n[0]);p["!margins"]||uc(p["!margins"]={},"xlml"),isNaN(+ee.Top)||(p["!margins"].top=+ee.Top),isNaN(+ee.Left)||(p["!margins"].left=+ee.Left),isNaN(+ee.Right)||(p["!margins"].right=+ee.Right),isNaN(+ee.Bottom)||(p["!margins"].bottom=+ee.Bottom);break;case"displayrighttoleft":V.Views||(V.Views=[]),V.Views[0]||(V.Views[0]={}),V.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":p["!outline"]||(p["!outline"]={}),p["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":p["!outline"]||(p["!outline"]={}),p["!outline"].left=!0;break;default:Q=!1}break;case"pivottable":case"pivotcache":switch(n[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:Q=!1}break;case"pagebreaks":switch(n[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:Q=!1}break;case"autofilter":switch(n[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:Q=!1}break;case"querytable":switch(n[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:Q=!1}break;case"datavalidation":switch(n[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:Q=!1}break;case"sorting":case"conditionalformatting":switch(n[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:Q=!1}break;case"mapinfo":case"schema":case"data":switch(n[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:Q=!1}break;case"smarttags":break;default:Q=!1}if(Q)break;if(n[3].match(/!\[CDATA/))break;if(!h[h.length-1][1])throw"Unrecognized tag: "+n[3]+"|"+h.join("|");if("customdocumentproperties"===h[h.length-1][0]){if("/>"===n[0].slice(-2))break;"/"===n[1]?xl(O,G,I,a.slice(R,n.index)):(I=n,R=n.index+n[0].length);break}if(r.WTF)throw"Unrecognized tag: "+n[3]+"|"+h.join("|")}var te={};return r.bookSheets||r.bookProps||(te.Sheets=u),te.SheetNames=d,te.Workbook=V,te.SSF=He(L),te.Props=C,te.Custprops=O,te}function Dl(e,t){switch(qf(t=t||{}),t.type||"base64"){case"base64":return Nl(m(e),t);case"binary":case"buffer":case"file":return Nl(e,t);case"array":return Nl(S(e),t)}}function Fl(e,t){var r=[];return e.Props&&r.push(function(e,t){var r=[];return _e(wn).map((function(e){for(var t=0;t<on.length;++t)if(on[t][1]==e)return on[t];for(t=0;t<un.length;++t)if(un[t][1]==e)return un[t];throw e})).forEach((function(a){if(null!=e[a[1]]){var n=t&&t.Props&&null!=t.Props[a[1]]?t.Props[a[1]]:e[a[1]];"date"===a[2]&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof n?n=String(n):!0===n||!1===n?n=n?"1":"0":n instanceof Date&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"")),r.push(Mt(wn[a[1]]||a[1],n))}})),Bt("DocumentProperties",r.join(""),{xmlns:ar})}(e.Props,t)),e.Custprops&&r.push(function(e,t){var r=["Worksheets","SheetNames"],a="CustomDocumentProperties",n=[];return e&&_e(e).forEach((function(t){if(Object.prototype.hasOwnProperty.call(e,t)){for(var a=0;a<on.length;++a)if(t==on[a][1])return;for(a=0;a<un.length;++a)if(t==un[a][1])return;for(a=0;a<r.length;++a)if(t==r[a])return;var s=e[t],i="string";"number"==typeof s?(i="float",s=String(s)):!0===s||!1===s?(i="boolean",s=s?"1":"0"):s=String(s),n.push(Bt(wt(t),s,{"dt:dt":i}))}})),t&&_e(t).forEach((function(r){if(Object.prototype.hasOwnProperty.call(t,r)&&(!e||!Object.prototype.hasOwnProperty.call(e,r))){var a=t[r],s="string";"number"==typeof a?(s="float",a=String(a)):!0===a||!1===a?(s="boolean",a=a?"1":"0"):a instanceof Date?(s="dateTime.tz",a=a.toISOString()):a=String(a),n.push(Bt(wt(r),a,{"dt:dt":s}))}})),"<"+a+' xmlns="'+ar+'">'+n.join("")+"</"+a+">"}(e.Props,e.Custprops)),r.join("")}function Pl(e){return Bt("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+vo(e.Ref,{r:0,c:0})})}function Ll(e,t,r,a,n,s,i){if(!e||null==e.v&&null==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+bt(vo(e.f,i))),e.F&&e.F.slice(0,t.length)==t){var c=aa(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(c.r==i.r?"":"["+(c.r-i.r)+"]")+"C"+(c.c==i.c?"":"["+(c.c-i.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=bt(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=bt(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],f=0;f!=l.length;++f)l[f].s.c==i.c&&l[f].s.r==i.r&&(l[f].e.c>l[f].s.c&&(o["ss:MergeAcross"]=l[f].e.c-l[f].s.c),l[f].e.r>l[f].s.r&&(o["ss:MergeDown"]=l[f].e.r-l[f].s.r));var h="",u="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=Ya[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||L[14]);break;case"s":h="String",u=((e.v||"")+"").replace(gt,(function(e){return pt[e]})).replace(Tt,(function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}))}var d=dc(a.cellXfs,e,a);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=i.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map((function(e){var t=Bt("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return Bt("Comment",t,{"ss:Author":e.a})})).join("")),Bt("Cell",m,o)}function Ml(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=xi(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function Ul(e,t,r){var a=[],n=r.SheetNames[e],s=r.Sheets[n],i=s?function(e,t,r,a){if(!e)return"";if(!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,s=[],i=0;i<n.length;++i){var o=n[i];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(Pl(o)))}return s.join("")}(s,0,e,r):"";return i.length>0&&a.push("<Names>"+i+"</Names>"),i=s?function(e,t){if(!e["!ref"])return"";var r=oa(e["!ref"]),a=e["!merges"]||[],n=0,s=[];e["!cols"]&&e["!cols"].forEach((function(e,t){ki(e);var r=!!e.width,a=hc(t,e),n={"ss:Index":t+1};r&&(n["ss:Width"]=wi(a.width)),e.hidden&&(n["ss:Hidden"]="1"),s.push(Bt("Column",null,n))}));for(var i=Array.isArray(e),o=r.s.r;o<=r.e.r;++o){for(var c=[Ml(o,(e["!rows"]||[])[o])],l=r.s.c;l<=r.e.c;++l){var f=!1;for(n=0;n!=a.length;++n)if(!(a[n].s.c>l||a[n].s.r>o||a[n].e.c<l||a[n].e.r<o)){a[n].s.c==l&&a[n].s.r==o||(f=!0);break}if(!f){var h={r:o,c:l},u=na(h),d=i?(e[o]||[])[l]:e[u];c.push(Ll(d,u,e,t,0,0,h))}}c.push("</Row>"),c.length>2&&s.push(c.join(""))}return s.join("")}(s,t):"",i.length>0&&a.push("<Table>"+i+"</Table>"),a.push(function(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(Bt("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(Bt("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(Bt("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r])if(a.Workbook.Sheets[r].Hidden)n.push(Bt("Visible",1==a.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&(!a.Workbook.Sheets[s]||a.Workbook.Sheets[s].Hidden);++s);s==r&&n.push("<Selected/>")}return((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(Mt("ProtectContents","True")),e["!protect"].objects&&n.push(Mt("ProtectObjects","True")),e["!protect"].scenarios&&n.push(Mt("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||n.push(Mt("EnableSelection","UnlockedCells")):n.push(Mt("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach((function(t){e["!protect"][t[0]]&&n.push("<"+t[1]+"/>")}))),0==n.length?"":Bt("WorksheetOptions",n.join(""),{xmlns:nr})}(s,0,e,r)),a.join("")}function Bl(e,t){t||(t={}),e.SSF||(e.SSF=He(L)),e.SSF&&(be(),ve(e.SSF),t.revssf=Ce(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],dc(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(Fl(e,t)),r.push(""),r.push(""),r.push("");for(var a=0;a<e.SheetNames.length;++a)r.push(Bt("Worksheet",Ul(a,t,e),{"ss:Name":bt(e.SheetNames[a])}));return r[2]=function(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach((function(e,t){var a=[];a.push(Bt("NumberFormat",null,{"ss:Format":bt(L[e.numFmtId])}));var n={"ss:ID":"s"+(21+t)};r.push(Bt("Style",a.join(""),n))})),Bt("Styles",r.join(""))}(0,t),r[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];null==n.Sheet&&(n.Name.match(/^_xlfn\./)||r.push(Pl(n)))}return Bt("Names",r.join(""))}(e),st+Bt("Workbook",r.join(""),{xmlns:sr,"xmlns:o":ar,"xmlns:x":nr,"xmlns:ss":sr,"xmlns:dt":ir,"xmlns:html":lr})}function Wl(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=function(e){return Ba(e,1)}(r),r.length-r.l<=4)return t;var a=r.read_shift(4);return 0==a||a>40?t:(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4||1907505652!==(a=r.read_shift(4))?t:(t.UnicodeClipboardFormat=function(e){return Ba(e,2)}(r),0==(a=r.read_shift(4))||a>40?t:(r.l-=4,void(t.Reserved2=r.read_shift(0,"lpwstr")))))}var Hl=[60,1084,2066,2165,2175];function Vl(e,t,r,a,n){var s=a,i=[],o=r.slice(r.l,r.l+s);if(n&&n.enc&&n.enc.insitu&&o.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:n.enc.insitu(o)}i.push(o),r.l+=s;for(var c=Nr(r,r.l),l=Zl[c],f=0;null!=l&&Hl.indexOf(c)>-1;)s=Nr(r,r.l+2),f=r.l+4,2066==c?f+=4:2165!=c&&2175!=c||(f+=12),o=r.slice(f,r.l+4+s),i.push(o),r.l+=4+s,l=Zl[c=Nr(r,r.l)];var h=k(i);zr(h,0);var u=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(u),u+=i[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return t.f(h,h.length,n)}function zl(e,t,r){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=L[a])}catch(s){if(t.WTF)throw s}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||Ya[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=X(e.v):e.w=Y(e.v):e.w=me(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(s){if(t.WTF)throw s}if(t.cellDates&&a&&"n"==e.t&&he(L[a]||String(a))){var n=W(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function Gl(e,t,r){return{v:e,ixfe:t,t:r}}function jl(e,t){var r,a,n,i,o,c,l,f,h={opts:{}},u={},d=t.dense?[]:{},p={},m={},g=null,v=[],b="",w={},T="",E={},S=[],y=[],k=[],_={Sheets:[],WBProps:{date1904:!1},Views:[{}]},A={},x=function(e){return e<8?$a[e]:e<64&&k[e-8]||$a[e]},C=function(e,t,a){if(!(B>1||a.sheetRows&&e.r>=a.sheetRows)){if(a.cellStyles&&t.XF&&t.XF.data&&function(e,t,r){var a,n=t.XF.data;n&&n.patternType&&r&&r.cellStyles&&(t.s={},t.s.patternType=n.patternType,(a=gi(x(n.icvFore)))&&(t.s.fgColor={rgb:a}),(a=gi(x(n.icvBack)))&&(t.s.bgColor={rgb:a}))}(0,t,a),delete t.ixfe,delete t.XF,r=e,T=na(e),m&&m.s&&m.e||(m={s:{r:0,c:0},e:{r:0,c:0}}),e.r<m.s.r&&(m.s.r=e.r),e.c<m.s.c&&(m.s.c=e.c),e.r+1>m.e.r&&(m.e.r=e.r+1),e.c+1>m.e.c&&(m.e.c=e.c+1),a.cellFormula&&t.f)for(var n=0;n<S.length;++n)if(!(S[n][0].s.c>e.c||S[n][0].s.r>e.r||S[n][0].e.c<e.c||S[n][0].e.r<e.r)){t.F=ia(S[n][0]),S[n][0].s.c==e.c&&S[n][0].s.r==e.r||delete t.f,t.f&&(t.f=""+Xo(S[n][1],0,e,P,O));break}a.dense?(d[e.r]||(d[e.r]=[]),d[e.r][e.c]=t):d[T]=t}},O={enc:!1,sbcch:0,snames:[],sharedf:E,arrayf:S,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(O.password=t.password);var R=[],I=[],N=[],D=[],F=!1,P=[];P.SheetNames=O.snames,P.sharedf=O.sharedf,P.arrayf=O.arrayf,P.names=[],P.XTI=[];var M,U=0,B=0,W=0,H=[],V=[];O.codepage=1200,s(1200);for(var z=!1;e.l<e.length-1;){var G=e.l,j=e.read_shift(2);if(0===j&&10===U)break;var X=e.l===e.length?0:e.read_shift(2),$=Zl[j];if($&&$.f){if(t.bookSheets&&133===U&&133!==j)break;if(U=j,2===$.r||12==$.r){var Y=e.read_shift(2);if(X-=2,!O.enc&&Y!==j&&((255&Y)<<8|Y>>8)!==j)throw new Error("rt mismatch: "+Y+"!="+j);12==$.r&&(e.l+=10,X-=10)}var K={};if(K=10===j?$.f(e,X,O):Vl(j,$,e,X,O),0==B&&-1===[9,521,1033,2057].indexOf(U))continue;switch(j){case 34:h.opts.Date1904=_.WBProps.date1904=K;break;case 134:h.opts.WriteProtect=!0;break;case 47:if(O.enc||(e.l=0),O.enc=K,!t.password)throw new Error("File is password-protected");if(null==K.valid)throw new Error("Encryption scheme unsupported");if(!K.valid)throw new Error("Password is incorrect");break;case 92:O.lastuser=K;break;case 66:var J=Number(K);switch(J){case 21010:J=1200;break;case 32768:J=1e4;break;case 32769:J=1252}s(O.codepage=J),z=!0;break;case 317:O.rrtabid=K;break;case 25:O.winlocked=K;break;case 439:h.opts.RefreshAll=K;break;case 12:h.opts.CalcCount=K;break;case 16:h.opts.CalcDelta=K;break;case 17:h.opts.CalcIter=K;break;case 13:h.opts.CalcMode=K;break;case 14:h.opts.CalcPrecision=K;break;case 95:h.opts.CalcSaveRecalc=K;break;case 15:O.CalcRefMode=K;break;case 2211:h.opts.FullCalc=K;break;case 129:K.fDialog&&(d["!type"]="dialog"),K.fBelow||((d["!outline"]||(d["!outline"]={})).above=!0),K.fRight||((d["!outline"]||(d["!outline"]={})).left=!0);break;case 224:y.push(K);break;case 430:P.push([K]),P[P.length-1].XTI=[];break;case 35:case 547:P[P.length-1].push(K);break;case 24:case 536:M={Name:K.Name,Ref:Xo(K.rgce,0,null,P,O)},K.itab>0&&(M.Sheet=K.itab-1),P.names.push(M),P[0]||(P[0]=[],P[0].XTI=[]),P[P.length-1].push(K),"_xlnm._FilterDatabase"==K.Name&&K.itab>0&&K.rgce&&K.rgce[0]&&K.rgce[0][0]&&"PtgArea3d"==K.rgce[0][0][0]&&(V[K.itab-1]={ref:ia(K.rgce[0][0][1][2])});break;case 22:O.ExternCount=K;break;case 23:0==P.length&&(P[0]=[],P[0].XTI=[]),P[P.length-1].XTI=P[P.length-1].XTI.concat(K),P.XTI=P.XTI.concat(K);break;case 2196:if(O.biff<8)break;null!=M&&(M.Comment=K[1]);break;case 18:d["!protect"]=K;break;case 19:0!==K&&O.WTF&&console.error("Password verifier: "+K);break;case 133:p[K.pos]=K,O.snames.push(K.name);break;case 10:if(--B)break;if(m.e){if(m.e.r>0&&m.e.c>0){if(m.e.r--,m.e.c--,d["!ref"]=ia(m),t.sheetRows&&t.sheetRows<=m.e.r){var q=m.e.r;m.e.r=t.sheetRows-1,d["!fullref"]=d["!ref"],d["!ref"]=ia(m),m.e.r=q}m.e.r++,m.e.c++}R.length>0&&(d["!merges"]=R),I.length>0&&(d["!objects"]=I),N.length>0&&(d["!cols"]=N),D.length>0&&(d["!rows"]=D),_.Sheets.push(A)}""===b?w=d:u[b]=d,d=t.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===O.biff&&(O.biff={9:2,521:3,1033:4}[j]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[K.BIFFVer]||8),O.biffguess=0==K.BIFFVer,0==K.BIFFVer&&4096==K.dt&&(O.biff=5,z=!0,s(O.codepage=28591)),8==O.biff&&0==K.BIFFVer&&16==K.dt&&(O.biff=2),B++)break;if(d=t.dense?[]:{},O.biff<8&&!z&&(z=!0,s(O.codepage=t.codepage||1252)),O.biff<5||0==K.BIFFVer&&4096==K.dt){""===b&&(b="Sheet1"),m={s:{r:0,c:0},e:{r:0,c:0}};var Z={pos:e.l-X,name:b};p[Z.pos]=Z,O.snames.push(b)}else b=(p[G]||{name:""}).name;32==K.dt&&(d["!type"]="chart"),64==K.dt&&(d["!type"]="macro"),R=[],I=[],O.arrayf=S=[],N=[],D=[],F=!1,A={Hidden:(p[G]||{hs:0}).hs,name:b};break;case 515:case 3:case 2:"chart"==d["!type"]&&(t.dense?(d[K.r]||[])[K.c]:d[na({c:K.c,r:K.r})])&&++K.c,c={ixfe:K.ixfe,XF:y[K.ixfe]||{},v:K.val,t:"n"},W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C({c:K.c,r:K.r},c,t);break;case 5:case 517:c={ixfe:K.ixfe,XF:y[K.ixfe],v:K.val,t:K.t},W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C({c:K.c,r:K.r},c,t);break;case 638:c={ixfe:K.ixfe,XF:y[K.ixfe],v:K.rknum,t:"n"},W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C({c:K.c,r:K.r},c,t);break;case 189:for(var Q=K.c;Q<=K.C;++Q){var ee=K.rkrec[Q-K.c][0];c={ixfe:ee,XF:y[ee],v:K.rkrec[Q-K.c][1],t:"n"},W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C({c:Q,r:K.r},c,t)}break;case 6:case 518:case 1030:if("String"==K.val){g=K;break}if((c=Gl(K.val,K.cell.ixfe,K.tt)).XF=y[c.ixfe],t.cellFormula){var te=K.formula;if(te&&te[0]&&te[0][0]&&"PtgExp"==te[0][0][0]){var re=te[0][0][1][0],ae=te[0][0][1][1],ne=na({r:re,c:ae});E[ne]?c.f=""+Xo(K.formula,0,K.cell,P,O):c.F=((t.dense?(d[re]||[])[ae]:d[ne])||{}).F}else c.f=""+Xo(K.formula,0,K.cell,P,O)}W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C(K.cell,c,t),g=K;break;case 7:case 519:if(!g)throw new Error("String record expects Formula");g.val=K,(c=Gl(K,g.cell.ixfe,"s")).XF=y[c.ixfe],t.cellFormula&&(c.f=""+Xo(g.formula,0,g.cell,P,O)),W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C(g.cell,c,t),g=null;break;case 33:case 545:S.push(K);var se=na(K[0].s);if(a=t.dense?(d[K[0].s.r]||[])[K[0].s.c]:d[se],t.cellFormula&&a){if(!g)break;if(!se||!a)break;a.f=""+Xo(K[1],0,K[0],P,O),a.F=ia(K[0])}break;case 1212:if(!t.cellFormula)break;if(T){if(!g)break;E[na(g.cell)]=K[0],((a=t.dense?(d[g.cell.r]||[])[g.cell.c]:d[na(g.cell)])||{}).f=""+Xo(K[0],0,r,P,O)}break;case 253:c=Gl(v[K.isst].t,K.ixfe,"s"),v[K.isst].h&&(c.h=v[K.isst].h),c.XF=y[c.ixfe],W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C({c:K.c,r:K.r},c,t);break;case 513:t.sheetStubs&&(c={ixfe:K.ixfe,XF:y[K.ixfe],t:"z"},W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C({c:K.c,r:K.r},c,t));break;case 190:if(t.sheetStubs)for(var ie=K.c;ie<=K.C;++ie){var oe=K.ixfe[ie-K.c];c={ixfe:oe,XF:y[oe],t:"z"},W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C({c:ie,r:K.r},c,t)}break;case 214:case 516:case 4:(c=Gl(K.val,K.ixfe,"s")).XF=y[c.ixfe],W>0&&(c.z=H[c.ixfe>>8&63]),zl(c,t,h.opts.Date1904),C({c:K.c,r:K.r},c,t);break;case 0:case 512:1===B&&(m=K);break;case 252:v=K;break;case 1054:if(4==O.biff){H[W++]=K[1];for(var ce=0;ce<W+163&&L[ce]!=K[1];++ce);ce>=163&&ge(K[1],W+163)}else ge(K[1],K[0]);break;case 30:H[W++]=K;for(var le=0;le<W+163&&L[le]!=K;++le);le>=163&&ge(K,W+163);break;case 229:R=R.concat(K);break;case 93:I[K.cmo[0]]=O.lastobj=K;break;case 438:O.lastobj.TxO=K;break;case 127:O.lastobj.ImData=K;break;case 440:for(o=K[0].s.r;o<=K[0].e.r;++o)for(i=K[0].s.c;i<=K[0].e.c;++i)(a=t.dense?(d[o]||[])[i]:d[na({c:i,r:o})])&&(a.l=K[1]);break;case 2048:for(o=K[0].s.r;o<=K[0].e.r;++o)for(i=K[0].s.c;i<=K[0].e.c;++i)(a=t.dense?(d[o]||[])[i]:d[na({c:i,r:o})])&&a.l&&(a.l.Tooltip=K[1]);break;case 28:if(O.biff<=5&&O.biff>=2)break;a=t.dense?(d[K[0].r]||[])[K[0].c]:d[na(K[0])];var fe=I[K[2]];a||(t.dense?(d[K[0].r]||(d[K[0].r]=[]),a=d[K[0].r][K[0].c]={t:"z"}):a=d[na(K[0])]={t:"z"},m.e.r=Math.max(m.e.r,K[0].r),m.s.r=Math.min(m.s.r,K[0].r),m.e.c=Math.max(m.e.c,K[0].c),m.s.c=Math.min(m.s.c,K[0].c)),a.c||(a.c=[]),n={a:K[1],t:fe.TxO.t},a.c.push(n);break;case 2173:y[K.ixfe],K.ext.forEach((function(e){e[0]}));break;case 125:if(!O.cellStyles)break;for(;K.e>=K.s;)N[K.e--]={width:K.w/256,level:K.level||0,hidden:!!(1&K.flags)},F||(F=!0,yi(K.w/256)),ki(N[K.e+1]);break;case 520:var he={};null!=K.level&&(D[K.r]=he,he.level=K.level),K.hidden&&(D[K.r]=he,he.hidden=!0),K.hpt&&(D[K.r]=he,he.hpt=K.hpt,he.hpx=xi(K.hpt));break;case 38:case 39:case 40:case 41:d["!margins"]||uc(d["!margins"]={}),d["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[j]]=K;break;case 161:d["!margins"]||uc(d["!margins"]={}),d["!margins"].header=K.header,d["!margins"].footer=K.footer;break;case 574:K.RTL&&(_.Views[0].RTL=!0);break;case 146:k=K;break;case 2198:f=K;break;case 140:l=K;break;case 442:b?A.CodeName=K||A.name:_.WBProps.CodeName=K||"ThisWorkbook"}}else $||console.error("Missing Info for XLS Record 0x"+j.toString(16)),e.l+=X}return h.SheetNames=_e(p).sort((function(e,t){return Number(e)-Number(t)})).map((function(e){return p[e].name})),t.bookSheets||(h.Sheets=u),!h.SheetNames.length&&w["!ref"]?(h.SheetNames.push("Sheet1"),h.Sheets&&(h.Sheets.Sheet1=w)):h.Preamble=w,h.Sheets&&V.forEach((function(e,t){h.Sheets[h.SheetNames[t]]["!autofilter"]=e})),h.Strings=v,h.SSF=He(L),O.enc&&(h.Encryption=O.enc),f&&(h.Themes=f),h.Metadata={},void 0!==l&&(h.Metadata.Country=l),P.names.length>0&&(_.Names=P.names),h.Workbook=_,h}var Xl="e0859ff2f94f6810ab9108002b27b3d9",$l="02d5cdd59c2e1b10939708002b2cf9ae",Yl="05d5cdd59c2e1b10939708002b2cf9ae";function Kl(e,t){var r,a,s,o;if(t||(t={}),qf(t),i(),t.codepage&&n(t.codepage),e.FullPaths){if(Se.find(e,"/encryption"))throw new Error("File is password-protected");r=Se.find(e,"!CompObj"),a=Se.find(e,"/Workbook")||Se.find(e,"/Book")}else{switch(t.type){case"base64":e=T(m(e));break;case"binary":e=T(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e))}zr(e,0),a={content:e}}if(r&&Wl(r),t.bookProps&&!t.bookSheets)s={};else{var c=g?"buffer":"array";if(a&&a.content)s=jl(a.content,t);else if((o=Se.find(e,"PerfectOffice_MAIN"))&&o.content)s=Vs.to_workbook(o.content,(t.type=c,t));else{if(!(o=Se.find(e,"NativeContent_MAIN"))||!o.content)throw(o=Se.find(e,"MN0"))&&o.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");s=Vs.to_workbook(o.content,(t.type=c,t))}t.bookVBA&&e.FullPaths&&Se.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(s.vbaraw=function(e){var t=Se.utils.cfb_new({root:"R"});return e.FullPaths.forEach((function(r,a){if("/"!==r.slice(-1)&&r.match(/_VBA_PROJECT_CUR/)){var n=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Se.utils.cfb_add(t,n,e.FileIndex[a].content)}})),Se.write(t)}(e))}var l={};return e.FullPaths&&function(e,t,r){var a=Se.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Pn(a,Va,$l);for(var s in n)t[s]=n[s]}catch(l){if(r.WTF)throw l}var i=Se.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var o=Pn(i,za,Xl);for(var c in o)null==t[c]&&(t[c]=o[c])}catch(l){if(r.WTF)throw l}t.HeadingPairs&&t.TitlesOfParts&&(pn(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}(e,l,t),s.Props=s.Custprops=l,t.bookFiles&&(s.cfb=e),s}function Jl(e,t){var r=t||{},a=Se.utils.cfb_new({root:"R"}),n="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":n="/Workbook",r.biff=8;break;case"biff5":n="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return Se.utils.cfb_add(a,n,lf(e,r)),8==r.biff&&(e.Props||e.Custprops)&&function(e,t){var r,a=[],n=[],s=[],i=0,o=Ae(Va,"n"),c=Ae(za,"n");if(e.Props)for(r=_e(e.Props),i=0;i<r.length;++i)(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(c,r[i])?n:s).push([r[i],e.Props[r[i]]]);if(e.Custprops)for(r=_e(e.Custprops),i=0;i<r.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},r[i])||(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(c,r[i])?n:s).push([r[i],e.Custprops[r[i]]]);var l=[];for(i=0;i<s.length;++i)Nn.indexOf(s[i][0])>-1||dn.indexOf(s[i][0])>-1||null!=s[i][1]&&l.push(s[i]);n.length&&Se.utils.cfb_add(t,"/SummaryInformation",Ln(n,Xl,c,za)),(a.length||l.length)&&Se.utils.cfb_add(t,"/DocumentSummaryInformation",Ln(a,$l,o,Va,l.length?l:null,Yl))}(e,a),8==r.biff&&e.vbaraw&&function(e,t){t.FullPaths.forEach((function(r,a){if(0!=a){var n=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==n.slice(-1)&&Se.utils.cfb_add(e,n,t.FileIndex[a].content)}}))}(a,Se.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),a}var ql={0:{f:function(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=n/20),r}},1:{f:function(e){return[Ta(e)]}},2:{f:function(e){return[Ta(e),Ia(e),"n"]}},3:{f:function(e){return[Ta(e),e.read_shift(1),"e"]}},4:{f:function(e){return[Ta(e),e.read_shift(1),"b"]}},5:{f:function(e){return[Ta(e),La(e),"n"]}},6:{f:function(e){return[Ta(e),pa(e),"str"]}},7:{f:function(e){return[Ta(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var a=e.l+t,n=Ta(e);n.r=r["!row"];var s=[n,pa(e),"str"];if(r.cellFormula){e.l+=2;var i=Qo(e,a-e.l,r);s[3]=Xo(i,0,n,r.supbooks,r)}else e.l=a;return s}},9:{f:function(e,t,r){var a=e.l+t,n=Ta(e);n.r=r["!row"];var s=[n,La(e),"n"];if(r.cellFormula){e.l+=2;var i=Qo(e,a-e.l,r);s[3]=Xo(i,0,n,r.supbooks,r)}else e.l=a;return s}},10:{f:function(e,t,r){var a=e.l+t,n=Ta(e);n.r=r["!row"];var s=[n,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var i=Qo(e,a-e.l,r);s[3]=Xo(i,0,n,r.supbooks,r)}else e.l=a;return s}},11:{f:function(e,t,r){var a=e.l+t,n=Ta(e);n.r=r["!row"];var s=[n,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var i=Qo(e,a-e.l,r);s[3]=Xo(i,0,n,r.supbooks,r)}else e.l=a;return s}},12:{f:function(e){return[Sa(e)]}},13:{f:function(e){return[Sa(e),Ia(e),"n"]}},14:{f:function(e){return[Sa(e),e.read_shift(1),"e"]}},15:{f:function(e){return[Sa(e),e.read_shift(1),"b"]}},16:{f:Mc},17:{f:function(e){return[Sa(e),pa(e),"str"]}},18:{f:function(e){return[Sa(e),e.read_shift(4),"s"]}},19:{f:va},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),s=Ca(e),i=ec(e,0,r),o=Aa(e);e.l=a;var c={Name:s,Ptg:i};return n<268435455&&(c.Sheet=n),o&&(c.Comment=o),c}},40:{},42:{},43:{f:function(e,t,r){var a={};a.sz=e.read_shift(2)/20;var n=function(e){var t=e.read_shift(1);return e.l++,{fBold:1&t,fItalic:2&t,fUnderline:4&t,fStrikeout:8&t,fOutline:16&t,fShadow:32&t,fCondense:64&t,fExtend:128&t}}(e);switch(n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1),700===e.read_shift(2)&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript"}var s=e.read_shift(1);0!=s&&(a.underline=s);var i=e.read_shift(1);i>0&&(a.family=i);var o=e.read_shift(1);switch(o>0&&(a.charset=o),e.l++,a.color=function(e){var t={},r=e.read_shift(1)>>>1,a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r){case 0:t.auto=1;break;case 1:t.index=a;var c=$a[a];c&&(t.rgb=gi(c));break;case 2:t.rgb=gi([s,i,o]);break;case 3:t.theme=a}return 0!=n&&(t.tint=n>0?n/32767:n/32768),t}(e),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor"}return a.name=pa(e),a}},44:{f:function(e,t){return[e.read_shift(2),pa(e)]}},45:{f:Mi},46:{f:Hi},47:{f:function(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:Ns},62:{f:function(e){return[Ta(e),va(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=na(r);var a=e.read_shift(1);return 2&a&&(t.l="1"),8&a&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Gr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},a=e[e.l];return++e.l,r.above=!(64&a),r.left=!(128&a),e.l+=18,r.name=ka(e),r}},148:{f:Pc,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?pa(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(65536&a),r.backupFile=!!(64&a),r.checkCompatibility=!!(4096&a),r.date1904=!!(1&a),r.filterPrivacy=!!(8&a),r.hidePivotFieldList=!!(1024&a),r.promptedSolutions=!!(16&a),r.publishItems=!!(2048&a),r.refreshAllConnections=!!(262144&a),r.saveExternalLinkValues=!!(128&a),r.showBorderUnselectedTables=!!(4&a),r.showInkAnnotation=!!(32&a),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(32768&a),r.updateLinks=["userSet","never","always"][a>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=Oa(e),r.name=pa(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:Fa},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Uc},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:pa(e)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:Oa},357:{},358:{},359:{},360:{T:1},361:{},362:{f:xs},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var a=e.l+t,n=Da(e),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var o=Zo(e,a-e.l,r);i[1]=o}else e.l=a;return i}},427:{f:function(e,t,r){var a=e.l+t,n=[Fa(e)];if(r.cellFormula){var s=tc(e,a-e.l,r);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return Wc.forEach((function(r){t[r]=La(e)})),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,a=Fa(e),n=Aa(e),s=pa(e),i=pa(e),o=pa(e);e.l=r;var c={rfx:a,relId:n,loc:s,display:o};return i&&(c.Tooltip=i),c}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:Oa},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:ho},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=Fa(e);return t.rfx=r.s,t.ref=na(r.s),e.l+=16,t}},636:{T:-1},637:{f:ba},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:pa(e)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},Zl={6:{f:Ko},10:{f:Mn},12:{f:Wn},13:{f:Wn},14:{f:Un},15:{f:Un},16:{f:La},17:{f:Un},18:{f:Un},19:{f:Wn},20:{f:ys},21:{f:ys},23:{f:xs},24:{f:As},25:{f:Un},26:{},27:{},28:{f:function(e,t,r){return function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),o=Kn(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},o,i,s]}}(e,0,r)}},29:{},34:{f:Un},35:{f:ks},38:{f:La},39:{f:La},40:{f:La},41:{f:La},42:{f:Un},43:{f:Un},47:{f:function(e,t,r){var a={Type:r.biff>=8?e.read_shift(2):0};return a.Type?pi(e,t-2,a):function(e,t,r,a){var n={key:Wn(e),verificationBytes:Wn(e)};r.password&&(n.verifier=hi(r.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=di(r.password))}(e,r.biff,r,a),a}},49:{f:function(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=Gn(e,0,r),a}},51:{f:Wn},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:Un},65:{f:function(){}},66:{f:Wn},77:{},80:{},81:{},82:{},85:{f:Wn},89:{},90:{},91:{},92:{f:function(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=Kn(e,0,r);return e.read_shift(t+a-e.l),n}},93:{f:function(e,t,r){if(r&&r.biff<8)return function(e,t,r){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var i=[];return i.push((Os[a]||Gr)(e,t,r)),{cmo:[n,a,s],ft:i}}(e,t,r);var a=hs(e),n=function(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(ds[n](e,r-e.l))}catch(s){return e.l=r,a}}return e.l!=r&&(e.l=r),a}(e,t-22,a[1]);return{cmo:a,ft:n}}},94:{},95:{f:Un},96:{},97:{},99:{f:Un},125:{f:Ns},128:{f:function(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(0!==t[0]&&t[0]--,0!==t[1]&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}},129:{f:function(e,t,r){var a=r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:Wn},131:{f:Un},132:{f:Un},133:{f:function(e,t,r){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}var i=Gn(e,0,r);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}},134:{},140:{f:function(e){var t,r=[0,0];return t=e.read_shift(2),r[0]=Ga[t]||t,t=e.read_shift(2),r[1]=Ga[t]||t,r}},141:{f:Wn},144:{},146:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(rs(e));return r}},151:{},152:{},153:{},154:{},155:{},156:{f:Wn},157:{},158:{},160:{f:Fs},161:{f:function(e,t){var r={};return t<32||(e.l+=16,r.header=La(e),r.footer=La(e),e.l+=2),r}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(is(e));if(e.l!==r)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:Mn},197:{},198:{},199:{},200:{},201:{},202:{f:Un},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:Wn},220:{},221:{f:Un},222:{},224:{f:function(e,t,r){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,a.data=function(e,t,r,a){var n={},s=e.read_shift(4),i=e.read_shift(4),o=e.read_shift(4),c=e.read_shift(2);return n.patternType=ja[o>>26],a.cellStyles?(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&o,n.icvBottom=o>>7&127,n.icvDiag=o>>14&127,n.dgDiag=o>>21&15,n.icvFore=127&c,n.icvBack=c>>7&127,n.fsxButton=c>>14&1,n):n}(e,0,a.fStyle,r),a}},225:{f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{f:Mn},227:{},229:{f:function(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(os(e));return r}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(jn(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e){var t=as(e);return t.isst=e.read_shift(4),t}},255:{f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:Vn},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:Un},353:{f:Mn},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=$n(e,s),o=[];a>e.l;)o.push(Yn(e));return[s,n,i,o]}},431:{f:Un},432:{},433:{},434:{},437:{},438:{f:function(e,t,r){var a=e.l,n="";try{e.l+=4;var s=(r.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(s)?e.l+=6:function(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[t,r]}(e);var i=e.read_shift(2);e.read_shift(2),Wn(e);var o=e.read_shift(2);e.l+=o;for(var c=1;c<e.lens.length-1;++c){if(e.l-a!=e.lens[c])throw new Error("TxO: bad continue record");var l=e[e.l];if((n+=$n(e,e.lens[c+1]-e.lens[c]-1)).length>=(l?i:2*i))break}if(n.length!==i&&n.length!==2*i)throw new Error("cchText: "+i+" != "+n.length);return e.l=a+t,{t:n}}catch(f){return e.l=a+t,{t:n}}}},439:{f:Un},440:{f:function(e,t){var r=os(e);e.l+=16;var a=function(e,t){var r=e.l+t,a=e.read_shift(4);if(2!==a)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,o,c,l,f,h="";16&n&&(s=Zn(e,e.l)),128&n&&(i=Zn(e,e.l)),257&~n||(o=Zn(e,e.l)),1==(257&n)&&(c=qn(e,e.l)),8&n&&(h=Zn(e,e.l)),32&n&&(l=e.read_shift(16)),64&n&&(f=En(e)),e.l=r;var u=i||o||c||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return l&&(d.guid=l),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,t-24);return[r,a]}},441:{},442:{f:Yn},443:{},444:{f:Wn},445:{},446:{},448:{f:Mn},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:Mn},512:{f:Ts},513:{f:Ds},515:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=as(e),n=La(e);return a.val=n,a}},516:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5),e.l;var a=as(e);2==r.biff&&e.l++;var n=Yn(e,e.l,r);return a.val=n,a}},517:{f:Ss},519:{f:Ps},520:{f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(t.level=7&a),32&a&&(t.hidden=!0),64&a&&(t.hpt=r/20),t}},523:{},545:{f:Cs},549:{f:vs},566:{},574:{f:function(e,t,r){return r&&r.biff>=2&&r.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=is(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,t,r){return[e.read_shift(2),Kn(e,0,r)]}},1084:{},1212:{f:function(e,t,r){var a=ls(e);e.l++;var n=e.read_shift(1);return[Yo(e,t-=8,r),n,a]}},2048:{f:function(e,t){e.read_shift(2);var r=os(e),a=e.read_shift((t-10)/2,"dbcs-cont");return[r,a=a.replace(_,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:ps},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:Mn},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{f:function(e,t){e.l,e.l+=2;var r=e.read_shift(2);e.l+=2;for(var a=e.read_shift(2),n=[];a-- >0;)n.push(ao(e,e.l));return{ixfe:r,ext:n}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:Un,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2);return[$n(e,a,r),$n(e,n,r)]}e.l+=t},r:12},2197:{},2198:{f:function(e,t,r){var a=e.l+t;if(124226!==e.read_shift(4))if(r.cellStyles){var n,s=e.slice(e.l);e.l=a;try{n=at(s,{type:"array"})}catch(o){return}var i=Ze(n,"theme/theme/theme1.xml",!0);if(i)return eo(i,r)}else e.l=a},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:Mn},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var t=function(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}(e);if(2211!=t.type)throw new Error("Invalid Future Record "+t.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:Wn},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,t,r){var a={area:!1};if(5!=r.biff)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(rs(e));return r}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:Ts},1:{},2:{f:function(e){var t=as(e);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}},3:{f:function(e){var t=as(e);++e.l;var r=La(e);return t.t="n",t.val=r,t}},4:{f:function(e,t,r){r.biffguess&&5==r.biff&&(r.biff=2);var a=as(e);++e.l;var n=Kn(e,0,r);return a.t="str",a.val=n,a}},5:{f:Ss},7:{f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{},9:{f:ps},11:{},22:{f:Wn},30:{f:ws},31:{},32:{},33:{f:Cs},36:{},37:{f:vs},50:{f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},62:{},52:{},67:{},68:{f:Wn},69:{},86:{},126:{},127:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,t,r){var a=e.l+t,n=as(e),s=e.read_shift(2),i=$n(e,s,r);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:Ko},521:{f:ps},536:{f:As},547:{f:ks},561:{},579:{},1030:{f:Ko},1033:{f:ps},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function Ql(e,t,r,a){var n=t;if(!isNaN(n)){var s=a||(r||[]).length||0,i=e.next(4);i.write_shift(2,n),i.write_shift(2,s),s>0&&Rr(r)&&e.push(r)}}function ef(e,t,r){return e||(e=jr(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function tf(e,t,r,a){if(null!=t.v)switch(t.t){case"d":case"n":var n="d"==t.t?Re(Be(t.v)):t.v;return void(n==(0|n)&&n>=0&&n<65536?Ql(e,2,(s=r,i=a,o=n,c=jr(9),ef(c,s,i),c.write_shift(2,o),c)):Ql(e,3,function(e,t,r){var a=jr(15);return ef(a,e,t),a.write_shift(8,r,"f"),a}(r,a,n)));case"b":case"e":return void Ql(e,5,function(e,t,r,a){var n=jr(9);return ef(n,e,t),zn(r,a||"b",n),n}(r,a,t.v,t.t));case"s":case"str":return void Ql(e,4,function(e,t,r){var a=jr(8+2*r.length);return ef(a,e,t),a.write_shift(1,r.length),a.write_shift(r.length,r,"sbcs"),a.l<a.length?a.slice(0,a.l):a}(r,a,(t.v||"").slice(0,255)))}var s,i,o,c;Ql(e,1,ef(null,r,a))}function rf(e,t){for(var r=t||{},a=$r(),n=0,s=0;s<e.SheetNames.length;++s)e.SheetNames[s]==r.sheet&&(n=s);if(0==n&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return Ql(a,4==r.biff?1033:3==r.biff?521:9,ms(0,16,r)),function(e,t,r,a){var n,s=Array.isArray(t),i=oa(t["!ref"]||"A1"),o="",c=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),n=ia(i)}for(var l=i.s.r;l<=i.e.r;++l){o=ea(l);for(var f=i.s.c;f<=i.e.c;++f){l===i.s.r&&(c[f]=ra(f)),n=c[f]+o;var h=s?(t[l]||[])[f]:t[n];h&&tf(e,h,l,f)}}}(a,e.Sheets[e.SheetNames[n]],0,r),Ql(a,10),a.end()}function af(e,t,r){Ql(e,49,function(e,t){var r=e.name||"Arial",a=t&&5==t.biff,n=jr(a?15+r.length:16+2*r.length);return n.write_shift(2,20*(e.sz||12)),n.write_shift(4,0),n.write_shift(2,400),n.write_shift(4,0),n.write_shift(2,0),n.write_shift(1,r.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*r.length,r,a?"sbcs":"utf16le"),n}({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function nf(e,t){if(t){var r=0;t.forEach((function(t,a){++r<=256&&t&&Ql(e,125,function(e,t){var r=jr(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,256*e.width),r.write_shift(2,0);var a=0;return e.hidden&&(a|=1),r.write_shift(1,a),a=e.level||0,r.write_shift(1,a),r.write_shift(2,0),r}(hc(a,t),a))}))}}function sf(e,t,r,a,n){var s=16+dc(n.cellXfs,t,n);if(null!=t.v||t.bf)if(t.bf)Ql(e,6,Jo(t,r,a,0,s));else switch(t.t){case"d":case"n":Ql(e,515,function(e,t,r,a){var n=jr(14);return ns(e,t,a,n),Ma(r,n),n}(r,a,"d"==t.t?Re(Be(t.v)):t.v,s));break;case"b":case"e":Ql(e,517,function(e,t,r,a,n,s){var i=jr(8);return ns(e,t,a,i),zn(r,s,i),i}(r,a,t.v,s,0,t.t));break;case"s":case"str":if(n.bookSST)Ql(e,253,function(e,t,r,a){var n=jr(10);return ns(e,t,a,n),n.write_shift(4,r),n}(r,a,fc(n.Strings,t.v,n.revStrings),s));else Ql(e,516,function(e,t,r,a,n){var s=!n||8==n.biff,i=jr(+s+8+(1+s)*r.length);return ns(e,t,a,i),i.write_shift(2,r.length),s&&i.write_shift(1,1),i.write_shift((1+s)*r.length,r,s?"utf16le":"sbcs"),i}(r,a,(t.v||"").slice(0,255),s,n));break;default:Ql(e,513,ns(r,a,s))}else Ql(e,513,ns(r,a,s))}function of(e,t,r){var a,n,s,i=$r(),o=r.SheetNames[e],c=r.Sheets[o]||{},l=(r||{}).Workbook||{},f=(l.Sheets||[])[e]||{},h=Array.isArray(c),u=8==t.biff,d="",p=[],m=oa(c["!ref"]||"A1"),g=u?65536:16384;if(m.e.c>255||m.e.r>=g){if(t.WTF)throw new Error("Range "+(c["!ref"]||"A1")+" exceeds format limit A1:IV16384");m.e.c=Math.min(m.e.c,255),m.e.r=Math.min(m.e.c,g-1)}Ql(i,2057,ms(0,16,t)),Ql(i,13,Hn(1)),Ql(i,12,Hn(100)),Ql(i,15,Bn(!0)),Ql(i,17,Bn(!1)),Ql(i,16,Ma(.001)),Ql(i,95,Bn(!0)),Ql(i,42,Bn(!1)),Ql(i,43,Bn(!1)),Ql(i,130,Hn(1)),Ql(i,128,(n=[0,0],(s=jr(8)).write_shift(4,0),s.write_shift(2,n[0]?n[0]+1:0),s.write_shift(2,n[1]?n[1]+1:0),s)),Ql(i,131,Bn(!1)),Ql(i,132,Bn(!1)),u&&nf(i,c["!cols"]),Ql(i,512,function(e,t){var r=8!=t.biff&&t.biff?2:4,a=jr(2*r+6);return a.write_shift(r,e.s.r),a.write_shift(r,e.e.r+1),a.write_shift(2,e.s.c),a.write_shift(2,e.e.c+1),a.write_shift(2,0),a}(m,t)),u&&(c["!links"]=[]);for(var v=m.s.r;v<=m.e.r;++v){d=ea(v);for(var b=m.s.c;b<=m.e.c;++b){v===m.s.r&&(p[b]=ra(b)),a=p[b]+d;var w=h?(c[v]||[])[b]:c[a];w&&(sf(i,w,v,b,t),u&&w.l&&c["!links"].push([a,w.l]))}}var T=f.CodeName||f.name||o;return u&&Ql(i,574,function(e){var t=jr(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}((l.Views||[])[0])),u&&(c["!merges"]||[]).length&&Ql(i,229,function(e){var t=jr(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)cs(e[r],t);return t}(c["!merges"])),u&&function(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];Ql(e,440,Rs(a)),a[1].Tooltip&&Ql(e,2048,Is(a))}delete t["!links"]}(i,c),Ql(i,442,Jn(T)),u&&function(e,t){var r=jr(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),Ql(e,2151,r),(r=jr(39)).write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),cs(oa(t["!ref"]||"A1"),r),r.write_shift(4,4),Ql(e,2152,r)}(i,c),Ql(i,10),i.end()}function cf(e,t,r){var a,n=$r(),s=(e||{}).Workbook||{},i=s.Sheets||[],o=s.WBProps||{},c=8==r.biff,l=5==r.biff;(Ql(n,2057,ms(0,5,r)),"xla"==r.bookType&&Ql(n,135),Ql(n,225,c?Hn(1200):null),Ql(n,193,function(e,t){t||(t=jr(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}(2)),l&&Ql(n,191),l&&Ql(n,192),Ql(n,226),Ql(n,92,function(e,t){var r=!t||8==t.biff,a=jr(r?112:54);for(a.write_shift(8==t.biff?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,859007059),a.write_shift(4,5458548|(r?0:536870912));a.l<a.length;)a.write_shift(1,r?0:32);return a}(0,r)),Ql(n,66,Hn(c?1200:1252)),c&&Ql(n,353,Hn(0)),c&&Ql(n,448),Ql(n,317,function(e){for(var t=jr(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),c&&e.vbaraw&&Ql(n,211),c&&e.vbaraw)&&Ql(n,442,Jn(o.CodeName||"ThisWorkbook"));Ql(n,156,Hn(17)),Ql(n,25,Bn(!1)),Ql(n,18,Bn(!1)),Ql(n,19,Hn(0)),c&&Ql(n,431,Bn(!1)),c&&Ql(n,444,Hn(0)),Ql(n,61,((a=jr(18)).write_shift(2,0),a.write_shift(2,0),a.write_shift(2,29280),a.write_shift(2,17600),a.write_shift(2,56),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,1),a.write_shift(2,500),a)),Ql(n,64,Bn(!1)),Ql(n,141,Hn(0)),Ql(n,34,Bn("true"==function(e){return e.Workbook&&e.Workbook.WBProps&&yt(e.Workbook.WBProps.date1904)?"true":"false"}(e))),Ql(n,14,Bn(!0)),c&&Ql(n,439,Bn(!1)),Ql(n,218,Hn(0)),af(n,0,r),function(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach((function(a){for(var n=a[0];n<=a[1];++n)null!=t[n]&&Ql(e,1054,bs(n,t[n],r))}))}(n,e.SSF,r),function(e,t){for(var r=0;r<16;++r)Ql(e,224,Es({numFmtId:0,style:!0},0,t));t.cellXfs.forEach((function(r){Ql(e,224,Es(r,0,t))}))}(n,r),c&&Ql(n,352,Bn(!1));var f=n.end(),h=$r();c&&Ql(h,140,function(e){return e||(e=jr(4)),e.write_shift(2,1),e.write_shift(2,1),e}()),c&&r.Strings&&function(e,t,r,a){var n=a||(r||[]).length||0;if(n<=8224)return Ql(e,t,r,n);var s=t;if(!isNaN(s)){for(var i=r.parts||[],o=0,c=0,l=0;l+(i[o]||8224)<=8224;)l+=i[o]||8224,o++;var f=e.next(4);for(f.write_shift(2,s),f.write_shift(2,l),e.push(r.slice(c,c+l)),c+=l;c<n;){for((f=e.next(4)).write_shift(2,60),l=0;l+(i[o]||8224)<=8224;)l+=i[o]||8224,o++;f.write_shift(2,l),e.push(r.slice(c,c+l)),c+=l}}}(h,252,function(e){var t=jr(8);t.write_shift(4,e.Count),t.write_shift(4,e.Unique);for(var r=[],a=0;a<e.length;++a)r[a]=Xn(e[a]);var n=k([t].concat(r));return n.parts=[t.length].concat(r.map((function(e){return e.length}))),n}(r.Strings)),Ql(h,10);var u=h.end(),d=$r(),p=0,m=0;for(m=0;m<e.SheetNames.length;++m)p+=(c?12:11)+(c?2:1)*e.SheetNames[m].length;var g=f.length+p+u.length;for(m=0;m<e.SheetNames.length;++m){Ql(d,133,gs({pos:g,hs:(i[m]||{}).Hidden||0,dt:0,name:e.SheetNames[m]},r)),g+=t[m].length}var v=d.end();if(p!=v.length)throw new Error("BS8 "+p+" != "+v.length);var b=[];return f.length&&b.push(f),v.length&&b.push(v),u.length&&b.push(u),k(b)}function lf(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];if(a&&a["!ref"])sa(a["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var n=t||{};switch(n.biff||2){case 8:case 5:return function(e,t){var r=t||{},a=[];e&&!e.SSF&&(e.SSF=He(L)),e&&e.SSF&&(be(),ve(e.SSF),r.revssf=Ce(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,Zf(r),r.cellXfs=[],dc(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var n=0;n<e.SheetNames.length;++n)a[a.length]=of(n,r,e);return a.unshift(cf(e,a,r)),k(a)}(e,t);case 4:case 3:case 2:return rf(e,t)}throw new Error("invalid type "+n.bookType+" for BIFF")}function ff(e,t){var r=t||{},a=r.dense?[]:{},n=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,o=s&&s.index||e.length,c=Xe(e.slice(i,o),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<c.length;++i){var m=c[i].trim(),g=m.slice(0,3).toLowerCase();if("<tr"!=g){if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(o=0;o<v.length;++o){var b=v[o].trim();if(b.match(/<t[dh]/i)){for(var w=b,T=0;"<"==w.charAt(0)&&(T=w.indexOf(">"))>-1;)w=w.slice(T+1);for(var E=0;E<p.length;++E){var S=p[E];S.s.c==f&&S.s.r<l&&l<=S.e.r&&(f=S.e.c+1,E=-1)}var y=ht(b.slice(0,b.indexOf(">")));u=y.colspan?+y.colspan:1,((h=+y.rowspan)>1||u>1)&&p.push({s:{r:l,c:f},e:{r:l+(h||1)-1,c:f+u-1}});var k=y.t||y["data-t"]||"";if(w.length)if(w=It(w),d.s.r>l&&(d.s.r=l),d.e.r<l&&(d.e.r=l),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),w.length){var _={t:"s",v:w};r.raw||!w.trim().length||"s"==k||("TRUE"===w?_={t:"b",v:!0}:"FALSE"===w?_={t:"b",v:!1}:isNaN(ze(w))?isNaN(je(w).getDate())||(_={t:"d",v:Be(w)},r.cellDates||(_={t:"n",v:Re(_.v)}),_.z=r.dateNF||L[14]):_={t:"n",v:ze(w)}),r.dense?(a[l]||(a[l]=[]),a[l][f]=_):a[na({r:l,c:f})]=_,f+=u}else f+=u;else f+=u}}}}else{if(++l,r.sheetRows&&r.sheetRows<=l){--l;break}f=0}}return a["!ref"]=ia(d),p.length&&(a["!merges"]=p),a}function hf(e,t,r,a){for(var n=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var o=0,c=0,l=0;l<n.length;++l)if(!(n[l].s.r>r||n[l].s.c>i||n[l].e.r<r||n[l].e.c<i)){if(n[l].s.r<r||n[l].s.c<i){o=-1;break}o=n[l].e.r-n[l].s.r+1,c=n[l].e.c-n[l].s.c+1;break}if(!(o<0)){var f=na({r:r,c:i}),h=a.dense?(e[r]||[])[i]:e[f],u=h&&null!=h.v&&(h.h||Et(h.w||(la(h),h.w)||""))||"",d={};o>1&&(d.rowspan=o),c>1&&(d.colspan=c),a.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push(Bt("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}function uf(e,t){var r=t||{},a=null!=r.header?r.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',n=null!=r.footer?r.footer:"</body></html>",s=[a],i=sa(e["!ref"]);r.dense=Array.isArray(e),s.push(function(e,t,r){return[].join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}(0,0,r));for(var o=i.s.r;o<=i.e.r;++o)s.push(hf(e,i,o,r));return s.push("</table>"+n),s.join("")}function df(e,t,r){var a=r||{},n=0,s=0;if(null!=a.origin)if("number"==typeof a.origin)n=a.origin;else{var i="string"==typeof a.origin?aa(a.origin):a.origin;n=i.r,s=i.c}var o=t.getElementsByTagName("tr"),c=Math.min(a.sheetRows||1e7,o.length),l={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=sa(e["!ref"]);l.s.r=Math.min(l.s.r,f.s.r),l.s.c=Math.min(l.s.c,f.s.c),l.e.r=Math.max(l.e.r,f.e.r),l.e.c=Math.max(l.e.c,f.e.c),-1==n&&(l.e.r=n=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,b=0,w=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<c;++p){var T=o[p];if(mf(T)){if(a.display)continue;d[m]={hidden:!0}}var E=T.children;for(g=v=0;g<E.length;++g){var S=E[g];if(!a.display||!mf(S)){var y=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):It(S.innerHTML),k=S.getAttribute("data-z")||S.getAttribute("z");for(u=0;u<h.length;++u){var _=h[u];_.s.c==v+s&&_.s.r<m+n&&m+n<=_.e.r&&(v=_.e.c+1-s,u=-1)}w=+S.getAttribute("colspan")||1,((b=+S.getAttribute("rowspan")||1)>1||w>1)&&h.push({s:{r:m+n,c:v+s},e:{r:m+n+(b||1)-1,c:v+s+(w||1)-1}});var A={t:"s",v:y},x=S.getAttribute("data-t")||S.getAttribute("t")||"";null!=y&&(0==y.length?A.t=x||"z":a.raw||0==y.trim().length||"s"==x||("TRUE"===y?A={t:"b",v:!0}:"FALSE"===y?A={t:"b",v:!1}:isNaN(ze(y))?isNaN(je(y).getDate())||(A={t:"d",v:Be(y)},a.cellDates||(A={t:"n",v:Re(A.v)}),A.z=a.dateNF||L[14]):A={t:"n",v:ze(y)})),void 0===A.z&&null!=k&&(A.z=k);var C="",O=S.getElementsByTagName("A");if(O&&O.length)for(var R=0;R<O.length&&(!O[R].hasAttribute("href")||"#"==(C=O[R].getAttribute("href")).charAt(0));++R);C&&"#"!=C.charAt(0)&&(A.l={Target:C}),a.dense?(e[m+n]||(e[m+n]=[]),e[m+n][v+s]=A):e[na({c:v+s,r:m+n})]=A,l.e.c<v+s&&(l.e.c=v+s),v+=w}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),l.e.r=Math.max(l.e.r,m-1+n),e["!ref"]=ia(l),m>=c&&(e["!fullref"]=ia((l.e.r=o.length-p+m-1+n,l))),e}function pf(e,t){return df((t||{}).dense?[]:{},e,t)}function mf(e){var t="",r=function(e){return e.ownerDocument.defaultView&&"function"==typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null}(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),"none"===t}var gf={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function vf(e,t){var r,a,n,s,i,o,c,l,f=t||{},h=Ht(e),u=[],d={name:""},p="",m=0,g={},v=[],b=f.dense?[]:{},w={value:""},T="",E=0,S=[],y=-1,k=-1,_={s:{r:1e6,c:1e7},e:{r:0,c:0}},A=0,x={},C=[],O={},R=[],I=1,N=1,D=[],F={Names:[]},P={},L=["",""],M=[],U={},B="",W=0,H=!1,V=!1,z=0;for(Vt.lastIndex=0,h=h.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");i=Vt.exec(h);)switch(i[3]=i[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===i[1]?(_.e.c>=_.s.c&&_.e.r>=_.s.r?b["!ref"]=ia(_):b["!ref"]="A1:A1",f.sheetRows>0&&f.sheetRows<=_.e.r&&(b["!fullref"]=b["!ref"],_.e.r=f.sheetRows-1,b["!ref"]=ia(_)),C.length&&(b["!merges"]=C),R.length&&(b["!rows"]=R),n.name=n["名称"]||n.name,"undefined"!=typeof JSON&&JSON.stringify(n),v.push(n.name),g[n.name]=b,V=!1):"/"!==i[0].charAt(i[0].length-2)&&(n=ht(i[0],!1),y=k=-1,_.s.r=_.s.c=1e7,_.e.r=_.e.c=0,b=f.dense?[]:{},C=[],R=[],V=!0);break;case"table-row-group":"/"===i[1]?--A:++A;break;case"table-row":case"行":if("/"===i[1]){y+=I,I=1;break}if((s=ht(i[0],!1))["行号"]?y=s["行号"]-1:-1==y&&(y=0),(I=+s["number-rows-repeated"]||1)<10)for(z=0;z<I;++z)A>0&&(R[y+z]={level:A});k=-1;break;case"covered-table-cell":"/"!==i[1]&&++k,f.sheetStubs&&(f.dense?(b[y]||(b[y]=[]),b[y][k]={t:"z"}):b[na({r:y,c:k})]={t:"z"}),T="",S=[];break;case"table-cell":case"数据":if("/"===i[0].charAt(i[0].length-2))++k,w=ht(i[0],!1),N=parseInt(w["number-columns-repeated"]||"1",10),o={t:"z",v:null},w.formula&&0!=f.cellFormula&&(o.f=sc(mt(w.formula))),"string"==(w["数据类型"]||w["value-type"])&&(o.t="s",o.v=mt(w["string-value"]||""),f.dense?(b[y]||(b[y]=[]),b[y][k]=o):b[na({r:y,c:k})]=o),k+=N-1;else if("/"!==i[1]){T="",E=0,S=[],N=1;var G=I?y+I-1:y;if(++k>_.e.c&&(_.e.c=k),k<_.s.c&&(_.s.c=k),y<_.s.r&&(_.s.r=y),G>_.e.r&&(_.e.r=G),M=[],U={},o={t:(w=ht(i[0],!1))["数据类型"]||w["value-type"],v:null},f.cellFormula)if(w.formula&&(w.formula=mt(w.formula)),w["number-matrix-columns-spanned"]&&w["number-matrix-rows-spanned"]&&(O={s:{r:y,c:k},e:{r:y+(parseInt(w["number-matrix-rows-spanned"],10)||0)-1,c:k+(parseInt(w["number-matrix-columns-spanned"],10)||0)-1}},o.F=ia(O),D.push([O,o.F])),w.formula)o.f=sc(w.formula);else for(z=0;z<D.length;++z)y>=D[z][0].s.r&&y<=D[z][0].e.r&&k>=D[z][0].s.c&&k<=D[z][0].e.c&&(o.F=D[z][1]);switch((w["number-columns-spanned"]||w["number-rows-spanned"])&&(O={s:{r:y,c:k},e:{r:y+(parseInt(w["number-rows-spanned"],10)||0)-1,c:k+(parseInt(w["number-columns-spanned"],10)||0)-1}},C.push(O)),w["number-columns-repeated"]&&(N=parseInt(w["number-columns-repeated"],10)),o.t){case"boolean":o.t="b",o.v=yt(w["boolean-value"]);break;case"float":case"percentage":case"currency":o.t="n",o.v=parseFloat(w.value);break;case"date":o.t="d",o.v=Be(w["date-value"]),f.cellDates||(o.t="n",o.v=Re(o.v)),o.z="m/d/yy";break;case"time":o.t="n",o.v=Pe(w["time-value"])/86400,f.cellDates&&(o.t="d",o.v=Fe(o.v)),o.z="HH:MM:SS";break;case"number":o.t="n",o.v=parseFloat(w["数据数值"]);break;default:if("string"!==o.t&&"text"!==o.t&&o.t)throw new Error("Unsupported value type "+o.t);o.t="s",null!=w["string-value"]&&(T=mt(w["string-value"]),S=[])}}else{if(H=!1,"s"===o.t&&(o.v=T||"",S.length&&(o.R=S),H=0==E),P.Target&&(o.l=P),M.length>0&&(o.c=M,M=[]),T&&!1!==f.cellText&&(o.w=T),H&&(o.t="z",delete o.v),(!H||f.sheetStubs)&&!(f.sheetRows&&f.sheetRows<=y))for(var j=0;j<I;++j){if(N=parseInt(w["number-columns-repeated"]||"1",10),f.dense)for(b[y+j]||(b[y+j]=[]),b[y+j][k]=0==j?o:He(o);--N>0;)b[y+j][k+N]=He(o);else for(b[na({r:y+j,c:k})]=o;--N>0;)b[na({r:y+j,c:k+N})]=He(o);_.e.c<=k&&(_.e.c=k)}k+=(N=parseInt(w["number-columns-repeated"]||"1",10))-1,N=0,o={},T="",S=[]}P={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===i[1]){if((r=u.pop())[0]!==i[3])throw"Bad state: "+r}else"/"!==i[0].charAt(i[0].length-2)&&u.push([i[3],!0]);break;case"annotation":if("/"===i[1]){if((r=u.pop())[0]!==i[3])throw"Bad state: "+r;U.t=T,S.length&&(U.R=S),U.a=B,M.push(U)}else"/"!==i[0].charAt(i[0].length-2)&&u.push([i[3],!1]);B="",W=0,T="",E=0,S=[];break;case"creator":"/"===i[1]?B=h.slice(W,i.index):W=i.index+i[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===i[1]){if((r=u.pop())[0]!==i[3])throw"Bad state: "+r}else"/"!==i[0].charAt(i[0].length-2)&&u.push([i[3],!1]);T="",E=0,S=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===i[1]){if(x[d.name]=p,(r=u.pop())[0]!==i[3])throw"Bad state: "+r}else"/"!==i[0].charAt(i[0].length-2)&&(p="",d=ht(i[0],!1),u.push([i[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(u[u.length-1][0]){case"time-style":case"date-style":a=ht(i[0],!1),p+=gf[i[3]]["long"===a.style?1:0]}break;case"text":if("/>"===i[0].slice(-2))break;if("/"===i[1])switch(u[u.length-1][0]){case"number-style":case"date-style":case"time-style":p+=h.slice(m,i.index)}else m=i.index+i[0].length;break;case"named-range":L=ic((a=ht(i[0],!1))["cell-range-address"]);var X={Name:a.name,Ref:L[0]+"!"+L[1]};V&&(X.Sheet=v.length),F.Names.push(X);break;case"p":case"文本串":if(["master-styles"].indexOf(u[u.length-1][0])>-1)break;if("/"!==i[1]||w&&w["string-value"])ht(i[0],!1),E=i.index+i[0].length;else{var $=(c=h.slice(E,i.index),l=void 0,l=c.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,(function(e,t){return Array(parseInt(t,10)+1).join(" ")})).replace(/<text:tab[^>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n"),[mt(l.replace(/<[^>]*>/g,""))]);T=(T.length>0?T+"\n":"")+$[0]}break;case"database-range":if("/"===i[1])break;try{g[(L=ic(ht(i[0])["target-range-address"]))[0]]["!autofilter"]={ref:L[1]}}catch(K){}break;case"a":if("/"!==i[1]){if(!(P=ht(i[0],!1)).href)break;P.Target=mt(P.href),delete P.href,"#"==P.Target.charAt(0)&&P.Target.indexOf(".")>-1?(L=ic(P.Target.slice(1)),P.Target="#"+L[0]+"!"+L[1]):P.Target.match(/^\.\.[\\\/]/)&&(P.Target=P.Target.slice(3))}break;default:switch(i[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(f.WTF)throw new Error(i)}}var Y={Sheets:g,SheetNames:v,Workbook:F};return f.bookSheets&&delete Y.Sheets,Y}function bf(e,t){t=t||{},Ke(e,"META-INF/manifest.xml")&&function(e,t){for(var r,a,n=Ht(e);r=Vt.exec(n);)switch(r[3]){case"manifest":break;case"file-entry":if("/"==(a=ht(r[0],!1)).path&&"application/vnd.oasis.opendocument.spreadsheet"!==a.type)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw r}}(qe(e,"META-INF/manifest.xml"),t);var r=Ze(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var a=vf(Ct(r),t);return Ke(e,"meta.xml")&&(a.Props=ln(qe(e,"meta.xml"))),a}function wf(e,t){return vf(e,t)}var Tf=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Ut({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return st+t}}(),Ef=function(){var e="          <table:table-cell />\n",t=function(t,r,a){var n=[];n.push('      <table:table table:name="'+bt(r.SheetNames[a])+'" table:style-name="ta1">\n');var s=0,i=0,o=sa(t["!ref"]||"A1"),c=t["!merges"]||[],l=0,f=Array.isArray(t);if(t["!cols"])for(i=0;i<=o.e.c;++i)n.push("        <table:table-column"+(t["!cols"][i]?' table:style-name="co'+t["!cols"][i].ods+'"':"")+"></table:table-column>\n");var h="",u=t["!rows"]||[];for(s=0;s<o.s.r;++s)h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+"></table:table-row>\n");for(;s<=o.e.r;++s){for(h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+">\n"),i=0;i<o.s.c;++i)n.push(e);for(;i<=o.e.c;++i){var d=!1,p={},m="";for(l=0;l!=c.length;++l)if(!(c[l].s.c>i||c[l].s.r>s||c[l].e.c<i||c[l].e.r<s)){c[l].s.c==i&&c[l].s.r==s||(d=!0),p["table:number-columns-spanned"]=c[l].e.c-c[l].s.c+1,p["table:number-rows-spanned"]=c[l].e.r-c[l].s.r+1;break}if(d)n.push("          <table:covered-table-cell/>\n");else{var g=na({r:s,c:i}),v=f?(t[s]||[])[i]:t[g];if(v&&v.f&&(p["table:formula"]=bt(("of:="+v.f.replace(go,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),v.F&&v.F.slice(0,g.length)==g)){var b=sa(v.F);p["table:number-matrix-columns-spanned"]=b.e.c-b.s.c+1,p["table:number-matrix-rows-spanned"]=b.e.r-b.s.r+1}if(v){switch(v.t){case"b":m=v.v?"TRUE":"FALSE",p["office:value-type"]="boolean",p["office:boolean-value"]=v.v?"true":"false";break;case"n":m=v.w||String(v.v||0),p["office:value-type"]="float",p["office:value"]=v.v||0;break;case"s":case"str":m=null==v.v?"":v.v,p["office:value-type"]="string";break;case"d":m=v.w||Be(v.v).toISOString(),p["office:value-type"]="date",p["office:date-value"]=Be(v.v).toISOString(),p["table:style-name"]="ce1";break;default:n.push(e);continue}var w=bt(m).replace(/  +/g,(function(e){return'<text:s text:c="'+e.length+'"/>'})).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(v.l&&v.l.Target){var T=v.l.Target;"#"==(T="#"==T.charAt(0)?"#"+T.slice(1).replace(/\./,"!"):T).charAt(0)||T.match(/^\w+:/)||(T="../"+T),w=Bt("text:a",w,{"xlink:href":T.replace(/&/g,"&amp;")})}n.push("          "+Bt("table:table-cell",Bt("text:p",w,{}),p)+"\n")}else n.push(e)}}n.push("        </table:table-row>\n")}return n.push("      </table:table>\n"),n.join("")};return function(e,r){var a=[st],n=Ut({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),s=Ut({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==r.bookType?(a.push("<office:document"+n+s+">\n"),a.push(sn().replace(/office:document-meta/g,"office:meta"))):a.push("<office:document-content"+n+">\n"),function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!cols"])for(var a=0;a<t["!cols"].length;++a)if(t["!cols"][a]){var n=t["!cols"][a];if(null==n.width&&null==n.wpx&&null==n.wch)continue;ki(n),n.ods=r;var s=t["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+s+'"/>\n'),e.push("  </style:style>\n"),++r}}));var a=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!rows"])for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=a;var n=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n'),e.push("  </style:style>\n"),++a}})),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")}(a,e),a.push("  <office:body>\n"),a.push("    <office:spreadsheet>\n");for(var i=0;i!=e.SheetNames.length;++i)a.push(t(e.Sheets[e.SheetNames[i]],e,i));return a.push("    </office:spreadsheet>\n"),a.push("  </office:body>\n"),"fods"==r.bookType?a.push("</office:document>"):a.push("</office:document-content>"),a.join("")}}();function Sf(e,t){if("fods"==t.bookType)return Ef(e,t);var r=rt(),a="",n=[],s=[];return tt(r,a="mimetype","application/vnd.oasis.opendocument.spreadsheet"),tt(r,a="content.xml",Ef(e,t)),n.push([a,"text/xml"]),s.push([a,"ContentFile"]),tt(r,a="styles.xml",Tf(e,t)),n.push([a,"text/xml"]),s.push([a,"StylesFile"]),tt(r,a="meta.xml",st+sn()),n.push([a,"text/xml"]),s.push([a,"MetadataFile"]),tt(r,a="manifest.rdf",function(e){var t,r,a=[st];a.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var n=0;n!=e.length;++n)a.push(nn(e[n][0],e[n][1])),a.push((t="",r=e[n][0],['  <rdf:Description rdf:about="'+t+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+r+'"/>\n',"  </rdf:Description>\n"].join("")));return a.push(nn("","Document","pkg")),a.push("</rdf:RDF>"),a.join("")}(s)),n.push([a,"application/rdf+xml"]),tt(r,a="META-INF/manifest.xml",function(e){var t=[st];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(n)),r}
/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function yf(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function kf(e){return"undefined"!=typeof TextDecoder?(new TextDecoder).decode(e):Ct(S(e))}function _f(e){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(e):T(Ot(e))}function Af(e){var t=e.reduce((function(e,t){return e+t.length}),0),r=new Uint8Array(t),a=0;return e.forEach((function(e){r.set(e,a),a+=e.length})),r}function xf(e){return 16843009*((e=(858993459&(e-=e>>1&1431655765))+(e>>2&858993459))+(e>>4)&252645135)>>>24}function Cf(e,t){var r=t?t[0]:0,a=127&e[r];e:if(e[r++]>=128){if(a|=(127&e[r])<<7,e[r++]<128)break e;if(a|=(127&e[r])<<14,e[r++]<128)break e;if(a|=(127&e[r])<<21,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,28),++r,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,35),++r,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,42),++r,e[r++]<128)break e}return t&&(t[0]=r),a}function Of(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383)break e;if(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)break e;if(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)break e;if(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)break e;if(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103)break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Rf(e){var t=0,r=127&e[t];e:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128)break e;if(r|=(127&e[t])<<14,e[t++]<128)break e;if(r|=(127&e[t])<<21,e[t++]<128)break e;r|=(127&e[t])<<28}return r}function If(e){for(var t=[],r=[0];r[0]<e.length;){var a,n=r[0],s=Cf(e,r),i=7&s,o=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var c=r[0];e[r[0]++]>=128;);a=e.slice(c,r[0]);break;case 5:o=4,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=Cf(e,r),a=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw new Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var l={data:a,type:i};null==t[s]?t[s]=[l]:t[s].push(l)}return t}function Nf(e){var t=[];return e.forEach((function(e,r){e.forEach((function(e){e.data&&(t.push(Of(8*r+e.type)),2==e.type&&t.push(Of(e.data.length)),t.push(e.data))}))})),Af(t)}function Df(e,t){return(null==e?void 0:e.map((function(e){return t(e.data)})))||[]}function Ff(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=Cf(e,a),s=If(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:Rf(s[1][0].data),messages:[]};s[2].forEach((function(t){var r=If(t.data),n=Rf(r[3][0].data);i.messages.push({meta:r,data:e.slice(a[0],a[0]+n)}),a[0]+=n})),(null==(t=s[3])?void 0:t[0])&&(i.merge=Rf(s[3][0].data)>>>0>0),r.push(i)}return r}function Pf(e){var t=[];return e.forEach((function(e){var r=[];r[1]=[{data:Of(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:Of(+!!e.merge),type:0}]);var a=[];e.messages.forEach((function(e){a.push(e.data),e.meta[3]=[{type:0,data:Of(e.data.length)}],r[2].push({data:Nf(e.meta),type:2})}));var n=Nf(r);t.push(Of(n.length)),t.push(n),a.forEach((function(e){return t.push(e)}))})),Af(t)}function Lf(e,t){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=Cf(t,r),n=[];r[0]<t.length;){var s=3&t[r[0]];if(0!=s){var i=0,o=0;if(1==s?(o=4+(t[r[0]]>>2&7),i=(224&t[r[0]++])<<3,i|=t[r[0]++]):(o=1+(t[r[0]++]>>2),2==s?(i=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(i=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[Af(n)],0==i)throw new Error("Invalid offset 0");if(i>n[0].length)throw new Error("Invalid offset beyond length");if(o>=i)for(n.push(n[0].slice(-i)),o-=i;o>=n[n.length-1].length;)n.push(n[n.length-1]),o-=n[n.length-1].length;n.push(n[0].slice(-i,-i+o))}else{var c=t[r[0]++]>>2;if(c<60)++c;else{var l=c-59;c=t[r[0]],l>1&&(c|=t[r[0]+1]<<8),l>2&&(c|=t[r[0]+2]<<16),l>3&&(c|=t[r[0]+3]<<24),c>>>=0,c++,r[0]+=l}n.push(t.slice(r[0],r[0]+c)),r[0]+=c}}var f=Af(n);if(f.length!=a)throw new Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}function Mf(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Lf(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw new Error("data is not a valid framed stream!");return Af(t)}function Uf(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,268435455),n=new Uint8Array(4);t.push(n);var s=Of(a),i=s.length;t.push(s),a<=60?(i++,t.push(new Uint8Array([a-1<<2]))):a<=256?(i+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(i+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=16777216?(i+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=4294967296&&(i+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),i+=a,n[0]=0,n[1]=255&i,n[2]=i>>8&255,n[3]=i>>16&255,r+=a}return Af(t)}function Bf(e,t,r){var a,n=yf(e),s=n.getUint32(8,!0),i=12,o=-1,c=-1,l=NaN,f=NaN,h=new Date(2001,0,1);switch(1&s&&(l=function(e,t){for(var r=(127&e[t+15])<<7|e[t+14]>>1,a=1&e[t+14],n=t+13;n>=t;--n)a=256*a+e[n];return(128&e[t+15]?-a:a)*Math.pow(10,r-6176)}(e,i),i+=16),2&s&&(f=n.getFloat64(i,!0),i+=8),4&s&&(h.setTime(h.getTime()+1e3*n.getFloat64(i,!0)),i+=8),8&s&&(c=n.getUint32(i,!0),i+=4),16&s&&(o=n.getUint32(i,!0),i+=4),e[1]){case 0:break;case 2:case 10:a={t:"n",v:l};break;case 3:a={t:"s",v:t[c]};break;case 5:a={t:"d",v:h};break;case 6:a={t:"b",v:f>0};break;case 7:a={t:"n",v:f/86400};break;case 8:a={t:"e",v:0};break;case 9:if(!(o>-1))throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)));a={t:"s",v:r[o]};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)))}return a}function Wf(e,t){var r=new Uint8Array(32),a=yf(r),n=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var a=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(127&a)<<1;for(var s=0;n>=1;++s,n/=256)e[t+s]=255&n;e[t+15]|=r>=0?0:128}(r,n,e.v),s|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,e.v?1:0,!0),s|=2,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,s,!0),r.slice(0,n)}function Hf(e,t){var r=new Uint8Array(32),a=yf(r),n=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),s|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,e.v?1:0,!0),s|=32,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,s,!0),r.slice(0,n)}function Vf(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return function(e,t,r,a){var n,s=yf(e),i=s.getUint32(4,!0),o=(a>1?12:8)+4*xf(i&(a>1?3470:398)),c=-1,l=-1,f=NaN,h=new Date(2001,0,1);switch(512&i&&(c=s.getUint32(o,!0),o+=4),o+=4*xf(i&(a>1?12288:4096)),16&i&&(l=s.getUint32(o,!0),o+=4),32&i&&(f=s.getFloat64(o,!0),o+=8),64&i&&(h.setTime(h.getTime()+1e3*s.getFloat64(o,!0)),o+=8),e[2]){case 0:break;case 2:n={t:"n",v:f};break;case 3:n={t:"s",v:t[l]};break;case 5:n={t:"d",v:h};break;case 6:n={t:"b",v:f>0};break;case 7:n={t:"n",v:f/86400};break;case 8:n={t:"e",v:0};break;case 9:if(c>-1)n={t:"s",v:r[c]};else if(l>-1)n={t:"s",v:t[l]};else{if(isNaN(f))throw new Error("Unsupported cell type ".concat(e.slice(0,4)));n={t:"n",v:f}}break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return n}(e,t,r,e[0]);case 5:return Bf(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function zf(e){return Cf(If(e)[1][0].data)}function Gf(e,t){var r=If(t.data),a=Rf(r[1][0].data),n=r[3],s=[];return(n||[]).forEach((function(t){var r=If(t.data),n=Rf(r[1][0].data)>>>0;switch(a){case 1:s[n]=kf(r[3][0].data);break;case 8:var i=If(e[zf(r[9][0].data)][0].data),o=e[zf(i[1][0].data)][0],c=Rf(o.meta[1][0].data);if(2001!=c)throw new Error("2000 unexpected reference to ".concat(c));var l=If(o.data);s[n]=l[3].map((function(e){return kf(e.data)})).join("")}})),s}function jf(e,t){var r,a=If(t.data),n=(null==(r=null==a?void 0:a[7])?void 0:r[0])?Rf(a[7][0].data)>>>0>0?1:0:-1,s=Df(a[5],(function(e){return function(e,t){var r,a,n,s,i,o,c,l,f,h,u,d,p,m,g,v,b=If(e),w=Rf(b[1][0].data)>>>0,T=Rf(b[2][0].data)>>>0,E=(null==(a=null==(r=b[8])?void 0:r[0])?void 0:a.data)&&Rf(b[8][0].data)>0||!1;if((null==(s=null==(n=b[7])?void 0:n[0])?void 0:s.data)&&0!=t)g=null==(o=null==(i=b[7])?void 0:i[0])?void 0:o.data,v=null==(l=null==(c=b[6])?void 0:c[0])?void 0:l.data;else{if(!(null==(h=null==(f=b[4])?void 0:f[0])?void 0:h.data)||1==t)throw"NUMBERS Tile missing ".concat(t," cell storage");g=null==(d=null==(u=b[4])?void 0:u[0])?void 0:d.data,v=null==(m=null==(p=b[3])?void 0:p[0])?void 0:m.data}for(var S=E?4:1,y=yf(g),k=[],_=0;_<g.length/2;++_){var A=y.getUint16(2*_,!0);A<65535&&k.push([_,A])}if(k.length!=T)throw"Expected ".concat(T," cells, found ").concat(k.length);var x=[];for(_=0;_<k.length-1;++_)x[k[_][0]]=v.subarray(k[_][1]*S,k[_+1][1]*S);return k.length>=1&&(x[k[k.length-1][0]]=v.subarray(k[k.length-1][1]*S)),{R:w,cells:x}}(e,n)}));return{nrows:Rf(a[4][0].data)>>>0,data:s.reduce((function(e,t){return e[t.R]||(e[t.R]=[]),t.cells.forEach((function(r,a){if(e[t.R][a])throw new Error("Duplicate cell r=".concat(t.R," c=").concat(a));e[t.R][a]=r})),e}),[])}}function Xf(e,t){var r={"!ref":"A1"},a=e[zf(If(t.data)[2][0].data)],n=Rf(a[0].meta[1][0].data);if(6001!=n)throw new Error("6000 unexpected reference to ".concat(n));return function(e,t,r){var a,n=If(t.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(Rf(n[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(Rf(n[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=ia(s);var i=If(n[4][0].data),o=Gf(e,e[zf(i[4][0].data)][0]),c=(null==(a=i[17])?void 0:a[0])?Gf(e,e[zf(i[17][0].data)][0]):[],l=If(i[3][0].data),f=0;l[1].forEach((function(t){var a=If(t.data),n=e[zf(a[2][0].data)][0],s=Rf(n.meta[1][0].data);if(6002!=s)throw new Error("6001 unexpected reference to ".concat(s));var i=jf(0,n);i.data.forEach((function(e,t){e.forEach((function(e,a){var n=na({r:f+t,c:a}),s=Vf(e,o,c);s&&(r[n]=s)}))})),f+=i.nrows}))}(e,a[0],r),r}function $f(e,t){var r={SheetNames:[],Sheets:{}};if(Df(If(t.data)[1],zf).forEach((function(t){e[t].forEach((function(t){if(2==Rf(t.meta[1][0].data)){var a=function(e,t){var r,a=If(t.data),n={name:(null==(r=a[1])?void 0:r[0])?kf(a[1][0].data):"",sheets:[]};return Df(a[2],zf).forEach((function(t){e[t].forEach((function(t){6e3==Rf(t.meta[1][0].data)&&n.sheets.push(Xf(e,t))}))})),n}(e,t);a.sheets.forEach((function(e,t){yh(r,e,0==t?a.name:a.name+"_"+t,!0)}))}}))})),0==r.SheetNames.length)throw new Error("Empty NUMBERS file");return r}function Yf(e){var t,r,a,n,s={},i=[];if(e.FullPaths.forEach((function(e){if(e.match(/\.iwpv2/))throw new Error("Unsupported password protection")})),e.FileIndex.forEach((function(e){if(e.name.match(/\.iwa$/)){var t,r;try{t=Mf(e.content)}catch(a){return console.log("?? "+e.content.length+" "+(a.message||a))}try{r=Ff(t)}catch(a){return console.log("## "+(a.message||a))}r.forEach((function(e){s[e.id]=e.messages,i.push(e.id)}))}})),!i.length)throw new Error("File has no messages");var o=(null==(n=null==(a=null==(r=null==(t=null==s?void 0:s[1])?void 0:t[0])?void 0:r.meta)?void 0:a[1])?void 0:n[0].data)&&1==Rf(s[1][0].meta[1][0].data)&&s[1][0];if(o||i.forEach((function(e){s[e].forEach((function(e){if(1==Rf(e.meta[1][0].data)>>>0){if(o)throw new Error("Document has multiple roots");o=e}}))})),!o)throw new Error("Cannot find Document root");return $f(s,o)}function Kf(e,t,r){var a,n,s,i;if(!(null==(a=e[6])?void 0:a[0])||!(null==(n=e[7])?void 0:n[0]))throw"Mutation only works on post-BNC storages!";if((null==(i=null==(s=e[8])?void 0:s[0])?void 0:i.data)&&Rf(e[8][0].data)>0||!1)throw"Math only works with normal offsets";for(var o=0,c=yf(e[7][0].data),l=0,f=[],h=yf(e[4][0].data),u=0,d=[],p=0;p<t.length;++p)if(null!=t[p]){var m,g;switch(c.setUint16(2*p,l,!0),h.setUint16(2*p,u,!0),typeof t[p]){case"string":m=Wf({t:"s",v:t[p]},r),g=Hf({t:"s",v:t[p]},r);break;case"number":m=Wf({t:"n",v:t[p]},r),g=Hf({t:"n",v:t[p]},r);break;case"boolean":m=Wf({t:"b",v:t[p]},r),g=Hf({t:"b",v:t[p]},r);break;default:throw new Error("Unsupported value "+t[p])}f.push(m),l+=m.length,d.push(g),u+=g.length,++o}else c.setUint16(2*p,65535,!0),h.setUint16(2*p,65535);for(e[2][0].data=Of(o);p<e[7][0].data.length/2;++p)c.setUint16(2*p,65535,!0),h.setUint16(2*p,65535,!0);return e[6][0].data=Af(f),e[3][0].data=Af(d),o}function Jf(e){return function(t){for(var r=0;r!=e.length;++r){var a=e[r];void 0===t[a[0]]&&(t[a[0]]=a[1]),"n"===a[2]&&(t[a[0]]=Number(t[a[0]]))}}}function qf(e){Jf([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function Zf(e){Jf([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function Qf(e,t,r,a,n,s,i,o,c,l,f,h){try{s[a]=tn(Ze(e,r,!0),t);var u,d=qe(e,t);switch(o){case"sheet":u=ul(d,t,n,c,s[a],l,f,h);break;case"chart":if(!(u=dl(d,t,n,c,s[a],l))||!u["!drawel"])break;var p=nt(u["!drawel"].Target,t),m=en(p),g=function(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}(Ze(e,p,!0),tn(Ze(e,m,!0),p)),v=nt(g,p),b=en(v);u=Kc(Ze(e,v,!0),0,0,tn(Ze(e,b,!0),v),0,u);break;case"macro":T=t,s[a],T.slice(-4),u={"!type":"macro"};break;case"dialog":u=function(e,t){return t.slice(-4),{"!type":"dialog"}}(0,t,0,0,s[a]);break;default:throw new Error("Unrecognized sheet type "+o)}i[a]=u;var w=[];s&&s[a]&&_e(s[a]).forEach((function(r){var n="";if(s[a][r].Type==Qa.CMNT){n=nt(s[a][r].Target,t);var i=gl(qe(e,n,!0),n,c);if(!i||!i.length)return;co(u,i,!1)}s[a][r].Type==Qa.TCMNT&&(n=nt(s[a][r].Target,t),w=w.concat(function(e,t){var r=[],a=!1,n={},s=0;return e.replace(ct,(function(i,o){var c=ht(i);switch(ut(c[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":n={author:c.personId,guid:c.id,ref:c.ref,T:1};break;case"</threadedComment>":null!=n.t&&r.push(n);break;case"<text>":case"<text":s=o+i.length;break;case"</text>":n.t=e.slice(s,o).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":a=!0;break;case"</mentions>":case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+c[0]+" in threaded comments")}return i})),r}(qe(e,n,!0),c)))})),w&&w.length&&co(u,w,!0,c.people||[])}catch(E){if(c.WTF)throw E}var T}function eh(e){return"/"==e.charAt(0)?e.slice(1):e}function th(e,t){if(be(),qf(t=t||{}),Ke(e,"META-INF/manifest.xml"))return bf(e,t);if(Ke(e,"objectdata.xml"))return bf(e,t);if(Ke(e,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw new Error("NUMBERS file parsing requires Uint8Array support");if(void 0!==Yf){if(e.FileIndex)return Yf(e);var r=Se.utils.cfb_new();return et(e).forEach((function(t){tt(r,t,Qe(e,t))})),Yf(r)}throw new Error("Unsupported NUMBERS file")}if(!Ke(e,"[Content_Types].xml")){if(Ke(e,"index.xml.gz"))throw new Error("Unsupported NUMBERS 08 file");if(Ke(e,"index.xml"))throw new Error("Unsupported NUMBERS 09 file");throw new Error("Unsupported ZIP file")}var a,n,s=et(e),i=function(e){var t={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};if(!e||!e.match)return t;var r={};if((e.match(ct)||[]).forEach((function(e){var a=ht(e);switch(a[0].replace(lt,"<")){case"<?xml":break;case"<Types":t.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[a.Extension]=a.ContentType;break;case"<Override":void 0!==t[Ja[a.ContentType]]&&t[Ja[a.ContentType]].push(a.PartName)}})),t.xmlns!==Xt)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}(Ze(e,"[Content_Types].xml")),o=!1;if(0===i.workbooks.length&&qe(e,n="xl/workbook.xml",!0)&&i.workbooks.push(n),0===i.workbooks.length){if(!qe(e,n="xl/workbook.bin",!0))throw new Error("Could not find workbook");i.workbooks.push(n),o=!0}"bin"==i.workbooks[0].slice(-3)&&(o=!0);var c={},l={};if(!t.bookSheets&&!t.bookProps){if(oc=[],i.sst)try{oc=ml(qe(e,eh(i.sst)),i.sst,t)}catch(R){if(t.WTF)throw R}t.cellStyles&&i.themes.length&&(c=function(e,t,r){return eo(e,r)}(Ze(e,i.themes[0].replace(/^\//,""),!0)||"",i.themes[0],t)),i.style&&(l=pl(qe(e,eh(i.style)),i.style,c,t))}i.links.map((function(r){try{tn(Ze(e,en(eh(r))),r);return bl(qe(e,eh(r)),0,r,t)}catch(R){}}));var f=hl(qe(e,eh(i.workbooks[0])),i.workbooks[0],t),h={},u="";i.coreprops.length&&((u=qe(e,eh(i.coreprops[0]),!0))&&(h=ln(u)),0!==i.extprops.length&&(u=qe(e,eh(i.extprops[0]),!0))&&function(e,t,r){var a={};t||(t={}),e=Ct(e),un.forEach((function(r){var n=(e.match(Rt(r[0]))||[])[1];switch(r[2]){case"string":n&&(t[r[1]]=mt(n));break;case"bool":t[r[1]]="true"===n;break;case"raw":var s=e.match(new RegExp("<"+r[0]+"[^>]*>([\\s\\S]*?)</"+r[0]+">"));s&&s.length>0&&(a[r[1]]=s[1])}})),a.HeadingPairs&&a.TitlesOfParts&&pn(a.HeadingPairs,a.TitlesOfParts,t,r)}(u,h,t));var d={};t.bookSheets&&!t.bookProps||0!==i.custprops.length&&(u=Ze(e,eh(i.custprops[0]),!0))&&(d=function(e,t){var r={},a="",n=e.match(gn);if(n)for(var s=0;s!=n.length;++s){var i=n[s],o=ht(i);switch(o[0]){case"<?xml":case"<Properties":break;case"<property":a=mt(o.name);break;case"</property>":a=null;break;default:if(0===i.indexOf("<vt:")){var c=i.split(">"),l=c[0].slice(4),f=c[1];switch(l){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":r[a]=mt(f);break;case"bool":r[a]=yt(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(f,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(f);break;case"filetime":case"date":r[a]=Be(f);break;default:if("/"==l.slice(-1))break;t.WTF&&"undefined"!=typeof console&&console.warn("Unexpected",i,l,c)}}else if("</"===i.slice(0,2));else if(t.WTF)throw new Error(i)}}return r}(u,t));var p={};if((t.bookSheets||t.bookProps)&&(f.Sheets?a=f.Sheets.map((function(e){return e.name})):h.Worksheets&&h.SheetNames.length>0&&(a=h.SheetNames),t.bookProps&&(p.Props=h,p.Custprops=d),t.bookSheets&&void 0!==a&&(p.SheetNames=a),t.bookSheets?p.SheetNames:t.bookProps))return p;a={};var m={};t.bookDeps&&i.calcchain&&(m=vl(qe(e,eh(i.calcchain)),i.calcchain));var g,v,b=0,w={},T=f.Sheets;h.Worksheets=T.length,h.SheetNames=[];for(var E=0;E!=T.length;++E)h.SheetNames[E]=T[E].name;var S=o?"bin":"xml",y=i.workbooks[0].lastIndexOf("/"),k=(i.workbooks[0].slice(0,y+1)+"_rels/"+i.workbooks[0].slice(y+1)+".rels").replace(/^\//,"");Ke(e,k)||(k="xl/_rels/workbook."+S+".rels");var _=tn(Ze(e,k,!0),k.replace(/_rels.*/,"s5s"));(i.metadata||[]).length>=1&&(t.xlmeta=wl(qe(e,eh(i.metadata[0])),i.metadata[0],t)),(i.people||[]).length>=1&&(t.people=function(e,t){var r=[],a=!1;return e.replace(ct,(function(e){var n=ht(e);switch(ut(n[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":r.push({name:n.displayname,id:n.id});break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+n[0]+" in threaded comments")}return e})),r}(qe(e,eh(i.people[0])),t)),_&&(_=function(e,t){if(!e)return 0;try{e=t.map((function(t){return t.id||(t.id=t.strRelID),[t.name,e["!id"][t.id].Target,(r=e["!id"][t.id].Type,Qa.WS.indexOf(r)>-1?"sheet":r==Qa.CS?"chart":r==Qa.DS?"dialog":r==Qa.MS?"macro":r&&r.length?r:"sheet")];var r}))}catch(R){return null}return e&&0!==e.length?e:null}(_,f.Sheets));var A=qe(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(b=0;b!=h.Worksheets;++b){var x="sheet";if(_&&_[b]?(g="xl/"+_[b][1].replace(/[\/]?xl\//,""),Ke(e,g)||(g=_[b][1]),Ke(e,g)||(g=k.replace(/_rels\/.*$/,"")+_[b][1]),x=_[b][2]):g=(g="xl/worksheets/sheet"+(b+1-A)+"."+S).replace(/sheet0\./,"sheet."),v=g.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&null!=t.sheets)switch(typeof t.sheets){case"number":if(b!=t.sheets)continue e;break;case"string":if(h.SheetNames[b].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var C=!1,O=0;O!=t.sheets.length;++O)"number"==typeof t.sheets[O]&&t.sheets[O]==b&&(C=1),"string"==typeof t.sheets[O]&&t.sheets[O].toLowerCase()==h.SheetNames[b].toLowerCase()&&(C=1);if(!C)continue e}}Qf(e,g,v,h.SheetNames[b],b,w,a,x,t,f,c,l)}return p={Directory:i,Workbook:f,Props:h,Custprops:d,Deps:m,Sheets:a,SheetNames:h.SheetNames,Strings:oc,Styles:l,Themes:c,SSF:He(L)},t&&t.bookFiles&&(e.files?(p.keys=s,p.files=e.files):(p.keys=[],p.files={},e.FullPaths.forEach((function(t,r){t=t.replace(/^Root Entry[\/]/,""),p.keys.push(t),p.files[t]=e.FileIndex[r]})))),t&&t.bookVBA&&(i.vba.length>0?p.vbaraw=qe(e,eh(i.vba[0]),!0):i.defaults&&"application/vnd.ms-office.vbaProject"===i.defaults.bin&&(p.vbaraw=qe(e,"xl/vbaProject.bin",!0))),p}function rh(e,t){var r,a,n=t||{},s="Workbook",i=Se.find(e,s);try{if(s="/!DataSpaces/Version",!(i=Se.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(r=i.content,(a={}).id=r.read_shift(0,"lpp4"),a.R=ai(r,4),a.U=ai(r,4),a.W=ai(r,4),s="/!DataSpaces/DataSpaceMap",!(i=Se.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var o=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(ni(e));return t}(i.content);if(1!==o.length||1!==o[0].comps.length||0!==o[0].comps[0].t||"StrongEncryptionDataSpace"!==o[0].name||"EncryptedPackage"!==o[0].comps[0].v)throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(i=Se.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var c=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}(i.content);if(1!=c.length||"StrongEncryptionTransform"!=c[0])throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(i=Se.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);si(i.content)}catch(f){}if(s="/EncryptionInfo",!(i=Se.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var l=function(e){var t=ai(e);switch(t.Minor){case 2:return[t.Minor,ci(e)];case 3:return[t.Minor,li()];case 4:return[t.Minor,fi(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}(i.content);if(s="/EncryptedPackage",!(i=Se.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(4==l[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(l[1],i.content,n.password||"",n);if(2==l[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(l[1],i.content,n.password||"",n);throw new Error("File is password-protected")}function ah(e,t){return"ods"==t.bookType?Sf(e,t):"numbers"==t.bookType?function(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var a=sa(r["!ref"]);a.s.r=a.s.c=0;var n=!1;a.e.c>9&&(n=!0,a.e.c=9),a.e.r>49&&(n=!0,a.e.r=49),n&&console.error("The Numbers writer is currently limited to ".concat(ia(a)));var s=mh(r,{range:a,header:1}),i=["~Sh33tJ5~"];s.forEach((function(e){return e.forEach((function(e){"string"==typeof e&&i.push(e)}))}));var o={},c=[],l=Se.read(t.numbers,{type:"base64"});l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&Ff(Mf(t.content)).forEach((function(e){c.push(e.id),o[e.id]={deps:[],location:r,type:Rf(e.messages[0].meta[1][0].data)}}))})),c.sort((function(e,t){return e-t}));var f=c.filter((function(e){return e>1})).map((function(e){return[e,Of(e)]}));l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&Ff(Mf(t.content)).forEach((function(e){e.messages.forEach((function(t){f.forEach((function(t){e.messages.some((function(e){return 11006!=Rf(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var a=0;a<t.length;++a)if(e[r+a]!=t[a])continue e;return!0}return!1}(e.data,t[1])}))&&o[t[0]].deps.push(e.id)}))}))}))}));for(var h,u=Se.find(l,o[1].location),d=Ff(Mf(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(h=m)}var g=zf(If(h.messages[0].data)[1][0].data);for(d=Ff(Mf((u=Se.find(l,o[g].location)).content)),p=0;p<d.length;++p)(m=d[p]).id==g&&(h=m);for(g=zf(If(h.messages[0].data)[2][0].data),d=Ff(Mf((u=Se.find(l,o[g].location)).content)),p=0;p<d.length;++p)(m=d[p]).id==g&&(h=m);for(g=zf(If(h.messages[0].data)[2][0].data),d=Ff(Mf((u=Se.find(l,o[g].location)).content)),p=0;p<d.length;++p)(m=d[p]).id==g&&(h=m);var v=If(h.messages[0].data);v[6][0].data=Of(a.e.r+1),v[7][0].data=Of(a.e.c+1);for(var b=zf(v[46][0].data),w=Se.find(l,o[b].location),T=Ff(Mf(w.content)),E=0;E<T.length&&T[E].id!=b;++E);if(T[E].id!=b)throw"Bad ColumnRowUIDMapArchive";var S=If(T[E].messages[0].data);S[1]=[],S[2]=[],S[3]=[];for(var y=0;y<=a.e.c;++y){var k=[];k[1]=k[2]=[{type:0,data:Of(y+420690)}],S[1].push({type:2,data:Nf(k)}),S[2].push({type:0,data:Of(y)}),S[3].push({type:0,data:Of(y)})}S[4]=[],S[5]=[],S[6]=[];for(var _=0;_<=a.e.r;++_)(k=[])[1]=k[2]=[{type:0,data:Of(_+726270)}],S[4].push({type:2,data:Nf(k)}),S[5].push({type:0,data:Of(_)}),S[6].push({type:0,data:Of(_)});T[E].messages[0].data=Nf(S),w.content=Uf(Pf(T)),w.size=w.content.length,delete v[46];var A=If(v[4][0].data);A[7][0].data=Of(a.e.r+1);var x=zf(If(A[1][0].data)[2][0].data);if((T=Ff(Mf((w=Se.find(l,o[x].location)).content)))[0].id!=x)throw"Bad HeaderStorageBucket";var C=If(T[0].messages[0].data);for(_=0;_<s.length;++_){var O=If(C[2][0].data);O[1][0].data=Of(_),O[4][0].data=Of(s[_].length),C[2][_]={type:C[2][0].type,data:Nf(O)}}T[0].messages[0].data=Nf(C),w.content=Uf(Pf(T)),w.size=w.content.length;var R=zf(A[2][0].data);if((T=Ff(Mf((w=Se.find(l,o[R].location)).content)))[0].id!=R)throw"Bad HeaderStorageBucket";for(C=If(T[0].messages[0].data),y=0;y<=a.e.c;++y)(O=If(C[2][0].data))[1][0].data=Of(y),O[4][0].data=Of(a.e.r+1),C[2][y]={type:C[2][0].type,data:Nf(O)};T[0].messages[0].data=Nf(C),w.content=Uf(Pf(T)),w.size=w.content.length;var I=zf(A[4][0].data);!function(){for(var e,t=Se.find(l,o[I].location),r=Ff(Mf(t.content)),a=0;a<r.length;++a){var n=r[a];n.id==I&&(e=n)}var s=If(e.messages[0].data);s[3]=[];var c=[];i.forEach((function(e,t){c[1]=[{type:0,data:Of(t)}],c[2]=[{type:0,data:Of(1)}],c[3]=[{type:2,data:_f(e)}],s[3].push({type:2,data:Nf(c)})})),e.messages[0].data=Nf(s);var f=Uf(Pf(r));t.content=f,t.size=t.content.length}();var N=If(A[3][0].data),D=N[1][0];delete N[2];var F=If(D.data),P=zf(F[2][0].data);!function(){for(var e,t=Se.find(l,o[P].location),r=Ff(Mf(t.content)),n=0;n<r.length;++n){var c=r[n];c.id==P&&(e=c)}var f=If(e.messages[0].data);delete f[6],delete N[7];var h=new Uint8Array(f[5][0].data);f[5]=[];for(var u=0,d=0;d<=a.e.r;++d){var p=If(h);u+=Kf(p,s[d],i),p[1][0].data=Of(d),f[5].push({data:Nf(p),type:2})}f[1]=[{type:0,data:Of(a.e.c+1)}],f[2]=[{type:0,data:Of(a.e.r+1)}],f[3]=[{type:0,data:Of(u)}],f[4]=[{type:0,data:Of(a.e.r+1)}],e.messages[0].data=Nf(f);var m=Uf(Pf(r));t.content=m,t.size=t.content.length}(),D.data=Nf(F),A[3][0].data=Nf(N),v[4][0].data=Nf(A),h.messages[0].data=Nf(v);var L=Uf(Pf(d));return u.content=L,u.size=u.content.length,l}(e,t):"xlsb"==t.bookType?function(e,t){io=1024,e&&!e.SSF&&(e.SSF=He(L));e&&e.SSF&&(be(),ve(e.SSF),t.revssf=Ce(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF);t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,lc?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xlsb"==t.bookType?"bin":"xml",a=po.indexOf(t.bookType)>-1,n={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};Zf(t=t||{});var s=rt(),i="",o=0;t.cellXfs=[],dc(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});if(tt(s,i="docProps/core.xml",hn(e.Props,t)),n.coreprops.push(i),an(t.rels,2,i,Qa.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var c=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,tt(s,i,mn(e.Props)),n.extprops.push(i),an(t.rels,3,i,Qa.EXT_PROPS),e.Custprops!==e.Props&&_e(e.Custprops||{}).length>0&&(tt(s,i="docProps/custom.xml",vn(e.Custprops)),n.custprops.push(i),an(t.rels,4,i,Qa.CUST_PROPS));for(o=1;o<=e.SheetNames.length;++o){var f={"!id":{}},h=e.Sheets[e.SheetNames[o-1]];(h||{})["!type"];if(tt(s,i="xl/worksheets/sheet"+o+"."+r,Tl(o-1,i,t,e,f)),n.sheets.push(i),an(t.wbrels,-1,"worksheets/sheet"+o+"."+r,Qa.WS[0]),h){var u=h["!comments"],d=!1,p="";u&&u.length>0&&(tt(s,p="xl/comments"+o+"."+r,El(u,p)),n.comments.push(p),an(f,-1,"../comments"+o+"."+r,Qa.CMNT),d=!0),h["!legacy"]&&d&&tt(s,"xl/drawings/vmlDrawing"+o+".vml",oo(o,h["!comments"])),delete h["!comments"],delete h["!legacy"]}f["!id"].rId1&&tt(s,en(i),rn(f))}null!=t.Strings&&t.Strings.length>0&&(tt(s,i="xl/sharedStrings."+r,function(e,t,r){return(".bin"===t.slice(-4)?ti:Qs)(e,r)}(t.Strings,i,t)),n.strs.push(i),an(t.wbrels,-1,"sharedStrings."+r,Qa.SST));tt(s,i="xl/workbook."+r,function(e,t){return(".bin"===t.slice(-4)?fl:ol)(e)}(e,i)),n.workbooks.push(i),an(t.rels,1,i,Qa.WB),tt(s,i="xl/theme/theme1.xml",to(e.Themes,t)),n.themes.push(i),an(t.wbrels,-1,"theme/theme1.xml",Qa.THEME),tt(s,i="xl/styles."+r,function(e,t,r){return(".bin"===t.slice(-4)?ji:Ni)(e,r)}(e,i,t)),n.styles.push(i),an(t.wbrels,-1,"styles."+r,Qa.STY),e.vbaraw&&a&&(tt(s,i="xl/vbaProject.bin",e.vbaraw),n.vba.push(i),an(t.wbrels,-1,"vbaProject.bin",Qa.VBA));return tt(s,i="xl/metadata."+r,function(e){return(".bin"===e.slice(-4)?no:so)()}(i)),n.metadata.push(i),an(t.wbrels,-1,"metadata."+r,Qa.XLMETA),tt(s,"[Content_Types].xml",Za(n,t)),tt(s,"_rels/.rels",rn(t.rels)),tt(s,"xl/_rels/workbook."+r+".rels",rn(t.wbrels)),delete t.revssf,delete t.ssf,s}(e,t):function(e,t){io=1024,e&&!e.SSF&&(e.SSF=He(L));e&&e.SSF&&(be(),ve(e.SSF),t.revssf=Ce(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF);t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,lc?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",a=po.indexOf(t.bookType)>-1,n={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};Zf(t=t||{});var s=rt(),i="",o=0;t.cellXfs=[],dc(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});if(tt(s,i="docProps/core.xml",hn(e.Props,t)),n.coreprops.push(i),an(t.rels,2,i,Qa.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var c=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,tt(s,i,mn(e.Props)),n.extprops.push(i),an(t.rels,3,i,Qa.EXT_PROPS),e.Custprops!==e.Props&&_e(e.Custprops||{}).length>0&&(tt(s,i="docProps/custom.xml",vn(e.Custprops)),n.custprops.push(i),an(t.rels,4,i,Qa.CUST_PROPS));var f=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]];(u||{})["!type"];if(tt(s,i="xl/worksheets/sheet"+o+"."+r,Dc(o-1,t,e,h)),n.sheets.push(i),an(t.wbrels,-1,"worksheets/sheet"+o+"."+r,Qa.WS[0]),u){var d=u["!comments"],p=!1,m="";if(d&&d.length>0){var g=!1;d.forEach((function(e){e[1].forEach((function(e){1==e.T&&(g=!0)}))})),g&&(tt(s,m="xl/threadedComments/threadedComment"+o+"."+r,fo(d,f,t)),n.threadedcomments.push(m),an(h,-1,"../threadedComments/threadedComment"+o+"."+r,Qa.TCMNT)),tt(s,m="xl/comments"+o+"."+r,lo(d)),n.comments.push(m),an(h,-1,"../comments"+o+"."+r,Qa.CMNT),p=!0}u["!legacy"]&&p&&tt(s,"xl/drawings/vmlDrawing"+o+".vml",oo(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&tt(s,en(i),rn(h))}null!=t.Strings&&t.Strings.length>0&&(tt(s,i="xl/sharedStrings."+r,Qs(t.Strings,t)),n.strs.push(i),an(t.wbrels,-1,"sharedStrings."+r,Qa.SST));tt(s,i="xl/workbook."+r,ol(e)),n.workbooks.push(i),an(t.rels,1,i,Qa.WB),tt(s,i="xl/theme/theme1.xml",to(e.Themes,t)),n.themes.push(i),an(t.wbrels,-1,"theme/theme1.xml",Qa.THEME),tt(s,i="xl/styles."+r,Ni(e,t)),n.styles.push(i),an(t.wbrels,-1,"styles."+r,Qa.STY),e.vbaraw&&a&&(tt(s,i="xl/vbaProject.bin",e.vbaraw),n.vba.push(i),an(t.wbrels,-1,"vbaProject.bin",Qa.VBA));tt(s,i="xl/metadata."+r,so()),n.metadata.push(i),an(t.wbrels,-1,"metadata."+r,Qa.XLMETA),f.length>1&&(tt(s,i="xl/persons/person.xml",function(e){var t=[st,Bt("personList",null,{xmlns:Yt,"xmlns:x":rr[0]}).replace(/[\/]>/,">")];return e.forEach((function(e,r){t.push(Bt("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+r).slice(-12)+"}",userId:e,providerId:"None"}))})),t.push("</personList>"),t.join("")}(f)),n.people.push(i),an(t.wbrels,-1,"persons/person.xml",Qa.PEOPLE));return tt(s,"[Content_Types].xml",Za(n,t)),tt(s,"_rels/.rels",rn(t.rels)),tt(s,"xl/_rels/workbook."+r+".rels",rn(t.wbrels)),delete t.revssf,delete t.ssf,s}(e,t)}function nh(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=m(e.slice(0,12));break;case"binary":r=e;break;default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function sh(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return Dl(e.slice(r),t);default:break e}return Hs.to_workbook(e,t)}function ih(e,t,r,a){return a?(r.type="string",Hs.to_workbook(e,r)):Hs.to_workbook(t,r)}function oh(e,t){i();var r=t||{};if("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer)return oh(new Uint8Array(e),((r=He(r)).type="array",r));"undefined"!=typeof Uint8Array&&e instanceof Uint8Array&&!r.type&&(r.type="undefined"!=typeof Deno?"buffer":"array");var a,n=e,s=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),cc={},r.dateNF&&(cc.dateNF=r.dateNF),r.type||(r.type=g&&Buffer.isBuffer(e)?"buffer":"base64"),"file"==r.type&&(r.type=g?"buffer":"binary",n=function(e){if("undefined"!=typeof Deno)return Deno.readFileSync(e);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}(e),"undefined"==typeof Uint8Array||g||(r.type="array")),"string"==r.type&&(s=!0,r.type="binary",r.codepage=65001,n=function(e){return e.match(/[^\x00-\x7F]/)?Ot(e):e}(e)),"array"==r.type&&"undefined"!=typeof Uint8Array&&e instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var o=new ArrayBuffer(3),c=new Uint8Array(o);if(c.foo="bar",!c.foo)return(r=He(r)).type="array",oh(y(n),r)}switch((a=nh(n,r))[0]){case 208:if(207===a[1]&&17===a[2]&&224===a[3]&&161===a[4]&&177===a[5]&&26===a[6]&&225===a[7])return function(e,t){return Se.find(e,"EncryptedPackage")?rh(e,t):Kl(e,t)}(Se.read(n,r),r);break;case 9:if(a[1]<=8)return Kl(n,r);break;case 60:return Dl(n,r);case 73:if(73===a[1]&&42===a[2]&&0===a[3])throw new Error("TIFF Image File is not a spreadsheet");if(68===a[1])return function(e,t){var r=t||{},a=!!r.WTF;r.WTF=!0;try{var n=Us.to_workbook(e,r);return r.WTF=a,n}catch(s){if(r.WTF=a,!s.message.match(/SYLK bad record ID/)&&a)throw s;return Hs.to_workbook(e,t)}}(n,r);break;case 84:if(65===a[1]&&66===a[2]&&76===a[3])return Bs.to_workbook(n,r);break;case 80:return 75===a[1]&&a[2]<9&&a[3]<9?function(e,t){var r=e,a=t||{};return a.type||(a.type=g&&Buffer.isBuffer(e)?"buffer":"base64"),th(at(r,a),a)}(n,r):ih(e,n,r,s);case 239:return 60===a[3]?Dl(n,r):ih(e,n,r,s);case 255:if(254===a[1])return function(e,t){var r=e;return"base64"==t.type&&(r=m(r)),r=l.utils.decode(1200,r.slice(2),"str"),t.type="binary",sh(r,t)}(n,r);if(0===a[1]&&2===a[2]&&0===a[3])return Vs.to_workbook(n,r);break;case 0:if(0===a[1]){if(a[2]>=2&&0===a[3])return Vs.to_workbook(n,r);if(0===a[2]&&(8===a[3]||9===a[3]))return Vs.to_workbook(n,r)}break;case 3:case 131:case 139:case 140:return Ms.to_workbook(n,r);case 123:if(92===a[1]&&114===a[2]&&116===a[3])return mi.to_workbook(n,r);break;case 10:case 13:case 32:return function(e,t){var r="",a=nh(e,t);switch(t.type){case"base64":r=m(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=We(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==a[0]&&187==a[1]&&191==a[2]&&(r=Ct(r)),t.type="binary",sh(r,t)}(n,r);case 137:if(80===a[1]&&78===a[2]&&71===a[3])throw new Error("PNG Image File is not a spreadsheet")}return Ls.indexOf(a[0])>-1&&a[2]<=12&&a[3]<=31?Ms.to_workbook(n,r):ih(e,n,r,s)}function ch(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return ke(t.file,Se.write(e,{type:g?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return Se.write(e,t)}function lh(e,t){var r=He(t||{});return function(e,t){var r={},a=g?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";t.compression&&(r.compression="DEFLATE");if(t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw new Error("Unrecognized type "+t.type)}var n=e.FullPaths?Se.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!=typeof Deno&&"string"==typeof n){if("binary"==t.type||"base64"==t.type)return n;n=new Uint8Array(E(n))}return t.password&&"undefined"!=typeof encrypt_agile?ch(encrypt_agile(n,t.password),t):"file"===t.type?ke(t.file,n):"string"==t.type?Ct(n):n}(ah(e,r),r)}function fh(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return p(Ot(a));case"binary":return Ot(a);case"string":return e;case"file":return ke(t.file,a,"utf8");case"buffer":return g?v(a,"utf8"):"undefined"!=typeof TextEncoder?(new TextEncoder).encode(a):fh(a,{type:"binary"}).split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function hh(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return"base64"==t.type?p(r):"string"==t.type?Ct(r):r;case"file":return ke(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function uh(e,t){i(),sl(e);var r=He(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var a=uh(e,r);return r.type="array",E(a)}var n=0;if(r.sheet&&(n="number"==typeof r.sheet?r.sheet:e.SheetNames.indexOf(r.sheet),!e.SheetNames[n]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return fh(Bl(e,r),r);case"slk":case"sylk":return fh(Us.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"htm":case"html":return fh(uf(e.Sheets[e.SheetNames[n]],r),r);case"txt":return function(e,t){switch(t.type){case"base64":return p(e);case"binary":case"string":return e;case"file":return ke(t.file,e,"binary");case"buffer":return g?v(e,"binary"):e.split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}(wh(e.Sheets[e.SheetNames[n]],r),r);case"csv":return fh(bh(e.Sheets[e.SheetNames[n]],r),r,"\ufeff");case"dif":return fh(Bs.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"dbf":return hh(Ms.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"prn":return fh(Hs.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"rtf":return fh(mi.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"eth":return fh(Ws.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"fods":return fh(Sf(e,r),r);case"wk1":return hh(Vs.sheet_to_wk1(e.Sheets[e.SheetNames[n]],r),r);case"wk3":return hh(Vs.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),hh(lf(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),function(e,t){var r=t||{};return ch(Jl(e,r),r)}(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return lh(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function dh(e,t,r){var a=r||{};return a.type="file",a.file=t,function(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"}[e.bookType]||e.bookType}}(a),uh(e,a)}function ph(e,t,r,a,n,s,i,o){var c=ea(r),l=o.defval,f=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(g){u.__rowNum__=r}else u.__rowNum__=r;if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=i?e[r][d]:e[a[d]+c];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==l)u[s[d]]=l;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:la(p,m,o);null!=m&&(h=!1)}}else{if(void 0===l)continue;null!=s[d]&&(u[s[d]]=l)}}return{row:u,isempty:h}}function mh(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,o="",c={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},f=null!=l.range?l.range:e["!ref"];switch(1===l.header?a=1:"A"===l.header?a=2:Array.isArray(l.header)?a=3:null==l.header&&(a=0),typeof f){case"string":c=oa(f);break;case"number":(c=oa(e["!ref"])).s.r=f;break;default:c=f}a>0&&(n=0);var h=ea(c.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=c.s.r,b=0,w={};g&&!e[v]&&(e[v]=[]);var T=l.skipHidden&&e["!cols"]||[],E=l.skipHidden&&e["!rows"]||[];for(b=c.s.c;b<=c.e.c;++b)if(!(T[b]||{}).hidden)switch(u[b]=ra(b),r=g?e[v][b]:e[u[b]+h],a){case 1:s[b]=b-c.s.c;break;case 2:s[b]=u[b];break;case 3:s[b]=l.header[b-c.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=i=la(r,null,l),m=w[i]||0){do{o=i+"_"+m++}while(w[o]);w[i]=m,w[o]=1}else w[i]=1;s[b]=o}for(v=c.s.r+n;v<=c.e.r;++v)if(!(E[v]||{}).hidden){var S=ph(e,c,v,u,a,s,g,l);(!1===S.isempty||(1===a?!1!==l.blankrows:l.blankrows))&&(d[p++]=S.row)}return d.length=p,d}var gh=/"/g;function vh(e,t,r,a,n,s,i,o){for(var c=!0,l=[],f="",h=ea(r),u=t.s.c;u<=t.e.c;++u)if(a[u]){var d=o.dense?(e[r]||[])[u]:e[a[u]+h];if(null==d)f="";else if(null!=d.v){c=!1,f=""+(o.rawNumbers&&"n"==d.t?d.v:la(d,null,o));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===n||m===s||34===m||o.forceQuotes){f='"'+f.replace(gh,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(c=!1,(f="="+d.f).indexOf(",")>=0&&(f='"'+f.replace(gh,'""')+'"'));l.push(f)}return!1===o.blankrows&&c?null:l.join(i)}function bh(e,t){var r=[],a=null==t?{}:t;if(null==e||null==e["!ref"])return"";var n=oa(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),o=void 0!==a.RS?a.RS:"\n",c=o.charCodeAt(0),l=new RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=ra(p));for(var m=0,g=n.s.r;g<=n.e.r;++g)(d[g]||{}).hidden||null!=(f=vh(e,n,g,h,i,c,s,a))&&(a.strip&&(f=f.replace(l,"")),(f||!1!==a.blankrows)&&r.push((m++?o:"")+f));return delete a.dense,r.join("")}function wh(e,t){return t||(t={}),t.FS="\t",t.RS="\n",bh(e,t)}function Th(e,t,r){var a,n=r||{},s=+!n.skipHeader,i=e||{},o=0,c=0;if(i&&null!=n.origin)if("number"==typeof n.origin)o=n.origin;else{var l="string"==typeof n.origin?aa(n.origin):n.origin;o=l.r,c=l.c}var f={s:{c:0,r:0},e:{c:c,r:o+t.length-1+s}};if(i["!ref"]){var h=oa(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==o&&(o=h.e.r+1,f.e.r=o+t.length-1+s)}else-1==o&&(o=0,f.e.r=t.length-1+s);var u=n.header||[],d=0;t.forEach((function(e,t){_e(e).forEach((function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var l=e[r],f="z",h="",p=na({c:c+d,r:o+t+s});a=Eh(i,p),!l||"object"!=typeof l||l instanceof Date?("number"==typeof l?f="n":"boolean"==typeof l?f="b":"string"==typeof l?f="s":l instanceof Date?(f="d",n.cellDates||(f="n",l=Re(l)),h=n.dateNF||L[14]):null===l&&n.nullError&&(f="e",l=0),a?(a.t=f,a.v=l,delete a.w,delete a.R,h&&(a.z=h)):i[p]=a={t:f,v:l},h&&(a.z=h)):i[p]=l}))})),f.e.c=Math.max(f.e.c,c+u.length-1);var p=ea(o);if(s)for(d=0;d<u.length;++d)i[ra(d+c)+p]={t:"s",v:u[d]};return i["!ref"]=ia(f),i}function Eh(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var a=aa(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return Eh(e,na("number"!=typeof t?t:{r:t,c:r||0}))}function Sh(){return{SheetNames:[],Sheets:{}}}function yh(e,t,r,a){var n=1;if(!r)for(;n<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+n);++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||r;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(r=i+n);++n);}if(nl(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function kh(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var _h={encode_col:ra,encode_row:ea,encode_cell:na,encode_range:ia,decode_col:ta,decode_row:Qr,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:aa,decode_range:sa,format_cell:la,sheet_add_aoa:ha,sheet_add_json:Th,sheet_add_dom:df,aoa_to_sheet:ua,json_to_sheet:function(e,t){return Th(null,e,t)},table_to_sheet:pf,table_to_book:function(e,t){return fa(pf(e,t),t)},sheet_to_csv:bh,sheet_to_txt:wh,sheet_to_json:mh,sheet_to_html:uf,sheet_to_formulae:function(e){var t,r="",a="";if(null==e||null==e["!ref"])return[];var n,s=oa(e["!ref"]),i="",o=[],c=[],l=Array.isArray(e);for(n=s.s.c;n<=s.e.c;++n)o[n]=ra(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=ea(f),n=s.s.c;n<=s.e.c;++n)if(r=o[n]+i,a="",void 0!==(t=l?(e[f]||[])[n]:e[r])){if(null!=t.F){if(r=t.F,!t.f)continue;a=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)a=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)a=""+t.v;else if("b"==t.t)a=t.v?"TRUE":"FALSE";else if(void 0!==t.w)a="'"+t.w;else{if(void 0===t.v)continue;a="s"==t.t?"'"+t.v:""+t.v}}c[c.length]=r+"="+a}return c},sheet_to_row_object_array:mh,sheet_get_cell:Eh,book_new:Sh,book_append_sheet:yh,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}throw new Error("Cannot find sheet |"+t+"|")}(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:kh,cell_set_internal_link:function(e,t,r){return kh(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,a){for(var n="string"!=typeof t?t:oa(t),s="string"==typeof t?t:ia(t),i=n.s.r;i<=n.e.r;++i)for(var o=n.s.c;o<=n.e.c;++o){var c=Eh(e,i,o);c.t="n",c.F=s,delete c.v,i==n.s.r&&o==n.s.c&&(c.f=r,a&&(c.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};export{oh as r,_h as u,dh as w};
