<template>
  <el-dialog
    v-model="dialogVisible"
    :title="type === 'add' ? '添加订单项目' : '编辑订单项目'"
    width="800px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="产品" prop="product_id">
            <el-select
              v-model="form.product_id"
              filterable
              remote
              :remote-method="handleSearchProducts"
              placeholder="请选择产品"
              style="width: 100%"
              @change="handleProductChange"
            >
              <el-option
                v-for="product in productOptions"
                :key="product.id"
                :label="`${product.name} (${product.model})`"
                :value="product.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品规格" prop="product_specification_id">
            <el-select
              v-model="form.product_specification_id"
              placeholder="请选择规格"
              style="width: 100%"
              @change="handleSpecificationChange"
            >
              <el-option
                v-for="spec in specificationOptions"
                :key="spec.id"
                :label="spec.specification"
                :value="spec.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="数量" prop="quantity">
            <el-input-number 
              v-model="form.quantity" 
              :min="1" 
              :precision="2"
              placeholder="请输入数量"
              style="width: 100%"
              @change="calculateTotal"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单价" prop="unit_price">
            <el-input-number 
              v-model="form.unit_price" 
              :precision="2" 
              :min="0" 
              placeholder="请输入单价"
              style="width: 100%"
              @change="calculateTotal"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="折扣(%)" prop="discount">
            <el-input-number 
              v-model="form.discount" 
              :precision="1" 
              :min="0" 
              :max="100"
              placeholder="请输入折扣"
              style="width: 100%"
              @change="calculateTotal"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="税率(%)" prop="tax_rate">
            <el-input-number 
              v-model="form.tax_rate" 
              :precision="1" 
              :min="0" 
              :max="100"
              placeholder="请输入税率"
              style="width: 100%"
              @change="calculateTotal"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="小计">
            <el-input 
              :value="`¥${form.total_price || '0.00'}`"
              readonly
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="含税小计">
            <el-input 
              :value="`¥${calculateTaxTotal()}`"
              readonly
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注" prop="notes">
        <el-input 
          v-model="form.notes" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息（可选）" 
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ type === 'add' ? '添加' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { productApi } from '@/api/product'
import { orderItemApi } from '@/api/order'

// Props
interface Props {
  visible: boolean
  type: 'add' | 'edit'
  data?: any
  orderId: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  type: 'add',
  data: () => ({}),
  orderId: 0
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'submit': []
}>()

// 表单引用
const formRef = ref()

// 状态
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = reactive({
  product_id: '',
  product_specification_id: '',
  quantity: 1,
  unit_price: 0,
  discount: 0,
  tax_rate: 13.0,
  total_price: '0.00',
  notes: ''
})

// 选项数据
const productOptions = ref([])
const specificationOptions = ref([])

// 表单验证规则
const rules = {
  product_id: [
    { required: true, message: '请选择产品', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  unit_price: [
    { required: true, message: '请输入单价', trigger: 'blur' }
  ],
  tax_rate: [
    { required: true, message: '请输入税率', trigger: 'blur' }
  ],
  notes: [
    { max: 500, message: '备注长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 监听数据变化，填充表单
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, {
      product_id: newData.product_id || '',
      product_specification_id: newData.product_specification_id || '',
      quantity: newData.quantity || 1,
      unit_price: parseFloat(newData.unit_price || '0'),
      discount: parseFloat(newData.discount || '0'),
      tax_rate: parseFloat(newData.tax_rate || '13'),
      total_price: newData.total_price || '0.00',
      notes: newData.notes || ''
    })
    
    // 如果有产品ID，加载产品信息
    if (newData.product_id) {
      loadProductInfo(newData.product_id)
    }
  }
}, { immediate: true, deep: true })

// 搜索产品
const handleSearchProducts = async (query: string) => {
  if (query) {
    try {
      const response = await productApi.getList({ name: query, per_page: 20 }) as any
      productOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('搜索产品失败:', error)
    }
  } else {
    productOptions.value = []
  }
}

// 加载产品信息
const loadProductInfo = async (productId: number) => {
  try {
    const response = await productApi.getById(productId) as any
    const product = response.data || response
    
    // 添加到产品选项中
    if (!productOptions.value.find((p: any) => p.id === product.id)) {
      productOptions.value.push(product)
    }
    
    // 加载产品规格
    if (product.specifications && product.specifications.length > 0) {
      specificationOptions.value = product.specifications
    }
  } catch (error) {
    console.error('加载产品信息失败:', error)
  }
}

// 产品变化处理
const handleProductChange = async (productId: number) => {
  if (productId) {
    await loadProductInfo(productId)
    
    // 重置规格选择
    form.product_specification_id = ''
    form.unit_price = 0
    form.tax_rate = 13.0
    
    calculateTotal()
  } else {
    specificationOptions.value = []
    form.product_specification_id = ''
    form.unit_price = 0
    calculateTotal()
  }
}

// 规格变化处理
const handleSpecificationChange = (specId: number) => {
  if (specId) {
    const spec = specificationOptions.value.find((s: any) => s.id === specId)
    if (spec) {
      form.unit_price = parseFloat(spec.suggested_price || '0')
      form.tax_rate = parseFloat(spec.tax_rate || '13')
      calculateTotal()
    }
  }
}

// 计算小计
const calculateTotal = () => {
  const quantity = form.quantity || 0
  const unitPrice = form.unit_price || 0
  const discount = form.discount || 0
  
  const subtotal = quantity * unitPrice
  const discountAmount = subtotal * (discount / 100)
  const total = subtotal - discountAmount
  
  form.total_price = total.toFixed(2)
}

// 计算含税小计
const calculateTaxTotal = () => {
  const total = parseFloat(form.total_price || '0')
  const taxRate = form.tax_rate || 0
  const taxAmount = total * (taxRate / 100)
  return (total + taxAmount).toFixed(2)
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    product_id: '',
    product_specification_id: '',
    quantity: 1,
    unit_price: 0,
    discount: 0,
    tax_rate: 13.0,
    total_price: '0.00',
    notes: ''
  })
  productOptions.value = []
  specificationOptions.value = []
  formRef.value?.clearValidate()
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理关闭
const handleClosed = () => {
  resetForm()
}

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData = { ...form }
    
    // 处理空值
    if (!submitData.notes || submitData.notes.trim() === '') {
      delete submitData.notes
    }
    
    if (props.type === 'add') {
      // 添加订单项目
      await orderItemApi.add(props.orderId, submitData)
      ElMessage.success('添加项目成功')
    } else {
      // 更新订单项目
      await orderItemApi.update(props.orderId, props.data.id, submitData)
      ElMessage.success('更新项目成功')
    }
    
    // 关闭对话框并触发刷新
    dialogVisible.value = false
    emit('submit')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(props.type === 'add' ? '添加项目失败' : '更新项目失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
