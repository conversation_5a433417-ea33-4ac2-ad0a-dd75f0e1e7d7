import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from config import get_config

if __name__ == '__main__':
    # 获取配置
    config_class = get_config()

    # 创建Flask应用实例
    application = create_app()

    # 应用配置
    application.config.from_object(config_class)

    # 从配置获取主机和端口
    host = application.config.get('HOST', '0.0.0.0')
    port = application.config.get('PORT', 5001)
    debug = application.config.get('DEBUG', True)

    # 启动应用
    print("🚀 启动EMB系统API服务...")
    print(f"📖 API文档地址: http://localhost:{port}/docs/")
    print(f"🧪 测试端点: http://localhost:{port}/test/health")
    print(f"🌍 环境: {os.environ.get('FLASK_ENV', 'development')}")
    print(f"🔧 调试模式: {'开启' if debug else '关闭'}")

    application.run(debug=debug, host=host, port=port)
