<template>
  <el-dialog
    v-model="dialogVisible"
    :title="type === 'add' ? '添加送货地址' : '编辑送货地址'"
    width="600px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contact_person">
            <el-input v-model="form.contact_person" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contact_phone">
            <el-input v-model="form.contact_phone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="省份" prop="province">
            <el-select
              v-model="form.province"
              placeholder="请选择省份"
              style="width: 100%"
              @change="handleProvinceChange"
              clearable
              filterable
            >
              <el-option
                v-for="province in provinces"
                :key="province.value"
                :label="province.label"
                :value="province.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="城市" prop="city">
            <el-select
              v-model="form.city"
              placeholder="请选择城市"
              style="width: 100%"
              @change="handleCityChange"
              clearable
              filterable
              :disabled="!form.province"
            >
              <el-option
                v-for="city in cities"
                :key="city.value"
                :label="city.label"
                :value="city.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="区县" prop="district">
            <el-select
              v-model="form.district"
              placeholder="请选择区县"
              style="width: 100%"
              clearable
              filterable
              :disabled="!form.city"
            >
              <el-option
                v-for="district in districts"
                :key="district.value"
                :label="district.label"
                :value="district.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="详细地址" prop="detailed_address">
        <el-input 
          v-model="form.detailed_address" 
          type="textarea" 
          :rows="3"
          placeholder="请输入详细地址" 
        />
      </el-form-item>
      
      <el-form-item label="是否默认">
        <el-switch v-model="form.is_default" />
      </el-form-item>
      
      <el-form-item label="备注" prop="notes">
        <el-input 
          v-model="form.notes" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息（可选）" 
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ type === 'add' ? '添加' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { customerApi } from '@/api/customer'
import { provinceAndCityData, regionData } from 'element-china-area-data'

// Props
interface Props {
  visible: boolean
  type: 'add' | 'edit'
  data?: any
  customerId: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  type: 'add',
  data: () => ({}),
  customerId: 0
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'submit': [data?: any]
}>()

// 表单引用
const formRef = ref()

// 状态
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = reactive({
  contact_person: '',
  contact_phone: '',
  province: '',
  city: '',
  district: '',
  detailed_address: '',
  is_default: false,
  notes: ''
})

// 省市区数据
const provinces = computed(() => {
  console.log('regionData 结构:', regionData.slice(0, 2))
  console.log('provinceAndCityData 结构:', provinceAndCityData.slice(0, 2))

  // 使用 regionData，它包含完整的三级数据
  return regionData.map(item => ({
    value: item.label,
    label: item.label
  }))
})

const cities = computed(() => {
  if (!form.province) return []

  console.log('查找城市数据:', { province: form.province })

  const province = regionData.find(item => item.label === form.province)
  console.log('找到省份:', province)

  if (!province || !province.children) return []

  const result = province.children.map(item => ({
    value: item.label,
    label: item.label
  }))

  console.log('城市数据:', result)
  return result
})

const districts = computed(() => {
  if (!form.province || !form.city) return []

  console.log('查找区县数据:', { province: form.province, city: form.city })

  const province = regionData.find(item => item.label === form.province)
  console.log('找到省份:', province)

  if (!province || !province.children) return []

  const city = province.children.find(item => item.label === form.city)
  console.log('找到城市:', city)

  if (!city) return []

  // 有些城市可能没有区县数据，直接返回空数组
  if (!city.children || city.children.length === 0) {
    console.log('该城市没有区县数据')
    return []
  }

  const result = city.children.map(item => ({
    value: item.label,
    label: item.label
  }))

  console.log('区县数据:', result)
  return result
})

// 省市区联动处理
const handleProvinceChange = () => {
  form.city = ''
  form.district = ''
}

const handleCityChange = () => {
  form.district = ''
}

// 表单验证规则
const rules = {
  contact_person: [
    { required: true, message: '请输入联系人', trigger: 'blur' },
    { max: 50, message: '联系人长度不能超过 50 个字符', trigger: 'blur' }
  ],
  contact_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  province: [
    { required: true, message: '请选择省份', trigger: 'change' }
  ],
  city: [
    { required: true, message: '请选择城市', trigger: 'change' }
  ],
  district: [
    { required: true, message: '请选择区县', trigger: 'change' }
  ],
  detailed_address: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { max: 200, message: '详细地址长度不能超过 200 个字符', trigger: 'blur' }
  ],
  notes: [
    { max: 500, message: '备注长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 监听数据变化，填充表单
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, {
      contact_person: newData.contact_person || '',
      contact_phone: newData.contact_phone || '',
      province: newData.province || '',
      city: newData.city || '',
      district: newData.district || '',
      detailed_address: newData.detailed_address || '',
      is_default: newData.is_default || false,
      notes: newData.notes || ''
    })
  }
}, { immediate: true, deep: true })

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    contact_person: '',
    contact_phone: '',
    province: '',
    city: '',
    district: '',
    detailed_address: '',
    is_default: false,
    notes: ''
  })
  formRef.value?.clearValidate()
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理关闭
const handleClosed = () => {
  resetForm()
}

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData: any = { ...form }

    // 处理空值 - 删除空字段而不是设置为null
    if (!submitData.notes || submitData.notes.trim() === '') {
      delete submitData.notes
    }

    if (props.customerId === 0) {
      // 新增客户时，不调用API，直接传递数据给父组件
      dialogVisible.value = false
      emit('submit', submitData)
    } else {
      // 编辑已存在客户时，调用API
      if (props.type === 'add') {
        // 添加送货地址
        await customerApi.addDeliveryAddress(props.customerId, submitData)
        ElMessage.success('添加送货地址成功')
      } else {
        // 更新送货地址
        await customerApi.updateDeliveryAddress(props.customerId, props.data.id, submitData)
        ElMessage.success('更新送货地址成功')
      }

      // 关闭对话框并触发刷新
      dialogVisible.value = false
      emit('submit')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(props.type === 'add' ? '添加送货地址失败' : '更新送货地址失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
