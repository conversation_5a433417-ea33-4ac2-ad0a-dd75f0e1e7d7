"""
测试Marshmallow Schema到Flask-RESTx模型的自动转换
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from flask_restx import Api
from app.schemas.product import ProductSchema, BrandSchema, ProductCategorySchema
from app.utils.schema_to_restx import create_input_model, create_output_model


def test_schema_conversion():
    """测试Schema转换功能"""
    app = Flask(__name__)
    api = Api(app)
    
    print("=== 测试Marshmallow Schema到Flask-RESTx模型转换 ===\n")
    
    # 测试ProductSchema转换
    print("1. 测试ProductSchema转换:")
    product_input_model = create_input_model(api, ProductSchema, 'ProductInput')
    product_output_model = create_output_model(api, ProductSchema, 'ProductOutput')
    
    print("输入模型字段:")
    for field_name, field_obj in product_input_model.items():
        required = "必填" if field_obj.required else "可选"
        allow_none = "允许None" if getattr(field_obj, 'allow_none', False) else "不允许None"
        print(f"  - {field_name}: {type(field_obj).__name__} ({required}, {allow_none})")
    
    print("\n输出模型字段:")
    for field_name, field_obj in product_output_model.items():
        required = "必填" if field_obj.required else "可选"
        allow_none = "允许None" if getattr(field_obj, 'allow_none', False) else "不允许None"
        print(f"  - {field_name}: {type(field_obj).__name__} ({required}, {allow_none})")
    
    # 测试BrandSchema转换
    print("\n2. 测试BrandSchema转换:")
    brand_model = create_input_model(api, BrandSchema, 'Brand')
    
    print("品牌模型字段:")
    for field_name, field_obj in brand_model.items():
        required = "必填" if field_obj.required else "可选"
        allow_none = "允许None" if getattr(field_obj, 'allow_none', False) else "不允许None"
        print(f"  - {field_name}: {type(field_obj).__name__} ({required}, {allow_none})")
    
    # 验证关键字段
    print("\n3. 验证关键字段:")
    
    # 检查brand_id字段
    if 'brand_id' in product_input_model:
        brand_id_field = product_input_model['brand_id']
        print(f"✅ brand_id字段存在: {type(brand_id_field).__name__}")
        print(f"   - 必填: {brand_id_field.required}")
        print(f"   - 允许None: {getattr(brand_id_field, 'allow_none', False)}")
    else:
        print("❌ brand_id字段不存在")
    
    # 检查必填字段
    required_fields = [name for name, field in product_input_model.items() if field.required]
    print(f"✅ 必填字段: {required_fields}")
    
    # 检查可选字段
    optional_fields = [name for name, field in product_input_model.items() if not field.required]
    print(f"✅ 可选字段: {optional_fields}")
    
    print("\n=== 转换测试完成 ===")


def compare_with_original():
    """对比原始手动定义和自动生成的差异"""
    app = Flask(__name__)
    api = Api(app)
    
    print("\n=== 对比原始定义和自动生成 ===\n")
    
    # 原始手动定义（修正版本）
    from flask_restx import fields

    # 创建字段并正确设置allow_none
    brand_id_field = fields.Integer(description='品牌ID（可选）')
    brand_id_field.allow_none = True

    image_field = fields.String(description='产品图片')
    image_field.allow_none = True

    description_field = fields.String(description='产品描述')
    description_field.allow_none = True

    notes_field = fields.String(description='备注')
    notes_field.allow_none = True

    original_model = api.model('ProductOriginal', {
        'name': fields.String(required=True, description='产品名称'),
        'model': fields.String(required=True, description='产品型号'),
        'unit': fields.String(required=True, description='计量单位'),
        'category_id': fields.Integer(required=True, description='产品分类ID'),
        'brand_id': brand_id_field,
        'image': image_field,
        'description': description_field,
        'notes': notes_field,
        'status': fields.String(description='状态'),
    })
    
    # 自动生成
    auto_model = create_input_model(api, ProductSchema, 'ProductAuto')
    
    print("字段对比:")
    all_fields = set(original_model.keys()) | set(auto_model.keys())
    
    for field_name in sorted(all_fields):
        original_field = original_model.get(field_name)
        auto_field = auto_model.get(field_name)
        
        if original_field and auto_field:
            orig_required = original_field.required
            auto_required = auto_field.required
            orig_allow_none = getattr(original_field, 'allow_none', False)
            auto_allow_none = getattr(auto_field, 'allow_none', False)
            
            if orig_required == auto_required and orig_allow_none == auto_allow_none:
                print(f"✅ {field_name}: 一致")
            else:
                print(f"⚠️  {field_name}: 不一致")
                print(f"   原始: required={orig_required}, allow_none={orig_allow_none}")
                print(f"   自动: required={auto_required}, allow_none={auto_allow_none}")
        elif original_field:
            print(f"❌ {field_name}: 只在原始定义中存在")
        elif auto_field:
            print(f"➕ {field_name}: 只在自动生成中存在")


if __name__ == "__main__":
    test_schema_conversion()
    compare_with_original()
