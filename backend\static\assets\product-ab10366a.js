import{I as e}from"./index-3d4c440c.js";function r(r){return new Promise(((t,o)=>{e({url:"/api/v1/products",method:"get",params:r}).then((e=>{if(e&&"object"==typeof e){const r=e;if(r.message&&r.message.includes("失败"))return console.warn("产品列表API返回错误消息:",r.message),void t({list:[],pagination:null,message:r.message});if(!r.list){console.warn("产品列表API响应缺少list字段，尝试提取或创建:",e);let o=[];return r.data&&Array.isArray(r.data)?o=r.data:r.items&&Array.isArray(r.items)?o=r.items:Array.isArray(r)&&(o=r),void t({list:o,pagination:r.pagination||r.page_info||r.page||null})}}t(e)})).catch((e=>{console.error("产品列表API调用失败:",e),t({list:[],pagination:null,error:e.message||"未知错误"})}))}))}function t(r){return e({url:`/api/v1/products/${r}`,method:"get"})}function o(r){return e({url:`/api/v1/products/${r}`,method:"DELETE"})}function s(r){return e({url:"/api/v1/products",method:"post",data:r})}function a(r,t){return e({url:`/api/v1/products/${r}`,method:"put",data:t})}function i(r){const t=r||{};return t.filter||(t.filter={}),e({url:"/api/v1/products/export",method:"post",data:t,responseType:"blob",headers:{Accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},timeout:6e4}).then((e=>{if(!(e instanceof Blob))throw new Error("导出失败，返回格式不正确");return"application/json"===e.type?new Promise(((r,t)=>{const o=new FileReader;o.onload=()=>{try{const e=JSON.parse(o.result);t(new Error(e.message||"导出失败"))}catch(e){t(new Error("导出过程中出现错误"))}},o.onerror=()=>t(new Error("导出过程中出现错误")),o.readAsText(e)})):e}))}function n(){return e({url:"/api/v1/products/export-template",method:"get",responseType:"blob",headers:{Accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}).then((e=>{if(!(e instanceof Blob))throw new Error("获取模板失败，返回格式不正确");return"application/json"===e.type?new Promise(((r,t)=>{const o=new FileReader;o.onload=()=>{try{const e=JSON.parse(o.result);t(new Error(e.message||"获取模板失败"))}catch(e){t(new Error("获取模板过程中出现错误"))}},o.onerror=()=>t(new Error("获取模板过程中出现错误")),o.readAsText(e)})):e}))}function p(r){if(r.size>52428800)return Promise.reject(new Error(`文件大小不能超过50MB，当前大小: ${(r.size/1024/1024).toFixed(2)}MB`));if(!r.name.endsWith(".xlsx")&&!r.name.endsWith(".xls"))return Promise.reject(new Error("仅支持Excel文件(.xlsx, .xls)格式"));const t=new FormData;return t.append("file",r),e({url:"/api/v1/products/preview-import",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"},timeout:6e4})}function c(r){const t=JSON.parse(JSON.stringify(r));return t.forEach((e=>{e.specifications&&Array.isArray(e.specifications)&&e.specifications.forEach((e=>{void 0!==e.cost_price&&(e.cost_price=Number(e.cost_price)||0),void 0!==e.suggested_price&&(e.suggested_price=Number(e.suggested_price)||0),void 0!==e.tax_rate&&(e.tax_rate=Number(e.tax_rate)||13),void 0!==e.min_price&&(e.min_price=Number(e.min_price)||0),void 0!==e.max_price&&(e.max_price=Number(e.max_price)||0)}))})),e({url:"/api/v1/products/import",method:"post",data:{products:t},timeout:6e4})}export{t as a,s as c,o as d,i as e,n as g,r as l,p,c as s,a as u};
