<template>
  <div class="customer-balance-list">
    <!-- 搜索筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索客户名称"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.customer_id"
            placeholder="选择客户"
            clearable
            filterable
            @change="handleSearch"
          >
            <el-option
              v-for="customer in customers"
              :key="customer.id"
              :label="customer.name"
              :value="customer.id"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      border
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="customer_name" label="客户名称" min-width="200" />
      
      <el-table-column prop="balance" label="可用余额" width="150" sortable="custom">
        <template #default="{ row }">
          <span class="balance-amount" :class="{ 'positive': row.balance > 0 }">
            {{ formatCurrency(row.balance) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="frozen_balance" label="冻结余额" width="150">
        <template #default="{ row }">
          <span class="frozen-amount">
            {{ formatCurrency(row.frozen_balance) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="总余额" width="150">
        <template #default="{ row }">
          <span class="total-amount">
            {{ formatCurrency(row.balance + row.frozen_balance) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="updated_at" label="更新时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.updated_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleRecharge(row)"
          >
            <el-icon><Plus /></el-icon>
            充值
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="handleViewTransactions(row)"
          >
            <el-icon><List /></el-icon>
            交易记录
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.per_page"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Plus, List } from '@element-plus/icons-vue'
import { paymentApi } from '@/api/payment'
import type { CustomerBalance } from '@/types/payment'
import { customerApi } from '@/api/customer'

// 定义事件
const emit = defineEmits<{
  recharge: [customerId: number]
  viewTransactions: [customerId: number]
}>()

// 响应式数据
const loading = ref(false)
const tableData = ref<CustomerBalance[]>([])
const customers = ref<any[]>([])

// 搜索表单
const searchForm = reactive({
  search: '',
  customer_id: undefined
})

// 分页数据
const pagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

// 排序数据
const sortData = reactive({
  prop: '',
  order: ''
})

// 格式化货币
const formatCurrency = (amount: number): string => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 格式化日期时间
const formatDateTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 加载客户列表
const loadCustomers = async () => {
  try {
    const response = await customerApi.getList({ per_page: 1000 })
    customers.value = response.data?.data || []
  } catch (error) {
    console.error('加载客户列表失败:', error)
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      per_page: pagination.per_page,
      ...searchForm
    }

    const response = await paymentApi.getCustomerBalances(params)
    tableData.value = response.data || []
    pagination.total = response.pagination?.total || 0
  } catch (error) {
    console.error('加载客户余额列表失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 排序处理
const handleSortChange = ({ prop, order }: any) => {
  sortData.prop = prop
  sortData.order = order
  loadData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.per_page = size
  pagination.page = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 处理充值
const handleRecharge = (row: CustomerBalance) => {
  emit('recharge', row.customer_id)
}

// 查看交易记录
const handleViewTransactions = (row: CustomerBalance) => {
  emit('viewTransactions', row.customer_id)
}

// 刷新数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  refresh
})

// 组件挂载时加载数据
onMounted(() => {
  loadCustomers()
  loadData()
})
</script>

<style scoped>
.customer-balance-list {
  padding: 0;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.balance-amount {
  font-weight: bold;
  color: #909399;
}

.balance-amount.positive {
  color: #67C23A;
}

.frozen-amount {
  color: #E6A23C;
  font-weight: 500;
}

.total-amount {
  font-weight: bold;
  color: #303133;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>
