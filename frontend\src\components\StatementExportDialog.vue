<template>
  <el-dialog
    v-model="visible"
    title="导出对账单配置"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form :model="exportConfig" label-width="120px">
      <el-form-item label="导出格式">
        <el-radio-group v-model="exportConfig.format">
          <el-radio value="xlsx">Excel (XLSX)</el-radio>
          <el-radio value="pdf">PDF</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="包含表头">
        <el-switch v-model="exportConfig.includeHeader" />
      </el-form-item>
      
      <el-form-item label="导出列">
        <div class="export-columns-container">
          <div class="select-all-container">
            <el-checkbox v-model="exportConfig.selectAll" @change="handleSelectAllChange" class="select-all-checkbox">
              全选
            </el-checkbox>
          </div>

          <div class="columns-grid">
            <!-- 基本信息字段 -->
            <div class="column-category">
              <div class="category-header">
                <div class="category-title">基本信息</div>
                <el-checkbox v-model="basicSelectAll" @change="handleBasicSelectAllChange" class="category-select-all">
                  全选
                </el-checkbox>
              </div>
              <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
                <el-checkbox v-for="column in basicColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                  {{ column.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 发货单明细字段 -->
            <div class="column-category">
              <div class="category-header">
                <div class="category-title">发货单明细</div>
                <el-checkbox v-model="deliverySelectAll" @change="handleDeliverySelectAllChange" class="category-select-all">
                  全选
                </el-checkbox>
              </div>
              <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
                <el-checkbox v-for="column in deliveryColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                  {{ column.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>

            <!-- 收款记录字段 -->
            <div class="column-category">
              <div class="category-header">
                <div class="category-title">收款记录</div>
                <el-checkbox v-model="paymentSelectAll" @change="handlePaymentSelectAllChange" class="category-select-all">
                  全选
                </el-checkbox>
              </div>
              <el-checkbox-group v-model="exportConfig.selectedColumns" class="column-checkboxes two-columns">
                <el-checkbox v-for="column in paymentColumns" :key="column.prop" :value="column.prop" @change="handleColumnChange">
                  {{ column.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exporting">
          确认导出
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { statementApi } from '@/api/statement'

interface Props {
  modelValue: boolean
  statementId?: number
  statementName?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  statementId: undefined,
  statementName: ''
})

const emit = defineEmits<Emits>()

const visible = ref(false)
const exporting = ref(false)

// 定义可导出的字段
const availableColumns = [
  // 基本信息字段
  { prop: 'statement_number', label: '对账单号', category: 'basic' },
  { prop: 'customer_name', label: '客户名称', category: 'basic' },
  { prop: 'statement_date', label: '对账日期', category: 'basic' },
  { prop: 'due_date', label: '应付款日期', category: 'basic' },
  { prop: 'status', label: '对账单状态', category: 'basic' },
  { prop: 'total_amount', label: '总金额', category: 'basic' },
  { prop: 'discount_amount', label: '优惠金额', category: 'basic' },
  { prop: 'adjusted_total_amount', label: '实际金额', category: 'basic' },
  { prop: 'paid_amount', label: '已收金额', category: 'basic' },
  { prop: 'unpaid_amount', label: '未收金额', category: 'basic' },
  { prop: 'notes', label: '备注', category: 'basic' },
  { prop: 'created_at', label: '创建时间', category: 'basic' },
  
  // 发货单明细字段
  { prop: 'delivery_number', label: '发货单号', category: 'delivery' },
  { prop: 'order_number', label: '订单号', category: 'delivery' },
  { prop: 'delivery_date', label: '发货日期', category: 'delivery' },
  { prop: 'delivery_status', label: '发货状态', category: 'delivery' },
  { prop: 'delivery_amount', label: '发货金额', category: 'delivery' },
  { prop: 'logistics_company', label: '物流公司', category: 'delivery' },
  { prop: 'tracking_number', label: '快递单号', category: 'delivery' },
  
  // 收款记录字段
  { prop: 'payment_date', label: '收款日期', category: 'payment' },
  { prop: 'payment_amount', label: '收款金额', category: 'payment' },
  { prop: 'payment_method', label: '支付方式', category: 'payment' },
  { prop: 'payment_source', label: '付款来源', category: 'payment' },
  { prop: 'reference_number', label: '交易流水号', category: 'payment' },
  { prop: 'bank_account', label: '收款账户', category: 'payment' },
  { prop: 'payment_notes', label: '收款备注', category: 'payment' }
]

// 分离不同分类的字段
const basicColumns = computed(() => availableColumns.filter(col => col.category === 'basic'))
const deliveryColumns = computed(() => availableColumns.filter(col => col.category === 'delivery'))
const paymentColumns = computed(() => availableColumns.filter(col => col.category === 'payment'))

const exportConfig = reactive({
  format: 'xlsx' as 'xlsx' | 'pdf',
  includeHeader: true,
  selectedColumns: availableColumns.map(col => col.prop),
  selectAll: true
})

// 分类全选状态
const basicSelectAll = ref(true)
const deliverySelectAll = ref(true)
const paymentSelectAll = ref(true)

const handleSelectAllChange = (val: boolean) => {
  if (val) {
    exportConfig.selectedColumns = availableColumns.map(col => col.prop)
    basicSelectAll.value = true
    deliverySelectAll.value = true
    paymentSelectAll.value = true
  } else {
    exportConfig.selectedColumns = []
    basicSelectAll.value = false
    deliverySelectAll.value = false
    paymentSelectAll.value = false
  }
}

// 基本信息分类全选处理
const handleBasicSelectAllChange = (val: boolean) => {
  const basicProps = basicColumns.value.map(col => col.prop)
  if (val) {
    const newSelectedColumns = [...exportConfig.selectedColumns]
    basicProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !basicProps.includes(col))
  }
  updateSelectAllStatus()
}

// 发货单明细分类全选处理
const handleDeliverySelectAllChange = (val: boolean) => {
  const deliveryProps = deliveryColumns.value.map(col => col.prop)
  if (val) {
    const newSelectedColumns = [...exportConfig.selectedColumns]
    deliveryProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !deliveryProps.includes(col))
  }
  updateSelectAllStatus()
}

// 收款记录分类全选处理
const handlePaymentSelectAllChange = (val: boolean) => {
  const paymentProps = paymentColumns.value.map(col => col.prop)
  if (val) {
    const newSelectedColumns = [...exportConfig.selectedColumns]
    paymentProps.forEach(prop => {
      if (!newSelectedColumns.includes(prop)) {
        newSelectedColumns.push(prop)
      }
    })
    exportConfig.selectedColumns = newSelectedColumns
  } else {
    exportConfig.selectedColumns = exportConfig.selectedColumns.filter(col => !paymentProps.includes(col))
  }
  updateSelectAllStatus()
}

// 更新全选状态
const updateSelectAllStatus = () => {
  const basicProps = basicColumns.value.map(col => col.prop)
  const deliveryProps = deliveryColumns.value.map(col => col.prop)
  const paymentProps = paymentColumns.value.map(col => col.prop)
  
  // 更新分类全选状态
  basicSelectAll.value = basicProps.every(prop => exportConfig.selectedColumns.includes(prop))
  deliverySelectAll.value = deliveryProps.every(prop => exportConfig.selectedColumns.includes(prop))
  paymentSelectAll.value = paymentProps.every(prop => exportConfig.selectedColumns.includes(prop))
  
  // 更新总全选状态
  exportConfig.selectAll = exportConfig.selectedColumns.length === availableColumns.length
}

const handleColumnChange = () => {
  updateSelectAllStatus()
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleClose = () => {
  visible.value = false
}

// 导出对账单
const handleExport = async () => {
  if (!props.statementId) {
    ElMessage.error('缺少对账单ID')
    return
  }

  if (exportConfig.selectedColumns.length === 0) {
    ElMessage.warning('请至少选择一列进行导出')
    return
  }

  exporting.value = true
  try {
    const response = await statementApi.export(props.statementId, {
      format: exportConfig.format,
      columns: exportConfig.selectedColumns,
      include_header: exportConfig.includeHeader
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(response)
    const link = document.createElement('a')
    link.href = url
    link.download = `对账单_${props.statementName || props.statementId}.${exportConfig.format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped>
.export-columns-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.select-all-container {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.select-all-checkbox {
  margin-bottom: 0;
}

.columns-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.column-category {
  margin-bottom: 16px;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.category-title {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.category-select-all {
  font-size: 12px;
  color: #409eff;
}

.column-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 20px;
}

.column-checkboxes.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 16px;
  padding-left: 20px;
}

.column-checkboxes .el-checkbox {
  margin-right: 0;
}
</style>
