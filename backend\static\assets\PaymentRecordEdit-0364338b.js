import{u as e,b as a,r as t,d as o,L as l,w as n,e as r,f as s,M as u,o as d,c,i,h as m,N as p,g as f,U as _,a as y,t as g,k as b,l as h,F as v,m as k,j as A,W as w,ak as N}from"./index-3d4c440c.js";import{l as D,c as V}from"./order-d6035a15.js";import{b as I,c as M,d as S,u as T}from"./finance-789ce3e6.js";import{g as F}from"./settings-1b359923.js";import{_ as j}from"./_plugin-vue_export-helper-1b428a4d.js";const x={class:"payment-record-edit"},U={class:"card-header"},$={class:"left"},Y={class:"page-title"},C={class:"right"},P={key:1,class:"form-section-title"},R={key:3,class:"form-section-title"},q=j({__name:"PaymentRecordEdit",setup(j){const q=e(),B=a(),L=t(null),O=t(!1),z=t(!1),E=t(!1),K=o((()=>"PaymentRecordEdit"===q.name)),W=o((()=>{var e;return"PaymentRecordDetail"===q.name||!0===(null==(e=q.meta)?void 0:e.isView)})),G=l({id:"",paymentNo:"",orderId:"",orderNo:"",customerId:"",customerName:"",paymentDate:(new Date).toISOString().split("T")[0],paymentMethod:"",amount:0,bankAccount:"",bankAccountDisplay:"",notes:"",status:"pending",transactionProof:[]}),H={orderId:[{required:!0,message:"请选择关联订单",trigger:"change"}],paymentDate:[{required:!0,message:"请选择收款日期",trigger:"change"}],paymentMethod:[{required:!0,message:"请选择收款方式",trigger:"change"}],amount:[{required:!0,message:"请输入收款金额",trigger:"blur"},{type:"number",min:.01,message:"收款金额必须大于0",trigger:"blur"}],bankAccount:[{required:!0,message:"请选择收款账户",trigger:"change"}]},J=t([]),Q=t([]),X=l({orderNo:"",totalAmount:0,paidAmount:0,unpaidAmount:0,status:"",statusText:"",createTime:""}),Z=t([]),ee=t([]),ae=async e=>{E.value=!0;try{const a={status:["已确认","发货中","已发货"],page:1,per_page:20};e&&(a.keyword=e),console.log("搜索订单参数:",a);const t=await D(a);console.log("搜索订单响应:",t),t&&t.data&&t.data.items?(J.value=t.data.items.map((e=>({id:e.id,orderNo:e.order_number,customerName:e.customer_name,totalAmount:e.total_amount,paidAmount:e.paid_amount,unpaidAmount:e.unpaid_amount||e.total_amount-e.paid_amount}))),console.log("订单选项已更新，数量:",J.value.length)):(J.value=[],console.warn("响应中没有找到items数组或为空"))}catch(a){console.error("搜索订单失败:",a),_.error("搜索订单失败")}finally{E.value=!1}},te=async()=>{E.value=!0;try{console.log("开始加载默认订单列表...");const e={status:["已确认","发货中","已发货"],page:1,per_page:10,sort:"-created_at"};console.log("请求参数:",e);const a=await D(e);console.log("获取订单列表响应:",a),a&&a.data&&a.data.items?(J.value=a.data.items.map((e=>({id:e.id,orderNo:e.order_number,customerName:e.customer_name,totalAmount:e.total_amount,paidAmount:e.paid_amount,unpaidAmount:e.unpaid_amount||e.total_amount-e.paid_amount}))),console.log("订单选项已更新，数量:",J.value.length)):(console.warn("响应中没有找到items数组"),_.warning("没有找到可收款的订单"))}catch(e){console.error("加载默认订单列表失败:",e),e.response&&(console.error("错误响应状态:",e.response.status),console.error("错误响应数据:",e.response.data)),_.error("加载订单列表失败，请稍后重试")}finally{E.value=!1}},oe=e=>{e&&0===J.value.length&&te()},le=async e=>{if(console.log("订单选择变化，选择的订单ID:",e),!e)return G.customerName="",G.customerId="",G.amount=0,X.orderNo="",X.totalAmount=0,X.paidAmount=0,X.unpaidAmount=0,X.status="",X.statusText="",X.createTime="",void(Z.value=[]);try{const a=J.value.find((a=>a.id===e));a&&(console.log("从选项中获取的订单信息:",a),G.customerName=a.customerName);const t=await V(e.toString());if(console.log("订单详情响应:",t),t&&t.data){const a=t.data;console.log("订单详情数据:",a),X.orderNo=a.order_number||"",X.totalAmount=parseFloat(a.total_amount)||0,X.paidAmount=parseFloat(a.paid_amount)||0,X.unpaidAmount=parseFloat(a.total_amount)-parseFloat(a.paid_amount)||0,X.status=a.status||"",X.statusText=a.status_text||a.status||"",X.createTime=a.created_at||"",console.log("更新后的订单信息:",X),a.customer_name&&(G.customerName=a.customer_name),a.customer_id&&(G.customerId=a.customer_id),K.value||W.value||(G.amount=X.unpaidAmount),await ne(e)}}catch(a){console.error("获取订单信息失败:",a),_.error("获取订单信息失败")}},ne=async e=>{try{console.log("获取收款历史，订单ID:",e);const a=await M(e);console.log("收款历史响应:",a);const t=q.params.id?parseInt(q.params.id):null;let o=[];if(a&&a.data)if(Array.isArray(a.data))o=a.data;else if(a.data.items&&Array.isArray(a.data.items))o=a.data.items;else if("object"==typeof a.data){const e=Object.values(a.data).find((e=>Array.isArray(e)));e&&(o=e)}console.log("处理后的收款历史数据:",o),Z.value=o.filter((e=>!t||e.id!==t)).map((e=>({id:e.id,paymentNo:e.payment_number||`SK${e.id}`,amount:parseFloat(e.amount)||0,paymentMethod:e.payment_method,paymentDate:e.payment_date,createBy:e.created_by||"-",status:e.status}))),console.log("格式化后的收款历史列表:",Z.value)}catch(a){console.error("获取收款历史失败:",a),a.response&&(console.error("错误响应状态:",a.response.status),console.error("错误响应数据:",a.response.data)),_.error("获取收款历史失败"),Z.value=[]}},re=async e=>{try{const{file:a}=e;if(!["image/jpeg","image/png","application/pdf"].includes(a.type))return void _.error("只支持jpg、png、pdf格式文件");if(a.size>10485760)return void _.error("文件大小不能超过10MB");_.info("正在上传文件，请稍候..."),await new Promise((e=>setTimeout(e,1e3)));const t=Date.now().toString();ee.value.push({name:a.name,url:URL.createObjectURL(a),id:t}),G.transactionProof.push(t),_.success("文件上传成功")}catch(a){console.error("文件上传失败:",a),_.error("文件上传失败")}},se=async(e=!1)=>{z.value=!0;try{await L.value.validate();const t={order_id:G.orderId,payment_date:G.paymentDate,amount:G.amount,payment_method:G.paymentMethod,bank_account:"bank_transfer"===G.paymentMethod?String(G.bankAccount):"",notes:G.notes};console.log("提交的收款记录数据:",t);try{const a=await S(t);if(console.log("保存收款记录响应:",a),e&&a&&a.data&&a.data.id){const e=await T(a.data.id,"已确认");console.log("更新收款记录状态响应:",e)}_.success(K.value?"收款记录修改成功":"收款记录创建成功"),B.push("/payment-records")}catch(a){console.error("API调用失败:",a),a.response&&(console.error("错误响应状态:",a.response.status),console.error("错误响应数据:",a.response.data)),_.error("保存失败: "+(a.message||"未知错误"))}}catch(t){console.error("保存收款记录失败:",t),_.error("表单验证失败，请检查填写的内容")}finally{z.value=!1}},ue=()=>{se(!1)},de=()=>{w.confirm("确认收款后将无法修改收款信息，是否继续？","确认提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((()=>{se(!0)})).catch((()=>{}))},ce=()=>{B.push("/payment-records")},ie=e=>({pending:"待确认",confirmed:"已确认",canceled:"已取消"}[e]||e),me=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN")},pe=e=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(e);return n((()=>G.paymentMethod),(e=>{if(console.log("收款方式变化:",e),"bank_transfer"===e){if(!G.bankAccount&&Q.value.length>0){console.log("自动选择银行账户");const e=Q.value.find((e=>e.isDefault));e?(console.log("选择默认账户:",e),G.bankAccount=e.id):(console.log("选择第一个账户:",Q.value[0]),G.bankAccount=Q.value[0].id)}}else G.bankAccount="",G.bankAccountDisplay=""})),r((()=>{console.log("组件挂载，路由名称:",q.name,"路由参数:",q.params),console.log("isEdit:",K.value,"isView:",W.value);const e=q.params.id;e?(console.log("编辑或查看模式，ID:",e),(async e=>{O.value=!0;try{console.log("获取收款记录详情，ID:",e);const a=await I(e);if(console.log("收款记录详情响应:",a),!a||!a.data)return void _.error("获取收款记录详情失败");const t=a.data;console.log("收款记录详情数据:",t),G.id=t.id,G.paymentNo=t.payment_number||`SK${t.id}`,G.paymentDate=t.payment_date,G.paymentMethod=t.payment_method,G.amount=parseFloat(t.amount)||0,G.notes=t.notes,G.status=t.status,G.orderId=t.order_id,G.customerName=t.customer_name||"",G.customerId=t.customer_id,"bank_transfer"===t.payment_method&&(t.bank_account_info?(console.log("使用后端格式化的银行账户信息:",t.bank_account_info),G.bankAccountDisplay=t.bank_account_info,G.bankAccount=t.bank_account):(G.bankAccount=t.bank_account,console.log("使用原始银行账户信息:",t.bank_account))),console.log("填充后的表单数据:",G),t.order_id?(X.orderNo=t.order_number||"",X.totalAmount=parseFloat(t.total_amount)||0,X.paidAmount=parseFloat(t.paid_amount)||0,X.unpaidAmount=parseFloat(t.total_amount)-parseFloat(t.paid_amount)||0,X.status=t.status||"",X.statusText=t.status||"",X.createTime=t.created_at||"",console.log("从收款记录中获取的订单信息:",X),await ne(t.order_id)):console.warn("收款记录中没有关联订单ID")}catch(a){console.error("获取收款记录详情失败:",a),a.response&&(console.error("错误响应状态:",a.response.status),console.error("错误响应数据:",a.response.data)),_.error("获取收款记录详情失败")}finally{O.value=!1}})(e)):(console.log("新增模式"),G.paymentDate=(new Date).toISOString().split("T")[0],G.paymentNo=`SK${(new Date).getFullYear()}${String((new Date).getMonth()+1).padStart(2,"0")}${String((new Date).getDate()).padStart(2,"0")}${String(Math.floor(1e3*Math.random())).padStart(3,"0")}`),te(),(async()=>{try{const e=await F();console.log("银行账户响应:",e);let a=[];Array.isArray(e)?a=e:e&&"object"==typeof e&&(e.data&&Array.isArray(e.data)?a=e.data:e.items&&Array.isArray(e.items)&&(a=e.items)),Q.value=a.map((e=>({id:e.id,bankName:e.bank_name,accountName:e.account_name,accountNumber:e.account_number,isDefault:e.is_default})));const t=Q.value.find((e=>e.isDefault));t&&"bank_transfer"===G.paymentMethod&&!G.bankAccount&&(G.bankAccount=t.id)}catch(e){console.error("获取银行账户失败:",e),_.error("获取银行账户失败")}})()})),(e,a)=>{const t=s("el-tag"),o=s("el-icon"),l=s("el-button"),n=s("el-card"),r=s("el-input"),_=s("el-form-item"),w=s("el-col"),D=s("el-date-picker"),V=s("el-row"),I=s("el-option"),M=s("el-select"),S=s("el-input-number"),T=s("el-descriptions-item"),F=s("el-descriptions"),j=s("el-table-column"),q=s("el-table"),B=s("el-upload"),te=s("el-form"),ne=u("loading");return d(),c("div",x,[i(n,{class:"header-card mb-20"},{default:m((()=>[y("div",U,[y("div",$,[y("h2",Y,g(K.value?"编辑收款记录":"新增收款记录"),1),i(t,{type:"info"},{default:m((()=>[b(g(K.value?"修改已有收款记录信息":"创建新的收款记录"),1)])),_:1})]),y("div",C,[i(l,{onClick:ce},{default:m((()=>[i(o,null,{default:m((()=>[i(h(N))])),_:1}),a[9]||(a[9]=b(" 返回列表 "))])),_:1})])])])),_:1}),p((d(),f(n,null,{default:m((()=>[i(te,{ref_key:"formRef",ref:L,model:G,rules:H,"label-width":"120px",class:"payment-form","status-icon":""},{default:m((()=>[a[15]||(a[15]=y("div",{class:"form-section-title"},"基本信息",-1)),i(V,{gutter:20},{default:m((()=>[i(w,{span:12},{default:m((()=>[i(_,{label:"收款单号",prop:"paymentNo"},{default:m((()=>[K.value?(d(),f(r,{key:0,modelValue:G.paymentNo,"onUpdate:modelValue":a[0]||(a[0]=e=>G.paymentNo=e),placeholder:"系统自动生成",disabled:""},null,8,["modelValue"])):(d(),f(r,{key:1,modelValue:G.paymentNo,"onUpdate:modelValue":a[1]||(a[1]=e=>G.paymentNo=e),placeholder:"系统自动生成，可手动修改"},null,8,["modelValue"]))])),_:1})])),_:1}),i(w,{span:12},{default:m((()=>[i(_,{label:"收款日期",prop:"paymentDate"},{default:m((()=>[i(D,{modelValue:G.paymentDate,"onUpdate:modelValue":a[2]||(a[2]=e=>G.paymentDate=e),type:"date",placeholder:"选择收款日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(V,{gutter:20},{default:m((()=>[i(w,{span:12},{default:m((()=>[i(_,{label:"关联订单",prop:"orderId"},{default:m((()=>[i(M,{modelValue:G.orderId,"onUpdate:modelValue":a[3]||(a[3]=e=>G.orderId=e),placeholder:"选择关联订单",filterable:"",remote:"","remote-method":ae,loading:E.value,style:{width:"100%"},onChange:le,"reserve-keyword":!1,clearable:"",onVisibleChange:oe},{default:m((()=>[(d(!0),c(v,null,k(J.value,(e=>(d(),f(I,{key:e.id,label:`${e.orderNo} - ${e.customerName}`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})])),_:1}),i(w,{span:12},{default:m((()=>[i(_,{label:"客户名称",prop:"customerName"},{default:m((()=>[i(r,{modelValue:G.customerName,"onUpdate:modelValue":a[4]||(a[4]=e=>G.customerName=e),placeholder:"选择订单后自动填充",disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(V,{gutter:20},{default:m((()=>[i(w,{span:12},{default:m((()=>[i(_,{label:"收款方式",prop:"paymentMethod"},{default:m((()=>[i(M,{modelValue:G.paymentMethod,"onUpdate:modelValue":a[5]||(a[5]=e=>G.paymentMethod=e),placeholder:"选择收款方式",style:{width:"100%"}},{default:m((()=>[i(I,{label:"银行转账",value:"bank_transfer"}),i(I,{label:"现金",value:"cash"}),i(I,{label:"在线支付",value:"online_payment"}),i(I,{label:"支票",value:"check"}),i(I,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),i(w,{span:12},{default:m((()=>[i(_,{label:"收款金额",prop:"amount"},{default:m((()=>[i(S,{modelValue:G.amount,"onUpdate:modelValue":a[6]||(a[6]=e=>G.amount=e),precision:2,step:100,min:0,style:{width:"100%"},placeholder:"输入收款金额"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),"bank_transfer"===G.paymentMethod?(d(),f(V,{key:0,gutter:20},{default:m((()=>[i(w,{span:24},{default:m((()=>[i(_,{label:"收款账户",prop:"bankAccount"},{default:m((()=>[W.value?(d(),f(r,{key:0,"model-value":G.bankAccountDisplay||G.bankAccount,disabled:""},null,8,["model-value"])):(d(),f(M,{key:1,modelValue:G.bankAccount,"onUpdate:modelValue":a[7]||(a[7]=e=>G.bankAccount=e),placeholder:"选择公司收款账户",filterable:"",style:{width:"100%"}},{default:m((()=>[(d(!0),c(v,null,k(Q.value,(e=>(d(),f(I,{key:e.id,label:`${e.bankName} - ${e.accountNumber} (${e.accountName})`,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]))])),_:1})])),_:1})])),_:1})):A("",!0),G.orderId?(d(),c("div",P,"订单信息")):A("",!0),G.orderId?(d(),f(V,{key:2,gutter:20},{default:m((()=>[i(w,{span:24},{default:m((()=>[i(F,{column:3,border:""},{default:m((()=>[i(T,{label:"订单编号"},{default:m((()=>[b(g(X.orderNo),1)])),_:1}),i(T,{label:"订单金额"},{default:m((()=>[b(g(pe(X.totalAmount)),1)])),_:1}),i(T,{label:"创建日期"},{default:m((()=>[b(g(me(X.createTime)),1)])),_:1}),i(T,{label:"订单状态"},{default:m((()=>{return[i(t,{type:(e=X.status,{draft:"info",pending:"warning",processing:"primary",completed:"success",canceled:"danger"}[e]||"info")},{default:m((()=>[b(g(X.statusText),1)])),_:1},8,["type"])];var e})),_:1}),i(T,{label:"已收款"},{default:m((()=>[b(g(pe(X.paidAmount)),1)])),_:1}),i(T,{label:"未收款"},{default:m((()=>[b(g(pe(X.totalAmount-X.paidAmount)),1)])),_:1})])),_:1})])),_:1})])),_:1})):A("",!0),G.orderId&&Z.value.length>0?(d(),c("div",R,"收款记录历史")):A("",!0),G.orderId&&Z.value.length>0?(d(),f(V,{key:4,gutter:20},{default:m((()=>[i(w,{span:24},{default:m((()=>[i(q,{data:Z.value,border:"",stripe:"",style:{width:"100%"}},{default:m((()=>[i(j,{prop:"paymentNo",label:"收款单号","min-width":"120"}),i(j,{prop:"amount",label:"收款金额",width:"120"},{default:m((e=>[b(g(pe(e.row.amount)),1)])),_:1}),i(j,{prop:"paymentMethod",label:"收款方式",width:"120"},{default:m((e=>{return[b(g((a=e.row.paymentMethod,{bank_transfer:"银行转账",cash:"现金",online_payment:"在线支付",check:"支票",other:"其他"}[a]||a)),1)];var a})),_:1}),i(j,{prop:"paymentDate",label:"收款日期",width:"120"}),i(j,{prop:"createBy",label:"录入人",width:"100"}),i(j,{prop:"status",label:"状态",width:"100"},{default:m((e=>{return[i(t,{type:(a=e.row.status,{pending:"warning",confirmed:"success",canceled:"danger"}[a]||"info")},{default:m((()=>[b(g(ie(e.row.status)),1)])),_:2},1032,["type"])];var a})),_:1})])),_:1},8,["data"])])),_:1})])),_:1})):A("",!0),a[16]||(a[16]=y("div",{class:"form-section-title"},"附加信息",-1)),i(V,{gutter:20},{default:m((()=>[i(w,{span:24},{default:m((()=>[i(_,{label:"交易凭证",prop:"transactionProof"},{default:m((()=>[i(B,{class:"transaction-proof-upload",action:"","http-request":re,"file-list":ee.value,limit:5,multiple:""},{tip:m((()=>a[11]||(a[11]=[y("div",{class:"el-upload__tip"},"支持jpg、png、pdf格式，单个文件不超过10MB",-1)]))),default:m((()=>[i(l,{type:"primary"},{default:m((()=>a[10]||(a[10]=[b("上传凭证")]))),_:1})])),_:1},8,["file-list"])])),_:1})])),_:1})])),_:1}),i(V,{gutter:20},{default:m((()=>[i(w,{span:24},{default:m((()=>[i(_,{label:"备注说明",prop:"notes"},{default:m((()=>[i(r,{modelValue:G.notes,"onUpdate:modelValue":a[8]||(a[8]=e=>G.notes=e),type:"textarea",rows:3,placeholder:"请输入收款相关的补充说明"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(_,{class:"form-buttons"},{default:m((()=>[i(l,{onClick:ce},{default:m((()=>a[12]||(a[12]=[b("取消")]))),_:1}),i(l,{type:"primary",onClick:ue,loading:z.value},{default:m((()=>a[13]||(a[13]=[b("保存")]))),_:1},8,["loading"]),i(l,{type:"success",onClick:de,loading:z.value},{default:m((()=>a[14]||(a[14]=[b("保存并确认收款")]))),_:1},8,["loading"])])),_:1})])),_:1},8,["model"])])),_:1})),[[ne,O.value]])])}}},[["__scopeId","data-v-d546a89f"]]);export{q as default};
