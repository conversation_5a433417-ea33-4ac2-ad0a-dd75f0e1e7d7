#!/usr/bin/env python3
"""
重置对账单状态脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.finance import Statement

def reset_statement_status():
    """重置对账单状态为部分收款"""
    app = create_app()
    
    with app.app_context():
        # 获取对账单2
        statement = Statement.query.get(2)
        if not statement:
            print("对账单不存在")
            return
        
        print(f"当前状态: {statement.status}")
        print(f"已付金额: {statement.paid_amount}")
        print(f"实际金额: {statement.adjusted_total_amount}")
        
        # 更新状态为部分收款
        statement.status = '部分收款'
        
        try:
            db.session.commit()
            print("✅ 状态更新成功")
        except Exception as e:
            db.session.rollback()
            print(f"❌ 状态更新失败: {str(e)}")

if __name__ == '__main__':
    reset_statement_status()
