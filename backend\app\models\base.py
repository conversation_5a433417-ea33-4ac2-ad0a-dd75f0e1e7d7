"""
基础模型类
提供所有模型的通用字段和方法
"""
from datetime import datetime
from app import db
from sqlalchemy import inspect


class BaseModel(db.Model):
    """所有模型的基类，提供通用字段和方法"""
    __abstract__ = True

    # 通用时间戳字段
    created_at = db.Column(db.DateTime, default=datetime.now, nullable=False, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新时间')

    def to_dict(self, exclude=None):
        """将模型转换为字典
        
        Args:
            exclude: 排除的字段列表
        
        Returns:
            dict: 模型的字典表示
        """
        if exclude is None:
            exclude = []
            
        result = {}
        for key in inspect(self).attrs.keys():
            if key not in exclude:
                value = getattr(self, key)
                # 处理datetime对象的序列化
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result
    
    def from_dict(self, data):
        """从字典更新模型
        
        Args:
            data: 包含字段值的字典
            
        Returns:
            self: 更新后的模型实例
        """
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
        return self
    
    def save(self):
        """保存模型到数据库"""
        db.session.add(self)
        db.session.commit()
        return self
    
    def delete(self):
        """从数据库删除模型"""
        db.session.delete(self)
        db.session.commit()
        return self
    
    def __repr__(self):
        """模型的字符串表示"""
        return f"<{self.__class__.__name__} {getattr(self, 'id', 'Unknown')}>"
