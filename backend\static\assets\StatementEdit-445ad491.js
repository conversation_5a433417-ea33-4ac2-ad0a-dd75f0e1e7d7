import{J as e,r as t,w as a,f as l,o,g as r,h as d,i as s,k as n,a as i,t as u,c as p,j as c,u as m,b as _,d as v,L as f,e as w,F as y,m as h,l as b,U as g}from"./index-3d4c440c.js";import{s as V}from"./customer-471ca075.js";import{a as k,b as x,f as A,u as j}from"./statement-2acbbb5b.js";import{b as q}from"./formatter-5775d610.js";import{_ as U}from"./_plugin-vue_export-helper-1b428a4d.js";const Y={class:"product-info"},C={key:0,class:"return-info"},D={class:"return-quantity"},S={class:"return-price"},I={class:"return-amount"},M={class:"amount-summary"},P={class:"summary-row"},F={key:0,class:"summary-row"},$={class:"return-amount"},z={class:"summary-row"},N={class:"net-amount"},R=U(e({__name:"ProductDetailDialog",props:{visible:{type:Boolean},data:{},title:{}},emits:["update:visible"],setup(e,{emit:m}){const _=e,v=m,f=t(_.visible);a((()=>_.visible),(e=>{f.value=e})),a(f,(e=>{v("update:visible",e)}));const w=e=>{if(null==e)return"¥0.00";const t=parseFloat(String(e));return isNaN(t)?"¥0.00":`¥${t.toFixed(2)}`},y=()=>{f.value=!1};return(e,t)=>{const a=l("el-table-column"),m=l("el-table"),_=l("el-button"),v=l("el-dialog");return o(),r(v,{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=e=>f.value=e),title:e.title||"产品明细",width:"80%","before-close":y},{footer:d((()=>[s(_,{onClick:y},{default:d((()=>t[6]||(t[6]=[n("关闭")]))),_:1})])),default:d((()=>[i("div",Y,[t[1]||(t[1]=i("h4",null,"发货商品明细：",-1)),s(m,{data:e.data.items||[],border:"",size:"small",style:{width:"100%","margin-bottom":"10px"}},{default:d((()=>[s(a,{type:"index",label:"序号",width:"60"}),s(a,{prop:"product_name",label:"产品名称","min-width":"120","show-overflow-tooltip":""}),s(a,{prop:"product_model",label:"型号",width:"80"}),s(a,{prop:"specification",label:"规格",width:"100","show-overflow-tooltip":""}),s(a,{prop:"unit",label:"单位",width:"60"}),s(a,{prop:"quantity",label:"数量",width:"70"}),s(a,{prop:"unit_price",label:"单价",width:"100"},{default:d((({row:e})=>[n(u(w(e.unit_price)),1)])),_:1}),s(a,{prop:"amount",label:"金额",width:"100"},{default:d((({row:e})=>[n(u(w(e.amount)),1)])),_:1})])),_:1},8,["data"])]),e.data.return_items&&e.data.return_items.length>0?(o(),p("div",C,[t[2]||(t[2]=i("h4",null,"退货商品明细：",-1)),s(m,{data:e.data.return_items,border:"",size:"small",style:{width:"100%","margin-bottom":"10px"}},{default:d((()=>[s(a,{type:"index",label:"序号",width:"60"}),s(a,{prop:"return_order_number",label:"退货单号","min-width":"120"}),s(a,{prop:"product_name",label:"产品名称","min-width":"120","show-overflow-tooltip":""}),s(a,{prop:"product_model",label:"型号",width:"80"}),s(a,{prop:"specification",label:"规格",width:"100","show-overflow-tooltip":""}),s(a,{prop:"unit",label:"单位",width:"60"}),s(a,{prop:"quantity",label:"数量",width:"70"},{default:d((({row:e})=>[i("span",D,u(e.quantity),1)])),_:1}),s(a,{prop:"unit_price",label:"单价",width:"100"},{default:d((({row:e})=>[i("span",S,u(w(e.unit_price)),1)])),_:1}),s(a,{prop:"amount",label:"金额",width:"100"},{default:d((({row:e})=>[i("span",I,u(w(e.amount)),1)])),_:1})])),_:1},8,["data"])])):c("",!0),i("div",M,[i("div",P,[t[3]||(t[3]=i("span",null,"发货金额：",-1)),i("span",null,u(w(e.data.total_amount||0)),1)]),e.data.return_amount>0?(o(),p("div",F,[t[4]||(t[4]=i("span",null,"退货金额：",-1)),i("span",$,u(w(e.data.return_amount)),1)])):c("",!0),i("div",z,[t[5]||(t[5]=i("span",null,"净金额：",-1)),i("span",N,u(w(e.data.net_amount||0)),1)])])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-7d059648"]]),B={class:"statement-edit"},E={class:"card-header"},J={key:0,style:{"margin-top":"10px"}},L=U(e({__name:"StatementEdit",setup(e){const a=m(),U=_(),Y=t(),C=t(!1),D=t({}),S=v((()=>!a.params.id)),I=f({customer_id:"",statement_date:"",due_date:"",notes:"",delivery_note_ids:[]}),M={customer_id:[{required:!0,message:"请选择客户",trigger:"change"}],statement_date:[{required:!0,message:"请选择对账日期",trigger:"change"}],due_date:[{required:!0,message:"请选择应付款日期",trigger:"change"}],delivery_note_ids:[{required:!0,message:"请选择发货单",trigger:"change"}]},P=t([]),F=t([]),$=e=>({pending:"待发出",delivered:"已发出",completed:"已完成",cancelled:"已作废","待发出":"待发出","已发出":"已发出","已完成":"已完成","已作废":"已作废"}[e]||"未知状态"),z=async e=>{try{const t=await V(e);console.log("搜索客户API响应:",t),t&&Array.isArray(t)?P.value=t:t&&t.list?(P.value=t.list,console.log("处理后的客户选项:",P.value)):t&&t.data&&Array.isArray(t.data)?(P.value=t.data,console.log("处理后的客户选项(data):",P.value)):(P.value=[],console.log("没有找到匹配的客户"))}catch(t){console.error("搜索客户失败:",t),g.error("搜索客户失败"),P.value=[]}},N=async()=>{if(I.customer_id)try{g.info("正在获取客户发货单数据...");const e=await k(String(I.customer_id),"已完成");console.log("发货单API原始响应:",e);let t=[];e&&"object"==typeof e&&"data"in e&&e.data&&(t=Array.isArray(e.data)?e.data:[]),console.log("处理后的发货单数据:",t),t.length>0?(F.value=t,g.success(`成功加载 ${t.length} 个发货单`)):(F.value=[],g.warning("该客户没有已完成的发货单"))}catch(e){console.error("获取可用发货单失败:",e),g.error("获取可用发货单失败"),F.value=[]}else F.value=[]},L=async()=>{Y.value&&await Y.value.validate((async e=>{if(e)try{if(S.value){const e={customer_id:I.customer_id,statement_date:I.statement_date,due_date:I.due_date,notes:I.notes||"",delivery_note_ids:I.delivery_note_ids};await A(e),g.success("创建对账单成功")}else{const e={statement_date:I.statement_date,due_date:I.due_date,notes:I.notes,delivery_note_ids:I.delivery_note_ids};await j(a.params.id,e),g.success("更新对账单成功")}U.push("/statements")}catch(t){console.error("保存对账单失败:",t),g.error("保存对账单失败")}}))},T=()=>{U.back()},W=e=>{console.log("选中的发货单:",e),I.delivery_note_ids=e.map((e=>String(e.id)))};return w((async()=>{try{const e=await V("");e&&e.list&&(P.value=e.list)}catch(e){console.error("加载客户列表失败:",e)}S.value?await(async()=>{try{g.info("正在加载已完成发货单数据...");const e=await k(void 0,"已完成");console.log("所有发货单API响应:",e);let t=[];e&&"object"==typeof e&&"data"in e&&e.data&&(t=Array.isArray(e.data)?e.data:[]),console.log("处理后的所有发货单数据:",t),t.length>0?(F.value=t,g.success(`成功加载 ${t.length} 个已完成发货单`)):(F.value=[],g.warning("暂无已完成状态的发货单"))}catch(e){console.error("加载已完成发货单失败:",e),g.error("加载已完成发货单失败"),F.value=[]}})():await(async t=>{try{const e=(await x(t)).data;I.customer_id=e.customer_id,I.statement_date=e.statement_date,I.due_date=e.due_date,I.notes=e.notes||"",I.delivery_note_ids=e.delivery_notes.map((e=>String(e.id))),await N()}catch(e){console.error("获取对账单详情失败:",e),g.error("获取对账单详情失败")}})(a.params.id)})),(e,t)=>{const a=l("el-option"),m=l("el-select"),_=l("el-form-item"),v=l("el-date-picker"),f=l("el-input"),w=l("el-table-column"),g=l("el-button"),V=l("el-tag"),k=l("el-table"),x=l("el-alert"),A=l("el-form"),j=l("el-card");return o(),p("div",B,[s(j,null,{header:d((()=>[i("div",E,[i("span",null,u(S.value?"新建对账单":"编辑对账单"),1)])])),default:d((()=>[s(A,{ref_key:"formRef",ref:Y,model:I,rules:M,"label-width":"120px"},{default:d((()=>[s(_,{label:"客户",prop:"customer_id"},{default:d((()=>[s(m,{modelValue:I.customer_id,"onUpdate:modelValue":t[0]||(t[0]=e=>I.customer_id=e),filterable:"",remote:"","remote-method":z,placeholder:"请输入客户名称",onChange:N},{default:d((()=>[(o(!0),p(y,null,h(P.value,(e=>(o(),r(a,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(_,{label:"对账日期",prop:"statement_date"},{default:d((()=>[s(v,{modelValue:I.statement_date,"onUpdate:modelValue":t[1]||(t[1]=e=>I.statement_date=e),type:"date",placeholder:"请选择对账日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),s(_,{label:"应付款日期",prop:"due_date"},{default:d((()=>[s(v,{modelValue:I.due_date,"onUpdate:modelValue":t[2]||(t[2]=e=>I.due_date=e),type:"date",placeholder:"请选择应付款日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),s(_,{label:"备注",prop:"notes"},{default:d((()=>[s(f,{modelValue:I.notes,"onUpdate:modelValue":t[3]||(t[3]=e=>I.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),s(_,{label:"发货单",prop:"delivery_note_ids"},{default:d((()=>[s(k,{ref:"deliveryTable",data:F.value,border:"",style:{width:"100%"},onSelectionChange:W},{default:d((()=>[s(w,{type:"selection",width:"55"}),s(w,{prop:"delivery_number",label:"发货单号","min-width":"120"}),s(w,{prop:"delivery_date",label:"发货日期",width:"100"}),s(w,{prop:"project_name",label:"项目名称","min-width":"120","show-overflow-tooltip":""}),s(w,{label:"产品信息","min-width":"150",align:"center"},{default:d((e=>[s(g,{type:"primary",link:"",onClick:t=>{return a=e.row,D.value=a,void(C.value=!0);var a}},{default:d((()=>t[5]||(t[5]=[n(" 查看明细 ")]))),_:2},1032,["onClick"])])),_:1}),s(w,{prop:"net_amount",label:"金额",width:"120"},{default:d((({row:e})=>[n(u(b(q)(e.net_amount)),1)])),_:1}),s(w,{prop:"status",label:"状态",width:"100"},{default:d((({row:e})=>{return[s(V,{type:(t=e.status,{pending:"info",delivered:"warning",completed:"success",cancelled:"danger","待发出":"info","已发出":"warning","已完成":"success","已作废":"danger"}[t]||"info")},{default:d((()=>[n(u($(e.status)),1)])),_:2},1032,["type"])];var t})),_:1})])),_:1},8,["data"]),0===F.value.length?(o(),p("div",J,[s(x,{type:"info",closable:!1,"show-icon":"",title:"暂无可用的发货单，请选择其他客户或确认客户是否有已完成状态的发货单"})])):c("",!0)])),_:1}),s(_,null,{default:d((()=>[s(g,{type:"primary",onClick:L},{default:d((()=>t[6]||(t[6]=[n("保存")]))),_:1}),s(g,{onClick:T},{default:d((()=>t[7]||(t[7]=[n("取消")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),s(R,{visible:C.value,"onUpdate:visible":t[4]||(t[4]=e=>C.value=e),data:D.value,title:"发货单产品明细"},null,8,["visible","data"])])}}}),[["__scopeId","data-v-cd186967"]]);export{L as default};
