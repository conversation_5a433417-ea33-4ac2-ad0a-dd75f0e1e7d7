#!/usr/bin/env python3
"""
应收账款表结构重构脚本
- 删除旧的应收账款表
- 创建新的应收账款表（按客户维度）
- 创建应收账款明细表（记录发货单/退货单贡献）
"""

import sqlite3
import os
from datetime import datetime

def update_receivables_structure():
    """更新应收账款表结构"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("开始更新应收账款表结构...")
        
        # 1. 删除旧的应收账款相关表
        print("1. 删除旧的应收账款相关表...")
        cursor.execute("DROP TABLE IF EXISTS receivable_details")
        cursor.execute("DROP TABLE IF EXISTS receivables")
        
        # 2. 创建新的应收账款表（按客户维度）
        print("2. 创建新的应收账款表...")
        cursor.execute("""
            CREATE TABLE receivables (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                UNIQUE(customer_id)
            )
        """)
        
        # 3. 创建应收账款明细表
        print("3. 创建应收账款明细表...")
        cursor.execute("""
            CREATE TABLE receivable_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                source_type VARCHAR(20) NOT NULL,
                source_id INTEGER NOT NULL,
                amount DECIMAL(12,2) NOT NULL,
                settlement_status VARCHAR(20) NOT NULL DEFAULT '未结清',
                settlement_date DATE NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                CHECK (source_type IN ('delivery_note', 'return_order')),
                CHECK (settlement_status IN ('未结清', '已结清')),
                UNIQUE(source_type, source_id)
            )
        """)
        
        # 4. 创建索引
        print("4. 创建索引...")
        cursor.execute("CREATE INDEX idx_receivables_customer_id ON receivables(customer_id)")
        cursor.execute("CREATE INDEX idx_receivable_details_customer_id ON receivable_details(customer_id)")
        cursor.execute("CREATE INDEX idx_receivable_details_source ON receivable_details(source_type, source_id)")
        cursor.execute("CREATE INDEX idx_receivable_details_settlement ON receivable_details(settlement_status)")
        
        # 5. 初始化现有客户的应收账款记录
        print("5. 初始化现有客户的应收账款记录...")
        cursor.execute("""
            INSERT INTO receivables (customer_id, amount)
            SELECT id, 0.00 FROM customers
        """)
        
        # 6. 根据现有已签收的发货单和退货单初始化应收账款明细
        print("6. 初始化应收账款明细...")
        
        # 添加已签收的发货单
        cursor.execute("""
            INSERT INTO receivable_details (customer_id, source_type, source_id, amount, settlement_status)
            SELECT 
                o.customer_id,
                'delivery_note',
                dn.id,
                dn.total_amount,
                dn.settlement_status
            FROM delivery_notes dn
            JOIN orders o ON dn.order_id = o.id
            WHERE dn.status = '已签收'
        """)
        
        # 添加已签收的退货单（负金额）
        cursor.execute("""
            INSERT INTO receivable_details (customer_id, source_type, source_id, amount, settlement_status)
            SELECT
                o.customer_id,
                'return_order',
                ro.id,
                -COALESCE((
                    SELECT SUM(
                        COALESCE(op.unit_price, 0) *
                        (1 - COALESCE(op.discount, 0) / 100.0) *
                        (1 + COALESCE(op.tax_rate, 0) / 100.0) *
                        COALESCE(roi.quantity, 0)
                    )
                    FROM return_order_items roi
                    JOIN order_products op ON roi.order_product_id = op.id
                    WHERE roi.return_order_id = ro.id
                ), 0),
                ro.settlement_status
            FROM return_orders ro
            JOIN orders o ON ro.order_id = o.id
            WHERE ro.status = '已完成'
        """)
        
        # 7. 更新客户应收账款总额
        print("7. 更新客户应收账款总额...")
        cursor.execute("""
            UPDATE receivables 
            SET amount = (
                SELECT COALESCE(SUM(
                    CASE WHEN rd.settlement_status = '未结清' THEN rd.amount ELSE 0 END
                ), 0)
                FROM receivable_details rd
                WHERE rd.customer_id = receivables.customer_id
            ),
            updated_at = CURRENT_TIMESTAMP
        """)
        
        conn.commit()
        print("✅ 应收账款表结构更新完成！")
        
        # 8. 验证数据
        print("\n📊 数据验证:")
        cursor.execute("SELECT COUNT(*) FROM receivables")
        receivables_count = cursor.fetchone()[0]
        print(f"应收账款记录数: {receivables_count}")
        
        cursor.execute("SELECT COUNT(*) FROM receivable_details")
        details_count = cursor.fetchone()[0]
        print(f"应收账款明细记录数: {details_count}")
        
        cursor.execute("""
            SELECT 
                COUNT(*) as total_customers,
                SUM(CASE WHEN amount > 0 THEN 1 ELSE 0 END) as customers_with_receivables,
                SUM(amount) as total_receivables
            FROM receivables
        """)
        stats = cursor.fetchone()
        print(f"客户总数: {stats[0]}")
        print(f"有应收账款的客户数: {stats[1]}")
        print(f"应收账款总额: ¥{stats[2]:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    success = update_receivables_structure()
    if success:
        print("\n🎉 应收账款表结构重构成功！")
    else:
        print("\n💥 应收账款表结构重构失败！")
