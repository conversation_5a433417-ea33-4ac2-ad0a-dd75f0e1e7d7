import type { RouteLocationNormalized } from 'vue-router'

// 权限检查
export function hasPermission(route: RouteLocationNormalized): boolean {
  const userRoles = getUserRoles()
  const requiredRoles = route.meta.roles as string[]

  // 如果没有设置角色要求，默认允许访问
  if (!requiredRoles || requiredRoles.length === 0) {
    return true
  }

  // 检查用户是否有所需角色
  return requiredRoles.some(role => userRoles.includes(role))
}

// 获取用户角色
export function getUserRoles(): string[] {
  // 从localStorage或其他地方获取用户角色
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    try {
      const user = JSON.parse(userInfo)
      return user.roles || ['user']
    } catch {
      return ['user']
    }
  }
  return ['user']
}

// 检查是否已登录
export function isAuthenticated(): boolean {
  const token = localStorage.getItem('token')
  return !!token
}

// 获取重定向路径
export function getRedirectPath(route: RouteLocationNormalized): string {
  const { query, fullPath } = route
  const redirect = query.redirect as string
  return redirect || fullPath
}

// 清除认证信息
export function clearAuth(): void {
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  localStorage.removeItem('refreshToken')
}

// 设置认证信息
export function setAuth(token: string, userInfo?: any, refreshToken?: string): void {
  localStorage.setItem('token', token)
  if (userInfo) {
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
  }
  if (refreshToken) {
    localStorage.setItem('refreshToken', refreshToken)
  }
}

// 权限指令检查
export function checkDirectivePermission(permissions: string | string[]): boolean {
  const userRoles = getUserRoles()
  const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions]

  return requiredPermissions.some(permission => userRoles.includes(permission))
}
