"""
文件上传管理API
提供文件上传、下载、管理等功能
基于原项目API实现，确保与现有系统100%兼容
"""
from flask import request, current_app, send_from_directory, send_from_directory, Response
from flask_restx import Namespace, Resource, fields
from werkzeug.utils import secure_filename
import os
import uuid
import mimetypes

from app.utils.response import (
    success_response, 
    error_response, 
    not_found_response
)

# 创建命名空间
api = Namespace('uploads', description='文件上传管理API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'docx', 'xlsx', 'txt', 'csv'}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_upload_folder():
    """获取上传目录"""
    upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
    if not os.path.isabs(upload_folder):
        upload_folder = os.path.join(current_app.root_path, upload_folder)
    return upload_folder

def ensure_upload_folder():
    """确保上传目录存在"""
    upload_folder = get_upload_folder()
    os.makedirs(upload_folder, exist_ok=True)
    return upload_folder

# 定义API模型用于Swagger文档
upload_response_model = api.model('UploadResponse', {
    'filename': fields.String(description='文件名'),
    'url': fields.String(description='文件URL'),
    'size': fields.Integer(description='文件大小'),
    'type': fields.String(description='文件类型')
})


@api.route('/upload')
class FileUpload(Resource):
    @api.doc('upload_file')
    def options(self):
        """处理上传接口的预检请求"""
        response = Response()
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        response.headers.add('Access-Control-Allow-Methods', 'POST, OPTIONS')
        response.headers.add('Access-Control-Max-Age', '3600')
        return response

    @api.doc('upload_file')
    def post(self):
        """上传文件接口"""
        try:
            current_app.logger.info(f"收到上传请求: 头部={dict(request.headers)}")
            
            # 检查是否有文件上传
            if 'file' not in request.files:
                return make_response(error_response, '未找到文件', code=400)
            
            file = request.files['file']
            
            # 如果用户未选择文件，浏览器也会提交一个空的文件部分
            if file.filename == '':
                return make_response(error_response, '未选择文件', code=400)
            
            if file and allowed_file(file.filename):
                # 生成安全的文件名
                filename = secure_filename(file.filename)
                # 使用UUID创建唯一文件名
                unique_filename = f"{uuid.uuid4().hex}_{filename}"
                
                # 确保上传目录存在
                upload_folder = ensure_upload_folder()
                current_app.logger.info(f"正在使用上传目录: {upload_folder}")
                
                # 保存文件
                file_path = os.path.join(upload_folder, unique_filename)
                file.save(file_path)
                
                # 获取文件信息
                file_size = os.path.getsize(file_path)
                file_type = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
                
                # 构建文件URL路径
                file_url = f"/api/v1/uploads/files/{unique_filename}"
                
                response_data = {
                    'filename': unique_filename,
                    'original_filename': filename,
                    'url': file_url,
                    'size': file_size,
                    'type': file_type
                }
                
                return make_response(success_response, response_data, '文件上传成功', 201)
            else:
                allowed_types = ', '.join(ALLOWED_EXTENSIONS)
                return make_response(error_response, f'不允许的文件类型，支持的格式: {allowed_types}', code=400)
                
        except Exception as e:
            current_app.logger.error(f"文件上传失败: {str(e)}")
            return make_response(error_response, f'文件上传失败: {str(e)}', code=500)


@api.route('/files/<filename>')
class FileService(Resource):
    @api.doc('download_file')
    def get(self, filename):
        """获取上传的文件"""
        try:
            # 安全检查，防止路径遍历攻击
            safe_filename = secure_filename(filename)
            upload_folder = get_upload_folder()
            file_path = os.path.join(upload_folder, safe_filename)

            if not os.path.exists(file_path):
                current_app.logger.error(f"请求的文件未找到: {filename}")
                return make_response(not_found_response, '文件未找到')

            # 获取MIME类型
            mimetype = mimetypes.guess_type(file_path)[0]
            if not mimetype:
                mimetype = 'application/octet-stream'

            return send_from_directory(upload_folder, safe_filename, mimetype=mimetype)

        except Exception as e:
            current_app.logger.error(f"获取文件 '{filename}' 失败: {str(e)}")
            return make_response(error_response, '获取文件失败', code=500)

    @api.doc('delete_file')
    def delete(self, filename):
        """删除上传的文件"""
        try:
            # 安全检查，防止路径遍历攻击
            safe_filename = secure_filename(filename)
            upload_folder = get_upload_folder()
            file_path = os.path.join(upload_folder, safe_filename)

            if not os.path.exists(file_path):
                return make_response(not_found_response, '文件未找到')

            # 删除文件
            os.remove(file_path)

            return make_response(success_response, message='文件删除成功')

        except Exception as e:
            current_app.logger.error(f"删除文件 '{filename}' 失败: {str(e)}")
            return make_response(error_response, f"删除文件失败: {str(e)}")


@api.route('/vouchers/<filename>')
class VoucherFileDownload(Resource):
    @api.doc('download_voucher_file')
    def get(self, filename):
        """获取收款凭据文件"""
        try:
            # 安全检查，防止路径遍历攻击
            safe_filename = secure_filename(filename)
            voucher_folder = os.path.join(current_app.root_path, '..', 'uploads', 'vouchers')
            file_path = os.path.join(voucher_folder, safe_filename)

            if not os.path.exists(file_path):
                current_app.logger.error(f"请求的凭据文件未找到: {filename}")
                return make_response(not_found_response, '凭据文件未找到')

            # 获取MIME类型
            mimetype = mimetypes.guess_type(file_path)[0]
            if not mimetype:
                mimetype = 'application/octet-stream'

            return send_from_directory(voucher_folder, safe_filename, mimetype=mimetype)

        except Exception as e:
            current_app.logger.error(f"获取凭据文件 '{filename}' 失败: {str(e)}")
            return make_response(error_response, '获取凭据文件失败', code=500)


@api.route('/files')
class FileList(Resource):
    @api.doc('list_files')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('type', '文件类型过滤')
    def get(self):
        """获取上传文件列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            file_type = request.args.get('type', '')
            
            upload_folder = get_upload_folder()
            
            if not os.path.exists(upload_folder):
                return make_response(success_response, [], "文件列表为空")
            
            # 获取所有文件
            all_files = []
            for filename in os.listdir(upload_folder):
                file_path = os.path.join(upload_folder, filename)
                if os.path.isfile(file_path):
                    file_info = {
                        'filename': filename,
                        'url': f"/api/v1/uploads/files/{filename}",
                        'size': os.path.getsize(file_path),
                        'type': mimetypes.guess_type(file_path)[0] or 'application/octet-stream',
                        'created_at': os.path.getctime(file_path),
                        'modified_at': os.path.getmtime(file_path)
                    }
                    
                    # 文件类型过滤
                    if file_type and not file_info['type'].startswith(file_type):
                        continue
                    
                    all_files.append(file_info)
            
            # 按修改时间倒序排序
            all_files.sort(key=lambda x: x['modified_at'], reverse=True)
            
            # 分页
            start = (page - 1) * per_page
            end = start + per_page
            files = all_files[start:end]
            
            pagination = {
                'page': page,
                'per_page': per_page,
                'total': len(all_files),
                'pages': (len(all_files) + per_page - 1) // per_page,
                'has_prev': page > 1,
                'has_next': end < len(all_files)
            }
            
            result = {
                'items': files,
                'pagination': pagination
            }
            
            return make_response(success_response, result, "获取文件列表成功")
            
        except Exception as e:
            current_app.logger.error(f"获取文件列表失败: {str(e)}")
            return make_response(error_response, f"获取文件列表失败: {str(e)}")





@api.route('/config')
class UploadConfig(Resource):
    @api.doc('get_upload_config')
    def get(self):
        """获取上传配置信息"""
        try:
            config = {
                'allowed_extensions': list(ALLOWED_EXTENSIONS),
                'max_file_size': current_app.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024),  # 默认16MB
                'upload_folder': get_upload_folder()
            }
            
            return make_response(success_response, config, "获取上传配置成功")
            
        except Exception as e:
            current_app.logger.error(f"获取上传配置失败: {str(e)}")
            return make_response(error_response, f"获取上传配置失败: {str(e)}")
