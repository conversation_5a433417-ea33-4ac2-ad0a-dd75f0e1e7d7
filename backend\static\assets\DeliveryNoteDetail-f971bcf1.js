import{u as e,b as l,r as a,d as t,e as r,f as o,M as u,o as s,c as n,i,h as d,N as c,g as v,j as p,U as _,a as m,k as f,t as y,F as g,m as b,W as h}from"./index-3d4c440c.js";import{b as w,a as k}from"./delivery-8900e898.js";import{_ as x}from"./_plugin-vue_export-helper-1b428a4d.js";const C={class:"delivery-note-detail"},D={class:"flex-between"},F={key:0,class:"total-amount"},$={class:"amount"},S={class:"dialog-footer"},j=x({__name:"DeliveryNoteDetail",setup(x){const j=e(),I=l(),V=a(!1),A=t((()=>j.params.id)),N=a({id:null,delivery_number:"",order_id:"",order_number:"",customer_name:"",delivery_date:"",receiver:"",contact_phone:"",delivery_address:"",notes:"",status:"",items:[],created_at:"",updated_at:"",sign_person:"",sign_time:"",sign_notes:"",sign_image:"",logistics_company:"",tracking_number:"",total_amount:0}),P=t((()=>{const e=[];return"cancelled"===N.value.status&&e.push({timestamp:"2023-08-14 16:30:00",content:"订单已取消",type:"danger",color:"#F56C6C"}),"completed"!==N.value.status&&"delivered"!==N.value.status&&"delivering"!==N.value.status||e.push({timestamp:"2023-08-14 13:00:00",content:"开始送货，送货员：张师傅，联系电话：13888888888",type:"primary",color:"#409EFF"}),"completed"!==N.value.status&&"delivered"!==N.value.status||e.push({timestamp:"2023-08-14 15:30:00",content:"货物已送达收货地址",type:"success",color:"#67C23A"}),"completed"===N.value.status&&e.push({timestamp:"2023-08-14 16:00:00",content:"客户已签收",type:"success",color:"#67C23A"}),e})),U=a(!1),B=a(""),M=a(!1),R=a({status:"",notes:"",file:null}),T=()=>{I.go(-1)},q=()=>{I.push(`/delivery/${A.value}/edit`)},z=()=>{_.success(`打印送货单：${N.value.delivery_number}`)},E=e=>({pending:"待发出",delivered:"已发出",completed:"已完成",cancelled:"已作废","待发出":"待发出","已发出":"已发出","已完成":"已完成","已作废":"已作废","":"未知状态"}[e]||"未知状态"),L=()=>{h.confirm('确认发货后，如果在5天内未进行"确认完成"操作，系统将自动将状态更新为"已完成"。是否继续？',"确认发货",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((()=>{W("已发出")})).catch((()=>{}))},W=e=>{B.value={"已发出":"确认发货","已完成":"确认完成","已作废":"作废送货单"}[e]||`更改为${e}状态`,R.value={status:e,notes:"",file:null},U.value=!0},Y=e=>{R.value.file=e.raw},G=async()=>{M.value=!0;try{const e=await k(A.value.toString(),R.value.status);200===e.code&&e.data?(N.value=e.data,_.success(`送货单状态已更新为：${E(R.value.status)}`),U.value=!1):_.error("更新送货单状态失败")}catch(e){console.error("更新送货单状态失败",e),_.error("更新送货单状态失败")}finally{M.value=!1}},H=e=>{if(!e)return"未设置";try{const l=new Date(e);return l.getFullYear()+"/"+String(l.getMonth()+1).padStart(2,"0")+"/"+String(l.getDate()).padStart(2,"0")}catch(l){return console.error("日期格式化错误:",l),e}};return r((()=>{(async()=>{V.value=!0;try{const e=A.value.toString();console.log(`正在获取送货单 ID: ${e} 的详情`);const l=await w(e);l?(200===l.code||0===l.code?l.data?(N.value=l.data,console.log("成功获取送货单详情:",N.value)):(_.warning("送货单详情数据为空"),console.warn("送货单API返回空数据")):l.data?(N.value=l.data,console.log("从非标准响应获取送货单详情:",N.value)):"object"!=typeof l||"code"in l?(_.error(`获取送货单详情失败: ${l.message||"未知错误"}`),console.error("送货单API返回错误:",l)):(N.value=l,console.log("从响应对象获取送货单详情:",N.value)),N.value.id||console.warn("获取的送货单数据有问题，没有ID:",N.value)):(_.error("获取送货单详情失败: 服务器无响应"),console.error("送货单API无响应"))}catch(e){console.error("获取送货单详情失败",e),_.error(`获取送货单详情失败: ${e.message||"未知错误"}`)}finally{V.value=!1}})()})),(e,l)=>{const a=o("el-button"),t=o("el-card"),r=o("el-descriptions-item"),_=o("el-link"),h=o("el-tag"),w=o("el-descriptions"),k=o("el-table-column"),x=o("el-table"),j=o("el-timeline-item"),A=o("el-timeline"),J=o("el-form-item"),K=o("el-image"),O=o("el-form"),Q=o("el-input"),X=o("el-upload"),Z=o("el-dialog"),ee=u("loading");return s(),n("div",C,[i(t,{class:"header-card mb-20"},{default:d((()=>[m("div",D,[l[12]||(l[12]=m("h2",{class:"page-title"},"送货单详情",-1)),m("div",null,[i(a,{onClick:T},{default:d((()=>l[6]||(l[6]=[f("返回")]))),_:1}),"待发出"===N.value.status?(s(),v(a,{key:0,type:"primary",onClick:q},{default:d((()=>l[7]||(l[7]=[f("编辑")]))),_:1})):p("",!0),i(a,{type:"warning",onClick:z},{default:d((()=>l[8]||(l[8]=[f("打印")]))),_:1}),"待发出"===N.value.status?(s(),v(a,{key:1,type:"success",onClick:L},{default:d((()=>l[9]||(l[9]=[f("确认发货")]))),_:1})):p("",!0),"已发出"===N.value.status?(s(),v(a,{key:2,type:"success",onClick:l[0]||(l[0]=e=>W("已完成"))},{default:d((()=>l[10]||(l[10]=[f("确认完成")]))),_:1})):p("",!0),"待发出"!==N.value.status&&"已发出"!==N.value.status||"已完成"===N.value.status?p("",!0):(s(),v(a,{key:3,type:"danger",onClick:l[1]||(l[1]=e=>W("已作废"))},{default:d((()=>l[11]||(l[11]=[f("作废送货单")]))),_:1}))])])])),_:1}),c((s(),n("div",null,[i(t,{class:"mb-20"},{header:d((()=>l[13]||(l[13]=[m("h3",{class:"card-title"},"基本信息",-1)]))),default:d((()=>[i(w,{column:3,border:""},{default:d((()=>[i(r,{label:"送货单号"},{default:d((()=>[f(y(N.value.delivery_number),1)])),_:1}),i(r,{label:"关联订单"},{default:d((()=>[i(_,{type:"primary",onClick:l[2]||(l[2]=e=>{return l=N.value.order_id,void I.push(`/orders/${l}`);var l})},{default:d((()=>{var e;return[f(y(null==(e=N.value.order)?void 0:e.order_number),1)]})),_:1})])),_:1}),i(r,{label:"送货状态"},{default:d((()=>{return[i(h,{type:(e=N.value.status,{pending:"info",delivered:"warning",completed:"success",cancelled:"danger","待发出":"info","已发出":"warning","已完成":"success","已作废":"danger","":"info"}[e]||"info")},{default:d((()=>[f(y(E(N.value.status)),1)])),_:1},8,["type"])];var e})),_:1}),i(r,{label:"送货日期"},{default:d((()=>[f(y(H(N.value.delivery_date)),1)])),_:1}),i(r,{label:"客户名称"},{default:d((()=>{var e,l;return[f(y((null==(l=null==(e=N.value.order)?void 0:e.customer)?void 0:l.name)||"未设置"),1)]})),_:1}),i(r,{label:"创建时间"},{default:d((()=>[f(y(H(N.value.created_at)),1)])),_:1}),i(r,{label:"收货人"},{default:d((()=>{var e;return[f(y(N.value.recipient_name||(null==(e=N.value.order)?void 0:e.contact_person)||"未设置"),1)]})),_:1}),i(r,{label:"联系电话"},{default:d((()=>{var e;return[f(y(N.value.recipient_phone||(null==(e=N.value.order)?void 0:e.contact_phone)||"未设置"),1)]})),_:1}),i(r,{label:"送货地址"},{default:d((()=>{var e,l;return[f(y(N.value.delivery_address_snapshot||(null==(e=N.value.order)?void 0:e.delivery_address)||(null==(l=N.value.order)?void 0:l.project_address)||"未设置"),1)]})),_:1}),i(r,{label:"备注",span:3},{default:d((()=>[f(y(N.value.notes||"暂无备注"),1)])),_:1})])),_:1})])),_:1}),i(t,{class:"mb-20"},{header:d((()=>l[14]||(l[14]=[m("h3",{class:"card-title"},"物流信息",-1)]))),default:d((()=>[i(w,{column:1,border:""},{default:d((()=>{var e,l;return[i(r,{label:"配送方式"},{default:d((()=>[f(y(N.value.logistics_company?"物流配送":"自主配送"),1)])),_:1}),N.value.logistics_company?(s(),v(r,{key:0,label:"物流公司"},{default:d((()=>[f(y(N.value.logistics_company),1)])),_:1})):p("",!0),N.value.tracking_number?(s(),v(r,{key:1,label:"物流单号"},{default:d((()=>[f(y(N.value.tracking_number),1)])),_:1})):p("",!0),!N.value.logistics_company&&(null==(e=N.value.order)?void 0:e.delivery_contact)?(s(),v(r,{key:2,label:"自主配送联系人"},{default:d((()=>{var e;return[f(y((null==(e=N.value.order)?void 0:e.delivery_contact)||"未设置"),1)]})),_:1})):p("",!0),!N.value.logistics_company&&(null==(l=N.value.order)?void 0:l.delivery_phone)?(s(),v(r,{key:3,label:"自主配送电话"},{default:d((()=>{var e;return[f(y((null==(e=N.value.order)?void 0:e.delivery_phone)||"未设置"),1)]})),_:1})):p("",!0)]})),_:1})])),_:1}),i(t,{class:"mb-20"},{header:d((()=>l[15]||(l[15]=[m("h3",{class:"card-title"},"商品信息",-1)]))),default:d((()=>{return[i(x,{data:N.value.items||[],border:"",style:{width:"100%"}},{default:d((()=>[i(k,{label:"序号",type:"index",width:"50",align:"center"}),i(k,{prop:"product_name",label:"产品名称","min-width":"180","show-overflow-tooltip":""}),i(k,{prop:"product_model",label:"型号",width:"100"}),i(k,{prop:"specification_description",label:"规格","min-width":"120","show-overflow-tooltip":""},{default:d((({row:e})=>[f(y(e.specification_description||"未设置"),1)])),_:1}),i(k,{prop:"quantity",label:"发货数量",width:"120",align:"center"}),i(k,{prop:"product_unit",label:"单位",width:"80",align:"center"},{default:d((({row:e})=>[f(y(e.product_unit||"未设置"),1)])),_:1}),i(k,{prop:"notes",label:"备注","min-width":"150","show-overflow-tooltip":""})])),_:1},8,["data"]),N.value.total_amount?(s(),n("div",F,[l[16]||(l[16]=m("span",null,"合计金额：",-1)),m("span",$,y((e=N.value.total_amount,Number(e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}))),1)])):p("",!0)];var e})),_:1}),"pending"!==N.value.status?(s(),v(t,{key:0,class:"mb-20"},{header:d((()=>l[17]||(l[17]=[m("div",{class:"card-header"},[m("h3",null,"物流跟踪")],-1)]))),default:d((()=>[i(A,null,{default:d((()=>[(s(!0),n(g,null,b(P.value,((e,l)=>(s(),v(j,{key:l,timestamp:e.timestamp,type:e.type,color:e.color},{default:d((()=>[f(y(e.content),1)])),_:2},1032,["timestamp","type","color"])))),128))])),_:1})])),_:1})):p("",!0),"completed"===N.value.status?(s(),v(t,{key:1},{header:d((()=>l[18]||(l[18]=[m("div",{class:"card-header"},[m("h3",null,"签收信息")],-1)]))),default:d((()=>[i(O,{"label-width":"100px"},{default:d((()=>[i(J,{label:"签收人"},{default:d((()=>{var e;return[f(y(N.value.sign_person||N.value.recipient_name||(null==(e=N.value.order)?void 0:e.contact_person)||"未设置"),1)]})),_:1}),i(J,{label:"签收时间"},{default:d((()=>[f(y(N.value.sign_time||"暂无记录"),1)])),_:1}),i(J,{label:"签收备注"},{default:d((()=>[f(y(N.value.sign_notes||"暂无备注"),1)])),_:1}),N.value.sign_image?(s(),v(J,{key:0,label:"签收凭证"},{default:d((()=>[i(K,{src:N.value.sign_image,style:{width:"200px",height:"150px"},fit:"contain","preview-src-list":[N.value.sign_image]},null,8,["src","preview-src-list"])])),_:1})):p("",!0)])),_:1})])),_:1})):p("",!0)])),[[ee,V.value]]),i(Z,{modelValue:U.value,"onUpdate:modelValue":l[5]||(l[5]=e=>U.value=e),title:B.value,width:"500px"},{footer:d((()=>[m("span",S,[i(a,{onClick:l[4]||(l[4]=e=>U.value=!1)},{default:d((()=>l[21]||(l[21]=[f("取消")]))),_:1}),i(a,{type:"primary",onClick:G,loading:M.value},{default:d((()=>l[22]||(l[22]=[f(" 确定 ")]))),_:1},8,["loading"])])])),default:d((()=>[i(O,{ref:"statusFormRef",model:R.value,"label-width":"100px"},{default:d((()=>[i(J,{label:"备注",prop:"notes"},{default:d((()=>[i(Q,{modelValue:R.value.notes,"onUpdate:modelValue":l[3]||(l[3]=e=>R.value.notes=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),"completed"===R.value.status?(s(),v(J,{key:0,label:"上传凭证"},{default:d((()=>[i(X,{class:"upload-demo",action:"#","auto-upload":!1,"on-change":Y,limit:1},{trigger:d((()=>[i(a,{type:"primary"},{default:d((()=>l[19]||(l[19]=[f("选择图片")]))),_:1})])),tip:d((()=>l[20]||(l[20]=[m("div",{class:"el-upload__tip"},"请上传签收凭证图片",-1)]))),_:1})])),_:1})):p("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-85196304"]]);export{j as default};
