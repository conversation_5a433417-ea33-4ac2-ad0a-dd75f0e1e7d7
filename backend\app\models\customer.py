"""
客户相关模型
包括客户基本信息、银行账户、送货地址
"""
from app.models.base import BaseModel
from app import db
from typing import List, Optional


class Customer(BaseModel):
    """客户表模型"""
    __tablename__ = 'customers'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False, unique=True, comment='公司名称')
    contact = db.Column(db.String(50), nullable=False, comment='联系人')
    phone = db.Column(db.String(20), nullable=True, comment='联系电话')
    email = db.Column(db.String(120), nullable=True, comment='邮箱地址')
    address = db.Column(db.String(255), nullable=True, comment='公司地址')
    tax_id = db.Column(db.String(50), nullable=True, unique=True, comment='税号')
    source = db.Column(db.String(50), nullable=True, comment='客户来源')
    level = db.Column(db.String(50), default='normal', comment='客户等级')
    status = db.Column(db.String(20), default='正常', comment='状态 (正常, 禁用)')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 关联关系
    bank_accounts = db.relationship('CustomerBankAccount', back_populates='customer', cascade='all, delete-orphan')
    delivery_addresses = db.relationship('CustomerDeliveryAddress', back_populates='customer', cascade='all, delete-orphan')
    orders = db.relationship('Order', back_populates='customer')
    quotations = db.relationship('Quotation', back_populates='customer')
    quotation_requests = db.relationship('QuotationRequest', back_populates='customer')

    def __repr__(self) -> str:
        return f"<Customer {self.name}>"
    
    def get_default_bank_account(self) -> Optional['CustomerBankAccount']:
        """获取默认银行账户"""
        for account in self.bank_accounts:
            if account.is_default:
                return account
        return None if not self.bank_accounts else self.bank_accounts[0]
    
    def get_default_delivery_address(self) -> Optional['CustomerDeliveryAddress']:
        """获取默认送货地址"""
        for address in self.delivery_addresses:
            if address.is_default:
                return address
        return None if not self.delivery_addresses else self.delivery_addresses[0]


class CustomerBankAccount(BaseModel):
    """客户银行账户表模型"""
    __tablename__ = 'customer_bank_accounts'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False, comment='关联客户ID')
    bank_name = db.Column(db.String(100), nullable=False, comment='开户行名称')
    account_name = db.Column(db.String(100), nullable=False, comment='账户名称')
    account_number = db.Column(db.String(50), nullable=False, comment='账号')
    is_default = db.Column(db.Boolean, nullable=False, default=False, comment='是否默认账户')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 关联关系
    customer = db.relationship('Customer', back_populates='bank_accounts')

    def __repr__(self) -> str:
        return f"<CustomerBankAccount {self.account_name} - {self.account_number}>"


class CustomerDeliveryAddress(BaseModel):
    """客户送货地址表模型"""
    __tablename__ = 'customer_delivery_addresses'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False, comment='关联客户ID')
    province = db.Column(db.String(50), nullable=False, comment='省份')
    city = db.Column(db.String(50), nullable=False, comment='城市')
    district = db.Column(db.String(50), nullable=False, comment='区县')
    detailed_address = db.Column(db.String(200), nullable=False, comment='详细地址')
    contact_person = db.Column(db.String(50), nullable=False, comment='送货联系人')
    contact_phone = db.Column(db.String(20), nullable=False, comment='联系电话')
    is_default = db.Column(db.Boolean, nullable=False, default=False, comment='是否默认地址')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    # 关联关系
    customer = db.relationship('Customer', back_populates='delivery_addresses')

    def __repr__(self) -> str:
        return f"<CustomerDeliveryAddress {self.province}{self.city}{self.district}{self.detailed_address}>"
    
    def get_full_address(self) -> str:
        """获取完整地址"""
        return f"{self.province}{self.city}{self.district}{self.detailed_address}"
