<template>
  <el-dialog
    v-model="dialogVisible"
    :title="type === 'add' ? '添加产品规格' : '编辑产品规格'"
    width="600px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="规格描述" prop="specification">
        <el-input v-model="form.specification" placeholder="请输入规格描述" />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="成本价" prop="cost_price">
            <el-input-number 
              v-model="form.cost_price" 
              :precision="2" 
              :min="0" 
              placeholder="请输入成本价"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建议售价" prop="suggested_price">
            <el-input-number 
              v-model="form.suggested_price" 
              :precision="2" 
              :min="0" 
              placeholder="请输入建议售价"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最低售价" prop="min_price">
            <el-input-number 
              v-model="form.min_price" 
              :precision="2" 
              :min="0" 
              placeholder="请输入最低售价"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最高售价" prop="max_price">
            <el-input-number 
              v-model="form.max_price" 
              :precision="2" 
              :min="0" 
              placeholder="请输入最高售价"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="税率(%)" prop="tax_rate">
            <el-input-number 
              v-model="form.tax_rate" 
              :precision="1" 
              :min="0" 
              :max="100"
              placeholder="请输入税率"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否默认规格">
            <el-switch v-model="form.is_default" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注" prop="notes">
        <el-input 
          v-model="form.notes" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息（可选）" 
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ type === 'add' ? '添加' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { productApi } from '@/api/product'

// Props
interface Props {
  visible: boolean
  type: 'add' | 'edit'
  data?: any
  productId: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  type: 'add',
  data: () => ({}),
  productId: 0
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'submit': [data?: any]
}>()

// 表单引用
const formRef = ref()

// 状态
const submitting = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = reactive({
  specification: '',
  cost_price: null,
  suggested_price: null,
  min_price: null,
  max_price: null,
  tax_rate: 13.0,
  is_default: false,
  notes: ''
})

// 表单验证规则
const rules = {
  specification: [
    { required: true, message: '请输入规格描述', trigger: 'blur' },
    { max: 100, message: '规格描述长度不能超过 100 个字符', trigger: 'blur' }
  ],
  cost_price: [
    { required: true, message: '请输入成本价', trigger: 'blur' }
  ],
  suggested_price: [
    { required: true, message: '请输入建议售价', trigger: 'blur' }
  ],
  tax_rate: [
    { required: true, message: '请输入税率', trigger: 'blur' }
  ],
  notes: [
    { max: 500, message: '备注长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 监听数据变化，填充表单
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, {
      specification: newData.specification || '',
      cost_price: newData.cost_price || null,
      suggested_price: newData.suggested_price || null,
      min_price: newData.min_price || null,
      max_price: newData.max_price || null,
      tax_rate: newData.tax_rate || 13.0,
      is_default: newData.is_default || false,
      notes: newData.notes || ''
    })
  }
}, { immediate: true, deep: true })

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    specification: '',
    cost_price: null,
    suggested_price: null,
    min_price: null,
    max_price: null,
    tax_rate: 13.0,
    is_default: false,
    notes: ''
  })
  formRef.value?.clearValidate()
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 处理关闭
const handleClosed = () => {
  resetForm()
}

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData = { ...form }

    // 确保必填字段有值
    if (!submitData.cost_price) {
      ElMessage.error('请输入成本价')
      return
    }
    if (!submitData.suggested_price) {
      ElMessage.error('请输入建议售价')
      return
    }

    // 处理空值
    if (!submitData.notes || submitData.notes.trim() === '') {
      submitData.notes = null
    }

    // 处理价格字段的空值
    if (!submitData.min_price || submitData.min_price === '') {
      submitData.min_price = null
    }
    if (!submitData.max_price || submitData.max_price === '') {
      submitData.max_price = null
    }
    
    if (props.productId === 0) {
      // 新增产品时，不调用API，直接传递数据给父组件
      dialogVisible.value = false
      emit('submit', submitData)
    } else {
      // 编辑已存在产品时，调用API
      if (props.type === 'add') {
        // 添加规格
        await productApi.addSpecification(props.productId, submitData)
        ElMessage.success('添加规格成功')
      } else {
        // 更新规格
        await productApi.updateSpecification(props.productId, props.data.id, submitData)
        ElMessage.success('更新规格成功')
      }

      // 关闭对话框并触发刷新
      dialogVisible.value = false
      emit('submit')
    }
  } catch (error: any) {
    console.error('提交失败:', error)

    // 显示详细错误信息
    let errorMessage = props.type === 'add' ? '添加规格失败' : '更新规格失败'
    if (error?.response?.data?.message) {
      errorMessage += ': ' + error.response.data.message
    } else if (error?.message) {
      errorMessage += ': ' + error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
