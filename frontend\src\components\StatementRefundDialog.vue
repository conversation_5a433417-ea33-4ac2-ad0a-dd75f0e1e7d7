<template>
  <el-dialog
    v-model="visible"
    title="对账单退款"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 对账单信息 -->
    <div class="statement-info">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="对账单号">
          {{ statementInfo?.statement_number }}
        </el-descriptions-item>
        <el-descriptions-item label="客户名称">
          {{ statementInfo?.customer?.name }}
        </el-descriptions-item>
        <el-descriptions-item label="实际金额">
          <span class="amount-text">{{ formatAmount(statementInfo?.adjusted_total_amount || 0) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已收金额">
          <span class="amount-text paid">{{ formatAmount(statementInfo?.paid_amount || 0) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="可退金额" :span="2">
          <span class="amount-text refund">{{ formatAmount(refundableAmount) }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 退款表单 -->
    <el-form
      ref="formRef"
      :model="refundForm"
      :rules="formRules"
      label-width="120px"
      class="refund-form"
    >
      <el-form-item label="退款日期" prop="refund_date">
        <el-date-picker
          v-model="refundForm.refund_date"
          type="date"
          placeholder="选择退款日期"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="退款金额" prop="amount">
        <el-input
          v-model="refundForm.amount"
          placeholder="请输入退款金额"
          type="number"
          step="0.01"
          min="0.01"
          :max="refundableAmount"
        >
          <template #append>元</template>
        </el-input>
      </el-form-item>

      <el-form-item label="退款方式" prop="refund_target">
        <el-radio-group v-model="refundForm.refund_target">
          <el-radio value="direct">直接退款</el-radio>
          <el-radio value="balance">退回余额</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item 
        v-if="refundForm.refund_target === 'direct'" 
        label="退款方式" 
        prop="refund_method"
      >
        <el-select v-model="refundForm.refund_method" placeholder="请选择退款方式" style="width: 100%">
          <el-option label="银行转账" value="bank_transfer" />
          <el-option label="现金" value="cash" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <el-form-item 
        v-if="refundForm.refund_target === 'direct'" 
        label="交易流水号" 
        prop="reference_number"
      >
        <el-input
          v-model="refundForm.reference_number"
          placeholder="请输入交易流水号（可选）"
        />
      </el-form-item>

      <el-form-item 
        v-if="refundForm.refund_target === 'direct'" 
        label="退款账户" 
        prop="bank_account"
      >
        <el-input
          v-model="refundForm.bank_account"
          placeholder="请输入退款账户信息"
          type="textarea"
          :rows="2"
        />
      </el-form-item>

      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="refundForm.notes"
          placeholder="请输入备注信息（可选）"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确认退款
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { statementRefundApi } from '@/api/statementRefund'

interface Props {
  modelValue: boolean
  statementId?: number
  statementInfo?: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  statementId: undefined,
  statementInfo: null
})

const emit = defineEmits<Emits>()

const visible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 退款表单
const refundForm = reactive({
  refund_date: new Date(),
  amount: '',
  refund_target: 'direct',
  refund_method: 'bank_transfer',
  reference_number: '',
  bank_account: '',
  notes: ''
})

// 计算可退款金额
const refundableAmount = computed(() => {
  if (!props.statementInfo) return 0
  const total = parseFloat(props.statementInfo.adjusted_total_amount || '0')
  const paid = parseFloat(props.statementInfo.paid_amount || '0')

  // 修复负金额对账单的可退金额计算逻辑
  if (total >= 0) {
    // 正金额对账单：可退金额 = 已收金额 - 总金额（如果已收金额大于总金额）
    return Math.max(0, paid - total)
  } else {
    // 负金额对账单：可退金额 = 总金额绝对值 - 已收金额绝对值（如果还需要退更多）
    const totalAbs = Math.abs(total)
    const paidAbs = Math.abs(paid)
    return Math.max(0, totalAbs - paidAbs)
  }
})

// 表单验证规则
const formRules: FormRules = {
  refund_date: [
    { required: true, message: '请选择退款日期', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        const amount = parseFloat(value)
        if (isNaN(amount) || amount <= 0) {
          callback(new Error('退款金额必须大于0'))
        } else if (amount > refundableAmount.value) {
          callback(new Error(`退款金额不能超过可退金额 ¥${refundableAmount.value.toFixed(2)}`))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  refund_target: [
    { required: true, message: '请选择退款方式', trigger: 'change' }
  ],
  refund_method: [
    { required: true, message: '请选择退款方式', trigger: 'change' }
  ]
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetForm()
  }
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置表单
const resetForm = () => {
  refundForm.refund_date = new Date()
  refundForm.amount = ''
  refundForm.refund_target = 'direct'
  refundForm.refund_method = 'bank_transfer'
  refundForm.reference_number = ''
  refundForm.bank_account = ''
  refundForm.notes = ''
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 格式化金额
const formatAmount = (amount: number | string) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return `¥${num.toFixed(2)}`
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 提交退款
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (!props.statementId) {
      ElMessage.error('缺少对账单ID')
      return
    }

    // 确认退款
    const confirmMessage = refundForm.refund_target === 'balance' 
      ? `确定要退款 ¥${refundForm.amount} 到客户余额吗？`
      : `确定要退款 ¥${refundForm.amount} 吗？`

    await ElMessageBox.confirm(confirmMessage, '确认退款', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    submitting.value = true

    // 准备提交数据
    const submitData = {
      statement_id: props.statementId,
      refund_date: refundForm.refund_date,
      amount: parseFloat(refundForm.amount),
      refund_target: refundForm.refund_target,
      notes: refundForm.notes
    }

    // 如果是直接退款，添加相关字段
    if (refundForm.refund_target === 'direct') {
      Object.assign(submitData, {
        refund_method: refundForm.refund_method,
        reference_number: refundForm.reference_number,
        bank_account: refundForm.bank_account
      })
    } else {
      // 余额退款固定使用 balance 方式
      Object.assign(submitData, {
        refund_method: 'balance'
      })
    }

    // 调用API
    const response = await statementRefundApi.create(submitData)

    ElMessage.success('退款记录创建成功')
    emit('success', response.data)
    handleClose()

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('创建退款记录失败:', error)
      ElMessage.error(error.message || '创建退款记录失败')
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.statement-info {
  margin-bottom: 20px;
}

.refund-form {
  margin-top: 20px;
}

.amount-text {
  font-weight: 600;
  font-size: 14px;
}

.amount-text.paid {
  color: #67c23a;
}

.amount-text.refund {
  color: #e6a23c;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
