"""
测试API端点
用于验证统一响应格式和错误处理
"""
from flask import request
from flask_restx import Namespace, Resource, fields

from app.utils.response import (
    success_response, 
    error_response, 
    paginated_response,
    validation_error_response,
    not_found_response
)
from app.utils.exceptions import (
    APIException,
    ValidationError,
    NotFoundError,
    BusinessLogicError
)

# 创建命名空间
api = Namespace('test', description='测试API端点')

# 定义响应模型
success_model = api.model('SuccessResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='响应消息'),
    'data': fields.Raw(description='响应数据'),
    'success': fields.Boolean(description='是否成功')
})

error_model = api.model('ErrorResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='错误消息'),
    'errors': fields.Raw(description='详细错误信息'),
    'success': fields.Boolean(description='是否成功')
})

pagination_model = api.model('PaginationResponse', {
    'code': fields.Integer(description='状态码'),
    'message': fields.String(description='响应消息'),
    'data': fields.List(fields.Raw, description='数据列表'),
    'success': fields.Boolean(description='是否成功'),
    'pagination': fields.Raw(description='分页信息')
})


@api.route('/success')
class TestSuccess(Resource):
    @api.doc('test_success_response')
    def get(self):
        """测试成功响应格式"""
        test_data = {
            'id': 1,
            'name': '测试数据',
            'description': '这是一个测试数据'
        }
        response_data, status_code = success_response(
            data=test_data,
            message='测试成功响应'
        )
        return response_data, status_code


@api.route('/error')
class TestError(Resource):
    @api.doc('test_error_response')
    @api.marshal_with(error_model)
    def get(self):
        """测试错误响应格式"""
        error_type = request.args.get('type', 'general')
        
        if error_type == 'validation':
            # 测试验证错误
            raise ValidationError(
                message='数据验证失败',
                errors={
                    'name': ['名称不能为空'],
                    'email': ['邮箱格式不正确']
                }
            )
        elif error_type == 'not_found':
            # 测试404错误
            raise NotFoundError(message='测试资源不存在')
        elif error_type == 'business':
            # 测试业务逻辑错误
            raise BusinessLogicError(
                message='业务逻辑错误',
                errors='库存不足，无法完成订单'
            )
        else:
            # 测试一般错误
            raise APIException(
                message='测试错误响应',
                code=400,
                errors='这是一个测试错误'
            )


@api.route('/pagination')
class TestPagination(Resource):
    @api.doc('test_pagination_response')
    @api.marshal_with(pagination_model)
    def get(self):
        """测试分页响应格式"""
        # 模拟分页数据
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        
        # 生成测试数据
        total = 100
        start = (page - 1) * per_page
        end = min(start + per_page, total)
        
        test_data = [
            {
                'id': i,
                'name': f'测试项目 {i}',
                'description': f'这是第 {i} 个测试项目'
            }
            for i in range(start + 1, end + 1)
        ]
        
        return paginated_response(
            data=test_data,
            total=total,
            page=page,
            per_page=per_page,
            message='分页查询成功'
        )


@api.route('/exception')
class TestException(Resource):
    @api.doc('test_unhandled_exception')
    def get(self):
        """测试未处理异常"""
        exception_type = request.args.get('type', 'runtime')
        
        if exception_type == 'zero_division':
            # 测试除零错误
            result = 1 / 0
        elif exception_type == 'key_error':
            # 测试键错误
            data = {}
            value = data['non_existent_key']
        elif exception_type == 'type_error':
            # 测试类型错误
            result = 'string' + 123
        else:
            # 测试运行时错误
            raise RuntimeError('这是一个测试运行时错误')
        
        return success_response(message='不应该到达这里')


@api.route('/health')
class HealthCheck(Resource):
    @api.doc('health_check')
    def get(self):
        """健康检查端点"""
        response_data, status_code = success_response(
            data={
                'status': 'healthy',
                'service': 'EMB System API',
                'version': '1.0.0'
            },
            message='服务运行正常'
        )
        return response_data, status_code
