<template>
  <div class="customer-detail" v-loading="loading">
    <!-- 页面头部 -->
    <el-card class="mb-20">
      <div class="flex-between">
        <h2 class="form-title">客户详情</h2>
        <div>
          <el-button @click="$router.back()">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button type="primary" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑客户
          </el-button>
          <el-button type="danger" @click="handleDelete">
            <el-icon><Delete /></el-icon>
            删除客户
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 应收账款信息 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>应收账款信息</span>
        </div>
      </template>

      <div v-if="receivableLoading" class="text-center py-8">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span class="ml-2">加载中...</span>
      </div>

      <div v-else-if="!receivableInfo" class="text-center text-gray-500 py-8">
        暂无应收账款信息
      </div>

      <div v-else>
        <!-- 应收账款汇总 -->
        <el-row :gutter="20" class="mb-20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ formatAmount(receivableInfo.total_amount) }}</div>
              <div class="stat-label">当前应收总额</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value unsettled">{{ formatAmount(receivableInfo.unsettled_amount) }}</div>
              <div class="stat-label">未结清金额</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value settled">{{ formatAmount(receivableInfo.settled_amount) }}</div>
              <div class="stat-label">已结清金额</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ receivableInfo.details?.length || 0 }}</div>
              <div class="stat-label">明细记录数</div>
            </div>
          </el-col>
        </el-row>

        <!-- 应收账款明细 -->
        <div v-if="receivableInfo.details && receivableInfo.details.length > 0">
          <h4 class="mb-10">应收账款明细</h4>
          <el-table :data="receivableInfo.details" border stripe>
            <el-table-column prop="source_type" label="来源类型" width="100">
              <template #default="{ row }">
                <el-tag type="success" size="small">
                  {{ row.source_type === 'statement' ? '对账单' : row.source_type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="单据号" width="180">
              <template #default="{ row }">
                <span v-if="row.source_info">
                  {{ row.source_info.statement_number || row.source_info.delivery_number || row.source_info.return_number }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="单据日期" width="120">
              <template #default="{ row }">
                <span v-if="row.source_info">
                  {{ formatDate(row.source_info.statement_date || row.source_info.delivery_date || row.source_info.return_date) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="金额" width="120" align="right">
              <template #default="{ row }">
                <span :class="{ 'text-red': row.amount < 0, 'text-green': row.amount > 0 }">
                  {{ formatAmount(row.amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="settlement_status" label="结清状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.settlement_status === '已结清' ? 'success' : 'warning'" size="small">
                  {{ row.settlement_status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="结清日期" width="120">
              <template #default="{ row }">
                {{ row.settlement_date ? formatDate(row.settlement_date) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="创建时间" width="120">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 客户基本信息 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>客户基本信息</span>
        </div>
      </template>

      <el-descriptions :column="2" border v-if="customer.id">
        <el-descriptions-item label="客户编号">{{ customer.id }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ customer.name }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ customer.contact }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ customer.phone }}</el-descriptions-item>
        <el-descriptions-item label="电子邮箱">{{ customer.email || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="客户地址" :span="2">{{ customer.address || '暂无' }}</el-descriptions-item>
        <el-descriptions-item label="统一社会信用代码">{{ customer.tax_id || '未提供' }}</el-descriptions-item>
        <el-descriptions-item label="客户来源">{{ getSourceLabel(customer.source) }}</el-descriptions-item>
        <el-descriptions-item label="客户等级">
          <el-tag :type="getLevelType(customer.level)">{{ getLevelLabel(customer.level) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="客户状态">
          <el-tag :type="customer.status === '正常' ? 'success' : 'danger'">
            {{ customer.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(customer.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(customer.updated_at) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ customer.notes || '暂无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 银行账户信息 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>银行账户信息</span>
        </div>
      </template>

      <div v-if="!bankAccounts || bankAccounts.length === 0" class="text-center text-gray-500 py-8">
        暂无银行账户信息
      </div>

      <el-table v-else :data="bankAccounts" border stripe>
        <el-table-column prop="bank_name" label="开户行" min-width="120" />
        <el-table-column prop="account_name" label="账户名称" min-width="120" />
        <el-table-column prop="account_number" label="账号" min-width="150" />
        <el-table-column prop="is_default" label="默认账户" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.is_default" type="success" size="small">默认</el-tag>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.notes" style="color: #606266;">{{ row.notes }}</span>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 送货地址信息 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>送货地址信息</span>
        </div>
      </template>

      <div v-if="!deliveryAddresses || deliveryAddresses.length === 0" class="text-center text-gray-500 py-8">
        暂无送货地址信息
      </div>

      <el-table v-else :data="deliveryAddresses" border stripe>
        <el-table-column prop="contact_person" label="联系人" width="120" />
        <el-table-column prop="contact_phone" label="联系电话" width="130" />
        <el-table-column label="详细地址" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.full_address || `${row.province || ''}${row.city || ''}${row.district || ''}${row.detailed_address || ''}` }}
          </template>
        </el-table-column>
        <el-table-column prop="is_default" label="默认地址" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.is_default" type="success" size="small">默认</el-tag>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.notes" style="color: #606266;">{{ row.notes }}</span>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  Delete,
  Loading
} from '@element-plus/icons-vue'
import { customerApi } from '@/api/customer'

const route = useRoute()
const router = useRouter()

// 数据状态
const loading = ref(false)
const customer = ref<any>({})
const bankAccounts = ref([])
const deliveryAddresses = ref([])
const receivableInfo = ref<any>(null)
const receivableLoading = ref(false)

// 获取客户详情
const getCustomerDetail = async () => {
  try {
    loading.value = true
    const customerId = Number(route.params.id)
    const response = await customerApi.getById(customerId) as any
    customer.value = response.data || response
  } catch (error) {
    console.error('获取客户详情失败:', error)
    ElMessage.error('获取客户详情失败')
  } finally {
    loading.value = false
  }
}

// 获取银行账户列表
const getBankAccounts = async () => {
  try {
    const customerId = Number(route.params.id)
    const response = await customerApi.getBankAccounts(customerId) as any
    bankAccounts.value = response.data || response || []
  } catch (error) {
    console.error('获取银行账户失败:', error)
  }
}

// 获取送货地址列表
const getDeliveryAddresses = async () => {
  try {
    const customerId = Number(route.params.id)
    const response = await customerApi.getDeliveryAddresses(customerId) as any
    deliveryAddresses.value = response.data || response || []
  } catch (error) {
    console.error('获取送货地址失败:', error)
  }
}

// 获取应收账款信息
const getReceivableInfo = async () => {
  try {
    receivableLoading.value = true
    const customerId = Number(route.params.id)
    const response = await customerApi.getReceivables(customerId) as any
    receivableInfo.value = response.data || response
  } catch (error) {
    console.error('获取应收账款信息失败:', error)
    ElMessage.error('获取应收账款信息失败')
  } finally {
    receivableLoading.value = false
  }
}

// 操作处理
const handleEdit = () => {
  router.push(`/customers/edit/${customer.value.id}`)
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除客户"${customer.value.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await customerApi.delete(customer.value.id)
    ElMessage.success('删除成功')
    router.push('/customers')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除客户失败:', error)
      ElMessage.error('删除客户失败')
    }
  }
}



// 工具函数
const getLevelType = (level: string) => {
  const typeMap: Record<string, string> = {
    'vip': 'danger',
    'important': 'warning',
    'normal': 'info'
  }
  return typeMap[level] || 'info'
}

const getLevelLabel = (level: string) => {
  const labelMap: Record<string, string> = {
    'vip': 'VIP客户',
    'important': '重要客户',
    'normal': '普通客户'
  }
  return labelMap[level] || level
}

const getSourceLabel = (source: string) => {
  const labelMap: Record<string, string> = {
    'internet': '网络渠道',
    'referral': '客户推荐',
    'exhibition': '展会',
    'visit': '销售拜访',
    'old_customer': '老客户',
    'other': '其他'
  }
  return labelMap[source] || source || '暂无'
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 格式化金额
const formatAmount = (amount: number | string) => {
  const num = Number(amount || 0)
  return `¥${num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 初始化
onMounted(() => {
  getCustomerDetail()
  getBankAccounts()
  getDeliveryAddresses()
  getReceivableInfo()
})
</script>

<style lang="scss" scoped>
.customer-detail {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .mb-10 {
    margin-bottom: 10px;
  }

  .ml-2 {
    margin-left: 8px;
  }

  .py-8 {
    padding: 32px 0;
  }

  .text-center {
    text-align: center;
  }

  .text-gray-500 {
    color: #9ca3af;
  }

  .text-red {
    color: #f56565;
  }

  .text-green {
    color: #48bb78;
  }

  .stat-card {
    text-align: center;
    padding: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #f8fafc;

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #2d3748;
      margin-bottom: 8px;

      &.unsettled {
        color: #e53e3e;
      }

      &.settled {
        color: #38a169;
      }
    }

    .stat-label {
      font-size: 14px;
      color: #718096;
    }
  }
}
</style>
