<template>
  <div class="delivery-list">
    <!-- 搜索表单 -->
    <el-card class="search-form-card mb-20">
      <el-form ref="searchFormRef" :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="发货单号" prop="delivery_number">
          <el-input v-model="searchForm.delivery_number" placeholder="请输入发货单号" clearable />
        </el-form-item>
        <el-form-item label="客户名称" prop="customer_id">
          <el-select
            v-model="searchForm.customer_id"
            filterable
            remote
            reserve-keyword
            placeholder="请输入客户名称"
            :remote-method="handleSearchCustomers"
            :loading="customerSearchLoading"
            clearable
          >
            <el-option
              v-for="item in customerOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联订单号" prop="order_number">
          <el-input v-model="searchForm.order_number" placeholder="请输入订单号" clearable />
        </el-form-item>
        <el-form-item label="发货状态" prop="status">
          <el-select v-model="searchForm.status" placeholder="请选择发货状态" clearable style="width: 120px;">
            <el-option label="待发出" value="待发出" />
            <el-option label="已发出" value="已发出" />
            <el-option label="运输中" value="运输中" />
            <el-option label="已签收" value="已签收" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已作废" value="已作废" />
          </el-select>
        </el-form-item>
        <el-form-item label="发货日期" prop="date_range">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearchForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="operator-card mb-20">
      <div class="operator-content">
        <div class="left-actions">
          <el-button type="success" @click="handleExport" :disabled="selectedDeliveries.length === 0">
            <el-icon><Download /></el-icon>
            批量导出
          </el-button>
          <el-button type="warning" @click="handleBatchPrint" :disabled="selectedDeliveries.length === 0">
            <el-icon><Printer /></el-icon>
            批量打印
          </el-button>
          <el-button type="danger" @click="handleBatchDelete" :disabled="selectedDeliveries.length === 0">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>

        <div class="right-actions">
          <!-- 卡片详细程度切换 -->
          <el-button-group>
            <el-button
              :type="cardMode === 'detailed' ? 'primary' : ''"
              @click="cardMode = 'detailed'"
              title="详细模式"
            >
              详细
            </el-button>
            <el-button
              :type="cardMode === 'simple' ? 'primary' : ''"
              @click="cardMode = 'simple'"
              title="简化模式"
            >
              简化
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 发货单列表 -->
    <el-card>
      <!-- 卡片视图 -->
      <div v-loading="loading">
        <!-- 批量选择工具栏 -->
        <div v-if="selectedDeliveries.length > 0" class="batch-selection-bar">
          <span>已选择 {{ selectedDeliveries.length }} 个发货单</span>
          <el-button size="small" @click="clearSelection">清空选择</el-button>
        </div>

        <!-- 卡片列表 -->
        <div class="delivery-cards-list">
          <div
            v-for="delivery in deliveryList"
            :key="delivery.id"
            :class="['delivery-card', { 'selected': isDeliverySelected(delivery) }]"
            @click="handleCardClick(delivery)"
          >
            <!-- 选择框 -->
            <div class="card-checkbox" @click.stop>
              <el-checkbox
                :model-value="isDeliverySelected(delivery)"
                @change="handleCardSelection(delivery, $event)"
              />
            </div>

            <!-- 详细模式卡片内容 -->
            <div v-if="cardMode === 'detailed'" class="card-content detailed">
              <!-- 第一行：发货单号、客户、订单号、状态 -->
              <div class="card-row-1">
                <div class="left-info">
                  <div class="delivery-number">{{ delivery.delivery_number || 'N/A' }}</div>
                  <div class="customer-status">
                    <span class="customer">{{ delivery.customer_name || delivery.customer?.name || '未知客户' }}</span>
                    <div class="status-tags">
                      <el-tag :type="getStatusType(delivery.status)" size="default" class="status-tag-prominent delivery-status">
                        {{ getStatusText(delivery.status) }}
                      </el-tag>
                      <el-tag :type="getSettlementStatusType(delivery.settlement_status)" size="default" class="status-tag-prominent settlement-status">
                        {{ delivery.settlement_status || '未结清' }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第二行：关联订单、金额和时间信息 -->
              <div class="card-row-2">
                <div class="left-details">
                  <div class="orders-info">
                    <div class="order-group">
                      <span class="order-label">关联订单</span>
                      <div v-if="delivery.order_number" class="order-links">
                        <el-tag
                          type="primary"
                          size="small"
                          class="order-tag"
                          @click.stop="handleViewOrder(delivery)"
                        >
                          {{ delivery.order_number }}
                        </el-tag>
                      </div>
                      <span v-else class="no-orders">暂无关联</span>
                    </div>
                  </div>
                  <div class="amount-info">
                    <div class="amount-group">
                      <span class="amount-label">发货金额</span>
                      <span class="amount-value total">¥{{ (delivery.total_amount || 0).toLocaleString() }}</span>
                    </div>
                  </div>
                  <div class="dates-info">
                    <div class="date-group">
                      <span class="date-label">发货日期</span>
                      <span class="date-value">{{ delivery.delivery_date ? formatDate(delivery.delivery_date) : 'N/A' }}</span>
                    </div>
                    <div class="date-group">
                      <span class="date-label">创建时间</span>
                      <span class="date-value">{{ delivery.created_at ? formatDate(delivery.created_at) : 'N/A' }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第三行：操作按钮 -->
              <div class="card-actions detailed" @click.stop>
                <!-- 编辑操作 -->
                <el-button v-if="delivery.status === '待发出'" type="warning" size="small" @click="handleEdit(delivery)" title="编辑发货单">
                  编辑
                </el-button>

                <!-- 状态变更操作 - 待发出状态 -->
                <el-button v-if="delivery.status === '待发出'" type="success" size="small" @click="handleStatusChange(delivery, '已发出')" title="标记为已发出">
                  确认发出
                </el-button>

                <!-- 状态变更操作 - 已发出状态 -->
                <el-button v-if="delivery.status === '已发出'" type="primary" size="small" @click="handleStatusChange(delivery, '运输中')" title="标记为运输中">
                  运输中
                </el-button>
                <el-button v-if="delivery.status === '已发出'" type="success" size="small" @click="handleStatusChange(delivery, '已签收')" title="标记为已签收">
                  签收
                </el-button>

                <!-- 状态变更操作 - 运输中状态 -->
                <el-button v-if="delivery.status === '运输中'" type="success" size="small" @click="handleStatusChange(delivery, '已签收')" title="标记为已签收">
                  签收
                </el-button>

                <!-- 作废操作 - 适用于多个状态 -->
                <el-button v-if="['待发出', '已发出', '运输中'].includes(delivery.status)" type="danger" size="small" @click="handleStatusChange(delivery, '已作废')" title="作废发货单">
                  作废
                </el-button>

                <!-- 打印操作 -->
                <el-button type="info" size="small" @click="handlePrint(delivery)" title="打印发货单">
                  打印
                </el-button>
              </div>
            </div>

            <!-- 简化模式卡片内容 -->
            <div v-else class="card-content simple">
              <div class="simple-row">
                <div class="simple-left">
                  <div class="delivery-number">{{ delivery.delivery_number || 'N/A' }}</div>
                  <div class="customer-order">
                    <span class="customer">{{ delivery.customer_name || delivery.customer?.name || '未知客户' }}</span>
                    <span class="separator">|</span>
                    <span class="order-number">{{ delivery.order_number || 'N/A' }}</span>
                  </div>
                </div>
                <div class="simple-right">
                  <div class="amount-info">
                    <span class="total-amount">¥{{ delivery.total_amount || '0.00' }}</span>
                    <span class="delivery-date">{{ delivery.delivery_date ? formatDate(delivery.delivery_date) : 'N/A' }}</span>
                  </div>
                  <!-- 状态 -->
                  <div class="status-tags">
                    <el-tag :type="getStatusType(delivery.status)" size="default" class="status-tag-prominent delivery-status">
                      {{ getStatusText(delivery.status) }}
                    </el-tag>
                    <el-tag :type="getSettlementStatusType(delivery.settlement_status)" size="small" class="status-tag-prominent settlement-status">
                      {{ delivery.settlement_status || '未结清' }}
                    </el-tag>
                  </div>
                  <div class="simple-actions" @click.stop>
                    <el-dropdown trigger="click">
                      <el-button type="text" size="small">
                        操作 <el-icon><ArrowDown /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item v-if="delivery.status === '待发出'" @click="handleEdit(delivery)">
                            编辑
                          </el-dropdown-item>
                          <el-dropdown-item v-if="delivery.status === '待发出'" @click="handleStatusChange(delivery, '已发出')">
                            确认发出
                          </el-dropdown-item>
                          <el-dropdown-item v-if="delivery.status === '已发出'" @click="handleStatusChange(delivery, '运输中')">
                            运输中
                          </el-dropdown-item>
                          <el-dropdown-item v-if="['已发出', '运输中'].includes(delivery.status)" @click="handleStatusChange(delivery, '已签收')">
                            签收
                          </el-dropdown-item>
                          <el-dropdown-item v-if="['待发出', '已发出', '运输中'].includes(delivery.status)" @click="handleStatusChange(delivery, '已作废')" divided>
                            作废
                          </el-dropdown-item>
                          <el-dropdown-item @click="handlePrint(delivery)">
                            打印
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <el-pagination
        class="pagination-container"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Download, Printer, Delete, ArrowDown } from '@element-plus/icons-vue'
import { deliveryNoteApi } from '@/api/delivery'
import { searchCustomers } from '@/api/customer'

const router = useRouter()

// 搜索表单引用
const searchFormRef = ref<FormInstance>()

// 定义状态常量
const DELIVERY_STATUS = {
  PENDING: 'pending',
  DELIVERED: 'delivered',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
}

// 搜索表单
const searchForm = reactive({
  customer_id: '',
  delivery_number: '',
  order_number: '',
  status: '',
  date_range: [] as string[]
})

// 客户搜索
const customerSearchLoading = ref(false)
const customerOptions = ref<any[]>([])

const handleSearchCustomers = async (query: string) => {
  if (query) {
    customerSearchLoading.value = true
    try {
      const response = await searchCustomers({ name: query, per_page: 20 })
      // 处理API响应
      if (response && Array.isArray(response)) {
        customerOptions.value = response
      } else if (response && response.data && Array.isArray(response.data)) {
        customerOptions.value = response.data
      } else {
        customerOptions.value = []
      }
    } catch (error) {
      console.error('搜索客户失败:', error)
      customerOptions.value = []
    } finally {
      customerSearchLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

// 列表数据
const loading = ref(false)
const deliveryList = ref<any[]>([])
const selectedDeliveries = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 卡片模式
const cardMode = ref('detailed') // 'detailed' | 'simple'

// 获取列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      customer_id: searchForm.customer_id,
      delivery_number: searchForm.delivery_number,
      order_number: searchForm.order_number,
      status: searchForm.status,
      start_date: searchForm.date_range?.[0],
      end_date: searchForm.date_range?.[1]
    }

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await deliveryNoteApi.getList(params) as any
    console.log('获取到的发货单数据:', response)

    // 兼容处理API响应格式
    if (response) {
      let listData: any[] = []
      let totalItems = 0

      if (Array.isArray(response)) {
        listData = response
        totalItems = response.length
      } else if (response && typeof response === 'object') {
        // 优先检查 items 字段（新的API格式）
        const data = response.items || response.data
        if (Array.isArray(data)) {
          listData = data
        } else if (data && typeof data === 'object' && Object.keys(data).length === 0) {
          listData = []
        } else {
          listData = []
        }
        totalItems = response.pagination?.total || response.total || 0
      } else {
        listData = []
        totalItems = 0
      }

      // 确保每个发货单对象有完整的客户名称和订单号
      deliveryList.value = listData.map((note: any) => {
        return {
          ...note,
          customer_name: note.customer_name || (note.order?.customer?.name) || '',
          order_number: note.order_number || (note.order?.order_number) || ''
        }
      })
      total.value = totalItems
    } else {
      deliveryList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取发货单列表失败:', error)
    ElMessage.error('获取发货单列表失败')
    deliveryList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 卡片选择相关函数
const isDeliverySelected = (delivery: any) => {
  return selectedDeliveries.value.some((selected: any) => selected.id === delivery.id)
}

const handleCardClick = (delivery: any) => {
  // 跳转到发货单详情页面
  router.push(`/delivery-notes/view/${delivery.id}`)
}

const handleCardSelection = (delivery: any, checked: boolean) => {
  if (checked) {
    if (!isDeliverySelected(delivery)) {
      selectedDeliveries.value.push(delivery)
    }
  } else {
    selectedDeliveries.value = selectedDeliveries.value.filter((selected: any) => selected.id !== delivery.id)
  }
}

const clearSelection = () => {
  selectedDeliveries.value = []
}

// 查看关联订单
const handleViewOrder = (delivery: any) => {
  if (delivery.order_id) {
    router.push(`/orders/view/${delivery.order_id}`)
  } else {
    ElMessage.warning('未找到关联订单信息')
  }
}

// 搜索和重置
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const resetSearchForm = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields()
  }
  Object.assign(searchForm, {
    customer_id: '',
    delivery_number: '',
    order_number: '',
    status: '',
    date_range: []
  })
  currentPage.value = 1
  fetchData()
}

// 兼容旧的重置函数
const handleReset = () => {
  resetSearchForm()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchData()
}

// 状态处理
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待发出': 'warning',      // 待发出 - 橙色，等待处理
    '已发出': 'primary',      // 已发出 - 蓝色，进行中
    '运输中': 'primary',      // 运输中 - 蓝色，进行中
    '已签收': 'success',      // 已签收 - 绿色，成功状态
    '已完成': 'success',      // 已完成 - 绿色，最终完成
    '已作废': 'info'          // 已作废 - 灰色，取消状态
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  // 后端直接返回中文状态，无需转换
  return status || '未知状态'
}

// 结清状态样式
const getSettlementStatusType = (settlementStatus: string) => {
  const statusMap: Record<string, string> = {
    '未结清': 'warning',
    '已结清': 'success'
  }
  return statusMap[settlementStatus] || 'warning'
}

// 操作处理
const handleEdit = (row: any) => {
  router.push(`/delivery-notes/edit/${row.id}`)
}

const handlePrint = async (row: any) => {
  try {
    const blob = await deliveryNoteApi.print(row.id)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `发货单_${row.delivery_number}.pdf`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('打印文件已生成')
  } catch (error) {
    console.error('打印发货单失败', error)
    ElMessage.error('打印发货单失败')
  }
}

const handleBatchPrint = async () => {
  if (selectedDeliveries.value.length === 0) {
    ElMessage.warning('请选择要打印的发货单')
    return
  }

  try {
    const ids = selectedDeliveries.value.map((delivery: any) => delivery.id)
    const blob = await deliveryNoteApi.batchPrint(ids)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `发货单批量打印_${new Date().toISOString().split('T')[0]}.pdf`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('批量打印文件已生成')
  } catch (error) {
    console.error('批量打印发货单失败', error)
    ElMessage.error('批量打印发货单失败')
  }
}

const handleExport = async () => {
  try {
    // 获取当前列表中所有发货单的ID
    const ids = selectedDeliveries.value.length > 0
      ? selectedDeliveries.value.map(item => item.id)
      : deliveryList.value.map(item => item.id)

    if (ids.length === 0) {
      ElMessage.warning('没有可导出的发货单')
      return
    }

    loading.value = true

    try {
      const response = await deliveryNoteApi.batchExport(ids, 'xlsx')

      if (response) {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `发货单列表_${formatDate(new Date())}.xlsx`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        ElMessage.success('导出成功')
      } else {
        ElMessage.error('导出失败：服务器返回空数据')
      }
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    } finally {
      loading.value = false
    }
  } catch (error) {
    console.error('导出处理错误:', error)
    ElMessage.error('导出处理出错')
  }
}

const handleBatchDelete = async () => {
  if (selectedDeliveries.value.length === 0) {
    ElMessage.warning('请先选择要删除的发货单')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedDeliveries.value.length} 个发货单吗？`, '批量删除', {
      type: 'warning'
    })

    // 这里应该调用批量删除API
    ElMessage.success('批量删除成功')
    clearSelection()
    fetchData()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 格式化日期
const formatDate = (dateString: string | Date) => {
  if (!dateString) return '未设置'
  try {
    const date = new Date(dateString)
    return date.getFullYear() + '/' +
           String(date.getMonth() + 1).padStart(2, '0') + '/' +
           String(date.getDate()).padStart(2, '0')
  } catch (error) {
    console.error('日期格式化错误:', error)
    return String(dateString)
  }
}

// 状态流转规则
const statusTransitions = {
  '待发出': ['已发出', '已作废'],
  '已发出': ['运输中', '已签收', '已作废'],
  '运输中': ['已签收', '已作废'],
  '已签收': [],
  '已作废': []
}

// 处理状态变更
const handleStatusChange = async (row: any, newStatus: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要将发货单"${row.delivery_number}"的状态更改为"${newStatus}"吗？`,
      '确认状态变更',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用API更新状态
    await deliveryNoteApi.updateStatus(row.id, newStatus)

    // 更新本地数据
    row.status = newStatus

    ElMessage.success(`发货单状态已更新为"${newStatus}"`)
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('更新发货单状态失败:', error)
      ElMessage.error(error?.message || '更新发货单状态失败')
    }
  }
}

// 初始化加载
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.delivery-list {
  padding: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

// 搜索表单样式
.search-form-card {
  .el-form {
    margin-bottom: 0;
  }
}

// 操作按钮区域样式
.operator-card {
  .operator-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      gap: 12px;
    }

    .right-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
}

// 批量选择工具栏
.batch-selection-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #606266;
}

// 卡片列表样式
.delivery-cards-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;

  // 发货单卡片样式
  .delivery-card {
    position: relative;
    width: 100%;
    border: 2px solid #e8eaed;
    border-radius: 12px;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 4px;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
      transform: translateY(-1px);
    }

    &.selected {
      border-color: #409eff;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2), 0 4px 16px rgba(64, 158, 255, 0.1);
      transform: translateY(-1px);
    }

    .card-checkbox {
      position: absolute;
      top: 16px;
      left: 16px;
      z-index: 2;
    }

    .card-content {
      padding: 12px 16px;
      padding-left: 44px;

      &.detailed {
        padding: 14px 16px;
        padding-left: 44px;
      }

      &.simple {
        padding: 12px 16px;
        padding-left: 44px;
      }

      // 详细模式样式
      .card-row-1 {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;

        .left-info {
          flex: 1;
          min-width: 0;

          .delivery-number {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .customer-status {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;

            .customer {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              flex-shrink: 0;
            }

            .status-tags {
              display: flex;
              gap: 8px;
              flex-shrink: 0;

              .status-tag-prominent {
                font-weight: 600;
                border-radius: 8px;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                border: 2px solid transparent;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                }

                &.delivery-status,
                &.settlement-status {
                  font-size: 13px;
                  padding: 5px 12px;
                  font-weight: 600;
                  letter-spacing: 0.3px;

                  &.el-tag--success {
                    background: linear-gradient(135deg, #67c23a, #85ce61);
                    border-color: #67c23a;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(103, 194, 58, 0.25);
                  }

                  &.el-tag--warning {
                    background: linear-gradient(135deg, #e6a23c, #ebb563);
                    border-color: #e6a23c;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25);
                  }

                  &.el-tag--primary {
                    background: linear-gradient(135deg, #409eff, #66b1ff);
                    border-color: #409eff;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
                  }

                  &.el-tag--info {
                    background: linear-gradient(135deg, #909399, #a6a9ad);
                    border-color: #909399;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(144, 147, 153, 0.25);
                  }
                }
              }
            }
          }
        }
      }

      .card-row-2 {
        margin-bottom: 12px;

        .left-details {
          display: flex;
          gap: 24px;
          align-items: flex-start;

          .orders-info {
            flex: 1;

            .order-group {
              display: flex;
              flex-direction: column;
              gap: 2px;

              .order-label {
                font-size: 12px;
                color: #909399;
                font-weight: 500;
              }

              .order-links {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
              }

              .order-tag {
                cursor: pointer;
                transition: all 0.2s;

                &:hover {
                  transform: scale(1.05);
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }
              }

              .no-orders {
                color: #999;
                font-size: 12px;
              }
            }
          }

          .amount-info {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .amount-group {
              display: flex;
              flex-direction: column;
              gap: 2px;

              .amount-label {
                font-size: 12px;
                color: #909399;
                font-weight: 500;
              }

              .amount-value {
                font-size: 14px;
                font-weight: 600;

                &.total {
                  color: #67c23a;
                  font-size: 16px;
                }
              }
            }
          }

          .dates-info {
            display: flex;
            gap: 16px;

            .date-group {
              display: flex;
              flex-direction: column;
              gap: 2px;

              .date-label {
                font-size: 12px;
                color: #909399;
                font-weight: 500;
              }

              .date-value {
                font-size: 13px;
                color: #606266;
              }
            }
          }
        }
      }

      .card-actions {
        display: flex;
        gap: 8px;
        padding-top: 8px;
        border-top: 1px solid #f0f0f0;

        &.detailed {
          justify-content: flex-start;
        }
      }

      // 简化模式样式
      .simple-row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .simple-left {
          flex: 1;
          min-width: 0;

          .delivery-number {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .customer-order {
            font-size: 12px;
            color: #909399;

            .customer {
              color: #606266;
            }

            .separator {
              margin: 0 8px;
              color: #dcdfe6;
            }

            .order-number {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }

        .simple-right {
          display: flex;
          align-items: center;
          gap: 12px;

          .amount-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 2px;

            .total-amount {
              font-size: 14px;
              font-weight: 600;
              color: #303133;
            }

            .delivery-date {
              font-size: 12px;
              color: #909399;
            }
          }

          .status-tags {
            display: flex;
            flex-direction: column;
            gap: 4px;
            align-items: flex-end;
          }

          .simple-actions {
            .el-dropdown {
              .el-button {
                padding: 0;
                border: none;
                background: none;
                color: #409eff;

                &:hover {
                  color: #66b1ff;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 分页样式
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
