<template>
  <div class="order-list">
    <!-- 搜索表单 -->
    <el-card class="search-form-card mb-20">
      <el-form ref="searchFormRef" :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="订单号" prop="order_number">
          <el-input v-model="searchForm.order_number" placeholder="请输入订单号" clearable />
        </el-form-item>
        <el-form-item label="客户名称" prop="customer_id">
          <el-select
            v-model="searchForm.customer_id"
            filterable
            remote
            reserve-keyword
            placeholder="请输入客户名称"
            :remote-method="handleSearchCustomers"
            :loading="customerSearchLoading"
            clearable
          >
            <el-option
              v-for="item in customerOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称" prop="project_name">
          <el-input v-model="searchForm.project_name" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="发货状态" prop="order_status">
          <el-select v-model="searchForm.order_status" placeholder="请选择发货状态" clearable style="width: 120px;">
            <el-option label="待确认" value="待确认" />
            <el-option label="已确认" value="已确认" />
            <el-option label="生产中" value="生产中" />
            <el-option label="待发货" value="待发货" />
            <el-option label="部分发货" value="部分发货" />
            <el-option label="全部发货" value="全部发货" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </el-form-item>
        <el-form-item label="收款状态" prop="payment_status">
          <el-select v-model="searchForm.payment_status" placeholder="请选择收款状态" clearable style="width: 120px;">
            <el-option label="未收款" value="未收款" />
            <el-option label="部分收款" value="部分收款" />
            <el-option label="已收款" value="已收款" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单日期" prop="date_range">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearchForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="operator-card mb-20">
      <div class="operator-content">
        <div class="left-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增订单
          </el-button>
          <el-button type="success" @click="handleBatchExport" :disabled="selectedOrders.length === 0">
            <el-icon><Download /></el-icon>
            批量导出
          </el-button>
          <el-button type="danger" @click="handleBatchDelete" :disabled="selectedOrders.length === 0">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>

        <div class="right-actions">
          <!-- 卡片详细程度切换 -->
          <el-button-group>
            <el-button
              :type="cardMode === 'detailed' ? 'primary' : ''"
              @click="cardMode = 'detailed'"
              title="详细模式"
            >
              详细
            </el-button>
            <el-button
              :type="cardMode === 'simple' ? 'primary' : ''"
              @click="cardMode = 'simple'"
              title="简化模式"
            >
              简化
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 订单列表 -->
    <el-card>
      <!-- 卡片视图 -->
      <div v-loading="loading">
        <!-- 批量选择工具栏 -->
        <div v-if="selectedOrders.length > 0" class="batch-selection-bar">
          <span>已选择 {{ selectedOrders.length }} 个订单</span>
          <el-button size="small" @click="clearSelection">清空选择</el-button>
        </div>

        <!-- 卡片列表 -->
        <div class="order-cards-list">
          <div
            v-for="order in orderList"
            :key="order.id"
            :class="['order-card', { 'selected': isOrderSelected(order) }]"
            @click="handleCardClick(order)"
          >
            <!-- 选择框 -->
            <div class="card-checkbox" @click.stop>
              <el-checkbox
                :model-value="isOrderSelected(order)"
                @change="handleCardSelection(order, $event)"
              />
            </div>

            <!-- 详细模式卡片内容 -->
            <div v-if="cardMode === 'detailed'" class="card-content detailed">
              <!-- 第一行：订单号、客户、项目、状态 -->
              <div class="card-row-1">
                <div class="left-info">
                  <div class="order-number">{{ order.order_number }}</div>
                  <div class="customer-project-status">
                    <span class="customer">{{ order.customer?.name || order.customer_name || 'N/A' }}</span>
                    <span class="separator">|</span>
                    <span class="project" :title="order.project_name">{{ order.project_name }}</span>
                    <div class="status-tags">
                      <el-tag :type="getOrderStatusType(order.order_status)" size="default" class="status-tag-prominent order-status">
                        {{ order.order_status }}
                      </el-tag>
                      <el-tag :type="getPaymentStatusType(order.payment_status)" size="default" class="status-tag-prominent payment-status">
                        {{ order.payment_status }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第二行：金额信息和时间信息 -->
              <div class="card-row-2">
                <div class="left-details">
                  <div class="amounts-info">
                    <div class="amount-group">
                      <span class="amount-label">订单金额</span>
                      <span class="amount-value total">{{ formatCurrency(Number(order.total_amount)) }}</span>
                    </div>
                    <div class="amount-group">
                      <span class="amount-label">已收款</span>
                      <span class="amount-value paid">{{ formatCurrency(order.paid_amount ?? 0) }}</span>
                    </div>
                    <div class="amount-group">
                      <span class="amount-label">待收款</span>
                      <span class="amount-value pending">
                        {{ formatCurrency(((order.total_amount ? Number(order.total_amount) : 0) - (order.paid_amount ?? 0))) }}
                      </span>
                    </div>
                  </div>
                  <div class="dates-info">
                    <div class="date-group">
                      <span class="date-label">预计交期</span>
                      <span class="date-value">{{ order.expected_date ? formatDate(order.expected_date) : 'N/A' }}</span>
                    </div>
                    <div class="date-group">
                      <span class="date-label">创建日期</span>
                      <span class="date-value">{{ order.created_at ? formatDate(order.created_at) : 'N/A' }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第三行：操作按钮 -->
              <div class="card-actions detailed" @click.stop>
                <!-- 基础操作 -->
                <el-button type="warning" size="small" @click="handleEdit(order)" v-if="order.status === '待确认'" title="编辑订单">
                  编辑
                </el-button>
                <el-button type="success" size="small" @click="handleExport(order)" title="导出订单">
                  导出
                </el-button>

                <!-- 状态相关操作 - 根据新的状态转换逻辑 -->
                <template v-if="isFlexibleStatus(order.order_status)">
                  <!-- 待确认状态：只能确认 -->
                  <template v-if="order.order_status === '待确认'">
                    <el-button
                      type="success"
                      size="small"
                      @click="handleStatusChange(order, '已确认')"
                      title="确认订单"
                    >
                      确认
                    </el-button>
                  </template>

                  <!-- 已确认状态：可以取消确认、开始生产、准备发货 -->
                  <template v-if="order.order_status === '已确认'">
                    <el-button
                      type="warning"
                      size="small"
                      @click="handleStatusChange(order, '待确认')"
                      title="取消确认"
                    >
                      取消确认
                    </el-button>
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleStatusChange(order, '生产中')"
                      title="开始生产"
                    >
                      开始生产
                    </el-button>
                    <el-button
                      type="info"
                      size="small"
                      @click="handleStatusChange(order, '待发货')"
                      title="准备发货"
                    >
                      准备发货
                    </el-button>
                  </template>

                  <!-- 生产中状态：可以回到已确认、准备发货 -->
                  <template v-if="order.order_status === '生产中'">
                    <el-button
                      type="success"
                      size="small"
                      @click="handleStatusChange(order, '已确认')"
                      title="回到已确认"
                    >
                      确认
                    </el-button>
                    <el-button
                      type="info"
                      size="small"
                      @click="handleStatusChange(order, '待发货')"
                      title="准备发货"
                    >
                      准备发货
                    </el-button>
                  </template>

                  <!-- 待发货状态：可以回到已确认、生产中 -->
                  <template v-if="order.order_status === '待发货'">
                    <el-button
                      type="success"
                      size="small"
                      @click="handleStatusChange(order, '已确认')"
                      title="回到已确认"
                    >
                      确认
                    </el-button>
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleStatusChange(order, '生产中')"
                      title="开始生产"
                    >
                      开始生产
                    </el-button>
                  </template>
                </template>

                <!-- 发货操作 -->
                <el-button
                  v-if="['已确认', '生产中', '待发货', '部分发货'].includes(order.order_status)"
                  type="success"
                  size="small"
                  @click="handleCreateDelivery(order)"
                  title="创建发货单"
                >
                  <el-icon><Van /></el-icon>
                  发货
                </el-button>

                <!-- 退货操作 -->
                <el-button
                  v-if="['部分发货', '全部发货', '已完成'].includes(order.order_status)"
                  type="warning"
                  size="small"
                  @click="handleCreateReturn(order)"
                  title="创建退货单"
                >
                  <el-icon><RefreshLeft /></el-icon>
                  退货
                </el-button>

                <!-- 对账操作 -->
                <el-button
                  v-if="['部分发货', '全部发货'].includes(order.order_status)"
                  type="warning"
                  size="small"
                  @click="handleCreateStatement(order)"
                  title="生成对账单"
                >
                  对账
                </el-button>

                <!-- 完成订单 -->
                <el-button
                  v-if="['已确认', '生产中', '待发货', '部分发货', '全部发货'].includes(order.order_status)"
                  type="success"
                  size="small"
                  @click="handleCompleteOrder(order)"
                  title="完成订单"
                >
                  完成
                </el-button>

                <!-- 取消订单 -->
                <el-button
                  v-if="canCancelOrder(order)"
                  type="warning"
                  size="small"
                  @click="handleCancelOrder(order)"
                  title="取消订单"
                >
                  取消订单
                </el-button>

                <!-- 删除操作 -->
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(order)"
                  v-if="order.order_status === '待确认' || order.order_status === '已取消'"
                  title="删除订单"
                >
                  删除
                </el-button>
              </div>
            </div>

            <!-- 简化模式卡片内容 -->
            <div v-else class="card-content simple">
              <!-- 紧凑单行信息展示 -->
              <div class="card-main simple">
                <!-- 订单号 -->
                <div class="order-number">{{ order.order_number }}</div>

                <!-- 客户和项目 -->
                <div class="customer-project">
                  <span class="customer">{{ order.customer?.name || order.customer_name || 'N/A' }}</span>
                  <span class="project" :title="order.project_name">{{ order.project_name }}</span>
                </div>

                <!-- 金额信息 -->
                <div class="amount-info">
                  <span class="total-amount">{{ formatCurrency(Number(order.total_amount)) }}</span>
                  <span class="payment-info">(已收: {{ formatCurrency(order.paid_amount ?? 0) }})</span>
                </div>

                <!-- 状态 -->
                <div class="status-tags">
                  <el-tag :type="getOrderStatusType(order.order_status)" size="default" class="status-tag-prominent order-status">
                    {{ order.order_status }}
                  </el-tag>
                  <el-tag :type="getPaymentStatusType(order.payment_status)" size="default" class="status-tag-prominent payment-status">
                    {{ order.payment_status }}
                  </el-tag>
                </div>

                <!-- 操作按钮 -->
                <div class="card-actions simple" @click.stop>
                  <!-- 查看详情按钮 -->
                  <el-button type="primary" size="small" @click="handleRowClick(order)" title="查看详情">
                    查看
                  </el-button>

                  <!-- 根据状态显示最重要的操作 -->
                  <template v-if="isFlexibleStatus(order.order_status)">
                    <!-- 待确认状态：显示确认按钮 -->
                    <el-button v-if="order.order_status === '待确认'" type="success" size="small" @click="handleStatusChange(order, '已确认')" title="确认订单">
                      确认
                    </el-button>
                    <!-- 已确认状态：显示生产按钮 -->
                    <el-button v-else-if="order.order_status === '已确认'" type="primary" size="small" @click="handleStatusChange(order, '生产中')" title="开始生产">
                      生产
                    </el-button>
                    <!-- 生产中状态：显示准备发货按钮 -->
                    <el-button v-else-if="order.order_status === '生产中'" type="info" size="small" @click="handleStatusChange(order, '待发货')" title="准备发货">
                      准备发货
                    </el-button>
                    <!-- 待发货状态：显示发货按钮 -->
                    <el-button v-else-if="order.order_status === '待发货'" type="success" size="small" @click="handleCreateDelivery(order)" title="创建发货单">
                      发货
                    </el-button>
                  </template>
                  <el-button
                    v-else-if="['已确认', '生产中', '待发货', '部分发货'].includes(order.order_status)"
                    type="success"
                    size="small"
                    @click="handleCreateDelivery(order)"
                    title="创建发货单"
                  >
                    <el-icon><Van /></el-icon>
                    发货
                  </el-button>
                  <el-button
                    v-else-if="['部分发货', '全部发货'].includes(order.order_status)"
                    type="warning"
                    size="small"
                    @click="handleCreateStatement(order)"
                    title="生成对账单"
                  >
                    对账
                  </el-button>

                  <!-- 更多操作下拉菜单 -->
                  <el-dropdown trigger="click" @click.stop>
                    <el-button size="small" type="info">
                      更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleExport(order)">导出订单</el-dropdown-item>
                        <el-dropdown-item
                          v-if="order.status === '待确认'"
                          @click="handleEdit(order)"
                        >
                          编辑订单
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="['部分发货', '全部发货', '已完成'].includes(order.order_status)"
                          @click="handleCreateReturn(order)"
                        >
                          创建退货单
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="['已确认', '生产中', '待发货', '部分发货', '全部发货'].includes(order.order_status)"
                          @click="handleCompleteOrder(order)"
                        >
                          完成订单
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="canCancelOrder(order)"
                          @click="handleCancelOrder(order)"
                        >
                          取消订单
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="order.order_status === '待确认' || order.order_status === '已取消'"
                          @click="handleDelete(order)"
                          divided
                        >
                          删除订单
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <el-pagination
        class="pagination-container"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </el-card>

    <!-- 导出对话框 -->
    <OrderExportDialog
      v-model="exportDialogVisible"
      :order-id="currentExportOrder?.id"
      :order-name="currentExportOrder?.project_name || currentExportOrder?.order_number"
      @success="handleExportSuccess"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, Download, Delete, Van, RefreshLeft, Grid, Postcard, ArrowDown } from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import { customerApi } from '@/api/customer'
import OrderExportDialog from '@/components/OrderExportDialog.vue'

const router = useRouter()

// 搜索表单引用
const searchFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  order_number: '',
  customer_id: '',
  project_name: '',
  order_status: '',  // 发货状态
  payment_status: '', // 收款状态
  date_range: null as [string, string] | null
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 订单列表数据
const orderList = ref([])
const selectedOrders = ref([])
const loading = ref(false)

// 客户选项
const customerOptions = ref([])
const customerSearchLoading = ref(false)

// 卡片模式
const cardMode = ref('detailed') // 'detailed' | 'simple'

// 导出相关状态
const exportDialogVisible = ref(false)
const currentExportOrder = ref(null)

// 获取订单列表
const getOrderList = async () => {
  try {
    loading.value = true
    const params: any = {
      page: pagination.currentPage,
      per_page: pagination.pageSize
    }

    // 处理搜索参数
    if (searchForm.order_number) {
      params.order_number = searchForm.order_number
    }
    if (searchForm.customer_id) {
      params.customer_id = searchForm.customer_id
    }
    if (searchForm.project_name) {
      params.project_name = searchForm.project_name
    }
    // 双状态筛选参数
    if (searchForm.order_status) {
      params.order_status = searchForm.order_status
    }
    if (searchForm.payment_status) {
      params.payment_status = searchForm.payment_status
    }
    if (searchForm.date_range && searchForm.date_range.length === 2) {
      params.start_date = searchForm.date_range[0]
      params.end_date = searchForm.date_range[1]
    }

    const response = await orderApi.getList(params) as any

    // 检查响应格式并处理数据
    if (Array.isArray(response)) {
      orderList.value = response
      pagination.total = response.length
    } else if (response && typeof response === 'object') {
      // 处理后端返回的数据格式
      const data = response.data
      if (Array.isArray(data)) {
        orderList.value = data
      } else if (data && typeof data === 'object' && Object.keys(data).length === 0) {
        // 空对象表示没有数据
        orderList.value = []
      } else {
        orderList.value = []
      }
      pagination.total = response.pagination?.total || 0
    } else {
      orderList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
    orderList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索客户
const handleSearchCustomers = async (query: string) => {
  if (query) {
    try {
      customerSearchLoading.value = true
      const response = await customerApi.getList({ name: query, per_page: 20 }) as any
      customerOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('搜索客户失败:', error)
    } finally {
      customerSearchLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  getOrderList()
}

// 重置搜索表单
const resetSearchForm = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    order_number: '',
    customer_id: '',
    project_name: '',
    status: '',
    date_range: null
  })
  pagination.currentPage = 1
  getOrderList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getOrderList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getOrderList()
}

// 选择处理
const handleSelectionChange = (selection: any[]) => {
  selectedOrders.value = selection
}

// 操作处理
const handleAdd = () => {
  router.push('/orders/new')
}

const handleEdit = (row: any) => {
  router.push(`/orders/edit/${row.id}`)
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除订单"${row.order_number}"吗？`, '确认删除', {
      type: 'warning'
    })

    await orderApi.delete(row.id)
    ElMessage.success('删除成功')
    getOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除订单失败:', error)
      ElMessage.error('删除订单失败')
    }
  }
}

const handleRowClick = (row: any) => {
  router.push(`/orders/view/${row.id}`)
}

// 卡片相关方法
const handleCardClick = (order: any) => {
  router.push(`/orders/view/${order.id}`)
}

const isOrderSelected = (order: any) => {
  return selectedOrders.value.some((selected: any) => selected.id === order.id)
}

const handleCardSelection = (order: any, checked: boolean) => {
  if (checked) {
    if (!isOrderSelected(order)) {
      selectedOrders.value.push(order)
    }
  } else {
    selectedOrders.value = selectedOrders.value.filter((selected: any) => selected.id !== order.id)
  }
}

const clearSelection = () => {
  selectedOrders.value = []
}

// 单个导出函数
const handleExport = (order: any) => {
  currentExportOrder.value = order
  exportDialogVisible.value = true
}

// 导出成功处理
const handleExportSuccess = () => {
  currentExportOrder.value = null
}

// 判断是否为灵活状态（前4个状态）
const isFlexibleStatus = (status: string) => {
  const flexibleStatuses = ['待确认', '已确认', '生产中', '待发货']
  return flexibleStatuses.includes(status)
}

// 通用状态变更方法
const handleStatusChange = async (row: any, targetStatus: string) => {
  const currentStatus = row.order_status

  const statusMap: Record<string, string> = {
    '待确认': '取消确认',
    '已确认': '确认',
    '生产中': '开始生产',
    '待发货': '准备发货'
  }

  const actionName = statusMap[targetStatus] || '状态变更'

  try {
    await ElMessageBox.confirm(
      `确定要执行"${actionName}"操作，将订单状态从"${currentStatus}"变更为"${targetStatus}"吗？`,
      actionName,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用API更新订单状态
    await orderApi.updateOrderStatus(row.id, targetStatus)
    ElMessage.success(`${actionName}成功`)

    // 刷新列表
    getOrderList()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || `${actionName}失败`)
    }
  }
}

const handleConfirmOrder = async (row: any) => {
  await handleStatusChange(row, '已确认')
}

const handleStartProduction = async (row: any) => {
  await handleStatusChange(row, '生产中')
}



const handleCreateDelivery = (row: any) => {
  const validStatuses = ['已确认', '生产中', '待发货', '部分发货']
  if (!validStatuses.includes(row.order_status)) {
    ElMessage.warning(`当前状态不支持发货操作，当前状态: ${row.order_status}`)
    return
  }

  // 跳转到发货单创建页面，带上订单ID
  router.push({
    path: '/delivery-notes/new',
    query: { order_id: row.id.toString() }
  })
}

const handleCreateStatement = (row: any) => {
  if (!['部分发货', '全部发货'].includes(row.order_status)) {
    ElMessage.warning(`只有发货后的订单才能生成对账单，当前状态: ${row.order_status}`)
    return
  }
  ElMessage.info('生成对账单功能开发中...')
}

const handleCreateReturn = (row: any) => {
  if (!['部分发货', '全部发货', '已完成'].includes(row.order_status)) {
    ElMessage.warning(`只有已发货的订单才能创建退货单，当前状态: ${row.order_status}。\n说明：需要有实际的发货单记录才能进行退货操作。`)
    return
  }

  // 跳转到创建退货单页面，并传递订单ID
  router.push({
    path: '/returns/create',
    query: {
      orderId: row.id,
      orderNumber: row.order_number,
      projectName: row.project_name
    }
  })
}

const handleCompleteOrder = async (row: any) => {
  const allowedStatuses = ['已确认', '生产中', '待发货', '部分发货', '全部发货']

  if (!allowedStatuses.includes(row.order_status)) {
    ElMessage.warning(`当前订单状态不支持完成操作，当前状态: ${row.order_status}`)
    return
  }

  try {
    await ElMessageBox.confirm(`确定要将订单 ${row.order_number} 标记为已完成吗？`, '确认完成', {
      type: 'warning'
    })

    await orderApi.updateOrderStatus(row.id, '已完成')
    ElMessage.success('订单已标记为已完成')
    getOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('订单完成失败:', error)
      ElMessage.error('订单完成失败')
    }
  }
}

// 取消订单
const handleCancelOrder = async (row: any) => {
  if (!canCancelOrder(row)) {
    ElMessage.warning(`当前订单状态不支持取消操作，当前状态: ${row.order_status}`)
    return
  }

  try {
    await ElMessageBox.confirm(`确定要取消订单 ${row.order_number} 吗？`, '确认取消', {
      type: 'warning'
    })

    await orderApi.updateOrderStatus(row.id, '已取消')
    ElMessage.success('订单已取消')
    getOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('订单取消失败:', error)
      ElMessage.error('订单取消失败')
    }
  }
}

// 判断订单是否可以取消
const canCancelOrder = (row: any) => {
  // 只有前期状态可以取消，部分发货后不可取消
  const cancellableStatuses = ['待确认', '已确认', '生产中', '待发货']
  return cancellableStatuses.includes(row.order_status)
}

// 批量操作

// 批量导出函数
const handleBatchExport = () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要导出的订单')
    return
  }

  if (selectedOrders.value.length === 1) {
    handleExport(selectedOrders.value[0])
  } else {
    // 处理多选导出逻辑
    ElMessage.info(`已选择${selectedOrders.value.length}个订单，当前版本仅支持单个导出`)
    handleExport(selectedOrders.value[0])
  }
}

const handleBatchDelete = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要删除的订单')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedOrders.value.length} 个订单吗？`, '确认删除', {
      type: 'warning'
    })

    const deletePromises = selectedOrders.value.map((order: any) => orderApi.delete(order.id))
    await Promise.all(deletePromises)

    ElMessage.success('批量删除成功')
    getOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 双状态系统常量
const ORDER_STATUS = {
  // 发货状态
  PENDING: '待确认',
  CONFIRMED: '已确认',
  PRODUCTION: '生产中',
  READY: '待发货',
  PARTIAL_SHIPPED: '部分发货',
  ALL_SHIPPED: '全部发货',
  COMPLETED: '已完成',
  CANCELLED: '已取消'
} as const

const PAYMENT_STATUS = {
  // 收款状态
  UNPAID: '未收款',
  PARTIAL_PAID: '部分收款',
  PAID: '已收款'
} as const

// 状态类型函数
const getOrderStatusType = (orderStatus: string) => {
  const statusMap: Record<string, string> = {
    [ORDER_STATUS.PENDING]: 'warning',
    [ORDER_STATUS.CONFIRMED]: 'success',
    [ORDER_STATUS.PRODUCTION]: 'primary',
    [ORDER_STATUS.READY]: 'info',
    [ORDER_STATUS.PARTIAL_SHIPPED]: 'warning',
    [ORDER_STATUS.ALL_SHIPPED]: 'success',
    [ORDER_STATUS.COMPLETED]: 'success',
    [ORDER_STATUS.CANCELLED]: 'danger'
  }
  return statusMap[orderStatus] || 'info'
}

const getPaymentStatusType = (paymentStatus: string) => {
  const statusMap: Record<string, string> = {
    [PAYMENT_STATUS.UNPAID]: 'danger',
    [PAYMENT_STATUS.PARTIAL_PAID]: 'warning',
    [PAYMENT_STATUS.PAID]: 'success'
  }
  return statusMap[paymentStatus] || 'info'
}

// 移除旧的getStatusLabel函数，现在直接使用状态值显示

const formatCurrency = (value: number) => {
  if (value === undefined || value === null) return 'N/A'
  const numericValue = Number(value)
  if (isNaN(numericValue)) return 'N/A'
  return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(numericValue)
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return 'N/A'
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) {
      return dateStr
    }
    return date.toLocaleDateString('zh-CN')
  } catch (e) {
    return dateStr
  }
}

// 初始化
onMounted(() => {
  getOrderList()
})
</script>

<style lang="scss" scoped>
.order-list {
  .search-form-card {
    .el-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .operator-card {
    .operator-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .left-actions {
      display: flex;
      gap: 10px;
    }

    .right-actions {
      display: flex;
      align-items: center;
      gap: 10px;

      .ml-10 {
        margin-left: 10px;
      }
    }

    .el-button {
      margin: 0;
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }

  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;

    .el-button {
      margin: 0;
      padding: 4px 8px;
      font-size: 12px;

      .el-icon {
        margin-right: 2px;
      }
    }
  }

  // 批量选择工具栏
  .batch-selection-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #606266;
  }

  // 卡片列表布局
  .order-cards-list {
    display: flex;
    flex-direction: column;
    gap: 16px; // 增加卡片间距
    margin-bottom: 20px;
  }

  // 订单卡片样式
  .order-card {
    position: relative;
    width: 100%;
    border: 2px solid #e8eaed; // 增加边框宽度，使用更柔和的颜色
    border-radius: 12px; // 增加圆角
    background: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); // 添加轻微阴影
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 4px; // 额外的底部间距

    &:hover {
      border-color: #409eff;
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15); // 增强悬停阴影
      transform: translateY(-1px); // 轻微上移效果
    }

    &.selected {
      border-color: #409eff;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2), 0 4px 16px rgba(64, 158, 255, 0.1); // 双重阴影效果
      transform: translateY(-1px);
    }

    .card-checkbox {
      position: absolute;
      top: 16px;
      left: 16px;
      z-index: 2;
    }

    .card-content {
      padding: 12px 16px;
      padding-left: 44px; // 为选择框留出空间

      &.detailed {
        padding: 14px 16px;
        padding-left: 44px;
      }

      &.simple {
        padding: 10px 16px;
        padding-left: 44px;
      }
    }

    // 详细模式样式
    .card-content.detailed {
      .card-row-1 {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;

        .left-info {
          flex: 1;
          min-width: 0;

          .order-number {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .customer-project-status {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;

            .customer {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              flex-shrink: 0;
            }

            .separator {
              color: #dcdfe6;
              font-size: 12px;
            }

            .project {
              font-size: 13px;
              color: #606266;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 200px;
              margin-right: 12px;
            }

            .status-tags {
              display: flex;
              gap: 8px;
              flex-shrink: 0;

              .status-tag-prominent {
                font-weight: 600;
                border-radius: 8px;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                border: 2px solid transparent;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                }

                // 订单状态 - 统一大小
                &.order-status {
                  font-size: 13px;
                  padding: 5px 12px;
                  font-weight: 600;
                  letter-spacing: 0.3px;

                  &.el-tag--success {
                    background: linear-gradient(135deg, #67c23a, #85ce61);
                    border-color: #67c23a;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(103, 194, 58, 0.25);
                  }

                  &.el-tag--warning {
                    background: linear-gradient(135deg, #e6a23c, #ebb563);
                    border-color: #e6a23c;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25);
                  }

                  &.el-tag--danger {
                    background: linear-gradient(135deg, #f56c6c, #f78989);
                    border-color: #f56c6c;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(245, 108, 108, 0.25);
                  }

                  &.el-tag--info {
                    background: linear-gradient(135deg, #909399, #a6a9ad);
                    border-color: #909399;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(144, 147, 153, 0.25);
                  }

                  &.el-tag--primary {
                    background: linear-gradient(135deg, #409eff, #66b1ff);
                    border-color: #409eff;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
                  }
                }

                // 收款状态 - 统一大小
                &.payment-status {
                  font-size: 13px;
                  padding: 5px 12px;
                  font-weight: 600;
                  letter-spacing: 0.3px;

                  &.el-tag--success {
                    background: linear-gradient(135deg, #67c23a, #85ce61);
                    border-color: #67c23a;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(103, 194, 58, 0.25);
                  }

                  &.el-tag--warning {
                    background: linear-gradient(135deg, #e6a23c, #ebb563);
                    border-color: #e6a23c;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25);
                  }

                  &.el-tag--danger {
                    background: linear-gradient(135deg, #f56c6c, #f78989);
                    border-color: #f56c6c;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(245, 108, 108, 0.25);
                  }

                  &.el-tag--info {
                    background: linear-gradient(135deg, #909399, #a6a9ad);
                    border-color: #909399;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(144, 147, 153, 0.25);
                  }

                  &.el-tag--primary {
                    background: linear-gradient(135deg, #409eff, #66b1ff);
                    border-color: #409eff;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
                  }
                }
              }
            }
          }
        }
      }

      .card-row-2 {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;

        .left-details {
          flex: 1;
          display: flex;
          gap: 32px;

          .amounts-info {
            display: flex;
            gap: 20px;

            .amount-group {
              display: flex;
              flex-direction: column;

              .amount-label {
                font-size: 11px;
                color: #909399;
                margin-bottom: 2px;
              }

              .amount-value {
                font-size: 13px;
                font-weight: 600;

                &.total {
                  color: #303133;
                }

                &.paid {
                  color: #67c23a;
                }

                &.pending {
                  color: #e6a23c;
                }
              }
            }
          }

          .dates-info {
            display: flex;
            gap: 16px;

            .date-group {
              display: flex;
              flex-direction: column;

              .date-label {
                font-size: 11px;
                color: #909399;
                margin-bottom: 2px;
              }

              .date-value {
                font-size: 12px;
                color: #606266;
              }
            }
          }
        }
      }

      .card-actions.detailed {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        border-top: 1px solid #ebeef5;
        padding-top: 8px;

        .el-button {
          font-size: 12px;
          padding: 3px 6px;
          height: 24px;
        }
      }
    }

    // 简化模式样式
    .card-content.simple {
      .card-main.simple {
        display: flex;
        align-items: center;
        gap: 16px;
        width: 100%;
      }

      .order-number {
        flex: 0 0 140px;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }

      .customer-project {
        flex: 1;
        min-width: 0;

        .customer {
          color: #303133;
          font-weight: 500;
          font-size: 13px;
          margin-right: 8px;
        }

        .project {
          color: #606266;
          font-size: 13px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .amount-info {
        flex: 0 0 160px;
        text-align: center;

        .total-amount {
          display: block;
          color: #303133;
          font-weight: 600;
          font-size: 13px;
          margin-bottom: 1px;
        }

        .payment-info {
          color: #909399;
          font-size: 11px;
        }
      }

      .status-tags {
        flex: 0 0 120px;
        display: flex;
        gap: 4px;
        justify-content: center;
      }

      .card-actions.simple {
        flex: 0 0 auto;
        display: flex;
        gap: 6px;
        align-items: center;

        .el-button {
          font-size: 11px;
          padding: 3px 6px;
          height: 24px;
        }
      }
    }
  }
}
</style>
