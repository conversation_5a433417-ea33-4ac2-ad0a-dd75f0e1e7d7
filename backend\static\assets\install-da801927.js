import{_ as t,an as e,ap as i,aq as a,ao as n,ca as o,cb as s,C as r,ax as l,z as h,aB as u,cc as c,Z as p,O as d,aA as g,cV as m,aC as y,j as f,bC as v,aE as b,cd as S,aj as _,f as x,dj as I,R as M,cM as w,er as A,V as D,S as N,es as P,aG as k,et as L,ay as O,az as C,eu as T,ar as z,bl as E,h as G,bK as V,ev as R,ew as X,x as Y,e as F,b6 as H,c8 as B,cT as W,ex as U,ey as j,ez as Z,cJ as q,eA as J}from"./install-028483b9.js";const K=function(n){function o(){var t=null!==n&&n.apply(this,arguments)||this;return t.type=o.type,t.hasSymbolVisual=!0,t}return t(o,n),o.prototype.getInitialData=function(t){return e(null,this,{useEncodeDefaulter:!0})},o.prototype.getLegendIcon=function(t){var e=new i,n=a("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);e.add(n),n.setStyle(t.lineStyle);var o=this.getData().getVisual("symbol"),s=this.getData().getVisual("symbolRotate"),r="none"===o?"circle":o,l=.8*t.itemHeight,h=a(r,(t.itemWidth-l)/2,(t.itemHeight-l)/2,l,l,t.itemStyle.fill);e.add(h),h.setStyle(t.itemStyle);var u="inherit"===t.iconRotate?s:t.iconRotate||0;return h.rotation=u*Math.PI/180,h.setOrigin([t.itemWidth/2,t.itemHeight/2]),r.indexOf("empty")>-1&&(h.style.stroke=h.style.fill,h.style.fill="#fff",h.style.lineWidth=2),e},o.type="series.line",o.dependencies=["grid","polar"],o.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},o}(n);function Q(t,e){this.parent.drift(t,e)}const $=function(e){function i(t,i,a,n){var o=e.call(this)||this;return o.updateData(t,i,a,n),o}return t(i,e),i.prototype._createSymbol=function(t,e,i,n,o){this.removeAll();var s=a(t,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:n[0]/2,scaleY:n[1]/2}),s.drift=Q,this._symbolType=t,this.add(s)},i.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},i.prototype.getSymbolType=function(){return this._symbolType},i.prototype.getSymbolPath=function(){return this.childAt(0)},i.prototype.highlight=function(){o(this.childAt(0))},i.prototype.downplay=function(){s(this.childAt(0))},i.prototype.setZ=function(t,e){var i=this.childAt(0);i.zlevel=t,i.z=e},i.prototype.setDraggable=function(t,e){var i=this.childAt(0);i.draggable=t,i.cursor=!e&&t?"move":i.cursor},i.prototype.updateData=function(t,e,a,n){this.silent=!1;var o=t.getItemVisual(e,"symbol")||"circle",s=t.hostModel,u=i.getSymbolSize(t,e),c=o!==this._symbolType,p=n&&n.disableAnimation;if(c){var d=t.getItemVisual(e,"symbolKeepAspect");this._createSymbol(o,t,e,u,d)}else{(m=this.childAt(0)).silent=!1;var g={scaleX:u[0]/2,scaleY:u[1]/2};p?m.attr(g):r(m,g,s,e),l(m)}if(this._updateCommon(t,e,u,a,n),c){var m=this.childAt(0);if(!p){g={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:m.style.opacity}};m.scaleX=m.scaleY=0,m.style.opacity=0,h(m,g,s,e)}}p&&this.childAt(0).stopAnimation("leave")},i.prototype._updateCommon=function(t,e,i,a,n){var o,s,r,l,h,f,v,b,S,_=this.childAt(0),x=t.hostModel;if(a&&(o=a.emphasisItemStyle,s=a.blurItemStyle,r=a.selectItemStyle,l=a.focus,h=a.blurScope,v=a.labelStatesModels,b=a.hoverScale,S=a.cursorStyle,f=a.emphasisDisabled),!a||t.hasItemOption){var I=a&&a.itemModel?a.itemModel:t.getItemModel(e),M=I.getModel("emphasis");o=M.getModel("itemStyle").getItemStyle(),r=I.getModel(["select","itemStyle"]).getItemStyle(),s=I.getModel(["blur","itemStyle"]).getItemStyle(),l=M.get("focus"),h=M.get("blurScope"),f=M.get("disabled"),v=u(I),b=M.getShallow("scale"),S=I.getShallow("cursor")}var w=t.getItemVisual(e,"symbolRotate");_.attr("rotation",(w||0)*Math.PI/180||0);var A=c(t.getItemVisual(e,"symbolOffset"),i);A&&(_.x=A[0],_.y=A[1]),S&&_.attr("cursor",S);var D=t.getItemVisual(e,"style"),N=D.fill;if(_ instanceof p){var P=_.style;_.useStyle(d({image:P.image,x:P.x,y:P.y,width:P.width,height:P.height},D))}else _.__isEmptyBrush?_.useStyle(d({},D)):_.useStyle(D),_.style.decal=null,_.setColor(N,n&&n.symbolInnerColor),_.style.strokeNoScale=!0;var k=t.getItemVisual(e,"liftZ"),L=this._z2;null!=k?null==L&&(this._z2=_.z2,_.z2+=k):null!=L&&(_.z2=L,this._z2=null);var O=n&&n.useNameLabel;g(_,v,{labelFetcher:x,labelDataIndex:e,defaultText:function(e){return O?t.getName(e):m(t,e)},inheritColor:N,defaultOpacity:D.opacity}),this._sizeX=i[0]/2,this._sizeY=i[1]/2;var C=_.ensureState("emphasis");C.style=o,_.ensureState("select").style=r,_.ensureState("blur").style=s;var T=null==b||!0===b?Math.max(1.1,3/this._sizeY):isFinite(b)&&b>0?+b:1;C.scaleX=this._sizeX*T,C.scaleY=this._sizeY*T,this.setSymbolScale(1),y(this,l,h,f)},i.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},i.prototype.fadeOut=function(t,e,i){var a=this.childAt(0),n=f(this).dataIndex,o=i&&i.animation;if(this.silent=a.silent=!0,i&&i.fadeLabel){var s=a.getTextContent();s&&v(s,{style:{opacity:0}},e,{dataIndex:n,removeOpt:o,cb:function(){a.removeTextContent()}})}else a.removeTextContent();v(a,{style:{opacity:0},scaleX:0,scaleY:0},e,{dataIndex:n,cb:t,removeOpt:o})},i.getSymbolSize=function(t,e){return b(t.getItemVisual(e,"symbolSize"))},i}(i);function tt(t,e,i,a){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(a.isIgnore&&a.isIgnore(i))&&!(a.clipShape&&!a.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(i,"symbol")}function et(t){return null==t||_(t)||(t={isIgnore:t}),t||{}}function it(t){var e=t.hostModel,i=e.getModel("emphasis");return{emphasisItemStyle:i.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:i.get("focus"),blurScope:i.get("blurScope"),emphasisDisabled:i.get("disabled"),hoverScale:i.get("scale"),labelStatesModels:u(e),cursorStyle:e.get("cursor")}}const at=function(){function t(t){this.group=new i,this._SymbolCtor=t||$}return t.prototype.updateData=function(t,e){this._progressiveEls=null,e=et(e);var i=this.group,a=t.hostModel,n=this._data,o=this._SymbolCtor,s=e.disableAnimation,l=it(t),h={disableAnimation:s},u=e.getSymbolPoint||function(e){return t.getItemLayout(e)};n||i.removeAll(),t.diff(n).add((function(a){var n=u(a);if(tt(t,n,a,e)){var s=new o(t,a,l,h);s.setPosition(n),t.setItemGraphicEl(a,s),i.add(s)}})).update((function(c,p){var d=n.getItemGraphicEl(p),g=u(c);if(tt(t,g,c,e)){var m=t.getItemVisual(c,"symbol")||"circle",y=d&&d.getSymbolType&&d.getSymbolType();if(!d||y&&y!==m)i.remove(d),(d=new o(t,c,l,h)).setPosition(g);else{d.updateData(t,c,l,h);var f={x:g[0],y:g[1]};s?d.attr(f):r(d,f,a)}i.add(d),t.setItemGraphicEl(c,d)}else i.remove(d)})).remove((function(t){var e=n.getItemGraphicEl(t);e&&e.fadeOut((function(){i.remove(e)}),a)})).execute(),this._getSymbolPoint=u,this._data=t},t.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl((function(e,i){var a=t._getSymbolPoint(i);e.setPosition(a),e.markRedraw()}))},t.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=it(t),this._data=null,this.group.removeAll()},t.prototype.incrementalUpdate=function(t,e,i){function a(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[],i=et(i);for(var n=t.start;n<t.end;n++){var o=e.getItemLayout(n);if(tt(e,o,n,i)){var s=new this._SymbolCtor(e,n,this._seriesScope);s.traverse(a),s.setPosition(o),this.group.add(s),e.setItemGraphicEl(n,s),this._progressiveEls.push(s)}}},t.prototype.eachRendered=function(t){S(this._progressiveEls||this.group,t)},t.prototype.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl((function(t){t.fadeOut((function(){e.remove(t)}),i.hostModel)})):e.removeAll()},t}();function nt(t,e,i){var a=t.getBaseAxis(),n=t.getOtherAxis(a),o=function(t,e){var i=0,a=t.scale.getExtent();"start"===e?i=a[0]:"end"===e?i=a[1]:M(e)&&!isNaN(e)?i=e:a[0]>0?i=a[0]:a[1]<0&&(i=a[1]);return i}(n,i),s=a.dim,r=n.dim,l=e.mapDimension(r),h=e.mapDimension(s),u="x"===r||"radius"===r?1:0,c=x(t.dimensions,(function(t){return e.mapDimension(t)})),p=!1,d=e.getCalculationInfo("stackResultDimension");return I(e,c[0])&&(p=!0,c[0]=d),I(e,c[1])&&(p=!0,c[1]=d),{dataDimsForPoint:c,valueStart:o,valueAxisDim:r,baseAxisDim:s,stacked:!!p,valueDim:l,baseDim:h,baseDataOffset:u,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function ot(t,e,i,a){var n=NaN;t.stacked&&(n=i.get(i.getCalculationInfo("stackedOverDimension"),a)),isNaN(n)&&(n=t.valueStart);var o=t.baseDataOffset,s=[];return s[o]=i.get(t.baseDim,a),s[1-o]=n,e.dataToPoint(s)}var st=Math.min,rt=Math.max;function lt(t,e){return isNaN(t)||isNaN(e)}function ht(t,e,i,a,n,o,s,r,l){for(var h,u,c,p,d,g,m=i,y=0;y<a;y++){var f=e[2*m],v=e[2*m+1];if(m>=n||m<0)break;if(lt(f,v)){if(l){m+=o;continue}break}if(m===i)t[o>0?"moveTo":"lineTo"](f,v),c=f,p=v;else{var b=f-h,S=v-u;if(b*b+S*S<.5){m+=o;continue}if(s>0){for(var _=m+o,x=e[2*_],I=e[2*_+1];x===f&&I===v&&y<a;)y++,m+=o,x=e[2*(_+=o)],I=e[2*_+1],b=(f=e[2*m])-h,S=(v=e[2*m+1])-u;var M=y+1;if(l)for(;lt(x,I)&&M<a;)M++,x=e[2*(_+=o)],I=e[2*_+1];var w=.5,A=0,D=0,N=void 0,P=void 0;if(M>=a||lt(x,I))d=f,g=v;else{A=x-h,D=I-u;var k=f-h,L=x-f,O=v-u,C=I-v,T=void 0,z=void 0;if("x"===r){var E=A>0?1:-1;d=f-E*(T=Math.abs(k))*s,g=v,N=f+E*(z=Math.abs(L))*s,P=v}else if("y"===r){var G=D>0?1:-1;d=f,g=v-G*(T=Math.abs(O))*s,N=f,P=v+G*(z=Math.abs(C))*s}else T=Math.sqrt(k*k+O*O),d=f-A*s*(1-(w=(z=Math.sqrt(L*L+C*C))/(z+T))),g=v-D*s*(1-w),P=v+D*s*w,N=st(N=f+A*s*w,rt(x,f)),P=st(P,rt(I,v)),N=rt(N,st(x,f)),g=v-(D=(P=rt(P,st(I,v)))-v)*T/z,d=st(d=f-(A=N-f)*T/z,rt(h,f)),g=st(g,rt(u,v)),N=f+(A=f-(d=rt(d,st(h,f))))*z/T,P=v+(D=v-(g=rt(g,st(u,v))))*z/T}t.bezierCurveTo(c,p,d,g,f,v),c=N,p=P}else t.lineTo(f,v)}h=f,u=v,m+=o}return y}var ut=function(){this.smooth=0,this.smoothConstraint=!0},ct=function(e){function i(t){var i=e.call(this,t)||this;return i.type="ec-polyline",i}return t(i,e),i.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},i.prototype.getDefaultShape=function(){return new ut},i.prototype.buildPath=function(t,e){var i=e.points,a=0,n=i.length/2;if(e.connectNulls){for(;n>0&&lt(i[2*n-2],i[2*n-1]);n--);for(;a<n&&lt(i[2*a],i[2*a+1]);a++);}for(;a<n;)a+=ht(t,i,a,n,n,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},i.prototype.getPointOn=function(t,e){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var i,a,n=this.path.data,o=N.CMD,s="x"===e,r=[],l=0;l<n.length;){var h=void 0,u=void 0,c=void 0,p=void 0,d=void 0,g=void 0,m=void 0;switch(n[l++]){case o.M:i=n[l++],a=n[l++];break;case o.L:if(h=n[l++],u=n[l++],(m=s?(t-i)/(h-i):(t-a)/(u-a))<=1&&m>=0){var y=s?(u-a)*m+a:(h-i)*m+i;return s?[t,y]:[y,t]}i=h,a=u;break;case o.C:h=n[l++],u=n[l++],c=n[l++],p=n[l++],d=n[l++],g=n[l++];var f=s?A(i,h,c,d,t,r):A(a,u,p,g,t,r);if(f>0)for(var v=0;v<f;v++){var b=r[v];if(b<=1&&b>=0){y=s?P(a,u,p,g,b):P(i,h,c,d,b);return s?[t,y]:[y,t]}}i=d,a=g}}},i}(D),pt=function(e){function i(){return null!==e&&e.apply(this,arguments)||this}return t(i,e),i}(ut),dt=function(e){function i(t){var i=e.call(this,t)||this;return i.type="ec-polygon",i}return t(i,e),i.prototype.getDefaultShape=function(){return new pt},i.prototype.buildPath=function(t,e){var i=e.points,a=e.stackedOnPoints,n=0,o=i.length/2,s=e.smoothMonotone;if(e.connectNulls){for(;o>0&&lt(i[2*o-2],i[2*o-1]);o--);for(;n<o&&lt(i[2*n],i[2*n+1]);n++);}for(;n<o;){var r=ht(t,i,n,o,o,1,e.smooth,s,e.connectNulls);ht(t,a,n+r-1,r,o,-1,e.stackedOnSmooth,s,e.connectNulls),n+=r+1,t.closePath()}},i}(D);function gt(t,e){if(t.length===e.length){for(var i=0;i<t.length;i++)if(t[i]!==e[i])return;return!0}}function mt(t){for(var e=1/0,i=1/0,a=-1/0,n=-1/0,o=0;o<t.length;){var s=t[o++],r=t[o++];isNaN(s)||(e=Math.min(s,e),a=Math.max(s,a)),isNaN(r)||(i=Math.min(r,i),n=Math.max(r,n))}return[[e,i],[a,n]]}function yt(t,e){var i=mt(t),a=i[0],n=i[1],o=mt(e),s=o[0],r=o[1];return Math.max(Math.abs(a[0]-s[0]),Math.abs(a[1]-s[1]),Math.abs(n[0]-r[0]),Math.abs(n[1]-r[1]))}function ft(t){return M(t)?t:t?.5:0}function vt(t,e,i,a,n){var o=i.getBaseAxis(),s="x"===o.dim||"radius"===o.dim?0:1,r=[],l=0,h=[],u=[],c=[],p=[];if(n){for(l=0;l<t.length;l+=2){var d=e||t;isNaN(d[l])||isNaN(d[l+1])||p.push(t[l],t[l+1])}t=p}for(l=0;l<t.length-2;l+=2)switch(c[0]=t[l+2],c[1]=t[l+3],u[0]=t[l],u[1]=t[l+1],r.push(u[0],u[1]),a){case"end":h[s]=c[s],h[1-s]=u[1-s],r.push(h[0],h[1]);break;case"middle":var g=(u[s]+c[s])/2,m=[];h[s]=m[s]=g,h[1-s]=u[1-s],m[1-s]=c[1-s],r.push(h[0],h[1]),r.push(m[0],m[1]);break;default:h[s]=u[s],h[1-s]=c[1-s],r.push(h[0],h[1])}return r.push(t[l++],t[l++]),r}function bt(t,e,i){var a=t.getVisual("visualMeta");if(a&&a.length&&t.count()&&"cartesian2d"===e.type){for(var n,o,s=a.length-1;s>=0;s--){var r=t.getDimensionInfo(a[s].dimension);if("x"===(n=r&&r.coordDim)||"y"===n){o=a[s];break}}if(o){var l=e.getAxis(n),h=x(o.stops,(function(t){return{coord:l.toGlobalCoord(l.dataToCoord(t.value)),color:t.color}})),u=h.length,c=o.outerColors.slice();u&&h[0].coord>h[u-1].coord&&(h.reverse(),c.reverse());var p=function(t,e){var i,a,n=[],o=t.length;function s(t,e,i){var a=t.coord,n=(i-a)/(e.coord-a);return{coord:i,color:Z(n,[t.color,e.color])}}for(var r=0;r<o;r++){var l=t[r],h=l.coord;if(h<0)i=l;else{if(h>e){a?n.push(s(a,l,e)):i&&n.push(s(i,l,0),s(i,l,e));break}i&&(n.push(s(i,l,0)),i=null),n.push(l),a=l}}return n}(h,"x"===n?i.getWidth():i.getHeight()),d=p.length;if(!d&&u)return h[0].coord<0?c[1]?c[1]:h[u-1].color:c[0]?c[0]:h[0].color;var g=p[0].coord-10,m=p[d-1].coord+10,y=m-g;if(y<.001)return"transparent";F(p,(function(t){t.offset=(t.coord-g)/y})),p.push({offset:d?p[d-1].offset:.5,color:c[1]||"transparent"}),p.unshift({offset:d?p[0].offset:.5,color:c[0]||"transparent"});var f=new H(0,0,0,0,p,!0);return f[n]=g,f[n+"2"]=m,f}}}function St(t,e,i){var a=t.get("showAllSymbol"),n="auto"===a;if(!a||n){var o=i.getAxesByScale("ordinal")[0];if(o&&(!n||!function(t,e){var i=t.getExtent(),a=Math.abs(i[1]-i[0])/t.scale.count();isNaN(a)&&(a=0);for(var n=e.count(),o=Math.max(1,Math.round(n/5)),s=0;s<n;s+=o)if(1.5*$.getSymbolSize(e,s)[t.isHorizontal()?1:0]>a)return!1;return!0}(o,e))){var s=e.mapDimension(o.dim),r={};return F(o.getViewLabels(),(function(t){var e=o.scale.getRawOrdinalNumber(t.tickValue);r[e]=1})),function(t){return!r.hasOwnProperty(e.get(s,t))}}}}function _t(t,e){return[t[2*e],t[2*e+1]]}function xt(t){if(t.get(["endLabel","show"]))return!0;for(var e=0;e<B.length;e++)if(t.get([B[e],"endLabel","show"]))return!0;return!1}function It(t,e,i,a){if(W(e,"cartesian2d")){var n=a.getModel("endLabel"),o=n.get("valueAnimation"),s=a.getData(),r={lastFrameIndex:0},l=xt(a)?function(i,a){t._endLabelOnDuring(i,a,s,r,o,n,e)}:null,h=e.getBaseAxis().isHorizontal(),u=U(e,i,a,(function(){var e=t._endLabel;e&&i&&null!=r.originalX&&e.attr({x:r.originalX,y:r.originalY})}),l);if(!a.get("clip",!0)){var c=u.shape,p=Math.max(c.width,c.height);h?(c.y-=p,c.height+=2*p):(c.x-=p,c.width+=2*p)}return l&&l(1,u),u}return j(e,i,a)}const Mt=function(e){function a(){return null!==e&&e.apply(this,arguments)||this}return t(a,e),a.prototype.init=function(){var t=new i,e=new at;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t,this._changePolyState=k(this._changePolyState,this)},a.prototype.render=function(t,e,i){var a=t.coordinateSystem,n=this.group,o=t.getData(),s=t.getModel("lineStyle"),r=t.getModel("areaStyle"),l=o.getLayout("points")||[],u="polar"===a.type,c=this._coordSys,p=this._symbolDraw,d=this._polyline,g=this._polygon,m=this._lineGroup,v=!e.ssr&&t.get("animation"),b=!r.isEmpty(),S=r.get("origin"),_=nt(a,o,S),x=b&&function(t,e,i){if(!i.valueDim)return[];for(var a=e.count(),n=w(2*a),o=0;o<a;o++){var s=ot(i,t,e,o);n[2*o]=s[0],n[2*o+1]=s[1]}return n}(a,o,_),I=t.get("showSymbol"),M=t.get("connectNulls"),A=I&&!u&&St(t,o,a),D=this._data;D&&D.eachItemGraphicEl((function(t,e){t.__temp&&(n.remove(t),D.setItemGraphicEl(e,null))})),I||p.remove(),n.add(m);var N,P=!u&&t.get("step");a&&a.getArea&&t.get("clip",!0)&&(null!=(N=a.getArea()).width?(N.x-=.1,N.y-=.1,N.width+=.2,N.height+=.2):N.r0&&(N.r0-=.5,N.r+=.5)),this._clipShapeForSymbol=N;var k=bt(o,a,i)||o.getVisual("style")[o.getVisual("drawType")];if(d&&c.type===a.type&&P===this._step){b&&!g?g=this._newPolygon(l,x):g&&!b&&(m.remove(g),g=this._polygon=null),u||this._initOrUpdateEndLabel(t,a,L(k));var T=m.getClipPath();if(T){var z=It(this,a,!1,t);h(T,{shape:z.shape},t)}else m.setClipPath(It(this,a,!0,t));I&&p.updateData(o,{isIgnore:A,clipShape:N,disableAnimation:!0,getSymbolPoint:function(t){return[l[2*t],l[2*t+1]]}}),gt(this._stackedOnPoints,x)&&gt(this._points,l)||(v?this._doUpdateAnimation(o,x,a,i,P,S,M):(P&&(x&&(x=vt(x,l,a,P,M)),l=vt(l,null,a,P,M)),d.setShape({points:l}),g&&g.setShape({points:l,stackedOnPoints:x})))}else I&&p.updateData(o,{isIgnore:A,clipShape:N,disableAnimation:!0,getSymbolPoint:function(t){return[l[2*t],l[2*t+1]]}}),v&&this._initSymbolLabelAnimation(o,a,N),P&&(x&&(x=vt(x,l,a,P,M)),l=vt(l,null,a,P,M)),d=this._newPolyline(l),b?g=this._newPolygon(l,x):g&&(m.remove(g),g=this._polygon=null),u||this._initOrUpdateEndLabel(t,a,L(k)),m.setClipPath(It(this,a,!0,t));var E=t.getModel("emphasis"),G=E.get("focus"),V=E.get("blurScope"),R=E.get("disabled");(d.useStyle(O(s.getLineStyle(),{fill:"none",stroke:k,lineJoin:"bevel"})),C(d,t,"lineStyle"),d.style.lineWidth>0&&"bolder"===t.get(["emphasis","lineStyle","width"]))&&(d.getState("emphasis").style.lineWidth=+d.style.lineWidth+1);f(d).seriesIndex=t.seriesIndex,y(d,G,V,R);var X=ft(t.get("smooth")),Y=t.get("smoothMonotone");if(d.setShape({smooth:X,smoothMonotone:Y,connectNulls:M}),g){var F=o.getCalculationInfo("stackedOnSeries"),H=0;g.useStyle(O(r.getAreaStyle(),{fill:k,opacity:.7,lineJoin:"bevel",decal:o.getVisual("style").decal})),F&&(H=ft(F.get("smooth"))),g.setShape({smooth:X,stackedOnSmooth:H,smoothMonotone:Y,connectNulls:M}),C(g,t,"areaStyle"),f(g).seriesIndex=t.seriesIndex,y(g,G,V,R)}var B=this._changePolyState;o.eachItemGraphicEl((function(t){t&&(t.onHoverStateChange=B)})),this._polyline.onHoverStateChange=B,this._data=o,this._coordSys=a,this._stackedOnPoints=x,this._points=l,this._step=P,this._valueOrigin=S,t.get("triggerLineEvent")&&(this.packEventData(t,d),g&&this.packEventData(t,g))},a.prototype.packEventData=function(t,e){f(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},a.prototype.highlight=function(t,e,i,a){var n=t.getData(),o=T(n,a);if(this._changePolyState("emphasis"),!(o instanceof Array)&&null!=o&&o>=0){var s=n.getLayout("points"),r=n.getItemGraphicEl(o);if(!r){var l=s[2*o],h=s[2*o+1];if(isNaN(l)||isNaN(h))return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(l,h))return;var u=t.get("zlevel")||0,c=t.get("z")||0;(r=new $(n,o)).x=l,r.y=h,r.setZ(u,c);var p=r.getSymbolPath().getTextContent();p&&(p.zlevel=u,p.z=c,p.z2=this._polyline.z2+1),r.__temp=!0,n.setItemGraphicEl(o,r),r.stopSymbolAnimation(!0),this.group.add(r)}r.highlight()}else z.prototype.highlight.call(this,t,e,i,a)},a.prototype.downplay=function(t,e,i,a){var n=t.getData(),o=T(n,a);if(this._changePolyState("normal"),null!=o&&o>=0){var s=n.getItemGraphicEl(o);s&&(s.__temp?(n.setItemGraphicEl(o,null),this.group.remove(s)):s.downplay())}else z.prototype.downplay.call(this,t,e,i,a)},a.prototype._changePolyState=function(t){var e=this._polygon;E(this._polyline,t),e&&E(e,t)},a.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new ct({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e,e},a.prototype._newPolygon=function(t,e){var i=this._polygon;return i&&this._lineGroup.remove(i),i=new dt({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(i),this._polygon=i,i},a.prototype._initSymbolLabelAnimation=function(t,e,i){var a,n,o=e.getBaseAxis(),s=o.inverse;"cartesian2d"===e.type?(a=o.isHorizontal(),n=!1):"polar"===e.type&&(a="angle"===o.dim,n=!0);var r=t.hostModel,l=r.get("animationDuration");G(l)&&(l=l(null));var h=r.get("animationDelay")||0,u=G(h)?h(null):h;t.eachItemGraphicEl((function(t,o){var r=t;if(r){var c=[t.x,t.y],p=void 0,d=void 0,g=void 0;if(i)if(n){var m=i,y=e.pointToCoord(c);a?(p=m.startAngle,d=m.endAngle,g=-y[1]/180*Math.PI):(p=m.r0,d=m.r,g=y[0])}else{var f=i;a?(p=f.x,d=f.x+f.width,g=t.x):(p=f.y+f.height,d=f.y,g=t.y)}var v=d===p?0:(g-p)/(d-p);s&&(v=1-v);var b=G(h)?h(o):l*v+u,S=r.getSymbolPath(),_=S.getTextContent();r.attr({scaleX:0,scaleY:0}),r.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:b}),_&&_.animateFrom({style:{opacity:0}},{duration:300,delay:b}),S.disableLabelAnimation=!0}}))},a.prototype._initOrUpdateEndLabel=function(t,e,i){var a=t.getModel("endLabel");if(xt(t)){var n=t.getData(),o=this._polyline,s=n.getLayout("points");if(!s)return o.removeTextContent(),void(this._endLabel=null);var r=this._endLabel;r||((r=this._endLabel=new V({z2:200})).ignoreClip=!0,o.setTextContent(this._endLabel),o.disableLabelAnimation=!0);var l=function(t){for(var e,i,a=t.length/2;a>0&&(e=t[2*a-2],i=t[2*a-1],isNaN(e)||isNaN(i));a--);return a-1}(s);l>=0&&(g(o,u(t,"endLabel"),{inheritColor:i,labelFetcher:t,labelDataIndex:l,defaultText:function(t,e,i){return null!=i?R(n,i):m(n,t)},enableTextSetter:!0},function(t,e){var i=e.getBaseAxis(),a=i.isHorizontal(),n=i.inverse,o=a?n?"right":"left":"center",s=a?"middle":n?"top":"bottom";return{normal:{align:t.get("align")||o,verticalAlign:t.get("verticalAlign")||s}}}(a,e)),o.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},a.prototype._endLabelOnDuring=function(t,e,i,a,n,o,s){var r=this._endLabel,l=this._polyline;if(r){t<1&&null==a.originalX&&(a.originalX=r.x,a.originalY=r.y);var h=i.getLayout("points"),u=i.hostModel,c=u.get("connectNulls"),p=o.get("precision"),d=o.get("distance")||0,g=s.getBaseAxis(),m=g.isHorizontal(),y=g.inverse,f=e.shape,v=y?m?f.x:f.y+f.height:m?f.x+f.width:f.y,b=(m?d:0)*(y?-1:1),S=(m?0:-d)*(y?-1:1),_=m?"x":"y",x=function(t,e,i){for(var a,n,o=t.length/2,s="x"===i?0:1,r=0,l=-1,h=0;h<o;h++)if(n=t[2*h+s],!isNaN(n)&&!isNaN(t[2*h+1-s]))if(0!==h){if(a<=e&&n>=e||a>=e&&n<=e){l=h;break}r=h,a=n}else a=n;return{range:[r,l],t:(e-a)/(n-a)}}(h,v,_),I=x.range,M=I[1]-I[0],w=void 0;if(M>=1){if(M>1&&!c){var A=_t(h,I[0]);r.attr({x:A[0]+b,y:A[1]+S}),n&&(w=u.getRawValue(I[0]))}else{(A=l.getPointOn(v,_))&&r.attr({x:A[0]+b,y:A[1]+S});var D=u.getRawValue(I[0]),N=u.getRawValue(I[1]);n&&(w=X(i,p,D,N,x.t))}a.lastFrameIndex=I[0]}else{var P=1===t||a.lastFrameIndex>0?I[0]:0;A=_t(h,P);n&&(w=u.getRawValue(P)),r.attr({x:A[0]+b,y:A[1]+S})}if(n){var k=Y(r);"function"==typeof k.setLabelText&&k.setLabelText(w)}}},a.prototype._doUpdateAnimation=function(t,e,i,a,n,o,s){var l=this._polyline,h=this._polygon,u=t.hostModel,c=function(t,e,i,a,n,o,s){for(var r=function(t,e){var i=[];return e.diff(t).add((function(t){i.push({cmd:"+",idx:t})})).update((function(t,e){i.push({cmd:"=",idx:e,idx1:t})})).remove((function(t){i.push({cmd:"-",idx:t})})).execute(),i}(t,e),l=[],h=[],u=[],c=[],p=[],d=[],g=[],m=nt(n,e,s),y=t.getLayout("points")||[],f=e.getLayout("points")||[],v=0;v<r.length;v++){var b=r[v],S=!0,_=void 0,x=void 0;switch(b.cmd){case"=":_=2*b.idx,x=2*b.idx1;var I=y[_],M=y[_+1],A=f[x],D=f[x+1];(isNaN(I)||isNaN(M))&&(I=A,M=D),l.push(I,M),h.push(A,D),u.push(i[_],i[_+1]),c.push(a[x],a[x+1]),g.push(e.getRawIndex(b.idx1));break;case"+":var N=b.idx,P=m.dataDimsForPoint,k=n.dataToPoint([e.get(P[0],N),e.get(P[1],N)]);x=2*N,l.push(k[0],k[1]),h.push(f[x],f[x+1]);var L=ot(m,n,e,N);u.push(L[0],L[1]),c.push(a[x],a[x+1]),g.push(e.getRawIndex(N));break;case"-":S=!1}S&&(p.push(b),d.push(d.length))}d.sort((function(t,e){return g[t]-g[e]}));var O=l.length,C=w(O),T=w(O),z=w(O),E=w(O),G=[];for(v=0;v<d.length;v++){var V=d[v],R=2*v,X=2*V;C[R]=l[X],C[R+1]=l[X+1],T[R]=h[X],T[R+1]=h[X+1],z[R]=u[X],z[R+1]=u[X+1],E[R]=c[X],E[R+1]=c[X+1],G[v]=p[V]}return{current:C,next:T,stackedOnCurrent:z,stackedOnNext:E,status:G}}(this._data,t,this._stackedOnPoints,e,this._coordSys,0,this._valueOrigin),p=c.current,d=c.stackedOnCurrent,g=c.next,m=c.stackedOnNext;if(n&&(d=vt(c.stackedOnCurrent,c.current,i,n,s),p=vt(c.current,null,i,n,s),m=vt(c.stackedOnNext,c.next,i,n,s),g=vt(c.next,null,i,n,s)),yt(p,g)>3e3||h&&yt(d,m)>3e3)return l.stopAnimation(),l.setShape({points:g}),void(h&&(h.stopAnimation(),h.setShape({points:g,stackedOnPoints:m})));l.shape.__points=c.current,l.shape.points=p;var y={shape:{points:g}};c.current!==p&&(y.shape.__points=c.next),l.stopAnimation(),r(l,y,u),h&&(h.setShape({points:p,stackedOnPoints:d}),h.stopAnimation(),r(h,{shape:{stackedOnPoints:m}},u),l.shape.points!==h.shape.points&&(h.shape.points=l.shape.points));for(var f=[],v=c.status,b=0;b<v.length;b++){if("="===v[b].cmd){var S=t.getItemGraphicEl(v[b].idx1);S&&f.push({el:S,ptIdx:b})}}l.animators&&l.animators.length&&l.animators[0].during((function(){h&&h.dirtyShape();for(var t=l.shape.__points,e=0;e<f.length;e++){var i=f[e].el,a=2*f[e].ptIdx;i.x=t[a],i.y=t[a+1],i.markRedraw()}}))},a.prototype.remove=function(t){var e=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl((function(t,a){t.__temp&&(e.remove(t),i.setItemGraphicEl(a,null))})),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},a.type="line",a}(z);function wt(t,e){return{seriesType:t,plan:q(),reset:function(t){var i=t.getData(),a=t.coordinateSystem,n=t.pipelineContext,o=e||n.large;if(a){var s=x(a.dimensions,(function(t){return i.mapDimension(t)})).slice(0,2),r=s.length,l=i.getCalculationInfo("stackResultDimension");I(i,s[0])&&(s[0]=l),I(i,s[1])&&(s[1]=l);var h=i.getStore(),u=i.getDimensionIndex(s[0]),c=i.getDimensionIndex(s[1]);return r&&{progress:function(t,e){for(var i=t.end-t.start,n=o&&w(i*r),s=[],l=[],p=t.start,d=0;p<t.end;p++){var g=void 0;if(1===r){var m=h.get(u,p);g=a.dataToPoint(m,null,l)}else s[0]=h.get(u,p),s[1]=h.get(c,p),g=a.dataToPoint(s,null,l);o?(n[d++]=g[0],n[d++]=g[1]):e.setItemLayout(p,g.slice())}o&&e.setLayout("points",n)}}}}}}function At(t){t.registerChartView(Mt),t.registerSeriesModel(K),t.registerLayout(wt("line",!0)),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),i=t.getModel("lineStyle").getLineStyle();i&&!i.stroke&&(i.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",i)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,J("line"))}export{dt as E,at as S,$ as a,At as i,wt as p};
