"""
系统设置相关模型
包括系统设置、公司信息、银行账户、文档模板、备份记录
"""
from datetime import datetime
from app import db
from app.models.base import BaseModel


class SystemSetting(BaseModel):
    """系统设置模型"""
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False, comment='设置键')
    value = db.Column(db.Text, nullable=True, comment='设置值')
    description = db.Column(db.Text, comment='设置描述')

    def __repr__(self):
        return f'<SystemSetting {self.key}>'

    def to_dict(self):
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class CompanyInfo(BaseModel):
    """企业信息模型"""
    __tablename__ = 'company_info'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, comment='企业名称')
    english_name = db.Column(db.String(200), nullable=True, comment='英文名称')
    tax_id = db.Column(db.String(50), nullable=False, comment='统一社会信用代码')
    legal_representative = db.Column(db.String(100), nullable=True, comment='法定代表人')
    registered_capital = db.Column(db.String(50), nullable=True, comment='注册资本')
    establishment_date = db.Column(db.Date, nullable=True, comment='成立日期')
    business_scope = db.Column(db.Text, nullable=True, comment='经营范围')
    contact = db.Column(db.String(50), nullable=True, comment='联系人')
    contact_title = db.Column(db.String(50), nullable=True, comment='联系人职务')
    phone = db.Column(db.String(20), nullable=True, comment='联系电话')
    mobile = db.Column(db.String(20), nullable=True, comment='手机号码')
    email = db.Column(db.String(100), nullable=True, comment='电子邮箱')
    fax = db.Column(db.String(20), nullable=True, comment='传真')
    website = db.Column(db.String(200), nullable=True, comment='网站')
    registered_address = db.Column(db.String(500), nullable=True, comment='注册地址')
    office_address = db.Column(db.String(500), nullable=True, comment='办公地址')
    postal_code = db.Column(db.String(10), nullable=True, comment='邮政编码')
    bank_name = db.Column(db.String(100), nullable=True, comment='开户银行')
    bank_account = db.Column(db.String(50), nullable=True, comment='银行账号')
    logo = db.Column(db.String(255), nullable=True, comment='公司Logo')
    license_image = db.Column(db.String(255), nullable=True, comment='营业执照图片')
    industry = db.Column(db.String(100), nullable=True, comment='行业')
    company_type = db.Column(db.String(50), nullable=True, comment='公司类型')
    employee_count = db.Column(db.String(50), nullable=True, comment='员工数量')
    description = db.Column(db.Text, nullable=True, comment='公司描述')
    address = db.Column(db.String(200), nullable=True, comment='公司地址')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'english_name': self.english_name,
            'tax_id': self.tax_id,
            'legal_representative': self.legal_representative,
            'registered_capital': self.registered_capital,
            'establishment_date': self.establishment_date.strftime('%Y-%m-%d') if self.establishment_date else None,
            'business_scope': self.business_scope,
            'contact': self.contact,
            'contact_title': self.contact_title,
            'phone': self.phone,
            'mobile': self.mobile,
            'email': self.email,
            'fax': self.fax,
            'website': self.website,
            'registered_address': self.registered_address,
            'office_address': self.office_address,
            'postal_code': self.postal_code,
            'bank_name': self.bank_name,
            'bank_account': self.bank_account,
            'logo': self.logo,
            'license_image': self.license_image,
            'industry': self.industry,
            'company_type': self.company_type,
            'employee_count': self.employee_count,
            'description': self.description,
            'address': self.address,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class CompanyBankAccount(BaseModel):
    """企业银行账户模型"""
    __tablename__ = 'company_bank_accounts'

    id = db.Column(db.Integer, primary_key=True)
    bank_name = db.Column(db.String(100), nullable=False, comment='开户行名称')
    account_name = db.Column(db.String(100), nullable=False, comment='账户名称')
    account_number = db.Column(db.String(50), nullable=False, comment='账号')
    is_default = db.Column(db.Boolean, default=False, nullable=False, comment='是否默认账户')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    def to_dict(self):
        return {
            'id': self.id,
            'bank_name': self.bank_name,
            'account_name': self.account_name,
            'account_number': self.account_number,
            'is_default': self.is_default,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class DocumentTemplate(BaseModel):
    """单据模板模型"""
    __tablename__ = 'document_templates'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='模板名称')
    type = db.Column(db.String(50), nullable=False, comment='模板类型(报价单/订单/送货单等)')
    content = db.Column(db.Text, nullable=True, comment='模板内容')
    is_default = db.Column(db.Boolean, default=False, nullable=False, comment='是否默认模板')
    status = db.Column(db.String(20), default='启用', nullable=False, comment='状态(启用/禁用)')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'type': self.type,
            'content': self.content,
            'is_default': self.is_default,
            'status': self.status,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class BackupRecord(BaseModel):
    """备份记录模型"""
    __tablename__ = 'backup_records'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, comment='备份名称')
    filename = db.Column(db.String(255), nullable=False, comment='备份文件名')
    file_path = db.Column(db.String(500), nullable=False, comment='文件路径')
    file_size = db.Column(db.BigInteger, nullable=False, comment='文件大小(字节)')
    backup_type = db.Column(db.String(20), nullable=False, comment='备份类型(manual/auto)')
    status = db.Column(db.String(20), nullable=False, default='pending', comment='状态(pending/running/completed/failed)')
    description = db.Column(db.String(500), nullable=True, comment='备份描述')
    checksum = db.Column(db.String(64), nullable=True, comment='文件MD5校验和')
    error_message = db.Column(db.Text, nullable=True, comment='错误信息')
    started_at = db.Column(db.DateTime, nullable=True, comment='开始时间')
    completed_at = db.Column(db.DateTime, nullable=True, comment='完成时间')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'filename': self.filename,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'backup_type': self.backup_type,
            'status': self.status,
            'description': self.description,
            'checksum': self.checksum,
            'error_message': self.error_message,
            'started_at': self.started_at.strftime('%Y-%m-%d %H:%M:%S') if self.started_at else None,
            'completed_at': self.completed_at.strftime('%Y-%m-%d %H:%M:%S') if self.completed_at else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class BackupSettings(BaseModel):
    """备份设置模型"""
    __tablename__ = 'backup_settings'

    id = db.Column(db.Integer, primary_key=True)
    auto_backup_enabled = db.Column(db.Boolean, nullable=False, default=False, comment='是否启用自动备份')
    backup_frequency = db.Column(db.String(20), nullable=False, default='daily', comment='备份频率(daily/weekly/monthly)')
    backup_time = db.Column(db.String(8), nullable=False, default='02:00:00', comment='备份时间(HH:MM:SS)')
    backup_day = db.Column(db.Integer, nullable=True, comment='备份日期(周几或几号)')
    keep_count = db.Column(db.Integer, nullable=False, default=10, comment='保留备份数量')
    backup_path = db.Column(db.String(500), nullable=False, comment='备份存储路径')
    last_backup_time = db.Column(db.DateTime, nullable=True, comment='上次备份时间')

    def to_dict(self):
        return {
            'id': self.id,
            'auto_backup_enabled': self.auto_backup_enabled,
            'backup_frequency': self.backup_frequency,
            'backup_time': self.backup_time,
            'backup_day': self.backup_day,
            'keep_count': self.keep_count,
            'backup_path': self.backup_path,
            'last_backup_time': self.last_backup_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_backup_time else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
