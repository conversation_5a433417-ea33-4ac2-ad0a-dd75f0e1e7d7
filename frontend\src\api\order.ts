import request from './request'

// 订单相关API
export const orderApi = {
  // 获取订单列表
  getList: (params: any) => {
    return request.get('/orders', { params })
  },

  // 获取订单详情
  getById: (id: number, params?: any) => {
    return request.get(`/orders/${id}`, { params })
  },

  // 创建订单
  create: (data: any) => {
    return request.post('/orders', data)
  },

  // 更新订单
  update: (id: number, data: any) => {
    return request.put(`/orders/${id}`, data)
  },

  // 删除订单
  delete: (id: number) => {
    return request.delete(`/orders/${id}`)
  },

  // 更新订单状态(兼容方法)
  updateStatus: (id: string | number, status: string, comment?: string) => {
    return request.put(`/orders/${id}/status`, { status, comment })
  },

  // 更新发货状态
  updateOrderStatus: (id: string | number, order_status: string, comment?: string) => {
    return request.put(`/orders/${id}/order-status`, { order_status, comment })
  },

  // 更新财务状态
  updatePaymentStatus: (id: string | number, payment_status: string, comment?: string) => {
    return request.put(`/orders/${id}/payment-status`, { payment_status, comment })
  },

  // 确认订单
  confirm: (id: number) => {
    return request.post(`/orders/${id}/confirm`)
  },

  // 取消订单
  cancel: (id: number, reason?: string) => {
    return request.post(`/orders/${id}/cancel`, { reason })
  },

  // 订单审批
  approve: (id: number, approved: boolean, comment?: string) => {
    return request.post(`/orders/${id}/approve`, { approved, comment })
  },

  // 导出订单
  export: (id: number, format: string = 'pdf') => {
    return request.get(`/orders/${id}/export`, {
      params: { format },
      responseType: 'blob'
    })
  },

  // 导出订单详情（支持列选择）
  exportDetail: (id: number, config: any = {}) => {
    const params = {
      format: config.format || 'xlsx',
      columns: config.columns ? config.columns.join(',') : undefined,
      include_header: config.include_header !== undefined ? config.include_header : true
    }
    // 移除undefined的参数
    Object.keys(params).forEach(key => params[key] === undefined && delete params[key])

    return request.get(`/order-export/${id}/export`, {
      params,
      responseType: 'blob'
    })
  },

  // 批量导出订单
  batchExport: (ids: number[], format: string = 'pdf') => {
    return request.post('/orders/batch-export', {
      order_ids: ids,
      format
    }, {
      responseType: 'blob'
    })
  },

  // 获取订单统计
  getStats: (params?: any) => {
    return request.get('/orders/stats', { params })
  },

  // 从报价单添加项目到订单
  addItemsFromQuotation: (orderId: number, quotationId: number) => {
    return request.post(`/orders/${orderId}/add-from-quotation`, { quotation_id: quotationId })
  },

  // 导入报价单到订单
  importQuotation: (orderId: string, data: { quotation_id: number }) => {
    return request.post(`/orders/${orderId}/import-quotation`, data)
  }
}

// 订单项目相关API
export const orderItemApi = {
  // 获取订单项目列表
  getList: (orderId: number) => {
    return request.get(`/orders/${orderId}/items`)
  },

  // 添加订单项目
  add: (orderId: number, data: any) => {
    return request.post(`/orders/${orderId}/items`, data)
  },

  // 更新订单项目
  update: (orderId: number, itemId: number, data: any) => {
    return request.put(`/orders/${orderId}/items/${itemId}`, data)
  },

  // 删除订单项目
  delete: (orderId: number, itemId: number) => {
    return request.delete(`/orders/${orderId}/items/${itemId}`)
  }
}

// 发货单管理API
export const deliveryApi = {
  // 获取发货单列表
  getList: (params?: PaginationParams & { status?: string }) => {
    return http.get<PaginatedResponse<DeliveryNote>>('/delivery-notes', params)
  },

  // 获取发货单详情
  getById: (id: number) => {
    return http.get<DeliveryNote>(`/delivery-notes/${id}`)
  },

  // 创建发货单
  create: (data: Partial<DeliveryNote>) => {
    return http.post<DeliveryNote>('/delivery-notes', data)
  },

  // 从订单创建发货单
  createFromOrder: (orderId: number, data?: Partial<DeliveryNote>) => {
    return http.post<DeliveryNote>(`/delivery-notes/from-order/${orderId}`, data)
  },

  // 更新发货单
  update: (id: number, data: Partial<DeliveryNote>) => {
    return http.put<DeliveryNote>(`/delivery-notes/${id}`, data)
  },

  // 更新发货单状态
  updateStatus: (id: number, status: string) => {
    return http.put(`/delivery-notes/${id}/status`, { status })
  },

  // 删除发货单
  delete: (id: number) => {
    return http.delete(`/delivery-notes/${id}`)
  },

  // 确认发货
  ship: (id: number, trackingNumber?: string) => {
    return http.post(`/delivery-notes/${id}/ship`, { trackingNumber })
  },

  // 确认送达
  deliver: (id: number) => {
    return http.post(`/delivery-notes/${id}/deliver`)
  },

  // 打印发货单
  print: (id: number) => {
    return http.download(`/delivery-notes/${id}/print`, {}, `delivery-note-${id}.pdf`)
  },
}

// 退货单管理API
export const returnApi = {
  // 获取退货单列表
  getList: (params?: PaginationParams & { status?: string }) => {
    return http.get<PaginatedResponse<ReturnOrder>>('/returns', params)
  },

  // 获取退货单详情
  getById: (id: number) => {
    return http.get<ReturnOrder>(`/returns/${id}`)
  },

  // 创建退货单
  create: (data: Partial<ReturnOrder>) => {
    return http.post<ReturnOrder>('/returns', data)
  },

  // 更新退货单
  update: (id: number, data: Partial<ReturnOrder>) => {
    return http.put<ReturnOrder>(`/returns/${id}`, data)
  },

  // 更新退货单状态
  updateStatus: (id: number, status: string) => {
    return http.put(`/returns/${id}/status`, { status })
  },

  // 删除退货单
  delete: (id: number) => {
    return http.delete(`/returns/${id}`)
  },

  // 审批退货单
  approve: (id: number, approved: boolean, reason?: string) => {
    return http.post(`/returns/${id}/approve`, { approved, reason })
  },

  // 完成退货
  complete: (id: number) => {
    return http.post(`/returns/${id}/complete`)
  },
}

// 对账单管理API
export const statementApi = {
  // 获取对账单列表
  getList: (params?: PaginationParams & { status?: string }) => {
    return http.get<PaginatedResponse<Statement>>('/statements', params)
  },

  // 获取对账单详情
  getById: (id: number) => {
    return http.get<Statement>(`/statements/${id}`)
  },

  // 创建对账单
  create: (data: Partial<Statement>) => {
    return http.post<Statement>('/statements', data)
  },

  // 更新对账单
  update: (id: number, data: Partial<Statement>) => {
    return http.put<Statement>(`/statements/${id}`, data)
  },

  // 删除对账单
  delete: (id: number) => {
    return http.delete(`/statements/${id}`)
  },

  // 发送对账单
  send: (id: number) => {
    return http.post(`/statements/${id}/send`)
  },

  // 确认对账单
  confirm: (id: number) => {
    return http.post(`/statements/${id}/confirm`)
  },

  // 打印对账单
  print: (id: number) => {
    return http.download(`/statements/${id}/print`, {}, `statement-${id}.pdf`)
  },

  // 生成对账单
  generate: (customerId: number, periodStart: string, periodEnd: string) => {
    return http.post<Statement>('/statements/generate', {
      customerId,
      periodStart,
      periodEnd,
    })
  },
}
