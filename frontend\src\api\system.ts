import request from './request'

// 系统设置相关API
export const systemSettingsApi = {
  // 获取企业信息
  getCompanyInfo: () => {
    return request.get('/system/company-info')
  },

  // 更新企业信息（纯JSON提交；图片请先通过 uploadFile 上传获取URL）
  updateCompanyInfo: (data: any) => {
    return request.put('/system/company-info', data, { headers: { 'Content-Type': 'application/json' } })
  },

  // 上传文件
  uploadFile: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return request.post('/system/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取系统配置
  getSystemConfig: () => {
    return request.get('/system/config')
  },

  // 更新系统配置
  updateSystemConfig: (data: any) => {
    return request.put('/system/config', data)
  }
}

// 银行账户相关API
export const bankAccountApi = {
  // 获取银行账户列表
  getList: (params?: any) => {
    return request.get('/system/company-bank-accounts', { params })
  },

  // 获取银行账户详情
  getById: (id: number) => {
    return request.get(`/system/company-bank-accounts/${id}`)
  },

  // 创建银行账户
  create: (data: any) => {
    return request.post('/system/company-bank-accounts', data)
  },

  // 更新银行账户
  update: (id: number, data: any) => {
    return request.put(`/system/company-bank-accounts/${id}`, data)
  },

  // 删除银行账户
  delete: (id: number) => {
    return request.delete(`/system/company-bank-accounts/${id}`)
  },

  // 设置默认账户
  setDefault: (id: number) => {
    return request.post(`/system/company-bank-accounts/${id}/set-default`)
  },

  // 切换账户状态（暂时不实现，因为后端模型没有status字段）
  toggleStatus: (id: number) => {
    return Promise.resolve({ message: '功能暂未实现' })
  },

  // 获取账户交易记录（暂时不实现）
  getTransactions: (id: number, params?: any) => {
    return Promise.resolve([])
  }
}

// 用户管理相关API
export const userManagementApi = {
  // 获取用户列表
  getList: (params?: any) => {
    return request.get('/system/users', { params })
  },

  // 获取用户详情
  getById: (id: number) => {
    return request.get(`/system/users/${id}`)
  },

  // 创建用户
  create: (data: any) => {
    return request.post('/system/users', data)
  },

  // 更新用户
  update: (id: number, data: any) => {
    return request.put(`/system/users/${id}`, data)
  },

  // 删除用户
  delete: (id: number) => {
    return request.delete(`/system/users/${id}`)
  },

  // 重置密码
  resetPassword: (id: number, newPassword: string) => {
    return request.post(`/system/users/${id}/reset-password`, { password: newPassword })
  },

  // 切换用户状态
  toggleStatus: (id: number) => {
    return request.post(`/system/users/${id}/toggle-status`)
  },

  // 获取角色列表
  getRoles: () => {
    return request.get('/system/roles')
  },

  // 分配角色
  assignRole: (userId: number, roleIds: number[]) => {
    return request.post(`/system/users/${userId}/assign-roles`, { role_ids: roleIds })
  }
}

// 品牌管理相关API
export const brandManagementApi = {
  // 获取品牌列表
  getList: (params?: any) => {
    return request.get('/products/brands', { params })
  },

  // 获取品牌详情
  getById: (id: number) => {
    return request.get(`/products/brands/${id}`)
  },

  // 创建品牌
  create: (data: any) => {
    return request.post('/products/brands', data)
  },

  // 更新品牌
  update: (id: number, data: any) => {
    return request.put(`/products/brands/${id}`, data)
  },

  // 删除品牌
  delete: (id: number) => {
    return request.delete(`/products/brands/${id}`)
  }
}

// 数据备份恢复相关API
export const backupApi = {
  // 获取备份列表
  getBackupList: (params?: { page?: number; per_page?: number }) => {
    return request.get('/backup/backups', { params })
  },

  // 获取备份详情
  getBackupDetail: (id: number) => {
    return request.get(`/backup/backups/${id}`)
  },

  // 创建备份
  createBackup: (data: { name: string; description?: string }) => {
    return request.post('/backup/backups', data)
  },

  // 下载备份
  downloadBackup: (id: number) => {
    return request.get(`/backup/backups/${id}/download`, {
      responseType: 'blob'
    })
  },

  // 删除备份
  deleteBackup: (id: number) => {
    return request.delete(`/backup/backups/${id}`)
  },

  // 恢复数据
  restoreData: (id: number) => {
    return request.post(`/backup/backups/${id}/restore`)
  },

  // 上传备份文件并恢复
  uploadAndRestore: (file: File, name: string, description?: string) => {
    const formData = new FormData()
    formData.append('backup_file', file)
    formData.append('name', name)
    if (description) {
      formData.append('description', description)
    }
    return request.post('/backup/backups/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取备份统计
  getStatistics: () => {
    return request.get('/backup/backups/statistics')
  },

  // 获取备份设置
  getSettings: () => {
    return request.get('/backup/backup-settings')
  },

  // 更新备份设置
  updateSettings: (data: any) => {
    return request.put('/backup/backup-settings', data)
  },

  // 获取调度器状态
  getSchedulerStatus: () => {
    return request.get('/backup/scheduler/status')
  }
}

// 保持向后兼容
export const dataBackupApi = backupApi

// 系统日志相关API
export const systemLogApi = {
  // 获取操作日志
  getOperationLogs: (params?: any) => {
    return request.get('/system/logs/operations', { params })
  },

  // 获取错误日志
  getErrorLogs: (params?: any) => {
    return request.get('/system/logs/errors', { params })
  },

  // 获取登录日志
  getLoginLogs: (params?: any) => {
    return request.get('/system/logs/logins', { params })
  },

  // 清理日志
  clearLogs: (type: string, beforeDate?: string) => {
    return request.delete('/system/logs/clear', {
      data: { type, before_date: beforeDate }
    })
  }
}

// 系统统计相关API
export const systemStatsApi = {
  // 获取系统概览统计
  getOverview: () => {
    return request.get('/system/stats/overview')
  },

  // 获取用户活跃度统计
  getUserActivity: (params?: any) => {
    return request.get('/system/stats/user-activity', { params })
  },

  // 获取系统性能统计
  getPerformance: (params?: any) => {
    return request.get('/system/stats/performance', { params })
  }
}

// 兼容原项目的API函数
export const getCompanyInfo = systemSettingsApi.getCompanyInfo
export const updateCompanyInfo = systemSettingsApi.updateCompanyInfo
export const uploadFile = systemSettingsApi.uploadFile
export const getSystemConfig = systemSettingsApi.getSystemConfig
export const updateSystemConfig = systemSettingsApi.updateSystemConfig

export const getBankAccounts = bankAccountApi.getList
export const getBankAccountDetail = bankAccountApi.getById
export const createBankAccount = bankAccountApi.create
export const updateBankAccount = bankAccountApi.update
export const deleteBankAccount = bankAccountApi.delete
export const setDefaultBankAccount = bankAccountApi.setDefault
export const toggleBankAccountStatus = bankAccountApi.toggleStatus
export const getBankAccountTransactions = bankAccountApi.getTransactions

export const getUsers = userManagementApi.getList
export const getUserDetail = userManagementApi.getById
export const createUser = userManagementApi.create
export const updateUser = userManagementApi.update
export const deleteUser = userManagementApi.delete
export const resetUserPassword = userManagementApi.resetPassword
export const toggleUserStatus = userManagementApi.toggleStatus
export const getUserRoles = userManagementApi.getRoles
export const assignUserRole = userManagementApi.assignRole

export const getBrands = brandManagementApi.getList
export const getBrandDetail = brandManagementApi.getById
export const createBrand = brandManagementApi.create
export const updateBrand = brandManagementApi.update
export const deleteBrand = brandManagementApi.delete

export const getBackupList = dataBackupApi.getBackupList
export const createDataBackup = dataBackupApi.createBackup
export const downloadDataBackup = dataBackupApi.downloadBackup
export const deleteDataBackup = dataBackupApi.deleteBackup
export const restoreDataFromBackup = dataBackupApi.restoreData
export const uploadDataBackup = dataBackupApi.uploadBackup

export const getOperationLogs = systemLogApi.getOperationLogs
export const getErrorLogs = systemLogApi.getErrorLogs
export const getLoginLogs = systemLogApi.getLoginLogs
export const clearSystemLogs = systemLogApi.clearLogs

export const getSystemOverview = systemStatsApi.getOverview
export const getUserActivityStats = systemStatsApi.getUserActivity
export const getSystemPerformanceStats = systemStatsApi.getPerformance
