import{J as e,b as a,r as t,L as l,e as o,f as n,M as i,o as r,c as s,i as p,h as u,a8 as d,l as c,k as m,N as f,g as _,t as g,a as h,j as y,U as v,X as w,Y as b,Z as C,a2 as k,ag as j,a4 as z}from"./index-3d4c440c.js";import{l as x,d as q}from"./quotationTemplate-3cbc44ef.js";import{a as Q}from"./format-552375ee.js";import{_ as T}from"./_plugin-vue_export-helper-1b428a4d.js";const U={class:"app-container"},S=T(e({__name:"QuotationTemplateList",setup(e){const T=a(),S=t(!1),V=t([]),F=t(0),I=l({page:1,per_page:20,name:""}),L=async()=>{var e,a;S.value=!0;try{const t=await x(I);V.value=t.list,F.value=(null==(e=t.pagination)?void 0:e.total_items)||(null==(a=t.pagination)?void 0:a.total)||0}catch(t){console.error("Failed to fetch quotation templates:",t),v.error("获取报价模板列表失败")}finally{S.value=!1}},N=()=>{I.page=1,L()},D=()=>{I.page=1,I.name="",L()},E=()=>{T.push({name:"QuotationTemplateCreate"})};return o((()=>{L()})),(e,a)=>{const t=n("el-input"),l=n("el-form-item"),o=n("el-button"),x=n("el-form"),J=n("el-col"),M=n("el-row"),P=n("el-table-column"),R=n("el-tag"),X=n("el-popconfirm"),Y=n("el-table"),Z=n("el-pagination"),$=n("el-card"),A=i("loading");return r(),s("div",U,[p($,null,{default:u((()=>[p(x,{inline:!0,model:I,onSubmit:d(N,["prevent"])},{default:u((()=>[p(l,{label:"模板名称"},{default:u((()=>[p(t,{modelValue:I.name,"onUpdate:modelValue":a[0]||(a[0]=e=>I.name=e),placeholder:"请输入模板名称",clearable:""},null,8,["modelValue"])])),_:1}),p(l,null,{default:u((()=>[p(o,{type:"primary",icon:c(w),onClick:N},{default:u((()=>a[3]||(a[3]=[m("搜索")]))),_:1},8,["icon"]),p(o,{icon:c(b),onClick:D},{default:u((()=>a[4]||(a[4]=[m("重置")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"]),p(M,{gutter:10,class:"mb-2"},{default:u((()=>[p(J,{span:1.5},{default:u((()=>[p(o,{type:"primary",icon:c(C),onClick:E},{default:u((()=>a[5]||(a[5]=[m("新建模板")]))),_:1},8,["icon"])])),_:1})])),_:1}),f((r(),_(Y,{data:V.value,border:""},{default:u((()=>[p(P,{label:"ID",prop:"id",width:"80",align:"center"}),p(P,{label:"模板名称",prop:"name","min-width":"200","show-overflow-tooltip":""}),p(P,{label:"描述",prop:"description","min-width":"250","show-overflow-tooltip":""}),p(P,{label:"是否默认",prop:"is_default",width:"100",align:"center"},{default:u((({row:e})=>[p(R,{type:e.is_default?"success":"info"},{default:u((()=>[m(g(e.is_default?"是":"否"),1)])),_:2},1032,["type"])])),_:1}),p(P,{label:"创建时间",prop:"created_at",width:"180",align:"center"},{default:u((({row:e})=>[h("span",null,g(c(Q)(e.created_at)),1)])),_:1}),p(P,{label:"操作",width:"280",align:"center",fixed:"right"},{default:u((({row:e})=>[p(o,{type:"primary",link:"",icon:c(k),onClick:a=>(e=>{T.push({name:"QuotationTemplateEdit",params:{id:e.id}})})(e)},{default:u((()=>a[6]||(a[6]=[m("编辑")]))),_:2},1032,["icon","onClick"]),p(o,{type:"success",link:"",icon:c(j),onClick:a=>(e=>{T.push({name:"QuotationNew",query:{template_id:e.id}}),v.info(`将基于模板 "${e.name}" 创建报价单，请完善其他信息。`)})(e)},{default:u((()=>a[7]||(a[7]=[m("创建报价单")]))),_:2},1032,["icon","onClick"]),p(X,{title:"确定删除此模板吗？",onConfirm:a=>(async e=>{try{await q(e),v.success("删除成功"),L()}catch(a){console.error("Failed to delete quotation template:",a),v.error("删除失败")}})(e.id)},{reference:u((()=>[p(o,{type:"danger",link:"",icon:c(z)},{default:u((()=>a[8]||(a[8]=[m("删除")]))),_:1},8,["icon"])])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])),[[A,S.value]]),F.value>0?(r(),_(Z,{key:0,"current-page":I.page,"onUpdate:currentPage":a[1]||(a[1]=e=>I.page=e),"page-size":I.per_page,"onUpdate:pageSize":a[2]||(a[2]=e=>I.per_page=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:F.value,onSizeChange:L,onCurrentChange:L,class:"mt-4 justify-end"},null,8,["current-page","page-size","total"])):y("",!0)])),_:1})])}}}),[["__scopeId","data-v-d560cc67"]]);export{S as default};
