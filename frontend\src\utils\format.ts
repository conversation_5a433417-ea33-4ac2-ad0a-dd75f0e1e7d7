// 格式化货币
export const formatCurrency = (amount: number, currency = 'CNY'): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

// 格式化日期时间
export const formatDateTime = (dateStr: string | Date): string => {
  if (!dateStr) return 'N/A'

  const date = new Date(dateStr)
  if (isNaN(date.getTime())) return 'N/A'

  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  }).format(date)
}

// 格式化日期
export const formatDate = (dateStr: string | Date): string => {
  if (!dateStr) return 'N/A'

  const date = new Date(dateStr)
  if (isNaN(date.getTime())) return 'N/A'

  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(date)
}

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// 格式化手机号
export const formatPhone = (phone: string): string => {
  if (!phone) return ''

  // 移除所有非数字字符
  const cleaned = phone.replace(/\D/g, '')

  // 格式化为 xxx-xxxx-xxxx
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3')
  }

  return phone
}

// 截断文本
export const truncateText = (text: string, maxLength: number): string => {
  if (!text) return ''

  if (text.length <= maxLength) return text

  return `${text.substring(0, maxLength)}...`
}

// 格式化百分比
export const formatPercentage = (value: number, decimals = 2): string => {
  return `${(value * 100).toFixed(decimals)}%`
}

// 格式化数字
export const formatNumber = (value: number, decimals = 0): string => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value)
}
