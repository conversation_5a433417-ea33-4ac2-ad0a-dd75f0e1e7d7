# EMB项目前端重构产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目背景
EMB项目是一个工程物资报价及订单管理系统，原项目由不懂编程的人使用AI工具创建，代码质量和架构存在问题。现需要对前端进行全面重构，保持与原项目UI界面的一致性，同时对接已重构完成的后端API。

### 1.2 项目目标
- 重构前端代码，提升代码质量和可维护性
- **严格保持原项目UI界面设计和用户体验**
- 对接新的后端API (http://localhost:5001/api/v1)
- 采用现代化前端技术栈
- 确保系统稳定性和性能

### 1.3 重构原则
- **⚠️ 重要：每次重构前必须仔细阅读原项目相关页面的代码**
- 保持原有的页面布局、组件结构和交互逻辑
- 保持原有的路由结构和导航菜单
- 保持原有的表单字段和验证规则
- 保持原有的表格列配置和操作按钮
- 只更新API调用部分以适配新后端

### 1.3 技术架构
- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **图表库**: ECharts
- **构建工具**: Vite (保持与原项目一致)
- **样式预处理**: SCSS
- **开发服务器**: Vite Dev Server (端口3001)
- **API代理**: 代理到后端服务 (http://127.0.0.1:5001)

## 2. 系统架构设计

### 2.1 目录结构
```
frontend/
├── src/
│   ├── api/           # API接口定义
│   ├── assets/        # 静态资源
│   ├── components/    # 公共组件
│   ├── layouts/       # 布局组件
│   ├── router/        # 路由配置
│   ├── stores/        # Pinia状态管理
│   ├── types/         # TypeScript类型定义
│   ├── utils/         # 工具函数
│   ├── views/         # 页面组件
│   ├── App.vue        # 根组件
│   └── main.ts        # 入口文件
├── public/            # 公共资源
└── package.json       # 依赖配置
```

### 2.2 Vite配置
```javascript
// vite.config.js (保持与原项目一致)
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: { '@': path.resolve(__dirname, 'src') }
  },
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:5001',
        changeOrigin: true
      }
    }
  }
})
```

### 2.3 API对接规范
- **基础URL**: http://localhost:5001/api/v1
- **代理配置**: 通过Vite代理 `/api` 到后端服务
- **认证方式**: 暂无（后续可扩展JWT）
- **数据格式**: JSON
- **错误处理**: 统一错误拦截和提示

## 3. 功能模块设计

> **⚠️ 重构前必读原项目代码**
>
> 在重构每个模块前，开发人员必须：
> 1. 仔细阅读原项目对应页面的Vue组件代码
> 2. 分析原有的数据结构、表单字段、表格列配置
> 3. 理解原有的用户交互流程和业务逻辑
> 4. 保持原有的UI布局和样式设计
> 5. 只更新API调用部分以适配新后端

### 3.1 工作台/仪表板模块

#### 3.1.1 页面列表
- 工作台首页 (`/`)

#### 3.1.2 功能描述
- 显示系统概览数据统计
- 展示销售趋势图表
- 显示产品分类占比图
- 待处理事项列表
- 快捷操作入口
- 用户信息和天气显示

#### 3.1.3 API对接
- `GET /api/v1/dashboard/stats` - 获取统计数据
- `GET /api/v1/dashboard/charts/sales-trends` - 销售趋势数据
- `GET /api/v1/dashboard/charts/product-distribution` - 产品分布数据

#### 3.1.4 UI组件
- 统计卡片组件
- ECharts图表组件
- 待办事项列表
- 快捷操作按钮组

### 3.2 客户管理模块

#### 3.2.1 页面列表
- 客户列表页 (`/customers`)
- 客户详情页 (`/customers/:id`)
- 客户编辑页 (`/customers/:id/edit`)
- 新增客户页 (`/customers/new`)
- 银行账户管理页
- 送货地址管理页

#### 3.2.2 功能描述
- 客户信息CRUD操作
- 客户银行账户管理
- 客户送货地址管理
- 客户订单历史查看
- 客户搜索和筛选

#### 3.2.3 API对接
- `GET /api/v1/customers` - 获取客户列表
- `POST /api/v1/customers` - 创建客户
- `GET /api/v1/customers/:id` - 获取客户详情
- `PUT /api/v1/customers/:id` - 更新客户
- `DELETE /api/v1/customers/:id` - 删除客户
- `GET /api/v1/customers/:id/bank-accounts` - 获取银行账户
- `GET /api/v1/customers/:id/delivery-addresses` - 获取送货地址

### 3.3 产品管理模块

#### 3.3.1 页面列表
- 产品列表页 (`/products`)
- 产品详情页 (`/products/:id`)
- 产品编辑页 (`/products/:id/edit`)
- 新增产品页 (`/products/new`)
- 产品分类管理页 (`/product-categories`)
- 品牌管理页 (`/brands`)

#### 3.3.2 功能描述
- 产品信息CRUD操作
- 产品图片上传和管理
- 产品分类管理
- 品牌管理
- 产品搜索和筛选
- 批量操作功能

#### 3.3.3 API对接
- `GET /api/v1/products` - 获取产品列表
- `POST /api/v1/products` - 创建产品
- `GET /api/v1/products/:id` - 获取产品详情
- `PUT /api/v1/products/:id` - 更新产品
- `DELETE /api/v1/products/:id` - 删除产品
- `GET /api/v1/products/categories` - 获取分类列表
- `GET /api/v1/products/brands` - 获取品牌列表

### 3.4 报价管理模块

#### 3.4.1 页面列表
- 报价单列表页 (`/quotations`)
- 报价单详情页 (`/quotations/:id`)
- 报价单编辑页 (`/quotations/:id/edit`)
- 新增报价单页 (`/quotations/new`)
- 报价需求表列表页 (`/quotation-requests`)
- 报价模板管理页 (`/quotation-templates`)

#### 3.4.2 功能描述
- 报价单CRUD操作
- 报价单状态管理
- 报价单打印和导出
- 报价模板管理
- 报价需求表管理
- 报价单审批流程

#### 3.4.3 API对接
- `GET /api/v1/quotations` - 获取报价单列表
- `POST /api/v1/quotations` - 创建报价单
- `GET /api/v1/quotations/:id` - 获取报价单详情
- `PUT /api/v1/quotations/:id` - 更新报价单
- `PUT /api/v1/quotations/:id/status` - 更新状态

### 3.5 订单管理模块

#### 3.5.1 页面列表
- 订单列表页 (`/orders`)
- 订单详情页 (`/orders/:id`)
- 订单编辑页 (`/orders/:id/edit`)
- 新增订单页 (`/orders/new`)
- **发货单管理页 (`/delivery`)** - 包含发货单CRUD和物流跟踪
- 发货单详情页 (`/delivery/:id`)
- 发货单编辑页 (`/delivery/:id/edit`)
- 新增发货单页 (`/delivery/new`)
- 退货管理页 (`/return-orders`)
- 退货详情页 (`/return-orders/:id`)
- 退货编辑页 (`/return-orders/:id/edit`)
- 对账单管理页 (`/statements`)
- 对账单详情页 (`/statements/:id`)
- 对账单编辑页 (`/statements/:id/edit`)

#### 3.5.2 功能描述
- 订单CRUD操作
- 订单状态流转管理
- **发货单生成和管理（从独立模块合并）**
- **发货单物流跟踪功能**
- 退货单处理
- 对账单生成和确认
- 订单打印和导出
- 发货单打印和导出

#### 3.5.3 API对接
- `GET /api/v1/orders` - 获取订单列表
- `POST /api/v1/orders` - 创建订单
- `GET /api/v1/orders/:id` - 获取订单详情
- `PUT /api/v1/orders/:id` - 更新订单
- `PUT /api/v1/orders/:id/status` - 更新订单状态
- **`GET /api/v1/delivery-notes` - 获取发货单列表**
- **`POST /api/v1/delivery-notes` - 创建发货单**
- **`GET /api/v1/delivery-notes/:id` - 获取发货单详情**
- **`PUT /api/v1/delivery-notes/:id` - 更新发货单**
- `GET /api/v1/returns` - 获取退货单列表
- `POST /api/v1/returns` - 创建退货单
- `GET /api/v1/statements` - 获取对账单列表
- `POST /api/v1/statements` - 创建对账单

### 3.6 财务管理模块

#### 3.6.1 页面列表
- 收款记录页 (`/payment-records`)
- 退款记录页 (`/refund-record`)
- 应收款项页 (`/receivables`)
- 财务报表页 (`/financial-reports`)

#### 3.6.2 功能描述
- 收款记录管理
- 退款记录管理
- 应收款项统计
- 财务报表生成
- 收款确认流程
- 退款处理流程

#### 3.6.3 API对接
- `GET /api/v1/finance/payments` - 获取收款记录
- `POST /api/v1/finance/payments` - 创建收款记录
- `GET /api/v1/finance/refunds` - 获取退款记录
- `POST /api/v1/finance/refunds` - 创建退款记录

### 3.7 系统设置模块

#### 3.7.1 页面列表
- 基本设置页 (`/settings`)
- 品牌管理页 (`/brands`)
- 数据备份页 (`/data-backup`)

#### 3.7.2 功能描述
- 企业基本信息设置
- 银行账户管理
- 文档模板管理
- 品牌信息管理
- 数据备份和恢复
- 系统参数配置

#### 3.7.3 API对接
- `GET /api/v1/system/company` - 获取企业信息
- `PUT /api/v1/system/company` - 更新企业信息
- `GET /api/v1/system/bank-accounts` - 获取银行账户
- `GET /api/v1/system/templates` - 获取文档模板

## 4. UI/UX设计要求

### 4.1 布局设计
- 采用左侧导航 + 主内容区域布局
- 支持侧边栏折叠功能
- 顶部显示面包屑导航和用户信息
- 标签页导航支持多页面切换

### 4.2 响应式设计
- 支持桌面端和平板端访问
- 断点设置：xs(<768px), sm(≥768px), md(≥992px), lg(≥1200px)
- 移动端优化导航和表格显示

### 4.3 主题和样式
- 主色调：#409EFF (Element Plus默认蓝色)
- 辅助色：成功#67C23A，警告#E6A23C，危险#F56C6C
- 字体：系统默认字体栈
- 圆角：4px统一圆角设计

### 4.4 交互设计
- 加载状态显示
- 操作反馈提示
- 确认对话框
- 表单验证提示
- 空状态页面设计

## 5. 开发计划

### 5.1 开发优先级
1. **第一阶段**：基础架构和布局组件
2. **第二阶段**：工作台和客户管理模块
3. **第三阶段**：产品管理和报价管理模块
4. **第四阶段**：订单管理和财务管理模块
5. **第五阶段**：系统设置和优化完善

### 5.2 质量保证
- TypeScript类型检查
- ESLint代码规范检查
- 组件单元测试
- E2E功能测试
- 性能优化和监控

## 6. 技术实现要点

### 6.1 状态管理
- 使用Pinia进行全局状态管理
- 模块化store设计
- 持久化存储用户设置

### 6.2 路由管理
- 路由懒加载
- 路由守卫实现权限控制
- 动态面包屑生成

### 6.3 API管理
- 统一的API请求封装
- 请求/响应拦截器
- 错误处理机制
- 请求缓存策略

### 6.4 组件设计
- 可复用的业务组件
- 统一的表单组件
- 通用的表格组件
- 文件上传组件

## 7. 重构实施指导

### 7.1 核心模块列表
1. **工作台/仪表板** - 数据统计和图表展示
2. **客户管理** - 客户CRUD、银行账户、送货地址
3. **产品管理** - 产品CRUD、分类、品牌管理
4. **报价管理** - 报价单、需求表、模板管理
5. **订单管理** - 订单、发货单、退货、对账单（合并发货管理）
6. **财务管理** - 收款、退款、应收款项
7. **系统设置** - 企业信息、银行账户、模板

### 7.2 重构前必读原项目代码
**⚠️ 每个模块重构前的必要步骤：**

1. **仔细阅读原项目对应的Vue组件文件**
   - 查看 `origin/frontend/src/views/` 下对应模块的所有页面
   - 分析组件的props、data、computed、methods
   - 理解组件间的父子关系和数据传递

2. **分析原有的数据结构和API调用**
   - 查看 `origin/frontend/src/api/` 下对应的API文件
   - 理解原有的请求参数和响应数据格式
   - 分析数据在组件中的使用方式

3. **保持原有的UI布局和交互**
   - 保持相同的表单字段和验证规则
   - 保持相同的表格列配置和操作按钮
   - 保持相同的弹窗、对话框和提示信息
   - 保持相同的路由结构和页面跳转逻辑

4. **只更新API对接部分**
   - 将API调用从原有格式改为新后端API格式
   - 适配新的请求参数和响应数据结构
   - 保持业务逻辑不变

### 7.3 技术栈确认
- **构建工具**: Vite (与原项目保持一致)
- **开发服务器**: 端口3001，代理API到5001端口
- **其他技术栈**: 完全沿用原项目配置

这个PRD文档为前端重构提供了详细的指导，确保重构后的系统能够与新的后端API完美对接，同时严格保持原有的用户体验和界面设计。
