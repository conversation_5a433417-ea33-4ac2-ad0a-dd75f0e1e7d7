"""
错误日志模型
"""
from app import db
from datetime import datetime
from sqlalchemy.dialects.sqlite import JSON
from app.models.base import BaseModel


class ErrorLog(BaseModel):
    """错误日志模型"""
    __tablename__ = 'error_logs'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    # 兼容旧版本的level字段
    level = db.Column(db.String(20), nullable=True, comment='日志级别(兼容字段)')
    # 新版本使用log_type字段
    log_type = db.Column(db.String(50), nullable=True, index=True, comment='日志类型')
    message = db.Column(db.Text, nullable=False, comment='错误消息')
    # 旧版本字段
    details = db.Column(db.Text, nullable=True, comment='错误详情(兼容字段)')
    # 新版本用这个字段
    stack_trace = db.Column(db.Text, nullable=True, comment='堆栈跟踪')
    # 额外信息
    additional_info = db.Column(JSON, nullable=True, comment='额外信息')
    # 旧版本字段
    origin = db.Column(db.String(100), nullable=True, comment='来源')
    user_agent = db.Column(db.String(255), nullable=True, comment='用户代理')
    ip_address = db.Column(db.String(50), nullable=True, comment='IP地址')
    
    def __repr__(self):
        return f'<ErrorLog {self.id} {self.log_type or self.level}>'

    def to_dict(self):
        return {
            'id': self.id,
            'log_type': self.log_type or self.level,
            'message': self.message,
            'stack_trace': self.stack_trace or self.details,
            'additional_info': self.additional_info,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }
