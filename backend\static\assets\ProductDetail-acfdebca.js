import{J as e,u as a,b as t,r as l,e as i,f as s,o as r,c as u,i as n,h as c,a as o,k as d,t as p,g as f,j as _,F as m,m as v,U as b,l as g,ab as y}from"./index-3d4c440c.js";import{a as h}from"./product-ab10366a.js";import{_ as w}from"./_plugin-vue_export-helper-1b428a4d.js";const x={class:"product-detail"},k={class:"flex-between"},A={class:"specification-summary mb-20"},j={class:"specification-cell"},F={class:"price-cell"},N={class:"price-range-cell"},P={class:"price-cell suggested"},z={class:"price-cell tax-included"},I={key:0,class:"product-images"},V={class:"image-slot"},$={class:"image-info"},C=w(e({__name:"ProductDetail",setup(e,{expose:w}){const C=a(),D=t(),E=l("basic"),M=l({id:null,name:"",model:"",unit:"",category_id:null,category_name:"",description:"",notes:"",status:"active",attributes:[],images:[],specifications:[]});function S(e){return null==e?"0.00":Number(e).toFixed(2)}function T(e){return e?"active"===e||"正常"===e?"success":"inactive"===e||"停用"===e?"danger":"info":"info"}function U(e){return e?"active"===e||"正常"===e?"正常":"inactive"===e||"停用"===e?"停用":e:"未知"}const J=()=>{D.back()},L=()=>{D.push({name:"ProductEdit",params:{id:M.value.id}})};function R(e){var a;if(!(null==(a=M.value.specifications)?void 0:a.length))return"0.00";const t=M.value.specifications.map((a=>Number(a[e])||0)).filter((e=>e>0));if(!t.length)return"0.00";const l=Math.min(...t),i=Math.max(...t);return l===i?l.toFixed(2):`${l.toFixed(2)} - ${i.toFixed(2)}`}function q(e){return(Number(e.suggested_price)||0)*(1+(Number(e.tax_rate)||0)/100)}return i((()=>{(async()=>{const e=C.params.id;if(!e)return b.error("无效的产品ID"),void D.push({name:"ProductList"});try{const a=await h(parseInt(e,10));console.log("原始产品响应:",a);const t=function(e){return e?e&&"object"==typeof e&&void 0!==e.data?e.data:e:null}(a);if(console.log("处理后的产品数据:",t),!t)throw new Error("未获取到产品数据");Array.isArray(t.attributes)||(t.attributes=[]),Array.isArray(t.specifications)||(t.specifications=[]),Array.isArray(t.images)||(t.images=[]),M.value=t}catch(a){console.error("获取产品详情失败:",a),b.error("获取产品详情失败")}})()})),w({formatPrice:S,getStatusType:T,getStatusText:U}),(e,a)=>{const t=s("el-button"),l=s("el-card"),i=s("el-descriptions-item"),b=s("el-tag"),h=s("el-descriptions"),w=s("el-tab-pane"),C=s("el-statistic"),D=s("el-col"),B=s("el-row"),G=s("el-table-column"),H=s("el-divider"),K=s("el-tooltip"),O=s("el-table"),Q=s("el-empty"),W=s("el-icon"),X=s("el-image"),Y=s("el-tabs");return r(),u("div",x,[n(l,{class:"header-card mb-20"},{default:c((()=>[o("div",k,[a[3]||(a[3]=o("h2",{class:"form-title"},"产品详情",-1)),o("div",null,[n(t,{onClick:J},{default:c((()=>a[1]||(a[1]=[d("返回")]))),_:1}),n(t,{type:"primary",onClick:L},{default:c((()=>a[2]||(a[2]=[d("编辑")]))),_:1})])])])),_:1}),n(Y,{modelValue:E.value,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value=e)},{default:c((()=>[n(w,{label:"基本信息",name:"basic"},{default:c((()=>[n(l,null,{default:c((()=>[n(h,{column:2,border:""},{default:c((()=>[n(i,{label:"产品名称"},{default:c((()=>[d(p(M.value.name),1)])),_:1}),n(i,{label:"产品型号"},{default:c((()=>[d(p(M.value.model),1)])),_:1}),n(i,{label:"计量单位"},{default:c((()=>[d(p(M.value.unit),1)])),_:1}),n(i,{label:"产品分类"},{default:c((()=>[d(p(M.value.category_name),1)])),_:1}),n(i,{label:"状态"},{default:c((()=>[n(b,{type:T(M.value.status)},{default:c((()=>[d(p(U(M.value.status)),1)])),_:1},8,["type"])])),_:1}),n(i,{label:"产品描述",span:2},{default:c((()=>[d(p(M.value.description||"暂无描述"),1)])),_:1}),n(i,{label:"备注",span:2},{default:c((()=>[d(p(M.value.notes||"暂无备注"),1)])),_:1})])),_:1})])),_:1})])),_:1}),n(w,{label:"规格与价格",name:"specifications"},{default:c((()=>[n(l,null,{default:c((()=>[o("div",A,[n(B,{gutter:20},{default:c((()=>[n(D,{span:6},{default:c((()=>{var e;return[n(C,{title:"规格数量",value:(null==(e=M.value.specifications)?void 0:e.length)||0},{suffix:c((()=>a[4]||(a[4]=[o("span",{class:"text-sm text-gray"},"个",-1)]))),_:1},8,["value"])]})),_:1}),n(D,{span:6},{default:c((()=>[n(C,{title:"最低售价区间",value:R("min_price"),precision:2},{prefix:c((()=>a[5]||(a[5]=[d("¥")]))),_:1},8,["value"])])),_:1}),n(D,{span:6},{default:c((()=>[n(C,{title:"建议售价区间",value:R("suggested_price"),precision:2},{prefix:c((()=>a[6]||(a[6]=[d("¥")]))),_:1},8,["value"])])),_:1}),n(D,{span:6},{default:c((()=>[n(C,{title:"最高售价区间",value:R("max_price"),precision:2},{prefix:c((()=>a[7]||(a[7]=[d("¥")]))),_:1},8,["value"])])),_:1})])),_:1})]),M.value.specifications&&M.value.specifications.length>0?(r(),f(O,{key:0,data:M.value.specifications,style:{width:"100%"},border:"",stripe:""},{default:c((()=>[n(G,{prop:"specification",label:"规格名称","min-width":"180"},{default:c((({row:e})=>[o("div",j,[o("span",null,p(e.specification),1),e.is_default?(r(),f(b,{key:0,type:"success",size:"small",effect:"dark",class:"ml-10"},{default:c((()=>a[8]||(a[8]=[d("默认")]))),_:1})):_("",!0)])])),_:1}),n(G,{prop:"cost_price",label:"成本价",width:"120"},{default:c((({row:e})=>[o("span",F,"¥"+p(S(e.cost_price)),1)])),_:1}),n(G,{label:"销售价格区间",width:"200"},{default:c((({row:e})=>[o("div",N,[o("span",null,"¥"+p(S(e.min_price)),1),n(H,{direction:"vertical"}),o("span",null,"¥"+p(S(e.max_price)),1)])])),_:1}),n(G,{prop:"suggested_price",label:"建议售价",width:"120"},{default:c((({row:e})=>[o("span",P,"¥"+p(S(e.suggested_price)),1)])),_:1}),n(G,{prop:"tax_rate",label:"税率",width:"100"},{default:c((({row:e})=>{return[n(b,{type:(a=e.tax_rate,a?a<=6?"success":a<=13?"warning":"danger":"info"),size:"small"},{default:c((()=>[d(p(e.tax_rate||0)+"%",1)])),_:2},1032,["type"])];var a})),_:1}),n(G,{label:"含税价",width:"120"},{default:c((({row:e})=>[n(K,{content:`不含税价：¥${S(e.suggested_price)}`,placement:"top"},{default:c((()=>[o("span",z," ¥"+p(S(q(e))),1)])),_:2},1032,["content"])])),_:1})])),_:1},8,["data"])):(r(),f(Q,{key:1,description:"暂无规格信息"}))])),_:1})])),_:1}),n(w,{label:"产品属性",name:"attributes"},{default:c((()=>[n(l,null,{default:c((()=>[M.value.attributes&&M.value.attributes.length>0?(r(),f(O,{key:0,data:M.value.attributes,style:{width:"100%"},border:"",stripe:""},{default:c((()=>[n(G,{prop:"attribute_name",label:"属性名称",width:"180"}),n(G,{prop:"attribute_value",label:"属性值",width:"180"}),n(G,{label:"排序",width:"80"},{default:c((({row:e})=>[d(p(e.sort_order||"-"),1)])),_:1}),n(G,{label:"备注"},{default:c((({row:e})=>[d(p(e.notes||"-"),1)])),_:1})])),_:1},8,["data"])):(r(),f(Q,{key:1,description:"暂无产品属性"}))])),_:1})])),_:1}),n(w,{label:"产品图片",name:"images"},{default:c((()=>[n(l,null,{default:c((()=>[M.value.images&&M.value.images.length>0?(r(),u("div",I,[(r(!0),u(m,null,v(M.value.images,(e=>(r(),u("div",{key:e.id||e.url,class:"image-item"},[n(X,{src:e.url,fit:"cover","preview-src-list":M.value.images.map((e=>e.url))},{error:c((()=>[o("div",V,[n(W,null,{default:c((()=>[n(g(y))])),_:1})])])),_:2},1032,["src","preview-src-list"]),o("div",$,[e.is_main?(r(),f(b,{key:0,type:"success",size:"small"},{default:c((()=>a[9]||(a[9]=[d("主图")]))),_:1})):_("",!0)])])))),128))])):(r(),f(Q,{key:1,description:"暂无产品图片"}))])),_:1})])),_:1})])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-bfb1f9e4"]]);export{C as default};
