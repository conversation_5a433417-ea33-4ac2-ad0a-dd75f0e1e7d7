import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { generateBreadcrumbs, getPageTitle } from '@/utils/breadcrumb'
import type { BreadcrumbItem } from '@/types/router'

/**
 * 导航相关的组合函数
 */
export function useNavigation() {
  const route = useRoute()
  const router = useRouter()

  // 当前路由信息
  const currentRoute = computed(() => route)

  // 面包屑数据
  const breadcrumbs = computed<BreadcrumbItem[]>(() => {
    return generateBreadcrumbs(route.matched)
  })

  // 页面标题
  const pageTitle = computed(() => {
    return getPageTitle(route)
  })

  // 当前激活的菜单
  const activeMenu = computed(() => {
    const { meta, path } = route
    if (meta.activeMenu) {
      return meta.activeMenu as string
    }
    return path
  })

  // 导航方法
  const navigateTo = (
    path: string,
    options?: { replace?: boolean; query?: Record<string, any> }
  ) => {
    const { replace = false, query } = options || {}

    if (replace) {
      router.replace({ path, query })
    } else {
      router.push({ path, query })
    }
  }

  // 返回上一页
  const goBack = () => {
    router.back()
  }

  // 前进到下一页
  const goForward = () => {
    router.forward()
  }

  // 刷新当前页面
  const refresh = () => {
    router.go(0)
  }

  // 检查是否为当前路由
  const isCurrentRoute = (path: string) => {
    return route.path === path
  }

  // 检查是否为激活的菜单项
  const isActiveMenu = (path: string) => {
    return activeMenu.value === path || route.path.startsWith(path)
  }

  // 获取路由参数
  const getRouteParam = (key: string) => {
    return route.params[key] as string
  }

  // 获取查询参数
  const getQueryParam = (key: string) => {
    return route.query[key] as string
  }

  // 设置查询参数
  const setQueryParams = (params: Record<string, any>, options?: { replace?: boolean }) => {
    const { replace = false } = options || {}

    if (replace) {
      router.replace({ query: { ...route.query, ...params } })
    } else {
      router.push({ query: { ...route.query, ...params } })
    }
  }

  // 清除查询参数
  const clearQueryParams = (keys?: string[]) => {
    if (keys) {
      const newQuery = { ...route.query }
      keys.forEach(key => delete newQuery[key])
      router.replace({ query: newQuery })
    } else {
      router.replace({ query: {} })
    }
  }

  return {
    // 响应式数据
    currentRoute,
    breadcrumbs,
    pageTitle,
    activeMenu,

    // 导航方法
    navigateTo,
    goBack,
    goForward,
    refresh,

    // 检查方法
    isCurrentRoute,
    isActiveMenu,

    // 参数方法
    getRouteParam,
    getQueryParam,
    setQueryParams,
    clearQueryParams,
  }
}
