"""
系统设置相关模型
包括系统设置、企业信息、企业银行账户、单据模板、备份记录等
"""
from app import db
from app.models.base import BaseModel


class SystemSetting(BaseModel):
    """系统设置表模型"""
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    key = db.Column(db.String(100), nullable=False, unique=True, comment='设置键')
    value = db.Column(db.Text, nullable=False, comment='设置值')
    description = db.Column(db.String(255), nullable=True, comment='设置描述')
    category = db.Column(db.String(50), nullable=False, default='general', comment='设置分类')
    data_type = db.Column(db.String(20), nullable=False, default='string', comment='数据类型')
    is_public = db.Column(db.<PERSON>, nullable=False, default=False, comment='是否公开')

    def __repr__(self) -> str:
        return f"<SystemSetting {self.key}: {self.value}>"


class CompanyInfo(BaseModel):
    """企业信息表模型"""
    __tablename__ = 'company_info'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    company_name = db.Column(db.String(200), nullable=False, comment='企业名称')
    legal_representative = db.Column(db.String(100), nullable=True, comment='法定代表人')
    registered_address = db.Column(db.String(500), nullable=True, comment='注册地址')
    office_address = db.Column(db.String(500), nullable=True, comment='办公地址')
    contact_phone = db.Column(db.String(50), nullable=True, comment='联系电话')
    contact_email = db.Column(db.String(100), nullable=True, comment='联系邮箱')
    tax_id = db.Column(db.String(50), nullable=True, comment='统一社会信用代码')
    business_license = db.Column(db.String(50), nullable=True, comment='营业执照号')
    website = db.Column(db.String(200), nullable=True, comment='企业网站')
    logo_url = db.Column(db.String(500), nullable=True, comment='企业Logo URL')
    description = db.Column(db.Text, nullable=True, comment='企业描述')

    def __repr__(self) -> str:
        return f"<CompanyInfo {self.company_name}>"


class CompanyBankAccount(BaseModel):
    """企业银行账户表模型"""
    __tablename__ = 'company_bank_accounts'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    bank_name = db.Column(db.String(100), nullable=False, comment='银行名称')
    account_name = db.Column(db.String(100), nullable=False, comment='账户名称')
    account_number = db.Column(db.String(50), nullable=False, comment='账号')
    account_type = db.Column(db.String(20), nullable=True, default='对公账户', comment='账户类型')
    is_default = db.Column(db.Boolean, nullable=False, default=False, comment='是否默认账户')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    def __repr__(self) -> str:
        return f"<CompanyBankAccount {self.account_name} - {self.account_number}>"


class DocumentTemplate(BaseModel):
    """单据模板表模型"""
    __tablename__ = 'document_templates'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False, comment='模板名称')
    type = db.Column(db.String(50), nullable=False, comment='模板类型')
    content = db.Column(db.Text, nullable=False, comment='模板内容')
    is_default = db.Column(db.Boolean, nullable=False, default=False, comment='是否默认模板')
    description = db.Column(db.String(255), nullable=True, comment='模板描述')

    def __repr__(self) -> str:
        return f"<DocumentTemplate {self.name} ({self.type})>"


class BackupRecord(BaseModel):
    """备份记录表模型"""
    __tablename__ = 'backup_records'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    backup_name = db.Column(db.String(200), nullable=False, comment='备份名称')
    backup_type = db.Column(db.String(20), nullable=False, default='manual', comment='备份类型')
    file_path = db.Column(db.String(500), nullable=True, comment='文件路径')
    file_size = db.Column(db.BigInteger, nullable=True, comment='文件大小(字节)')
    status = db.Column(db.String(20), nullable=False, default='pending', comment='备份状态')
    error_message = db.Column(db.Text, nullable=True, comment='错误信息')
    started_at = db.Column(db.DateTime, nullable=True, comment='开始时间')
    completed_at = db.Column(db.DateTime, nullable=True, comment='完成时间')

    def __repr__(self) -> str:
        return f"<BackupRecord {self.backup_name} ({self.status})>"
