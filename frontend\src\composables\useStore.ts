import { useUserStore, useAppStore, useCacheStore } from '@/stores'

/**
 * 统一的store组合函数
 * 提供便捷的store访问方式
 */
export function useStore() {
  const userStore = useUserStore()
  const appStore = useAppStore()
  const cacheStore = useCacheStore()

  return {
    user: userStore,
    app: appStore,
    cache: cacheStore,
  }
}

/**
 * 用户相关的组合函数
 */
export function useUser() {
  const userStore = useUserStore()

  return {
    // 状态
    token: userStore.token,
    userInfo: userStore.userInfo,
    isLoggedIn: userStore.isLoggedIn,
    
    // 计算属性
    hasToken: userStore.hasToken,
    userName: userStore.userName,
    userRoles: userStore.userRoles,
    userPermissions: userStore.userPermissions,
    userAvatar: userStore.userAvatar,
    hasPermission: userStore.hasPermission,
    hasRole: userStore.hasRole,
    
    // 方法
    login: userStore.login,
    logout: userStore.logout,
    refreshToken: userStore.refreshUserToken,
    fetchUserInfo: userStore.fetchUserInfo,
    updateUserInfo: userStore.updateUserInfo,
    initUserState: userStore.initUserState,
  }
}

/**
 * 应用设置相关的组合函数
 */
export function useApp() {
  const appStore = useAppStore()

  return {
    // 状态
    settings: appStore.settings,
    loading: appStore.loading,
    device: appStore.device,
    
    // 计算属性
    isDark: appStore.isDark,
    sidebarWidth: appStore.sidebarWidth,
    isMobile: appStore.isMobile,
    isTablet: appStore.isTablet,
    isDesktop: appStore.isDesktop,
    
    // 方法
    toggleSidebar: appStore.toggleSidebar,
    setSidebarCollapsed: appStore.setSidebarCollapsed,
    toggleTheme: appStore.toggleTheme,
    setTheme: appStore.setTheme,
    setPrimaryColor: appStore.setPrimaryColor,
    setLanguage: appStore.setLanguage,
    setLayout: appStore.setLayout,
    setDevice: appStore.setDevice,
    setLoading: appStore.setLoading,
    updateSettings: appStore.updateSettings,
    resetSettings: appStore.resetSettings,
    initAppSettings: appStore.initAppSettings,
  }
}

/**
 * 缓存相关的组合函数
 */
export function useCache() {
  const cacheStore = useCacheStore()

  return {
    // 状态
    config: cacheStore.config,
    cacheSize: cacheStore.cacheSize,
    cacheKeys: cacheStore.cacheKeys,
    
    // 基础方法
    set: cacheStore.set,
    get: cacheStore.get,
    has: cacheStore.has,
    remove: cacheStore.remove,
    clear: cacheStore.clear,
    clearExpired: cacheStore.clearExpired,
    
    // 专用缓存方法
    cacheApiResponse: cacheStore.cacheApiResponse,
    getCachedApiResponse: cacheStore.getCachedApiResponse,
    cacheUserData: cacheStore.cacheUserData,
    getCachedUserData: cacheStore.getCachedUserData,
    cacheListData: cacheStore.cacheListData,
    getCachedListData: cacheStore.getCachedListData,
    
    // 清理方法
    clearByPattern: cacheStore.clearByPattern,
    startAutoCleanup: cacheStore.startAutoCleanup,
    
    // 信息方法
    getInfo: cacheStore.getInfo,
    getAllInfo: cacheStore.getAllInfo,
  }
}
