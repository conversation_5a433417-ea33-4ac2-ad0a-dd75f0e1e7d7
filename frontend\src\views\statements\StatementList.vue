<template>
  <div class="statement-list">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="客户">
          <el-select
            v-model="searchForm.customer_id"
            filterable
            remote
            :remote-method="handleSearchCustomers"
            placeholder="请输入客户名称"
            clearable
          >
            <el-option
              v-for="item in customerOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待确认" value="待确认" />
            <el-option label="已确认" value="已确认" />
            <el-option label="部分收款" value="部分收款" />
            <el-option label="已结清" value="已结清" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="operation-card">
      <div class="operation-header">
        <div class="left-actions">
          <el-button type="primary" @click="handleCreate">新建对账单</el-button>
          <el-button type="success" :disabled="selectedStatements.length === 0" @click="handleExport">
            导出选中{{ selectedStatements.length ? `(${selectedStatements.length})` : '' }}
          </el-button>
        </div>
        <div class="right-actions">
          <!-- 卡片详细程度切换 -->
          <el-button-group>
            <el-button
              :type="cardMode === 'detailed' ? 'primary' : ''"
              @click="cardMode = 'detailed'"
              title="详细模式"
            >
              详细
            </el-button>
            <el-button
              :type="cardMode === 'simple' ? 'primary' : ''"
              @click="cardMode = 'simple'"
              title="简化模式"
            >
              简化
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 对账单列表 -->
    <el-card>
      <!-- 卡片视图 -->
      <div v-loading="loading">
        <!-- 批量选择工具栏 -->
        <div v-if="selectedStatements.length > 0" class="batch-selection-bar">
          <span>已选择 {{ selectedStatements.length }} 个对账单</span>
          <el-button size="small" @click="clearSelection">清空选择</el-button>
        </div>

        <!-- 卡片列表 -->
        <div class="statement-cards-list">
          <div
            v-for="statement in tableData"
            :key="statement.id"
            :class="['statement-card', { 'selected': isStatementSelected(statement) }]"
            @click="handleCardClick(statement)"
          >
            <!-- 选择框 -->
            <div class="card-checkbox" @click.stop>
              <el-checkbox
                :model-value="isStatementSelected(statement)"
                @change="handleCardSelection(statement, $event)"
              />
            </div>

            <!-- 详细模式卡片内容 -->
            <div v-if="cardMode === 'detailed'" class="card-content detailed">
              <!-- 第一行：对账单号、客户、状态 -->
              <div class="card-row-1">
                <div class="left-info">
                  <div class="statement-number">{{ statement.statement_number }}</div>
                  <div class="customer-status">
                    <span class="customer">{{ statement.customer_name || 'N/A' }}</span>
                    <div class="status-tags">
                      <el-tag :type="getStatusType(statement.status)" size="default" class="status-tag-prominent statement-status">
                        {{ getStatusText(statement.status) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第二行：金额和日期信息 -->
              <div class="card-row-2">
                <div class="left-details">
                  <div class="amount-details">
                    <div class="amount-row">
                      <div class="amount-group">
                        <span class="amount-label">总金额</span>
                        <span class="total-amount">{{ formatAmount(statement.total_amount) }}</span>
                      </div>
                      <div class="amount-group">
                        <span class="amount-label">已收金额</span>
                        <span class="paid-amount">{{ formatAmount(statement.paid_amount || 0) }}</span>
                      </div>
                    </div>
                    <div v-if="statement.discount_amount && statement.discount_amount > 0" class="amount-group">
                      <span class="amount-label">优惠金额</span>
                      <span class="discount-amount">-{{ formatAmount(statement.discount_amount) }}</span>
                    </div>
                    <div v-if="statement.adjusted_total_amount" class="amount-group">
                      <span class="amount-label">调整后金额</span>
                      <span class="adjusted-amount">{{ formatAmount(statement.adjusted_total_amount) }}</span>
                    </div>
                  </div>
                </div>
                <div class="right-info">
                  <div class="date-info">
                    <span class="date-label">对账日期</span>
                    <span class="statement-date">{{ formatDate(statement.statement_date) }}</span>
                  </div>
                  <div v-if="statement.due_date" class="date-info">
                    <span class="date-label">应付日期</span>
                    <span class="due-date">{{ formatDate(statement.due_date) }}</span>
                  </div>
                </div>
              </div>

              <!-- 第三行：操作按钮 -->
              <div class="card-actions detailed" @click.stop>
                <!-- 待确认状态：可以编辑、确认、删除 -->
                <template v-if="statement.status === '待确认'">
                  <el-button type="warning" size="small" @click="handleEdit(statement)" title="编辑对账单">
                    编辑
                  </el-button>
                  <el-button type="primary" size="small" @click="handleConfirm(statement)" title="确认对账单">
                    确认
                  </el-button>
                  <el-button type="danger" size="small" @click="handleDelete(statement)" title="删除对账单">
                    删除
                  </el-button>
                </template>

                <!-- 已确认状态：可以收款/退款、导出 -->
                <template v-else-if="statement.status === '已确认'">
                  <!-- 根据总金额正负数显示收款或退款按钮 -->
                  <el-button
                    v-if="Number(statement.adjusted_total_amount || statement.total_amount || 0) >= 0"
                    type="success"
                    size="small"
                    @click="handlePayment(statement)"
                    title="收款"
                  >
                    收款
                  </el-button>
                  <el-button
                    v-else
                    type="warning"
                    size="small"
                    @click="handleRefund(statement)"
                    title="退款"
                  >
                    退款
                  </el-button>
                  <el-button type="primary" size="small" @click="handleExportSingle(statement)" title="导出">
                    导出
                  </el-button>
                </template>

                <!-- 部分收款状态：可以继续收款/退款和导出 -->
                <template v-else-if="statement.status === '部分收款'">
                  <!-- 根据总金额正负数显示收款或退款按钮 -->
                  <el-button
                    v-if="Number(statement.adjusted_total_amount || statement.total_amount || 0) >= 0"
                    type="success"
                    size="small"
                    @click="handlePayment(statement)"
                    title="收款"
                  >
                    收款
                  </el-button>
                  <el-button
                    v-else
                    type="warning"
                    size="small"
                    @click="handleRefund(statement)"
                    title="退款"
                  >
                    退款
                  </el-button>
                  <el-button type="primary" size="small" @click="handleExportSingle(statement)" title="导出">
                    导出
                  </el-button>
                </template>

                <!-- 已结清状态：只能导出 -->
                <template v-else-if="statement.status === '已结清'">
                  <el-button type="primary" size="small" @click="handleExportSingle(statement)" title="导出">
                    导出
                  </el-button>
                </template>

                <!-- 已取消状态：只能导出 -->
                <template v-else-if="statement.status === '已取消'">
                  <el-button type="info" size="small" @click="handleExportSingle(statement)" title="导出">
                    导出
                  </el-button>
                </template>
              </div>
            </div>

            <!-- 简化模式卡片内容 -->
            <div v-else class="card-content simple">
              <!-- 紧凑单行信息展示 -->
              <div class="card-main simple">
                <!-- 对账单号 -->
                <div class="statement-number">{{ statement.statement_number }}</div>

                <!-- 客户 -->
                <div class="customer-info">
                  <span class="customer">{{ statement.customer_name || 'N/A' }}</span>
                </div>

                <!-- 金额信息 -->
                <div class="amount-info">
                  <span class="total-amount">{{ formatAmount(statement.total_amount) }}</span>
                  <span class="paid-separator">/</span>
                  <span class="paid-amount">{{ formatAmount(statement.paid_amount || 0) }}</span>
                </div>

                <!-- 日期信息 -->
                <div class="date-info">
                  <span class="statement-date">{{ formatDate(statement.statement_date) }}</span>
                </div>

                <!-- 状态 -->
                <div class="status-tags">
                  <el-tag :type="getStatusType(statement.status)" size="default" class="status-tag-prominent statement-status">
                    {{ getStatusText(statement.status) }}
                  </el-tag>
                </div>

                <!-- 操作按钮 -->
                <div class="card-actions simple" @click.stop>
                  <!-- 根据状态显示最重要的操作 -->
                  <template v-if="statement.status === '待确认'">
                    <el-button type="primary" size="small" @click="handleConfirm(statement)" title="确认对账单">
                      确认
                    </el-button>
                    <el-button type="warning" size="small" @click="handleEdit(statement)" title="编辑对账单">
                      编辑
                    </el-button>
                  </template>
                  <template v-else-if="statement.status === '已确认' || statement.status === '部分收款'">
                    <!-- 根据总金额正负数显示收款或退款按钮 -->
                    <el-button
                      v-if="Number(statement.adjusted_total_amount || statement.total_amount || 0) >= 0"
                      type="success"
                      size="small"
                      @click="handlePayment(statement)"
                      title="收款"
                    >
                      收款
                    </el-button>
                    <el-button
                      v-else
                      type="warning"
                      size="small"
                      @click="handleRefund(statement)"
                      title="退款"
                    >
                      退款
                    </el-button>
                  </template>
                  <template v-else>
                    <el-button type="primary" size="small" @click="handleExportSingle(statement)" title="导出">
                      导出
                    </el-button>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空数据显示 -->
        <div v-if="tableData.length === 0 && !loading" class="empty-placeholder">
          <el-empty description="暂无对账单数据">
            <el-button type="primary" @click="handleRefresh">刷新</el-button>
          </el-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 确认对账单对话框 -->
    <el-dialog
      v-model="confirmDialogVisible"
      title="确认对账单"
      width="500px"
    >
      <el-form
        ref="confirmFormRef"
        :model="confirmForm"
        :rules="confirmRules"
        label-width="100px"
      >
        <el-form-item label="应付款日期" prop="due_date">
          <el-date-picker
            v-model="confirmForm.due_date"
            type="date"
            placeholder="请选择应付款日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="confirmDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 收款对话框 -->
    <StatementPaymentDialog
      v-model="paymentDialogVisible"
      :statement-id="Number(selectedStatement?.id || 0)"
      :statement-info="selectedStatement"
      @success="handlePaymentSuccess"
    />

    <!-- 退款对话框 -->
    <StatementRefundDialog
      v-model="refundDialogVisible"
      :statement-id="Number(selectedStatement?.id || 0)"
      :statement-info="selectedStatement"
      @success="handleRefundSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import type { CustomerSimple } from '@/types/customer'
import type { Statement, StatementStatus } from '@/types/statement'
import { searchCustomers } from '@/api/customer'
import { statementApi } from '@/api/statement'
import { formatCurrency } from '@/utils/formatter'
import StatementPaymentDialog from '@/components/StatementPaymentDialog.vue'
import StatementRefundDialog from '@/components/StatementRefundDialog.vue'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  customer_id: '',
  status: '',
  date_range: []
})

// 客户选项
const customerOptions = ref<CustomerSimple[]>([])

// 表格数据
const loading = ref(false)
const tableData = ref<Statement[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 确认对话框
const confirmDialogVisible = ref(false)
const confirmFormRef = ref<FormInstance>()
const confirmForm = reactive({
  statement_id: '',
  due_date: ''
})

// 收款对话框
const paymentDialogVisible = ref(false)
const selectedStatement = ref<Statement | null>(null)

// 退款对话框
const refundDialogVisible = ref(false)
const confirmRules = {
  due_date: [
    { required: true, message: '请选择应付款日期', trigger: 'change' }
  ]
}

// 选中的对账单数组
const selectedStatements = ref<Statement[]>([])

// 卡片模式
const cardMode = ref<'detailed' | 'simple'>('detailed')

// 卡片选择相关方法
const isStatementSelected = (statement: Statement) => {
  return selectedStatements.value.some(item => item.id === statement.id)
}

const handleCardSelection = (statement: Statement, selected: boolean) => {
  if (selected) {
    if (!isStatementSelected(statement)) {
      selectedStatements.value.push(statement)
    }
  } else {
    const index = selectedStatements.value.findIndex(item => item.id === statement.id)
    if (index > -1) {
      selectedStatements.value.splice(index, 1)
    }
  }
}

const handleCardClick = (statement: Statement) => {
  // 点击卡片跳转到详情页
  router.push(`/statements/${statement.id}`)
}

const clearSelection = () => {
  selectedStatements.value = []
}

// 搜索客户
const handleSearchCustomers = async (query: string) => {
  if (query) {
    try {
      const response = await searchCustomers(query)
      customerOptions.value = response.list || []
    } catch (error) {
      console.error('搜索客户失败:', error)
    }
  } else {
    customerOptions.value = []
  }
}

// 搜索
const handleSearch = async () => {
  await fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.customer_id = ''
  searchForm.status = ''
  searchForm.date_range = []
  handleSearch()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      customer_id: searchForm.customer_id,
      status: searchForm.status,
      start_date: searchForm.date_range?.[0],
      end_date: searchForm.date_range?.[1]
    }
    const response = await statementApi.getList(params)

    // 详细打印API响应，用于调试
    console.log('获取对账单列表原始响应:', JSON.stringify(response))

    // 确保tableData有数据
    if (response.list && Array.isArray(response.list) && response.list.length > 0) {
      tableData.value = response.list
      console.log('成功获取并设置对账单数据:', tableData.value.length, '条')
    } else {
      console.warn('获取到的对账单列表为空')
      tableData.value = []
    }

    // 处理分页信息
    if (response.pagination) {
      // 根据不同的字段名设置总数
      if (response.pagination.total_count !== undefined) {
        total.value = response.pagination.total_count
      } else if (response.pagination.total !== undefined) {
        total.value = response.pagination.total
      } else {
        total.value = tableData.value.length
      }
    } else {
      total.value = tableData.value.length
    }

    console.log('对账单列表数据最终状态 - 显示:', tableData.value.length, '条, 总数:', total.value)
  } catch (error) {
    console.error('获取对账单列表失败:', error)
    ElMessage.error('获取对账单列表失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 新建对账单
const handleCreate = () => {
  router.push('/statements/create')
}

// 查看对账单
const handleView = (row: any) => {
  router.push(`/statements/${row.id}`)
}

// 编辑对账单
const handleEdit = (row: any) => {
  router.push(`/statements/edit/${row.id}`)
}

// 收款操作
const handlePayment = (row: any) => {
  selectedStatement.value = row
  paymentDialogVisible.value = true
}

// 退款操作
const handleRefund = (row: any) => {
  selectedStatement.value = row
  refundDialogVisible.value = true
}

// 收款成功回调
const handlePaymentSuccess = (data: any) => {
  console.log('收款成功:', data)
  ElMessage.success('收款成功')
  // 刷新列表
  fetchData()
}

// 退款成功回调
const handleRefundSuccess = (data: any) => {
  console.log('退款成功:', data)
  ElMessage.success('退款成功')
  // 刷新列表
  fetchData()
}

// 导出单个对账单
const handleExportSingle = async (row: any) => {
  try {
    const response = await statementApi.exportStatement(row.id)

    // 创建下载链接
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `对账单_${row.statement_number}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出对账单失败:', error)
    ElMessage.error('导出失败')
  }
}

// 行点击处理
const handleRowClick = (row: any, _column: any, event: Event) => {
  // 检查是否点击了操作按钮或复选框
  const target = event.target as HTMLElement
  if (target.closest('.el-button') || target.closest('.el-checkbox')) {
    return
  }

  // 检查是否有文本选择（避免误触发）
  const selection = window.getSelection()
  if (selection && selection.toString().length > 0) {
    return
  }

  // 跳转到详情页
  handleView(row)
}

// 确认对账单
const handleConfirm = async (row: any) => {
  confirmForm.statement_id = row.id

  // 如果对账单已有应付款日期，则使用现有日期
  if (row.due_date) {
    confirmForm.due_date = row.due_date
  } else {
    // 否则清空，让用户填写
    confirmForm.due_date = ''
  }

  confirmDialogVisible.value = true
}

// 提交确认
const submitConfirm = async () => {
  if (!confirmFormRef.value) return

  await confirmFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await statementApi.confirm(confirmForm.statement_id, {
          due_date: confirmForm.due_date
        })
        ElMessage.success('确认对账单成功')
        confirmDialogVisible.value = false
        await fetchData()
      } catch (error) {
        console.error('确认对账单失败:', error)
        ElMessage.error('确认对账单失败')
      }
    }
  })
}

// 删除对账单
const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    '确定要删除该对账单吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await statementApi.delete(row.id)
      ElMessage.success('删除成功')
      await fetchData()
    } catch (error) {
      console.error('删除对账单失败:', error)
      ElMessage.error('删除对账单失败')
    }
  })
}

// 处理多选变化
const handleSelectionChange = (selection: Statement[]) => {
  selectedStatements.value = selection
  console.log('选中的对账单:', selection)
}

// 导出
const handleExport = async () => {
  try {
    if (selectedStatements.value.length === 0) {
      ElMessage.warning('请先选择要导出的对账单')
      return
    }

    // 对每个选中的对账单进行导出
    for (const statement of selectedStatements.value) {
      try {
        const response = await statementApi.exportStatement(statement.id)

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `对账单_${statement.statement_number}.xlsx`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error(`导出对账单 ${statement.statement_number} 失败:`, error)
        ElMessage.error(`导出对账单 ${statement.statement_number} 失败`)
      }
    }

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 分页
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchData()
}

// 状态处理
const getStatusType = (status: StatementStatus) => {
  const statusMap: Record<string, string> = {
    'draft': 'warning',
    'confirmed': 'success',
    'paid': 'success',
    'cancelled': 'info',
    '待确认': 'warning',
    '已确认': 'primary',
    '部分收款': 'warning',
    '已结清': 'success',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: StatementStatus) => {
  const statusMap: Record<string, string> = {
    'draft': '待确认',
    'confirmed': '已确认',
    'paid': '已结清',
    'cancelled': '已取消',
    '待确认': '待确认',
    '已确认': '已确认',
    '部分收款': '部分收款',
    '已结清': '已结清',
    '已取消': '已取消'
  }
  return statusMap[status] || status
}

// 格式化金额
const formatAmount = (amount: number | string) => {
  if (amount === undefined || amount === null) return formatCurrency(0)
  // 直接格式化金额，保留负号
  return formatCurrency(Number(amount))
}

// 格式化日期
const formatDate = (date: string | Date) => {
  if (!date) return '-'
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN')
}

// 手动刷新
const handleRefresh = () => {
  console.log('手动刷新对账单列表')
  fetchData()
}

onMounted(() => {
  // 重置页码
  currentPage.value = 1
  pageSize.value = 10

  // 清空搜索条件以确保获取所有数据
  searchForm.customer_id = ''
  searchForm.status = ''
  searchForm.date_range = []

  // 获取数据并打印详细日志
  console.log('组件挂载，开始获取对账单列表数据...')
  fetchData()
})
</script>

<style lang="scss" scoped>
.statement-list {
  .search-card {
    margin-bottom: 16px;
  }

  .operation-card {
    margin-bottom: 16px;

    .operation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left-actions {
        display: flex;
        gap: 12px;
      }

      .right-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .empty-placeholder {
    padding: 40px 0;
    text-align: center;
  }

  // 批量选择工具栏
  .batch-selection-bar {
    background: #f5f7fa;
    padding: 12px 16px;
    margin-bottom: 16px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e4e7ed;
  }

  // 卡片列表样式
  .statement-cards-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
  }

  // 对账单卡片样式
  .statement-card {
    position: relative;
    width: 100%;
    border: 2px solid #e8eaed;
    border-radius: 12px;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 4px;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
      transform: translateY(-1px);
    }

    &.selected {
      border-color: #409eff;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2), 0 4px 16px rgba(64, 158, 255, 0.1);
      transform: translateY(-1px);
    }

    .card-checkbox {
      position: absolute;
      top: 16px;
      left: 16px;
      z-index: 2;
    }

    .card-content {
      padding: 12px 16px;
      padding-left: 44px;

      &.detailed {
        padding: 14px 16px;
        padding-left: 44px;
      }

      &.simple {
        padding: 10px 16px;
        padding-left: 44px;
      }
    }

    // 详细模式样式
    .card-content.detailed {
        .card-row-1 {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;

          .left-info {
            flex: 1;
            min-width: 0;

            .statement-number {
              font-size: 16px;
              font-weight: 600;
              color: #303133;
              margin-bottom: 4px;
            }

            .customer-status {
              display: flex;
              align-items: center;
              gap: 8px;
              flex-wrap: wrap;

              .customer {
                font-size: 14px;
                font-weight: 500;
                color: #303133;
                flex-shrink: 0;
              }

              .status-tags {
                display: flex;
                gap: 8px;
                flex-shrink: 0;

                .status-tag-prominent {
                  font-weight: 500;
                }
              }
            }
          }
        }

        .card-row-2 {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          margin-bottom: 12px;

          .left-details {
            flex: 1;

            .amount-details {
              display: flex;
              flex-direction: column;
              gap: 8px;

              .amount-row {
                display: flex;
                gap: 24px;
                align-items: flex-start;
              }

              .amount-group {
                display: flex;
                flex-direction: column;
                gap: 2px;

                .amount-label {
                  font-size: 12px;
                  color: #909399;
                  font-weight: 500;
                }

                .total-amount {
                  font-size: 14px;
                  font-weight: 600;
                  color: #f56c6c;
                }

                .discount-amount {
                  font-size: 14px;
                  font-weight: 600;
                  color: #f56c6c;
                }

                .adjusted-amount {
                  font-size: 16px;
                  font-weight: 600;
                  color: #67c23a;
                }

                .paid-amount {
                  font-size: 14px;
                  font-weight: 600;
                  color: #409eff;
                }
              }
            }
          }

          .right-info {
            display: flex;
            gap: 16px;

            .date-info {
              display: flex;
              flex-direction: column;
              gap: 2px;

              .date-label {
                font-size: 12px;
                color: #909399;
                font-weight: 500;
              }

              .statement-date, .due-date {
                font-size: 13px;
                color: #606266;
              }
            }
          }
        }

        .card-actions {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          &.detailed {
            padding-top: 8px;
            border-top: 1px solid #f0f2f5;
          }
        }
      }

      &.simple {
        .card-main {
          display: flex;
          align-items: center;
          gap: 16px;
          min-height: 40px;

          .statement-number {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            min-width: 140px;
            flex-shrink: 0;
          }

          .customer-info {
            min-width: 150px;
            flex-shrink: 0;

            .customer {
              font-size: 13px;
              font-weight: 500;
              color: #303133;
            }
          }

          .amount-info {
            min-width: 140px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 4px;

            .total-amount {
              color: #303133;
              font-weight: 600;
              font-size: 14px;
            }

            .paid-separator {
              color: #c0c4cc;
              font-size: 12px;
              margin: 0 2px;
            }

            .paid-amount {
              color: #409eff;
              font-size: 14px;
              font-weight: 600;
            }
          }

          .date-info {
            min-width: 100px;
            flex-shrink: 0;

            .statement-date {
              font-size: 13px;
              color: #606266;
            }
          }

          .status-tags {
            display: flex;
            gap: 6px;
            flex-shrink: 0;

            .status-tag-prominent {
              font-size: 12px;
              padding: 3px 8px;
              font-weight: 600;
              border-radius: 6px;
            }
          }

          .card-actions.simple {
            display: flex;
            gap: 6px;
            flex-shrink: 0;

            .el-button {
              font-size: 12px;
              padding: 4px 8px;
            }
          }
        }
      }
    }
  }
</style>
