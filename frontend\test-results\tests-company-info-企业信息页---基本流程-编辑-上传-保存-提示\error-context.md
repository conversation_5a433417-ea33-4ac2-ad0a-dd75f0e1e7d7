# Page snapshot

```yaml
- complementary:
  - img "EMB物资"
  - text: EMB物资管理
  - menubar:
    - menuitem "工作台":
      - img
      - text: 工作台
    - menuitem "客户管理":
      - img
      - text: 客户管理
      - img
    - menuitem "产品管理":
      - img
      - text: 产品管理
      - img
    - menuitem "报价管理":
      - img
      - text: 报价管理
      - img
    - menuitem "订单管理":
      - img
      - text: 订单管理
      - img
    - menuitem "财务管理":
      - img
      - text: 财务管理
      - img
    - menuitem "企业设置" [expanded]:
      - img
      - text: 企业设置
      - img
      - menu:
        - menuitem "企业信息"
        - menuitem "银行账户"
        - menuitem "用户管理"
    - menuitem "系统管理":
      - img
      - text: 系统管理
      - img
- button:
  - img
- navigation "Breadcrumb":
  - link "企业信息"
- button:
  - img
- button "未知用户":
  - img
  - text: 未知用户
- main:
  - tabpanel "企业信息"
  - tabpanel "企业信息"
  - tablist:
    - tab "工作台"
    - tab "企业信息" [selected]:
      - text: 企业信息
      - img
    - tab "企业信息" [selected]:
      - text: 企业信息
      - img
  - heading "企业信息" [level=2]
  - button "保存"
  - button "取消"
  - text: 企业详细信息 * 企业名称
  - textbox "* 企业名称": 测试企业（Playwright）
  - text: 统一社会信用代码
  - textbox "统一社会信用代码": 91110000TESTPLAYW
  - text: 联系人
  - textbox "联系人": 测试联系人
  - text: 联系电话
  - textbox "联系电话": 010-88888888
  - text: 电子邮箱
  - textbox "电子邮箱": <EMAIL>
  - text: 传真
  - textbox "传真": 010-66666666
  - text: 公司地址
  - textbox "公司地址": 北京市海淀区测试路88号
  - group "公司Logo":
    - text: 公司Logo
    - img
  - group "营业执照":
    - text: 营业执照
    - img
- img
- img
- alert:
  - img
  - paragraph: "获取企业信息失败: (sqlite3.OperationalError) no such column: company_info.address [SQL: SELECT company_info.created_at AS company_info_created_at, company_info.updated_at AS company_info_updated_at, company_info.id AS company_info_id, company_info.name AS company_info_name, company_info.tax_id AS company_info_tax_id, company_info.contact AS company_info_contact, company_info.phone AS company_info_phone, company_info.email AS company_info_email, company_info.fax AS company_info_fax, company_info.address AS company_info_address, company_info.logo AS company_info_logo, company_info.license_image AS company_info_license_image FROM company_info LIMIT ? OFFSET ?] [parameters: (1, 0)] (Background on this error at: https://sqlalche.me/e/14/e3q8)"
- alert:
  - img
  - paragraph: 获取企业信息失败，使用模拟数据
```