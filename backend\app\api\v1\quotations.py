"""
报价管理API
提供报价需求、报价单和报价模板的CRUD操作，支持基于需求生成报价单、PDF导出和版本管理
基于原项目API实现，确保与现有数据库结构100%兼容
"""
from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from sqlalchemy import or_, desc
from sqlalchemy.orm import selectinload, joinedload
from typing import Dict, List, Optional, Any, Tuple
import uuid
from datetime import datetime, date
from decimal import Decimal
import os
from io import BytesIO
import pandas as pd

from app.models.quotation import (
    Quotation, QuotationItem, QuotationRequest, QuotationRequestItem,
    QuotationTemplate, QuotationTemplateItem
)
from app.models.customer import Customer
from app.models.product import Product, ProductSpecification
from app.models.order import Order, OrderProduct
from app.schemas.quotation import (
    QuotationSchema, QuotationSimpleSchema, QuotationItemSchema,
    QuotationRequestSchema, QuotationRequestCreateSchema, QuotationRequestUpdateSchema,
    QuotationRequestItemSchema, QuotationTemplateSchema, QuotationTemplateSimpleSchema,
    QuotationTemplateItemSchema
)
from app.schemas.order import OrderSchema
from app.utils.response import (
    success_response,
    error_response,
    paginated_response,
    validation_error_response,
    not_found_response
)
from app.utils.pagination import PaginationHelper
from app.utils.exceptions import ValidationError as CustomValidationError, NotFoundError
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('quotations', description='报价管理API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 自动从Marshmallow Schema生成API模型
quotation_request_model = create_input_model(api, QuotationRequestCreateSchema, 'QuotationRequestInput')
quotation_model = create_input_model(api, QuotationSchema, 'QuotationInput')
quotation_item_model = create_input_model(api, QuotationItemSchema, 'QuotationItemInput')
quotation_template_model = create_input_model(api, QuotationTemplateSchema, 'QuotationTemplateInput')


# 报价需求相关API
@api.route('/requests')
class QuotationRequestList(Resource):
    @api.doc('get_quotation_requests')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('project_name', '项目名称搜索')
    @api.param('status', '状态')
    def get(self):
        """获取报价需求列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            customer_id = request.args.get('customer_id', type=int)
            project_name = request.args.get('project_name', '')
            status = request.args.get('status', '')
            
            query = QuotationRequest.query.options(
                joinedload(QuotationRequest.customer),
                selectinload(QuotationRequest.quotations)
            )
            
            # 应用过滤条件
            if customer_id:
                query = query.filter(QuotationRequest.customer_id == customer_id)
            if project_name:
                query = query.filter(QuotationRequest.project_name.ilike(f'%{project_name}%'))
            if status:
                query = query.filter(QuotationRequest.status == status)
            
            # 排序
            query = query.order_by(QuotationRequest.created_at.desc())
            
            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query,
                serializer_func=lambda item: QuotationRequestSchema(exclude=('items',)).dump(item),
                page=page,
                per_page=per_page,
                message="获取报价需求列表成功"
            )
            
        except Exception as e:
            current_app.logger.error(f"获取报价需求列表失败: {str(e)}")
            return make_response(error_response, f"获取报价需求列表失败: {str(e)}")

    @api.doc('create_quotation_request',
             body=quotation_request_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建报价需求"""
        try:
            data = request.get_json() or {}
            items_data = data.pop('items', [])
            
            # 数据验证
            request_schema = QuotationRequestCreateSchema(exclude=(
                'id', 'request_number', 'customer', 'items', 'created_at', 'updated_at'
            ))
            validated_data = request_schema.load(data)

            # 检查客户是否存在
            customer = Customer.query.get(validated_data['customer_id'])
            if not customer:
                return make_response(error_response, "客户不存在", errors={"customer_id": "指定的客户不存在"})

            # 生成需求编号
            request_number = f"REQ{datetime.now().strftime('%Y%m%d%H%M%S')}{str(uuid.uuid4())[:4].upper()}"
            validated_data['request_number'] = request_number

            # 创建报价需求对象
            quotation_request = QuotationRequest(**validated_data)
            db.session.add(quotation_request)
            db.session.flush()  # 获取ID

            # 创建需求项目
            if items_data:
                item_schema = QuotationRequestItemSchema(many=True, exclude=(
                    'id', 'request_id', 'matched_product', 'matched_specification', 'created_at', 'updated_at'
                ))
                validated_items = item_schema.load(items_data)
                
                for item_data in validated_items:
                    item_data['request_id'] = quotation_request.id
                    
                    # 处理产品匹配逻辑
                    item = self._process_and_match_request_item(item_data, quotation_request.id)
                    db.session.add(item)

            db.session.commit()

            # 返回创建的报价需求信息
            created_request = QuotationRequest.query.get(quotation_request.id)
            response_data = QuotationRequestSchema().dump(created_request)
            return make_response(success_response, response_data, "报价需求创建成功", 201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建报价需求失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建报价需求失败: {str(e)}")

    def _process_and_match_request_item(self, item_data: dict, request_id: int) -> QuotationRequestItem:
        """处理并匹配报价需求项目"""
        item = QuotationRequestItem(request_id=request_id)
        
        # 设置基本字段
        for key, value in item_data.items():
            if hasattr(item, key):
                setattr(item, key, value)
        
        # 如果是新创建且original字段为空，用对应字段填充
        if not item.original_product_name and item.product_name:
            item.original_product_name = item.product_name
        if not item.original_product_model and item.product_model:
            item.original_product_model = item.product_model
        if not item.original_product_spec and item.product_spec:
            item.original_product_spec = item.product_spec
        if not item.original_unit and item.unit:
            item.original_unit = item.unit
        
        # 产品匹配逻辑
        self._match_product_for_item(item)
        
        return item
    
    def _match_product_for_item(self, item: QuotationRequestItem):
        """为报价需求项目匹配产品"""
        name_to_match = item.product_name or item.original_product_name
        model_to_match = item.product_model or item.original_product_model
        spec_to_match = item.product_spec or item.original_product_spec
        
        match_notes = []
        
        if not name_to_match:
            match_notes.append("产品名称为空，无法匹配。")
            item.matched_product_id = None
            item.matched_product_specification_id = None
            item.notes = "\n".join(match_notes)
            return
        
        # 构建查询条件
        query_filters = [Product.name.ilike(f'%{name_to_match}%')]
        if model_to_match:
            query_filters.append(Product.model.ilike(f'%{model_to_match}%'))
        
        matched_products = Product.query.filter(*query_filters).all()
        
        if len(matched_products) == 1:
            matched_product = matched_products[0]
            item.matched_product_id = matched_product.id
            match_notes.append(f"产品匹配: {matched_product.name} (型号: {matched_product.model or 'N/A'})")
            
            # 更新产品信息
            item.product_name = matched_product.name
            item.product_model = matched_product.model
            item.unit = matched_product.unit
            
            # 匹配规格
            if spec_to_match:
                matched_spec = ProductSpecification.query.filter(
                    ProductSpecification.product_id == matched_product.id,
                    ProductSpecification.specification.ilike(f'%{spec_to_match}%')
                ).first()
                
                if matched_spec:
                    item.matched_product_specification_id = matched_spec.id
                    item.product_spec = matched_spec.specification
                    match_notes.append(f"规格匹配: {matched_spec.specification}")
                else:
                    item.matched_product_specification_id = None
                    match_notes.append(f"规格 '{spec_to_match}' 未找到")
            else:
                item.matched_product_specification_id = None
                match_notes.append("未提供规格信息")
                
        elif len(matched_products) > 1:
            # 尝试精确匹配型号
            exact_match = next((p for p in matched_products 
                              if p.model and model_to_match and 
                              p.model.lower() == model_to_match.lower()), None)
            if exact_match:
                item.matched_product_id = exact_match.id
                match_notes.append(f"产品精确匹配: {exact_match.name}")
            else:
                match_notes.append(f"找到多个产品匹配，需人工确认")
                item.matched_product_id = None
        else:
            match_notes.append(f"产品 '{name_to_match}' 未找到")
            item.matched_product_id = None
            item.matched_product_specification_id = None
        
        # 设置匹配备注到专门的字段
        if match_notes:
            item.match_notes = "\n".join(match_notes)


@api.route('/requests/<int:request_id>/auto-match')
class QuotationRequestAutoMatch(Resource):
    @api.doc('auto_match_quotation_request')
    def post(self, request_id):
        """自动匹配报价需求中的所有产品"""
        try:
            quotation_request = QuotationRequest.query.get(request_id)
            if not quotation_request:
                return make_response(not_found_response, "报价需求不存在")

            matched_count = 0
            # 统计待匹配条目数量
            unmatched_items = [item for item in quotation_request.items if not item.matched_product_id]
            total_count = len(unmatched_items)

            # 如果没有待匹配条目，直接返回
            if total_count == 0:
                return make_response(success_response, {
                    'matched_count': 0,
                    'total_count': 0,
                    'success_rate': 100
                }, "所有产品都已匹配，无需进行自动匹配")

            for item in quotation_request.items:
                # 只对未匹配的条目进行自动匹配
                if not item.matched_product_id:
                    # 清空之前的匹配信息
                    item.matched_product_specification_id = None
                    item.match_notes = None
                    item.match_type = None

                    # 执行自动匹配
                    self._match_product_for_item(item, match_type='auto')

                    # 只统计本次新匹配成功的条目
                    if item.matched_product_id:
                        matched_count += 1

            db.session.commit()

            return make_response(success_response, {
                'matched_count': matched_count,
                'total_count': total_count,
                'success_rate': round(matched_count / total_count * 100, 2) if total_count > 0 else 0
            }, f"自动匹配完成，成功匹配 {matched_count}/{total_count} 个产品")

        except Exception as e:
            current_app.logger.error(f"自动匹配失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"自动匹配失败: {str(e)}")

    def _match_product_for_item(self, item: QuotationRequestItem, match_type: str = 'auto'):
        """为报价需求项目匹配产品"""
        # 调用QuotationRequestDetail类的匹配方法
        detail_resource = QuotationRequestDetail()
        detail_resource._match_product_for_item(item, match_type)


@api.route('/requests/<int:request_id>/export')
class QuotationRequestExport(Resource):
    @api.doc('export_quotation_request')
    def get(self, request_id):
        """导出报价需求表"""
        try:
            quotation_request = QuotationRequest.query.options(
                joinedload(QuotationRequest.customer),
                joinedload(QuotationRequest.items).joinedload(QuotationRequestItem.matched_product),
                joinedload(QuotationRequest.items).joinedload(QuotationRequestItem.matched_specification)
            ).get(request_id)

            if not quotation_request:
                return make_response(not_found_response, "报价需求不存在")

            # 获取请求参数
            format_type = request.args.get('format', 'xlsx')
            columns = request.args.get('columns', '').split(',') if request.args.get('columns') else None
            include_header = request.args.get('include_header', 'true').lower() == 'true'

            # 准备导出数据
            export_data = []
            for item in quotation_request.items:
                # 获取匹配产品信息
                matched_product_name = ''
                matched_product_model = ''
                matched_spec_name = ''
                matched_price = ''
                match_status = '未匹配'

                if item.matched_product_id and item.matched_product:
                    matched_product_name = item.matched_product.name or ''
                    matched_product_model = item.matched_product.model or ''

                    if item.matched_product_specification_id and item.matched_specification:
                        matched_spec_name = item.matched_specification.specification or ''
                        matched_price = str(item.matched_specification.suggested_price or '')
                        match_status = '已匹配'
                    else:
                        match_status = '部分匹配'

                row_data = {
                    'product_name': item.product_name or '',
                    'product_model': item.product_model or '',
                    'product_spec': item.product_spec or '',
                    'quantity': item.quantity or 0,
                    'unit': item.unit or '',
                    'match_status': match_status,
                    'matched_product_name': matched_product_name,
                    'matched_product_model': matched_product_model,
                    'matched_spec_name': matched_spec_name,
                    'suggested_price': matched_price,
                    'notes': item.notes or ''
                }

                # 如果指定了列，只导出指定的列
                if columns and columns != ['']:
                    filtered_data = {}
                    for col in columns:
                        if col in row_data:
                            filtered_data[col] = row_data[col]
                    export_data.append(filtered_data)
                else:
                    export_data.append(row_data)

            # 列名映射
            column_mapping = {
                'product_name': '需求产品名称',
                'product_model': '需求产品型号',
                'product_spec': '需求产品规格',
                'quantity': '数量',
                'unit': '单位',
                'match_status': '匹配状态',
                'matched_product_name': '匹配产品名称',
                'matched_product_model': '匹配产品型号',
                'matched_spec_name': '匹配规格',
                'suggested_price': '建议价格',
                'notes': '备注'
            }

            if format_type.lower() == 'xlsx':
                # Excel导出
                df = pd.DataFrame(export_data)

                # 重命名列
                if not df.empty:
                    df = df.rename(columns=column_mapping)

                # 创建BytesIO对象
                output = BytesIO()

                # 使用pandas的ExcelWriter
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    # 写入基本信息
                    if include_header:
                        info_data = {
                            '项目信息': [
                                f"需求编号: {quotation_request.request_number}",
                                f"项目名称: {quotation_request.project_name or ''}",
                                f"项目地址: {quotation_request.project_address or ''}",
                                f"客户名称: {quotation_request.customer.name if quotation_request.customer else ''}",
                                f"预期时间: {quotation_request.expected_date or ''}",
                                f"状态: {quotation_request.status}",
                                f"备注: {quotation_request.notes or ''}"
                            ]
                        }
                        info_df = pd.DataFrame(info_data)
                        info_df.to_excel(writer, sheet_name='报价需求表', index=False, startrow=0)

                        # 写入产品明细，从第10行开始
                        if not df.empty:
                            df.to_excel(writer, sheet_name='报价需求表', index=False, startrow=10)
                    else:
                        # 只写入产品明细
                        if not df.empty:
                            df.to_excel(writer, sheet_name='报价需求表', index=False, startrow=0)

                output.seek(0)

                filename = f"报价需求表_{quotation_request.project_name or '未命名'}_{datetime.now().strftime('%Y%m%d')}.xlsx"

                return send_file(
                    output,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )

            else:  # PDF格式
                from reportlab.lib.pagesizes import A4
                from reportlab.lib import colors
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                from io import BytesIO

                # 注册中文字体
                try:
                    pdfmetrics.registerFont(TTFont('SimHei', 'C:/Windows/Fonts/simhei.ttf'))
                    font_name = 'SimHei'
                except:
                    try:
                        pdfmetrics.registerFont(TTFont('SimSun', 'C:/Windows/Fonts/simsun.ttc'))
                        font_name = 'SimSun'
                    except:
                        font_name = 'Helvetica'

                buffer = BytesIO()
                doc = SimpleDocTemplate(buffer, pagesize=A4)
                story = []

                # 样式
                styles = getSampleStyleSheet()
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontName=font_name,
                    fontSize=16,
                    alignment=1,  # 居中
                    spaceAfter=20
                )

                normal_style = ParagraphStyle(
                    'CustomNormal',
                    parent=styles['Normal'],
                    fontName=font_name,
                    fontSize=10
                )

                # 标题
                story.append(Paragraph('报价需求表', title_style))
                story.append(Spacer(1, 12))

                # 基本信息
                if include_header:
                    info_data = [
                        ['需求编号', quotation_request.request_number],
                        ['项目名称', quotation_request.project_name or ''],
                        ['项目地址', quotation_request.project_address or ''],
                        ['客户名称', quotation_request.customer.name if quotation_request.customer else ''],
                        ['预期时间', str(quotation_request.expected_date) if quotation_request.expected_date else ''],
                        ['状态', quotation_request.status],
                        ['备注', quotation_request.notes or '']
                    ]

                    info_table = Table(info_data, colWidths=[100, 300])
                    info_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (0, -1), colors.grey),
                        ('TEXTCOLOR', (0, 0), (0, -1), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, -1), font_name),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                        ('BACKGROUND', (1, 0), (1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))

                    story.append(info_table)
                    story.append(Spacer(1, 20))

                # 产品明细表
                if export_data:
                    # 表头
                    headers = []
                    for key in export_data[0].keys():
                        headers.append(column_mapping.get(key, key))

                    # 数据行
                    table_data = [headers]
                    for row in export_data:
                        table_data.append([str(row[key]) for key in row.keys()])

                    # 创建表格
                    table = Table(table_data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, -1), font_name),
                        ('FONTSIZE', (0, 0), (-1, -1), 8),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))

                    story.append(table)

                # 生成PDF
                doc.build(story)
                buffer.seek(0)

                filename = f"报价需求表_{quotation_request.project_name or '未命名'}_{datetime.now().strftime('%Y%m%d')}.pdf"

                return send_file(
                    buffer,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/pdf'
                )

        except Exception as e:
            current_app.logger.error(f"导出报价需求表失败: {str(e)}")
            return make_response(error_response, f"导出报价需求表失败: {str(e)}")


@api.route('/requests/<int:request_id>/status')
class QuotationRequestStatus(Resource):
    @api.doc('update_quotation_request_status')
    def put(self, request_id):
        """更新报价需求状态"""
        try:
            quotation_request = QuotationRequest.query.options(
                joinedload(QuotationRequest.items),
                selectinload(QuotationRequest.quotations)
            ).get(request_id)
            if not quotation_request:
                return make_response(not_found_response, "报价需求不存在")

            data = request.get_json() or {}

            if 'status' not in data:
                return make_response(error_response, "缺少状态参数", code=400)

            new_status = data['status']
            valid_status = ['待确认', '已确认']

            if new_status not in valid_status:
                return make_response(error_response, "无效的状态值", code=400)

            # 检查状态变更是否合法
            current_status = quotation_request.status
            valid_transitions = {
                '待确认': ['已确认'],
                '已确认': ['待确认']
            }

            if new_status not in valid_transitions.get(current_status, []):
                return make_response(error_response, f"不能从{current_status}状态变更为{new_status}状态", code=400)

            # 状态变更校验：待确认 -> 已确认时，检查产品匹配状态
            if new_status == '已确认' and current_status == '待确认':
                all_items_matched = True
                if not quotation_request.items:
                    all_items_matched = False
                else:
                    for item in quotation_request.items:
                        if item.matched_product_specification_id is None:
                            all_items_matched = False
                            break
                if not all_items_matched:
                    return make_response(error_response, "所有需求条目必须完全匹配后才能确认")

            # 状态变更校验：已确认 -> 待确认时，检查是否已关联报价单
            if new_status == '待确认' and current_status == '已确认':
                if quotation_request.quotations:
                    return make_response(error_response, "已关联报价单的需求表不允许取消确认")

            quotation_request.status = new_status
            db.session.commit()

            return make_response(success_response, {"status": new_status}, "状态更新成功")

        except Exception as e:
            current_app.logger.error(f"更新报价需求状态失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新报价需求状态失败: {str(e)}")


@api.route('/requests/<int:request_id>')
class QuotationRequestDetail(Resource):
    @api.doc('get_quotation_request')
    def get(self, request_id):
        """获取报价需求详情"""
        try:
            quotation_request = QuotationRequest.query.options(
                selectinload(QuotationRequest.items).selectinload(QuotationRequestItem.matched_product),
                selectinload(QuotationRequest.items).selectinload(QuotationRequestItem.matched_specification),
                selectinload(QuotationRequest.customer),
                selectinload(QuotationRequest.quotations)
            ).get(request_id)
            if not quotation_request:
                return make_response(not_found_response, "报价需求不存在")

            request_data = QuotationRequestSchema().dump(quotation_request)
            return make_response(success_response, request_data, "获取报价需求详情成功")

        except Exception as e:
            current_app.logger.error(f"获取报价需求详情失败: {str(e)}")
            return make_response(error_response, f"获取报价需求详情失败: {str(e)}")

    @api.doc('update_quotation_request',
             body=quotation_request_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Request Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, request_id):
        """更新报价需求"""
        try:
            quotation_request = QuotationRequest.query.options(
                joinedload(QuotationRequest.items),
                selectinload(QuotationRequest.quotations)
            ).get(request_id)
            if not quotation_request:
                return make_response(not_found_response, "报价需求不存在")

            # 权限控制：已确认且已关联报价单的需求表不允许编辑
            if quotation_request.status == '已确认' and quotation_request.quotations:
                return make_response(error_response, "已关联报价单的需求表不允许编辑", code=403)

            data = request.get_json() or {}
            items_data = data.pop('items', None)
            new_status = data.get('status')

            # 状态变更校验：待确认 -> 已确认
            if new_status == '已确认' and quotation_request.status == '待确认':
                all_items_matched = True
                if not quotation_request.items:
                    all_items_matched = False
                else:
                    for item in quotation_request.items:
                        if item.matched_product_specification_id is None:
                            all_items_matched = False
                            break
                if not all_items_matched:
                    return make_response(error_response, "所有需求条目必须完全匹配后才能提交")

            # 验证主表数据
            request_schema = QuotationRequestUpdateSchema(exclude=(
                'id', 'request_number', 'customer', 'items', 'created_at', 'updated_at'
            ))
            validated_data = request_schema.load(data, partial=True)

            # 检查客户是否存在
            if 'customer_id' in validated_data:
                customer = Customer.query.get(validated_data['customer_id'])
                if not customer:
                    return make_response(error_response, "客户不存在", errors={"customer_id": "指定的客户不存在"})

            # 更新主表字段
            for key, value in validated_data.items():
                setattr(quotation_request, key, value)

            # 处理项目数据
            if items_data is not None:
                updated_item_ids = set()

                for item_payload in items_data:
                    item_id = item_payload.get('id')
                    if item_id:
                        # 更新现有项目
                        item_instance = QuotationRequestItem.query.get(item_id)
                        if item_instance and item_instance.request_id == quotation_request.id:
                            # 更新项目数据，保留匹配信息
                            item_schema = QuotationRequestItemSchema(exclude=(
                                'matched_product', 'matched_specification', 'created_at', 'updated_at'
                            ))
                            validated_item_data = item_schema.load(item_payload, partial=True)

                            for k, v in validated_item_data.items():
                                if hasattr(item_instance, k):
                                    setattr(item_instance, k, v)

                            # 不再自动匹配，如果有手动匹配信息，设置为手动匹配类型
                            if item_instance.matched_product_id and not item_instance.match_type:
                                item_instance.match_type = 'manual'
                            updated_item_ids.add(item_id)
                    else:
                        # 创建新项目
                        item_payload['request_id'] = quotation_request.id
                        new_item = self._process_and_match_request_item(item_payload, quotation_request.id)
                        db.session.add(new_item)

                # 删除不在更新列表中的项目
                existing_item_ids = {item.id for item in quotation_request.items}
                items_to_delete = existing_item_ids - updated_item_ids
                if items_to_delete:
                    QuotationRequestItem.query.filter(
                        QuotationRequestItem.id.in_(items_to_delete)
                    ).delete(synchronize_session=False)

            db.session.commit()

            # 返回更新后的数据
            updated_data = QuotationRequestSchema().dump(quotation_request)
            return make_response(success_response, updated_data, "报价需求更新成功")

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            db.session.rollback()
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新报价需求失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新报价需求失败: {str(e)}")

    @api.doc('delete_quotation_request')
    def delete(self, request_id):
        """删除报价需求"""
        try:
            quotation_request = QuotationRequest.query.options(
                selectinload(QuotationRequest.quotations)
            ).get(request_id)
            if not quotation_request:
                return make_response(not_found_response, "报价需求不存在")

            # 检查是否可以删除
            if quotation_request.status not in ['待确认']:
                return make_response(error_response, "只有待确认状态的报价需求可以删除")

            # 额外检查：如果已关联报价单，不允许删除
            if quotation_request.quotations:
                return make_response(error_response, "已关联报价单的需求表不允许删除")

            db.session.delete(quotation_request)
            db.session.commit()
            return make_response(success_response, message="报价需求删除成功")

        except Exception as e:
            current_app.logger.error(f"删除报价需求失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除报价需求失败: {str(e)}")

    def _process_and_match_request_item(self, item_data: dict, request_id: int) -> QuotationRequestItem:
        """处理并匹配报价需求项目"""
        item = QuotationRequestItem(request_id=request_id)

        # 设置基本字段
        for key, value in item_data.items():
            if hasattr(item, key):
                setattr(item, key, value)

        # 如果是新创建且original字段为空，用对应字段填充
        if not item.original_product_name and item.product_name:
            item.original_product_name = item.product_name
        if not item.original_product_model and item.product_model:
            item.original_product_model = item.product_model
        if not item.original_product_spec and item.product_spec:
            item.original_product_spec = item.product_spec
        if not item.original_unit and item.unit:
            item.original_unit = item.unit

        # 不再自动匹配，只有用户主动触发时才匹配
        # 如果有手动匹配信息，设置为手动匹配类型
        if item.matched_product_id:
            item.match_type = 'manual'

        return item

    def _match_product_for_item(self, item: QuotationRequestItem, match_type: str = 'auto'):
        """为报价需求项目匹配产品

        Args:
            item: 报价需求项目
            match_type: 匹配类型，'auto'自动匹配，'manual'手动匹配
        """
        name_to_match = item.product_name or item.original_product_name
        model_to_match = item.product_model or item.original_product_model
        spec_to_match = item.product_spec or item.original_product_spec

        match_notes = []

        if not name_to_match:
            match_notes.append("产品名称为空，无法匹配。")
            item.matched_product_id = None
            item.matched_product_specification_id = None
            item.notes = "\n".join(match_notes)
            return

        # 构建查询条件
        query_filters = [Product.name.ilike(f'%{name_to_match}%')]
        if model_to_match:
            query_filters.append(Product.model.ilike(f'%{model_to_match}%'))

        matched_products = Product.query.filter(*query_filters).all()

        if len(matched_products) == 1:
            matched_product = matched_products[0]
            item.matched_product_id = matched_product.id
            item.match_type = match_type
            match_notes.append(f"产品匹配: {matched_product.name} (型号: {matched_product.model or 'N/A'})")

            # 不再更新原产品信息，保留用户的原始输入
            # 匹配信息通过关联字段获取

            # 匹配规格
            if spec_to_match:
                matched_spec = ProductSpecification.query.filter(
                    ProductSpecification.product_id == matched_product.id,
                    ProductSpecification.specification.ilike(f'%{spec_to_match}%')
                ).first()

                if matched_spec:
                    item.matched_product_specification_id = matched_spec.id
                    item.product_spec = matched_spec.specification
                    match_notes.append(f"规格匹配: {matched_spec.specification}")
                else:
                    item.matched_product_specification_id = None
                    match_notes.append(f"规格 '{spec_to_match}' 未找到")
            else:
                item.matched_product_specification_id = None
                match_notes.append("未提供规格信息")

        elif len(matched_products) > 1:
            # 尝试精确匹配型号
            exact_match = next((p for p in matched_products
                              if p.model and model_to_match and
                              p.model.lower() == model_to_match.lower()), None)
            if exact_match:
                item.matched_product_id = exact_match.id
                item.match_type = match_type
                match_notes.append(f"产品精确匹配: {exact_match.name}")
            else:
                match_notes.append(f"找到多个产品匹配，需人工确认")
                item.matched_product_id = None
                item.match_type = None
        else:
            match_notes.append(f"产品 '{name_to_match}' 未找到")
            item.matched_product_id = None
            item.matched_product_specification_id = None
            item.match_type = None

        # 设置匹配备注到专门的字段
        if match_notes:
            item.match_notes = "\n".join(match_notes)


# 报价单相关API
@api.route('')
class QuotationList(Resource):
    @api.doc('get_quotations')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('quotation_number', '报价单号搜索')
    @api.param('customer_id', '客户ID', type='integer')
    @api.param('customer_name', '客户名称搜索')
    @api.param('project_name', '项目名称搜索')
    @api.param('status', '状态')
    @api.param('start_date', '开始日期 (YYYY-MM-DD)')
    @api.param('end_date', '结束日期 (YYYY-MM-DD)')
    def get(self):
        """获取报价单列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            quotation_number = request.args.get('quotation_number', '')
            customer_id = request.args.get('customer_id', type=int)
            customer_name = request.args.get('customer_name', '')
            project_name = request.args.get('project_name', '')
            status = request.args.get('status', '')
            start_date = request.args.get('start_date', '')
            end_date = request.args.get('end_date', '')

            query = Quotation.query.options(joinedload(Quotation.customer))

            # 应用过滤条件
            if quotation_number:
                query = query.filter(Quotation.quotation_number.ilike(f'%{quotation_number}%'))
            if customer_id:
                query = query.filter(Quotation.customer_id == customer_id)
            if customer_name:
                # 通过关联的客户表搜索客户名称
                query = query.join(Customer).filter(Customer.name.ilike(f'%{customer_name}%'))
            if project_name:
                query = query.filter(Quotation.project_name.ilike(f'%{project_name}%'))
            if status:
                query = query.filter(Quotation.status == status)
            if start_date:
                try:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    query = query.filter(Quotation.created_at >= start)
                except ValueError:
                    return make_response(error_response, "开始日期格式错误，请使用 YYYY-MM-DD 格式", code=400)
            if end_date:
                try:
                    end = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
                    query = query.filter(Quotation.created_at <= end)
                except ValueError:
                    return make_response(error_response, "结束日期格式错误，请使用 YYYY-MM-DD 格式", code=400)

            # 排序
            query = query.order_by(Quotation.created_at.desc())

            # 分页查询
            pagination = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

            # 序列化数据并添加关联状态信息
            quotations_data = []
            for quotation in pagination.items:
                quotation_dict = QuotationSimpleSchema().dump(quotation)

                # 检查是否已关联订单
                linked_order = None
                orders = Order.query.filter(Order.quotation_ids.isnot(None)).all()
                for order in orders:
                    try:
                        import json
                        quotation_ids = json.loads(order.quotation_ids)
                        if quotation.id in quotation_ids:
                            linked_order = order
                            break
                    except:
                        continue

                quotation_dict['is_linked_to_order'] = linked_order is not None
                quotation_dict['linked_order_id'] = linked_order.id if linked_order else None
                quotation_dict['linked_order_number'] = linked_order.order_number if linked_order else None

                quotations_data.append(quotation_dict)

            # 构建分页响应
            response_data = {
                'items': quotations_data,
                'total': pagination.total,
                'page': pagination.page,
                'per_page': pagination.per_page,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }

            return make_response(success_response, response_data, "获取报价单列表成功")

        except Exception as e:
            current_app.logger.error(f"获取报价单列表失败: {str(e)}")
            return make_response(error_response, f"获取报价单列表失败: {str(e)}")

    @api.doc('create_quotation',
             body=quotation_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建报价单"""
        try:
            data = request.get_json() or {}
            items_data = data.pop('items', [])

            # 数据验证
            quotation_schema = QuotationSchema(exclude=(
                'id', 'quotation_number', 'total_amount', 'customer', 'quotation_request',
                'items', 'created_at', 'updated_at'
            ))
            validated_data = quotation_schema.load(data)

            # 检查客户是否存在
            customer = Customer.query.get(validated_data['customer_id'])
            if not customer:
                return make_response(error_response, "客户不存在", errors={"customer_id": "指定的客户不存在"})

            # 检查报价需求是否存在（如果提供了）
            if validated_data.get('request_id'):
                quotation_request = QuotationRequest.query.get(validated_data['request_id'])
                if not quotation_request:
                    return make_response(error_response, "报价需求不存在", errors={"request_id": "指定的报价需求不存在"})

            # 生成报价单编号
            quotation_number = f"QUO{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"
            validated_data['quotation_number'] = quotation_number

            # 创建报价单对象
            quotation = Quotation(**validated_data)
            db.session.add(quotation)
            db.session.flush()  # 获取ID

            # 创建报价单项目
            if items_data:
                item_schema = QuotationItemSchema(many=True, exclude=(
                    'id', 'quotation_id', 'total_price', 'product', 'specification',
                    'created_at', 'updated_at'
                ))
                validated_items = item_schema.load(items_data)

                for item_data in validated_items:
                    # 检查产品是否存在
                    product = Product.query.get(item_data['product_id'])
                    if not product:
                        current_app.logger.warning(f"产品ID {item_data['product_id']} 不存在，跳过此项目")
                        continue

                    # 检查规格是否存在
                    spec = None
                    if item_data.get('product_specification_id'):
                        spec = ProductSpecification.query.get(item_data['product_specification_id'])
                        if not spec or spec.product_id != product.id:
                            current_app.logger.warning(f"规格ID {item_data['product_specification_id']} 不存在或不属于产品 {product.id}")
                            spec = None

                    # 创建报价单项目
                    item = QuotationItem(
                        quotation_id=quotation.id,
                        product_id=item_data['product_id'],
                        product_specification_id=spec.id if spec else None,
                        quantity=item_data['quantity'],
                        unit_price=item_data['unit_price'],
                        tax_rate=item_data.get('tax_rate', Decimal('13.0')),
                        discount=item_data.get('discount', Decimal('0.0')),
                        notes=item_data.get('notes', '')
                    )

                    # 填充快照字段
                    item.product_name = product.name
                    item.product_model = product.model
                    item.product_unit = product.unit
                    if spec:
                        item.product_specification = spec.specification

                    # 计算总价
                    item.total_price = item.calculate_total_price()
                    db.session.add(item)

            db.session.flush()

            # 更新报价单总金额
            quotation.total_amount = quotation.calculate_total_amount()

            db.session.commit()

            # 返回创建的报价单信息
            created_quotation = Quotation.query.get(quotation.id)
            response_data = QuotationSchema().dump(created_quotation)
            return make_response(success_response, response_data, "报价单创建成功", 201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建报价单失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建报价单失败: {str(e)}")


@api.route('/<int:quotation_id>')
class QuotationDetail(Resource):
    @api.doc('get_quotation')
    def get(self, quotation_id):
        """获取报价单详情"""
        try:
            quotation = Quotation.query.get(quotation_id)
            if not quotation:
                return make_response(not_found_response, "报价单不存在")

            quotation_data = QuotationSchema().dump(quotation)

            # 获取关联的订单列表
            related_orders = quotation.get_related_orders()
            if related_orders:
                from ...schemas.order import OrderSimpleSchema
                quotation_data['related_orders'] = OrderSimpleSchema(many=True, only=(
                    'id', 'order_number', 'order_status', 'payment_status', 'total_amount', 'created_at'
                )).dump(related_orders)
                quotation_data['is_linked_to_order'] = True
            else:
                quotation_data['related_orders'] = []
                quotation_data['is_linked_to_order'] = False

            return make_response(success_response, quotation_data, "获取报价单详情成功")

        except Exception as e:
            current_app.logger.error(f"获取报价单详情失败: {str(e)}")
            return make_response(error_response, f"获取报价单详情失败: {str(e)}")

    @api.doc('update_quotation',
             body=quotation_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Quotation Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, quotation_id):
        """更新报价单"""
        try:
            quotation = Quotation.query.get(quotation_id)
            if not quotation:
                return make_response(not_found_response, "报价单不存在")

            # 检查是否可以编辑
            if quotation.status != '待确认':
                return make_response(error_response, "只有待确认状态的报价单可以编辑")

            data = request.get_json() or {}
            items_data = data.pop('items', None)

            # 验证主表数据
            quotation_schema = QuotationSchema(exclude=(
                'id', 'quotation_number', 'total_amount', 'customer', 'quotation_request',
                'items', 'created_at', 'updated_at'
            ))
            validated_data = quotation_schema.load(data, partial=True)

            # 检查客户是否存在
            if 'customer_id' in validated_data:
                customer = Customer.query.get(validated_data['customer_id'])
                if not customer:
                    return make_response(error_response, "客户不存在", errors={"customer_id": "指定的客户不存在"})

            # 更新主表字段
            for key, value in validated_data.items():
                if key == 'valid_until' and isinstance(value, str):
                    try:
                        # 处理日期字符串
                        dt_obj = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        setattr(quotation, key, dt_obj.date())
                    except ValueError:
                        setattr(quotation, key, value)
                else:
                    setattr(quotation, key, value)

            # 处理项目数据
            if items_data is not None:
                # 获取现有项目ID
                existing_ids = {item.id for item in quotation.items}
                request_ids = {item.get('id') for item in items_data if item.get('id')}

                # 删除不在请求中的项目
                for item_id in existing_ids - request_ids:
                    item = QuotationItem.query.get(item_id)
                    if item:
                        db.session.delete(item)

                # 更新或创建项目
                for item_data in items_data:
                    try:
                        if 'id' in item_data and item_data['id']:
                            # 更新现有项目
                            item = QuotationItem.query.get(item_data['id'])
                            if item and item.quotation_id == quotation.id:
                                # 创建验证数据的副本，移除不需要验证的字段
                                validation_data = {k: v for k, v in item_data.items() if k not in ['id', 'quotation_id']}

                                # 验证项目数据
                                item_schema = QuotationItemSchema(exclude=(
                                    'id', 'quotation_id', 'total_price', 'product', 'specification',
                                    'created_at', 'updated_at'
                                ))
                                validated_item_data = item_schema.load(validation_data, partial=True)

                                # 检查产品和规格
                                product = None
                                if 'product_id' in validated_item_data:
                                    product = Product.query.get(validated_item_data['product_id'])
                                    if not product:
                                        current_app.logger.warning(f"产品ID {validated_item_data['product_id']} 不存在")
                                        continue

                                spec = None
                                if product and 'product_specification_id' in validated_item_data and validated_item_data['product_specification_id']:
                                    spec = ProductSpecification.query.get(validated_item_data['product_specification_id'])
                                    if not spec or spec.product_id != product.id:
                                        current_app.logger.warning(f"规格ID {validated_item_data['product_specification_id']} 无效")
                                        spec = None

                                # 更新项目属性
                                for k, v in validated_item_data.items():
                                    setattr(item, k, v)

                                # 快照字段优先使用前端传递的数据，如果没有则从产品库获取
                                if 'product_name' not in validated_item_data and product:
                                    item.product_name = product.name
                                if 'product_model' not in validated_item_data and product:
                                    item.product_model = product.model
                                if 'product_unit' not in validated_item_data and product:
                                    item.product_unit = product.unit
                                if 'product_specification' not in validated_item_data:
                                    if spec:
                                        item.product_specification = spec.specification
                                    elif 'product_specification_id' in validated_item_data and validated_item_data['product_specification_id'] is None:
                                        item.product_specification = None

                                item.total_price = item.calculate_total_price()
                        else:
                            # 创建新项目
                            # 创建验证数据的副本，移除不需要验证的字段
                            validation_data = {k: v for k, v in item_data.items() if k not in ['id', 'quotation_id']}

                            item_schema = QuotationItemSchema(exclude=(
                                'id', 'quotation_id', 'total_price', 'product', 'specification',
                                'created_at', 'updated_at'
                            ))
                            validated_item_data = item_schema.load(validation_data)

                            product = Product.query.get(validated_item_data['product_id'])
                            if not product:
                                current_app.logger.warning(f"产品ID {validated_item_data['product_id']} 不存在，跳过")
                                continue

                            spec = None
                            if validated_item_data.get('product_specification_id'):
                                spec = ProductSpecification.query.filter_by(
                                    id=validated_item_data['product_specification_id'],
                                    product_id=validated_item_data['product_id']
                                ).first()
                                if not spec:
                                    current_app.logger.warning(f"规格ID {validated_item_data['product_specification_id']} 无效，跳过")
                                    continue

                            # 创建项目
                            item = QuotationItem(
                                quotation_id=quotation.id,
                                product_id=product.id,
                                product_specification_id=spec.id if spec else None,
                                quantity=validated_item_data['quantity'],
                                unit_price=validated_item_data['unit_price'],
                                tax_rate=validated_item_data.get('tax_rate', Decimal('13.0')),
                                discount=validated_item_data.get('discount', Decimal('0.0')),
                                notes=validated_item_data.get('notes', '')
                            )

                            # 填充快照字段 - 优先使用前端传递的数据
                            item.product_name = validated_item_data.get('product_name', product.name)
                            item.product_model = validated_item_data.get('product_model', product.model)
                            item.product_unit = validated_item_data.get('product_unit', product.unit)
                            if 'product_specification' in validated_item_data:
                                item.product_specification = validated_item_data['product_specification']
                            elif spec:
                                item.product_specification = spec.specification

                            item.total_price = item.calculate_total_price()
                            db.session.add(item)

                    except ValidationError as ve:
                        current_app.logger.error(f"项目数据验证失败: {ve.messages}")
                        # 将项目级别的验证错误转换为更清晰的错误信息
                        item_index = items_data.index(item_data)
                        error_msg = f"第{item_index + 1}个项目数据验证失败: {ve.messages}"
                        return make_response(validation_error_response, {"items": [error_msg]})
                    except Exception as ie:
                        current_app.logger.error(f"处理项目数据失败: {str(ie)}")
                        item_index = items_data.index(item_data)
                        error_msg = f"第{item_index + 1}个项目处理失败: {str(ie)}"
                        return make_response(error_response, error_msg)

                db.session.flush()

                # 更新报价单总金额
                quotation.total_amount = quotation.calculate_total_amount()

            db.session.commit()

            # 返回更新后的数据
            updated_data = QuotationSchema().dump(quotation)
            return make_response(success_response, updated_data, "报价单更新成功")

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            db.session.rollback()
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新报价单失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新报价单失败: {str(e)}")

    @api.doc('delete_quotation')
    def delete(self, quotation_id):
        """删除报价单"""
        try:
            quotation = Quotation.query.get(quotation_id)
            if not quotation:
                return make_response(not_found_response, "报价单不存在")

            # 检查是否可以删除
            if quotation.status not in ['待确认', '已拒绝', '已过期']:
                return make_response(error_response, "当前状态的报价单不可删除")

            db.session.delete(quotation)
            db.session.commit()
            return make_response(success_response, message="报价单删除成功")

        except Exception as e:
            current_app.logger.error(f"删除报价单失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除报价单失败: {str(e)}")


@api.route('/<int:quotation_id>/status')
class QuotationStatus(Resource):
    @api.doc('update_quotation_status')
    def put(self, quotation_id):
        """更新报价单状态"""
        try:
            quotation = Quotation.query.get(quotation_id)
            if not quotation:
                return make_response(not_found_response, "报价单不存在")

            data = request.get_json() or {}

            if 'status' not in data:
                return make_response(error_response, "缺少状态参数", code=400)

            new_status = data['status']
            valid_status = ['待确认', '已确认', '已拒绝', '已过期']

            if new_status not in valid_status:
                return make_response(error_response, "无效的状态值", code=400)

            # 检查状态变更是否合法
            current_status = quotation.status
            valid_transitions = {
                '待确认': ['已确认', '已拒绝'],
                '已确认': ['待确认', '已拒绝', '已过期'],
                '已拒绝': ['待确认', '已确认'],
                '已过期': []
            }

            if new_status not in valid_transitions.get(current_status, []):
                return make_response(error_response, f"不能从{current_status}状态变更为{new_status}状态", code=400)

            quotation.status = new_status
            db.session.commit()

            return make_response(success_response, {"status": new_status}, "状态更新成功")

        except Exception as e:
            current_app.logger.error(f"更新报价单状态失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新报价单状态失败: {str(e)}")


@api.route('/<int:quotation_id>/export')
class QuotationExport(Resource):
    @api.doc('export_quotation')
    def get(self, quotation_id):
        """导出报价单"""
        try:
            quotation = Quotation.query.options(
                joinedload(Quotation.customer),
                joinedload(Quotation.items)
            ).get(quotation_id)

            if not quotation:
                return make_response(not_found_response, "报价单不存在")

            # 获取请求参数
            format_type = request.args.get('format', 'xlsx')
            columns = request.args.get('columns', '').split(',') if request.args.get('columns') else None
            include_header = request.args.get('include_header', 'true').lower() == 'true'

            # 准备导出数据
            export_data = []
            for item in quotation.items:
                row_data = {
                    'product_name': item.product_name or '',
                    'product_model': item.product_model or '',
                    'product_specification': item.product_specification or '',
                    'product_unit': item.product_unit or '',
                    'quantity': item.quantity or 0,
                    'unit_price': float(item.unit_price or 0),
                    'discount': float(item.discount or 0),
                    'tax_rate': float(item.tax_rate or 0),
                    'total_price': float(item.total_price or 0),
                    'notes': item.notes or ''
                }

                # 如果指定了列，只导出指定的列
                if columns and columns != ['']:
                    filtered_data = {}
                    for col in columns:
                        if col in row_data:
                            filtered_data[col] = row_data[col]
                    export_data.append(filtered_data)
                else:
                    export_data.append(row_data)

            if format_type == 'xlsx':
                # 创建Excel文件
                import pandas as pd
                from io import BytesIO

                # 列名映射
                column_mapping = {
                    'product_name': '产品名称',
                    'product_model': '型号',
                    'product_specification': '规格',
                    'product_unit': '单位',
                    'quantity': '数量',
                    'unit_price': '单价',
                    'discount': '折扣(%)',
                    'tax_rate': '税率(%)',
                    'total_price': '金额',
                    'notes': '备注'
                }

                df = pd.DataFrame(export_data)
                if not df.empty:
                    # 重命名列为中文
                    df = df.rename(columns=column_mapping)

                # 创建BytesIO对象
                output = BytesIO()

                # 使用pandas的ExcelWriter
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    # 写入基本信息
                    basic_info = pd.DataFrame([
                        ['报价单编号', quotation.quotation_number],
                        ['客户名称', quotation.customer.name if quotation.customer else ''],
                        ['项目名称', quotation.project_name or ''],
                        ['项目地址', quotation.project_address or ''],
                        ['有效期至', str(quotation.valid_until) if quotation.valid_until else ''],
                        ['状态', quotation.status],
                        ['总金额', f"{quotation.total_amount} 元" if quotation.total_amount else ''],
                        ['创建时间', quotation.created_at.strftime('%Y-%m-%d %H:%M:%S') if quotation.created_at else ''],
                        ['备注', quotation.notes or '']
                    ], columns=['项目', '内容'])

                    basic_info.to_excel(writer, sheet_name='报价单', index=False, startrow=0)

                    # 写入产品明细
                    if not df.empty:
                        df.to_excel(writer, sheet_name='报价单', index=False, startrow=len(basic_info) + 2)

                output.seek(0)

                filename = f"报价单_{quotation.quotation_number}.xlsx"

                return send_file(
                    output,
                    download_name=filename,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )

            else:  # PDF格式
                from reportlab.lib.pagesizes import A4
                from reportlab.lib import colors
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                from io import BytesIO

                # 注册中文字体
                try:
                    # 尝试使用系统字体
                    import os
                    font_path = None
                    possible_fonts = [
                        'C:/Windows/Fonts/simsun.ttc',  # Windows 宋体
                        'C:/Windows/Fonts/msyh.ttc',    # Windows 微软雅黑
                        '/System/Library/Fonts/PingFang.ttc',  # macOS
                        '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf'  # Linux
                    ]

                    for font in possible_fonts:
                        if os.path.exists(font):
                            font_path = font
                            break

                    if font_path:
                        pdfmetrics.registerFont(TTFont('Chinese', font_path))
                        font_name = 'Chinese'
                    else:
                        font_name = 'Helvetica'  # 回退到默认字体
                except:
                    font_name = 'Helvetica'

                # 创建PDF
                buffer = BytesIO()
                doc = SimpleDocTemplate(buffer, pagesize=A4)
                story = []

                # 样式
                styles = getSampleStyleSheet()
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontName=font_name,
                    fontSize=18,
                    alignment=1,  # 居中
                    spaceAfter=20
                )

                normal_style = ParagraphStyle(
                    'CustomNormal',
                    parent=styles['Normal'],
                    fontName=font_name,
                    fontSize=10
                )

                # 标题
                story.append(Paragraph('报价单', title_style))
                story.append(Spacer(1, 20))

                # 基本信息表格
                basic_data = [
                    ['报价单编号', quotation.quotation_number or ''],
                    ['客户名称', quotation.customer.name if quotation.customer else ''],
                    ['项目名称', quotation.project_name or ''],
                    ['项目地址', quotation.project_address or ''],
                    ['有效期至', str(quotation.valid_until) if quotation.valid_until else ''],
                    ['状态', quotation.status or ''],
                    ['总金额', f"{quotation.total_amount} 元" if quotation.total_amount else ''],
                    ['创建时间', quotation.created_at.strftime('%Y-%m-%d %H:%M:%S') if quotation.created_at else '']
                ]

                basic_table = Table(basic_data, colWidths=[100, 300])
                basic_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, -1), font_name),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                    ('BACKGROUND', (1, 0), (1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(basic_table)
                story.append(Spacer(1, 20))

                # 产品明细标题
                story.append(Paragraph('产品明细', ParagraphStyle(
                    'SubTitle',
                    parent=styles['Heading2'],
                    fontName=font_name,
                    fontSize=14,
                    spaceAfter=10
                )))

                # 产品明细表格
                if export_data:
                    # 表头
                    headers = ['产品名称', '型号', '规格', '单位', '数量', '单价', '折扣(%)', '税率(%)', '金额', '备注']

                    # 如果指定了列，只显示指定的列
                    if columns and columns != ['']:
                        column_mapping = {
                            'product_name': '产品名称',
                            'product_model': '型号',
                            'product_specification': '规格',
                            'product_unit': '单位',
                            'quantity': '数量',
                            'unit_price': '单价',
                            'discount': '折扣(%)',
                            'tax_rate': '税率(%)',
                            'total_price': '金额',
                            'notes': '备注'
                        }
                        headers = [column_mapping[col] for col in columns if col in column_mapping]

                    # 数据行
                    table_data = [headers]
                    for item_data in export_data:
                        if columns and columns != ['']:
                            row = [str(item_data.get(col, '')) for col in columns]
                        else:
                            row = [
                                item_data.get('product_name', ''),
                                item_data.get('product_model', ''),
                                item_data.get('product_specification', ''),
                                item_data.get('product_unit', ''),
                                str(item_data.get('quantity', '')),
                                str(item_data.get('unit_price', '')),
                                str(item_data.get('discount', '')),
                                str(item_data.get('tax_rate', '')),
                                str(item_data.get('total_price', '')),
                                item_data.get('notes', '')
                            ]
                        table_data.append(row)

                    # 创建表格
                    items_table = Table(table_data)
                    items_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, -1), font_name),
                        ('FONTSIZE', (0, 0), (-1, -1), 8),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))

                    story.append(items_table)
                else:
                    story.append(Paragraph('暂无产品明细', normal_style))

                # 备注
                if quotation.notes:
                    story.append(Spacer(1, 20))
                    story.append(Paragraph(f'备注: {quotation.notes}', normal_style))

                # 生成PDF
                doc.build(story)
                buffer.seek(0)

                filename = f"报价单_{quotation.quotation_number}.pdf"

                return send_file(
                    buffer,
                    download_name=filename,
                    mimetype='application/pdf'
                )

        except Exception as e:
            current_app.logger.error(f"导出报价单失败: {str(e)}")
            return make_response(error_response, f"导出报价单失败: {str(e)}")



@api.route('/requests/<int:request_id>/generate-quotation')
class GenerateQuotationFromRequest(Resource):
    @api.doc('generate_quotation_from_request')
    def post(self, request_id):
        """基于报价需求生成报价单"""
        try:
            quotation_request = QuotationRequest.query.options(
                joinedload(QuotationRequest.items),
                joinedload(QuotationRequest.customer)
            ).get(request_id)

            if not quotation_request:
                return make_response(not_found_response, "报价需求不存在")

            if quotation_request.status != '已确认':
                return make_response(error_response, "只有已确认状态的报价需求可以生成报价单")

            # 检查是否所有项目都已匹配
            unmatched_items = [item for item in quotation_request.items
                             if item.matched_product_specification_id is None]
            if unmatched_items:
                return make_response(error_response, "存在未匹配的需求项目，无法生成报价单")

            data = request.get_json() or {}

            # 生成报价单编号
            quotation_number = f"QUO{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"

            # 创建报价单
            quotation = Quotation(
                quotation_number=quotation_number,
                request_id=quotation_request.id,
                customer_id=quotation_request.customer_id,
                project_name=quotation_request.project_name,
                project_address=quotation_request.project_address,
                valid_until=data.get('valid_until', date.today()),
                payment_terms=data.get('payment_terms', ''),
                delivery_terms=data.get('delivery_terms', ''),
                status='待确认',
                notes=data.get('notes', '')
            )

            db.session.add(quotation)
            db.session.flush()

            # 基于需求项目创建报价单项目
            for req_item in quotation_request.items:
                if req_item.matched_product_specification_id:
                    spec = ProductSpecification.query.get(req_item.matched_product_specification_id)
                    product = Product.query.get(req_item.matched_product_id)

                    if spec and product:
                        quotation_item = QuotationItem(
                            quotation_id=quotation.id,
                            product_id=product.id,
                            product_specification_id=spec.id,
                            quantity=req_item.quantity,
                            unit_price=spec.suggested_price,
                            tax_rate=spec.tax_rate or Decimal('13.0'),
                            discount=Decimal('0.0'),
                            notes=req_item.notes or ''
                        )

                        # 填充快照字段
                        quotation_item.product_name = product.name
                        quotation_item.product_model = product.model
                        quotation_item.product_unit = product.unit
                        quotation_item.product_specification = spec.specification

                        # 计算总价
                        quotation_item.total_price = quotation_item.calculate_total_price()
                        db.session.add(quotation_item)

            db.session.flush()

            # 更新报价单总金额
            quotation.total_amount = quotation.calculate_total_amount()

            # 更新报价需求状态
            quotation_request.status = '已确认'

            db.session.commit()

            # 返回生成的报价单
            response_data = QuotationSchema().dump(quotation)
            return make_response(success_response, response_data, "报价单生成成功", 201)

        except Exception as e:
            current_app.logger.error(f"生成报价单失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"生成报价单失败: {str(e)}")


# 报价模板相关API
@api.route('/templates')
class QuotationTemplateList(Resource):
    @api.doc('get_quotation_templates')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    def get(self):
        """获取报价模板列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)

            query = QuotationTemplate.query.order_by(
                QuotationTemplate.is_default.desc(),
                QuotationTemplate.created_at.desc()
            )

            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query,
                serializer_func=lambda item: QuotationTemplateSimpleSchema().dump(item),
                page=page,
                per_page=per_page,
                message="获取报价模板列表成功"
            )

        except Exception as e:
            current_app.logger.error(f"获取报价模板列表失败: {str(e)}")
            return make_response(error_response, f"获取报价模板列表失败: {str(e)}")

    @api.doc('create_quotation_template',
             body=quotation_template_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建报价模板"""
        try:
            data = request.get_json() or {}
            items_data = data.pop('items', [])

            # 数据验证
            template_schema = QuotationTemplateSchema(exclude=(
                'id', 'items', 'created_at', 'updated_at'
            ))
            validated_data = template_schema.load(data)

            # 如果设置为默认模板，取消其他默认模板
            if validated_data.get('is_default'):
                QuotationTemplate.query.filter_by(is_default=True).update({'is_default': False})

            # 创建模板对象
            template = QuotationTemplate(**validated_data)
            db.session.add(template)
            db.session.flush()  # 获取ID

            # 创建模板项目
            if items_data:
                item_schema = QuotationTemplateItemSchema(many=True, exclude=(
                    'id', 'template_id', 'product_name_snapshot', 'product_model_snapshot',
                    'product_specification_snapshot', 'product_unit_snapshot', 'product', 'specification',
                    'created_at', 'updated_at'
                ))
                validated_items = item_schema.load(items_data)

                for item_data in validated_items:
                    # 检查产品是否存在
                    product = Product.query.get(item_data['product_id'])
                    if not product:
                        current_app.logger.warning(f"产品ID {item_data['product_id']} 不存在，跳过此项目")
                        continue

                    # 检查规格是否存在
                    spec = None
                    if item_data.get('product_specification_id'):
                        spec = ProductSpecification.query.get(item_data['product_specification_id'])
                        if not spec or spec.product_id != product.id:
                            current_app.logger.warning(f"规格ID {item_data['product_specification_id']} 不存在或不属于产品 {product.id}")
                            spec = None

                    # 创建模板项目
                    template_item = QuotationTemplateItem(
                        template_id=template.id,
                        product_id=item_data['product_id'],
                        product_specification_id=spec.id if spec else None,
                        quantity=item_data['quantity'],
                        unit_price=item_data.get('unit_price'),
                        tax_rate=item_data.get('tax_rate'),
                        discount=item_data.get('discount'),
                        notes=item_data.get('notes', '')
                    )

                    # 填充快照字段
                    template_item.product_name_snapshot = product.name
                    template_item.product_model_snapshot = product.model
                    template_item.product_unit_snapshot = product.unit
                    if spec:
                        template_item.product_specification_snapshot = spec.specification

                    db.session.add(template_item)

            db.session.commit()

            # 返回创建的模板信息
            created_template = QuotationTemplate.query.get(template.id)
            response_data = QuotationTemplateSchema().dump(created_template)
            return make_response(success_response, response_data, "报价模板创建成功", 201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建报价模板失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建报价模板失败: {str(e)}")


@api.route('/requests/template')
class QuotationRequestTemplate(Resource):
    @api.doc('download_quotation_request_template')
    def get(self):
        """下载询价单模板"""
        try:
            # 创建智能导入模板数据
            template_data = {
                '产品名称': ['示例产品A', '示例产品B', '示例产品C', '示例产品D'],
                '产品型号': ['型号A001', '型号B002', '型号C003', '型号D004'],
                '产品规格': ['规格描述A', '规格描述B', '规格描述C', '规格描述D'],
                '数量': [10, 5, 8, 15],
                '单位': ['个', '套', '台', '米'],
                '备注': ['产品A的备注', '产品B的备注', '产品C的备注', '产品D的备注']
            }

            # 创建DataFrame
            df = pd.DataFrame(template_data)

            # 创建Excel文件
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # 写入产品明细
                df.to_excel(writer, sheet_name='产品明细', index=False)

                # 获取工作表对象进行格式化
                worksheet = writer.sheets['产品明细']

                # 设置列宽
                worksheet.column_dimensions['A'].width = 20  # 产品名称
                worksheet.column_dimensions['B'].width = 15  # 产品型号
                worksheet.column_dimensions['C'].width = 25  # 产品规格
                worksheet.column_dimensions['D'].width = 10  # 数量
                worksheet.column_dimensions['E'].width = 10  # 单位
                worksheet.column_dimensions['F'].width = 30  # 备注

                # 添加详细说明信息
                worksheet['A8'] = '📋 智能导入模板使用说明：'
                worksheet['A9'] = ''
                worksheet['A10'] = '✨ 智能表头识别功能：'
                worksheet['A11'] = '• 系统支持多种列名格式，无需严格按照模板列名'
                worksheet['A12'] = '• 产品名称列：支持"产品名称"、"物料名称"、"名称"、"品名"'
                worksheet['A13'] = '• 产品型号列：支持"型号"、"产品型号"、"规格型号"'
                worksheet['A14'] = '• 产品规格列：支持"规格"、"产品规格"、"技术参数"、"规格描述"'
                worksheet['A15'] = '• 数量列：支持"数量"、"需求数量"、"用量"、"采购数量"（必填）'
                worksheet['A16'] = '• 单位列：支持"单位"、"计量单位"'
                worksheet['A17'] = '• 备注列：支持"备注"、"产品备注"、"说明"'
                worksheet['A18'] = ''
                worksheet['A19'] = '📝 填写要求：'
                worksheet['A20'] = '• 产品名称和数量为必填项，其他字段可选'
                worksheet['A21'] = '• 数量必须为正数，支持小数'
                worksheet['A22'] = '• 系统会自动跳过空行和无效数据'
                worksheet['A23'] = '• 表头可以在Excel文件的前10行内任意位置'
                worksheet['A24'] = ''
                worksheet['A25'] = '🚀 使用步骤：'
                worksheet['A26'] = '1. 在上方示例数据下方添加您的产品信息'
                worksheet['A27'] = '2. 保存Excel文件'
                worksheet['A28'] = '3. 在系统中选择此文件进行导入'
                worksheet['A29'] = '4. 系统会自动识别表头并导入数据'

            output.seek(0)

            # 设置响应头
            filename = f"智能导入模板_{datetime.now().strftime('%Y%m%d')}.xlsx"

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )

        except Exception as e:
            current_app.logger.error(f"生成询价单模板失败: {str(e)}")
            return make_response(error_response, f"生成询价单模板失败: {str(e)}")
