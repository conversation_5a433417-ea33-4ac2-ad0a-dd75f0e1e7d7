<template>
  <div class="category-list">
    <!-- 页面头部 -->
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="form-title">产品分类管理</h2>
        <div>
          <el-button type="primary" @click="handleAddTopCategory">
            <el-icon><Plus /></el-icon>
            新增顶级分类
          </el-button>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20">
      <!-- 左侧分类树 -->
      <el-col :span="10">
        <el-card v-loading="loading">
          <template #header>
            <div class="card-header">
              <span>分类列表</span>
            </div>
          </template>
          <el-tree
            ref="treeRef"
            :data="categoryTree"
            :props="defaultProps"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
            :key="treeKey"
            class="category-tree"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <span>{{ data.name }}</span>
                <span class="tree-actions">
                  <el-button 
                    v-if="data.level < 3" 
                    type="primary" 
                    link 
                    size="small" 
                    @click.stop="handleAddChild(data)"
                  >
                    添加子分类
                  </el-button>
                  <el-button type="primary" link size="small" @click.stop="handleEdit(data)">
                    编辑
                  </el-button>
                  <el-button type="danger" link size="small" @click.stop="handleDelete(node, data)">
                    删除
                  </el-button>
                </span>
              </div>
            </template>
          </el-tree>
        </el-card>
      </el-col>

      <!-- 右侧详情/编辑区域 -->
      <el-col :span="14">
        <el-card v-if="currentCategory.id">
          <template #header>
            <div class="card-header">
              <span>{{ isEdit ? '编辑分类' : '分类详情' }}</span>
              <el-button v-if="!isEdit" type="primary" link @click="isEdit = true">
                编辑
              </el-button>
            </div>
          </template>
          
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="100px" 
            :disabled="!isEdit" 
          >
            <el-form-item label="分类名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入分类名称" />
            </el-form-item>
            <el-form-item label="父级分类" prop="parent_id">
              <el-select
                v-model="formData.parent_id"
                placeholder="请选择父级分类"
                style="width: 100%"
                :disabled="!isEdit"
                clearable
              >
                <el-option label="无父级分类 (设为顶级)" :value="null" />
                <el-option
                  v-for="item in availableParentCategories" 
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="排序" prop="sort_order">
              <el-input-number v-model="formData.sort_order" :min="0" :max="999" style="width: 100%" />
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入分类描述"
              />
            </el-form-item>
            <el-form-item label="备注" prop="notes">
              <el-input
                v-model="formData.notes"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>
            <el-form-item v-if="isEdit">
              <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
              <el-button @click="cancelEdit">取消</el-button>
            </el-form-item>
          </el-form>

        </el-card>
        <el-card v-else>
          <template #header>
            <div class="card-header">
              <span>分类详情</span>
            </div>
          </template>
          <el-empty description="请选择左侧分类查看详情" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增分类对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增分类' : '添加子分类'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="dialogFormRef"
        :model="dialogForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="dialogForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="父级分类" prop="parent_id">
          <el-input 
            v-if="dialogType === 'addChild'" 
            :value="parentCategoryName" 
            readonly 
          />
          <el-select
            v-else 
            v-model="dialogForm.parent_id"
            placeholder="请选择父级分类 (可选)"
            style="width: 100%"
            clearable 
          >
            <el-option label="无父级分类 (设为顶级)" :value="null" />
            <el-option
              v-for="item in availableParentCategories"
              :key="item.id"
              :label="item.name" 
              :value="item.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="dialogForm.sort_order" :min="0" :max="999" style="width: 100%" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="dialogForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="dialogForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddSave" :loading="saving">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { categoryApi } from '@/api/product'
import type { ProductCategory } from '@/types/api'

// 引用
const treeRef = ref()
const formRef = ref()
const dialogFormRef = ref()

// 状态
const loading = ref(false)
const saving = ref(false)
const isEdit = ref(false)
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'addChild'>('add')
const treeKey = ref(0)

// 数据
const categoryList = ref<ProductCategory[]>([])
const currentCategory = ref<Partial<ProductCategory>>({})

// 表单数据
const formData = reactive<Partial<ProductCategory>>({
  name: '',
  parent_id: null,
  sort_order: 0,
  description: '',
  notes: ''
})

const dialogForm = reactive<Partial<ProductCategory>>({
  name: '',
  parent_id: null,
  sort_order: 0,
  description: '',
  notes: ''
})

// 分类树配置
const defaultProps = {
  children: 'children',
  label: 'name'
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', message: '排序必须为数字', trigger: 'blur' }
  ]
}

// 计算属性
const categoryTree = computed(() => {
  return buildTree(categoryList.value)
})

const availableParentCategories = computed(() => {
  // 过滤掉当前分类及其子分类，避免循环引用
  return categoryList.value.filter(cat => {
    if (!currentCategory.value.id) return true
    return cat.id !== currentCategory.value.id && !isDescendant(cat.id, currentCategory.value.id)
  })
})

const parentCategoryName = computed(() => {
  if (dialogForm.parent_id) {
    const parent = categoryList.value.find(c => c.id === dialogForm.parent_id)
    return parent ? parent.name : '未知分类'
  }
  return '无父级分类 (设为顶级)'
})

// 构建树结构
const buildTree = (categories: ProductCategory[]): ProductCategory[] => {
  const map = new Map<number, ProductCategory>()
  const roots: ProductCategory[] = []

  // 创建映射
  categories.forEach(cat => {
    map.set(cat.id, { ...cat, children: [] })
  })

  // 构建树
  categories.forEach(cat => {
    const node = map.get(cat.id)!
    if (cat.parent_id && map.has(cat.parent_id)) {
      const parent = map.get(cat.parent_id)!
      if (!parent.children) parent.children = []
      parent.children.push(node)
    } else {
      roots.push(node)
    }
  })

  return roots
}

// 检查是否为子孙节点
const isDescendant = (categoryId: number, ancestorId: number): boolean => {
  const category = categoryList.value.find(c => c.id === categoryId)
  if (!category || !category.parent_id) return false
  if (category.parent_id === ancestorId) return true
  return isDescendant(category.parent_id, ancestorId)
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    loading.value = true
    const response = await categoryApi.getList({ per_page: 999 }) as any
    categoryList.value = Array.isArray(response) ? response : (response.data || response.items || [])
    treeKey.value += 1
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 处理节点点击
const handleNodeClick = (data: ProductCategory) => {
  currentCategory.value = { ...data }
  Object.assign(formData, data)
  isEdit.value = false
}

// 处理添加顶级分类
const handleAddTopCategory = () => {
  dialogType.value = 'add'
  Object.assign(dialogForm, {
    name: '',
    parent_id: null,
    sort_order: 0,
    description: '',
    notes: ''
  })
  dialogVisible.value = true
}

// 处理添加子分类
const handleAddChild = (data: ProductCategory) => {
  dialogType.value = 'addChild'
  Object.assign(dialogForm, {
    name: '',
    parent_id: data.id,
    sort_order: 0,
    description: '',
    notes: ''
  })
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (data: ProductCategory) => {
  handleNodeClick(data)
  isEdit.value = true
}

// 处理删除
const handleDelete = async (node: any, data: ProductCategory) => {
  try {
    await ElMessageBox.confirm(`确定要删除分类"${data.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await categoryApi.delete(data.id)
    ElMessage.success('删除成功')
    fetchCategories()
    
    // 如果删除的是当前选中的分类，清空详情
    if (currentCategory.value.id === data.id) {
      currentCategory.value = {}
      Object.assign(formData, {
        name: '',
        parent_id: null,
        sort_order: 0,
        description: '',
        notes: ''
      })
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除分类失败')
    }
  }
}

// 保存编辑
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true

    // 只提交可编辑的字段
    const submitData = {
      name: formData.name,
      parent_id: formData.parent_id,
      sort_order: formData.sort_order,
      description: formData.description,
      notes: formData.notes
    }

    // 清理空字段
    if (!submitData.description) delete submitData.description
    if (!submitData.notes) delete submitData.notes

    await categoryApi.update(currentCategory.value.id!, submitData)
    ElMessage.success('更新成功')
    isEdit.value = false
    fetchCategories()
  } catch (error) {
    console.error('更新分类失败:', error)
    ElMessage.error('更新分类失败')
  } finally {
    saving.value = false
  }
}

// 取消编辑
const cancelEdit = () => {
  isEdit.value = false
  Object.assign(formData, currentCategory.value)
}

// 保存新增
const handleAddSave = async () => {
  try {
    await dialogFormRef.value?.validate()
    saving.value = true
    
    const submitData = { ...dialogForm }
    if (!submitData.description) delete submitData.description
    if (!submitData.notes) delete submitData.notes
    
    await categoryApi.create(submitData)
    ElMessage.success('创建成功')
    dialogVisible.value = false
    fetchCategories()
  } catch (error) {
    console.error('创建分类失败:', error)
    ElMessage.error('创建分类失败')
  } finally {
    saving.value = false
  }
}

// 初始化
onMounted(() => {
  fetchCategories()
})
</script>

<style lang="scss" scoped>
.category-list {
  .header-card {
    .form-title {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .category-tree {
    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      padding-right: 8px;
      
      .tree-actions {
        opacity: 0;
        transition: opacity 0.3s;
        
        .el-button {
          margin-left: 8px;
        }
      }
      
      &:hover .tree-actions {
        opacity: 1;
      }
    }
  }
}
</style>
