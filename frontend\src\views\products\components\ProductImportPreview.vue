<template>
  <div class="product-import-preview">
    <el-alert
      v-if="previewData.length > 0"
      type="info"
      title="请确认识别结果是否正确，可以进行编辑"
      :closable="false"
      show-icon
      class="mb-20"
    />
    <el-alert
      v-else
      type="warning"
      title="未能识别任何有效产品数据，请检查Excel文件格式"
      :closable="false"
      show-icon
      class="mb-20"
    />

    <!-- 数据预览表格 -->
    <el-table
      v-if="previewData.length > 0"
      :data="previewData"
      border
      stripe
      style="width: 100%"
      max-height="500px"
      v-loading="loading"
    >
      <!-- 操作列 -->
      <el-table-column fixed="left" label="操作" width="80" align="center">
        <template #default="{ row, $index }">
          <el-button
            type="danger"
            size="small"
            circle
            @click="removeProduct($index)"
            :title="'删除此行'"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </template>
      </el-table-column>

      <!-- 基础信息列 -->
      <el-table-column label="产品名称" min-width="150">
        <template #default="{ row }">
          <el-input v-model="row.name" placeholder="必填" size="small" />
        </template>
      </el-table-column>

      <el-table-column label="产品型号" min-width="120">
        <template #default="{ row }">
          <el-input v-model="row.model" placeholder="型号" size="small" />
        </template>
      </el-table-column>

      <el-table-column label="单位" width="80">
        <template #default="{ row }">
          <el-input v-model="row.unit" placeholder="个" size="small" />
        </template>
      </el-table-column>

      <el-table-column label="产品类别" min-width="150">
        <template #default="{ row }">
          <el-select v-model="row.category_id" placeholder="选择类别" size="small" style="width: 100%">
            <el-option
              v-for="cat in sortedCategories"
              :key="cat.value || cat.id"
              :label="formatCategoryLabel(cat)"
              :value="cat.value || cat.id"
            />
            <!-- 如果没有匹配的类别，显示新建选项 -->
            <el-option
              v-if="row.category_name && !getCategoryById(row.category_id)"
              :label="`创建新类别: ${row.category_name}`"
              :value="-1"
            />
          </el-select>
        </template>
      </el-table-column>

      <!-- 规格价格信息 -->
      <el-table-column label="规格" min-width="120">
        <template #default="{ row }">
          <div v-if="row.specifications && row.specifications.length">
            <div v-for="(spec, specIndex) in row.specifications" :key="specIndex" class="specification-item">
              <el-input
                v-model="spec.specification"
                placeholder="规格名称"
                size="small"
                style="margin-bottom: 5px;"
              />
            </div>
            <el-button type="primary" link size="small" @click="addSpecification(row)">
              添加规格
            </el-button>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="成本价" width="100">
        <template #default="{ row }">
          <div v-if="row.specifications && row.specifications.length">
            <div v-for="(spec, specIndex) in row.specifications" :key="specIndex" class="specification-item">
              <el-input-number
                v-model="spec.cost_price"
                :precision="2"
                :step="0.1"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 100%; margin-bottom: 5px;"
              />
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="建议售价" width="100">
        <template #default="{ row }">
          <div v-if="row.specifications && row.specifications.length">
            <div v-for="(spec, specIndex) in row.specifications" :key="specIndex" class="specification-item">
              <el-input-number
                v-model="spec.suggested_price"
                :precision="2"
                :step="0.1"
                :min="0"
                controls-position="right"
                size="small"
                style="width: 100%; margin-bottom: 5px;"
              />
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="税率(%)" width="100">
        <template #default="{ row }">
          <div v-if="row.specifications && row.specifications.length">
            <div v-for="(spec, specIndex) in row.specifications" :key="specIndex" class="specification-item">
              <div class="spec-row">
                <el-input-number
                  v-model="spec.tax_rate"
                  :precision="2"
                  :step="0.5"
                  :min="0"
                  :max="100"
                  controls-position="right"
                  size="small"
                  style="width: 100%; margin-bottom: 5px;"
                />
                <el-button
                  v-if="row.specifications.length > 1"
                  type="danger"
                  size="small"
                  circle
                  @click="removeSpecification(row, specIndex)"
                  style="margin-left: 5px;"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 其他属性列 -->
      <el-table-column
        v-for="attr in dynamicAttributes"
        :key="attr.key"
        :label="attr.label"
        min-width="120"
      >
        <template #default="{ row }">
          <el-input
            v-model="row.attributes[attr.key]"
            :placeholder="attr.label"
            size="small"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 导入按钮 -->
    <div v-if="previewData.length > 0" class="import-actions" style="margin-top: 20px; text-align: center;">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="handleImport" :loading="importing">
        确认导入 ({{ previewData.length }} 个产品)
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { productApi } from '@/api/product'

// Props
const props = defineProps<{
  previewData: any[]
  categories: any[]
  mappings: any
}>()

// Emits
const emit = defineEmits<{
  success: [result: any]
  cancel: []
}>()

// 响应式数据
const loading = ref(false)
const importing = ref(false)

// 计算属性
const sortedCategories = computed(() => {
  return props.categories.sort((a, b) => {
    const aLevel = a.level || 0
    const bLevel = b.level || 0
    if (aLevel !== bLevel) {
      return aLevel - bLevel
    }
    return (a.name || '').localeCompare(b.name || '')
  })
})

const dynamicAttributes = computed(() => {
  const attrs = new Set<string>()
  props.previewData.forEach(product => {
    if (product.attributes) {
      Object.keys(product.attributes).forEach(key => attrs.add(key))
    }
  })
  return Array.from(attrs).map(key => ({
    key,
    label: key
  }))
})

// 方法
const formatCategoryLabel = (category: any) => {
  const level = category.level || 0
  const indent = '　'.repeat(Math.max(0, level - 1))
  const displayName = category.label || category.name || '未知类别'
  return `${indent}${displayName}`
}

const getCategoryById = (id: any) => {
  return props.categories.find(cat => (cat.value || cat.id) === id)
}

const removeProduct = (index: number) => {
  props.previewData.splice(index, 1)
}

const addSpecification = (product: any) => {
  if (!product.specifications) {
    product.specifications = []
  }
  product.specifications.push({
    specification: '',
    cost_price: 0,
    suggested_price: 0,
    tax_rate: 13
  })
}

const removeSpecification = (product: any, specIndex: number) => {
  if (product.specifications && product.specifications.length > 1) {
    product.specifications.splice(specIndex, 1)
  }
}

const handleImport = async () => {
  try {
    importing.value = true

    // 验证数据
    const errors = validateData()
    if (errors.length > 0) {
      ElMessage.error(`数据验证失败：${errors[0]}`)
      return
    }

    // 准备导入数据
    const importData = {
      products: props.previewData.map(product => ({
        name: product.name?.trim(),
        model: product.model?.trim(),
        unit: product.unit?.trim() || '个',
        category_id: product.category_id === -1 ? null : product.category_id,
        category_name: product.category_id === -1 ? product.category_name : null,
        description: product.description?.trim(),
        status: product.status || '正常',
        specifications: product.specifications || [],
        attributes: Object.entries(product.attributes || {})
          .filter(([key, value]) => key && value)
          .map(([key, value]) => ({
            attribute_name: key,
            attribute_value: String(value).trim()
          }))
      }))
    }

    // 调用导入API
    const response = await productApi.importFromPreview(importData)

    // 响应拦截器已经处理了success判断，直接使用返回的数据
    emit('success', response)
    ElMessage.success('导入成功')
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

const validateData = () => {
  const errors: string[] = []
  
  props.previewData.forEach((product, index) => {
    if (!product.name?.trim()) {
      errors.push(`第${index + 1}行：产品名称不能为空`)
    }
    if (!product.model?.trim()) {
      errors.push(`第${index + 1}行：产品型号不能为空`)
    }
    if (!product.category_id) {
      errors.push(`第${index + 1}行：请选择产品类别`)
    }
  })
  
  return errors
}
</script>

<style scoped>
.product-import-preview {
  padding: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.specification-item {
  margin-bottom: 5px;
}

.spec-row {
  display: flex;
  align-items: center;
}

.import-actions {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}
</style>
