/**
 * 退货管理API
 * 包括退货单管理、退货状态管理、退货原因管理等功能
 */
import { http } from './request'
import type {
  ReturnOrder,
  ListReturnOrderParams,
  CreateReturnOrderData,
  UpdateReturnOrderData,
  ReturnOrderStatus,
  ReturnableDeliveryNote
} from '@/types/return'
import type { ApiResponse, PaginatedListResponse } from '@/types/api'

/**
 * 退货管理API类
 */
export class ReturnApi {
  private baseUrl = '/returns'

  /**
   * 获取退货单列表
   */
  async getList(params?: ListReturnOrderParams): Promise<ApiResponse<PaginatedListResponse<ReturnOrder>>> {
    return http.get(`${this.baseUrl}`, params)
  }

  /**
   * 获取退货单详情
   */
  async getById(id: number): Promise<ApiResponse<ReturnOrder>> {
    return http.get(`${this.baseUrl}/${id}`)
  }

  /**
   * 创建退货单
   */
  async create(data: CreateReturnOrderData): Promise<ApiResponse<ReturnOrder>> {
    return http.post(`${this.baseUrl}`, data)
  }

  /**
   * 更新退货单
   */
  async update(id: number, data: UpdateReturnOrderData): Promise<ApiResponse<ReturnOrder>> {
    return http.put(`${this.baseUrl}/${id}`, data)
  }

  /**
   * 删除退货单
   */
  async delete(id: number): Promise<ApiResponse<void>> {
    return http.delete(`${this.baseUrl}/${id}`)
  }

  /**
   * 更新退货单状态
   */
  async updateStatus(id: number, status: ReturnOrderStatus, notes?: string): Promise<ApiResponse<ReturnOrder>> {
    return http.put(`${this.baseUrl}/${id}/status`, { status, notes })
  }

  /**
   * 获取可退货的发货单列表（必须是已完成状态的发货单）
   */
  async getReturnableDeliveryNotes(customerId?: string, status: string = '已签收'): Promise<ApiResponse<ReturnableDeliveryNote[]>> {
    return http.get(`${this.baseUrl}/available-delivery-notes`, {
      customer_id: customerId,
      status
    })
  }

  /**
   * 获取指定订单产品的已退货数量（只计算"退货中"、"已完成"状态的退货单）
   */
  async getProductReturnedQuantity(orderProductId: number): Promise<ApiResponse<number>> {
    return http.get(`${this.baseUrl}/product-returned-quantity`, {
      order_product_id: orderProductId
    })
  }

  /**
   * 基于订单创建退货单模板
   */
  async createFromOrder(orderId: number): Promise<ApiResponse<any>> {
    return http.post(`${this.baseUrl}/from-order/${orderId}`)
  }

  /**
   * 获取退货状态列表
   */
  async getStatuses(): Promise<ApiResponse<any>> {
    return http.get(`${this.baseUrl}/statuses`)
  }

  /**
   * 获取退货原因列表
   */
  async getReasons(): Promise<ApiResponse<string[]>> {
    return http.get(`${this.baseUrl}/reasons`)
  }

  /**
   * 导出退货单
   */
  async export(id: number, params: {
    format: 'xlsx' | 'pdf'
    columns?: string[]
    include_header?: boolean
  }): Promise<Blob> {
    const queryParams = new URLSearchParams()
    queryParams.append('format', params.format)
    if (params.columns && params.columns.length > 0) {
      queryParams.append('columns', params.columns.join(','))
    }
    if (params.include_header !== undefined) {
      queryParams.append('include_header', params.include_header.toString())
    }

    const response = await fetch(`/api/v1/return-export/${id}/export?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`导出失败: ${response.statusText}`)
    }

    return response.blob()
  }
}

// 导出API实例
export const returnApi = new ReturnApi()

// 导出默认实例
export default returnApi


