import{I as t}from"./index-3d4c440c.js";const r="/api/v1/orders";async function e(e){return t({url:r,method:"get",params:e,transformResponse:t=>{try{const r=JSON.parse(t);return 200===r.code&&r.data&&r.data.list&&(r.data.list=r.data.list.map((t=>{if(void 0!==t.statement_amount&&null!==t.statement_amount)return t;let r=0;return t.statement_items&&Array.isArray(t.statement_items)&&(r=t.statement_items.reduce(((t,r)=>t+(Number(r.amount)||0)),0)),{...t,statement_amount:r}}))),r}catch(r){return console.error("解析订单列表响应数据失败:",r),{code:500,message:"解析响应失败",data:null}}}})}function a(e){return t({url:`${r}/${e}`,method:"get",transformResponse:t=>{try{const r=JSON.parse(t);if(200===r.code&&r.data){const t=r.data;t.products&&Array.isArray(t.products)?console.log(`订单ID ${e} 成功获取 ${t.products.length} 个产品`):(console.warn(`订单ID ${e} 缺少产品信息或格式不正确`),t.products=[]);let a=0;t.delivery_notes&&Array.isArray(t.delivery_notes)&&t.delivery_notes.forEach((t=>{t.statement_id&&t.items&&Array.isArray(t.items)&&(a+=t.items.reduce(((t,r)=>{const e=Number(r.unit_price||0);return t+r.quantity*e}),0))})),t.statement_amount=a}return r}catch(r){return console.error("解析订单响应数据失败:",r),{code:500,message:"解析响应失败",data:null}}}})}async function s(e){return t({url:`${r}`,method:"get",params:{query:e,search:!0}})}async function n(e){return t({url:`${r}/${e}/available-products`,method:"get"})}async function o(r){return t({url:"/api/v1/orders",method:"post",data:r})}async function u(e,a){return t({url:`${r}/${e}`,method:"put",data:a})}async function d(r,e){return t({url:`/api/v1/order-products/${r}`,method:"put",data:e})}async function c(e){return t({url:`${r}/${e}`,method:"delete"})}async function i(e,a){return t({url:`${r}/${e}/status`,method:"put",data:a})}async function m(r,e,a={}){return t({url:`/api/v1/orders/${r}/add-items-from-quotation/${e}`,method:"post",data:a})}async function l(e,a){return t({url:`${r}/${e}/payments`,method:"post",data:a})}async function p(r){return t({url:"/api/v1/orders/batch/delete",method:"post",data:{ids:r}})}async function y(r){return t({url:"/api/v1/order-products/get-orders",method:"post",data:{order_product_ids:r}})}function f(r){return t({url:"/api/v1/orders/payable",method:"get",params:r})}export{e as a,p as b,a as c,c as d,u as e,o as f,y as g,d as h,n as i,l as j,m as k,f as l,s,i as u};
